USE [live_ilmdata]
GO
/****** Object:  StoredProcedure [dbo].[ElvyUpd]    Script Date: 12/12/2017 16:16:37 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[ElvyUpd] 
	-- Add the parameters for the stored procedure here
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.

	SET NOCOUNT ON;
	CREATE TABLE dbo.tmp
    (
		fctlid_hd varchar(10) NOT NULL,
		[list] varchar(1000)  NULL,
		[list1] varchar(1000)  NULL
	)

	DECLARE @fctlid_hd varchar(50)
	DECLARE cur cursor local fast_forward for
	select fctlid_hd from mlvyclass

	OPEN CUR
	FETCH NEXT FROM CUR INTO @fctlid_hd
	While @@fetch_status=0 
		BEGIN
			declare @str varchar(1000)
			set @str = ' '

			select @str = cast(rtrim(fclass) as varchar) + ',' + @str  from mlvyclass where fctlid_hd=@fctlid_hd and fexclude = '2'

			declare @str1 varchar(1000)
			set @str1 = ' '
			select @str1 = @str1 + ',' + cast(rtrim(fclass) as varchar) from mlvyclass where fctlid_hd=@fctlid_hd and fexclude = '1'

			insert into tmp (fctlid_hd,list,list1) VALUES (@fctlid_hd,@str,@str1)

			FETCH NEXT FROM CUR INTO @fctlid_hd
		END
	CLOSE CUR
	DEALLOCATE CUR

			select distinct fctlid,fid as Levy#,fdesc as Desc#,fefffr as [Eff From] ,feffto as [Eff To], 
			case when factive='1' then 'Yes' else 'No' end as factive,fupperlmt,frate,
			case when factive='1' then 'Yes' else 'No' end as fapplymax,list,list1 
			from mlvyhd a left join tmp b on a.fctlid = b.fctlid_hd

			drop table tmp
END


GO
/****** Object:  StoredProcedure [dbo].[SurplusList]    Script Date: 12/12/2017 16:16:37 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE PROCEDURE [dbo].[SurplusList] 
	-- Add the parameters for the stored procedure here
	@fctlid_1 nvarchar(50)
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.

	SET NOCOUNT ON;
	CREATE TABLE dbo.tmp
    (
		[fctlid_1] varchar(50) NOT NULL,
		[fctlid_2] varchar(50) NOT NULL,
		[list] varchar(1000) NOT NULL,
		[list1] varchar(1000) NOT NULL
	)

	DECLARE @fctlid_2 varchar(50)
	DECLARE cur cursor local fast_forward for
	select fctlid from mtysec where fctlid_1=@fctlid_1

	OPEN CUR
	FETCH NEXT FROM CUR INTO @fctlid_2
	While @@fetch_status=0 
		BEGIN
			declare @str varchar(1000)
			set @str = ''
			select @str = @str + ',' + cast(rtrim(fdesc) as varchar) from mtyclass where fctlid_1=@fctlid_1 and fctlid_2 = @fctlid_2 and ftype='1'
			set @str = right(@str , len(@str) - 1)

			declare @str1 varchar(1000)
			set @str1 = ''
			select @str1 = @str1 + ',' + cast(rtrim(fdesc) as varchar) from mtyclass where fctlid_1=@fctlid_1 and fctlid_2 = @fctlid_2 and ftype='2'
			set @str1 = right(@str1 , len(@str1) - 1)

			insert into tmp (fctlid_1,fctlid_2,list,list1) VALUES (@fctlid_1,@fctlid_2,@str,@str1)
			
			FETCH NEXT FROM CUR INTO @fctlid_2
		END
	CLOSE CUR
	DEALLOCATE CUR

	select b.fctlid, b.fid as Sec#, b.ftylmt as [Treaty Limit], ftimes, fscale1 ,fscale2 ,fcom ,
	case when fgrnet='1' then 'Gross' else 'Net' end as fgrnet,a.fctlid_1,list, list1 
	from tmp a left join mtysec b on a.fctlid_1 = b.fctlid_1 and a.fctlid_2 = b.fctlid  

	drop table tmp
END




GO
/****** Object:  StoredProcedure [dbo].[XOLList]    Script Date: 12/12/2017 16:16:37 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[XOLList] 
	-- Add the parameters for the stored procedure here
	@fctlid_1 nvarchar(50),
	@fctlid_2 nvarchar(50)
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.

	SET NOCOUNT ON;
	CREATE TABLE dbo.tmp
    (
		[fctlid_1] varchar(50) NOT NULL,
		[fctlid_2] varchar(50) NOT NULL,
		[fctlid_3] varchar(50) NOT NULL,
		[list] varchar(1000) NOT NULL,
		[list1] varchar(1000) NOT NULL
	)

	DECLARE @fctlid_3 varchar(50)
	DECLARE cur cursor local fast_forward for
	select fctlid from mxllayrb where fctlid_1=@fctlid_1 and fctlid_2=@fctlid_2

	OPEN CUR
	FETCH NEXT FROM CUR INTO @fctlid_3
	While @@fetch_status=0 
		BEGIN
			declare @str varchar(1000)
			set @str = ''
			select @str = @str + ',' + cast(rtrim(fdesc) as varchar) from mxlclass where fctlid_1=@fctlid_1 and fctlid_2 = @fctlid_2 and fctlid_3 = @fctlid_3 and ftype='1'
			set @str = right(@str , len(@str) - 1)

			declare @str1 varchar(1000)
			set @str1 = ''
			select @str1 = @str1 + ',' + cast(rtrim(fdesc) as varchar) from mxlclass where fctlid_1=@fctlid_1 and fctlid_2 = @fctlid_2 and fctlid_3 = @fctlid_3 and ftype='2'
			set @str1 = right(@str1 , len(@str1) - 1)

			insert into tmp (fctlid_1,fctlid_2,fctlid_3,list,list1) VALUES (@fctlid_1,@fctlid_2,@fctlid_3,@str,@str1)
			
			FETCH NEXT FROM CUR INTO @fctlid_3
		END
	CLOSE CUR
	DEALLOCATE CUR

	select a.fctlid_1, a.fctlid_2, a.fctlid_3, b.fid as Item#,b.flimit as [Loss Limit], b.fexcess as [Excess Point],fctlid,list, list1 
	from tmp a left join mxllayrb b on a.fctlid_2 = b.fctlid_2 and a.fctlid_3 = b.fctlid where a.fctlid_2=@fctlid_2

	drop table tmp
END




GO
/****** Object:  StoredProcedure [dbo].[XOLListUpd]    Script Date: 12/12/2017 16:16:37 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[XOLListUpd] 
	-- Add the parameters for the stored procedure here
	@fctlid_1 nvarchar(50)
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.

	SET NOCOUNT ON;
	CREATE TABLE dbo.tmp
    (
		[fctlid_1] varchar(50) NOT NULL,
		[fctlid_2] varchar(50) NOT NULL,
		[fctlid_3] varchar(50) NOT NULL,
		[list] varchar(1000) NOT NULL,
		[list1] varchar(1000) NOT NULL
	)

	DECLARE @fctlid_2 varchar(50)
	DECLARE @fctlid_3 varchar(50)
	DECLARE cur cursor local fast_forward for
	select fctlid_2,fctlid from mxllayrb where fctlid_1=@fctlid_1

	OPEN CUR
	FETCH NEXT FROM CUR INTO @fctlid_2,@fctlid_3
	While @@fetch_status=0 
		BEGIN
			declare @str varchar(1000)
			set @str = ''
			select @str = @str + ',' + cast(rtrim(fdesc) as varchar) from mxlclass where fctlid_1=@fctlid_1 and fctlid_2 = @fctlid_2 and fctlid_3 = @fctlid_3 and ftype='1'
			set @str = right(@str , len(@str) - 1)

			declare @str1 varchar(1000)
			set @str1 = ''
			select @str1 = @str1 + ',' + cast(rtrim(fdesc) as varchar) from mxlclass where fctlid_1=@fctlid_1 and fctlid_2 = @fctlid_2 and fctlid_3 = @fctlid_3 and ftype='2'
			set @str1 = right(@str1 , len(@str1) - 1)

			insert into tmp (fctlid_1,fctlid_2,fctlid_3,list,list1) VALUES (@fctlid_1,@fctlid_2,@fctlid_3,@str,@str1)
			
			FETCH NEXT FROM CUR INTO @fctlid_2,@fctlid_3
		END
	CLOSE CUR
	DEALLOCATE CUR

	select a.fctlid_1, a.fctlid_2, a.fctlid_3, b.fid as Item#,b.flimit as [Loss Limit], b.fexcess as [Excess Point],fctlid,list, list1 
	from tmp a left join mxllayrb b on a.fctlid_2 = b.fctlid_2 and a.fctlid_3 = b.fctlid  

	drop table tmp
END


GO
