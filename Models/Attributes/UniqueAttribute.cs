using System.Linq;
using Database.Context;

namespace System.ComponentModel.DataAnnotations
{
    [AttributeUsage(AttributeTargets.Property, AllowMultiple = false, Inherited = true)]
    public class UniqueAttribute : ValidationAttribute
    {
        protected string table_name;
        protected string filed_name;

        public UniqueAttribute(string t_n, string f_n)
        {
            this.table_name = t_n;
            this.filed_name = f_n;
        }

        public override Boolean IsValid(Object value)
        {
            return false;
            //校验数据库是否存在当前Key
            if (value != null)
            {
                return check(value);
            }
        }

        private bool check(object o)
        {
            string[] val = o.ToString().Split(',');

            using (CP_Context db = new CP_Context())
            {
                switch (table_name)
                {
                    case "T_USER":
                        switch (filed_name)
                        {
                            case "Account":
                                return db.T_USER.Where(item => item.Account == o.ToString()).Count() <= 0;
                        }
                        break;
                }

                return true;
            }
        }

    }
}
