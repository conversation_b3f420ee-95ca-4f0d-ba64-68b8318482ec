@model Models.EntityDtos.Apply2BDto

@{
    ViewBag.Title = "";
}

@section Footer{

    <script type="text/javascript" src="/Scripts/lib/My97DatePicker/WdatePicker.js"></script>
    <script>

        layer.open({
            title: '',closeBtn:0,
            content: '請注意，本表格只為方便閣下/貴公司填寫及記錄有關資料。如慾利用此表格向勞工處呈報意外，請將填妥的表格列印（一式兩份）并蓋上公司印/簽名，然後交回有關的勞工處僱員補償科辦事處。勞工處只接受有公司蓋印/簽名 正本文件。如閣下/貴公司希望透過網上呈報意外，請利用電子表格(http://www.labour.gov.hk/tc/form/form.htm)。',
            btn: ['確定', '取消'],
            yes: function (index, layero) {
                layer.closeAll();
            },
            btn2: function (index, layero) {
                window.location.href = "@Url.Action("List")?l=1";
            }
        });

        JsCode.SubmitTip();

        $("#admin-role-save").click(function () {
            var data = { title: "工傷报告新案Form2B通知——中海保險理賠號碼：" + Model.claim_no", content: "@("請查詢系統： <br>意外日期：" + Model.happened_date_str + " <br>申請人：" + Model.user_name + " <br>聯絡電話：" + Model.user_mobile)", attach: "@("PublicFiles/pdf/2B/"+Model.id+".pdf")", email:"@(@CP_NET.Models.Env.CurrentUser.Email)" };
            JsCode.DoRestful("post", "/api/Apply2BApi/SendMail", data, function (res) {
                layer.alert("發送成功");
            });
        });
    </script>
}

<nav class="breadcrumb">
    <i class="Hui-iconfont"></i> 僱員補償條例 <span class="c-gray en">&gt;</span> 僱主呈報引致僱員喪失工作能力不超過三天的意外的通知
    <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a>
</nav>
<article class="page-container">

    <div class="cl pd-5 bg-1 bk-gray mt-20">
        <span>
            <a class="btn btn-primary radius" href="@Url.Action("List")?l=1">返回</a>
        </span>
    </div>

    @using (Html.BeginForm("Edit", "Apply2B", FormMethod.Post, new { @class = "form form-horizontal" }))
    {
        @Html.AntiForgeryToken()
        @Html.HiddenFor(m => m.id)
        @Html.HiddenFor(m => m.user_id)
        @Html.HiddenFor(m => m.dept_id)
        @Html.HiddenFor(m => m.claim_no)
        @Html.HiddenFor(m => m.language, new { @Value = "1" })


        <div class="mt-20">
            <strong class="c-blue">致：勞工處處長</strong>
            <table class="mt-20 table table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td>
                            <strong>謹此聲明，盡本人所知，在本表格內呈報的資料，全屬真實準確。</strong>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <strong>姓名</strong>：
                            @Html.TextBoxFor(m => m.apply_name, new { @class = "input-text", @maxlength = "50" })
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <strong>職位</strong>：
                            @Html.RadioButtonFor(m => m.job_id, ViewBag.list_option_1 as SelectList, Model.job_id.ToString())
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <strong>日期</strong>：
                            @Html.TextBoxFor(m => m.create_time, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM-dd' })", @Value = Model.create_time_str })
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">A. 僱員詳情</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td class="col-xs-4 col-sm-4">
                            <strong>僱員姓名</strong>：<br />
                            @Html.TextBoxFor(m => m.employee_name, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.employee_name)
                        </td>
                        <td class="col-xs-3 col-sm-3">
                            <strong>證件類型</strong>：<br />
                            @Html.DropDownListFor(m => m.employee_id_type, ViewBag.list_option_2 as SelectList, new { @class = "input-text", @style = "width:60%;" })
                        </td>
                        <td class="col-xs-5 col-sm-5">
                            <strong>證件號碼</strong>：<br />
                            @Html.TextBoxFor(m => m.employee_id_number, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.employee_id_number)
                        </td>
                    </tr>
                    <tr>
                        <td class="col-xs-4 col-sm-4">
                            <strong>電話號碼</strong>：
                            @Html.TextBoxFor(m => m.employee_phone, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.employee_phone)
                        </td>
                        <td class="col-xs-8 col-sm-8">
                            <strong>地址</strong>：
                            @Html.TextBoxFor(m => m.employee_address, new { @class = "input-text", @maxlength = "300" })
                            @Html.ValidationMessageFor(m => m.employee_address)
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">B. 僱主詳情</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td class="col-xs-6 col-sm-6">
                            <strong>僱用公司名稱 / 僱主姓名</strong>：
                            @Html.TextBoxFor(m => m.employer_name, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.employer_name)
                        </td>
                        <td class="col-xs-6 col-sm-6">
                            <strong>商業登記證號碼</strong>：
                            @Html.TextBoxFor(m => m.employer_id_number, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.employer_id_number)
                        </td>
                    </tr>
                    <tr>
                        <td class="col-xs-4 col-sm-4">
                            <strong>電話號碼</strong>：
                            @Html.TextBoxFor(m => m.employer_phone, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.employer_phone)
                        </td>
                        <td class="col-xs-8 col-sm-8">
                            <strong>地址</strong>：
                            @Html.TextBoxFor(m => m.employer_address, new { @class = "input-text", @maxlength = "300" })
                            @Html.ValidationMessageFor(m => m.employer_address)
                        </td>
                    </tr>
                    <tr>
                        <td class="col-xs-4 col-sm-4">
                            <strong>傳真</strong>：
                            @Html.TextBoxFor(m => m.employer_fax, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.employer_fax)
                        </td>
                        <td class="col-xs-8 col-sm-8">
                            <strong>行業</strong>：
                            @Html.TextBoxFor(m => m.employer_industry, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.employer_industry)
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">C. 意外詳情</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td class="col-xs-6 col-sm-6">
                            <strong>意外發生日期</strong>：
                            @Html.TextBoxFor(m => m.happened_date, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM-dd' })", @Value = Model.happened_date_str })
                            @Html.ValidationMessageFor(m => m.happened_date)
                        </td>
                        <td class="col-xs-6 col-sm-6">
                            <strong>意外發生地點</strong>：
                            @Html.TextBoxFor(m => m.happened_address, new { @class = "input-text", @maxlength = "300" })
                            @Html.ValidationMessageFor(m => m.happened_address)
                        </td>
                    </tr>
                    <tr>
                        <td class="col-xs-12 col-sm-12">
                            <strong>病假總日數</strong>：
                            @Html.TextBoxFor(m => m.leave_days, new { @class = "input-text", @maxlength = "8" })
                            @Html.ValidationMessageFor(m => m.leave_days)
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">D. 補償詳情</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td class="col-xs-12 col-sm-12">
                            <strong>用作計算受傷僱員應得補償的每月收入</strong>：$
                            @Html.TextBoxFor(m => m.pay_month, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.pay_month)
                        </td>
                    </tr>
                    <tr>
                        <td class="col-xs-4 col-sm-4">
                            <strong>補償額</strong>：$
                            @Html.TextBoxFor(m => m.pay_amount, new { @class = "input-text", @maxlength = "10" })
                            @Html.ValidationMessageFor(m => m.pay_amount)
                        </td>
                        <td class="col-xs-2 col-sm-2">
                            <strong>已支付</strong>：<br />
                            @Html.DropDownListFor(m => m.has_paid,
                           new[] { new SelectListItem() { Text = "是", Value = "true" },
                               new SelectListItem() { Text = "否", Value = "false" } }, new { @class = "input-text", @style = "width:60%;" })
                        </td>
                        <td class="col-xs-6 col-sm-6">
                            <strong>支付日期（已支付則不填）</strong>：
                            @Html.TextBoxFor(m => m.pay_date, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM-dd' })", @Value = Model.pay_date_str })
                            @Html.ValidationMessageFor(m => m.pay_date)
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="row cl">
            <label class="form-label col-xs-2 col-sm-2"></label>
            <div class="formControls col-xs-10 col-sm-10">
                @Html.ValidationMessage("error")
                @Html.ValidationMessage("ok", new { @style = "display:none;", @class = "valid-ok" })
                @Html.ValidationMessage("url", new { @style = "display:none;", @class = "valid-tourl" })
            </div>
        </div>

        <div class="mt-20">
            <button id="admin-role-save" name="admin-role-save" class="btn btn-primary radius" type="submit">
                <i class="Hui-iconfont">&#xe632;</i> 确定
            </button>
            &nbsp;&nbsp;
            <a href="@Url.Action("List")?l=1" class="btn btn-default radius">&nbsp;&nbsp;取消&nbsp;&nbsp;</a>
        </div>
    }
</article>