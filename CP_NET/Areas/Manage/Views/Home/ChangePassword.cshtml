@model Models.ChangePasswordModel
@{
    ViewBag.Title = "";
}

@section Footer{
    <script>
        JsCode.SubmitTip();

        $(function () {
            var error = $(".valid-error").html();
            if (error != null && error != "") {
                layer.msg(error);
            }
        });
    </script>
}

<article class="page-container">
    @using (Html.BeginForm("ChangePassword", "Home", FormMethod.Post, new { @class = "form form-horizontal" }))
    {
        @Html.AntiForgeryToken()

        <div class="row cl">
            <label class="form-label col-xs-4 col-sm-3"><span class="c-red">*</span>旧密码：</label>
            <div class="formControls col-xs-8 col-sm-9">
                @Html.PasswordFor(m => m.OldPassword, new { @class = "input-text" })
                @Html.ValidationMessageFor(m => m.OldPassword)
            </div>
        </div>

        <div class="row cl">
            <label class="form-label col-xs-4 col-sm-3"><span class="c-red">*</span>新密码：</label>
            <div class="formControls col-xs-8 col-sm-9">
                @Html.PasswordFor(m => m.NewPassword, new { @class = "input-text" })
                @Html.ValidationMessageFor(m => m.NewPassword)
            </div>
        </div>

        <div class="row cl">
            <label class="form-label col-xs-4 col-sm-3"><span class="c-red">*</span>确认密码：</label>
            <div class="formControls col-xs-8 col-sm-9">
                @Html.PasswordFor(m => m.ConfirmPassword, new { @class = "input-text" })
                @Html.ValidationMessageFor(m => m.ConfirmPassword)
            </div>
        </div>

        <div class="row cl">
            <div class="formControls col-xs-8 col-sm-9">
                @Html.ValidationMessage("error", new { @style = "display:none;", @class = "valid-error" })
                @Html.ValidationMessage("ok", new { @style = "display:none;", @class = "valid-ok" })
                @Html.ValidationMessage("url", new { @style = "display:none;", @class = "valid-url" })
            </div>
        </div>

        <div class="row cl">
            <div class="col-xs-8 col-sm-9 col-xs-offset-4 col-sm-offset-3">
                <button type="submit" class="btn btn-success radius" id="admin-role-save" name="admin-role-save"><i class="icon-ok"></i>确定</button>
                <button onclick="JsCode.layer_onlyclose();" class="btn btn-default radius" type="button">&nbsp;&nbsp;取消&nbsp;&nbsp;</button>
            </div>
        </div>
    }
</article>