@model Models.EntityDtos.Apply2Dto
@User  Models.EntityDtos

@{
    ViewBag.Title = "";
}

@section Footer{

    <script type="text/javascript" src="/Scripts/lib/My97DatePicker/WdatePicker.js"></script>
    <script>
        $("input[type='text']").attr("readonly", "true");
        $("input[type='radio']").attr("disabled", true);
        $("input[type='checkbox']").attr("disabled", true);

        $("#a_sendmail").click(function () {
            var data = { title: "@("工傷报告新案Form2通知—中海保險理賠號碼：" + Model.claim_no +",ID" + Model.id + ",ClaimReportNo" + Model.claim_report_no)", content: "@("請查詢系統：<br>保险公司：" + Model.tocomp + "： <br>地盤名稱：" + Model.sitename_str + " <br>意外日期：" + Model.happened_date_str + " <br>申請人：" + Model.user_name + " <br>聯絡電話：" + Model.user_mobile+ " <br>中海保險理賠號碼：" + Model.claim_no + " <br>附件名：<br>" + Model.attach_url_upd.Replace("<p style='line - height: 16px;'>","").Replace("<p><br/></p>", "").Replace("</p>", "<br>"))", attach: "@("PublicFiles/pdf/2/"+Model.id+".pdf")", email:"@(@CP_NET.Models.Env.CurrentUser.Email)"};
            JsCode.DoRestful("post", "/api/Form2Api/SendMail", data, function (res) {
                layer.alert("發送成功");
            });
        });
    </script>
}

<nav class="breadcrumb">
    <i class="Hui-iconfont"></i> 僱員補償條例 <span class="c-gray en">&gt;</span> 僱主呈報僱員死亡或引致僱員死亡或喪失工作能力的意外的通知
    <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="刷新"><i class="Hui-iconfont">&#xe68f;</i></a>
</nav>

<article class="page-container">

    <div class="cl pd-5 bg-1 bk-gray mt-20">
        <span>
            <a class="btn btn-primary radius" href="@Url.Action("List")?l=1">返回</a>
            <a class="btn btn-danger radius" href="/PublicFiles/pdf/2/@(Model.id).pdf" target="_blank">列印</a>
            <a class="btn btn-warning radius" href="javascript:;" id="a_sendmail">通知</a>
            @if (CP_NET.Models.Env.CurrentUser.Authority != (byte)Utils.ENUserAuthority.没有修改权限)
            {
                <a class="btn btn-success radius" href="@Url.Action("Edit")/@Model.id?l=1">编辑</a>
            }
        </span>
    </div>

    @using (Html.BeginForm("Detail", "Form2", FormMethod.Post, new { @class = "form form-horizontal" }))
    {
        if (!string.IsNullOrEmpty(Model.attach_url))
        {
<div class="cl pd-5 bg-1 bk-gray mt-20">
    <table>
        <tr>
            <td>
                附件：@Html.Raw(Model.attach_url)
            </td>
            <td>
                附件列表：<br />
                @Html.Raw(Model.attach_url_upd)
            </td>
        </tr>
    </table>
</div>

        }
            <div class="mt-20">

            </div>
            <div class="mt-20">
                <strong class="c-blue">致：勞工處處長</strong>
                <table class="mt-20 table table-bordered table-bg table-hover table-sort dataTable">
                    <tbody>
                        <tr>
                            <td>
                                <strong>所屬保險公司</strong>：
                                @Html.DropDownListFor(m => m.tocomp, new[] {
                                new SelectListItem() { Value = "1", Text = "中國海外保險有限公司 - 理賠部" },
                                new SelectListItem() { Value = "2", Text = "其他" }
                            }, new { @disabled = "disabled", @style = "width:60%;" })
                            </td>
                            <td>
                                <strong>上報日期</strong>：
                                @Model.reportdate_str
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <strong>謹此聲明，盡本人所知，在本表格內呈報的資料，全屬真實準確。</strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <strong>姓名</strong>：
                                @Model.apply_name
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <strong>職位</strong>：

                                @Html.RadioButtonFor(m => m.job_id, ViewBag.list_option_1 as SelectList, Model.job_id.ToString())
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <strong>日期</strong>：
                                @Model.create_time_str
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <strong>地盤名稱</strong>：
                                @Html.DropDownListFor(m => m.sitename, ViewBag.list_option_50 as SelectList, new { @class = "select-box", @disabled = "disabled" })
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-20 text-c f-18">
                <strong>《第Ⅰ部》</strong>
            </div>

            <div class="mt-20">
                <strong class="c-blue">A. 僱員詳情</strong>
                <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                    <tbody>
                        <tr>
                            <td class="col-xs-3 col-sm-3">
                                <strong>僱員姓名</strong>：
                                @*@Html.TextBoxFor(m => m.employee_name, new { @class = "input-text", @maxlength = "100" })*@
                                @Model.employee_name
                            </td>
                            <td class="col-xs-3 col-sm-3">
                                <strong>僱員姓名英文</strong>：
                                @*@Html.TextBoxFor(m => m.employee_name, new { @class = "input-text", @maxlength = "100" })*@
                                @Model.employee_name_eng
                            </td>
                            <td class="col-xs-3 col-sm-3">
                                <strong>證件類型</strong>：
                                @Model.employee_id_type
                                @*@Html.DropDownListFor(m => m.employee_id_type, ViewBag.list_option_2 as SelectList, new { @class = "input-text", @style = "width:60%;" })*@
                            </td>
                            <td class="col-xs-3 col-sm-3">
                                <strong>證件號碼</strong>：
                                @Model.employee_id_number
                                @*@Html.TextBoxFor(m => m.employee_id_number, new { @class = "input-text", @maxlength = "100" })*@
                            </td>
                        </tr>
                        <tr>
                            <td class="col-xs-4 col-sm-4">
                                <strong>電話號碼</strong>：
                                @Model.employee_phone
                                @*@Html.TextBoxFor(m => m.employee_phone, new { @class = "input-text", @maxlength = "100" })*@
                            </td>
                            <td class="col-xs-4 col-sm-4">
                                <strong>傳真號碼</strong>：
                                @Model.employee_fax
                                @*@Html.TextBoxFor(m => m.employee_fax, new { @class = "input-text", @maxlength = "100" })*@
                            </td>
                            <td class="col-xs-4 col-sm-4">
                                <strong>地址</strong>：
                                @Model.employee_address
                                @*@Html.TextBoxFor(m => m.employee_address, new { @class = "input-text", @maxlength = "300" })*@
                            </td>
                        </tr>
                        <tr>
                            <td class="col-xs-3 col-sm-3">
                                <strong>出生日期</strong>：
                                @Model.employee_birthday_str
                                @*@Html.TextBoxFor(m => m.employee_birthday, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM-dd' })" })*@
                            </td>
                            <td class="col-xs-3 col-sm-3">
                                <strong>性別</strong>：<br />
                                @Html.DropDownListFor(m => m.employee_gender, new[] {
                                    new SelectListItem() { Value = "1", Text = "男" },
                                    new SelectListItem() { Value = "2", Text = "女" }
                                }, new { @disabled = "disabled", @style = "width:60%;" })
                            </td>
                            <td class="col-xs-3 col-sm-3">
                                <strong>職業</strong>：
                                @Model.employee_job
                                @*@Html.TextBoxFor(m => m.employee_job, new { @class = "input-text", @maxlength = "100" })*@
                            </td>
                            <td class="col-xs-3 col-sm-3">
                                <strong>學徒</strong>：<br />
                                @Html.DropDownListFor(m => m.employee_is_trainee, new[] {
                                    new SelectListItem() { Value = "True", Text = "是" },
                                    new SelectListItem() { Value = "False", Text = "否" }
                                }, new { @disabled = "disabled", @style = "width:60%;" })
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-20">
                <strong class="c-blue">B. 僱主詳情</strong>
                <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                    <tbody>
                        <tr>
                            <td class="col-xs-6 col-sm-6">
                                <strong>僱用公司名稱 / 僱主姓名</strong>：
                                @Model.employer_name
                                @*@Model.group_id*@
                                @Html.DropDownListFor(m => m.group_id, ViewBag.list_option_51 as SelectList, new { @class = "select-box", @disabled = "disabled" })
                                @*@Html.TextBoxFor(m => m.employer_name, new { @class = "input-text", @maxlength = "100" })*@
                            </td>
                            <td class="col-xs-6 col-sm-6">
                                <strong>商業登記證號碼</strong>：
                                @Model.employer_id_number
                                @*@Html.TextBoxFor(m => m.employer_id_number, new { @class = "input-text", @maxlength = "100" })*@
                            </td>
                        </tr>
                        <tr>
                            <td class="col-xs-4 col-sm-4">
                                <strong>電話號碼</strong>：
                                @Model.employer_phone
                                @*@Html.TextBoxFor(m => m.employer_phone, new { @class = "input-text", @maxlength = "100" })*@
                            </td>
                            <td class="col-xs-8 col-sm-8">
                                <strong>地址</strong>：
                                @Model.employer_address
                                @*@Html.TextBoxFor(m => m.employer_address, new { @class = "input-text", @maxlength = "300" })*@
                            </td>
                        </tr>
                        <tr>
                            <td class="col-xs-4 col-sm-4">
                                <strong>傳真</strong>：
                                @Model.employer_fax
                                @*@Html.TextBoxFor(m => m.employer_fax, new { @class = "input-text", @maxlength = "100" })*@
                            </td>
                            <td class="col-xs-8 col-sm-8">
                                <strong>行業</strong>：
                                @Model.employer_industry
                                @*@Html.TextBoxFor(m => m.employer_industry, new { @class = "input-text", @maxlength = "100" })*@
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-20">
                <strong class="c-blue">C. 總承判商/控股公司詳情</strong>
                <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                    <tbody>
                        <tr>
                            <td class="col-xs-6 col-sm-6">
                                <strong>總承判商/控股公司名稱</strong>：
                                @Model.company_name
                                @*@Html.TextBoxFor(m => m.company_name, new { @class = "input-text", @maxlength = "100" })*@
                            </td>
                            <td class="col-xs-6 col-sm-6">
                                <strong>商業登記證號碼</strong>：
                                @Model.company_id_number
                                @*@Html.TextBoxFor(m => m.company_id_number, new { @class = "input-text", @maxlength = "100" })*@
                            </td>
                        </tr>
                        <tr>
                            <td class="col-xs-4 col-sm-4">
                                <strong>電話號碼</strong>：
                                @Model.company_phone
                                @*@Html.TextBoxFor(m => m.company_phone, new { @class = "input-text", @maxlength = "100" })*@
                            </td>
                            <td class="col-xs-8 col-sm-8">
                                <strong>地址</strong>：
                                @Model.company_address
                                @*@Html.TextBoxFor(m => m.company_address, new { @class = "input-text", @maxlength = "300" })*@
                            </td>
                        </tr>
                        <tr>
                            <td class="col-xs-4 col-sm-4">
                                <strong>傳真</strong>：
                                @Model.company_fax
                                @*@Html.TextBoxFor(m => m.company_fax, new { @class = "input-text", @maxlength = "100" })*@
                            </td>
                            <td class="col-xs-8 col-sm-8">
                                <strong>行業</strong>：
                                @Model.company_industry
                                @*@Html.TextBoxFor(m => m.company_industry, new { @class = "input-text", @maxlength = "100" })*@
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-20">
                <strong class="c-blue">D. 意外的敘述</strong>
                <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                    <tbody>
                        <tr>
                            <td class="col-xs-12 col-sm-12">
                                <strong>請敘述意外如何發生，並說明僱員當時正在進行的工作</strong>：
                                @Model.accident_desc
                                @*@Html.TextBoxFor(m => m.accident_desc, new { @class = "input-text", @style = "width:100%;" })*@
                            </td>
                        </tr>
                        <tr>
                            <td class="col-xs-4 col-sm-4">
                                <strong>述明意外是否于工作期間發生</strong>：<br />
                                @Html.DropDownListFor(m => m.happened_on_work, new[] {
                                    new SelectListItem() { Value = "True", Text = "是" },
                                    new SelectListItem() { Value = "False", Text = "否" }
                                }, new { @disabled = "disabled", @style = "width:60%;" })
                            </td>
                            <td class="col-xs-4 col-sm-4">
                                <strong>意外發生日期</strong>：
                                @Model.happened_date_str
                                @*@Html.TextBoxFor(m => m.happened_date, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM-dd HH:mm' })", @Value = Model.happened_date_str })*@
                            </td>
                            @*<td class="col-xs-6 col-sm-6">
                                    <strong>意外發生時間</strong>：
                                    @Model.happened_time
                                    @Html.TextBoxFor(m => m.happened_time, new { @class = "input-text", @maxlength = "100" })
                                </td>*@
                            <td class="col-xs-4 col-sm-4">
                                <strong>意外結果</strong>：<br />
                                @Model.accident_result
                                @*@Html.DropDownListFor(m => m.accident_result, ViewBag.list_option_3 as SelectList, new { @class = "input-text", @style = "width:60%;" })*@
                            </td>
                        </tr>
                        <tr>
                            <td class="col-xs-6 col-sm-6">
                                <strong>意外發生地點</strong>：
                                @Model.happened_address
                                @*@Html.TextBoxFor(m => m.happened_address, new { @class = "input-text", @maxlength = "300" })*@
                            </td>
                            <td class="col-xs-6 col-sm-6">
                                <strong>僱員接受治療的醫院/診所名稱</strong>：
                                @Model.hospital_name
                                @*@Html.TextBoxFor(m => m.hospital_name, new { @class = "input-text", @maxlength = "300" })*@
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-20">
                <strong class="c-blue">E. 保險的細節</strong>
                <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                    <tbody>
                        <tr>
                            <td class="col-xs-8 col-sm-8">
                                <strong>意外發生時，承保的保險公司名稱及地址（請參照保險單）</strong>：<br />
                                @Model.insurance_detail
                                @*@Html.TextBoxFor(m => m.insurance_detail, new { @class = "input-text", @maxlength = "200" })*@
                            </td>
                            <td class="col-xs-4 col-sm-4">
                                <strong>保險單號碼</strong>：<br />
                                @Model.insurance_number
                                @*@Html.TextBoxFor(m => m.insurance_number, new { @class = "input-text", @maxlength = "100" })*@
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-20">
                <strong class="c-blue">F. 僱員收入細節</strong>
                <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                    <tbody>
                        <tr>
                            <td class="col-xs-6 col-sm-6">
                                <strong>每月平均工作日數</strong>：<br />
                                @Html.RadioButtonFor(m => m.work_days_id, ViewBag.list_option_4 as SelectList, Model.work_days_id.ToString())
                                @Html.TextBoxFor(m => m.other_days, new { @class = "input-text", @maxlength = "2", @style = "width:50%;", @placeholder = "請指明" })
                            </td>
                            <td class="col-xs-6 col-sm-6">
                                <strong>休息日</strong>：<br />
                                (a) @Html.RadioButtonFor(m => m.is_paid_at_rest, new[] {
            new SelectListItem() { Value = "False", Text = "無薪" },
            new SelectListItem() { Value = "True", Text = "有薪" }
        }, Model.is_paid_at_rest.ToString())

                                (b) @Html.RadioButtonFor(m => m.is_regular_at_rest, new[] {
            new SelectListItem() { Value = "False", Text = "非固定" },
            new SelectListItem() { Value = "True", Text = "固定于星期" }
        }, Model.is_regular_at_rest.ToString())
                                @Html.TextBoxFor(m => m.regular_at_day, new { @class = "input-text", @maxlength = "2", @style = "width:10%;", @placeholder = "請填写星期的那一天" })
                            </td>
                        </tr>
                        <tr>
                            <td class="col-xs-12 col-sm-12">
                                僱員在緊接意外發生日期的上一個月的每月收入細節：<br />
                                <div class="row cl">
                                    <div class="col-xs-2 col-sm-2">
                                        (a) 底薪/基本工資：
                                    </div>
                                    <div class="col-xs-6 col-sm-6">
                                        每月$ @Model.basic_salary
                                        @*@Html.TextBoxFor(m => m.basic_salary, new { @class = "input-text", @maxlength = "10", @style = "width:50%;" })*@
                                    </div>
                                </div>
                                <div class="row cl">
                                    <div class="col-xs-2 col-sm-2">
                                        (b) 伙食津貼/僱主免費供應食物的價值：
                                    </div>
                                    <div class="col-xs-6 col-sm-6">
                                        每月$ @Model.allowance
                                        @*@Html.TextBoxFor(m => m.allowance, new { @class = "input-text", @maxlength = "10", @style = "width:50%;" })*@
                                    </div>
                                </div>
                                <div class="row cl">
                                    <div class="col-xs-2 col-sm-2">
                                        (c) 其他項目：@Model.other_income_name
                                        @*  @Html.TextBoxFor(m => m.other_income_name, new { @class = "input-text", @maxlength = "50", @style = "width:50%;", @placeholder = "請指明" })*@
                                    </div>
                                    <div class="col-xs-6 col-sm-6">
                                        每月$ @Model.other_income
                                        @* @Html.TextBoxFor(m => m.other_income, new { @class = "input-text", @maxlength = "10", @style = "width:50%;" })*@
                                    </div>
                                </div>
                                <div class="row cl">
                                    <div class="col-xs-2 col-sm-2">總收入（a) + (b) +（c)：</div>
                                    <div class="col-xs-6 col-sm-6">
                                        每月$ @Model.total_salary_monthly
                                        @* @Html.TextBoxFor(m => m.total_salary_monthly, new { @class = "input-text", @maxlength = "10", @style = "width:50%;" })*@
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-xs-12 col-sm-12">
                                僱員在意外發生前12個月內（如不足12個月，則以整段受傷期間計）的每月平均收入細為：<br />
                                <div class="row cl">
                                    <div class="col-xs-2 col-sm-2">
                                    </div>
                                    <div class="col-xs-6 col-sm-6">
                                        每月$ @Model.average_salary_monthly
                                        @*@Html.TextBoxFor(m => m.average_salary_monthly, new { @class = "input-text", @maxlength = "10", @style = "width:50%;" })*@
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-20">
                <strong class="c-blue">G. 死亡個案（只須於意外引致死亡時填寫）</strong>
                <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                    <tbody>
                        <tr>
                            <td class="col-xs-3 col-sm-3">
                                <strong>是否已報警</strong>：<br />
                                @Html.RadioButtonFor(m => m.has_called_police, new[] {
                                    new SelectListItem() { Value = "False", Text = "否" },
                                    new SelectListItem() { Value = "True", Text = "是" }
                                }, Model.has_called_police.ToString())
                                @Html.TextBoxFor(m => m.police_station, new { @class = "input-text", @maxlength = "50", @placeholder = "警署名稱", @style = "width:50%" })
                            </td>
                            <td class="col-xs-4 col-sm-4">
                                <strong>已故僱員的最近親姓名及地址</strong>：
                                @Model.kin_detail
                                @* @Html.TextBoxFor(m => m.kin_detail, new { @class = "input-text", @maxlength = "200" })*@
                            </td>
                            <td class="col-xs-3 col-sm-3">
                                <strong>與已故僱員的關係</strong>：<br />
                                @Model.kin_relationship
                                @*@Html.TextBoxFor(m => m.kin_relationship, new { @class = "input-text", @maxlength = "50" })*@
                            </td>
                            <td class="col-xs-2 col-sm-2">
                                <strong>電話號碼</strong>：<br />
                                @Model.kin_phone
                                @* @Html.TextBoxFor(m => m.kin_phone, new { @class = "input-text", @maxlength = "100" })*@
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-20">
                <strong class="c-blue">H. 直接和解（只在損傷引致暫時喪失工作能力為期不多於7天及並無引致永久喪失工作能力，而且僱主和僱員已選擇以直接和解方式來解決工傷個案時，始須填寫。）</strong>
                <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                    <tbody>
                        <tr>
                            <td class="col-xs-6 col-sm-6">
                                <strong>病假期</strong>：<br />
                                由 @Model.leave_from_1
                                @* @Html.TextBoxFor(m => m.leave_from_1, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM-dd' })", @style = "width:20%", @Value = Model.leave_from_1_str })*@
                                至  @Model.leave_end_1
                                @* @Html.TextBoxFor(m => m.leave_end_1, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM-dd' })", @style = "width:20%", @Value = Model.leave_end_1_str })*@
                                <br /><br />
                                由 @Model.leave_from_2
                                @*@Html.TextBoxFor(m => m.leave_from_2, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM-dd' })", @style = "width:20%", @Value = Model.leave_from_2_str })*@
                                至 @Model.leave_end_2
                                @*@Html.TextBoxFor(m => m.leave_end_2, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM-dd' })", @style = "width:20%", @Value = Model.leave_end_2_str })*@
                                病假總日數：@Model.leave_days
                                @*@Html.TextBoxFor(m => m.leave_days, new { @class = "input-text", @maxlength = "5", @style = "width:20%" })*@
                            </td>
                            <td class="col-xs-6 col-sm-6">
                                <strong>補償額</strong>：<br />
                                $ @Model.pay_amount
                                @*@Html.TextBoxFor(m => m.pay_amount, new { @class = "input-text", @maxlength = "10", @style = "width:60%" })*@
                                <br /><br />
                                @Html.RadioButtonFor(m => m.has_paid, new[] {
                                    new SelectListItem() { Value = "True", Text = "已支付" },
                                    new SelectListItem() { Value = "False", Text = "將於" }
                                }, Model.has_paid.ToString())
                                @Model.pay_date
                                @* @Html.TextBoxFor(m => m.pay_date, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM-dd' })", @style = "width:30%", @Value = Model.pay_date_str }) 支付*@
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-20">
                <strong class="c-blue">I. 意外地點</strong>
                <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                    <tbody>
                        <tr>
                            <td class="col-xs-12 col-sm-12">
                                <strong>這意外發生於</strong>：<br /><br />
                                <div class="col-xs-3 col-sm-3">
                                    <u>建築地盤</u><br />
                                    @Html.RadioButtonFor(m => m.happened_location_id, ViewBag.list_option_5 as SelectList, Model.happened_location_id.ToString(), false)
                                </div>
                                <div class="col-xs-3 col-sm-3">
                                    <u>船廠</u><br />
                                    @Html.RadioButtonFor(m => m.happened_location_id, ViewBag.list_option_6 as SelectList, Model.happened_location_id.ToString(), false)
                                </div>
                                <div class="col-xs-3 col-sm-3">
                                    <u>製造廠</u><br />
                                    @Html.RadioButtonFor(m => m.happened_location_id, ViewBag.list_option_7 as SelectList, Model.happened_location_id.ToString(), false)
                                </div>
                                <div class="col-xs-3 col-sm-3">
                                    <u>其他</u><br />
                                    @Html.RadioButtonFor(m => m.happened_location_id, ViewBag.list_option_8 as SelectList, Model.happened_location_id.ToString(), false)
                                    @Model.other_happened_location
                                    @*@Html.TextBoxFor(m => m.other_happened_location, new { @class = "input-text", @maxlength = "50" })*@
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-xs-12 col-sm-12">
                                在意外發生時現場進行的活動：<br />
                                @Model.happened_activity
                                @*@Html.TextBoxFor(m => m.happened_activity, new { @class = "input-text", @maxlength = "300" })*@
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-20">
                <strong class="c-blue">J. 損傷性質</strong>
                <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                    <tbody>
                        <tr>
                            <td class="col-xs-12 col-sm-12">
                                <strong>敘述損傷性質</strong><br />
                                @Model.injure_desc
                                @* @Html.TextBoxFor(m => m.injure_desc, new { @class = "input-text", @maxlength = "300" })*@
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <strong>指出損傷性質</strong>：<br />
                                @{int i_9 = 1; }
                                @foreach (var i in ViewBag.list_option_9)
                                {
                                    if (i_9 % 5 == 1)
                                    {
                                        @Html.Raw("<div class=\"col-xs-3 col-sm-3\">");
                                    }
                                    <div>
                                        @if (Model.injure_id_list.Contains(i.id.ToString()))
                                        {
                                            <input type='checkbox' value='@(i.id)'
                                                   name='_injure_id' id='injure_id@(i_9)'
                                                   checked="checked" />
                                        }
                                        else
                                        {
                                            <input type='checkbox' value='@(i.id)'
                                                   name='_injure_id' id='injure_id@(i_9)' />
                                        }
                                        <label for='injure_id@(i_9)' style='margin-right:10px;'>@i.name</label>
                                    </div>
                                    if (i_9 % 5 == 0)
                                    {
                                        if (i_9 == 20)
                                        {@Html.TextBoxFor(m => m.other_injure, new { @class = "input-text", @maxlength = "50" }) }
                                    @Html.Raw("</div>");
                                }
                                i_9++;
                            }
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <strong>身體的損傷部位</strong>：<br /><br />
                                <div class="col-xs-2 col-sm-2">
                                    <u>頭部</u><br />
                                    @Html.CheckBoxFor(m => m._injure_part_id, ViewBag.list_option_10 as SelectList, Model.injure_part_id, false)
                                </div>
                                <div class="col-xs-2 col-sm-2">
                                    <u>頸部及軀幹</u><br />
                                    @Html.CheckBoxFor(m => m._injure_part_id, ViewBag.list_option_11 as SelectList, Model.injure_part_id, false)
                                </div>
                                <div class="col-xs-2 col-sm-2">
                                    <u>上肢</u><br />
                                    @Html.CheckBoxFor(m => m._injure_part_id, ViewBag.list_option_12 as SelectList, Model.injure_part_id, false)
                                </div>
                                <div class="col-xs-2 col-sm-2">
                                    <u>下肢</u><br />
                                    @Html.CheckBoxFor(m => m._injure_part_id, ViewBag.list_option_13 as SelectList, Model.injure_part_id, false)
                                </div>
                                <div class="col-xs-2 col-sm-2">
                                    @Html.CheckBoxFor(m => m._injure_part_id, ViewBag.list_option_14 as SelectList, Model.injure_part_id, false)
                                    @Html.TextBoxFor(m => m.other_part, new { @class = "input-text", @maxlength = "50", @placeholder = "請指明" })
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-20">
                <strong class="c-blue">K. 意外類別</strong>
                <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                    <tbody>
                        <tr>
                            <td>
                                <div class="col-xs-3 col-sm-3">
                                    @Html.RadioButtonFor(m => m.accident_type_id, ViewBag.list_option_15 as SelectList, Model.accident_type_id.ToString(), false)
                                    @Html.TextBoxFor(m => m.accident_height, new { @class = "input-text", @maxlength = "6", @placeholder = "人體墜下的距離", @style = "width:40%" }) 米
                                </div>
                                <div class="col-xs-3 col-sm-3">
                                    @Html.RadioButtonFor(m => m.accident_type_id, ViewBag.list_option_16 as SelectList, Model.accident_type_id.ToString(), false)
                                </div>
                                <div class="col-xs-3 col-sm-3">
                                    @Html.RadioButtonFor(m => m.accident_type_id, ViewBag.list_option_17 as SelectList, Model.accident_type_id.ToString(), false)
                                </div>
                                <div class="col-xs-3 col-sm-3">
                                    @Html.RadioButtonFor(m => m.accident_type_id, ViewBag.list_option_18 as SelectList, Model.accident_type_id.ToString(), false)
                                    @Html.TextBoxFor(m => m.other_type, new { @class = "input-text", @maxlength = "50", @placeholder = "請指明", @style = "width:60%" })
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-20">
                <strong class="c-blue">L. 引致受傷的媒介（如有的話，在一個或多個選項打“√”）</strong>
                <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                    <tbody>
                        <tr>
                            <td>
                                <div class="col-xs-3 col-sm-3">
                                    @Html.CheckBoxFor(m => m.agent_id, ViewBag.list_option_19 as SelectList, Model.agent_ids, false)
                                    @Html.TextBoxFor(m => m.other_agent_type, new { @class = "input-text", @maxlength = "20", @placeholder = "請指明機器類別", @style = "width:60%" })
                                    <br />令僱員受傷的機器部分：
                                    @Html.CheckBoxFor(m => m.agent_part, ViewBag.list_option_23 as SelectList, Model.agent_parts, false)
                                </div>
                                <div class="col-xs-3 col-sm-3">
                                    @Html.CheckBoxFor(m => m.agent_id, ViewBag.list_option_20 as SelectList, Model.agent_ids, false)
                                </div>
                                <div class="col-xs-3 col-sm-3">
                                    @Html.CheckBoxFor(m => m.agent_id, ViewBag.list_option_21 as SelectList, Model.agent_ids, false)
                                </div>
                                <div class="col-xs-3 col-sm-3">
                                    @Html.CheckBoxFor(m => m.agent_id, ViewBag.list_option_22 as SelectList, Model.agent_ids, false)
                                    @Html.TextBoxFor(m => m.other_agent, new { @class = "input-text", @maxlength = "50", @placeholder = "請指明", @style = "width:60%" })
                                </div>

                                @Html.HiddenFor(m => m.agent_ids)
                                @Html.HiddenFor(m => m.agent_parts)
                            </td>
                        </tr>
                        <tr>
                            <td class="col-xs-12 col-sm-12">
                                <strong>簡述你如上所指的媒介</strong><br />
                                @Model.agent_desc
                                @*@Html.TextBoxFor(m => m.agent_desc, new { @class = "input-text", @maxlength = "300" })*@
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-20">
                <strong class="c-blue">M. 草圖（如認為需要補充以上敘述不足之處，PDF列印后補充）</strong>
            </div>

            <div class="mt-20 mb-20 text-c f-18">
                <strong>《第Ⅰ部完》</strong>
            </div>

            <hr />

            <div class="mt-20 text-c f-18">
                <strong>《第Ⅱ部》</strong><br />
                <strong>（如意外發生在建築地盤內則須填寫此部）</strong>
            </div>

            <div class="mt-20">
                <strong class="c-blue">N. 在意外發生時僱員所進行的工作類別</strong>
                <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                    <tbody>
                        <tr>
                            <td>
                                @{int i_24 = 1; }
                                @foreach (var i in ViewBag.list_option_24)
                                {
                                    if (i_24 % 6 == 1)
                                    {
                                        @Html.Raw("<div class=\"col-xs-3 col-sm-3\">");
                                    }
                                    <div>
                                        @if (Model.work_type_id == i.id)
                                        {
                                            <input type='Radio' value='@(i.id)'
                                                   name='work_type_id' id='work_type_id@(i_24)'
                                                   checked="checked" />
                                        }
                                        else
                                        {
                                            <input type='Radio' value='@(i.id)'
                                                   name='work_type_id' id='work_type_id@(i_24)' />
                                        }
                                        <label for='work_type_id@(i_24)' style='margin-right:10px;'>@i.name</label>
                                    </div>
                                    if (i_24 % 6 == 0 || i_24 == 20)
                                    {
                                        if (i_24 == 20)
                                        {@Html.TextBoxFor(m => m.other_work_type, new { @class = "input-text", @maxlength = "20" }) }
                                    @Html.Raw("</div>");
                                }
                                i_24++;
                            }
                            </td>
                        </tr>
                        <tr>
                            <td class="col-xs-12 col-sm-12">
                                <strong>上述工作在建築地盤內何處進行</strong><br />
                                @Html.TextBoxFor(m => m.work_site, new { @class = "input-text", @maxlength = "300" })
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-20">
                <strong class="c-blue">O. 涉及的機器（如有的話，在一個或多個選項打“√”）</strong>
                <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                    <tbody>
                        <tr>
                            <td>
                                <div class="col-xs-4 col-sm-4">
                                    @Html.CheckBoxFor(m => m.machine_id, ViewBag.list_option_25 as SelectList, Model.machine_ids, false)
                                </div>
                                <div class="col-xs-4 col-sm-4">
                                    @Html.CheckBoxFor(m => m.machine_id, ViewBag.list_option_26 as SelectList, Model.machine_ids, false)
                                </div>
                                <div class="col-xs-4 col-sm-4">
                                    @Html.CheckBoxFor(m => m.machine_id, ViewBag.list_option_27 as SelectList, Model.machine_ids, false)
                                    @Html.TextBoxFor(m => m.other_machine, new { @class = "input-text", @maxlength = "50", @placeholder = "請指明", @style = "width:60%" })
                                </div>

                                @Html.HiddenFor(m => m.machine_ids)
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-20">
                <strong class="c-blue">P. 涉及的運輸機器或建築機器（如有的話）</strong>
                <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                    <tbody>
                        <tr>
                            <td>
                                @{int i_28 = 1; }
                                @foreach (var i in ViewBag.list_option_28)
                                {
                                    if (i_28 % 3 == 1)
                                    {
                                        @Html.Raw("<div class=\"col-xs-4 col-sm-4\">");
                                    }
                                    <div>
                                        @if (Model.transport_id == i.id)
                                        {
                                            <input type='Radio' value='@(i.id)'
                                                   name='transport_id' id='transport_id@(i_28)'
                                                   checked="checked" />
                                        }
                                        else
                                        {
                                            <input type='Radio' value='@(i.id)'
                                                   name='transport_id' id='transport_id@(i_28)' />
                                        }
                                        <label for='transport_id@(i_28)' style='margin-right:10px;'>@i.name</label>
                                    </div>
                                    if (i_28 % 3 == 0 || i_28 == 7)
                                    {
                                        if (i_28 == 7)
                                        {@Html.TextBoxFor(m => m.other_transport, new { @class = "input-text", @maxlength = "20" }) }
                                    @Html.Raw("</div>");
                                }
                                i_28++;
                            }
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-20 mb-20 text-c f-18">
                <strong>《第Ⅱ部完》</strong>
            </div>

            <hr />

            <div class="mt-20 text-c f-18">
                <strong>建築地盤意外之附加資料</strong><br />
            </div>

            <div class="mt-20">
                <strong>注釋：本表格<u>並非</u>《僱員補償條例》內作為呈報意外的法定表格，但仍請僱主合作，就發生在建築地盤內的意外填寫本表格的第1至第5部份。填報的資料會被政府及相關的公營機構作為分析意外之用。</strong>
            </div>

            <div class="mt-20">
                <strong class="c-blue">Ⅰ. 工地細節</strong>
                <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                    <tbody>
                        <tr>
                            <td class="col-xs-4 col-sm-4">
                                <strong>建築工程開始施工日期</strong>：
                                @Model.construction_date
                                @*@Html.TextBoxFor(m => m.construction_date, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM' })", @Value = Model.construction_date_str })*@
                            </td>
                            <td class="col-xs-4 col-sm-4">
                                <strong>預計完工日期</strong>：
                                @Model.expired_date
                                @*@Html.TextBoxFor(m => m.expired_date, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM' })", @Value = Model.expired_date_str })*@
                            </td>
                            <td class="col-xs-4 col-sm-4">
                                <strong>意外發生日期</strong>：
                                @Model.accident_date_str
                                @*@Html.TextBoxFor(m => m.accident_date, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM-dd' })", @Value = Model.accident_date_str })*@
                            </td>
                        </tr>
                        <tr>
                            <td class="col-xs-4 col-sm-4">
                                <strong>判承商名稱</strong>：
                                @Model.constractor_name
                                @* @Html.TextBoxFor(m => m.constractor_name, new { @class = "input-text", @maxlength = "100" })*@
                            </td>
                            <td class="col-xs-4 col-sm-4">
                                <strong>合約號碼（如有的話）</strong>：
                                @Model.contract_no
                                @* @Html.TextBoxFor(m => m.contract_no, new { @class = "input-text", @maxlength = "100" })*@
                            </td>
                            <td class="col-xs-4 col-sm-4">
                                <strong>聯絡電話</strong>：
                                @Model.contact_phone
                                @* @Html.TextBoxFor(m => m.contact_phone, new { @class = "input-text", @maxlength = "100" })*@
                            </td>
                        </tr>
                        <tr>
                            <td class="col-xs-12 col-sm-12">
                                <strong>地盤地址</strong>：
                                @Model.site_address
                                @* @Html.TextBoxFor(m => m.site_address, new { @class = "input-text", @maxlength = "300" })*@
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-20">
                <strong class="c-blue">Ⅱ. 工程细节</strong>
                <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                    <tbody>
                        <tr>
                            <td>
                                <strong>（A）工程性质</strong>：
                                @Html.RadioButtonFor(m => m.natrue_id, ViewBag.list_option_29 as SelectList, Model.natrue_id.ToString())
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <strong>（B）私人工程</strong>：
                                @Html.DropDownListFor(m => m.is_private, new[] {
                                    new SelectListItem() { Value = "", Text = "请选择" },
                                    new SelectListItem() { Value = "True", Text = "是" },
                                    new SelectListItem() { Value = "False", Text = "否" }
                                }, new { @disabled = "@disabled", @style = "width:20%;", @onchange = "ShowProject();" }) &nbsp;
                                若果是的话，請填寫授權人士或工程經理的名字及聯絡電話。若果否的話，請在下欄指出該公營工程/政府工程的類別

                                <div id="div_private_info">
                                    <div class="col-xs-4 col-sm-4">
                                        <strong>姓名</strong>：
                                        @Model.private_name
                                        @*@Html.TextBoxFor(m => m.private_name, new { @class = "input-text", @maxlength = "100" })*@
                                    </div>
                                    <div class="col-xs-4 col-sm-4">
                                        <strong>職位</strong>：
                                        @Model.private_job
                                        @* @Html.TextBoxFor(m => m.private_job, new { @class = "input-text", @maxlength = "50" })*@
                                    </div>
                                    <div class="col-xs-4 col-sm-4">
                                        <strong>電話</strong>：
                                        @Model.private_phone
                                        @* @Html.TextBoxFor(m => m.private_phone, new { @class = "input-text", @maxlength = "100" })*@
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <strong>（C）公營工程或政府工程</strong>：<br /><br />
                                @{int i_30 = 1; }
                                @foreach (var i in ViewBag.list_option_30)
                                {
                                    if (i_30 % 5 == 1)
                                    {
                                        @Html.Raw("<div class=\"col-xs-4 col-sm-4\">");
                                    }
                                    <div>
                                        @if (Model.project_id == i.id)
                                        {
                                            <input type='Radio' value='@(i.id)'
                                                   name='project_id' id='project_id@(i_30)'
                                                   checked="checked" />
                                        }
                                        else
                                        {
                                            <input type='Radio' value='@(i.id)'
                                                   name='project_id' id='project_id@(i_30)' />
                                        }
                                        <label for='project_id@(i_30)' style='margin-right:10px;'>@i.name</label>
                                    </div>
                                    if (i_30 % 5 == 0 || i_30 == 18)
                                    {
                                        if (i_30 == 18)
                                        {@Html.TextBoxFor(m => m.other_project, new { @class = "input-text", @maxlength = "20" }) }
                                    @Html.Raw("</div>");
                                }
                                i_30++;
                            }
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-20">
                <strong class="c-blue">Ⅲ. 墜下地點細節（如從高處墜下受傷）</strong>
                <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                    <tbody>
                        <tr>
                            <td>
                                @{int i_31 = 1; }
                                @foreach (var i in ViewBag.list_option_31)
                                {
                                    if (i_31 % 3 == 1)
                                    {
                                        @Html.Raw("<div class=\"col-xs-4 col-sm-4\">");
                                    }
                                    <div>
                                        @if (Model.place_id == i.id)
                                        {
                                            <input type='Radio' value='@(i.id)'
                                                   name='place_id' id='place_id@(i_31)'
                                                   checked="checked" />
                                        }
                                        else
                                        {
                                            <input type='Radio' value='@(i.id)'
                                                   name='place_id' id='place_id@(i_31)' />
                                        }
                                        <label for='place_id@(i_31)' style='margin-right:10px;'>@i.name</label>
                                    </div>
                                    if (i_31 % 3 == 0 || i_31 == 8)
                                    {
                                        if (i_31 == 8)
                                        {@Html.TextBoxFor(m => m.other_place, new { @class = "input-text", @maxlength = "20" }) }
                                    @Html.Raw("</div>");
                                }
                                i_31++;
                            }
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-20">
                <strong class="c-blue">Ⅳ. 種族</strong>
                <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                    <tbody>
                        <tr>
                            <td>
                                @{int i_32 = 1; }
                                @foreach (var i in ViewBag.list_option_32)
                                {
                                    if (i_32 % 3 == 1)
                                    {
                                        @Html.Raw("<div class=\"col-xs-3 col-sm-3\">");
                                    }
                                    <div>
                                        @if (Model.ethnicity_id == i.id)
                                        {
                                            <input type='Radio' value='@(i.id)'
                                                   name='ethnicity_id' id='ethnicity_id@(i_32)'
                                                   checked="checked" />
                                        }
                                        else
                                        {
                                            <input type='Radio' value='@(i.id)'
                                                   name='ethnicity_id' id='ethnicity_id@(i_32)' />
                                        }
                                        <label for='ethnicity_id@(i_32)' style='margin-right:10px;'>@i.name</label>
                                    </div>
                                    if (i_32 % 3 == 0 || i_32 == 11)
                                    {
                                        if (i_32 == 11)
                                        {@Html.TextBoxFor(m => m.other_ethnicity, new { @class = "input-text", @maxlength = "20" }) }
                                    @Html.Raw("</div>");
                                }
                                i_32++;
                            }
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-20">
                <strong class="c-blue">Ⅴ. 語文能力</strong>
                <table class="mt-20 table table-border table-bordered table-bg table-hover">
                    <tbody>
                        <tr>
                            <td class="col-xs-4 col-sm-4">
                                <table class="table table-border table-bordered table-bg table-hover">
                                    <tbody>
                                        <tr>
                                            <td class="col-xs-12 col-sm-12 text-c">
                                                會話
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="col-xs-3 col-sm-3 text-c">
                                                廣東話
                                            </td>
                                            <td class="col-xs-9 col-sm-9">
                                                @Html.RadioButtonFor(m => m.spoken_ability_cantonese, ViewBag.list_option_33 as SelectList, Model.spoken_ability_cantonese.ToString())
                                            </td>
                                            <td class="col-xs-3 col-sm-3 text-c">
                                                普通話
                                            </td>
                                            <td class="col-xs-9 col-sm-9">
                                                @Html.RadioButtonFor(m => m.spoken_ability_putonghua, ViewBag.list_option_33 as SelectList, Model.spoken_ability_putonghua.ToString())
                                            </td>
                                            <td class="col-xs-3 col-sm-3 text-c">
                                                英文
                                            </td>
                                            <td class="col-xs-9 col-sm-9">
                                                @Html.RadioButtonFor(m => m.spoken_ability_english, ViewBag.list_option_33 as SelectList, Model.spoken_ability_english.ToString())
                                            </td>
                                            <td class="col-xs-6 col-sm-6">
                                                其他 @Html.TextBoxFor(m => m.spoken_other, new { @maxlength = "20", @style = "width:60%" })
                                            </td>
                                            <td class="col-xs-6 col-sm-6">
                                                @Html.RadioButtonFor(m => m.spoken_ability_other, ViewBag.list_option_34 as SelectList, Model.spoken_ability_other.ToString())
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                            <td class="col-xs-4 col-sm-4">
                                <table class="table table-border table-bordered table-bg table-hover">
                                    <tbody>
                                        <tr>
                                            <td class="col-xs-12 col-sm-12 text-c">
                                                閱讀
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="col-xs-3 col-sm-3 text-c">
                                                中文
                                            </td>
                                            <td class="col-xs-9 col-sm-9">
                                                @Html.RadioButtonFor(m => m.reading_ability_chinese, ViewBag.list_option_33 as SelectList, Model.reading_ability_chinese.ToString())
                                            </td>
                                            <td class="col-xs-3 col-sm-3 text-c">
                                                &nbsp;
                                            </td>
                                            <td class="col-xs-9 col-sm-9">
                                                &nbsp;
                                            </td>
                                            <td class="col-xs-3 col-sm-3 text-c">
                                                英文
                                            </td>
                                            <td class="col-xs-9 col-sm-9">
                                                @Html.RadioButtonFor(m => m.reading_ability_english, ViewBag.list_option_33 as SelectList, Model.reading_ability_english.ToString())
                                            </td>
                                            <td class="col-xs-6 col-sm-6">
                                                其他 @Html.TextBoxFor(m => m.reading_other, new { @maxlength = "20", @style = "width:60%" })
                                            </td>
                                            <td class="col-xs-6 col-sm-6">
                                                @Html.RadioButtonFor(m => m.reading_ability_other, ViewBag.list_option_34 as SelectList, Model.reading_ability_other.ToString())
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                            <td class="col-xs-4 col-sm-4">
                                <table class="table table-border table-bordered table-bg table-hover">
                                    <tbody>
                                        <tr>
                                            <td class="col-xs-12 col-sm-12 text-c">
                                                書寫
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="col-xs-3 col-sm-3 text-c">
                                                中文
                                            </td>
                                            <td class="col-xs-9 col-sm-9">
                                                @Html.RadioButtonFor(m => m.written_ability_chinese, ViewBag.list_option_33 as SelectList, Model.written_ability_chinese.ToString())
                                            </td>
                                            <td class="col-xs-3 col-sm-3 text-c">
                                                &nbsp;
                                            </td>
                                            <td class="col-xs-9 col-sm-9">
                                                &nbsp;
                                            </td>
                                            <td class="col-xs-3 col-sm-3 text-c">
                                                英文
                                            </td>
                                            <td class="col-xs-9 col-sm-9">
                                                @Html.RadioButtonFor(m => m.written_ability_english, ViewBag.list_option_33 as SelectList, Model.written_ability_english.ToString())
                                            </td>
                                            <td class="col-xs-6 col-sm-6">
                                                其他 @Html.TextBoxFor(m => m.written_other, new { @maxlength = "20", @style = "width:60%" })
                                            </td>
                                            <td class="col-xs-6 col-sm-6">
                                                @Html.RadioButtonFor(m => m.written_ability_other, ViewBag.list_option_34 as SelectList, Model.written_ability_other.ToString())
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        }
</article>