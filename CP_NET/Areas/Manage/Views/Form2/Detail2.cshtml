@model Models.EntityDtos.Apply2Dto

@{
    ViewBag.Title = "";
}

@section Footer{

    <script type="text/javascript" src="/Scripts/lib/My97DatePicker/WdatePicker.js"></script>
    <script>
        $("input[type='text']").attr("readonly", "true");
        $("input[type='radio']").attr("disabled", true);
        $("input[type='checkbox']").attr("disabled", true);

        $("#a_sendmail").click(function () {
            var data = { title: "工傷理賠新案", content: "@("請查詢系統，申請人職位："+Model.job_name+"，申請人："+Model.user_name+"，手機號碼："+Model.user_mobile)" };
            JsCode.DoRestful("post", "/api/Form2Api/SendMail", data, function (res) {
                layer.alert("發送成功");
            });
        });
    </script>
}

<nav class="breadcrumb">
    <i class="Hui-iconfont"></i> EMPLOYEES' COMPENSATION ORDIANCE (CAP.282)<span class="c-gray en">&gt;</span> NOTICE BY EMPLOYER OF THE DEATH OF AN EMPLOYEE OR OF AN ACCIDENT
    <a class="btn btn-success radius r" style="line-height:1.6em;margin-top:3px" href="javascript:location.replace(location.href);" title="refresh"><i class="Hui-iconfont">&#xe68f;</i></a>
</nav>

<article class="page-container">

    <div class="cl pd-5 bg-1 bk-gray mt-20">
        <span>
            <a class="btn btn-primary radius" href="@Url.Action("List")?l=2">Back</a>
            <a class="btn btn-danger radius" href="/PublicFiles/pdf/2/@(Model.id).pdf" target="_blank">Print</a>
            <a class="btn btn-warning radius" href="javascript:;" id="a_sendmail">Send Mail</a>
            @if (CP_NET.Models.Env.CurrentUser.Authority != (byte)Utils.ENUserAuthority.没有修改权限)
            {
                <a class="btn btn-success radius" href="@Url.Action("Edit")/@Model.id?l=2">Edit</a>
            }
        </span>
    </div>

    @using (Html.BeginForm("Detail", "Form2", FormMethod.Post, new { @class = "form form-horizontal" }))
    {

        if (!string.IsNullOrEmpty(Model.attach_url))
        {
            <div class="mt-20">
                Attachment：@Html.Raw(Model.attach_url)
            </div>
        }

        <div class="mt-20">
            <strong class="c-blue">To the Commissioner for Labour</strong>
            <table class="mt-20 table table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td>
                            <strong>I declare that the information given in this form is,to the best of my knowledge,true and accurate.</strong>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <strong>Name(in block letters)</strong>：
                            @Model.user_name_e
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <strong>Position</strong>：
                            @Html.RadioButtonFor(m => m.job_id, ViewBag.list_option_1 as SelectList, Model.job_id.ToString())
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <strong>Date</strong>：
                            @Model.create_time_str
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20 text-c f-18">
            <strong>Part Ⅰ</strong>
        </div>

        <div class="mt-20">
            <strong class="c-blue">A. Particulars of the employee</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td class="col-xs-4 col-sm-4">
                            <strong>Name of employee(Surname first)</strong>：
                            @Html.TextBoxFor(m => m.employee_name, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.employee_name)
                        </td>
                        <td class="col-xs-3 col-sm-3">
                            <strong>Identity Type</strong>：<br />
                            @Html.DropDownListFor(m => m.employee_id_type, ViewBag.list_option_2 as SelectList, new { @class = "input-text", @style = "width:60%;" })
                        </td>
                        <td class="col-xs-5 col-sm-5">
                            <strong>Identity No</strong>：
                            @Html.TextBoxFor(m => m.employee_id_number, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.employee_id_number)
                        </td>
                    </tr>
                    <tr>
                        <td class="col-xs-4 col-sm-4">
                            <strong>Telphone No.</strong>：
                            @Html.TextBoxFor(m => m.employee_phone, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.employee_phone)
                        </td>
                        <td class="col-xs-4 col-sm-4">
                            <strong>Fax No.</strong>：
                            @Html.TextBoxFor(m => m.employee_fax, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.employee_fax)
                        </td>
                        <td class="col-xs-4 col-sm-4">
                            <strong>Address</strong>：
                            @Html.TextBoxFor(m => m.employee_address, new { @class = "input-text", @maxlength = "300" })
                            @Html.ValidationMessageFor(m => m.employee_address)
                        </td>
                    </tr>
                    <tr>
                        <td class="col-xs-3 col-sm-3">
                            <strong>Date of Birth</strong>：
                            @Html.TextBoxFor(m => m.employee_birthday, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM-dd' })" })
                            @Html.ValidationMessageFor(m => m.employee_birthday)
                        </td>
                        <td class="col-xs-3 col-sm-3">
                            <strong>Sex</strong>：<br />
                            @Html.DropDownListFor(m => m.employee_gender, new[] {
                                new SelectListItem() { Value = "1", Text = "男" },
                                new SelectListItem() { Value = "2", Text = "女" }
                            }, new { @class = "input-text", @style = "width:60%;" })
                        </td>
                        <td class="col-xs-3 col-sm-3">
                            <strong>Occupation</strong>：
                            @Html.TextBoxFor(m => m.employee_job, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.employee_job)
                        </td>
                        <td class="col-xs-3 col-sm-3">
                            <strong>An apprentice</strong>：<br />
                            @Html.DropDownListFor(m => m.employee_is_trainee, new[] {
                                new SelectListItem() { Value = "True", Text = "是" },
                                new SelectListItem() { Value = "False", Text = "否" }
                            }, new { @class = "input-text", @style = "width:60%;" })
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">B. Particulars of employer</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td class="col-xs-6 col-sm-6">
                            <strong>Name of employing company/person</strong>：
                            @Html.TextBoxFor(m => m.employer_name, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.employer_name)
                        </td>
                        <td class="col-xs-6 col-sm-6">
                            <strong>Business Registration Certificated No.</strong>：
                            @Html.TextBoxFor(m => m.employer_id_number, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.employer_id_number)
                        </td>
                    </tr>
                    <tr>
                        <td class="col-xs-4 col-sm-4">
                            <strong>Telphone No.</strong>：
                            @Html.TextBoxFor(m => m.employer_phone, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.employer_phone)
                        </td>
                        <td class="col-xs-8 col-sm-8">
                            <strong>Address</strong>：
                            @Html.TextBoxFor(m => m.employer_address, new { @class = "input-text", @maxlength = "300" })
                        </td>
                    </tr>
                    <tr>
                        <td class="col-xs-4 col-sm-4">
                            <strong>Fax No.</strong>：
                            @Html.TextBoxFor(m => m.employer_fax, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.employer_fax)
                        </td>
                        <td class="col-xs-8 col-sm-8">
                            <strong>Trade</strong>：
                            @Html.TextBoxFor(m => m.employer_industry, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.employer_industry)
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">C. Particulars of principal contrator/holding company</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td class="col-xs-6 col-sm-6">
                            <strong>Name of principal contrator/holding company</strong>：
                            @Html.TextBoxFor(m => m.company_name, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.company_name)
                        </td>
                        <td class="col-xs-6 col-sm-6">
                            <strong>Business Registration Certificate No.</strong>：
                            @Html.TextBoxFor(m => m.company_id_number, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.company_id_number)
                        </td>
                    </tr>
                    <tr>
                        <td class="col-xs-4 col-sm-4">
                            <strong>Telphone No.</strong>：
                            @Html.TextBoxFor(m => m.company_phone, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.company_phone)
                        </td>
                        <td class="col-xs-8 col-sm-8">
                            <strong>Address</strong>：
                            @Html.TextBoxFor(m => m.company_address, new { @class = "input-text", @maxlength = "300" })
                            @Html.ValidationMessageFor(m => m.company_address)
                        </td>
                    </tr>
                    <tr>
                        <td class="col-xs-4 col-sm-4">
                            <strong>Fax No.</strong>：
                            @Html.TextBoxFor(m => m.company_fax, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.company_fax)
                        </td>
                        <td class="col-xs-8 col-sm-8">
                            <strong>Trade</strong>：
                            @Html.TextBoxFor(m => m.company_industry, new { @class = "input-text", @maxlength = "100" })
                            @Html.ValidationMessageFor(m => m.company_industry)
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">D. Description of accident</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td class="col-xs-12 col-sm-12">
                            <strong>Descibe how the accident happend and state what the employee was doing at the time</strong>：
                            @Html.TextBoxFor(m => m.accident_desc, new { @class = "input-text", @style = "width:100%;" })
                            @Html.ValidationMessageFor(m => m.accident_desc)
                        </td>
                    </tr>
                    <tr>
                        <td class="col-xs-4 col-sm-4">
                            <strong>State whether the accident occured in the course of work</strong>：<br />
                            @Html.DropDownListFor(m => m.happened_on_work, new[] {
                                new SelectListItem() { Value = "True", Text = "Yes" },
                                new SelectListItem() { Value = "False", Text = "No" }
                            }, new { @class = "input-text", @style = "width:60%;" })
                        </td>
                        <td class="col-xs-4 col-sm-4">
                            <strong>Date of accident</strong>：
                            @Html.TextBoxFor(m => m.happened_date, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM-dd HH:mm' })", @Value = Model.happened_date_str })
                            @Html.ValidationMessageFor(m => m.happened_date)
                        </td>
                        <td class="col-xs-4 col-sm-4">
                            <strong>Result of accident</strong>：<br />
                            @Html.DropDownListFor(m => m.accident_result, ViewBag.list_option_3 as SelectList, new { @class = "input-text", @style = "width:60%;" })
                        </td>
                    </tr>
                    <tr>
                        <td class="col-xs-6 col-sm-6">
                            <strong>Address of the place of accident</strong>：
                            @Html.TextBoxFor(m => m.happened_address, new { @class = "input-text", @maxlength = "300" })
                        </td>
                        <td class="col-xs-6 col-sm-6">
                            <strong>Name of hospital/clinic where the employee received treatment</strong>：
                            @Html.TextBoxFor(m => m.hospital_name, new { @class = "input-text", @maxlength = "300" })
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">E. Details of insurance</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td class="col-xs-8 col-sm-8">
                            <strong>Name and address of insurance company at the time of accident(Please refer to the insurance policy)</strong>：<br />
                            @Html.TextBoxFor(m => m.insurance_detail, new { @class = "input-text", @maxlength = "200" })
                        </td>
                        <td class="col-xs-4 col-sm-4">
                            <strong>Policy No.</strong>：<br />
                            @Html.TextBoxFor(m => m.insurance_number, new { @class = "input-text", @maxlength = "100" })
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">F. Details of earnings of the employee</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td class="col-xs-6 col-sm-6">
                            <strong>Average numbers of working days per month</strong>：<br />
                            @Html.RadioButtonFor(m => m.work_days_id, ViewBag.list_option_4 as SelectList, Model.work_days_id.ToString())
                            @Html.TextBoxFor(m => m.other_days, new { @class = "input-text", @maxlength = "2", @style = "width:50%;", @placeholder = "please specify" })
                        </td>
                        <td class="col-xs-6 col-sm-6">
                            <strong>Restday is</strong>：<br />
                            (a) @Html.RadioButtonFor(m => m.is_paid_at_rest, new[] {
                                new SelectListItem() { Value = "False", Text = "not paid" },
                                new SelectListItem() { Value = "True", Text = "paid" }
                            }, Model.is_paid_at_rest.ToString())

                            (b) @Html.RadioButtonFor(m => m.is_regular_at_rest, new[] {
                                new SelectListItem() { Value = "False", Text = "not fixed" },
                                new SelectListItem() { Value = "True", Text = "fixed on" }
                            }, Model.is_regular_at_rest.ToString())
                            @Html.TextBoxFor(m => m.regular_at_day, new { @class = "input-text", @maxlength = "2", @style = "width:10%;", @placeholder = "Day of week" })
                        </td>
                    </tr>
                    <tr>
                        <td class="col-xs-12 col-sm-12">
                            Details of earnings per month for the month immediately preceding the date of accident：<br />
                            <div class="row cl">
                                <div class="col-xs-2 col-sm-2">
                                    (a) Basic salary/wages：
                                </div>
                                <div class="col-xs-6 col-sm-6">
                                    $ @Html.TextBoxFor(m => m.basic_salary, new { @class = "input-text", @maxlength = "10", @style = "width:50%;" }) / month
                                    @Html.ValidationMessageFor(m => m.basic_salary)
                                </div>
                            </div>
                            <div class="row cl">
                                <div class="col-xs-2 col-sm-2">
                                    (b) Food allowances/values of free food provided by employer：
                                </div>
                                <div class="col-xs-6 col-sm-6">
                                    $ @Html.TextBoxFor(m => m.allowance, new { @class = "input-text", @maxlength = "10", @style = "width:50%;" }) / month
                                    @Html.ValidationMessageFor(m => m.allowance)
                                </div>
                            </div>
                            <div class="row cl">
                                <div class="col-xs-2 col-sm-2">
                                    (c) Other items：@Html.TextBoxFor(m => m.other_income_name, new { @class = "input-text", @maxlength = "50", @style = "width:50%;", @placeholder = "please specify" })
                                </div>
                                <div class="col-xs-6 col-sm-6">
                                    $ @Html.TextBoxFor(m => m.other_income, new { @class = "input-text", @maxlength = "10", @style = "width:50%;" }) / month
                                    @Html.ValidationMessageFor(m => m.other_income)
                                </div>
                            </div>
                            <div class="row cl">
                                <div class="col-xs-2 col-sm-2">Total（a) + (b) +（c)：</div>
                                <div class="col-xs-6 col-sm-6">
                                    $ @Html.TextBoxFor(m => m.total_salary_monthly, new { @class = "input-text", @maxlength = "10", @style = "width:50%;" }) / month
                                    @Html.ValidationMessageFor(m => m.total_salary_monthly)
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="col-xs-12 col-sm-12">
                            Average monthly earnings of the employee for the past 12 months(or total period of employment,if less than 12 months),preceding the accident were：<br />
                            <div class="row cl">
                                <div class="col-xs-2 col-sm-2">
                                </div>
                                <div class="col-xs-6 col-sm-6">
                                    $ @Html.TextBoxFor(m => m.average_salary_monthly, new { @class = "input-text", @maxlength = "10", @style = "width:50%;" }) / month
                                    @Html.ValidationMessageFor(m => m.average_salary_monthly)
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">G. Fatal accident(to be completed where accident result in death)</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td class="col-xs-3 col-sm-3">
                            <strong>Whether police was notified</strong>：<br />
                            @Html.RadioButtonFor(m => m.has_called_police, new[] {
                                new SelectListItem() { Value = "False", Text = "No" },
                                new SelectListItem() { Value = "True", Text = "Yes" }
                            }, Model.has_called_police.ToString())
                            @Html.TextBoxFor(m => m.police_station, new { @class = "input-text", @maxlength = "50", @placeholder = "Name of police station", @style = "width:50%" })
                        </td>
                        <td class="col-xs-4 col-sm-4">
                            <strong>Name and address of next-of-kin of the deceased employee</strong>：
                            @Html.TextBoxFor(m => m.kin_detail, new { @class = "input-text", @maxlength = "200" })
                        </td>
                        <td class="col-xs-3 col-sm-3">
                            <strong>Relationship with the deceased employee</strong>：<br />
                            @Html.TextBoxFor(m => m.kin_relationship, new { @class = "input-text", @maxlength = "50" })
                        </td>
                        <td class="col-xs-2 col-sm-2">
                            <strong>Telphone No.</strong>：<br />
                            @Html.TextBoxFor(m => m.kin_phone, new { @class = "input-text", @maxlength = "100" })
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">H. Direct settlement(to be completed only where the injury results in temporary incapacity for not more than 7 days and no permanent incapacity,and the employer and employee have chosen to directly settle the employee's compensation claim)</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td class="col-xs-6 col-sm-6">
                            <strong>Period of sick leave</strong>：<br />
                            from @Html.TextBoxFor(m => m.leave_from_1, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM-dd' })", @style = "width:20%", @Value = Model.leave_from_1_str })
                            to @Html.TextBoxFor(m => m.leave_end_1, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM-dd' })", @style = "width:20%", @Value = Model.leave_end_1_str })
                            <br /><br />
                            from @Html.TextBoxFor(m => m.leave_from_2, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM-dd' })", @style = "width:20%", @Value = Model.leave_from_2_str })
                            to @Html.TextBoxFor(m => m.leave_end_2, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM-dd' })", @style = "width:20%", @Value = Model.leave_end_2_str })
                            Total number of sick leave days：@Html.TextBoxFor(m => m.leave_days, new { @class = "input-text", @maxlength = "5", @style = "width:20%" })
                        </td>
                        <td class="col-xs-6 col-sm-6">
                            <strong>Amount of compenstaion</strong>：<br />
                            $ @Html.TextBoxFor(m => m.pay_amount, new { @class = "input-text", @maxlength = "10", @style = "width:60%" })
                            <br /><br />
                            @Html.RadioButtonFor(m => m.has_paid, new[] {
                                new SelectListItem() { Value = "True", Text = "paid" },
                                new SelectListItem() { Value = "False", Text = "to be paid on" }
                            }, Model.has_paid.ToString())
                            @Html.TextBoxFor(m => m.pay_date, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM-dd' })", @style = "width:30%", @Value = Model.pay_date_str })
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">I. Place of accident</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td class="col-xs-12 col-sm-12">
                            <strong>The accident occurred in</strong>：<br /><br />
                            <div class="col-xs-3 col-sm-3">
                                <u>Construction site</u><br />
                                @Html.RadioButtonFor(m => m.happened_location_id, ViewBag.list_option_5 as SelectList, Model.happened_location_id.ToString(), false)
                            </div>
                            <div class="col-xs-3 col-sm-3">
                                <u>Shipyard</u><br />
                                @Html.RadioButtonFor(m => m.happened_location_id, ViewBag.list_option_6 as SelectList, Model.happened_location_id.ToString(), false)
                            </div>
                            <div class="col-xs-3 col-sm-3">
                                <u>Manufactory</u><br />
                                @Html.RadioButtonFor(m => m.happened_location_id, ViewBag.list_option_7 as SelectList, Model.happened_location_id.ToString(), false)
                            </div>
                            <div class="col-xs-3 col-sm-3">
                                <u>Others</u><br />
                                @Html.RadioButtonFor(m => m.happened_location_id, ViewBag.list_option_8 as SelectList, Model.happened_location_id.ToString(), false)
                                @Html.TextBoxFor(m => m.other_happened_location, new { @class = "input-text", @maxlength = "50" })
                            </div>
                        </td>
                    </tr>                    
                    <tr>
                        <td class="col-xs-12 col-sm-12">
                            Activity carried out on the site at the time of accident：<br />
                            @Html.TextBoxFor(m => m.happened_activity, new { @class = "input-text", @maxlength = "300" })
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">J. Nature of injury</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td class="col-xs-12 col-sm-12">
                            <strong>Describe the nature of injury</strong><br />
                            @Html.TextBoxFor(m => m.injure_desc, new { @class = "input-text", @maxlength = "300" })
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <strong>Indicate nature of injury</strong>：<br />
                            @{int i_9 = 1;  }
                            @foreach (var i in ViewBag.list_option_9)
                            {
                                if (i_9 % 5 == 1)
                                {
                                    @Html.Raw("<div class=\"col-xs-3 col-sm-3\">");
                                }
                                <div>
                                    @if (Model.injure_id_list.Contains(i.id.ToString()))
                                    {
                                        <input type='checkbox' value='@(i.id)'
                                               name='_injure_id' id='injure_id@(i_9)'
                                               checked="checked" />
                                    }
                                    else
                                    {
                                        <input type='checkbox' value='@(i.id)'
                                               name='_injure_id' id='injure_id@(i_9)' />
                                    }
                                    <label for='injure_id@(i_9)' style='margin-right:10px;'>@i.name</label>
                                </div>
                                if (i_9 % 5 == 0)
                                {
                                    if (i_9 == 20)
                                    {@Html.TextBoxFor(m => m.other_injure, new { @class = "input-text", @maxlength = "50" }) }
                                    @Html.Raw("</div>");
                                }
                                i_9++;
                            }
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <strong>Part of body injured</strong>：<br /><br />
                            <div class="col-xs-2 col-sm-2">
                                <u>Head</u><br />
                                @Html.CheckBoxFor(m => m._injure_part_id, ViewBag.list_option_10 as SelectList, Model.injure_part_id, false)
                            </div>
                            <div class="col-xs-2 col-sm-2">
                                <u>Neck & Trunk</u><br />
                                @Html.CheckBoxFor(m => m._injure_part_id, ViewBag.list_option_11 as SelectList, Model.injure_part_id, false)
                            </div>
                            <div class="col-xs-2 col-sm-2">
                                <u>Upper Limbs</u><br />
                                @Html.CheckBoxFor(m => m._injure_part_id, ViewBag.list_option_12 as SelectList, Model.injure_part_id, false)
                            </div>
                            <div class="col-xs-2 col-sm-2">
                                <u>Lower Limbs</u><br />
                                @Html.CheckBoxFor(m => m._injure_part_id, ViewBag.list_option_13 as SelectList, Model.injure_part_id, false)
                            </div>
                            <div class="col-xs-2 col-sm-2">
                                @Html.CheckBoxFor(m => m._injure_part_id, ViewBag.list_option_14 as SelectList, Model.injure_part_id, false)
                                @Html.TextBoxFor(m => m.other_part, new { @class = "input-text", @maxlength = "50", @placeholder="please specify" })
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">K. Type of accident</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td>
                            <div class="col-xs-3 col-sm-3">
                                @Html.RadioButtonFor(m => m.accident_type_id, ViewBag.list_option_15 as SelectList, Model.accident_type_id.ToString(), false)
                                @Html.TextBoxFor(m => m.accident_height, new { @class = "input-text", @maxlength = "6", @placeholder = "distance through which person fell", @style = "width:40%" }) metres
                                @Html.ValidationMessageFor(m => m.accident_height)
                            </div>
                            <div class="col-xs-3 col-sm-3">
                                @Html.RadioButtonFor(m => m.accident_type_id, ViewBag.list_option_16 as SelectList, Model.accident_type_id.ToString(), false)
                            </div>
                            <div class="col-xs-3 col-sm-3">
                                @Html.RadioButtonFor(m => m.accident_type_id, ViewBag.list_option_17 as SelectList, Model.accident_type_id.ToString(), false)
                            </div>
                            <div class="col-xs-3 col-sm-3">
                                @Html.RadioButtonFor(m => m.accident_type_id, ViewBag.list_option_18 as SelectList, Model.accident_type_id.ToString(), false)
                                @Html.TextBoxFor(m => m.other_type, new { @class = "input-text", @maxlength = "50", @placeholder = "please specify", @style = "width:60%" })
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">L. Agents involved,if any(tick one or more boxes)</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td>
                            <div class="col-xs-3 col-sm-3">
                                @Html.CheckBoxFor(m => m.agent_id, ViewBag.list_option_19 as SelectList, Model.agent_ids, false)
                                @Html.TextBoxFor(m => m.other_agent_type, new { @class = "input-text", @maxlength = "20", @placeholder = "Type", @style = "width:60%" })
                                <br />Part causing injury：
                                @Html.CheckBoxFor(m => m.agent_part, ViewBag.list_option_23 as SelectList, Model.agent_parts, false)
                            </div>
                            <div class="col-xs-3 col-sm-3">
                                @Html.CheckBoxFor(m => m.agent_id, ViewBag.list_option_20 as SelectList, Model.agent_ids, false)
                            </div>
                            <div class="col-xs-3 col-sm-3">
                                @Html.CheckBoxFor(m => m.agent_id, ViewBag.list_option_21 as SelectList, Model.agent_ids, false)
                            </div>
                            <div class="col-xs-3 col-sm-3">
                                @Html.CheckBoxFor(m => m.agent_id, ViewBag.list_option_22 as SelectList, Model.agent_ids, false)
                                @Html.TextBoxFor(m => m.other_agent, new { @class = "input-text", @maxlength = "50", @placeholder = "please specify", @style = "width:60%" })
                            </div>

                            @Html.HiddenFor(m => m.agent_ids)
                            @Html.HiddenFor(m => m.agent_parts)
                        </td>
                    </tr>
                    <tr>
                        <td class="col-xs-12 col-sm-12">
                            <strong>Describe briefly the agents you have indicated</strong><br />
                            @Html.TextBoxFor(m => m.agent_desc, new { @class = "input-text", @maxlength = "300" })
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">M. Sketch（to supplement the discriptions given above,if considered necessary，after PDF printed）</strong>           
        </div>

        <div class="mt-20 mb-20 text-c f-18">
            <strong>End of PartⅠ</strong>
        </div>

        <hr />

        <div class="mt-20 text-c f-18">
            <strong>Part Ⅱ</strong><br />
            <strong>(To be completed if the accident occured on a construction site)</strong>
        </div>

        <div class="mt-20">
            <strong class="c-blue">N. Type of work performed by the employee at the time of accident</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td>
                            @{int i_24 = 1;  }
                            @foreach (var i in ViewBag.list_option_24)
                            {
                                if (i_24 % 6 == 1)
                                {
                                    @Html.Raw("<div class=\"col-xs-3 col-sm-3\">");
                                }
                                <div>
                                    @if (Model.work_type_id == i.id)
                                    {
                                        <input type='Radio' value='@(i.id)'
                                               name='work_type_id' id='work_type_id@(i_24)'
                                               checked="checked" />
                                    }
                                    else
                                    {
                                        <input type='Radio' value='@(i.id)'
                                               name='work_type_id' id='work_type_id@(i_24)' />
                                    }
                                    <label for='work_type_id@(i_24)' style='margin-right:10px;'>@i.name</label>
                                </div>
                                if (i_24 % 6 == 0 || i_24 == 20)
                                {
                                    if (i_24 == 20)
                                    {@Html.TextBoxFor(m => m.other_work_type, new { @class = "input-text", @maxlength = "20" }) }
                                    @Html.Raw("</div>");
                                }
                                i_24++;
                            }
                        </td>
                    </tr>
                    <tr>
                        <td class="col-xs-12 col-sm-12">
                            <strong>Whereabouts on the site such work was performed</strong><br />
                            @Html.TextBoxFor(m => m.work_site, new { @class = "input-text", @maxlength = "300" })
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">O. Machinery involved,if any(tick one or more boxes)</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td>
                            <div class="col-xs-4 col-sm-4">
                                @Html.CheckBoxFor(m => m.machine_id, ViewBag.list_option_25 as SelectList, Model.machine_ids, false)
                            </div>
                            <div class="col-xs-4 col-sm-4">
                                @Html.CheckBoxFor(m => m.machine_id, ViewBag.list_option_26 as SelectList, Model.machine_ids, false)
                            </div>
                            <div class="col-xs-4 col-sm-4">
                                @Html.CheckBoxFor(m => m.machine_id, ViewBag.list_option_27 as SelectList, Model.machine_ids, false)
                                @Html.TextBoxFor(m => m.other_machine, new { @class = "input-text", @maxlength = "50", @placeholder = "please specify", @style = "width:60%" })
                            </div>

                            @Html.HiddenFor(m => m.machine_ids)
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">P. Transporting or construction machinery involved,if any</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td>
                            @{int i_28 = 1;  }
                            @foreach (var i in ViewBag.list_option_28)
                            {
                                if (i_28 % 3 == 1)
                                {
                                    @Html.Raw("<div class=\"col-xs-4 col-sm-4\">");
                                }
                                <div>
                                    @if (Model.transport_id == i.id)
                                    {
                                        <input type='Radio' value='@(i.id)'
                                               name='transport_id' id='transport_id@(i_28)'
                                               checked="checked" />
                                    }
                                    else
                                    {
                                        <input type='Radio' value='@(i.id)'
                                               name='transport_id' id='transport_id@(i_28)' />
                                    }
                                    <label for='transport_id@(i_28)' style='margin-right:10px;'>@i.name</label>
                                </div>
                                if (i_28 % 3 == 0 || i_28 == 7)
                                {
                                    if (i_28 == 7)
                                    {@Html.TextBoxFor(m => m.other_transport, new { @class = "input-text", @maxlength = "20" }) }
                                    @Html.Raw("</div>");
                                }
                                i_28++;
                            }
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20 mb-20 text-c f-18">
            <strong>End of Part Ⅱ</strong>
        </div>

        <hr />

        <div class="mt-20 text-c f-18">
            <strong>Supplementary Information on Accidents on Construction Sites</strong><br />
        </div>

        <div class="mt-20">
            <strong>Explanatory Note：This is <u>not</u> a statutory form required to be submitted under the Employees' Compensation Ordinance for reporting accident.However,the co-operation of employers is sought to complete Sections Ⅰ to Ⅴ below for accidents occured on construction sites.The supplementary information will be used for the purpose of accident analysis within Government and by the public bodies concerned.</strong>
        </div>

        <div class="mt-20">
            <strong class="c-blue">Ⅰ. Particulars of Worksite</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td class="col-xs-4 col-sm-4">
                            <strong>Commencement of Construction Work</strong>：
                            @Html.TextBoxFor(m => m.construction_date, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM' })", @Value=Model.construction_date_str })
                        </td>
                        <td class="col-xs-4 col-sm-4">
                            <strong>Expected Completion Date</strong>：
                            @Html.TextBoxFor(m => m.expired_date, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM' })", @Value = Model.expired_date_str })
                        </td>
                        <td class="col-xs-4 col-sm-4">
                            <strong>Date of Accident</strong>：
                            @Html.TextBoxFor(m => m.accident_date, new { @class = "input-text Wdate", @onfocus = "WdatePicker({ dateFmt:'yyyy-MM-dd' })", @Value = Model.accident_date_str })
                        </td>
                    </tr>
                    <tr>
                        <td class="col-xs-4 col-sm-4">
                            <strong>Contractor Name</strong>：
                            @Html.TextBoxFor(m => m.constractor_name, new { @class = "input-text", @maxlength = "100" })
                        </td>
                        <td class="col-xs-4 col-sm-4">
                            <strong>Contract No.(if available)</strong>：
                            @Html.TextBoxFor(m => m.contract_no, new { @class = "input-text", @maxlength = "100" })
                        </td>
                        <td class="col-xs-4 col-sm-4">
                            <strong>Contact Telephone</strong>：
                            @Html.TextBoxFor(m => m.contact_phone, new { @class = "input-text", @maxlength = "100" })
                        </td>
                    </tr>
                    <tr>
                        <td class="col-xs-12 col-sm-12">
                            <strong>Site Address</strong>：
                            @Html.TextBoxFor(m => m.site_address, new { @class = "input-text", @maxlength = "300" })
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">Ⅱ. Particulars of Project</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td>
                            <strong>（A）Nature of Project</strong>：
                            @Html.RadioButtonFor(m => m.natrue_id, ViewBag.list_option_29 as SelectList, Model.natrue_id.ToString())
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <strong>（B）Private Project</strong>：
                            @Html.DropDownListFor(m => m.is_private, new[] {
                                new SelectListItem() { Value = "", Text = "Chose" },
                                new SelectListItem() { Value = "True", Text = "Yes" },
                                new SelectListItem() { Value = "False", Text = "No" }
                            }, new { @class = "input-text", @style = "width:20%;" ,@onchange = "ShowProject();" }) &nbsp;
                            If Yes,please give name and contact telephone no. of authorized person or project manager.If No,please indicate below the type of public works/government project.

                            <div id="div_private_info">
                                <div class="col-xs-4 col-sm-4">
                                    <strong>Name</strong>：@Html.TextBoxFor(m => m.private_name, new { @class = "input-text", @maxlength = "100" })
                                </div>
                                <div class="col-xs-4 col-sm-4">
                                    <strong>Positon</strong>：@Html.TextBoxFor(m => m.private_job, new { @class = "input-text", @maxlength = "50" })
                                </div>
                                <div class="col-xs-4 col-sm-4">
                                    <strong>Tel. No.</strong>：@Html.TextBoxFor(m => m.private_phone, new { @class = "input-text", @maxlength = "100" })
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <strong>（C）Public Works or Government Project</strong>：<br /><br />
                            @{int i_30 = 1;  }
                            @foreach (var i in ViewBag.list_option_30)
                            {
                                if (i_30 % 5 == 1)
                                {
                                    @Html.Raw("<div class=\"col-xs-4 col-sm-4\">");
                                }
                                <div>
                                    @if (Model.project_id == i.id)
                                    {
                                        <input type='Radio' value='@(i.id)'
                                               name='project_id' id='project_id@(i_30)'
                                               checked="checked" />
                                    }
                                    else
                                    {
                                        <input type='Radio' value='@(i.id)'
                                               name='project_id' id='project_id@(i_30)' />
                                    }
                                    <label for='project_id@(i_30)' style='margin-right:10px;'>@i.name</label>
                                </div>
                                if (i_30 % 5 == 0 || i_30 == 18)
                                {
                                    if (i_30 == 18)
                                    {@Html.TextBoxFor(m => m.other_project, new { @class = "input-text", @maxlength = "20" }) }
                                    @Html.Raw("</div>");
                                }
                                i_30++;
                            }
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">Ⅲ. Particulars of Place of Fall(If injured by Fall from Height)</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td>
                            @{int i_31 = 1;  }
                            @foreach (var i in ViewBag.list_option_31)
                            {
                                if (i_31 % 3 == 1)
                                {
                                    @Html.Raw("<div class=\"col-xs-4 col-sm-4\">");
                                }
                                <div>
                                    @if (Model.place_id == i.id)
                                    {
                                        <input type='Radio' value='@(i.id)'
                                               name='place_id' id='place_id@(i_31)'
                                               checked="checked" />
                                    }
                                    else
                                    {
                                        <input type='Radio' value='@(i.id)'
                                               name='place_id' id='place_id@(i_31)' />
                                    }
                                    <label for='place_id@(i_31)' style='margin-right:10px;'>@i.name</label>
                                </div>
                                if (i_31 % 3 == 0 || i_31 == 8)
                                {
                                    if (i_31 == 8)
                                    {@Html.TextBoxFor(m => m.other_place, new { @class = "input-text", @maxlength = "20" }) }
                                    @Html.Raw("</div>");
                                }
                                i_31++;
                            }
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">Ⅳ. Ethnicity</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover table-sort dataTable">
                <tbody>
                    <tr>
                        <td>
                            @{int i_32 = 1;  }
                            @foreach (var i in ViewBag.list_option_32)
                            {
                                if (i_32 % 3 == 1)
                                {
                                    @Html.Raw("<div class=\"col-xs-3 col-sm-3\">");
                                }
                                <div>
                                    @if (Model.ethnicity_id == i.id)
                                    {
                                        <input type='Radio' value='@(i.id)'
                                               name='ethnicity_id' id='ethnicity_id@(i_32)'
                                               checked="checked" />
                                    }
                                    else
                                    {
                                        <input type='Radio' value='@(i.id)'
                                               name='ethnicity_id' id='ethnicity_id@(i_32)' />
                                    }
                                    <label for='ethnicity_id@(i_32)' style='margin-right:10px;'>@i.name</label>
                                </div>
                                if (i_32 % 3 == 0 || i_32 == 11)
                                {
                                    if (i_32 == 11)
                                    {@Html.TextBoxFor(m => m.other_ethnicity, new { @class = "input-text", @maxlength = "20" }) }
                                    @Html.Raw("</div>");
                                }
                                i_32++;
                            }
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-20">
            <strong class="c-blue">Ⅴ. Language Ability</strong>
            <table class="mt-20 table table-border table-bordered table-bg table-hover">
                <tbody>
                    <tr>
                        <td class="col-xs-4 col-sm-4">
                            <table class="table table-border table-bordered table-bg table-hover">
                                <tbody>
                                    <tr>
                                        <td class="col-xs-12 col-sm-12 text-c">
                                            Spoken
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="col-xs-3 col-sm-3 text-c">
                                            Cantonese
                                        </td>
                                        <td class="col-xs-9 col-sm-9">
                                            @Html.RadioButtonFor(m => m.spoken_ability_cantonese, ViewBag.list_option_33 as SelectList, Model.spoken_ability_cantonese.ToString())
                                        </td>
                                        <td class="col-xs-3 col-sm-3 text-c">
                                            Putonghua
                                        </td>
                                        <td class="col-xs-9 col-sm-9">
                                            @Html.RadioButtonFor(m => m.spoken_ability_putonghua, ViewBag.list_option_33 as SelectList, Model.spoken_ability_putonghua.ToString())
                                        </td>
                                        <td class="col-xs-3 col-sm-3 text-c">
                                            English
                                        </td>
                                        <td class="col-xs-9 col-sm-9">
                                            @Html.RadioButtonFor(m => m.spoken_ability_english, ViewBag.list_option_33 as SelectList, Model.spoken_ability_english.ToString())
                                        </td>
                                        <td class="col-xs-6 col-sm-6">
                                            Others @Html.TextBoxFor(m => m.spoken_other, new { @maxlength = "20", @style = "width:60%" })
                                        </td>
                                        <td class="col-xs-6 col-sm-6">
                                            @Html.RadioButtonFor(m => m.spoken_ability_other, ViewBag.list_option_34 as SelectList, Model.spoken_ability_other.ToString())
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                        <td class="col-xs-4 col-sm-4">
                            <table class="table table-border table-bordered table-bg table-hover">
                                <tbody>
                                    <tr>
                                        <td class="col-xs-12 col-sm-12 text-c">
                                            Reading
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="col-xs-3 col-sm-3 text-c">
                                            Chinese
                                        </td>
                                        <td class="col-xs-9 col-sm-9">
                                            @Html.RadioButtonFor(m => m.reading_ability_chinese, ViewBag.list_option_33 as SelectList, Model.reading_ability_chinese.ToString())
                                        </td>
                                        <td class="col-xs-3 col-sm-3 text-c">
                                            &nbsp;
                                        </td>
                                        <td class="col-xs-9 col-sm-9">
                                            &nbsp;
                                        </td>
                                        <td class="col-xs-3 col-sm-3 text-c">
                                            English
                                        </td>
                                        <td class="col-xs-9 col-sm-9">
                                            @Html.RadioButtonFor(m => m.reading_ability_english, ViewBag.list_option_33 as SelectList, Model.reading_ability_english.ToString())
                                        </td>
                                        <td class="col-xs-6 col-sm-6">
                                            Others @Html.TextBoxFor(m => m.reading_other, new { @maxlength = "20", @style = "width:60%" })
                                        </td>
                                        <td class="col-xs-6 col-sm-6">
                                            @Html.RadioButtonFor(m => m.reading_ability_other, ViewBag.list_option_34 as SelectList, Model.reading_ability_other.ToString())
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                        <td class="col-xs-4 col-sm-4">
                            <table class="table table-border table-bordered table-bg table-hover">
                                <tbody>
                                    <tr>
                                        <td class="col-xs-12 col-sm-12 text-c">
                                            Written
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="col-xs-3 col-sm-3 text-c">
                                            Chinese
                                        </td>
                                        <td class="col-xs-9 col-sm-9">
                                            @Html.RadioButtonFor(m => m.written_ability_chinese, ViewBag.list_option_33 as SelectList, Model.written_ability_chinese.ToString())
                                        </td>
                                        <td class="col-xs-3 col-sm-3 text-c">
                                            &nbsp;
                                        </td>
                                        <td class="col-xs-9 col-sm-9">
                                            &nbsp;
                                        </td>
                                        <td class="col-xs-3 col-sm-3 text-c">
                                            English
                                        </td>
                                        <td class="col-xs-9 col-sm-9">
                                            @Html.RadioButtonFor(m => m.written_ability_english, ViewBag.list_option_33 as SelectList, Model.written_ability_english.ToString())
                                        </td>
                                        <td class="col-xs-6 col-sm-6">
                                            Others @Html.TextBoxFor(m => m.written_other, new { @maxlength = "20", @style = "width:60%" })
                                        </td>
                                        <td class="col-xs-6 col-sm-6">
                                            @Html.RadioButtonFor(m => m.written_ability_other, ViewBag.list_option_34 as SelectList, Model.written_ability_other.ToString())
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="row cl">
            <label class="form-label col-xs-2 col-sm-2"></label>
            <div class="formControls col-xs-10 col-sm-10">
                @Html.ValidationMessage("error")
                @Html.ValidationMessage("ok", new { @style = "display:none;", @class = "valid-ok" })
                @Html.ValidationMessage("url", new { @style = "display:none;", @class = "valid-tourl" })
            </div>
        </div>

        <div class="mt-20">
            <button id="admin-role-save" name="admin-role-save" class="btn btn-primary radius" type="submit" onclick="DoAction(); return false;">
                <i class="Hui-iconfont">&#xe632;</i> Submit
            </button>
            &nbsp;&nbsp;
            <a href="@Url.Action("List")?l=2" class="btn btn-default radius">&nbsp;&nbsp;Cancel&nbsp;&nbsp;</a>
		</div>
    }
</article>