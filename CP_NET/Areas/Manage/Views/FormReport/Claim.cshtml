@model Models.EntityDtos.ReportDto

@{
    ViewBag.Title = "";
}

@section Footer{
    <script>
        JsCode.SubmitTip();
    </script>
}

    <article class="page-container">

        @using (Html.BeginForm("Claim", "FormReport", FormMethod.Post, new { @class = "form form-horizontal" }))
        {
            @Html.AntiForgeryToken()
            @Html.HiddenFor(m => m.id)


            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-2">中海保險理賠號碼：</label>
                <div class="formControls col-xs-8 col-sm-9">
                    @Html.TextBoxFor(m => m.claim_no, new { @class = "input-text", @maxlength = "30" })
                </div>
            </div>

            <div class="row cl">
                <label class="form-label col-xs-2 col-sm-2"></label>
                <div class="formControls col-xs-10 col-sm-10">
                    @Html.ValidationMessage("error")
                    @Html.ValidationMessage("ok", new { @style = "display:none;", @class = "valid-ok" })
                    @Html.ValidationMessage("url", new { @style = "display:none;", @class = "valid-url" })
                </div>
            </div>

            <div class="row cl mt-20">
                <div class="col-xs-8 col-sm-9 col-xs-offset-4 col-sm-offset-2">
                    <button id="admin-role-save" name="admin-role-save" class="btn btn-primary radius" type="submit">
                        <i class="Hui-iconfont">&#xe632;</i> 确定
                    </button>
                    &nbsp;&nbsp;
                    <button onClick="parent.layer.closeAll();" class="btn btn-default radius" type="button">&nbsp;&nbsp;取消&nbsp;&nbsp;</button>
                </div>
            </div>
        }

    </article>