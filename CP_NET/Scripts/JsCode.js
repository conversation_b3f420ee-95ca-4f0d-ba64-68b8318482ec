!(function ($) {
    var JsCode = function () {
        return {
            SubmitTip: function () {
                var msg = $(".valid-ok").html();
                var url = $(".valid-url").html();
                if (msg != null && msg != '') {
                    layer.msg(msg, { time: 1500 }, function () {
                        if (url != null && url.length > 0) {
                            JsCode.layer_close();
                        } else {
                            var tourl = $(".valid-tourl").html();
                            if (tourl != null && tourl.length > 0) {
                                window.location.href = tourl;
                            }
                        }
                    });
                }
            },
            InitDelete: function () {
                $("a[name='delete']").click(function () {
                    var btn = $(this);
                    var ctr = $(this).html();
                    layer.confirm('请确定 ' + ctr + ' 操作？', {
                        btn: ['确定', '取消']
                    }, function () {
                        DoAction($(btn).attr('data-ID'));
                    }, function () {
                        layer.closeAll();
                    });
                });
            },
            post: function (url, data, success) {
                $.ajax({
                    type: "POST",
                    url: url,
                    data: data,
                    success: success
                });
            },
            getQueryString: function (name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
                var r = window.location.search.substr(1).match(reg);
                if (r != null) return unescape(r[2]); return null;
            },
            InitEdit: function () {
                $(".linkEdit").click(function () {
                    var url = $(this).attr('_href');
                    var _width = $(this).attr('_width');
                    var _height = $(this).attr('_height');

                    if (_width == undefined) {
                        _height = '400px';
                        _width = '400px';
                    }

                    var index = layer.open({
                        type: 2,
                        area: [_width, _height],
                        fix: true, //不固定
                        maxmin: false,
                        shade: 0.4,
                        title: '',
                        content: url
                    });
                })
            },
            layer_close: function () {
                if (parent != undefined) {
                    parent.window.location.reload();
                }
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            },
            layer_onlyclose: function () {
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            },
            DoRestful: function (api_type, api_url, api_data, success_do) {
                var clientId = "EQ123";
                var clientSecret = "EQPWD123";
                $.ajax({
                    url: "/Token",
                    type: "post",
                    data: { "grant_type": "client_credentials" },
                    dataType: "json",
                    headers: {
                        "Authorization": "Basic " + JsCode.Base64_Encode(clientId + ":" + clientSecret)
                    },
                    success: function (data) {
                        var accessToken = data.access_token;
                        $.ajax({
                            type: api_type,
                            url: api_url,
                            contentType: 'application/json',
                            headers: {
                                "Authorization": "Bearer " + accessToken
                            },
                            data: JSON.stringify(api_data),
                            success: function (r, status) {
                                //console.log(r);
                                if (r.IsSuccess) {
                                    success_do(r);
                                }
                                else {
                                    layer.alert(r.Message, 0);
                                    return false;
                                }
                            }
                        });
                    }
                });
            },
            Base64_Encode: function (str) {
                var c1, c2, c3;
                var base64EncodeChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
                var i = 0, len = str.length, string = '';

                while (i < len) {
                    c1 = str.charCodeAt(i++) & 0xff;
                    if (i === len) {
                        string += base64EncodeChars.charAt(c1 >> 2);
                        string += base64EncodeChars.charAt((c1 & 0x3) << 4);
                        string += "==";
                        break;
                    }
                    c2 = str.charCodeAt(i++);
                    if (i === len) {
                        string += base64EncodeChars.charAt(c1 >> 2);
                        string += base64EncodeChars.charAt(((c1 & 0x3) << 4) | ((c2 & 0xF0) >> 4));
                        string += base64EncodeChars.charAt((c2 & 0xF) << 2);
                        string += "=";
                        break;
                    }
                    c3 = str.charCodeAt(i++);
                    string += base64EncodeChars.charAt(c1 >> 2);
                    string += base64EncodeChars.charAt(((c1 & 0x3) << 4) | ((c2 & 0xF0) >> 4));
                    string += base64EncodeChars.charAt(((c2 & 0xF) << 2) | ((c3 & 0xC0) >> 6));
                    string += base64EncodeChars.charAt(c3 & 0x3F);
                }
                return string;
            }
        }
    }();

    window.JsCode = JsCode;
    Date.prototype.format = JsCode.format;
})(jQuery);
