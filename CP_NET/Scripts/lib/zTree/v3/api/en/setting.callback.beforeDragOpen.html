<div class="apiDetail">
<div>
	<h2><span>Function(treeId, treeNode)</span><span class="path">setting.callback.</span>beforeDragOpen</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.exedit</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Callback executed before drag node to collapsed parent node, The return value controls the auto expand behaviour of the parent node.</p>
			<p>Default: null</p>
		</div>
	</div>
	<h3>Function Parameter Descriptions</h3>
	<div class="desc">
	<h4><b>treeId</b><span>String</span></h4>
	<p>zTree unique identifier: <b class="highlight_red">treeId</b>, the tree is what the treeNode(parent node) is belong to.</p>
	<h4 class="topLine"><b>treeNode</b><span>JSON</span></h4>
	<p>JSON data object of the parent node which will be auto expanded</p>
	<h4 class="topLine"><b>Return </b><span>Boolean</span></h4>
	<p>return true or false</p>
	<p class="highlight_red">If return false, zTree will not auto expand parent node.</p>
	</div>
	<h3>Examples of setting & function</h3>
	<h4>1. disable to auto expand parent node.</h4>
	<pre xmlns=""><code>function myBeforeDragOpen(treeId, treeNode) {
    return false;
};
var setting = {
	edit: {
		enable: true
	},
	callback: {
		beforeDragOpen: myBeforeDragOpen
	}
};
......</code></pre>
</div>
</div>