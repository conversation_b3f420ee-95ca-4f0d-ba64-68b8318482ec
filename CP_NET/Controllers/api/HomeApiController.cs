using Models;
using System;
using System.Web;
using System.Web.Http;

namespace CP_NET.Controllers
{
    public class HomeApiController : ApiController
    {
        [HttpPost]
        [Authorize]
        public object MobileUploadImage(dynamic obj)
        {
            ResultModel res = ResultOfUploadImage(obj);

            //处理完毕，返回JOSN格式的文件信息
            ApiResultModel api_res = new ApiResultConvent().Convert(res);
            return api_res;
        }


        [HttpPost]
        [Authorize]
        public object WebUploadImage(dynamic obj)
        {
            ResultModel res = ResultOfUploadImage(obj);
            return res;
        }

        private ResultModel ResultOfUploadImage(dynamic obj)
        {
            ResultModel res = new ResultModel { IsSuccess = true, Message = "上传成功" };
            try
            {
                string file_path = "/PublicImg/RecordPic/" + DateTime.Now.ToString("yyyyMMdd") + "/";
                string real_path = HttpContext.Current.Server.MapPath(file_path);

                string file_base = Convert.ToString(obj.file_base);
                string file_name = DateTime.Now.ToString("yyyyMMddHHmmss") + Utils.Md5Helper.CreateRandomCode(4) + ".jpg";

                Utils.PicUploadHelper.UploadPicByBase64(file_base, file_name, real_path, true);

                string result_url = file_path + file_name;
                string result_url_s = Utils.ImageCutHelper.GetThumbnailFileName(result_url);
                res.Message = result_url_s + "," + result_url;
            }
            catch (Exception ex)
            {
                res.IsSuccess = false;
                res.Message = ex.Message;
            }

            return res;
        }
    }
}