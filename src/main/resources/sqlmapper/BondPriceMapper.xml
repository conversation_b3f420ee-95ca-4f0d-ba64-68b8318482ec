<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.csci.exam.mapper.BondPriceMapper">

  <insert id="insert" parameterType="com.csci.exam.model.BondPrice">
    insert into BondData (BondSin, GetDate, Price)
    values (#{Sin,jdbcType=VARCHAR}, #{GetDate,jdbcType=TIMESTAMP},
      #{Price,jdbcType=VARCHAR})
  </insert>

  <select id="getTodayData" resultType="com.csci.exam.model.BondPrice">
    SELECT ID,
    BondSin [Sin],
    GetDate,
    Price,
    IsSend,
    SendDate
    FROM dbo.BondData
    WHERE CONVERT(VARCHAR(10), GetDate, 120) = CONVERT(VARCHAR(10), GETDATE(), 120);
  </select>

</mapper>