server:
  port: 18090
swagger:
  request:
    host: "bot.csci.com.hk/bot-base"
#    host: "localhost:${server.port}"
chrome:
  driver:
    path: C:/chromedriver.exe
#funds
datasource:
  exam:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      jdbc-url: *****************************************************************************************************************
      driver-class-name: org.mariadb.jdbc.Driver
      username: fundsauto_prod
      password: '!csci3311#'
      connection-timeout: 60000
      connection-test-query: select 1
  invest:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      jdbc-url: **************************************************************************************************
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      username: common
      password: gn9gnahc14
      connection-timeout: 60000
      connection-test-query: select 1
# remote driver url
selenium:
  remote:
    driver:
      url: http://**********:4444/wd/hub
# excel config
excel:
  file:
    path: /tmp/excel
# cwyth
cwyth:
  site: https://fip.cscec.com/OSPPortal/CSCPortal.jsp?locale=zh_CN
  username: 20226015
  password: Fip*Cwyth2050
exclude:
  company: 海宏技術有限公司,中国海外保险,中国海外保险顾问
  #  ,中建宏達投資有限公司
  retry:
    error: 已开通直联查询,流水已存在

env:
  key: prod

pulsar:
  serviceURL: pulsar://**********:6650
  topic:
    name: persistent://public/default/bot-base-prod
  subscription:
    name: bot-base

app:
  name: bot-base

notify:
  email: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

mc:
  url: https://msg.csci.com.hk/mc/message/save

failed:
  record:
    download:
      url: https://bot.csci.com.hk/bot-base/funds/v1/downloadFiled/

mq:
  consumer:
    ip: **********

target:
  page:
    url: https://training.cscec.com/

spring:
  redis:
    host: **********
    port: 6379
    password: Passw0rd