package com.csci.exam.aspectj;

import com.csci.exam.annotation.DS;
import com.csci.exam.configuration.DataSourceContextHolder;
import com.csci.exam.configuration.DatasourceContextEnum;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * Aop 数据源注入配置
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 10/17/2019
 */
@Aspect
@Component
@Order(-1)
public class DatasourceDeterminer {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(DatasourceDeterminer.class);

    @Around(value = "@within(com.csci.exam.annotation.DS) || @annotation(com.csci.exam.annotation.DS)")
    public Object detemineDatabaseContext(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            /*if (!checkClass(joinPoint)) {
                // 如果类级别已经配置了数据源，则方法级别不再检查，提升效率
                checkMethod(joinPoint);
            }*/
            // 当类和方法都有设置datasource时，以方法为准
            checkClass(joinPoint);
            checkMethod(joinPoint);
            return joinPoint.proceed();
        } catch (Throwable throwable) {
            logger.error("DatasourceDeterminer#detemineDatabaseContext, exception caught");
            throw throwable;
        } finally {
            DataSourceContextHolder.remove();
        }
    }

    private boolean checkClass(ProceedingJoinPoint joinPoint) {
        try {
            if (joinPoint.getTarget().getClass().isAnnotationPresent(DS.class)) {
                DS ds = joinPoint.getTarget().getClass().getAnnotation(DS.class);
                DatasourceContextEnum dsKey = ds.value();
                DataSourceContextHolder.set(dsKey);
                return true;
            }
        } catch (Exception e) {
            logger.error("确定数据源出错，不影响主流程", e);
        }
        return false;
    }

    private void checkMethod(ProceedingJoinPoint joinPoint) {
        try {
            MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
            Method method = methodSignature.getMethod();
            if (method.isAnnotationPresent(DS.class)) {
                DS ds = method.getAnnotation(DS.class);
                DataSourceContextHolder.set(ds.value());
            }
        } catch (Exception e) {
            logger.error("确定数据源出错，不影响主流程", e);
        }
    }
}
