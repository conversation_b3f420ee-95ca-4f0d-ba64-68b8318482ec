package com.csci.exam.job;

import com.csci.exam.bot.impl.InvestBot;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.ArrayList;
import java.util.List;

@ConditionalOnProperty(value = "app.scheduling.enable", havingValue = "true", matchIfMissing = true)
@Configuration
@EnableScheduling
public class DayJob {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(DayJob.class);

    /**
     * 自动抓取银行汇率
     * 每天早上8點-9点每10分钟抓取一次
     */
    @Scheduled(cron = "0 0,10,20,40,50 08 * * ?")
    public void fetchRateEveryday() {

        InvestBot investBot = new InvestBot();
        List<String> bondList = new ArrayList<>();
        bondList.add("USY3422VCT36");
        bondList.add("XS0852986313");
        bondList.add("XS1075180379");
        bondList.add("XS1788513734");
        bondList.add("XS1596795358");
        bondList.add("XS1750118462");
        bondList.add("XS1974522937");
        bondList.add("XS1958532829");

        for (String bondCode : bondList) {
            investBot.SIN = bondCode;
            try {
                investBot.start();
            } catch (Exception e) {
                investBot.start();
            }
        }
    }

    /**
     * 测试定时任务是否正常
     */
    @Scheduled(cron = "0/5 * * * * ?")
    public void testSchedule() {
        //输出 heart beat
        System.out.println("heart beat");
    }

}
