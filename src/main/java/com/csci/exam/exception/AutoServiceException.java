package com.csci.exam.exception;

import com.csci.common.exception.ServiceException;

public class AutoServiceException extends ServiceException {

    private int code;

    public AutoServiceException(String message) {
        super(message);
    }

    public AutoServiceException(String message, Throwable throwable) {
        super(message, throwable);
    }

    public AutoServiceException(int code, String message) {
        super(message);
        this.code = code;
    }

    public AutoServiceException(int code, String message, Throwable throwable) {
        super(message, throwable);
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
