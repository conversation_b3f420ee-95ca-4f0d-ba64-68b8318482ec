package com.csci.exam.bot.impl;

import com.csci.exam.bot.CustomElement;
import com.csci.exam.bot.IAutomatic;
import com.csci.exam.util.SpringContextUtil;
import com.csci.common.exception.ServiceException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.openqa.selenium.*;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;

import java.net.MalformedURLException;
import java.net.URL;
import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public abstract class AbstractBaseAuto implements IAutomatic {

    static final Integer DEFAULT_WAIT_TIME = 90;
    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(AbstractBaseAuto.class);

    protected WebDriver driver;

    protected Actions actions;

    protected String url;

    protected String username;

    protected String password;

    /**
     * 判斷是否是生產環境
     *
     * @return
     */
    public static boolean isProd() {
        Environment environment = SpringContextUtil.getBean(Environment.class);
        String activeProfile = environment.getProperty("env.key");
        return StringUtils.equalsIgnoreCase("prod", activeProfile);
    }

    @Override
    public void init() {
        initDriver();
    }

    @Override
    public void destroy() {
        if (Objects.nonNull(driver)) {
            driver.close();
            driver.quit();
        }
    }

    private String getChromeDriverPath() {
        Environment environment = SpringContextUtil.getBean(Environment.class);
        String driverPath = environment.getProperty("chrome.driver.path");
        if (StringUtils.isBlank(driverPath)) {
            driverPath = Optional.ofNullable(AbstractBaseAuto.class.getClassLoader().getResource("chromedriver.exe"))
                    .map(URL::getPath).orElseThrow(() -> new ServiceException("未找到chrome驱动程序"));
        }
        return driverPath;
    }

    private WebDriver getChromeDriver() throws MalformedURLException {
        System.setProperty("webdriver.chrome.driver", getChromeDriverPath());
        ChromeOptions options = new ChromeOptions();
        options.addArguments("start-maximized"); // https://stackoverflow.com/a/26283818/1689770
        options.addArguments("--lang=zh");
        // options.addArguments("enable-automation"); // https://stackoverflow.com/a/43840128/1689770
        // options.addArguments("--headless"); // only if you are ACTUALLY running headless
        // options.addArguments("--no-sandbox"); //https://stackoverflow.com/a/50725918/1689770
        // options.addArguments("--disable-infobars"); //https://stackoverflow.com/a/43840128/1689770
        // options.addArguments("--disable-dev-shm-usage"); //https://stackoverflow.com/a/50725918/1689770
        // options.addArguments("--disable-browser-side-navigation");
        // options.addArguments("--disable-gpu");
        options.setPageLoadStrategy(PageLoadStrategy.NORMAL);

        Environment environment = SpringContextUtil.getBean(Environment.class);
        String remoteDriverURL = environment.getProperty("selenium.remote.driver.url");
        if (isProd()) {
            // 生產環境則不用彈出界面，使用遠程驅動
            if (SystemUtils.IS_OS_WINDOWS) {
                return new ChromeDriver(options);
            } else {
                return new RemoteWebDriver(new URL(remoteDriverURL), options);
            }
        } else {
            // 本地開發環境則展示界面
            return new ChromeDriver(options);
            // return new RemoteWebDriver(new URL("http://localhost:4444/wd/hub"), options);
        }
    }

    /**
     * 初始化驱动
     */
    protected void initDriver() {
        try {
            this.driver = getChromeDriver();
        } catch (Exception e) {
            logger.error("AbstractBaseAuto#initDriver, error", e);
            throw new ServiceException("初始化webdriver失败");
        }
        actions = new Actions(driver);
        driver.manage().timeouts().pageLoadTimeout(20, TimeUnit.SECONDS);
        // driver.manage().timeouts().implicitlyWait(10, TimeUnit.SECONDS);
    }

    CustomElement getPresentElement(String xpath) {
        return getPresentElement(xpath, DEFAULT_WAIT_TIME);
    }

    CustomElement getPresentElement(String xpath, String errorMsg) {
        return getPresentElement(xpath, DEFAULT_WAIT_TIME, errorMsg);
    }

    CustomElement getPresentElement(String xpath, long seconds) {
        return getPresentElement(xpath, seconds, null);
    }

    CustomElement getPresentElement(String xpath, long seconds, String errorMsg) {
        try {
            WebElement element = new WebDriverWait(driver, Duration.ofSeconds(seconds)).until(ExpectedConditions.presenceOfElementLocated(By.xpath(xpath)));
            return new CustomElement(element, xpath);
        } catch (Exception e) {
            // logger.error("AbstractBaseAuto#getPresentElement, error", e);
            if (StringUtils.isBlank(errorMsg)) {
                throw new ServiceException("未找到指定的元素，xpath=" + xpath);
            } else {
                throw new ServiceException(errorMsg);
            }
        }
    }

    List<WebElement> getPresentElements(String xpath, long seconds) {
        return getPresentElements(xpath, seconds, null);
    }

    List<WebElement> getPresentElements(String xpath) {
        return getPresentElements(xpath, DEFAULT_WAIT_TIME, null);
    }

    List<WebElement> getPresentElements(String xpath, long seconds, String errorMsg) {
        try {
            return new WebDriverWait(driver, Duration.ofSeconds(seconds)).until(ExpectedConditions.presenceOfAllElementsLocatedBy(By.xpath(xpath)));
        } catch (Exception e) {
            // logger.error("AbstractBaseAuto#getPresentElements, error", e);
            if (StringUtils.isBlank(errorMsg)) {
                throw new ServiceException("未找到指定的元素，xpath=" + xpath);
            } else {
                throw new ServiceException(errorMsg);
            }
        }
    }

    /**
     * 获取到可以点击的元素
     *
     * @param xpath
     * @return
     */
    CustomElement getClickableElement(String xpath) {
        return getClickableElement(xpath, DEFAULT_WAIT_TIME);
    }

    CustomElement getClickableElement(String xpath, String errorMsg) {
        return getClickableElement(xpath, DEFAULT_WAIT_TIME, errorMsg);
    }

    CustomElement getClickableElement(String xpath, long seconds) {
        return getClickableElement(xpath, seconds, null);
    }

    CustomElement getClickableElement(String xpath, long seconds, String errorMsg) {
        try {
            WebElement webElement = new WebDriverWait(driver, Duration.ofSeconds(seconds)).until(ExpectedConditions.elementToBeClickable(By.xpath(xpath)));
            return new CustomElement(webElement, xpath);
        } catch (Exception e) {
            // logger.error("AbstractBaseAuto#getClickableElement, error", e);
            if (StringUtils.isBlank(errorMsg)) {
                throw new ServiceException("未找到指定的元素，xpath=" + xpath);
            } else {
                throw new ServiceException(errorMsg);
            }
        }
    }

    CustomElement getVisibleElement(String xpath) {
        return getVisibleElement(xpath, DEFAULT_WAIT_TIME);
    }

    CustomElement getVisibleElement(String xpath, String errorMsg) {
        return getVisibleElement(xpath, DEFAULT_WAIT_TIME, errorMsg);
    }

    CustomElement getVisibleElement(String xpath, long seconds) {
        return getVisibleElement(xpath, seconds, null);
    }

    CustomElement getVisibleElement(String xpath, long seconds, String errorMsg) {
        try {
            WebElement element = new WebDriverWait(driver, Duration.ofSeconds(seconds)).until(ExpectedConditions.visibilityOfElementLocated(By.xpath(xpath)));
            return new CustomElement(element, xpath);
        } catch (Exception e) {
            // logger.error("AbstractBaseAuto#getVisibleElement, error", e);
            if (StringUtils.isBlank(errorMsg)) {
                throw new ServiceException("未找到指定的元素，xpath=" + xpath);
            } else {
                throw new ServiceException(errorMsg);
            }
        }
    }

    List<CustomElement> getVisibleElements(String xpath, long seconds) {
        return getVisibleElements(xpath, seconds, null);
    }

    List<CustomElement> getVisibleElements(String xpath, long seconds, String errorMsg) {
        try {
            List<WebElement> lstElement = new WebDriverWait(driver, Duration.ofSeconds(seconds)).until(ExpectedConditions.visibilityOfAllElementsLocatedBy(By.xpath(xpath)));
            return lstElement.stream().map(x -> new CustomElement(x, xpath)).collect(Collectors.toList());
        } catch (Exception e) {
            if (StringUtils.isBlank(errorMsg)) {
                throw new ServiceException("未找到指定的元素, xpath=" + xpath);
            } else {
                throw new ServiceException(errorMsg);
            }
        }
    }

    void alertOk() {
        alertOk(DEFAULT_WAIT_TIME);
    }

    void alertOk(long seconds) {
        new WebDriverWait(driver, Duration.ofSeconds(seconds)).until(ExpectedConditions.alertIsPresent());
        Alert alert = driver.switchTo().alert();
        alert.accept();
    }

    String getAlertText(long seconds) {
        new WebDriverWait(driver, Duration.ofSeconds(seconds)).until(ExpectedConditions.alertIsPresent());
        Alert alert = driver.switchTo().alert();
        return alert.getText();
    }

    void alertCancel(long seconds) {
        new WebDriverWait(driver, Duration.ofSeconds(seconds)).until(ExpectedConditions.alertIsPresent());
        Alert alert = driver.switchTo().alert();
        alert.dismiss();
    }

    void sleep(long ms) {
        try {
            Thread.sleep(ms);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    protected void doubleClickByXpath(String xpath) {
        doubleClick(getClickableElement(xpath));
    }

    protected void doubleClickByXpath(String xpath, long seconds) {
        doubleClick(getClickableElement(xpath, seconds));
    }

    protected void doubleClick(CustomElement element) {
        actions.moveToElement(element.getElement()).doubleClick().build().perform();
    }

    protected void actionClick(WebElement element) {
        actions.moveToElement(element).click().build().perform();
    }

    protected void doClick(CustomElement element) {
        doClick(element, 0);
    }

    protected void doClick(CustomElement element, int count) {
        if (count == 10) {
            String errorMsg = "尝试了十次还是出错，无法对该元素执行点击操作: ";
            if (StringUtils.isBlank(element.getErrorMsg())) {
                errorMsg += element.getXpath();
            } else {
                errorMsg += element.getErrorMsg();
            }
            throw new ServiceException(errorMsg);
        }
        try {
            element.getElement().click();
        } catch (Exception e) {
            sleep(1000);
            doClick(element, count + 1);
        }
    }


    protected void clickByXpath(String xpath, String symbol, String replacement) {
        String strSelectedRowLink = xpath.replaceAll(symbol, replacement);
        clickByXpath(strSelectedRowLink);
    }

    protected void clickByXpath(String xpath) {
        CustomElement element = getClickableElement(xpath);
        doClick(element);
    }

    protected void clickByXpath(String xpath, long seconds) {
        CustomElement element = getClickableElement(xpath, seconds);
        doClick(element);
    }

    protected void clickByXpath(String xpath, long seconds, String errorMsg) {
        CustomElement element = getClickableElement(xpath, seconds, errorMsg);
        doClick(element);
    }

    protected void clickByXpathByTimes(String xpath, int times) {
        for (int i = 0; i < times; i++) {
            try {
                doClick(getClickableElement(xpath));
                break;
            } catch (Exception e) {
                // ignore
                if (i == times - 1) {
                    throw e;
                }
            }
        }
    }

    protected void closeNotice() {
        waitWhileLoading();
        try {
            clickByXpath("//*[text()='通知公告']/following::*[text()='确 定']", 30);
        } catch (Exception e) {
            // ignore; means notice window does not exist
        }
        /*boolean loading = true;
        int count = 0;
        while (true) {
            if (count == 10) {
                throw new ServiceException("加载首页失败！");
            }
            try {
                clickByXpath("//span[text()='通知公告']/following::p[text()='确定']", 3);
                break;
            } catch (Exception e) {
                // ignore
                if (!loading) {
                    // 如果已经不在加载状态中了，这里表示通知窗口已经关闭的情况下
                    break;
                }
            }
            try {
                // 如果我的单据按钮处于可点击的状态，则表示通知窗口已经关闭了
                getClickableElement("//label[text()='我的单据']/parent::div", 1);
                loading = false;
            } catch (Exception e) {
                // ignore
            }
            count++;
        }*/
    }

    protected void waitWhileLoading() {
        sleep(100);
        CustomElement loading = null;
        try {
            loading = getPresentElement("html[@class='nprogress-busy']", 1);
        } catch (Exception e) {
            // ignore
        }
        while (Objects.nonNull(loading)) {
            logger.info("loading...");
            sleep(500);
            try {
                loading = getPresentElement("html[@class='nprogress-busy']", 1);
            } catch (Exception e) {
                // 未找到加载中的class表示加载完成了，因为加载中的时候html的class会被換成nprogress-busy
                loading = null;
                // ignore
            }
        }
        logger.info("load successful...");
    }

    protected void openSite() {
        driver.get(url);
    }

    protected void login() {
        try {
            driver.switchTo().frame("qrcont");
            CustomElement customElement = getPresentElement("//div[@id='policy1']//*[text()='密码登录']", 10);
            actionClick(customElement.getElement());
        } catch (Exception e) {
            logger.error("未找到密码登录框", e);
            throw e;
        }

        WebElement eleUsername = getEleUserName();
        eleUsername.sendKeys(username);
        WebElement elePassword = getElePassword();
        elePassword.sendKeys(password);

        clickByXpath("//form[@id='loginways8']//button[text()='登录']");

        waitWhileLoading();

        checkWrongPassword();

        try {
            clickByXpath("//p[contains(text(),'该用户在其他客户端已经登录')]/following::span[contains(text(),'否')]", 1);
        } catch (Exception e) {
            // ignore
        }
    }

    private WebElement getEleUserName() {
        return getVisibleElement("//form[@id='loginways8']//input[@name='username']");
        // return driver.findElement(By.id("username"));
    }

    private WebElement getElePassword() {
        return getVisibleElement("//form[@id='loginways8']//input[@name='authcode']");
    }

    private void checkWrongPassword() {
        WebElement wrongMsg = null;
        try {
            wrongMsg = getVisibleElement("//span[contains(text(),'用户名或密码错误')]", 1);
        } catch (ServiceException e) {
            // 进入此处表示找不到错误提示，则不用管，继续后续的逻辑
        }
        if (Objects.nonNull(wrongMsg)) {
            throw new ServiceException("密码错误，表示该账号已经设置过密码了");
        }
    }
}
