package com.csci.exam.bot.impl;

import com.csci.exam.bot.CustomElement;
import com.csci.exam.model.BondPrice;
import com.csci.exam.service.BondPriceService;
import com.csci.exam.util.SpringContextUtil;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public class InvestBot extends AbstractBaseAuto{

    /** 日志记录对象 */
    private static final Logger logger = LoggerFactory.getLogger(InvestBot.class);
    public static String SIN ="";
    private static String price ="";
    @Override
    public void process() {
        if(SIN.equals("USY3422VCT36")){

            url = "https://www.97caijing.com/#/primaryMarket/bondDetail/101951";
        }else if(SIN.equals("XS0852986313")){

            url = "https://www.97caijing.com/#/primaryMarket/bondDetail/2793";
        }else if(SIN.equals("XS1075180379")){

            url = "https://www.97caijing.com/#/primaryMarket/bondDetail/2463";
        }else if(SIN.equals("XS1788513734")){

            url = "https://www.97caijing.com/#/primaryMarket/bondDetail/978";
        }else if(SIN.equals("XS1596795358")){

            url = "https://www.97caijing.com/#/primaryMarket/bondDetail/1451";
        }else if(SIN.equals("XS1750118462")){

            url = "https://www.97caijing.com/#/primaryMarket/bondDetail/1077";
        }else if(SIN.equals("XS1974522937")){

            url = "https://www.97caijing.com/#/primaryMarket/bondDetail/386";
        }else if(SIN.equals("XS1958532829")){

            url = "https://www.97caijing.com/#/primaryMarket/bondDetail/438";
        }
        // 打开网站
        openSite();

        // 登录
        login();

        //获取数据
        getData();

        //保存数据
        record();
    }

    private void getData() {

        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        // 获取价格信息
        logger.info("获取价格信息...");
        CustomElement elementPrice = getVisibleElement("//*[@id=\"root\"]//div[@_nk=\"hDaQ2t\"]/div[2]");
        price = elementPrice.getText();

        logger.info(price);
    }

    private void record() {
        BondPriceService bondPriceService = SpringContextUtil.getBean(BondPriceService.class);
        try {
            List<BondPrice> list = bondPriceService.getTodayData();
            if(list.stream().anyMatch(i -> i.getSin().equalsIgnoreCase(SIN))){
                return;
            }

            BondPrice BondRecord = new BondPrice();
            BondRecord.setPrice(price);
            BondRecord.setGetDate(LocalDateTime.now());
            BondRecord.setSin(SIN);
            bondPriceService.insert(BondRecord);
            logger.info("record bonds: [{}]", SIN + "-"+price.toString());
        } catch (Exception e) {
            // ignore
            logger.error(e.getMessage(), e);
        }
    }


    @Override
    protected void login() {

        logger.info("知道了，继续体验...");
        clickByXpath("/html/body/div[3]/div/div[2]/div/div[2]/div[2]/div[2]/button");

        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        logger.info("开始登录...");
        clickByXpath("//*[@id=\"root\"]/div/section/div/header/div/div[2]/div[2]/div/button[1]/span");

        // 弹出网页内执行
        String mainWindowHandle = driver.getWindowHandle();
        Set<String> allWindowHandles = driver.getWindowHandles();

        // 切换到新打开的窗口
        for (String handle : allWindowHandles) {
            if (handle.equals(mainWindowHandle)) {
                driver.switchTo().window(handle);
                // 在新窗口中进行操作

                // 输入用户名
                logger.info("输入用户名...");
                CustomElement element = getVisibleElement("//*[@id='Phone']");
                element.sendKeys("15814667251");

                // 输入密码
                CustomElement passwd = getVisibleElement("//*[@id=\"Password\"]");
                passwd.sendKeys("82503002");

                // 点击登录按钮
                clickByXpath("//button[@_nk=\"d2FY6a\"]");
            }
        }

        logger.info("登录成功...");
    }
}
