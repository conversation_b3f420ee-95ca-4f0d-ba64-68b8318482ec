package com.csci.exam.util;

import org.apache.commons.lang3.StringUtils;

/**
 * Mybatis相关帮助类
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 10/14/2019
 */
public abstract class MybatisHelper {

    /**
     * 两边添加%
     *
     * @param value
     * @return
     */
    public static String like(String value) {
        return "%" + StringUtils.trimToEmpty(value) + "%";
    }

    /**
     * 左边添加%
     *
     * @param value
     * @return
     */
    public static String leftLike(String value) {
        return "%" + StringUtils.trimToEmpty(value);
    }

    /**
     * 右边添加%
     *
     * @param value
     * @return
     */
    public static String rightLike(String value) {
        return StringUtils.trimToEmpty(value) + "%";
    }
}
