package com.csci.exam.util;

import org.springframework.core.NamedThreadLocal;

import java.util.*;

/**
 * <AUTHOR>
 */
public class ContextUtils {

    private static final String KEY_BILL_STATE = "state";

    private static final String KEY_PRINT_FLAG = "printFlag";

    private static final String KEY_BANK_ACCOUNT = "bankAccount";

    private static final String KEY_HANDLED_BILLNO = "zzStartBillNo";

    private static final NamedThreadLocal<Map<String, Object>> requestContext = new NamedThreadLocal<>("context");

    public static void init() {
        requestContext.set(new HashMap<>());
    }

    public static void remove() {
        requestContext.remove();
    }

    static Map<String, Object> getContext() {
        Map<String, Object> context = requestContext.get();
        if (Objects.isNull(context)) {
            synchronized (ContextUtils.class) {
                if (Objects.isNull(requestContext.get())) {
                    context = new HashMap<>();
                    requestContext.set(context);
                }
            }
        }
        return context;
    }

    public static String getBillState() {
        return Optional.ofNullable(getContext()).map(x -> x.get(KEY_BILL_STATE)).map(String::valueOf).orElse(null);
    }

    public static void setBillState(String state) {
        getContext().put(KEY_BILL_STATE, state);
    }

    public static String getPrintFlag() {
        return Optional.ofNullable(getContext()).map(x -> x.get(KEY_PRINT_FLAG)).map(String::valueOf).orElse(null);
    }

    public static void setPrintFlag(String printFlag) {
        getContext().put(KEY_PRINT_FLAG, printFlag);
    }


    public static boolean isBillNoHandled(String billNo) {
        Set<String> handledBillNoSet = getBillNoSet();
        return handledBillNoSet.contains(billNo);
    }

    public static void addHandledBillNo(String billNo) {
        Set<String> handledBillNoSet = getBillNoSet();
        handledBillNoSet.add(billNo);
    }

    private static Set<String> getBillNoSet() {
        Set<String> billNoSet = Optional.ofNullable(getContext().get(KEY_HANDLED_BILLNO)).map(x -> (Set<String>) x).orElse(null);
        if (Objects.isNull(billNoSet)) {
            synchronized (ContextUtils.class) {
                billNoSet = Optional.ofNullable(getContext().get(KEY_HANDLED_BILLNO)).map(x -> (Set<String>) x).orElse(null);
                if (Objects.isNull(billNoSet)) {
                    billNoSet = new HashSet<>();
                    getContext().put(KEY_HANDLED_BILLNO, billNoSet);
                }
            }
        }
        return billNoSet;
    }
}
