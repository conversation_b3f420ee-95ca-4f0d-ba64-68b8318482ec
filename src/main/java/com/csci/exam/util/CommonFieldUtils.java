package com.csci.exam.util;

import com.csci.common.constant.CommonConsts;
import com.csci.common.util.CommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.apache.ibatis.mapping.SqlCommandType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 公共字段处理工具类
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 10/30/2019
 */
public class CommonFieldUtils {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(CommonFieldUtils.class);

    private CommonFieldUtils() {
        //    hide constructor
    }

    /**
     * 根据不同的sql类型设置通用字段，如果是insert语句，设置所有7个通用字段，如果是update语句，值设置更新相关的通用字段
     *
     * @param args
     * @param sqlCommandType
     */
    public static void processObject(Object args, SqlCommandType sqlCommandType) {
        if (args instanceof Collection) {
            processCollection((Collection<?>) args, sqlCommandType);
        } else if (args instanceof Map) {
            processMap((Map<?, ?>) args, sqlCommandType);
        } else {
            // 真正的通用字段处理逻辑
            // 获取参数所有定义的属性以及从父类继承的属性
            List<Field> fields = FieldUtils.getAllFieldsList(args.getClass());
            for (Field field : fields) {
                if (Objects.equals(SqlCommandType.INSERT, sqlCommandType)) {
                    handleInsert(field, args);
                } else if (Objects.equals(SqlCommandType.UPDATE, sqlCommandType)) {
                    handleUpdate(field, args);
                }
            }
        }
    }

    /**
     * 判断如果是新增时通用字段，则设置默认值
     *
     * @param field
     * @param args
     */
    private static void handleInsert(Field field, Object args) {
        String fieldName = field.getName();
        try {
            field.setAccessible(true);
            if (Objects.equals(CommonConsts.CommonField.ID, fieldName)) {
                // 如果新增操作时遇到id字段，且该字段的值为空时，默认为其设置一个id
                // 这里没有每次新增都生成一个id是因为像粮单，物资付办单等单据是通过接口同步过来的数据，设计时两方决定用同一个字段作为id
                Object value = field.get(args);
                if (Objects.equals(field.getType(), String.class)) {
                    if (value == null || StringUtils.isBlank(String.valueOf(value))) {
                        field.set(args, CommonUtils.randomUuid());
                    }
                }
            }
            if (Objects.equals(CommonConsts.CommonField.CREATION_TIME, fieldName)) {
                field.set(args, LocalDateTime.now());
            }
            if (Objects.equals(CommonConsts.CommonField.CREATE_USER_ID, fieldName)) {
                // todo 设置当前用户
                // field.set(args, ContextUtils.getCurrentUser().getEmployee().getCode());
            }
            if (Objects.equals(CommonConsts.CommonField.CREATE_USER_ACCOUNT, fieldName)) {
                // todo 当前用户账号
                // field.set(args, ContextUtils.getCurrentUser().getAccount());
            }
            if (Objects.equals(CommonConsts.CommonField.LAST_UPDATE_VERSION, fieldName)) {
                field.set(args, 0);
            }
            // 新增时也需要设置更新字段
            handleUpdate(field, args);
        } catch (IllegalAccessException e) {
            logger.error("CommonFieldInterceptor#handleInsert, 设置通用字段失败, fieldName: {}", fieldName);
        }
    }

    /**
     * 判断如果是更新时通用字段，则设置默认值
     *
     * @param field
     * @param args
     */
    private static void handleUpdate(Field field, Object args) {
        String fieldName = field.getName();
        try {
            field.setAccessible(true);
            if (Objects.equals(CommonConsts.CommonField.LAST_UPDATE_TIME, fieldName)) {
                field.set(args, LocalDateTime.now());
            }
            if (Objects.equals(CommonConsts.CommonField.LAST_UPDATE_USER_ID, fieldName)) {
                // todo 当前用户id
                // field.set(args, ContextUtils.getCurrentUser().getEmployee().getCode());
            }
            if (Objects.equals(CommonConsts.CommonField.LAST_UPDATE_USER_ACCOUNT, fieldName)) {
                // todo 当前用户账号
                // field.set(args, ContextUtils.getCurrentUser().getAccount());
            }
            if (Objects.equals(CommonConsts.CommonField.LAST_UPDATE_VERSION, fieldName)) {
                Object value = field.get(args);
                if (value != null) {
                    int targetValue = Integer.parseInt(String.valueOf(value));
                    field.set(args, targetValue + 1);
                }
            }
        } catch (Exception e) {
            String errorMsg = MessageFormat.format("CommonFieldUtils#handleUpdate, 设置通用字段失败, fieldName: [{0}]", fieldName);
            logger.error(errorMsg, e);
        }
    }

    /**
     * 对于参数集，对集合中每个元素分别处理
     *
     * @param argList
     * @param sqlCommandType
     */
    private static void processCollection(Collection<?> argList, SqlCommandType sqlCommandType) {
        argList.parallelStream().forEach(arg -> processObject(arg, sqlCommandType));
    }

    private static void processMap(Map<?, ?> args, SqlCommandType sqlCommandType) {
        args.values().parallelStream().forEach(arg -> processObject(arg, sqlCommandType));
    }

}
