package com.csci.exam.util;

import java.awt.*;
import java.awt.datatransfer.Clipboard;
import java.awt.datatransfer.DataFlavor;
import java.awt.datatransfer.Transferable;
import java.util.Objects;

public class ClipboardUtils {

    /**
     * 获取剪贴板内容
     *
     * @return
     */
    public static String getClipboardContent() {
        String content = "";
        Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
        Transferable transferable = clipboard.getContents(null);
        if (Objects.nonNull(transferable)) {
            if (transferable.isDataFlavorSupported(DataFlavor.stringFlavor)) {
                try {
                    content = (String) transferable.getTransferData(DataFlavor.stringFlavor);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return content;
    }
}
