package com.csci.exam.util.email;

import com.csci.common.constant.CommonConsts;
import com.csci.common.exception.ServiceException;
import com.csci.common.util.CommonUtils;
import com.csci.common.util.CustomGsonBuilder;
import com.csci.common.util.HttpSimulator;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class EmailSender {

    /**
     * 日志记录对象
     */
    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(EmailSender.class);

    private final Gson gson = CustomGsonBuilder.createGson();

    @Value("${app.name}")
    private String appName;

    @Value("${mc.url}")
    private String msgRequestUrl;

    /**
     * 发送 email
     *
     * @param title   标题
     * @param content 内容
     * @param email   待发送邮箱地址
     */
    public void sendMessage(String title, String content, String email) {
        if (StringUtils.isBlank(content)) {
            throw new ServiceException("请不要发送空的消息");
        }
        if (StringUtils.isBlank(email)) {
            throw new ServiceException("请指定需要发送的电子邮箱地址");
        }

        Message objMessage = new Message();
        objMessage.setContent(content);
        objMessage.setFromAppName(appName);
        objMessage.setMessageFormat("text");
        objMessage.setTitle(title);
        objMessage.setUrgencyLevel(1);
        UserInfo userInfo = new UserInfo();
        userInfo.setEmail(email);
        objMessage.setUserList(Collections.singletonList(userInfo));

        String strMessage = gson.toJson(objMessage);
        logger.info("url: {}, msg: {}", msgRequestUrl, strMessage);
        Map<String, String> headers = new HashMap<>();
        headers.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        String strResponse = HttpSimulator.sendPostRequest(msgRequestUrl, strMessage, headers);
        Integer code = Optional.of(gson.fromJson(strResponse, JsonObject.class)).map(x -> x.get("code")).map(JsonElement::getAsInt).orElse(null);
        if (Objects.equals(code, CommonConsts.RespStatus.SUCCESS)) {
            // ignore
        } else {
            throw new ServiceException(strResponse);
        }

    }

    public static void main(String[] args) {
        logger.info("local ip: {}", CommonUtils.getLocalIps());
    }

}
