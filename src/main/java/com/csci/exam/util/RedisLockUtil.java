package com.csci.exam.util;

import java.util.concurrent.TimeUnit;

//redis分布式锁
public final class RedisLockUtil {

    /**
     * redis锁默认过期时间，单位s
     */
    static final long REDIS_LOCK_DEFAULT_EXPIRE = 600;

    private RedisLockUtil() {
        //
    }

    /**
     * 加锁
     *
     * @param key    redis key
     * @param expire 过期时间，单位秒
     * @return true:加锁成功，false，加锁失败
     */
    public static boolean lockSimple(String key, int expire) {

        RedisCache redisCache = SpringContextUtil.getBean(RedisCache.class);
        boolean status = redisCache.setIfAbsent(key, "1");

        if (status) {
            redisCache.expire(key, expire, TimeUnit.SECONDS);
            return true;
        }

        return false;
    }

    /**
     * 加锁;
     * 默认两分钟过期
     *
     * @param key redis key
     * @return
     */
    public static boolean lock(String key) {
        return lock(key, REDIS_LOCK_DEFAULT_EXPIRE * 1000);
    }

    /**
     * 加锁
     *
     * @param key    redis key
     * @param expire 过期时间，单位毫秒
     * @return true:加锁成功，false，加锁失败
     */
    public static boolean lock(String key, long expire) {

        RedisCache redisCache = SpringContextUtil.getBean(RedisCache.class);

        long value = System.currentTimeMillis() + expire;
        // 如果该key值不存在于redis缓存中，则设置进去并返回true，否则返回false
        boolean status = redisCache.setIfAbsent(key, String.valueOf(value));

        if (status) {
            return true;
        }
        long oldExpireTime = Long.parseLong(String.valueOf(redisCache.get(key, "0")));
        if (oldExpireTime < System.currentTimeMillis()) {
            //超时
            long newExpireTime = System.currentTimeMillis() + expire;
            // 当前redis中保存的过期时间
            long currentExpireTime = Long.parseLong(String.valueOf(redisCache.getAndSet(key, String.valueOf(newExpireTime))));
            return currentExpireTime == oldExpireTime;
        }
        return false;
    }

    public static void unLockSimple(String key) {
        RedisCache redisCache = SpringContextUtil.getBean(RedisCache.class);
        redisCache.delete(key);
    }

    public static void unlock(String key) {
        RedisCache redisCache = SpringContextUtil.getBean(RedisCache.class);
        long oldExpireTime = Long.parseLong(String.valueOf(redisCache.get(key, "0")));
        if (oldExpireTime > System.currentTimeMillis()) {
            redisCache.delete(key);
        }
    }

}