package com.csci.exam.util;

import net.sourceforge.tess4j.ITesseract;
import net.sourceforge.tess4j.Tesseract1;
import net.sourceforge.tess4j.TesseractException;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class OcrUtils {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(OcrUtils.class);

    /**
     * 对图片进行OCR识别
     *
     * @param path 图片路径
     * @return
     */
    public static String ocr(String path) {
        ITesseract instance = new Tesseract1();
        // File tessDataFolder = LoadLibs.extractTessResources("tessdata");
        // instance.setDatapath(tessDataFolder.getPath());
        instance.setDatapath("C:\\project\\tessdata");
        try {
            String result = instance.doOCR(new File(path));
            System.out.println(result);
            logger.info("result: {}", result);
            return result;
        } catch (TesseractException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 通过python脚本进行OCR识别
     *
     * @param path 图片路径
     * @return
     */
    public static String pyOcr(String path) {
        try {
            Process proc = Runtime.getRuntime().exec(new String[]{"python", "C:\\project\\3311\\csciocr\\read_image.py", path});
            String result = IOUtils.toString(proc.getInputStream(), StandardCharsets.UTF_8);
            // logger.info("result: {}", result);
            proc.destroy();

            Pattern pattern = Pattern.compile("[{](.*)[}]");
            Matcher matcher = pattern.matcher(result);
            if (matcher.find()) {
                result = matcher.group(1);
                return StringUtils.trim(result);
            }
            return "";
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
