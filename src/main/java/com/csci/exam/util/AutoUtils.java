package com.csci.exam.util;

import com.csci.common.exception.ServiceException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

public final class AutoUtils {

    public static final String TEMPLATE_FILE_NAME = "template.xlsx";

    /**
     * @param target
     * @return
     */
    public static String removeComma(String target) {
        if (StringUtils.isBlank(target)) {
            throw new ServiceException("target cannot be empty");
        }
        return target.replaceAll("'", "");
        // return target;
    }

    /**
     * 当前自动化程序将使用的目录
     *
     * @return
     */
    public static Path getBasePath() {
        return Paths.get(SystemUtils.getUserHome().getPath()).resolve("import-excel-dir");
    }

    public static Path getTemplatePath() {
        return getBasePath().resolve(TEMPLATE_FILE_NAME);
    }

    public static void initBasePath() {
        try {
            Files.createDirectories(getBasePath());
        } catch (IOException e) {
            throw new ServiceException("初始化基础目录失败，该目录用于存放模板文件以及 excel 文件", e);
        }
    }

    public static void copyTemplateToBasePath() {
        ClassPathResource resource = new ClassPathResource(TEMPLATE_FILE_NAME, AutoUtils.class.getClassLoader());
        try (InputStream in = resource.getInputStream()) {
            Path path = getBasePath().resolve(TEMPLATE_FILE_NAME);
            if (!Files.exists(path)) {
                // 不存在则复制过去
                Files.copy(in, getBasePath().resolve(TEMPLATE_FILE_NAME), StandardCopyOption.REPLACE_EXISTING);
            }
        } catch (IOException e) {
            throw new ServiceException("初始化模板文件失败", e);
        }
    }

    /**
     * 按提供的符号截断公司名称, 保留第一个符号后面的字符串
     * eg: "asd - jkl" 执行该方法之后会剩余：" jkl"
     *
     * @param companyName
     * @return
     */
    public static String truncateCompanyName(String companyName, String symbol) {
        String result = companyName;
        if (StringUtils.contains(result, symbol)) {
            result = StringUtils.substring(result, result.indexOf(symbol) + 1);
        }
        // 移除空格
        result = StringUtils.trim(result);
        if (StringUtils.isBlank(result)) {
            return companyName;
        } else {
            return result;
        }
    }
}
