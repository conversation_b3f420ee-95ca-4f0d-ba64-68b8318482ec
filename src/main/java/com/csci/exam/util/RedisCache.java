package com.csci.exam.util;

import com.csci.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * to handle the redis cache
 *
 * <AUTHOR>
 * @date 2019-11-26
 */
@Component
public class RedisCache {

    private static final long DEFAULT_EXPIRE_TIME = 30;

    private static final long TIME_TO_REFRESH_EXPIRE = DEFAULT_EXPIRE_TIME / 2;

    private static final TimeUnit DEFAULT_EXPIRE_TIME_UNIT = TimeUnit.MINUTES;

    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * Constructor inject
     *
     * @param redisTemplate used for connecting to the cache service
     */
    @Autowired
    public RedisCache(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * set key-value to the redis cache, which will expired in 30 minutes
     *
     * @param key   the specific key
     * @param value the specified value
     */
    public void set(String key, Object value) {
        redisTemplate.opsForValue().set(key, value, DEFAULT_EXPIRE_TIME, DEFAULT_EXPIRE_TIME_UNIT);
    }

    public void set(String key, Object value, long timeout, TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
    }

    /**
     * get the value of the specified key in redis cache, and update the expire time of the object
     *
     * @param key the specific key
     * @return the value of the key
     */
    public Object get(String key) {
        Object value = redisTemplate.opsForValue().get(key);
        Long expire = redisTemplate.getExpire(key, DEFAULT_EXPIRE_TIME_UNIT);
        if (expire != null && expire <= TIME_TO_REFRESH_EXPIRE) {
            // 如果小于
            redisTemplate.expire(key, DEFAULT_EXPIRE_TIME, DEFAULT_EXPIRE_TIME_UNIT);
        }
        return value;
    }

    /**
     * get String value of a specified key in redis cache
     *
     * @param key the specified key
     * @return String value for the key
     */
    public String getString(String key) {
        Object value = get(key);
        return value == null ? null : String.valueOf(value);
    }

    /**
     * delete a specific key value pair from the redis cache
     *
     * @param key the specified key
     */
    public void delete(String key) {
        redisTemplate.delete(key);
    }

    /**
     * to check if the specified key is exist in redis cache
     *
     * @param key the specified key
     * @return a boolean value
     */
    public Boolean exists(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * set a key - value pair with specified timeout minutes
     *
     * @param key
     * @param value
     * @param timeout
     * @param unit
     */
    public void set(String key, String value, long timeout, TimeUnit unit) {
        redisTemplate.opsForValue().set(key, value, timeout, unit);
    }

    public void printExpire(String key) {
        System.out.println(MessageFormat.format("--- current time: {0}, key will expire in {1} seconds", DateUtils.toDatetimeString(LocalDateTime.now()), redisTemplate.getExpire(key)));
    }

    public Boolean setIfAbsent(String key, Object value) {
        return redisTemplate.opsForValue().setIfAbsent(key, value);
    }

    public Boolean expire(String key, long timeout, TimeUnit timeUnit) {
        return redisTemplate.expire(key, timeout, timeUnit);
    }

    public Object getAndSet(String key, Object value) {
        return redisTemplate.opsForValue().getAndSet(key, value);
    }

    public Object get(String key, Object defaultValue) {
        Object value = redisTemplate.opsForValue().get(key);
        return Objects.isNull(value) ? defaultValue : value;
    }
}
