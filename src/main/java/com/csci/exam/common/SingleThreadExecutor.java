package com.csci.exam.common;

import com.csci.common.parallel.ThreadPool;

import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

public class SingleThreadExecutor {

    private static final ThreadPool threadPool = new SingleThreadPool(120, TimeUnit.SECONDS, "Funds-SingleThreadPool");

    /**
     * 提交一个异步任务
     *
     * @param callable
     * @return
     */
    public static <T> Future<T> submit(Callable<T> callable) {
        return threadPool.submit(callable);
    }

    /**
     * 提交一个异步任务
     *
     * @param runnable
     * @return
     */
    public static Future<?> submit(Runnable runnable) {
        return threadPool.submit(runnable);
    }

    /**
     * 添加多个并行任务并等待执行完毕
     *
     * @param callables
     */
    public static void submitAndWait(Callable<?>... callables) {
        threadPool.submitAndWait(callables);
    }

    /**
     * 添加多个并行任务并等待执行完毕
     *
     * @param tasks
     */
    public static void submitAndWait(Runnable... tasks) {
        threadPool.submitAndWait(tasks);
    }

}
