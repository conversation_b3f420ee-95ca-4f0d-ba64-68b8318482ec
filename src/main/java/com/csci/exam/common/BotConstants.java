package com.csci.exam.common;

public interface BotConstants {

    /**
     * 自动导入流水重试最大次数
     */
    int RETRY_IMPORT_MAX_FAILED_COUNT = 10;

    /**
     * 展示的最大行数
     */
    int MAX_ROW_COUNT = 200;

    int SEARCH_MATCH_TIME_SEC = 10;

    /**
     * 遇到该错误码，直接退出程序
     */
    int EXIT_ERROR_CODE = 9999;

    /**
     * 定义错误码
     */
    interface ErrorCode {
        int INCREASE_ROW_INDEX = 100;
    }

    interface StartStep {
        int one = 1;
        int two = 2;
        int four = 4;
    }

    interface ExcelBatchStatus {
        int create = 1;
        int uploaded = 2;
        int processing = 3;
        int imported = 4;
        int importFailed = 9;
    }
}
