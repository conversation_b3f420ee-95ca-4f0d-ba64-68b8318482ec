package com.csci.exam.common;

import com.csci.common.parallel.ThreadPool;

import java.util.concurrent.TimeUnit;

/**
 * 异步单线程执行
 */
public class SingleThreadPool extends ThreadPool {

    public SingleThreadPool(long timeOut, TimeUnit timeUnit, String threadNamePrefix) {
        super(timeOut, timeUnit, threadNamePrefix);
    }

    @Override
    protected void initialize() {
        executor.setCorePoolSize(1);
        executor.setMaxPoolSize(1);
    }
}
