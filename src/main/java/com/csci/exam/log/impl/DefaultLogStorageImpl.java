package com.csci.exam.log.impl;

import com.csci.exam.log.ILogStorage;

import java.time.LocalDateTime;

public class DefaultLogStorageImpl implements ILogStorage {

    @Override
    public void setBasic(String className, String methodName, LocalDateTime enteringTime) {
    }

    @Override
    public void setParameter(String parameter) {
    }

    @Override
    public void setResult(String result) {
    }

    @Override
    public void setException(String exception) {
    }

    @Override
    public void setDuration(long duration) {
    }

    @Override
    public void store() {
        // ignore
    }
}
