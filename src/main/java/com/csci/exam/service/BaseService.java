package com.csci.exam.service;

import com.csci.common.exception.ServiceException;
import com.csci.common.model.ResultPage;
import com.csci.exam.annotation.LogMethod;
import com.csci.exam.mapper.BaseMapper;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@LogMethod
public class BaseService {

    @Autowired
    BaseMapper baseMapper;

    public ResultPage<Map> listByPage(String sql, int pageNo, int pageSize) {
        PageHelper.startPage(pageNo, pageSize);
        List<Map> lstMap = baseMapper.select(sql);
        return new ResultPage<>(lstMap);
    }

    public List<Map> list(String sql) {
        if (StringUtils.isBlank(sql)) {
            throw new ServiceException("请指定需要执行的sql脚本");
        }
        return baseMapper.select(sql);
    }

    public String selectTime() {
        List<Map> list = baseMapper.select("select current_timestamp as time");
        return new Gson().toJson(list.get(0));
    }
}
