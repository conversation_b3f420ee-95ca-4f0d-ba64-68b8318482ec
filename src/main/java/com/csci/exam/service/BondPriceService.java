package com.csci.exam.service;

import com.csci.exam.annotation.LogMethod;
import com.csci.exam.mapper.BondPriceMapper;
import com.csci.exam.model.BondPrice;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
@LogMethod

public class BondPriceService {

    @Resource
    private BondPriceMapper bondPriceMapper;

    @Transactional(rollbackFor = Exception.class)
    public int insert(BondPrice record) {
        return bondPriceMapper.insert(record);
    }

    public List<BondPrice> getTodayData() {
        return bondPriceMapper.getTodayData();
    }

}
