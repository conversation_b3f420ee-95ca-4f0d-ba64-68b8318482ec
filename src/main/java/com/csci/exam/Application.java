package com.csci.exam;

import com.csci.exam.job.DayJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;

@SpringBootApplication(exclude = {
        DataSourceAutoConfiguration.class
})
// @ComponentScan({"com.csci"})
public class Application {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(Application.class);


    public static void main(String[] args) {
        //System.setProperty("java.awt.headless", "false");
        ConfigurableApplicationContext ctx = SpringApplication.run(Application.class, args);
        //DayJob dayJob = ctx.getBean(DayJob.class);
        //dayJob.fetchRateEveryday();
    }

}