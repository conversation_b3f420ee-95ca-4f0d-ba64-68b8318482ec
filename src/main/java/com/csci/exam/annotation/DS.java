package com.csci.exam.annotation;

import com.csci.exam.configuration.DatasourceContextEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Generated Comments
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 10/17/2019
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface DS {
    DatasourceContextEnum value() default DatasourceContextEnum.EXAM;
    //DatasourceContextEnum value() default DatasourceContextEnum.INVEST;
}
