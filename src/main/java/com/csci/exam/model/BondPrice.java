package com.csci.exam.model;

import java.time.LocalDateTime;

public class BondPrice {


    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_exam_question.creation_time
     *
     * @mbg.generated
     */
    private LocalDateTime GetDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_exam_question.question_content
     *
     * @mbg.generated
     */
    private String Price;

    private String Sin;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_exam_question.creation_time
     *
     * @return the value of t_exam_question.creation_time
     *
     * @mbg.generated
     */
    public LocalDateTime getGetDate() {
        return GetDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_exam_question.creation_time
     *
     * @param creationTime the value for t_exam_question.creation_time
     *
     * @mbg.generated
     */
    public void setGetDate(LocalDateTime creationTime) {
        this.GetDate = creationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column bond.price
     *
     * @return the value of bond.price
     *
     * @mbg.generated
     */
    public String getPrice() {
        return Price;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column bond.price
     *
     * @param Price the value for bond.price
     *
     * @mbg.generated
     */
    public void setPrice(String Price) {
        this.Price = Price == null ? null : Price.trim();
    }

    public String getSin() {
        return Sin;
    }

    public void setSin(String Sin) {
        this.Sin = Sin == null ? null : Sin.trim();
    }
}