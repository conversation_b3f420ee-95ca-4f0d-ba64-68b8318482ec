package com.csci.exam.configuration;

import org.springframework.core.NamedInheritableThreadLocal;

/**
 * Generated Comments
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 10/17/2019
 */
public class DataSourceContextHolder {

    private static final ThreadLocal<DatasourceContextEnum> databaseKeyContext = new NamedInheritableThreadLocal<>("database-key-holder");

    public static void set(DatasourceContextEnum key) {
        databaseKeyContext.set(key);
    }

    public static DatasourceContextEnum get() {
        return databaseKeyContext.get();
    }

    public static void remove() {
        databaseKeyContext.remove();
    }
}
