package com.csci.exam.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;

/**
 * 配置swagger
 *
 * <AUTHOR>
 * @date 2019-09-19
 */
@Configuration
@EnableSwagger2
public class SwaggerConfig {

    @Value("${swagger.request.host}")
    private String host;

    @Bean
    public Docket productApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .select().apis(RequestHandlerSelectors.basePackage("com.csci.exam.controller"))
                .paths(PathSelectors.regex("/funds.*|/test.*|/cheque.*"))
                .build().apiInfo(metaData()).host(host);

    }

    private ApiInfo metaData() {
        return new ApiInfo(
                "CSCI micro service for sync-acct",
                "Provide the APIs manual for business system",
                "1.0",
                "Terms of service",
                new Contact("Tao Li", "", "<EMAIL>"), "", ""
                /*"Apache License Version 2.0",
                "https://www.apache.org/licenses/LICENSE-2.0"*/, new ArrayList<>());
    }
}