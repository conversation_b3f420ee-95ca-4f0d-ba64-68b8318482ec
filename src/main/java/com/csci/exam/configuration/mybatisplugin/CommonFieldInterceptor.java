package com.csci.exam.configuration.mybatisplugin;

import com.csci.exam.util.CommonFieldUtils;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.Properties;

/**
 * 通用字段设置，mybatis拦截插件实现
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 10/23/2019
 */
@Intercepts({
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})
})
public class CommonFieldInterceptor implements Interceptor {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(CommonFieldInterceptor.class);

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        try {
            MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
            Object args = invocation.getArgs()[1];
            // sql类型
            SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
            if (Objects.equals(SqlCommandType.INSERT, sqlCommandType) || Objects.equals(SqlCommandType.UPDATE, sqlCommandType)) {
                CommonFieldUtils.processObject(args, sqlCommandType);
            }
        } catch (Exception e) {
            logger.error("CommonFieldInterceptor#intercept, 通用字段设值出错", e);
        }
        return invocation.proceed();
    }


    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // nothing special
    }
}
