package com.csci.exam.configuration;

import com.csci.common.constant.CommonConsts;
import com.csci.common.exception.ServiceException;
import com.csci.common.model.ResultBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.WebRequest;

import java.util.ArrayList;
import java.util.Optional;

/**
 * 通用控制器异常处理配置
 * <p>
 *
 * <AUTHOR> terrytao
 * @date : 10/29/2019
 */
@ControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 日志记录对象
     */
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler({Exception.class})
    @ResponseBody
    public final ResultBean<?> handleException(Exception ex, WebRequest request) {
        logger.error("GlobalExceptionHandler#handleException, 错误记录", ex);

        if (ex instanceof ServiceException || ex instanceof IllegalArgumentException) {
            return new ResultBean<>(CommonConsts.RespStatus.FAIL, ex.getLocalizedMessage());
        }

        if (ex instanceof DuplicateKeyException) {
            return new ResultBean<>(CommonConsts.RespStatus.FAIL, "主键冲突，请不要添加重复的记录", ex.getLocalizedMessage());
        }

        // 参数验证失败的时候会抛出此异常
        if (ex instanceof MethodArgumentNotValidException) {
            StringBuilder sbErrorMsg = new StringBuilder();
            Optional.of(((MethodArgumentNotValidException) ex).getBindingResult()).map(Errors::getAllErrors).orElse(new ArrayList<>()).forEach(error -> {
                if (sbErrorMsg.length() > 0) {
                    sbErrorMsg.append(";");
                }
                sbErrorMsg.append(error.getDefaultMessage());
            });
            if (sbErrorMsg.length() == 0) {
                sbErrorMsg.append("参数验证失败");
            }
            return new ResultBean<>(CommonConsts.RespStatus.FAIL, sbErrorMsg.toString(), ex.getLocalizedMessage());
        }

        return new ResultBean<>(CommonConsts.RespStatus.FAIL, "出错了，请联系管理员解决", ex.getLocalizedMessage());
    }

}
