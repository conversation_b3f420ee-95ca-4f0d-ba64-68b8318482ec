package com.csci.exam.configuration;

import com.csci.exam.configuration.mybatisplugin.CommonFieldInterceptor;
import com.github.pagehelper.PageInterceptor;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * data base access configuration
 *
 * <AUTHOR>
 * @date 2020-08-22
 */
@Configuration
@MapperScan({"com.csci.exam.mapper"})
public class DatasourceConfiguration {

    @Bean
    @ConfigurationProperties(prefix = "datasource.exam.hikari")
    public DataSource examDataSource() {
        return new HikariDataSource();
    }
    @Bean
    @ConfigurationProperties(prefix = "datasource.invest.hikari")
    public DataSource investDatasource() {
        return new HikariDataSource();
    }
    @Bean
    public DataSource dataSource() {
        DynamicDataSource dataSource = new DynamicDataSource();
        DataSource exam = examDataSource();
        DataSource invest = investDatasource();
        dataSource.setDefaultTargetDataSource(invest);

        Map<Object, Object> dsStore = new HashMap<>(DatasourceContextEnum.values().length);
        dsStore.put(DatasourceContextEnum.EXAM, exam);
        dsStore.put(DatasourceContextEnum.INVEST, invest);
        dataSource.setTargetDataSources(dsStore);

        return dataSource;
    }

    @Bean
    public SqlSessionFactory sqlSessionFactoryBean() throws Exception {

        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource());

        // 分页插件
        // PageHelper pageHelper = new PageHelper();
        Properties properties = new Properties();
        properties.setProperty("reasonable", "true");
        properties.setProperty("supportMethodsArguments", "true");
        properties.setProperty("returnPageInfo", "check");
        properties.setProperty("params", "count=countSql");
        properties.setProperty("autoRuntimeDialect", "true");
        properties.setProperty("helperDialect", "sqlserver");
        // pageHelper.setProperties(properties);

        PageInterceptor pageInterceptor = new PageInterceptor();
        pageInterceptor.setProperties(properties);

        // 通用字段处理插件
        CommonFieldInterceptor commonFieldInterceptor = new CommonFieldInterceptor();
        // 添加插件
        sqlSessionFactoryBean.setPlugins(pageInterceptor, commonFieldInterceptor);

        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        sqlSessionFactoryBean.setMapperLocations(resolver.getResources("classpath:/sqlmapper/**/*.xml"));

        return sqlSessionFactoryBean.getObject();
    }

    @Bean
    public PlatformTransactionManager transactionManager() {
        return new DataSourceTransactionManager(dataSource());
    }
}
