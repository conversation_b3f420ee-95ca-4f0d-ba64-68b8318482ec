package com.csci.exam.bot.impl;

import com.csci.exam.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class InvestBotTest extends BaseTest {

    @Test
    void process() {
        InvestBot investBot = new InvestBot();
        List<String> bondList = new ArrayList<>();
        bondList.add("USY3422VCT36");
        bondList.add("XS0852986313");
        bondList.add("XS1075180379");
        bondList.add("XS1788513734");
        bondList.add("XS1596795358");
        bondList.add("XS1750118462");
        bondList.add("XS1974522937");
        bondList.add("XS1958532829");

        for (String bondCode : bondList) {
            investBot.SIN = bondCode;
            try {
                investBot.start();
            } catch (Exception e) {
                investBot.start();
            }
        }


    }
}