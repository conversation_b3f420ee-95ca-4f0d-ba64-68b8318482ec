package com.csci.exam.util;

import com.csci.exam.BaseTest;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static org.junit.jupiter.api.Assertions.*;

class RedisLockUtilTest extends BaseTest {

    /** 日志记录对象 */
    private static final Logger logger = LoggerFactory.getLogger(RedisLockUtilTest.class);

    @Test
    void lock() {
        boolean getLock = RedisLockUtil.lock("test", 1000);
        logger.info("getLock: {}", getLock);

        getLock = RedisLockUtil.lock("test", 1000);
        logger.info("getLock: {}", getLock);

    }
}