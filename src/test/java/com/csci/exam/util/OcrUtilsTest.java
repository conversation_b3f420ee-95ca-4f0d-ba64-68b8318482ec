package com.csci.exam.util;

import com.csci.exam.BaseTest;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class OcrUtilsTest extends BaseTest {

    @Test
    void ocr() {
        // String result = OcrUtils.ocr("C:\\data\\img.jpg");
        String result = OcrUtils.ocr("C:\\project\\tess4j\\src\\test\\resources\\test-data\\eurotext.png");
        System.out.println(result);
    }

    @Test
    void pyOcr() {
        String code = OcrUtils.pyOcr("C:\\data\\saved.png");
        System.out.println("code: " + code);

        // 根据正则表达式获取字符串
    }
}