package com.csci.exam.service;

import com.csci.exam.BaseTest;
import com.csci.exam.model.BondPrice;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

class BondPriceServiceTest extends BaseTest {

    @Resource
    BondPriceService bondPriceService;

    @Test
    void insert() {
        BondPrice record = new BondPrice();
        record.setSin("1");
        record.setGetDate(LocalDateTime.now());
        record.setSin("XS0852986313");
        bondPriceService.insert(record);

    }
}