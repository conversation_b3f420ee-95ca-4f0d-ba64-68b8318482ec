using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using iTextSharp.text;
using iTextSharp.text.pdf;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.Extensions;
using OpenQA.Selenium.Support.UI;

namespace SignPDF.PDF
{
    public class url
    {
        public static string Code(string Url)
        {
            string urlAddress = Url;

            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(urlAddress);
            HttpWebResponse response = (HttpWebResponse)request.GetResponse();

            if (response.StatusCode == HttpStatusCode.OK)
            {
                Stream receiveStream = response.GetResponseStream();
                StreamReader readStream = null;

                if (String.IsNullOrWhiteSpace(response.CharacterSet))
                    readStream = new StreamReader(receiveStream);
                else
                    readStream = new StreamReader(receiveStream, Encoding.GetEncoding(response.CharacterSet));

                string result = readStream.ReadToEnd();

                response.Close();
                readStream.Close();
                return result;

                
            }
            return "";
        }

        /// <summary>
        /// 將Html文字 輸出到PDF檔裡
        /// </summary>
        /// <param name="htmlText"></param>
        /// <returns></returns>
        //public static byte[] ConvertHtmlTextToPDF(string htmlText)
        //{
        //    if (string.IsNullOrEmpty(htmlText))
        //    {
        //        return null;
        //    }
        //    //避免當htmlText無任何html tag標籤的純文字時，轉PDF時會掛掉，所以一律加上<p>標籤
        //    htmlText = "<p>" + htmlText + "</p>";
        //    MemoryStream outputStream = new MemoryStream();//要把PDF寫到哪個串流
        //    System.Text.EncodingProvider ppp = System.Text.CodePagesEncodingProvider.Instance;
        //    Encoding.RegisterProvider(ppp);
        //    byte[] data = Encoding.UTF8.GetBytes(htmlText);//字串轉成byte[]
        //    MemoryStream msInput = new MemoryStream(data);
        //    Document doc = new Document();//要寫PDF的文件，建構子沒填的話預設直式A4
        //    PdfWriter writer = PdfWriter.GetInstance(doc, outputStream);
        //    //指定文件預設開檔時的縮放為100%
        //    PdfDestination pdfDest = new PdfDestination(PdfDestination.XYZ, 0, doc.PageSize.Height, 1f);
        //    //開啟Document文件 
        //    doc.Open();
        //    //使用XMLWorkerHelper把Html parse到PDF檔裡
        //    XMLWorkerHelper.GetInstance().ParseXHtml(writer, doc, msInput, null, Encoding.UTF8, new UnicodeFontFactory());
        //    //將pdfDest設定的資料寫到PDF檔
        //    PdfAction action = PdfAction.GotoLocalPage(1, pdfDest, writer);

        //    writer.SetOpenAction(action);
        //    msInput.Close();
        //    doc.Close();
        //    outputStream.Close();
        //    //回傳PDF檔案 
        //    return outputStream.ToArray();

        //}

        /// <summary>
        /// HTML文本内容转换为PDF
        /// </summary>
        /// <param name="strHtml">HTML文本内容</param>
        /// <param name="savePath">PDF文件保存的路径</param>
        /// <returns></returns>
        public static bool HtmlTextConvertToPdf(string strHtml, string savePath)
        {
            bool flag = false;
            try
            {
                string htmlPath = HtmlTextConvertFile(strHtml);

                flag = HtmlConvertToPdf(htmlPath, savePath);
                File.Delete(htmlPath);
            }
            catch
            {
                flag = false;
            }
            return flag;
        }

        /// <summary>
        /// HTML转换为PDF
        /// </summary>
        /// <param name="htmlPath">可以是本地路径，也可以是网络地址</param>
        /// <param name="savePath">PDF文件保存的路径</param>
        /// <returns></returns>
        public static bool HtmlConvertToPdf(string htmlPath, string savePath)
        {
            bool flag = false;
            CheckFilePath(savePath);

            ///这个路径为程序集的目录，因为我把应用程序 wkhtmltopdf.exe 放在了程序集同一个目录下
            string exePath = AppDomain.CurrentDomain.BaseDirectory.ToString() + "wkhtmltopdf.exe";
            if (!File.Exists(exePath))
            {
                throw new Exception("No application wkhtmltopdf.exe was found.");
            }

            try
            {
                ProcessStartInfo processStartInfo = new ProcessStartInfo();
                processStartInfo.FileName = exePath;
                processStartInfo.WorkingDirectory = Path.GetDirectoryName(exePath);
                processStartInfo.UseShellExecute = false;
                processStartInfo.CreateNoWindow = true;
                processStartInfo.RedirectStandardInput = true;
                processStartInfo.RedirectStandardOutput = true;
                processStartInfo.RedirectStandardError = true;
                processStartInfo.Arguments = GetArguments(htmlPath, savePath);

                Process process = new Process();
                process.StartInfo = processStartInfo;
                process.Start();
                process.WaitForExit();

                ///用于查看是否返回错误信息
                //StreamReader srone = process.StandardError;
                //StreamReader srtwo = process.StandardOutput;
                //string ss1 = srone.ReadToEnd();
                //string ss2 = srtwo.ReadToEnd();
                //srone.Close();
                //srone.Dispose();
                //srtwo.Close();
                //srtwo.Dispose();

                process.Close();
                process.Dispose();

                flag = true;
            }
            catch
            {
                flag = false;
            }
            return flag;
        }

        /// <summary>
        /// 获取命令行参数
        /// </summary>
        /// <param name="htmlPath"></param>
        /// <param name="savePath"></param>
        /// <returns></returns>
        private static string GetArguments(string htmlPath, string savePath)
        {
            if (string.IsNullOrEmpty(htmlPath))
            {
                throw new Exception("HTML local path or network address can not be empty.");
            }

            if (string.IsNullOrEmpty(savePath))
            {
                throw new Exception("The path saved by the PDF document can not be empty.");
            }

            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.Append(" --run-script ");
            stringBuilder.Append(" --images ");
            stringBuilder.Append(" --encoding BIG5 ");
            stringBuilder.Append(" --debug-javascript ");
            stringBuilder.Append(" --page-size A3 ");
            stringBuilder.Append(" " + htmlPath + " ");       //本地 HTML 的文件路径或网页 HTML 的URL地址
            stringBuilder.Append(" " + savePath + " ");       //生成的 PDF 文档的保存路径
            return stringBuilder.ToString();
        }

        /// <summary>
        /// 验证保存路径
        /// </summary>
        /// <param name="savePath"></param>
        private static void CheckFilePath(string savePath)
        {
            string ext = string.Empty;
            string path = string.Empty;
            string fileName = string.Empty;

            ext = Path.GetExtension(savePath);
            if (string.IsNullOrEmpty(ext) || ext.ToLower() != ".pdf")
            {
                throw new Exception("Extension error:This method is used to generate PDF files.");
            }

            fileName = Path.GetFileName(savePath);
            if (string.IsNullOrEmpty(fileName))
            {
                throw new Exception("File name is empty.");
            }

            try
            {
                path = savePath.Substring(0, savePath.IndexOf(fileName));
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
            }
            catch
            {
                throw new Exception("The file path does not exist.");
            }
        }

        /// <summary>
        /// HTML文本内容转HTML文件
        /// </summary>
        /// <param name="strHtml">HTML文本内容</param>
        /// <returns>HTML文件的路径</returns>
        public static string HtmlTextConvertFile(string strHtml)
        {
            if (string.IsNullOrEmpty(strHtml))
            {
                throw new Exception("HTML text content cannot be empty.");
            }

            try
            {
                string path = AppDomain.CurrentDomain.BaseDirectory.ToString() + @"html\";
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
                string fileName = path + DateTime.Now.ToString("yyyyMMddHHmmssfff") + new Random().Next(1000, 10000) + ".html";
                FileStream fileStream = new FileStream(fileName, FileMode.Create, FileAccess.ReadWrite, FileShare.ReadWrite);
                StreamWriter streamWriter = new StreamWriter(fileStream, Encoding.Default);
                streamWriter.Write(strHtml);
                streamWriter.Flush();

                streamWriter.Close();
                streamWriter.Dispose();
                fileStream.Close();
                fileStream.Dispose();
                return fileName;
            }
            catch
            {
                throw new Exception("HTML text content error.");
            }
        }


        [Obsolete]
        public static Boolean Selenium(string TargetUrl, string filename, string filepath)
        {
            try
            {
                ChromeOptions options = new ChromeOptions();
                options.AddArguments("--enable-print-browser", "--disable-infobars", "--disable-extensions", "--allow-insecure-localhost", "--ignore-certificate-errors", "--ignore-ssl-errors=yes", "--kiosk-printing", "--allow-running-insecure-content");
                options.UnhandledPromptBehavior = UnhandledPromptBehavior.Ignore;
                options.AddUserProfilePreference("plugins.always_open_pdf_externally", true);
                options.AddUserProfilePreference("print.always_print_silent", true);
                options.AddUserProfilePreference("download.default_directory", filepath);
                options.AddUserProfilePreference("savefile.default_directory", filepath);
                options.AddUserProfilePreference("download.prompt_for_download", false);
                options.AddUserProfilePreference("printing.default_destination_selection_rules", "{\"kind\": \"local\", \"namePattern\": \"Save as PDF\"}");
                options.AddUserProfilePreference("printing.print_preview_sticky_settings.appState", "{\"recentDestinations\": [{\"id\": \"Save as PDF\", \"origin\": \"local\", \"account\": \"\" }],\"version\":2,\"scalingType\":3,\"scaling\":\"80\",\"marginsType\":2,\"customMargins\":{\"top\":2,\"bottom\":0},\"isHeaderFooterEnabled\":true,\"isGcpPromoDismissed\":false,\"selectedDestinationId\":\"Save as PDF\"}");
                options.BinaryLocation = @"C:\Program Files\Google\Chrome\Application\chrome.exe";
               
                ChromeDriverService chromeDriverService = ChromeDriverService.CreateDefaultService();
                //chromeDriverService.SuppressInitialDiagnosticInformation = true;
                //chromeDriverService.HideCommandPromptWindow = true;//even we can hide command prompt window (with un comment this line)  

                using (IWebDriver driver = new OpenQA.Selenium.Chrome.ChromeDriver(chromeDriverService, options, TimeSpan.FromSeconds(120)))
                {

                    driver.Navigate().GoToUrl(TargetUrl);
                    var waitClickButton = new WebDriverWait(driver, TimeSpan.FromSeconds(120));
                    var printButton = waitClickButton.Until(ExpectedConditions.ElementIsVisible(By.Id("print")));
                    printButton.Click();
                    System.Threading.Thread.Sleep(6000);
                    var windows = driver.WindowHandles;
                    foreach (string window in windows)
                    {
                        if (driver.SwitchTo().Window(window).Title.Equals("打印"))

                        {
                            var wait = new WebDriverWait(driver, TimeSpan.FromSeconds(30));
                            var clickableElement = wait.Until(ExpectedConditions.ElementIsVisible(By.Id("mineFormPage")));
                            driver.FindElement(By.Id("dataNameBox7")).Click();
                            driver.ExecuteJavaScript("document.title='" + filename + ".pdf';window.print()");
                            System.Threading.Thread.Sleep(6000);
                        }
                    }
                    driver.Quit();
                    return true;
                }
            }
            catch (WebException we)
            {
                return false;
            }
        }

        [Obsolete]
        public static Boolean SeleniumAttach(string TargetUrl, string filename, string filepath)
        {
            try
            {
                ChromeOptions options = new ChromeOptions();
                options.AddArguments("--enable-print-browser", "--disable-infobars", "--disable-extensions", "--allow-insecure-localhost", "--ignore-certificate-errors", "--ignore-ssl-errors=yes", "--kiosk-printing", "--allow-running-insecure-content");
                options.UnhandledPromptBehavior = UnhandledPromptBehavior.Ignore;
                options.AddUserProfilePreference("plugins.always_open_pdf_externally", true);
                options.AddUserProfilePreference("print.always_print_silent", true);
                options.AddUserProfilePreference("download.default_directory", filepath);
                options.AddUserProfilePreference("savefile.default_directory", filepath);
                options.AddUserProfilePreference("download.prompt_for_download", false);
                options.AddUserProfilePreference("printing.default_destination_selection_rules", "{\"kind\": \"local\", \"namePattern\": \"Save as PDF\"}");
                options.AddUserProfilePreference("printing.print_preview_sticky_settings.appState", "{\"recentDestinations\": [{\"id\": \"Save as PDF\", \"origin\": \"local\", \"account\": \"\" }],\"version\":2,\"scalingType\":3,\"scaling\":\"80\",\"marginsType\":2,\"customMargins\":{\"top\":2,\"bottom\":0},\"isHeaderFooterEnabled\":false,\"isGcpPromoDismissed\":false,\"selectedDestinationId\":\"Save as PDF\"}");
                options.BinaryLocation = @"C:\Program Files\Google\Chrome\Application\chrome.exe";


                ChromeDriverService chromeDriverService = ChromeDriverService.CreateDefaultService();
                //chromeDriverService.SuppressInitialDiagnosticInformation = true;
                //chromeDriverService.HideCommandPromptWindow = true;//even we can hide command prompt window (with un comment this line)  
                //chromeDriverService.Start();
                using (IWebDriver driver = new OpenQA.Selenium.Chrome.ChromeDriver(chromeDriverService, options, TimeSpan.FromSeconds(20)))
                {
                    driver.Navigate().GoToUrl(TargetUrl);
                    System.Threading.Thread.Sleep(8000);
                    driver.Quit();
                    chromeDriverService.Dispose();
                    DirectoryInfo TheFolder = new DirectoryInfo(filepath);
                    foreach (FileInfo Fileinfo in TheFolder.GetFiles())
                    {
                        if (Fileinfo.Extension == ".html")
                        {
                            if (File.Exists(filepath + filename) == true)
                            {
                                File.Delete(filepath + filename);
                                File.Move(Fileinfo.FullName, filepath + filename);
                            }
                            else
                            {
                                File.Move(Fileinfo.FullName, filepath + filename);
                            }
                        }
                    }
                    return true;
                }
            }
            catch (WebException we)
            {
                return false;
            }
        }
        
        public static string GetPolicy(string claimNo) {
            string Conn = "Data Source=cob-server-090;User ID=common;PassWord=**********;Initial Catalog=live_ilodata;MultipleActiveResultSets=True;Connection Timeout=400";
            SqlConnection connection = new SqlConnection(Conn);
            string sql = "select * from oclaim where fclmno ='" + claimNo + "'";
            using (SqlCommand command = new SqlCommand(sql, connection))
            {
                command.Connection.Open();
                var reader = command.ExecuteReader();
                DataTable dt = new DataTable(); 
                dt.Load(reader);
                return dt.Rows[0]["fpolno"].ToString().Trim();
            }
        }
    }
}
