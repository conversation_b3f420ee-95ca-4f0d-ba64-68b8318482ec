using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.IO;
using System.Text;
using iTextSharp.text.pdf;
using iTextSharp.text.pdf.parser;

namespace ScanToSign.PDF
{
    public class PdfKeywordFinder
    {

        /**
     * @description: 查找插入签名图片的最终位置，因为是插入签名图片，所以之前的关键字应只会出现一次
     * 这里直接使用了第一次查找到关键字的位置，并返回该关键字之后的坐标位置
     * @return: float[0]:页码，float[1]:最后一个字的x坐标，float[2]:最后一个字的y坐标
     */
        public static float[] getAddImagePositionXY(string pdfName, string keyword)
        {
            float[] temp = new float[3];
            IList<float[]> positions = PdfKeywordFinder.findKeywordPostions(pdfName, keyword);
            if (keyword == "Authorised Signature")
            {
                temp[0] = positions[0][0];
                temp[1] = positions[0][1] + keyword.Length / 2;
                temp[2] = positions[0][2];
            }
            if (keyword == "________________________________________")
            {
                temp[0] = positions[0][0];
                //temp[1] = positions[0][1] + (keyword.Length * positions[0][3]);
                temp[1] = positions[0][1] + keyword.Length / 2;
                temp[2] = positions[0][2] - positions[0][3];
            }
            if (keyword == "(CHAPTER 272)" || keyword == "THE SCHEDULE" || keyword == "SCHEDULE" || keyword == "ENDORSEMENT" || keyword == "EXPIRY NOTICE" || keyword == "Schedule")
            {
                temp[0] = positions[0][0];
                temp[1] = positions[0][1] + keyword.Length / 2;
                temp[2] = positions[0][2];
            }
            if (keyword == "Approved by: Zhou Jin Xing")
            {
                temp[0] = positions[0][0];
                temp[1] = positions[0][1] + keyword.Length / 2;
                temp[2] = positions[0][2] - positions[0][3];
            }
            if (keyword == "Checked by: April Lim")
            {
                temp[0] = positions[0][0];
                temp[1] = positions[0][1] + keyword.Length / 2;
                temp[2] = positions[0][2] - positions[0][3];
            }
            if (keyword == "Prepared by: Arthur Chang")
            {
                temp[0] = positions[0][0];
                temp[1] = positions[0][1] + keyword.Length / 2;
                temp[2] = positions[0][2] - positions[0][3];
            }
            if (keyword == "Checked by: Claims Department")
            {
                temp[0] = positions[0][0];
                temp[1] = positions[0][1] + keyword.Length / 2;
                temp[2] = positions[0][2] - positions[0][3];
            }
            if (keyword == "FACULTATIVE")
            {
                temp[0] = positions[0][0];
                temp[1] = positions[0][1] + keyword.Length / 2;
                temp[2] = positions[0][2] - positions[0][3];
            }
            if (keyword == "CHINA OVERSEAS INSURANCE LIMITED")
            {
                temp[0] = positions[0][0];
                temp[1] = positions[0][1] + keyword.Length / 2;
                temp[2] = positions[0][2] - positions[0][3];
            }
            if (keyword == "Our Ref")
            {
                temp[0] = positions[0][0];
                temp[1] = positions[0][1] + keyword.Length / 2;
                temp[2] = positions[0][2] - positions[0][3];
            }
            if (keyword == "Reinsurance confirmed and accepted")
            {
                temp[0] = positions[0][0];
                temp[1] = positions[0][1] + keyword.Length / 2;
                temp[2] = positions[0][2] - positions[0][3];
            }
            return temp;
        }

        /**
    * findKeywordPostions
    *  返回查找到关键字的首个文字的左上角坐标值
    *
    * @param pdfName
    * @param keyword
    * @return List<float [ ]> : float[0]:pageNum float[1]:x float[2]:y
    * @throws IOException
    */
        public static IList<float[]> findKeywordPostions(string pdfName, string keyword)
        {

            byte[] pdfData = FileToByte.ReadFileToByte(pdfName);
            //从输入流中读取pdfData.length个字节到字节数组中，返回读入缓冲区的总字节数，若到达文件末尾，则返回-1
            var result = new List<float[]>();
            List<PdfPageContentPositions> pdfPageContentPositions = getPdfContentPostionsList(pdfData);

            foreach (PdfPageContentPositions pdfPageContentPosition in pdfPageContentPositions)
            {
                IList<float[]> charPositions = findPositions(keyword, pdfPageContentPosition);
                if (charPositions == null || charPositions.Count < 1)
                {
                    var keywordNoSpace = keyword.Replace(" ", "");
                    charPositions = findPositions(keywordNoSpace, pdfPageContentPosition);
                    if (charPositions == null || charPositions.Count < 1)
                    {
                        continue;
                    }
                }
                ((List<float[]>)result).AddRange(charPositions);
            }
            return result;
        }
        private static List<PdfPageContentPositions> getPdfContentPostionsList(byte[] pdfData)
        {
            PdfReader reader = new PdfReader(pdfData);
            List<PdfPageContentPositions> result = new List<PdfPageContentPositions>();

            int pages = reader.NumberOfPages;
            for (int pageNum = 1; pageNum <= pages; pageNum++)
            {
                float width = reader.GetPageSize(pageNum).Width;
                float height = reader.GetPageSize(pageNum).Height;
                PdfRenderListener pdfRenderListener = new PdfRenderListener(pageNum, width, height);
                //解析pdf，定位位置
                PdfContentStreamProcessor processor = new PdfContentStreamProcessor(pdfRenderListener);
                PdfDictionary pageDic = reader.GetPageN(pageNum);
                PdfDictionary resourcesDic = pageDic.GetAsDict(PdfName.RESOURCES);
                try
                {
                    processor.ProcessContent(ContentByteUtils.GetContentBytesForPage(reader, pageNum), resourcesDic);
                }
                catch (IOException e)
                {
                    reader.Close();
                    throw e;
                }

                string content = pdfRenderListener.Content;
                IList<CharPosition> charPositions = pdfRenderListener.getcharPositions();

                IList<float[]> positionsList = new List<float[]>();
                foreach (CharPosition charPosition in charPositions)
                {
                    float[] positions = new float[] { charPosition.PageNum, charPosition.X, charPosition.Y, charPosition.CharWidth };
                    positionsList.Add(positions);
                }


                PdfPageContentPositions pdfPageContentPositions = new PdfPageContentPositions();
                pdfPageContentPositions.Content = content;
                pdfPageContentPositions.Postions = positionsList;
                result.Add(pdfPageContentPositions);
            }
            reader.Close();
            return result;
        }

        private static List<float[]> findPositions(string keyword, PdfPageContentPositions pdfPageContentPositions)
        {
            List<float[]> result = new List<float[]>();
            string content = pdfPageContentPositions.Content;
            IList<float[]> charPositions = pdfPageContentPositions.Positions;
            for (int pos = 0; pos < content.Length;)
            {
                int positionIndex = content.IndexOf(keyword, pos, StringComparison.Ordinal);
                if (positionIndex == -1)
                {
                    break;
                }
                float[] postions = charPositions[positionIndex];
                result.Add(postions);
                pos = positionIndex + 1;
            }
            return result;
        }

        private class PdfPageContentPositions
        {
            private string content;
            private IList<float[]> positions;

            public virtual string Content
            {
                get
                {
                    return content;
                }
                set
                {
                    this.content = value;
                }
            }

            public virtual IList<float[]> Positions
            {
                get
                {
                    return positions;
                }
            }

            public virtual IList<float[]> Postions
            {
                set
                {
                    this.positions = value;
                }
            }
        }




    }
}
