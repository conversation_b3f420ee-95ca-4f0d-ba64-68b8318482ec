
using System;
using System.IO;
using iTextSharp.text.pdf;
using iTextSharp.text.pdf.parser;
using Microsoft.Office.Interop.Word;

namespace ScanToSign.PDF
{
    public class PDFImageTest
    {
        string urlPushToA8 = "";
        string urlSendToCustomer = "";
        string urlSendToCustomerCopy = "";
        public void sign(string Filename, string fclass,string Policy)
        {
            urlPushToA8 = @"\\10.1.8.27\保險公司\COIL_DATA\PushToA8\";
            if (Policy == "")
            {
                urlSendToCustomer = @"\\10.1.8.27\保險公司\COIL_DATA\COIL_ePolicy\" + fclass + @"\";
                urlSendToCustomer = urlSendToCustomer + Filename.Replace("_CI.pdf", "").Replace(".pdf", "") + "\\";
                urlSendToCustomerCopy = @"\\10.1.8.27\保險公司\COIL_DATA\COIL_ePolicyCopy\" + fclass + @"\";
                urlSendToCustomerCopy = urlSendToCustomerCopy + Filename.Replace("_CI.pdf", "").Replace(".pdf", "") + "\\";
            }
            else {
                urlSendToCustomer = @"\\10.1.8.27\保險公司\COIL_DATA\COIL_ePolicy\" + fclass + @"\";
                urlSendToCustomer = urlSendToCustomer + Policy.Replace("_CI.pdf", "").Replace(".pdf", "") + "\\";
                urlSendToCustomerCopy = @"\\10.1.8.27\保險公司\COIL_DATA\COIL_ePolicyCopy\" + fclass + @"\";
                urlSendToCustomerCopy = urlSendToCustomerCopy + Policy.Replace("_CI.pdf", "").Replace(".pdf", "") + "\\";
            }

            if (readfilesSchedule(Filename) != "")
            {
                string pdfname = urlPushToA8 + Filename;
                string keyword = "", keyword1 = "";
                //查找签名位置
                if (pdfname.Contains("_CI") || pdfname.Contains("_NI"))
                {
                    keyword = "Authorised Signature";
                }
                else
                {
                    keyword = "________________________________________";
                }
                float[] position = PdfKeywordFinder.getAddImagePositionXY(pdfname, keyword);
                //Read file using PdfReader
                PdfReader pdfReader = new PdfReader(pdfname);
                int pages = pdfReader.NumberOfPages;
                //Modify file using PdfReader
                PdfStamper pdfStamper = new PdfStamper(pdfReader, new FileStream(urlSendToCustomer + Filename, FileMode.Open));

                iTextSharp.text.Image image = iTextSharp.text.Image.GetInstance(urlPushToA8 + "\\zhou.png");

                //Fixed Positioning
                image.ScaleAbsolute(80, 50);
                //Scale to new height and new width of image
                image.SetAbsolutePosition(position[1], position[2]);

                PdfContentByte content = pdfStamper.GetUnderContent((int)position[0]);
                content.AddImage(image);

                if (pdfname.Contains("_CI") || pdfname.Contains("_NI"))
                {
                    keyword1 = "(CHAPTER 272)";
                }
                else if (pdfname.Contains("END"))
                {
                    keyword1 = "ENDORSEMENT";
                }
                else if (fclass == "CAR" || fclass == "PAR" || fclass == "CPM")
                {
                    keyword1 = "SCHEDULE";
                }
                else if (fclass == "PII")
                    keyword1 = "Schedule";
                else
                {
                    keyword1 = "THE SCHEDULE";
                }
                float[] position1 = PdfKeywordFinder.getAddImagePositionXY(pdfname, keyword1);
                iTextSharp.text.Image image1 = iTextSharp.text.Image.GetInstance(urlPushToA8 + "\\B.png");

                image1.ScaleToFit(25, 25);

                if (keyword1 == "(CHAPTER 272)") { 
                    image1.SetAbsolutePosition(position1[1] + 15, position1[2] + 50);
                }
                if (keyword1 == "SCHEDULE")
                {
                    image1.SetAbsolutePosition(position1[1] + 15, position1[2] + 15);
                }
                if (keyword1 == "Schedule")
                {
                    image1.SetAbsolutePosition(position1[1] + 15, position1[2] + 15);
                }
                if (keyword1 == "THE SCHEDULE")
                {
                    image1.SetAbsolutePosition(position1[1] + 30, position1[2] + 15);
                }
                if (keyword1 == "ENDORSEMENT")
                {
                    image1.SetAbsolutePosition(position1[1] - 140, position1[2] - 50);
                }
                //iTextSharp.text.Image image2 = iTextSharp.text.Image.GetInstance(urlPushToA8 + "\\original.png");
                //image2.ScaleToFit(180, 40);
                //if (keyword1 == "THE SCHEDULE")
                //{
                //    image2.SetAbsolutePosition(position1[1] + 175, position1[2] + 10);
                //}

                for (int i = 1; i <= pages; i++)
                {
                    PdfContentByte under = pdfStamper.GetUnderContent(i);
                    under.AddImage(image1);
                    // under.AddImage(image2);
                }
                pdfStamper.Close();
                pdfReader.Close();
            }
            if (File.Exists(urlSendToCustomerCopy + Filename.Replace(".pdf", "_Copy.pdf")))
            {
                string pdfname = urlPushToA8 + Filename;
                string keyword = "";
                //查找签名位置
                if (pdfname.Contains("_CI") || pdfname.Contains("_NI"))
                {
                    keyword = "Authorised Signature";
                }
                else
                {
                    keyword = "________________________________________";
                }
                float[] position = PdfKeywordFinder.getAddImagePositionXY(pdfname, keyword);

                //Read file using PdfReader
                PdfReader pdfReader = new PdfReader(pdfname);

                //Modify file using PdfReader
                PdfStamper pdfStamper = new PdfStamper(pdfReader, new FileStream(urlSendToCustomerCopy + Filename.Replace(".pdf", "_Copy.pdf"), FileMode.Open));

                iTextSharp.text.Image image = iTextSharp.text.Image.GetInstance(urlPushToA8 + "zhou.png");

                //Fixed Positioning
                image.ScaleAbsolute(80, 50);
                //Scale to new height and new width of image
                image.SetAbsolutePosition(position[1], position[2]);

                PdfContentByte content = pdfStamper.GetUnderContent((int)position[0]);
                content.AddImage(image);
                pdfStamper.Close();
                pdfReader.Close();
            }
        }

        public void SignRI(string Filename, string fclass, string Policy)
        {
            urlPushToA8 = @"\\10.1.8.27\保險公司\COIL_DATA\PushToA8\";

            if (Policy == "")
            {
                urlSendToCustomer = @"\\10.1.8.27\保險公司\COIL_DATA\COIL_ePolicy\" + fclass + @"\";
                urlSendToCustomer = urlSendToCustomer + Filename.Replace("_RI.pdf", "").Replace(".pdf", "") + "\\";
                urlSendToCustomerCopy = @"\\10.1.8.27\保險公司\COIL_DATA\COIL_ePolicyCopy\" + fclass + @"\";
                urlSendToCustomerCopy = urlSendToCustomerCopy + Filename.Replace("_RI.pdf", "").Replace(".pdf", "") + "\\";
            }
            else
            {
                urlSendToCustomer = @"\\10.1.8.27\保險公司\COIL_DATA\COIL_ePolicy\" + fclass + @"\";
                urlSendToCustomer = urlSendToCustomer + Policy.Replace("_RI.pdf", "").Replace(".pdf", "") + "\\";
                urlSendToCustomerCopy = @"\\10.1.8.27\保險公司\COIL_DATA\COIL_ePolicyCopy\" + fclass + @"\";
                urlSendToCustomerCopy = urlSendToCustomerCopy + Policy.Replace("_RI.pdf", "").Replace(".pdf", "") + "\\";
            }


            string pdfname = urlPushToA8 + Filename;
            string keyword = "", keyword1 = "", keyword2 = "", keyword3 = "";
            //查找签名位置
            keyword = "FACULTATIVE";
            keyword1 = "Our Ref";
            keyword3 = "CHINA OVERSEAS INSURANCE LIMITED";
            keyword2 = "Reinsurance confirmed and accepted";

            if (readfilesSchedule(Filename) != "")
            {
                //Read file using PdfReader
                PdfReader pdfReader = new PdfReader(pdfname);
                int pages = pdfReader.NumberOfPages;
                //Modify file using PdfReader
                PdfStamper pdfStamper = new PdfStamper(pdfReader, new FileStream(urlSendToCustomer + Filename, FileMode.Open));

                for (int i = 1; i <= pages; i++)
                {

                    float[] position = PdfKeywordFinder.getAddImagePositionXY(pdfname, keyword);
                    iTextSharp.text.Image image = iTextSharp.text.Image.GetInstance(urlPushToA8 + "header.png");
                    //Fixed Positioning
                    image.ScaleAbsolute(577,37);
                    //Scale to new height and new width of image
                    image.SetAbsolutePosition(position[1] - 190, position[2] + 140);
                    PdfContentByte header_IR = pdfStamper.GetUnderContent(i);
                    header_IR.AddImage(image);

                    float[] position1 = PdfKeywordFinder.getAddImagePositionXY(pdfname, keyword1);
                    iTextSharp.text.Image image1 = iTextSharp.text.Image.GetInstance(urlPushToA8 + "original.png");
                    image1.ScaleAbsolute(94, 27);
                    image1.SetAbsolutePosition(position1[1], position1[2] + 15);
                    PdfContentByte original = pdfStamper.GetUnderContent(i);
                    original.AddImage(image1);

                    //float[] position2 = PdfKeywordFinder.getAddImagePositionXY(pdfname, keyword2);
                    //iTextSharp.text.Image image2 = iTextSharp.text.Image.GetInstance(urlPushToA8 + "record.png");
                    //image2.ScaleAbsolute(150, 80);
                    //image2.SetAbsolutePosition(position2[1] - 15, position2[2]);
                    //PdfContentByte record = pdfStamper.GetUnderContent(i);
                    //record.AddImage(image2);

                    float[] position3 = PdfKeywordFinder.getAddImagePositionXY(pdfname, keyword3);
                    iTextSharp.text.Image image3 = iTextSharp.text.Image.GetInstance(urlPushToA8 + "Leanne.png");
                    image3.ScaleAbsolute(80, 50);
                    image3.SetAbsolutePosition(position3[1], position3[2] - 65);
                    PdfContentByte zhou = pdfStamper.GetUnderContent(i);
                    zhou.AddImage(image3);
                    
                }
                pdfStamper.Close();
                pdfReader.Close();
            }
            if (File.Exists(urlSendToCustomerCopy + Filename.Replace(".pdf", "_Copy.pdf")))
            {
                //Read file using PdfReader
                PdfReader pdfReader = new PdfReader(pdfname);
                int pages = pdfReader.NumberOfPages;
                //Modify file using PdfReader
                PdfStamper pdfStamper = new PdfStamper(pdfReader, new FileStream(urlSendToCustomerCopy + Filename.Replace(".pdf", "_Copy.pdf"), FileMode.Open));

                for (int i = 1; i <= pages; i++)
                {

                    float[] position = PdfKeywordFinder.getAddImagePositionXY(pdfname, keyword);
                    iTextSharp.text.Image image = iTextSharp.text.Image.GetInstance(urlPushToA8 + "header.png");
                    //Fixed Positioning
                    image.ScaleAbsolute(575, 55);
                    //Scale to new height and new width of image
                    image.SetAbsolutePosition(position[1] - 190, position[2] + 140);
                    PdfContentByte header_IR = pdfStamper.GetUnderContent(i);
                    header_IR.AddImage(image);

                    float[] position1 = PdfKeywordFinder.getAddImagePositionXY(pdfname, keyword1);
                    iTextSharp.text.Image image1 = iTextSharp.text.Image.GetInstance(urlPushToA8 + "dup.png");
                    image1.ScaleAbsolute(80, 60);
                    image1.SetAbsolutePosition(position1[1], position1[2] + 15);
                    PdfContentByte dup = pdfStamper.GetUnderContent(i);
                    dup.AddImage(image1);

                    float[] position2 = PdfKeywordFinder.getAddImagePositionXY(pdfname, keyword2);
                    iTextSharp.text.Image image2 = iTextSharp.text.Image.GetInstance(urlPushToA8 + "record.png");
                    image2.ScaleAbsolute(150, 80);
                    image2.SetAbsolutePosition(position2[1] - 15, position2[2]);
                    PdfContentByte record = pdfStamper.GetUnderContent(i);
                    record.AddImage(image2);

                    float[] position3 = PdfKeywordFinder.getAddImagePositionXY(pdfname, keyword3);
                    iTextSharp.text.Image image3 = iTextSharp.text.Image.GetInstance(urlPushToA8 + "Leanne.png");
                    image3.ScaleAbsolute(80, 50);
                    image3.SetAbsolutePosition(position3[1], position3[2] - 65);
                    PdfContentByte zhou = pdfStamper.GetUnderContent(i);
                    zhou.AddImage(image3);

                }
                pdfStamper.Close();
                pdfReader.Close();
            }
        }
        public void SignBusinessReport(string Filename, string fclass)
        {
            urlPushToA8 = @"\\10.1.8.27\保險公司\COIL_DATA\PushToA8\Business\" + fclass + @"\";
            urlSendToCustomer = @"\\10.1.8.27\保險公司\COIL_DATA\COIL_eReport\Business\" + fclass + @"\";

            if (readfilesSchedule(Filename) != "")
            {
                string pdfname = urlPushToA8 + Filename;
                string keyword = "", keyword1 = "", keyword2 = "";
                //查找签名位置
                keyword = "Approved by: Zhou Jin Xing";
                keyword1 = "Checked by: April Lim";
                keyword2 = "Prepared by: Arthur Chang";
 
                //Read file using PdfReader
                PdfReader pdfReader = new PdfReader(pdfname);
                int pages = pdfReader.NumberOfPages;
                //Modify file using PdfReader
                PdfStamper pdfStamper = new PdfStamper(pdfReader, new FileStream(urlSendToCustomer + Filename, FileMode.Open));

                float[] position = PdfKeywordFinder.getAddImagePositionXY(pdfname, keyword);
                iTextSharp.text.Image image = iTextSharp.text.Image.GetInstance(urlPushToA8.Replace("\\Business\\"+fclass,"") + "\\zhou.png");
                //Fixed Positioning
                image.ScaleAbsolute(80, 50);
                //Scale to new height and new width of image
                image.SetAbsolutePosition(position[1], position[2]);
                PdfContentByte Zhou = pdfStamper.GetUnderContent((int)position[0]);
                Zhou.AddImage(image);

                float[] position1 = PdfKeywordFinder.getAddImagePositionXY(pdfname, keyword1);
                iTextSharp.text.Image image1 = iTextSharp.text.Image.GetInstance(urlPushToA8.Replace("\\Business\\" + fclass, "") + "\\April.png");
                image1.ScaleAbsolute(80, 50);
                image1.SetAbsolutePosition(position1[1], position[2]);
                PdfContentByte April = pdfStamper.GetUnderContent((int)position1[0]);
                April.AddImage(image1);

                float[] position2 = PdfKeywordFinder.getAddImagePositionXY(pdfname, keyword2);
                iTextSharp.text.Image image2 = iTextSharp.text.Image.GetInstance(urlPushToA8.Replace("\\Business\\" + fclass, "") + "\\Arthur.png");
                image2.ScaleAbsolute(80, 50);
                image2.SetAbsolutePosition(position2[1], position[2]);
                PdfContentByte Arthur = pdfStamper.GetUnderContent((int)position2[0]);
                Arthur.AddImage(image2);

                pdfStamper.Close();
                pdfReader.Close();
            }

        }

        public void SignClaimsReport(string Filename, string fclass)
        {
            urlPushToA8 = @"\\10.1.8.27\保險公司\COIL_DATA\PushToA8\Claims\" + fclass + @"\";
            urlSendToCustomer = @"\\10.1.8.27\保險公司\COIL_DATA\COIL_eReport\Claims\" + fclass + @"\";

            if (readfilesSchedule(Filename) != "")
            {
                string pdfname = urlPushToA8 + Filename;
                string keyword = "", keyword1 = "";
                //查找签名位置
                keyword = "Approved by: Zhou Jin Xing";
                keyword1 = "Checked by: Claims Department";

                //Read file using PdfReader
                PdfReader pdfReader = new PdfReader(pdfname);
                int pages = pdfReader.NumberOfPages;
                //Modify file using PdfReader
                PdfStamper pdfStamper = new PdfStamper(pdfReader, new FileStream(urlSendToCustomer + Filename, FileMode.Open));

                float[] position = PdfKeywordFinder.getAddImagePositionXY(pdfname, keyword);
                iTextSharp.text.Image image = iTextSharp.text.Image.GetInstance(urlPushToA8.Replace("\\Claims\\" + fclass, "") + "\\zhou.png");
                //Fixed Positioning
                image.ScaleAbsolute(80, 50);
                //Scale to new height and new width of image
                image.SetAbsolutePosition(position[1], position[2]);
                //if (Filename == "4.pdf")
                //{
                //    image.SetAbsolutePosition(position[1], position[2]);
                //}
                //else
                //{ image.SetAbsolutePosition(position[1]-80, position[2]+25); }
                PdfContentByte Zhou = pdfStamper.GetUnderContent((int)position[0]);
                Zhou.AddImage(image);

                float[] position1 = PdfKeywordFinder.getAddImagePositionXY(pdfname, keyword1);
                iTextSharp.text.Image image1 = iTextSharp.text.Image.GetInstance(urlPushToA8.Replace("\\Claims\\" + fclass, "") + "\\Peggy.png");
                image1.ScaleAbsolute(80, 50);
                image1.SetAbsolutePosition(position[1] + 250, position[2] + 5);
                //if (Filename == "4.pdf")
                //{
                //    image1.SetAbsolutePosition(position[1] + 250, position[2] + 5);
                //}
                //else {
                //    image1.SetAbsolutePosition(position[1] + 150, position[2] + 25);
                //}
                
                PdfContentByte Peggy = pdfStamper.GetUnderContent((int)position1[0]);
                Peggy.AddImage(image1);


                pdfStamper.Close();
                pdfReader.Close();
            }

        }

        public void SignCoverNote(string Filename)
        {
            urlPushToA8 = @"\\10.1.8.27\保險公司\COIL_DATA\PushToA8\";
            urlSendToCustomer = @"\\10.1.8.27\保險公司\COIL_DATA\COIL_eCoverNote\";

            if (readfilesSchedule(Filename) != "")
            {
                string pdfname = urlPushToA8 + Filename;
                string keyword = "";
                //查找签名位置
                keyword = "Authorised Signature";

                //Read file using PdfReader
                PdfReader pdfReader = new PdfReader(pdfname);
                int pages = pdfReader.NumberOfPages;
                //Modify file using PdfReader
                PdfStamper pdfStamper = new PdfStamper(pdfReader, new FileStream(urlSendToCustomer + Filename, FileMode.Open));

                float[] position = PdfKeywordFinder.getAddImagePositionXY(pdfname, keyword);
                iTextSharp.text.Image image = iTextSharp.text.Image.GetInstance(urlPushToA8 + "\\zhou.png");
                //Fixed Positioning
                image.ScaleAbsolute(80, 50);
                //Scale to new height and new width of image
                image.SetAbsolutePosition(position[1], position[2]);

                PdfContentByte Zhou = pdfStamper.GetUnderContent((int)position[0]);
                Zhou.AddImage(image);

                pdfStamper.Close();
                pdfReader.Close();
            }

        }

        public void signExpire(string Filename)
        {
            string url = @"\\10.1.8.27\保險公司\COIL_DATA\COIL_eExpireNote\";
            String sourcefile = url + Filename;
            String modifile = url + Filename.Replace(".pdf","_s.pdf");
            if (File.Exists(sourcefile))
            {
                string keyword = "";
                //查找签名位置
                keyword = "EXPIRY NOTICE";

                //Read file using PdfReader
                PdfReader pdfReader = new PdfReader(sourcefile);
                int pages = pdfReader.NumberOfPages;
                File.Copy(sourcefile, modifile, true);
                
                //Modify file using PdfReader
                PdfStamper pdfStamper = new PdfStamper(pdfReader, new FileStream(modifile, FileMode.Open));

                float[] position = PdfKeywordFinder.getAddImagePositionXY(sourcefile, keyword);
                iTextSharp.text.Image image = iTextSharp.text.Image.GetInstance(url + "\\zhou.png");
                //Fixed Positioning
                image.ScaleAbsolute(330, 90);
                //Scale to new height and new width of image
                image.SetAbsolutePosition(position[1]-100, position[2]+16);
                PdfContentByte Header = pdfStamper.GetUnderContent((int)position[0]);
                Header.AddImage(image);

                pdfStamper.Close();
                pdfReader.Close();
                File.Delete(sourcefile);
            }

        }

        public string readfilesSchedule(string Filename)
        {
            int filesum = 0;  //更新文件数
            string LocalPath = urlSendToCustomer;
            string LocalPathCopy = urlSendToCustomerCopy;
            //从配置文件App中取文件服务器更新目录：
            string ServerPath = urlPushToA8;
            if (Directory.Exists(ServerPath))
            {
                foreach (string SourceFile in Directory.GetFiles(ServerPath))  //循环取服务器更新路径文件
                {
                    string FileName = System.IO.Path.GetFileName(SourceFile);//取更新文件名   
                    //本地目录有相同文件名就需要判断是否为可用更新文件   
                    if (FileName == Filename)
                    {
                        if (File.Exists(LocalPath + FileName) == true)
                        {
                            DateTime dtLocal = File.GetLastWriteTime(LocalPath + FileName);//本地文件修改日期   
                            DateTime dtUpdate = File.GetLastWriteTime(SourceFile);//更新目录文件的修改日期   
                            if (dtUpdate != dtLocal)//可用更新   
                            {
                                //++filesum;
                                //File.Copy(SourceFile, LocalPath + FileName, true);
                                //File.Copy(SourceFile, LocalPath + FileName.Replace(".pdf", "_Copy.pdf"), true);
                            }
                        }
                        else
                        {
                            ++filesum;
                            File.Copy(SourceFile, LocalPath + FileName, true);
                        }
                        if (File.Exists(LocalPathCopy + FileName) == true)
                        {
                            DateTime dtLocal = File.GetLastWriteTime(LocalPathCopy + FileName);//本地文件修改日期   
                            DateTime dtUpdate = File.GetLastWriteTime(SourceFile);//更新目录文件的修改日期   
                            if (dtUpdate != dtLocal)//可用更新   
                            {
                                //++filesum;
                                //File.Copy(SourceFile, LocalPath + FileName, true);
                                //File.Copy(SourceFile, LocalPath + FileName.Replace(".pdf", "_Copy.pdf"), true);
                            }
                        }
                        else
                        {
                            ++filesum;
                            File.Copy(SourceFile, LocalPathCopy + FileName.Replace(".pdf", "_Copy.pdf"), true);
                        }
                        
                    }
                }
                if (filesum > 0)
                {
                    return "刚才从服务器更新文件" + filesum.ToString() + "个";
                }
                else return "";
            }
            else
            {
                return "";
            }
        }

        public string WordToPdfAttachment(string Filename)
        {
            int filesum = 0;  //更新文件数

            try
            {  // word 檔案位置
                string sourcedocx = "M:\\COIL\\Business Department\\Policy Administration\\Policy Attachment\\2020 Policy\\" + Filename + ".doc";
                // PDF 儲存位置
                string targetpdf = urlPushToA8 + Filename + "_Attach.pdf";

                //建立 word application instance
                Microsoft.Office.Interop.Word.Application appWord = new Microsoft.Office.Interop.Word.Application();
                //開啟 word 檔案
                var wordDocument = appWord.Documents.Open(sourcedocx);
                //匯出為 pdf
                wordDocument.ExportAsFixedFormat(targetpdf, WdExportFormat.wdExportFormatPDF);

                //關閉 word 檔
                wordDocument.Close();
                //結束 word
                appWord.Quit();
                ++filesum;
                return "刚才从服务器更新文件" + filesum.ToString() + "个";
            }
            catch (Exception ex)
            {
                return "";
            }
        }

        public string WordToPdfJacket(string Filename)
        {
            int filesum = 0;  //更新文件数

            try
            {  // word 檔案位置
                string sourcedocx = "M:\\COIL\\Business Department\\Policy Administration\\Policy Jacket\\" + Filename + ".doc";
                // PDF 儲存位置
                string targetpdf = urlPushToA8 + Filename + "_Jacket.pdf";

                //建立 word application instance
                Microsoft.Office.Interop.Word.Application appWord = new Microsoft.Office.Interop.Word.Application();
                //開啟 word 檔案
                var wordDocument = appWord.Documents.Open(sourcedocx);
                //匯出為 pdf
                wordDocument.ExportAsFixedFormat(targetpdf, WdExportFormat.wdExportFormatPDF);

                //關閉 word 檔
                wordDocument.Close();
                //結束 word
                appWord.Quit();
                ++filesum;
                return "刚才从服务器更新文件" + filesum.ToString() + "个";
            }
            catch (Exception ex)
            {
                return "";
            }
        }

        

    }

}
