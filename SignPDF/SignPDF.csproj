<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="itext7" Version="7.1.12" />
    <PackageReference Include="iTextSharp" Version="5.5.13.1" />
    <PackageReference Include="Microsoft.Office.Interop.Word" Version="15.0.4797.1003" />
    <PackageReference Include="Nancy" Version="2.0.0" />
    <PackageReference Include="NLog" Version="4.7.4" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="4.7.0" />
    <PackageReference Include="Selenium.Support" Version="3.141.0" />
    <PackageReference Include="Selenium.WebDriver" Version="3.141.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.2" />
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Public\" />
  </ItemGroup>



</Project>
