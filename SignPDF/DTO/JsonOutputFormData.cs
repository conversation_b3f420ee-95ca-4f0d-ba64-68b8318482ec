namespace ScanToSign
{
    public class RowData
    {
        public string id { get; set; }
        public string state { get; set; }
        public string start_member_id { get; set; }
        public string start_date { get; set; }
        public string approve_member_id { get; set; }
        public string finishedflag { get; set; }
        public string ratifyflag { get; set; }
        public string ratify_member_id { get; set; }
        public string sort { get; set; }
        public string modify_member_id { get; set; }
        public string modify_date { get; set; }
        public string field0001 { get; set; }
        public string field0002 { get; set; }
        public string field0003 { get; set; }
        public string field0004 { get; set; }
        public string field0005 { get; set; }
        public string field0006 { get; set; }
        public string field0007 { get; set; }
        public string field0013 { get; set; }
        public string field0014 { get; set; }
        public string field0015 { get; set; }
        public string field0016 { get; set; }
        public string field0020 { get; set; }
        public string field0021 { get; set; }
        public string field0024 { get; set; }
        public string field0026 { get; set; }
        public string field0027 { get; set; }
        public string field0031 { get; set; }

    }
    public class DataMap
    {
        public string field0031 { get; set; }
        public string field0013 { get; set; }
        public string field0014 { get; set; }
        public string field0015 { get; set; }
        public string field0016 { get; set; }
        public string field0006 { get; set; }
        public string field0007 { get; set; }
        public string id { get; set; }
        public string state { get; set; }
        public string modify_member_id { get; set; }
        public string start_date { get; set; }
        public string field0020 { get; set; }
        public string field0021 { get; set; }
        public string ratifyflag { get; set; }
        public string field0001 { get; set; }
        public string field0002 { get; set; }
        public string field0024 { get; set; }
        public string field0003 { get; set; }
        public string field0004 { get; set; }
        public string field0026 { get; set; }
        public string field0005 { get; set; }
        public string field0027 { get; set; }
        public string sort { get; set; }
        public string ratify_member_id { get; set; }
        public string start_member_id { get; set; }
        public string approve_member_id { get; set; }
        public string finishedflag { get; set; }
        public string modify_date { get; set; }

    }
    public class Application
    {
        public string StartDate { get; set; }
        public RowData RowData { get; set; }
        public string code { get; set; }
        public string FinishedFlag { get; set; }
        public string State { get; set; }
        public string Sort { get; set; }
        public string DataJson { get; set; }
        public string id { get; set; }
        public DataMap DataMap { get; set; }

    }

}