using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SignPDF.DTO
{
    public class QJasonOutPut
    {
        // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
        public class RowData
        {
            public long id { get; set; }
            public int state { get; set; }
            public long start_member_id { get; set; }
            public long start_date { get; set; }
            public long approve_member_id { get; set; }
            public object approve_date { get; set; }
            public int finishedflag { get; set; }
            public int ratifyflag { get; set; }
            public long ratify_member_id { get; set; }
            public object ratify_date { get; set; }
            public int sort { get; set; }
            public long modify_member_id { get; set; }
            public long modify_date { get; set; }
            public object field0001 { get; set; }
            public string field0002 { get; set; }
            public string field0003 { get; set; }
            public string field0004 { get; set; }
            public string field0005 { get; set; }
            public string field0012 { get; set; }
            public object field0013 { get; set; }
            public string field0014 { get; set; }
            public string field0011 { get; set; }
            public long field0015 { get; set; }
            public object field0016 { get; set; }
            public object field0017 { get; set; }
            public object field0018 { get; set; }
        }

        public class DataMap
        {
            public List<object> field0010 { get; set; }
            public string field0011 { get; set; }
            public string field0012 { get; set; }
            public object field0013 { get; set; }
            public List<long> formmain_id { get; set; }
            public string field0014 { get; set; }
            public long field0015 { get; set; }
            public object field0016 { get; set; }
            public object field0017 { get; set; }
            public object field0018 { get; set; }
            public List<object> field0006 { get; set; }
            public List<object> field0007 { get; set; }
            public List<object> field0008 { get; set; }
            public List<long> id { get; set; }
            public int state { get; set; }
            public long modify_member_id { get; set; }
            public List<object> field0009 { get; set; }
            public object approve_date { get; set; }
            public object ratify_date { get; set; }
            public long start_date { get; set; }
            public int ratifyflag { get; set; }
            public object field0001 { get; set; }
            public string field0002 { get; set; }
            public string field0003 { get; set; }
            public string field0004 { get; set; }
            public string field0005 { get; set; }
            public List<string> sort { get; set; }
            public long ratify_member_id { get; set; }
            public long start_member_id { get; set; }
            public long approve_member_id { get; set; }
            public int finishedflag { get; set; }
            public long modify_date { get; set; }
        }

        public class QulityRoot
        {
            public long StartDate { get; set; }
            public RowData RowData { get; set; }
            public string code { get; set; }
            public object ApproveDate { get; set; }
            public int FinishedFlag { get; set; }
            public int State { get; set; }
            public int Sort { get; set; }
            public string DataJson { get; set; }
            public long id { get; set; }
            public DataMap DataMap { get; set; }
        }


    }
}
