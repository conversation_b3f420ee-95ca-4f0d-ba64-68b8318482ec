using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SignPDF.DTO
{
    public class BJasonOutPut
    {

        public class Rootobject
        {
            public long StartDate { get; set; }
            public Rowdata RowData { get; set; }
            public string code { get; set; }
            public object ApproveDate { get; set; }
            public int FinishedFlag { get; set; }
            public int State { get; set; }
            public int Sort { get; set; }
            public string DataJson { get; set; }
            public long id { get; set; }
            public Datamap DataMap { get; set; }
        }

        public class Rowdata
        {
            public long id { get; set; }
            public int state { get; set; }
            public long start_member_id { get; set; }
            public long start_date { get; set; }
            public int approve_member_id { get; set; }
            public object approve_date { get; set; }
            public int finishedflag { get; set; }
            public int ratifyflag { get; set; }
            public int ratify_member_id { get; set; }
            public object ratify_date { get; set; }
            public int sort { get; set; }
            public long modify_member_id { get; set; }
            public long modify_date { get; set; }
            public long field0002 { get; set; }
            public string field0015 { get; set; }
            public string field0016 { get; set; }
            public string field0017 { get; set; }
            public string field0018 { get; set; }
            public string field0022 { get; set; }
            public string field0023 { get; set; }
            public string field0024 { get; set; }
            public long field0025 { get; set; }
            public long field0027 { get; set; }
            public long field0028 { get; set; }
            public object field0029 { get; set; }
            public string field0030 { get; set; }
            public string field0019 { get; set; }
            public string field0020 { get; set; }
            public string field0021 { get; set; }
            public string field0031 { get; set; }
            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public long field0035 { get; set; }
            public long field0034 { get; set; }
            public object field0036 { get; set; }
            public object field0037 { get; set; }
            public double field0038 { get; set; }
            public string field0039 { get; set; }
            public object field0040 { get; set; }
            public object field0041 { get; set; }
            public object field0042 { get; set; }
            public long field0043 { get; set; }
            public object field0044 { get; set; }
        }

        public class Datamap
        {
            public string field0031 { get; set; }
            public string[] field0033 { get; set; }
            public long field0034 { get; set; }
            [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
            public long field0035 { get; set; }
            public long[] formmain_id { get; set; }
            public object field0036 { get; set; }
            public string field0015 { get; set; }
            public string field0016 { get; set; }
            public string field0030 { get; set; }
            public long field0028 { get; set; }
            public object field0029 { get; set; }
            public long[] id { get; set; }
            public int state { get; set; }
            public long modify_member_id { get; set; }
            public object approve_date { get; set; }
            public object ratify_date { get; set; }
            public long start_date { get; set; }
            public string field0020 { get; set; }
            public string field0021 { get; set; }
            public string field0022 { get; set; }
            public int ratifyflag { get; set; }
            public string field0023 { get; set; }
            public long field0002 { get; set; }
            public string field0024 { get; set; }
            public long field0025 { get; set; }
            public long field0027 { get; set; }
            public string[] sort { get; set; }
            public int ratify_member_id { get; set; }
            public long start_member_id { get; set; }
            public int approve_member_id { get; set; }
            public int finishedflag { get; set; }
            public string field0017 { get; set; }
            public string field0018 { get; set; }
            public string field0019 { get; set; }
            public long modify_date { get; set; }
        }

    }
}
