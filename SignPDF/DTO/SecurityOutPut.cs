using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SignPDF.DTO
{
    public class SecurityOutPut
    {
        // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
        public class RowData
        {
            public long id { get; set; }
            public int state { get; set; }
            public long start_member_id { get; set; }
            public long start_date { get; set; }
            public long approve_member_id { get; set; }
            public object approve_date { get; set; }
            public int finishedflag { get; set; }
            public int ratifyflag { get; set; }
            public long ratify_member_id { get; set; }
            public object ratify_date { get; set; }
            public int sort { get; set; }
            public long modify_member_id { get; set; }
            public long modify_date { get; set; }
            public object field0001 { get; set; }
            public object field0003 { get; set; }
            public string field0004 { get; set; }
            public string field0005 { get; set; }
            public string field0006 { get; set; }
            public string field0007 { get; set; }
            public string field0008 { get; set; }
            public string field0009 { get; set; }
            public string field0010 { get; set; }
            public string field0011 { get; set; }
            public string field0012 { get; set; }
            public string field0013 { get; set; }
            public int field0014 { get; set; }
            public int field0015 { get; set; }
            public int field0016 { get; set; }
            public int field0017 { get; set; }
            public int field0018 { get; set; }
            public int field0019 { get; set; }
            public int field0020 { get; set; }
            public int field0021 { get; set; }
            public int field0022 { get; set; }
            public int field0023 { get; set; }
            public int field0024 { get; set; }
            public int field0025 { get; set; }
            public int field0026 { get; set; }
            public int field0027 { get; set; }
            public int field0028 { get; set; }
            public int field0029 { get; set; }
            public int field0030 { get; set; }
            public int field0031 { get; set; }
            public int field0032 { get; set; }
            public int field0033 { get; set; }
            public int field0034 { get; set; }
            public int field0035 { get; set; }
            public int field0036 { get; set; }
            public int field0037 { get; set; }
            public int field0038 { get; set; }
            public int field0039 { get; set; }
            public int field0040 { get; set; }
            public int field0041 { get; set; }
            public int field0042 { get; set; }
            public int field0043 { get; set; }
            public int field0044 { get; set; }
            public int field0045 { get; set; }
            public int field0046 { get; set; }
            public int field0047 { get; set; }
            public int field0048 { get; set; }
            public int field0049 { get; set; }
            public int field0050 { get; set; }
            public int field0051 { get; set; }
            public int field0052 { get; set; }
            public int field0053 { get; set; }
            public int field0054 { get; set; }
            public int field0055 { get; set; }
            public int field0056 { get; set; }
            public int field0057 { get; set; }
            public int field0058 { get; set; }
            public int field0059 { get; set; }
            public int field0060 { get; set; }
            public int field0061 { get; set; }
            public int field0062 { get; set; }
            public int field0063 { get; set; }
            public int field0064 { get; set; }
            public string field0084 { get; set; }
            public string field0085 { get; set; }
            public string field0086 { get; set; }
            public string field0087 { get; set; }
            public string field0088 { get; set; }
            public string field0089 { get; set; }
            public string field0090 { get; set; }
            public string field0091 { get; set; }
            public string field0092 { get; set; }
            public string field0093 { get; set; }
            public string field0099 { get; set; }
            public string field0100 { get; set; }
            public string field0101 { get; set; }
            public string field0102 { get; set; }
            public string field0103 { get; set; }
            public string field0104 { get; set; }
            public string field0105 { get; set; }
            public string field0106 { get; set; }
            public string field0107 { get; set; }
            public string field0108 { get; set; }
            public string field0109 { get; set; }
            public string field0110 { get; set; }
            public string field0111 { get; set; }
            public string field0112 { get; set; }
            public string field0113 { get; set; }
            public string field0114 { get; set; }
            public string field0115 { get; set; }
            public string field0116 { get; set; }
            public string field0117 { get; set; }
            public string field0118 { get; set; }
            public string field0119 { get; set; }
            public string field0120 { get; set; }
            public string field0121 { get; set; }
            public string field0122 { get; set; }
            public string field0123 { get; set; }
            public string field0124 { get; set; }
            public object field0125 { get; set; }
            public object field0126 { get; set; }
            public string field0127 { get; set; }
            public string field0128 { get; set; }
            public string field0129 { get; set; }
            public string field0130 { get; set; }
            public string field0131 { get; set; }
            public object field0132 { get; set; }
            public object field0133 { get; set; }
            public string field0134 { get; set; }
            public string field0135 { get; set; }
            public string field0136 { get; set; }
            public string field0137 { get; set; }
            public string field0138 { get; set; }
            public string field0139 { get; set; }
            public string field0140 { get; set; }
            public string field0141 { get; set; }
            public string field0142 { get; set; }
            public string field0143 { get; set; }
            public string field0144 { get; set; }
            public string field0145 { get; set; }
            public string field0146 { get; set; }
            public string field0147 { get; set; }
            public string field0148 { get; set; }
            public string field0149 { get; set; }
            public string field0150 { get; set; }
            public string field0151 { get; set; }
            public string field0152 { get; set; }
            public string field0153 { get; set; }
            public string field0154 { get; set; }
            public string field0155 { get; set; }
            public object field0156 { get; set; }
            public string field0159 { get; set; }
            public string field0160 { get; set; }
            public long field0002 { get; set; }
            public long field0161 { get; set; }
            public string field0068 { get; set; }
            public string field0069 { get; set; }
            public string field0070 { get; set; }
            public string field0071 { get; set; }
            public string field0072 { get; set; }
            public string field0073 { get; set; }
            public string field0074 { get; set; }
            public string field0075 { get; set; }
            public string field0076 { get; set; }
            public object field0077 { get; set; }
            public string field0078 { get; set; }
            public string field0079 { get; set; }
            public string field0080 { get; set; }
            public string field0081 { get; set; }
            public string field0082 { get; set; }
            public string field0083 { get; set; }
            public string field0065 { get; set; }
            public string field0066 { get; set; }
            public string field0067 { get; set; }
            public object field0162 { get; set; }
            public object field0163 { get; set; }
            public object field0164 { get; set; }
            public object field0165 { get; set; }
            public object field0166 { get; set; }
            public object field0167 { get; set; }
            public object field0168 { get; set; }
            public object field0169 { get; set; }
            public object field0170 { get; set; }
            public object field0171 { get; set; }
            public object field0172 { get; set; }
            public object field0173 { get; set; }
            public object field0174 { get; set; }
            public object field0179 { get; set; }
            public object field0180 { get; set; }
            public object field0181 { get; set; }
            public object field0182 { get; set; }
            public object field0183 { get; set; }
            public object field0184 { get; set; }
            public object field0185 { get; set; }
            public object field0186 { get; set; }
            public object field0187 { get; set; }
            public object field0188 { get; set; }
            public object field0189 { get; set; }
            public object field0190 { get; set; }
            public int field0191 { get; set; }
            public object field0192 { get; set; }
            public object field0193 { get; set; }
            public object field0194 { get; set; }
            public object field0195 { get; set; }
            public object field0196 { get; set; }
            public object field0197 { get; set; }
            public object field0198 { get; set; }
            public object field0199 { get; set; }
            public object field0200 { get; set; }
            public object field0201 { get; set; }
            public object field0202 { get; set; }
            public object field0203 { get; set; }
            public object field0204 { get; set; }
            public object field0205 { get; set; }
            public int field0206 { get; set; }
            public object field0207 { get; set; }
            public object field0208 { get; set; }
            public object field0209 { get; set; }
            public object field0210 { get; set; }
            public object field0211 { get; set; }
            public object field0212 { get; set; }
            public object field0213 { get; set; }
            public object field0214 { get; set; }
            public object field0215 { get; set; }
            public object field0216 { get; set; }
            public object field0217 { get; set; }
            public object field0218 { get; set; }
            public object field0219 { get; set; }
            public object field0220 { get; set; }
            public int field0221 { get; set; }
            public object field0222 { get; set; }
            public object field0223 { get; set; }
            public object field0224 { get; set; }
            public object field0225 { get; set; }
            public object field0226 { get; set; }
            public object field0227 { get; set; }
            public object field0228 { get; set; }
            public object field0229 { get; set; }
            public object field0230 { get; set; }
            public object field0231 { get; set; }
            public object field0232 { get; set; }
            public object field0233 { get; set; }
            public object field0234 { get; set; }
            public object field0235 { get; set; }
            public int field0236 { get; set; }
            public object field0237 { get; set; }
            public object field0238 { get; set; }
            public object field0239 { get; set; }
            public object field0240 { get; set; }
            public object field0241 { get; set; }
            public object field0242 { get; set; }
            public object field0243 { get; set; }
            public object field0244 { get; set; }
            public object field0245 { get; set; }
            public object field0246 { get; set; }
            public object field0247 { get; set; }
            public object field0248 { get; set; }
            public object field0249 { get; set; }
            public object field0250 { get; set; }
            public int field0251 { get; set; }
            public object field0252 { get; set; }
            public object field0253 { get; set; }
            public object field0254 { get; set; }
            public object field0255 { get; set; }
            public object field0256 { get; set; }
            public object field0257 { get; set; }
            public object field0258 { get; set; }
            public object field0259 { get; set; }
            public object field0260 { get; set; }
            public object field0261 { get; set; }
            public object field0262 { get; set; }
            public object field0263 { get; set; }
            public object field0264 { get; set; }
            public object field0265 { get; set; }
            public int field0266 { get; set; }
            public object field0267 { get; set; }
            public object field0268 { get; set; }
            public string field0269 { get; set; }
            public object field0270 { get; set; }
        }

        public class DataMap
        {
            public int state { get; set; }
            public object approve_date { get; set; }
            public object field0200 { get; set; }
            public object field0201 { get; set; }
            public object field0202 { get; set; }
            public object field0203 { get; set; }
            public long ratify_member_id { get; set; }
            public string field0099 { get; set; }
            public string field0090 { get; set; }
            public string field0091 { get; set; }
            public string field0092 { get; set; }
            public string field0093 { get; set; }
            public long modify_member_id { get; set; }
            public object field0230 { get; set; }
            public string field0110 { get; set; }
            public object field0231 { get; set; }
            public string field0111 { get; set; }
            public object field0232 { get; set; }
            public string field0112 { get; set; }
            public object field0233 { get; set; }
            public string field0113 { get; set; }
            public object field0234 { get; set; }
            public string field0114 { get; set; }
            public object field0235 { get; set; }
            public string field0115 { get; set; }
            public int field0236 { get; set; }
            public string field0105 { get; set; }
            public object field0226 { get; set; }
            public string field0106 { get; set; }
            public object field0227 { get; set; }
            public string field0107 { get; set; }
            public object field0228 { get; set; }
            public string field0108 { get; set; }
            public object field0229 { get; set; }
            public string field0109 { get; set; }
            public object field0240 { get; set; }
            public string field0120 { get; set; }
            public object field0241 { get; set; }
            public string field0121 { get; set; }
            public object field0242 { get; set; }
            public int ratifyflag { get; set; }
            public object field0001 { get; set; }
            public string field0122 { get; set; }
            public object field0243 { get; set; }
            public string field0123 { get; set; }
            public long field0002 { get; set; }
            public object field0244 { get; set; }
            public object field0003 { get; set; }
            public string field0124 { get; set; }
            public object field0245 { get; set; }
            public string field0004 { get; set; }
            public object field0125 { get; set; }
            public object field0246 { get; set; }
            public string field0005 { get; set; }
            public object field0126 { get; set; }
            public object field0247 { get; set; }
            public string field0116 { get; set; }
            public object field0237 { get; set; }
            public string field0117 { get; set; }
            public object field0238 { get; set; }
            public string field0118 { get; set; }
            public object field0239 { get; set; }
            public string field0119 { get; set; }
            public object field0210 { get; set; }
            public object field0211 { get; set; }
            public object field0212 { get; set; }
            public object field0213 { get; set; }
            public object field0214 { get; set; }
            public object field0204 { get; set; }
            public object field0205 { get; set; }
            public int field0206 { get; set; }
            public object field0207 { get; set; }
            public object field0208 { get; set; }
            public object field0209 { get; set; }
            public object field0220 { get; set; }
            public string field0100 { get; set; }
            public int field0221 { get; set; }
            public string field0101 { get; set; }
            public object field0222 { get; set; }
            public string field0102 { get; set; }
            public object field0223 { get; set; }
            public string field0103 { get; set; }
            public object field0224 { get; set; }
            public string field0104 { get; set; }
            public object field0225 { get; set; }
            public long start_member_id { get; set; }
            public object field0215 { get; set; }
            public object field0216 { get; set; }
            public object field0217 { get; set; }
            public object field0218 { get; set; }
            public object field0219 { get; set; }
            public long modify_date { get; set; }
            public int field0031 { get; set; }
            public string field0152 { get; set; }
            public int field0032 { get; set; }
            public string field0153 { get; set; }
            public int field0033 { get; set; }
            public string field0154 { get; set; }
            public int field0034 { get; set; }
            public string field0155 { get; set; }
            public int field0035 { get; set; }
            public object field0156 { get; set; }
            public int field0036 { get; set; }
            public int field0037 { get; set; }
            public int field0038 { get; set; }
            public string field0159 { get; set; }
            public object field0270 { get; set; }
            public string field0150 { get; set; }
            public int field0030 { get; set; }
            public string field0151 { get; set; }
            public int field0028 { get; set; }
            public string field0149 { get; set; }
            public int field0029 { get; set; }
            public List<long> id { get; set; }
            public int field0042 { get; set; }
            public object field0163 { get; set; }
            public int field0043 { get; set; }
            public object field0164 { get; set; }
            public int field0044 { get; set; }
            public object field0165 { get; set; }
            public int field0045 { get; set; }
            public object field0166 { get; set; }
            public int field0046 { get; set; }
            public object field0167 { get; set; }
            public int field0047 { get; set; }
            public object field0168 { get; set; }
            public int field0048 { get; set; }
            public object field0169 { get; set; }
            public int field0049 { get; set; }
            public string field0160 { get; set; }
            public int field0040 { get; set; }
            public long field0161 { get; set; }
            public int field0041 { get; set; }
            public object field0162 { get; set; }
            public int finishedflag { get; set; }
            public int field0039 { get; set; }
            public string field0130 { get; set; }
            public int field0251 { get; set; }
            public string field0010 { get; set; }
            public string field0131 { get; set; }
            public object field0252 { get; set; }
            public string field0011 { get; set; }
            public object field0132 { get; set; }
            public object field0253 { get; set; }
            public string field0012 { get; set; }
            public object field0133 { get; set; }
            public object field0254 { get; set; }
            public string field0013 { get; set; }
            public string field0134 { get; set; }
            public object field0255 { get; set; }
            public int field0014 { get; set; }
            public string field0135 { get; set; }
            public object field0256 { get; set; }
            public int field0015 { get; set; }
            public string field0136 { get; set; }
            public object field0257 { get; set; }
            public int field0016 { get; set; }
            public string field0137 { get; set; }
            public object field0258 { get; set; }
            public object field0250 { get; set; }
            public string field0006 { get; set; }
            public string field0127 { get; set; }
            public object field0248 { get; set; }
            public string field0007 { get; set; }
            public string field0128 { get; set; }
            public object field0249 { get; set; }
            public string field0008 { get; set; }
            public string field0129 { get; set; }
            public string field0009 { get; set; }
            public long start_date { get; set; }
            public int field0020 { get; set; }
            public string field0141 { get; set; }
            public object field0262 { get; set; }
            public int field0021 { get; set; }
            public string field0142 { get; set; }
            public object field0263 { get; set; }
            public int field0022 { get; set; }
            public string field0143 { get; set; }
            public object field0264 { get; set; }
            public int field0023 { get; set; }
            public string field0144 { get; set; }
            public object field0265 { get; set; }
            public int field0024 { get; set; }
            public string field0145 { get; set; }
            public int field0266 { get; set; }
            public int field0025 { get; set; }
            public string field0146 { get; set; }
            public object field0267 { get; set; }
            public int field0026 { get; set; }
            public string field0147 { get; set; }
            public object field0268 { get; set; }
            public int field0027 { get; set; }
            public string field0148 { get; set; }
            public string field0269 { get; set; }
            public object field0260 { get; set; }
            public string field0140 { get; set; }
            public object field0261 { get; set; }
            public int approve_member_id { get; set; }
            public int field0017 { get; set; }
            public string field0138 { get; set; }
            public object field0259 { get; set; }
            public int field0018 { get; set; }
            public string field0139 { get; set; }
            public int field0019 { get; set; }
            public string field0075 { get; set; }
            public object field0196 { get; set; }
            public string field0076 { get; set; }
            public object field0197 { get; set; }
            public object field0077 { get; set; }
            public object field0198 { get; set; }
            public string field0078 { get; set; }
            public object field0199 { get; set; }
            public string field0079 { get; set; }
            public object field0190 { get; set; }
            public string field0070 { get; set; }
            public int field0191 { get; set; }
            public string field0071 { get; set; }
            public object field0192 { get; set; }
            public string field0072 { get; set; }
            public object field0193 { get; set; }
            public string field0073 { get; set; }
            public object field0194 { get; set; }
            public string field0074 { get; set; }
            public object field0195 { get; set; }
            public object ratify_date { get; set; }
            public string field0086 { get; set; }
            public string field0087 { get; set; }
            public string field0088 { get; set; }
            public string field0089 { get; set; }
            public string field0080 { get; set; }
            public List<string> sort { get; set; }
            public string field0081 { get; set; }
            public string field0082 { get; set; }
            public string field0083 { get; set; }
            public string field0084 { get; set; }
            public string field0085 { get; set; }
            public int field0053 { get; set; }
            public object field0174 { get; set; }
            public int field0054 { get; set; }
            public List<int> field0175 { get; set; }
            public int field0055 { get; set; }
            public List<object> field0176 { get; set; }
            public int field0056 { get; set; }
            public List<object> field0177 { get; set; }
            public int field0057 { get; set; }
            public List<long> formmain_id { get; set; }
            public List<object> field0178 { get; set; }
            public int field0058 { get; set; }
            public object field0179 { get; set; }
            public int field0059 { get; set; }
            public object field0170 { get; set; }
            public int field0050 { get; set; }
            public object field0171 { get; set; }
            public int field0051 { get; set; }
            public object field0172 { get; set; }
            public int field0052 { get; set; }
            public object field0173 { get; set; }
            public int field0064 { get; set; }
            public object field0185 { get; set; }
            public string field0065 { get; set; }
            public object field0186 { get; set; }
            public string field0066 { get; set; }
            public object field0187 { get; set; }
            public string field0067 { get; set; }
            public object field0188 { get; set; }
            public string field0068 { get; set; }
            public object field0189 { get; set; }
            public string field0069 { get; set; }
            public object field0180 { get; set; }
            public int field0060 { get; set; }
            public object field0181 { get; set; }
            public int field0061 { get; set; }
            public object field0182 { get; set; }
            public int field0062 { get; set; }
            public object field0183 { get; set; }
            public int field0063 { get; set; }
            public object field0184 { get; set; }
        }

        public class SecurityRoot
        {
            public long StartDate { get; set; }
            public RowData RowData { get; set; }
            public string code { get; set; }
            public object ApproveDate { get; set; }
            public int FinishedFlag { get; set; }
            public int State { get; set; }
            public int Sort { get; set; }
            public string DataJson { get; set; }
            public long id { get; set; }
            public DataMap DataMap { get; set; }
        }


    }
}
