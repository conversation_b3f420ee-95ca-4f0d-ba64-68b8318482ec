using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Data.SqlClient;
using System.Net.Http;
using Nancy.Json;
using ScanToSign.DTO;
using ScanToSign.PDF;
using System.Data;
using System.IO;
using Microsoft.VisualBasic;
using NLog;
using System.Diagnostics;
using Newtonsoft.Json.Linq;
using SignPDF.PDF;
using SignPDF.DTO;
using System.Web;
using Utils;
using Newtonsoft.Json;
using static SignPDF.DTO.BJasonOutPut;
using static SignPDF.DTO.SecurityOutPut;
using static SignPDF.DTO.QJasonOutPut;
using System.Text.RegularExpressions;
using static SignPDF.DTO.PJasonOutPut;
using static SignPDF.DTO.JsonIn;
using System.Collections;
using System.Text;
using System.Net.Mail;
using System.Net;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using iText.StyledXmlParser.Jsoup.Select;
using iText.StyledXmlParser.Jsoup;
using System.Collections.Concurrent;
using OpenQA.Selenium.Remote;
using Microsoft.Extensions.Logging;
using SignPDF.Controllers;

namespace ScanToSign
{

    public class ScanCheck
    {
        string classtype = "", reportdate = "";
        string folderName = @"\\*********\保險公司\COIL_DATA\COIL_ePolicy\";
        string folderNameCopy = @"\\*********\保險公司\COIL_DATA\COIL_ePolicyCopy\";
        string Conn = "Data Source=COB-SERVER-090;User ID=common;PassWord=**********;Initial Catalog=live_ilodata;MultipleActiveResultSets=True;Connection Timeout=400";
        string Conn1 = "Data Source=COB-SERVER-188;User ID=SSIS;PassWord=*********;Initial Catalog=CSCI_DATASOURCE;MultipleActiveResultSets=True;Connection Timeout=400";

        private readonly ILogger<ScanCheck> _logger;

        public ScanCheck(ILogger<ScanCheck> logger)
        {
            _logger = logger;
        }

        /// 截取字符串中开始和结束字符串中间的字符串
        /// </summary>
        /// <param name="source">源字符串</param>
        /// <param name="startStr">开始字符串</param>
        /// <param name="endStr">结束字符串</param>
        /// <returns>中间字符串</returns>
        public string SubstringSingle(string source, string startStr, string endStr)
        {
            Regex rg = new Regex("(?<=(" + startStr + "))[.\\s\\S]*?(?=(" + endStr + "))", RegexOptions.Multiline | RegexOptions.Singleline);
            return rg.Match(source).Value;
        }

        /// <summary>
        /// （批量）截取字符串中开始和结束字符串中间的字符串
        /// </summary>
        /// <param name="source">源字符串</param>
        /// <param name="startStr">开始字符串</param>
        /// <param name="endStr">结束字符串</param>
        /// <returns>中间字符串</returns>
        public List<string> SubstringMultiple(string source, string startStr, string endStr)
        {
            Regex rg = new Regex("(?<=(" + startStr + ")).{1,200}(?=(" + endStr + "))", RegexOptions.Multiline | RegexOptions.Singleline);

            MatchCollection matches = rg.Matches(source.Replace(" ", "").Replace("\r\n", ""));

            List<string> resList = new List<string>();

            foreach (Match item in matches)
                resList.Add(item.Value);

            return resList;

        }

        //获取到本地的Json文件并且解析返回对应的json字符串
        public static string GetJsonFile(string filepath)
        {
            string json = string.Empty;
            using (FileStream fs = new FileStream(filepath, FileMode.OpenOrCreate, System.IO.FileAccess.ReadWrite, FileShare.ReadWrite))
            {
                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                using (StreamReader sr = new StreamReader(fs, Encoding.GetEncoding("utf-8")))
                {
                    json = sr.ReadToEnd().ToString();
                }
            }
            return json;
        }


        public string Scan()
        {
            HttpClient client = new HttpClient();
            HttpResponseMessage response = client.GetAsync("https://pur.csci.com.hk/push/api/push?Id=anna_miao").Result;
            var result = response.Content.ReadAsStringAsync().Result;
            var s = Newtonsoft.Json.JsonConvert.DeserializeObject(result);
            JavaScriptSerializer js = new JavaScriptSerializer();//实例化一个能够序列化数据的类
            JsonOutput oput = js.Deserialize<JsonOutput>(s.ToString()); //将json数据转化为对象类型并赋值给list
            return oput.Result.ToString();
        }
        public int InsertECO()
        {
            using (SqlConnection connection = new SqlConnection(Conn))
            {
                string sql = "INSERT INTO [ECO].[dbo].[Notification] " +
                    "select 'T_Report_O' as Type,a.ID,b.Name,a.ApplyerName,c.Email, " +
                    "EmployeeName,CONVERT(DATE, HappenedDate),case when a.ToComp = '1' then '中海保險' else '其他' end as ToComp, " +
                    "isnull(Fclmno, '-') as Fclmno,GETDATE(),'0',null " +
                    "from [ECO].[dbo].T_Report_O a " +
                    "left join [ECO].[dbo].T_DEPARTMENT b on a.SiteName = b.ID " +
                    "left join [ECO].[dbo].T_USER c on a.UserID = c.ID " +
                    "where a.id in " +
                    "(select max(ID) from [ECO].[dbo].T_Report_O group by replace(VersionID, '-1', '')) " +
                    "and GETDATE() > DATEADD(DAY, 75, HappenedDate) and HappenedDate > '2021-01-01' and EmployeeName<>'test' " +
                    "and isnull(Fclmno,'-')  not in ('Double', 'cancel') " +
                    "and(a.EmployeeName not in (select EmployeeName from [ECO].[dbo].[Notification]) and a.HappenedDate not in (select HappenedDate from [ECO].[dbo].[Notification])) " +
                    "union " +
                    "select 'T_APPLY_2' as Type,a.ID,b.Name,a.ApplyerName,c.Email, " +
                    "EmployeeName,CONVERT(DATE, HappenedDate),case when a.ToComp = '1' then '中海保險' else '其他' end as ToComp, " +
                    "isnull(Fclmno, '-') as Fclmno,GETDATE(),'0',null " +
                    "from [ECO].[dbo].T_APPLY_2 a " +
                    "left join [ECO].[dbo].T_DEPARTMENT b on a.SiteName = b.ID " +
                    "left join [ECO].[dbo].T_USER c on a.UserID = c.ID " +
                    "where GETDATE() > DATEADD(DAY, 75, HappenedDate) and HappenedDate > '2021-01-01' and EmployeeName<>'test' " +
                    "and isnull(Fclmno,'-')  not in ('Double', 'cancel') " +
                    "and(a.EmployeeName not in (select EmployeeName from [ECO].[dbo].[Notification]) and a.HappenedDate not in (select HappenedDate from [ECO].[dbo].[Notification]))";
                SqlCommand command = new SqlCommand(sql, connection);
                command.Connection.Open();
                int record = command.ExecuteNonQuery();
                return record;
            }
        }
        public void ECO()
        {
            using (SqlConnection connection = new SqlConnection(Conn))
            {
                DataTable dt = new DataTable("Notification");
                string sql = "select * from [ECO].[dbo].[Notification] where[Index] in (SELECT max([Index]) FROM [ECO].[dbo].[Notification] group by HappenedDate,EmployeeName) and SendEmail = 0";
                SqlCommand command = new SqlCommand(sql, connection);
                command.Connection.Open();
                var reader = command.ExecuteReader();
                dt.Load(reader);
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    string EmailTo = dt.Rows[i]["Email"].ToString().Trim();
                    DateTime pmdate = (DateTime)dt.Rows[i]["HappenedDate"];
                    string HappenedDate = string.Format("{0:yyyy-MM-dd}", pmdate);
                    string EmployeeName = dt.Rows[i]["EmployeeName"].ToString().Trim();
                    string DepartName = dt.Rows[i]["DepartName"].ToString().Trim();
                    string ApplyerName = dt.Rows[i]["ApplyerName"].ToString().Trim();
                    string Fclmno = dt.Rows[i]["Fclmno"].ToString().Trim();
                    string ToComp = dt.Rows[i]["ToComp"].ToString().Trim();

                    string result = SendEmail.sendtoApplyName(EmailTo, HappenedDate, EmployeeName, DepartName, ApplyerName, Fclmno, ToComp);
                    if (result == "發送成功")
                    {
                        using (SqlConnection connection1 = new SqlConnection(Conn))
                        {
                            string sql1 = "update [ECO].[dbo].[Notification] set SendEmail = 1,SendDate=GETDATE() where HappenedDate='" + HappenedDate + "' and EmployeeName = N'" + dt.Rows[i]["EmployeeName"].ToString().Trim() + "'";
                            SqlCommand command1 = new SqlCommand(sql1, connection1);
                            command1.Connection.Open();
                            command1.ExecuteNonQuery();
                        }
                    }
                }
            }
        }

        public void Bond()
        {
            if (DateTime.Now.DayOfWeek == DayOfWeek.Saturday || DateTime.Now.DayOfWeek == DayOfWeek.Sunday)
            {
                return;
            }
            using (SqlConnection connection = new SqlConnection(Conn))
            {
                DataTable dt = new DataTable("BondData");
                string sql = "select [BondName],[BondSin],floor([Share]) as [Share], " +
                        "[BuyPrice], CAST(round([Cost], 2) as decimal(38, 2)) as [Cost], " +
                        "CAST([Price] as decimal(38, 4)) as [Price],CAST(round([CurrentValue], 2) as decimal(38, 2)) as CurrentValue, " +
                        "CAST(round([result], 2) as decimal(38, 2)) as [result] from [live_ilodata].[dbo].[View_BondCaculate] ";
                SqlCommand command = new SqlCommand(sql, connection);
                command.Connection.Open();
                var reader = command.ExecuteReader();
                dt.Load(reader);
                _logger.LogInformation("get email data success, data count: " + dt.Rows.Count.ToString());
                if (dt.Rows.Count != 10)
                {
                    return;
                }
                if (SendMailBond(dt))
                {
                    _logger.LogInformation("send email success");
                    using (SqlConnection connection1 = new SqlConnection(Conn))
                    {
                        string sql1 = "update [live_ilodata].[dbo].[BondData] set [IsSend] = 1,SendDate=GETDATE() where [IsSend] is null";
                        SqlCommand command1 = new SqlCommand(sql1, connection1);
                        command1.Connection.Open();
                        command1.ExecuteNonQuery();
                    }
                }
            }
        }

        public bool SendMailBond(DataTable dt)
        {
            if (dt.Rows.Count > 1)
            {
                #region 绘制表格
                StringBuilder sbBody = new StringBuilder();
                string strSubject = DateTime.Now.ToString("yyyy") + "年" + DateTime.Now.ToString("MM") + "月" + DateTime.Now.ToString("dd") + "日持仓债券情况";
                sbBody.Length = 0;
                sbBody.AppendLine("<b>" + DateTime.Now.ToString("yyyy-MM-dd") + "數據統計明細如下，單位:萬港元</b><br /><br />");
                sbBody.AppendLine("<table border=\"1\" cellpadding=\"0\" cellspacing=\"0\">");
                sbBody.AppendLine("<tr>");

                sbBody.AppendLine("<th width=\"27%\">");
                sbBody.AppendLine("債券");
                sbBody.AppendLine("</th>");

                sbBody.AppendLine("<th width=\"13%\">");
                sbBody.AppendLine("ISIN");
                sbBody.AppendLine("</th>");

                sbBody.AppendLine("<th width=\"10%\">");
                sbBody.AppendLine("份額<br/>萬美元");
                sbBody.AppendLine("</th>");

                sbBody.AppendLine("<th width=\"10%\">");
                sbBody.AppendLine("買入價");
                sbBody.AppendLine("</th>");

                sbBody.AppendLine("<th width=\"10%\">");
                sbBody.AppendLine("成本");
                sbBody.AppendLine("</th>");

                sbBody.AppendLine("<th width=\"10%\">");
                sbBody.AppendLine("市價");
                sbBody.AppendLine("</th>");

                sbBody.AppendLine("<th width=\"10%\">");
                sbBody.AppendLine("市值");
                sbBody.AppendLine("</th>");

                sbBody.AppendLine("<th width=\"10%\">");
                sbBody.AppendLine("賬面盈(虧)");
                sbBody.AppendLine("</th>");

                sbBody.AppendLine("</tr>");
                decimal totalCost = 0, totalCurrentValue = 0, totalresult = 0;
                foreach (DataRow row in dt.Rows)
                {
                    sbBody.AppendLine("<tr>");
                    for (int i = 0; i < dt.Columns.Count; i++)
                    {
                        if (i == 0)
                        {
                            if (row["BondName"].ToString().Contains("碧桂園"))
                            {
                                sbBody.AppendLine("<td align=\"left\">  <font color=\"#FF0000\" > ");
                            }
                            else
                            {
                                sbBody.AppendLine("<td align=\"left\">");
                            }
                        }
                        else
                        {

                            if (row["BondName"].ToString().Contains("碧桂園"))
                            {
                                sbBody.AppendLine("<td align=\"center\"> <font color=\"#FF0000\" >");
                            }
                            else
                            {
                                sbBody.AppendLine("<td align=\"center\"> ");
                            }
                        }
                        if (i == 4 || i == 6)
                        { sbBody.AppendLine(Convert.ToDecimal(Convert.ToString(row[i].ToString())).ToString("N")); }
                        else
                        { sbBody.AppendLine(row[i].ToString()); }

                        sbBody.AppendLine("</td>");
                    }
                    sbBody.AppendLine("</tr>");
                }
                for (int j = 0; j < dt.Rows.Count; j++)
                {
                    totalCost = totalCost + Convert.ToDecimal(dt.Rows[j]["Cost"].ToString());
                    totalCurrentValue = totalCurrentValue + Convert.ToDecimal(dt.Rows[j]["CurrentValue"].ToString());
                    totalresult = totalresult + Convert.ToDecimal(dt.Rows[j]["result"].ToString());
                }
                #region 添加汇总行

                sbBody.AppendLine("<tr>");
                sbBody.AppendLine("<td align=\"center\">");
                sbBody.AppendLine("<b>總計</b>");
                sbBody.AppendLine("</td>");
                sbBody.AppendLine("<td align=\"center\">");
                sbBody.AppendLine("</td>");
                sbBody.AppendLine("<td align=\"center\">");
                sbBody.AppendLine("</td>");
                sbBody.AppendLine("<td align=\"center\">");
                sbBody.AppendLine("</td>");

                sbBody.AppendLine("<td align=\"center\">");
                sbBody.AppendLine("<b>" + totalCost.ToString("N") + "</b>");
                sbBody.AppendLine("</td>");

                sbBody.AppendLine("<td align=\"center\">");

                sbBody.AppendLine("</td>");

                sbBody.AppendLine("<td align=\"center\">");
                sbBody.AppendLine("<b>" + totalCurrentValue.ToString("N") + "</b>");
                sbBody.AppendLine("</td>");

                sbBody.AppendLine("<td align=\"center\">");
                sbBody.AppendLine("<b>" + totalresult.ToString("N") + "</b>");
                sbBody.AppendLine("</td>");
                sbBody.AppendLine("</tr>");

                #endregion
                sbBody.AppendLine("</table>");
                #endregion

                #region 发送邮件
                try
                {
                    SmtpClient mailServer = new SmtpClient("owahk.cohl.com", 25);
                    //邮件发送方式  通过网络发送到smtp服务器
                    mailServer.DeliveryMethod = SmtpDeliveryMethod.Network;

                    //如果服务器支持安全连接，则将安全连接设为true
                    mailServer.EnableSsl = true;

                    //是否使用默认凭据，若为false，则使用自定义的证书，就是下面的networkCredential实例对象
                    mailServer.UseDefaultCredentials = false;

                    //指定邮箱账号和密码,需要注意的是授权码
                    NetworkCredential networkCredential = new NetworkCredential("<EMAIL>", "Cce3311#");

                    //认证
                    mailServer.Credentials = networkCredential;

                    ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(ValidateServerCertificate);

                    string from = "<EMAIL>";
                    string to = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"; //

                    MailMessage msg = new MailMessage(from, to);
                    msg.Priority = MailPriority.Normal;
                    msg.IsBodyHtml = true;
                    msg.Subject = strSubject; //邮件标题
                                              //msg.SubjectEncoding = Encoding.GetEncoding(936); //如果你的邮件标题包含中文，这里一定要指定，否则对方收到的极有可能是乱码。
                    msg.IsBodyHtml = true; //邮件正文是否是HTML格式
                                           //msg.BodyEncoding = Encoding.GetEncoding(936); //邮件正文的编码， 设置不正确， 接收者会收到乱码
                    msg.Bcc.Add("<EMAIL>");
                    msg.Body = sbBody.ToString(); //邮件正文
                    mailServer.Send(msg);
                    return true;
                }
                catch (Exception ex)
                {
                    return false;
                }

                #endregion
            }
            else
            {
                return false;
            }
        }

        public static bool ValidateServerCertificate(Object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors)
        {
            return true;
        }

        public void Send()
        {
            string filename = "";
            using (SqlConnection connection = new SqlConnection(Conn))
            {
                DataTable dt = new DataTable("polh");
                string sql = "select * from polh where len(fctldel)> 10 and fctldel LIKE '%Finished%' and fctldel NOT LIKE '%Send%'";
                SqlCommand command = new SqlCommand(sql, connection);
                command.Connection.Open();
                var reader = command.ExecuteReader();
                dt.Load(reader);
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    classtype = dt.Rows[i]["fclass"].ToString().Trim() + @"\";
                    folderName = @"\\*********\保險公司\COIL_DATA\COIL_ePolicy\" + classtype;
                    filename = dt.Rows[i]["fpolno"].ToString().Trim();
                    if (Directory.Exists(folderName + filename.Trim() + "\\"))
                    {
                        string result = SendEmail.sendtoCus(filename.Trim(), "", classtype);
                        if (result == "發送成功")
                        {
                            using (SqlConnection connection1 = new SqlConnection(Conn))
                            {
                                string sql1 = "update polh set fctldel = concat(isnull(rtrim(fctldel),''),' Send') where fctlid = '" + dt.Rows[i]["fctlid"].ToString().Trim() + "'";
                                SqlCommand command1 = new SqlCommand(sql1, connection1);
                                command1.Connection.Open();
                                command1.ExecuteNonQuery();
                            }
                        }
                    }
                }
            }
        }

        public void State()
        {
            using (SqlConnection connection = new SqlConnection(Conn))
            {
                DataTable dt = new DataTable("ostat2");
                string sql = "SELECT * FROM [ostat2] where(摘要 like '%CN-%' OR 摘要 like '%DN-%') and(制单人 <> N'趙雅儀' and  制单人 <> '趙雅儀')";
                SqlCommand command = new SqlCommand(sql, connection);
                command.Connection.Open();
                var reader = command.ExecuteReader();
                dt.Load(reader);
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    string EXPLANATION = dt.Rows[i]["摘要"].ToString().Trim();
                    string refno1Original = dt.Rows[i]["refno1"].ToString().Trim();

                    string pattern = @"CN-[0-9]+(-[0-9]+)*"; string refno1 = "";
                    Regex regex = new Regex(pattern);
                    MatchCollection matches = regex.Matches(EXPLANATION);
                    foreach (Match match in matches)
                    { refno1 = match.Value.ToString(); }

                    if (refno1 == "")
                    {
                        string pattern1 = @"DN-[0-9]+(-[0-9]+)*";
                        Regex regex1 = new Regex(pattern1);
                        MatchCollection matches1 = regex1.Matches(EXPLANATION);
                        foreach (Match match in matches1)
                        { refno1 = match.Value.ToString(); }
                    }



                    string id = dt.Rows[i]["id"].ToString().Trim();
                    if (refno1 != "")
                    {
                        string[] sArray1 = refno1.Split('-');
                        string newfinvno = "", value = "";
                        if (sArray1.Length != 0)
                        {
                            for (int j = 0; j < sArray1.Length; j++)
                            {
                                value = sArray1[j];

                                if (j == 2)
                                {
                                    value = value.PadLeft(5, '0');
                                }
                                if (newfinvno != "") { newfinvno = newfinvno + "-" + value; }
                                else { newfinvno = value; }
                            }
                        }
                        if (refno1Original != newfinvno)
                        {
                            using (SqlConnection connection1 = new SqlConnection(Conn))
                            {
                                string sql1 = "  update [ostat2] set refno1 ='" + newfinvno + "' WHERE ID = '" + id + "'";
                                SqlCommand command1 = new SqlCommand(sql1, connection1);
                                command1.Connection.Open();
                                command1.ExecuteNonQuery();
                            }
                        }
                    }
                }
            }
        }

        public void Cheque()
        {
            using (SqlConnection connection = new SqlConnection(Conn))
            {
                DataTable dt = new DataTable("Cheque");
                string sql = "select b.PK_DETAIL,startdate,serialnum,fvseq,fpayee,fpayamt, " +
                    "fchequenumber,a.faccnum,fuser,b.EXPLANATION,b.EXPLANATIONDETAIL,ChequeDate from cheque a " +
                    "left join (select * from  NCRecord where subjname = '應付賠款') " +
                    "b on a.faccnum = b.faccnum and fpayamt = b.totalcredit " +
                    "where b.faccnum is not null and b.PK_DETAIL not in (select id from ocpay_cheque) " +
                    "ORDER BY A.fvseq";
                SqlCommand command = new SqlCommand(sql, connection);
                command.Connection.Open();
                var reader = command.ExecuteReader();
                dt.Load(reader);
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    string id = dt.Rows[i]["PK_DETAIL"].ToString().Trim();
                    DateTime pmdate = (DateTime)dt.Rows[i]["startdate"];
                    string startdate = string.Format("{0:yyyy-MM-dd}", pmdate);
                    string chequedate = dt.Rows[i]["ChequeDate"].ToString().Trim();
                    string serialnum = dt.Rows[i]["serialnum"].ToString().Trim();
                    string fvseq = dt.Rows[i]["fvseq"].ToString().Trim();
                    string fpayee = dt.Rows[i]["fpayee"].ToString().Trim();
                    string fpayamt = dt.Rows[i]["fpayamt"].ToString().Trim();
                    string fchequenumber = dt.Rows[i]["fchequenumber"].ToString().Trim();
                    string faccnum = dt.Rows[i]["faccnum"].ToString().Trim();
                    string fuser = dt.Rows[i]["fuser"].ToString().Trim();
                    string EXPLANATION = dt.Rows[i]["EXPLANATION"].ToString().Trim();
                    string EXPLANATIONDETAIL = dt.Rows[i]["EXPLANATIONDETAIL"].ToString().Trim();

                    string pattern = @"CN-[0-9]+(-[0-9]+)*"; string refno1 = "";
                    Regex regex = new Regex(pattern);
                    MatchCollection matches = regex.Matches(EXPLANATIONDETAIL);
                    foreach (Match match in matches)
                    { refno1 = match.Value.ToString(); }

                    if (refno1 == "")
                    {
                        string pattern1 = @"DN-[0-9]+(-[0-9]+)*";
                        Regex regex1 = new Regex(pattern1);
                        MatchCollection matches1 = regex1.Matches(EXPLANATIONDETAIL);
                        foreach (Match match in matches1)
                        { refno1 = match.Value.ToString(); }
                    }

                    //string[] sArray = EXPLANATIONDETAIL.Split(' ');
                    //string finvno = sArray[0];
                    string[] sArray1 = refno1.Split('-');
                    string newfinvno = "", value = "";
                    if (sArray1.Length != 0)
                    {
                        for (int j = 0; j < sArray1.Length; j++)
                        {
                            value = sArray1[j];

                            if (j == 2)
                            {
                                value = value.PadLeft(5, '0');
                            }
                            if (newfinvno != "") { newfinvno = newfinvno + "-" + value; }
                            else { newfinvno = value; }
                        }
                    }
                    using (SqlConnection connection1 = new SqlConnection(Conn))
                    {
                        string sql1 = "INSERT INTO [dbo].[ocpay_cheque]([ID],[startdate],[serialnum]," +
                        "[fvseq],[fpayee],[fpayamt],[fchequenumber],[faccnum],[fremark],[fuser]," +
                        "[finvno],[fclmno],[EXPLANATION],[EXPLANATIONDETAIL],chequedate)VALUES( " +
                        "'" + id + "','" + startdate + "','" + serialnum + "','" + fvseq + "'," +
                        "'" + fpayee + "','" + fpayamt + "','" + fchequenumber + "','" + faccnum + "','','" + fuser + "'," +
                        "'" + DeleteChineseWord(newfinvno) + "','','" + EXPLANATION + "','" + EXPLANATIONDETAIL + "','" + chequedate + "')";
                        SqlCommand command1 = new SqlCommand(sql1, connection1);
                        command1.Connection.Open();
                        command1.ExecuteNonQuery();
                    }
                }
            }
        }
        public static string DeleteChineseWord(string str)
        {
            string retValue = str;
            if (System.Text.RegularExpressions.Regex.IsMatch(str, @"[\u4e00 - \u9fa5]"))
            {
                retValue = string.Empty;
                var strsStrings = str.ToCharArray();
                for (int index = 0; index < strsStrings.Length; index++)
                {
                    if (strsStrings[index] >= 0x4e00 && strsStrings[index] <= 0x9fa5)
                    {
                        continue;
                    }
                    retValue += strsStrings[index];
                }
            }
            return retValue;
        }


        public void Check()
        {
            if (Scan() == "更新记录0条")
            {
                PDFImageTest Signed = new PDFImageTest();
                using (SqlConnection connection = new SqlConnection(Conn))
                {
                    DataTable dt = new DataTable("polh");
                    string sql = "select * from polh where len(fctldel)> 10 and fctldel LIKE '%Finished%' and fctldel NOT LIKE '%Send%'";
                    //string sql = "select * from polh where fpolno ='PII-23-0001'";
                    SqlCommand command = new SqlCommand(sql, connection);
                    command.Connection.Open();
                    var reader = command.ExecuteReader();
                    dt.Load(reader);
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        classtype = dt.Rows[i]["fclass"].ToString().Trim() + @"\";
                        folderName = @"\\*********\保險公司\COIL_DATA\COIL_ePolicy\" + classtype;
                        folderNameCopy = @"\\*********\保險公司\COIL_DATA\COIL_ePolicyCopy\" + classtype;
                        string pathString = System.IO.Path.Combine(folderName, dt.Rows[i]["fpolno"].ToString().Trim() + @"\");
                        string pathStringCopy = System.IO.Path.Combine(folderNameCopy, dt.Rows[i]["fpolno"].ToString().Trim() + @"\");
                        if (!System.IO.Directory.Exists(pathString))
                        {
                            System.IO.Directory.CreateDirectory(pathString);
                        }
                        if (!System.IO.Directory.Exists(pathStringCopy))
                        {
                            System.IO.Directory.CreateDirectory(pathStringCopy);
                        }
                        if (dt.Rows[i]["ftype"].ToString().Trim() == "P")
                        {
                            if (dt.Rows[i]["fctldel"].ToString().Trim().Contains("CIFinished") && (dt.Rows[i]["fclass"].ToString().Trim() == "PMP" || dt.Rows[i]["fclass"].ToString().Trim() == "CMP"))
                            {
                                Signed.sign(dt.Rows[i]["fpolno"].ToString().Trim() + "_CI" + ".pdf", dt.Rows[i]["fclass"].ToString().Trim(), "");
                            }
                            else if (dt.Rows[i]["fctldel"].ToString().Trim().Contains("NIFinished") && dt.Rows[i]["fclass"].ToString().Trim() == "EEC")
                            {
                                Signed.sign(dt.Rows[i]["fpolno"].ToString().Trim() + "_NI" + ".pdf", dt.Rows[i]["fclass"].ToString().Trim(), "");
                            }
                            else if (dt.Rows[i]["fctldel"].ToString().Trim().Contains("POLHFinished") && (dt.Rows[i]["fclass"].ToString().Trim() == "PMP" || dt.Rows[i]["fclass"].ToString().Trim() == "CMP"))
                            {
                                Signed.sign(dt.Rows[i]["fpolno"].ToString().Trim() + "_CI" + ".pdf", dt.Rows[i]["fclass"].ToString().Trim(), "");
                                Signed.sign(dt.Rows[i]["fpolno"].ToString().Trim() + ".pdf", dt.Rows[i]["fclass"].ToString().Trim(), "");
                            }
                            else if (dt.Rows[i]["fctldel"].ToString().Trim().Contains("POLHFinished") && dt.Rows[i]["fclass"].ToString().Trim() == "EEC")
                            {
                                Signed.sign(dt.Rows[i]["fpolno"].ToString().Trim() + "_NI" + ".pdf", dt.Rows[i]["fclass"].ToString().Trim(), "");
                                Signed.sign(dt.Rows[i]["fpolno"].ToString().Trim() + ".pdf", dt.Rows[i]["fclass"].ToString().Trim(), "");
                            }
                            else
                            {
                                Signed.sign(dt.Rows[i]["fpolno"].ToString().Trim() + ".pdf", dt.Rows[i]["fclass"].ToString().Trim(), "");
                                Signed.SignRI(dt.Rows[i]["fpolno"].ToString().Trim() + "_RI" + ".pdf", dt.Rows[i]["fclass"].ToString().Trim(), "");
                            }
                        }
                        else
                        {
                            if (dt.Rows[i]["fctldel"].ToString().Trim().Contains("POLHFinished") && (dt.Rows[i]["fclass"].ToString().Trim() == "PMP" || dt.Rows[i]["fclass"].ToString().Trim() == "CMP"))
                            {
                                Signed.sign(dt.Rows[i]["fendtno"].ToString().Trim() + ".pdf", dt.Rows[i]["fclass"].ToString().Trim(), dt.Rows[i]["fpolno"].ToString().Trim());
                                Signed.sign(dt.Rows[i]["fendtno"].ToString().Trim() + "_CI" + ".pdf", dt.Rows[i]["fclass"].ToString().Trim(), dt.Rows[i]["fpolno"].ToString().Trim());
                            }
                            else
                            {
                                Signed.sign(dt.Rows[i]["fendtno"].ToString().Trim() + ".pdf", dt.Rows[i]["fclass"].ToString().Trim(), dt.Rows[i]["fpolno"].ToString().Trim());
                                Signed.SignRI(dt.Rows[i]["fendtno"].ToString().Trim() + "_RI" + ".pdf", dt.Rows[i]["fclass"].ToString().Trim(), dt.Rows[i]["fpolno"].ToString().Trim());
                            }
                        }

                        if (System.IO.Directory.GetFiles(pathString).Length == 0)
                        {
                            Directory.Delete(pathString);
                        }
                        if (System.IO.Directory.GetFiles(pathStringCopy).Length == 0)
                        {
                            Directory.Delete(pathStringCopy);
                        }
                        if (Directory.Exists(pathString))
                        {
                            using (SqlConnection connection1 = new SqlConnection(Conn))
                            {
                                string sql1 = "update polh set fctldel = concat(isnull(rtrim(fctldel),''),' Send') where fctlid = '" + dt.Rows[i]["fctlid"].ToString().Trim() + "'";
                                SqlCommand command1 = new SqlCommand(sql1, connection1);
                                command1.Connection.Open();
                                command1.ExecuteNonQuery();
                            }
                        }
                    }
                }
            }
        }



        public void CertAlertA8()
        {
            string cdmsConn = "Data Source=**********;User ID=SSIS;PassWord=*********;Initial Catalog=CSCI_DATASOURCE;MultipleActiveResultSets=True;Connection Timeout=400";
            using (SqlConnection concdms = new SqlConnection(cdmsConn))
            {
                //从 T_CW_REVENUE_Alert_A8_LOG 检查是否有当天的数据（从ExecDate判断）
                string sql1 = $@"SELECT COUNT(*) AS cnt
                                FROM T_CW_REVENUE_Alert_A8_LOG
                                WHERE CONVERT(VARCHAR(10), ExecDate, 120) = CONVERT(VARCHAR(10), GETDATE(), 120)";
                using (SqlCommand cmd = new SqlCommand(sql1, concdms))
                {
                    if (cmd.Connection.State != ConnectionState.Open)
                        cmd.Connection.Open();
                    int cnt = Convert.ToInt32(cmd.ExecuteScalar());
                    if (cnt > 0)
                    {
                        return;
                    }
                }

                DataTable dt = new DataTable("T_CW_REVENUE_Alert_A8");
                string sql = $@"SELECT 
		ID,
    LINENAME,
    CORPNAME,
    PROJCORP,
    PROJNAME,
    PERIOD,
	CERTDATE,
    Sitemanager,
    DiffDate,
    QS FROM 
(
	SELECT a.ID,
            a.LINENAME,
            a.CORPNAME,
            a.PROJCORP,
            a.PROJNAME,
            a.PERIOD + 1 AS PERIOD,
			DATEADD(day, ISNULL(pro_status.[業主付款書協議天數],0) + 30, a.cutoffdate) AS CERTDATE,
            a.Sitemanager,
            DATEDIFF(DAY, DATEADD(day, ISNULL(pro_status.[業主付款書協議天數],0) + 30, a.cutoffdate), GETDATE()) AS DiffDate,
            QS,
			SUBMITDATE,
			status,
			SiteStatus,
			Noticecert
    FROM [CSCI_DATASOURCE].[dbo].[T_CW_REVENUE_Alert_A8] a
        JOIN
                    (
                    SELECT *
                    FROM
                    (
                SELECT ROW_NUMBER() OVER(PARTITION BY PROJCORP ORDER BY YEAR DESC, MONTH DESC) xid,
                        YEAR,
                        MONTH,
                        PROJCORP AS code
                FROM[T_CW_REVENUE_Alert_A8]
                    ) t
            WHERE t.xid = 1
                    ) b
            ON a.PROJCORP = b.code
                AND a.MONTH = b.MONTH
                AND a.YEAR = b.YEAR
        JOIN
        (
            SELECT MAX([Index]) AS[index]
            FROM[T_CW_REVENUE_Alert_A8]
            GROUP BY PROJCORP
        ) c
            ON c.[index] = a.[Index]
		JOIN [CSCI_DATASOURCE].[dbo].[T_CW_PROJECT_STATUS] pro_status on pro_status.地盘编号 = a.PROJCORP
) t
WHERE SUBMITDATE >= '2022-07-29'
AND DATEDIFF(DAY, CERTDATE, GETDATE()) >= -2
AND DATEDIFF(DAY, CERTDATE, GETDATE()) <> ISNULL(Noticecert, 0)
AND ISNULL(Noticecert, 0) < 85
AND DATEDIFF(DAY, CERTDATE, GETDATE()) < 85
AND status<> 3
AND PROJCORP NOT IN( 'FDN', 'SBS', 'MGD', 'MGI', 'BJZ', 'BLW', 'BLI' )
AND PROJCORP NOT IN( SELECT distinct 地盘编号 FROM [CSCI_DATASOURCE].[dbo].[T_CW_PROJECT_STATUS] where 工程狀態 in (N'歷年竣工工程', N'本年竣工工程', N'在建工程 (內部工程,JV,服務合約等)',N'歷年竣工工程 (內部工程,JV,服務合約等)',N'本年竣工工程 (內部工程,JV,服務合約等)')) ";
                using (SqlCommand command = new SqlCommand(sql, concdms))
                {
                    if (command.Connection.State != ConnectionState.Open)
                        command.Connection.Open();
                    var reader = command.ExecuteReader();
                    dt.Load(reader);
                    if (dt.Rows.Count > 0)
                    {
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            CertAlertPushA8(dt.Rows[i]["ID"].ToString().Trim(), dt.Rows[i]["QS"].ToString().Trim(), dt.Rows[i]["LINENAME"].ToString().Trim(), dt.Rows[i]["CORPNAME"].ToString().Trim(), dt.Rows[i]["PROJCORP"].ToString().Trim(), dt.Rows[i]["PROJNAME"].ToString().Trim(), dt.Rows[i]["PERIOD"].ToString().Trim(), dt.Rows[i]["CERTDATE"].ToString().Trim(), dt.Rows[i]["Sitemanager"].ToString().Trim(), Convert.ToInt32(dt.Rows[i]["DiffDate"].ToString().Trim()));
                        }
                    }
                }
                //插入T_CW_REVENUE_Alert_A8_LOG，把dt序列化到ExecData字段，ExecDate为当前时间
                string sql2 = $@"INSERT INTO T_CW_REVENUE_Alert_A8_LOG(ExecDate, ExecData)
                                VALUES(CONVERT(VARCHAR(10), GETDATE(), 120), @dt)";
                using (SqlCommand cmd2 = new SqlCommand(sql2, concdms))
                {
                    cmd2.Parameters.Add("@dt", SqlDbType.NVarChar);
                    cmd2.Parameters["@dt"].Value = JsonConvert.SerializeObject(dt);
                    if (cmd2.Connection.State != ConnectionState.Open)
                        cmd2.Connection.Open();
                    cmd2.ExecuteNonQuery();
                }
            }
        }
        public void CertAlertPushA8(string ID, string QS, string LINENAME, string CORPNAME, string PROJCORP, string PROJNAME, string PERIOD, string CERTDATE, string Sitemanager, int DiffDate)
        {
            string cdmsConn = "Data Source=**********;User ID=SSIS;PassWord=*********;Initial Catalog=CSCI_DATASOURCE;MultipleActiveResultSets=True;Connection Timeout=400";
            var accessToken = TokenFetcher.GetUserRestToken("CDMS_FlowSubscriber");
            using (SqlConnection connection = new SqlConnection(cdmsConn))
            {
                string sqlselect = "select * from T_CW_REVENUE_Alert_A8 where ID ='" + ID + "'";
                using (SqlCommand command = new SqlCommand(sqlselect, connection))
                {



                    command.Connection.Open();
                    var reader = command.ExecuteReader();
                    reader.Read();
                    if ((reader.HasRows) && (DiffDate == -2 || DiffDate == 3 || DiffDate == 7 || DiffDate == 14 || DiffDate == 21 || DiffDate == 28 || DiffDate == 35 || DiffDate == 42 || DiffDate == 49 || DiffDate == 56 || DiffDate == 63 || DiffDate == 70 || DiffDate == 77 || DiffDate == 84))
                    {
                        DateTime dt;
                        string stringDate = "";
                        if (DateTime.TryParse(CERTDATE, out dt))
                        {
                            stringDate = dt.ToString("yyyy-MM-dd");
                        }
                        string subject = "";
                        if (DiffDate == -2)
                        {
                            subject = "!!!未提交CERT預警 提醒" + DiffDate + "天-" + PROJCORP + "," + PERIOD.Trim() + "期";
                        }
                        else
                        {
                            subject = "!!!未提交CERT預警 超過" + DiffDate + "天-" + PROJCORP + "," + PERIOD.Trim() + "期";
                        }
                        var jsonParam = new
                        {
                            appName = "collaboration",
                            data = new
                            {
                                templateCode = "altercert2",
                                draft = "0",
                                //!!! 未收業主款預警 超過10天-MCT,12期$350萬
                                subject = subject,
                                data = new
                                {
                                    formmain_64672 = new
                                    {
                                        年月 = DateTime.Now.ToString("yyyy-MM-dd"),
                                        超期天數 = DiffDate,
                                        QS = QS,
                                        LineName = CORPNAME,
                                    },
                                    formson_64673 = new
                                    {
                                        項目公司 = LINENAME,
                                        項目名稱 = PROJNAME.Trim(),
                                        預計提交cert日期 = stringDate,
                                        地盤經理 = Sitemanager,
                                        期數 = PERIOD,
                                        项目编号 = PROJCORP
                                    },

                                },
                            },
                        };
                        var jsonData = JsonConvert.SerializeObject(jsonParam);
                        string results;
                        try
                        {
                            results = HttpClientHelper.FetchResultsViaHttpPostCAP3("https://i.3311csci.com/seeyon/rest/bpm/process/start", jsonData, accessToken, "code", "message");

                            JavaScriptSerializer js = new JavaScriptSerializer();//实例化一个能够序列化数据的类
                            JsonInRoot oput = js.Deserialize<JsonInRoot>(results); //将json数据转化为对象类型并赋值给list
                            string summary = oput.data.app_bussiness_data.ToString().Replace("}", "");
                            string[] result = summary.Split("\"");
                            var workflowId = "";
                            foreach (String str in result)
                            {
                                if (str.Length > 5) { workflowId = str; }
                            }

                            if (string.IsNullOrEmpty(workflowId))
                            {
                                throw new Exception("发起流程失败：" + results[1]);
                            }
                            else
                            {
                                if (workflowId.Length > 5)
                                {
                                    string updsql = "update [dbo].[T_CW_REVENUE_Alert_A8] set certsummaryid ='" + workflowId + "',Noticecert = '" + DiffDate + "' where ID ='" + ID.Trim() + "'";
                                    using (SqlCommand commandinsert = new SqlCommand(updsql, connection))
                                    {
                                        commandinsert.ExecuteNonQuery();
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            throw ex;
                        }

                    }

                }
            }
        }


        public void PayslipAlertA8()
        {
            string cdmsConn = "Data Source=**********;User ID=SSIS;PassWord=*********;Initial Catalog=CSCI_DATASOURCE;MultipleActiveResultSets=True;Connection Timeout=400";
            using (SqlConnection concdms = new SqlConnection(cdmsConn))
            {
                DataTable dt = new DataTable("T_CW_REVENUE_Alert_A8");
                string sql = "SELECT a.[PERIOD],[LINENAME],[CORPNAME],a.[PROJCORP],[PROJNAME] ,[CUTOFFDATE],[EXPECTEDATE], " +
                            "a.[ACTUALDATE],a.[SUBMITDATE],[CERTDATE] ,[ACTUALAMOUNT], DATEDIFF(DAY, EXPECTEDATE, GETDATE()) as DiffDate, " +
                            "QS, Sitemanager,Notice,ID FROM [CSCI_DATASOURCE].[dbo].[T_CW_REVENUE_Alert_A8] a " +
                            "right join(select max(ACTUALDATE) as ACTUALDATE, period,[PROJCORP] " +
                            "from [CSCI_DATASOURCE].[dbo].[T_CW_REVENUE_Alert_A8]  group by period,[PROJCORP]) b " +
                            "on isnull(a.ACTUALDATE, '') = isnull(b.ACTUALDATE, '') and a.PROJCORP = b.PROJCORP and a.PERIOD = b.PERIOD " +
                            "where a.SUBMITDATE >= '2022-07-29' and a.ACTUALDATE is null " +
                            "and DATEDIFF(DAY, EXPECTEDATE, GETDATE()) >= -2 and DATEDIFF(DAY, EXPECTEDATE, GETDATE()) <> isnull(Notice, 0) " +
                            "and isnull(Notice,0) < 85 and status<> 3  AND a.PROJCORP NOT IN ('FDN','SBS','BJZ','BLW')" +
                            " and a.PROJCORP NOT IN( SELECT distinct 地盘编号 FROM [CSCI_DATASOURCE].[dbo].[T_CW_PROJECT_STATUS] where 工程狀態 in (N'在建工程 (內部工程,JV,服務合約等)',N'歷年竣工工程 (內部工程,JV,服務合約等)',N'本年竣工工程 (內部工程,JV,服務合約等)')) ";
                // status : 1 完成 ，3终止，0进行中
                using (SqlCommand command = new SqlCommand(sql, concdms))
                {
                    command.Connection.Open();
                    var reader = command.ExecuteReader();
                    dt.Load(reader);
                    if (dt.Rows.Count > 0)
                    {
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            PayslipAlertPushA8(dt.Rows[i]["ID"].ToString().Trim(), dt.Rows[i]["QS"].ToString().Trim(), dt.Rows[i]["LINENAME"].ToString().Trim(), dt.Rows[i]["CORPNAME"].ToString().Trim(), dt.Rows[i]["PROJCORP"].ToString().Trim(), dt.Rows[i]["PROJNAME"].ToString().Trim(), dt.Rows[i]["PERIOD"].ToString().Trim(), dt.Rows[i]["EXPECTEDATE"].ToString().Trim(), dt.Rows[i]["ACTUALAMOUNT"].ToString().Trim(), dt.Rows[i]["Sitemanager"].ToString().Trim(), Convert.ToInt32(dt.Rows[i]["DiffDate"].ToString().Trim()));
                        }
                    }
                }
            }
        }

        public void PayslipAlertPushA8(string ID, string QS, string LINENAME, string CORPNAME, string PROJCORP, string PROJNAME, string PERIOD, string EXPECTEDATE, string ACTUALAMOUNT, string Sitemanager, int DiffDate)
        {
            string cdmsConn = "Data Source=**********;User ID=SSIS;PassWord=*********;Initial Catalog=CSCI_DATASOURCE;MultipleActiveResultSets=True;Connection Timeout=400";
            var accessToken = TokenFetcher.GetUserRestToken("CDMS_FlowSubscriber");
            using (SqlConnection connection = new SqlConnection(cdmsConn))
            {
                string sqlselect = "select * from T_CW_REVENUE_Alert_A8 where ID ='" + ID + "'";
                using (SqlCommand command = new SqlCommand(sqlselect, connection))
                {
                    command.Connection.Open();
                    var reader = command.ExecuteReader();
                    reader.Read();
                    if ((reader.HasRows) && (DiffDate == -2 || DiffDate == 3 || DiffDate == 7 || DiffDate == 14 || DiffDate == 21 || DiffDate == 28 || DiffDate == 35 || DiffDate == 42 || DiffDate == 49 || DiffDate == 56 || DiffDate == 63 || DiffDate == 70 || DiffDate == 77 || DiffDate == 84))
                    {
                        DateTime dt;
                        string stringDate = "", prjcode = "";
                        if (DateTime.TryParse(EXPECTEDATE, out dt))
                        {
                            stringDate = dt.ToString("yyyy-MM-dd");
                        }
                        string subject = "";
                        if (DiffDate == -2)
                        {
                            subject = "!!!未收業主款預警 提醒2天後-" + PROJCORP + "," + PERIOD.Trim() + "期$" + Math.Round(Convert.ToDecimal(ACTUALAMOUNT) / 10000, 0) + "萬";
                        }
                        else
                        {
                            subject = "!!!未收業主款預警 超過" + DiffDate + "天-" + PROJCORP + "," + PERIOD.Trim() + "期$" + Math.Round(Convert.ToDecimal(ACTUALAMOUNT) / 10000, 0) + "萬";
                        }
                        var jsonParam = new
                        {
                            appName = "collaboration",
                            data = new
                            {
                                templateCode = "alertturnover2",
                                draft = "0",
                                //!!! 未收業主款預警 超過10天-MCT,12期$350萬
                                subject = subject,
                                data = new
                                {
                                    formmain_64197 = new
                                    {
                                        年月 = DateTime.Now.ToString("yyyy-MM-dd"),
                                        超期天數 = DiffDate,
                                        QS = QS,
                                        LineName = CORPNAME,

                                    },
                                    formson_64198 = new
                                    {
                                        項目公司 = LINENAME,
                                        項目名稱 = PROJNAME.Trim(),
                                        預計收款日期 = stringDate,
                                        遲付天數 = DiffDate,
                                        地盤經理 = Sitemanager,
                                        期數 = PERIOD,
                                        金额 = ACTUALAMOUNT,
                                        项目编号 = PROJCORP
                                    },

                                },
                            },
                        };
                        var jsonData = JsonConvert.SerializeObject(jsonParam);
                        string results;
                        try
                        {
                            results = HttpClientHelper.FetchResultsViaHttpPostCAP3("https://i.3311csci.com/seeyon/rest/bpm/process/start", jsonData, accessToken, "code", "message");

                            JavaScriptSerializer js = new JavaScriptSerializer();//实例化一个能够序列化数据的类
                            JsonInRoot oput = js.Deserialize<JsonInRoot>(results); //将json数据转化为对象类型并赋值给list
                            string summary = oput.data.app_bussiness_data.ToString().Replace("}", "");
                            string[] result = summary.Split("\"");
                            var workflowId = "";
                            foreach (String str in result)
                            {
                                if (str.Length > 5) { workflowId = str; }
                            }

                            if (string.IsNullOrEmpty(workflowId))
                            {
                                throw new Exception("发起流程失败：" + results[1]);
                            }
                            else
                            {
                                if (workflowId.Length > 5)
                                {
                                    string updsql = "update [dbo].[T_CW_REVENUE_Alert_A8] set summaryid ='" + workflowId + "',notice = '" + DiffDate + "' where ID ='" + ID.Trim() + "'";
                                    using (SqlCommand commandinsert = new SqlCommand(updsql, connection))
                                    {
                                        commandinsert.ExecuteNonQuery();
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            throw ex;
                        }

                    }

                }
            }
        }

        public void Form2A8()
        {
            using (SqlConnection concdms = new SqlConnection(Conn))
            {
                DataTable dt = new DataTable("T_APPLY_2");
                string sql = "select * from [ECO].dbo.T_APPLY_2 where ID not in (select isnull(form2id,0) as form2id from [ECO].dbo.ToA8)";
                using (SqlCommand command = new SqlCommand(sql, concdms))
                {
                    command.Connection.Open();
                    var reader = command.ExecuteReader();
                    dt.Load(reader);
                    if (dt.Rows.Count > 0)
                    {
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            Form2PushA8(dt.Rows[i]["ID"].ToString().Trim());
                        }
                    }
                }
            }
        }

        public void Form2PushA8(string ID)
        {
            var accessToken = TokenFetcher.GetUserRestToken("anna_miao");
            using (SqlConnection connection = new SqlConnection(Conn))
            {
                string sqlselect = "select top 1 a.ID,SUBSTRING(b.Name,1,3) as Name,REPlace(EmployeeName,N'𡢃',N'爛') as EmployeeName,   case when EmployeeSex = 1 then'男' else '女' end as EmployeeSex,  " +
                          "format(EmployeeBirthday, 'yyyy-MM-dd 00:00:00') as EmployeeBirthday, EmployeeJob,  format(HappenedDate, 'yyyy-MM-dd 00:00:00') as HappenedDate,    " +
                          "d.Name as AccidentType, e.name as WorkType,REPLACE(InjureDescription, N'𠝹', N'鎅') as HappenedDescription, CompanyName,  EmployeeIDNumber,f.name as contractors,EmployerName,   " +
                          "format(a.CreateTime, 'yyyy-MM-dd 00:00:00') as ReportDate,  case when ToComp = 1 then '中國海外保險有限公司' else '其他' end as ToComp, C.Name as '地盘名称',   " +
                          "'anna_miao' as 流程发起人登录名 from [ECO].dbo.[T_APPLY_2] a  " +
                          "left join [ECO].dbo.T_DEPARTMENT b on a.SiteName = b.ID  " +
                          "left join [ECO].dbo.MapSiteCode c on c.Code = SUBSTRING(b.Name, 1, 3)  " +
                          "left join [ECO].dbo.[T_ATTRIBUTE] d on d.id = a.AccidentTypeID  " +
                          "left join [ECO].dbo.[T_ATTRIBUTE] e on e.id = a.WorkTypeID  " +
                          "left join [ECO].dbo.[upper_contractors] f on f.id = a.groupid  " +
                          "where EmployeeName<> 'test' and a.ID ='" + ID + "'";
                //"and a.id not in (select 系統編號 from [**********].CSCI_DATASOURCE.dbo.DW_F_ECO_Form2 where [系統編號] is not null) ";
                using (SqlCommand command = new SqlCommand(sqlselect, connection))
                {
                    command.Connection.Open();
                    var reader = command.ExecuteReader();
                    DataTable dt = new DataTable("T_APPLY_2");
                    dt.Load(reader);
                    if (dt.Rows.Count > 0)
                    {
                        var jsonParam = new
                        {
                            appName = "collaboration",
                            data = new
                            {
                                templateCode = "Form2",
                                draft = "0",
                                subject = "Form2工傷呈報記錄 -" + dt.Rows[0]["Name"].ToString().Trim() + " - " + dt.Rows[0]["HappenedDate"].ToString().Trim() + "- " + dt.Rows[0]["EmployeeName"].ToString().Trim(),
                                data = new
                                {
                                    formmain_56480 = new
                                    {
                                        所在地盤文本 = dt.Rows[0]["地盘名称"].ToString().Trim(),
                                        受傷類型 = dt.Rows[0]["AccidentType"].ToString().Trim(),
                                        傷者姓名 = dt.Rows[0]["EmployeeName"].ToString().Trim(),
                                        受傷描述 = dt.Rows[0]["HappenedDescription"].ToString().Trim(),
                                        意外日期 = dt.Rows[0]["HappenedDate"].ToString().Trim(),
                                        申報日期 = dt.Rows[0]["ReportDate"].ToString().Trim(),
                                        地盤編碼 = dt.Rows[0]["Name"].ToString().Trim(),
                                        性別 = dt.Rows[0]["EmployeeSex"].ToString().Trim(),
                                        系統編號 = dt.Rows[0]["ID"].ToString().Trim(),
                                        工种 = dt.Rows[0]["EmployeeJob"].ToString().Trim(),
                                        理賠公司 = dt.Rows[0]["ToComp"].ToString().Trim(),
                                        出生日期 = dt.Rows[0]["EmployeeBirthday"].ToString().Trim(),
                                        身份證號 = dt.Rows[0]["EmployeeIDNumber"].ToString().Trim(),
                                        總承判商 = dt.Rows[0]["CompanyName"].ToString().Trim(),
                                        僱用公司名稱 = dt.Rows[0]["EmployerName"].ToString().Trim(),
                                        分判列表 = dt.Rows[0]["contractors"].ToString().Trim(),

                                    },
                                },
                            },
                        };
                        var jsonData = JsonConvert.SerializeObject(jsonParam);
                        string results;
                        try
                        {
                            results = HttpClientHelper.FetchResultsViaHttpPostCAP3("https://i.3311csci.com/seeyon/rest/bpm/process/start", jsonData, accessToken, "code", "message");

                            JavaScriptSerializer js = new JavaScriptSerializer();//实例化一个能够序列化数据的类
                            JsonInRoot oput = js.Deserialize<JsonInRoot>(results); //将json数据转化为对象类型并赋值给list
                            string summary = oput.data.app_bussiness_data.ToString().Replace("}", "");
                            string[] result = summary.Split("\"");
                            var workflowId = "";
                            foreach (String str in result)
                            {
                                if (str.Length > 5) { workflowId = str; }
                            }

                            if (string.IsNullOrEmpty(workflowId))
                            {
                                throw new Exception("发起流程失败：" + results[1]);
                            }
                            else
                            {
                                if (workflowId.Length > 5)
                                {
                                    string insertsql = "INSERT INTO [ECO].dbo.[ToA8]([ID],[UploadDate],[Form2Id]) VALUES ('" + workflowId + "',  '" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "','" + ID + "')";
                                    using (SqlCommand commandinsert = new SqlCommand(insertsql, connection))
                                    {
                                        commandinsert.ExecuteNonQuery();
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            throw ex;
                        }

                    }

                }
            }
        }

        public void CDMSExpenseCheck()
        {
            string cdmsConn = "Data Source=cob-server-005;User ID=cdms_reader;PassWord=********;Initial Catalog=FinancialPlatform;MultipleActiveResultSets=True;Connection Timeout=400";
            using (SqlConnection concdms = new SqlConnection(cdmsConn))
            {
                DataTable dt = new DataTable("fpfExpense");
                string sql = "select a.BillNo,a.CustomerName,a.TotalPrice,a.IsDeleted,BillStatus, " +
                                "'發票號      發票日期            港幣            摘要' + CHAR(10) + CHAR(13) + b.InvoiceNo + '    ' + convert(varchar, b.InvoiceDate, 23) + '  ' + CAST(b.Price AS NVARCHAR(20)) + " +
                                "'    ' + b.Digest + CHAR(10) + CHAR(13) + CHAR(10) + CHAR(13) + '備注  ' + a.Remark as Remark,a.CreationTime,a.CreatorId,REPLACE(REPLACE(c.UNITNAME,'CIS-',''),'CIL-','') AS UNITNAME " +
                                "from [dbo].[fpfExpense] a " +
                                "left join fpfExpenseDetail b on a.Id = b.BillId " +
                                "left join NcCorp c on a.PK_CORP = c.PK_CORP " +
                                "where (BillNo like '%CIL%' or BillNo like '%CIS%') and A.IsDeleted = 0 and a.BillStatus <> 10 and a.CreationTime > '2021-03-08' and a.CreatorId in ('CSCI\\carol_chan','CSCI\\april_lim','CSCI\\catt_lam','CSCI\\VANESSA_LAM') ";
                // BillStatus ：0 草稿;1 提交;10审核 // Appendix :撤销次数
                using (SqlCommand command = new SqlCommand(sql, concdms))
                {
                    command.Connection.Open();
                    var reader = command.ExecuteReader();
                    dt.Load(reader);
                    if (dt.Rows.Count > 0)
                    {
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            ExpensePushA8(dt.Rows[i]["UNITNAME"].ToString().Trim(), dt.Rows[i]["BillNo"].ToString().Trim(), dt.Rows[i]["CustomerName"].ToString().Trim(), dt.Rows[i]["Remark"].ToString().Trim(), dt.Rows[i]["TotalPrice"].ToString().Trim(), dt.Rows[i]["CreationTime"].ToString().Trim(), dt.Rows[i]["CreatorId"].ToString().Trim(), dt.Rows[i]["IsDeleted"].ToString().Trim(), dt.Rows[i]["BillStatus"].ToString().Trim());
                        }
                    }
                }
            }
        }

        public void ExpensePushA8(string UNITNAME, string BillNo, string CustomerName, string Remark, string TotalPrice, string CreationTime, string CreateId, string IsDeleted, string BillStatus)
        {
            var accessToken = TokenFetcher.GetUserRestToken(CreateId.Replace("CSCI\\", "")); //CreateId.Replace("CSCI\\", "")
            using (SqlConnection connection = new SqlConnection(Conn))
            {
                string sqlselect = "select * from CAP4FormApprove where ID = (select max(ID) as ID from CAP4FormApprove where Finvno = '" + BillNo.Trim() + "')";
                using (SqlCommand command = new SqlCommand(sqlselect, connection))
                {
                    command.Connection.Open();
                    var reader = command.ExecuteReader();
                    reader.Read();
                    if ((!reader.HasRows && FormReference.UserName(CreateId) != "") || (reader.HasRows && BillStatus == "1" && reader["BillStatus"].ToString().Trim() != BillStatus))
                    {
                        var jsonParam = new
                        {
                            appName = "collaboration",
                            data = new
                            {
                                templateCode = "adminvoucher",
                                draft = "1",
                                subject = "行政付款憑單" + " / " + BillNo.Trim(),
                                data = new
                                {
                                    formmain_58093 = new
                                    {
                                        編號 = BillNo.Trim(),
                                        收款單位 = CustomerName,
                                        摘要 = Remark,
                                        港幣 = TotalPrice,
                                        憑單日期 = CreationTime,
                                        文所屬部門 = UNITNAME,
                                        經辦人 = FormReference.UserName(CreateId)
                                    },
                                },
                            },
                        };
                        var jsonData = JsonConvert.SerializeObject(jsonParam);
                        string[] results;
                        try
                        {
                            results = HttpClientHelper.FetchResultsViaHttpPostCAP4("https://i.3311csci.com/seeyon/rest/bpm/process/start", jsonData, accessToken, "code", "message", "summaryId");
                        }
                        catch (Exception ex)
                        {
                            throw ex;
                        }
                        var workflowId = results[0];

                        if (string.IsNullOrEmpty(workflowId))
                        {
                            throw new Exception("发起流程失败：" + results[1]);
                        }

                        if (workflowId == "0")
                        {
                            string insertsql = "INSERT INTO [dbo].[CAP4FormApprove]([SummaryId],[PolicyNo],[ClaimNo],[Finvno],[InputDate],[BillStatus],[IsDeleted]) VALUES ('" + results[2] + "', '" + UNITNAME + "', 'Expense', '" + BillNo.Trim() + "', '" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "','" + BillStatus + "','" + IsDeleted + "')";
                            using (SqlCommand commandinsert = new SqlCommand(insertsql, connection))
                            {
                                commandinsert.ExecuteNonQuery();
                            }
                        }
                    }
                    else
                    {
                        string insertsql = "update [dbo].[CAP4FormApprove] set BillStatus ='" + BillStatus + "' where finvno ='" + BillNo.Trim() + "' ";
                        using (SqlCommand commandinsert = new SqlCommand(insertsql, connection))
                        {
                            commandinsert.ExecuteNonQuery();
                        }
                    }
                }
            }
        }

        public void CDMSReceiptCheck()
        {
            string cdmsConn = "Data Source=cob-server-005;User ID=cdms_reader;PassWord=********;Initial Catalog=FinancialPlatform;MultipleActiveResultSets=True;Connection Timeout=400";
            using (SqlConnection concdms = new SqlConnection(cdmsConn))
            {
                DataTable dt = new DataTable("fpfReceipt");
                string sql = "select a.BillNo,a.CustomerName,a.TotalPrice,a.IsDeleted,BillStatus, " +
                                "'發票號      發票日期            港幣            摘要' + CHAR(10) + CHAR(13) + isnull(b.InvoiceNo,'') + '    ' + case when b.InvoiceDate is null then '' else " +
                                "convert(varchar, b.InvoiceDate, 23) + '  ' + CAST(isnull(b.Price, 0) AS NVARCHAR(20)) end + '  ' + CAST(b.Price AS NVARCHAR(20)) + " +
                                "'    ' + isnull(b.Digest,'') + CHAR(10) + CHAR(13) + CHAR(10) + CHAR(13) + '備注  ' + a.Remark as Remark,a.CreationTime,a.CreatorId,REPLACE(REPLACE(c.UNITNAME,'CIS-',''),'CIL-','') AS UNITNAME " +
                                "from[dbo].[fpfReceipt] a " +
                                "left join fpfReceiptDetail b on a.Id = b.BillId " +
                                "left join NcCorp c on a.PK_CORP = c.PK_CORP " +
                                "where (BillNo like '%CIL%' or BillNo like '%CIS%') and A.IsDeleted = 0 and a.BillStatus <> 10 and a.CreationTime > '2019-03-20' and a.CreatorId in ('CSCI\\carol_chan','CSCI\\april_lim') ";
                using (SqlCommand command = new SqlCommand(sql, concdms))
                {
                    command.Connection.Open();
                    var reader = command.ExecuteReader();
                    dt.Load(reader);
                    if (dt.Rows.Count > 0)
                    {
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            ReceiptPushA8(dt.Rows[i]["UNITNAME"].ToString().Trim(), dt.Rows[i]["BillNo"].ToString().Trim(), dt.Rows[i]["CustomerName"].ToString().Trim(), dt.Rows[i]["Remark"].ToString().Trim(), dt.Rows[i]["TotalPrice"].ToString().Trim(), dt.Rows[i]["CreationTime"].ToString().Trim(), dt.Rows[i]["CreatorId"].ToString().Trim(), dt.Rows[i]["IsDeleted"].ToString().Trim(), dt.Rows[i]["BillStatus"].ToString().Trim());
                        }
                    }
                }
            }
        }

        public void ReceiptPushA8(string UNITNAME, string BillNo, string CustomerName, string Remark, string TotalPrice, string CreationTime, string CreateId, string IsDeleted, string BillStatus)
        {
            var accessToken = TokenFetcher.GetUserRestToken(CreateId.Replace("CSCI\\", "")); //CreateId.Replace("CSCI\\", "")
            using (SqlConnection connection = new SqlConnection(Conn))
            {
                string sqlselect = "select * from CAP4FormApprove where ID = (select max(ID) as ID from CAP4FormApprove where Finvno = '" + BillNo.Trim() + "')";
                using (SqlCommand command = new SqlCommand(sqlselect, connection))
                {
                    command.Connection.Open();
                    var reader = command.ExecuteReader();
                    reader.Read();
                    if ((!reader.HasRows && FormReference.UserName(CreateId) != "") || (reader.HasRows && BillStatus == "1" && reader["BillStatus"].ToString().Trim() != BillStatus))
                    {
                        var jsonParam = new
                        {
                            appName = "collaboration",
                            data = new
                            {
                                templateCode = "adminreceive",
                                draft = "1",
                                subject = "行政收款憑單" + " / " + BillNo.Trim(),
                                data = new
                                {
                                    formmain_58388 = new
                                    {
                                        編號 = BillNo.Trim(),
                                        付款單位 = CustomerName,
                                        摘要 = Remark,
                                        港幣 = TotalPrice,
                                        憑單日期 = CreationTime,
                                        文所屬部門 = UNITNAME,
                                        經辦人 = FormReference.UserName(CreateId)
                                    },
                                },
                            },
                        };
                        var jsonData = JsonConvert.SerializeObject(jsonParam);
                        string[] results;
                        try
                        {
                            results = HttpClientHelper.FetchResultsViaHttpPostCAP4("https://i.3311csci.com/seeyon/rest/bpm/process/start", jsonData, accessToken, "code", "message");
                        }
                        catch (Exception ex)
                        {
                            throw ex;
                        }
                        var workflowId = results[0];
                        if (string.IsNullOrEmpty(workflowId))
                        {
                            throw new Exception("发起流程失败：" + results[1]);
                        }

                        if (workflowId == "0")
                        {
                            string insertsql = "INSERT INTO [dbo].[CAP4FormApprove]([SummaryId],[PolicyNo],[ClaimNo],[Finvno],[InputDate],[BillStatus],[IsDeleted]) VALUES ('" + results[1] + "', '" + UNITNAME + "', 'Expense', '" + BillNo.Trim() + "', '" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "','" + BillStatus + "','" + IsDeleted + "')";
                            using (SqlCommand commandinsert = new SqlCommand(insertsql, connection))
                            {
                                commandinsert.ExecuteNonQuery();
                            }
                        }
                    }
                    else
                    {
                        string insertsql = "update [dbo].[CAP4FormApprove] set BillStatus ='" + BillStatus + "' where finvno ='" + BillNo.Trim() + "' ";
                        using (SqlCommand commandinsert = new SqlCommand(insertsql, connection))
                        {
                            commandinsert.ExecuteNonQuery();
                        }
                    }
                }
            }
        }

        public void CheckBusinessReport()
        {
            PDFImageTest Signed = new PDFImageTest();
            using (SqlConnection connection = new SqlConnection(Conn))
            {
                DataTable dt = new DataTable("reportrecord");
                //string sql = "select * from reportrecord where len(flowid)> 10 and flowid LIKE '%Finished%' and flowid NOT LIKE '%Send%'";
                string sql = "select convert(varchar,reportdate, 112) as reportdate, filename from reportrecord where len(flowid)> 10 and status ='1'";
                SqlCommand command = new SqlCommand(sql, connection);
                command.Connection.Open();
                var reader = command.ExecuteReader();
                dt.Load(reader);
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    reportdate = "20230727";
                    folderName = @"\\*********\保險公司\COIL_DATA\COIL_eReport\Business\" + reportdate + @"\";

                    string pathString = System.IO.Path.Combine(folderName, dt.Rows[i]["filename"].ToString().Trim() + @"\");
                    if (!System.IO.Directory.Exists(pathString))
                    {
                        System.IO.Directory.CreateDirectory(pathString);
                    }
                    Signed.SignBusinessReport(dt.Rows[i]["filename"].ToString().Trim() + ".pdf", reportdate);
                    if (System.IO.Directory.GetFiles(pathString).Length == 0)
                    {
                        Directory.Delete(pathString);
                    }
                }
                string sql1 = "update reportrecord set status ='3' where  status ='1' AND len(flowid)> 10";
                SqlCommand command1 = new SqlCommand(sql1, connection);
                command1.ExecuteNonQuery();
            }

        }

        public void CheckClaimsReport()
        {
            PDFImageTest Signed = new PDFImageTest();
            using (SqlConnection connection = new SqlConnection(Conn))
            {
                DataTable dt = new DataTable("reportrecord");
                //string sql = "select * from reportrecord where len(flowid)> 10 and flowid LIKE '%Finished%' and flowid NOT LIKE '%Send%'";
                string sql = "select convert(varchar,reportdate, 112) as reportdate, filename from reportrecord where len(flowid)> 10 and status ='2' ";
                SqlCommand command = new SqlCommand(sql, connection);
                command.Connection.Open();
                var reader = command.ExecuteReader();
                dt.Load(reader);
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    reportdate = "20230727";
                    folderName = @"\\*********\保險公司\COIL_DATA\COIL_eReport\Claims\" + reportdate + @"\";

                    string pathString = System.IO.Path.Combine(folderName, dt.Rows[i]["filename"].ToString().Trim() + @"\");
                    if (!System.IO.Directory.Exists(pathString))
                    {
                        System.IO.Directory.CreateDirectory(pathString);
                    }
                    Signed.SignClaimsReport(dt.Rows[i]["filename"].ToString().Trim() + ".pdf", reportdate);
                    if (System.IO.Directory.GetFiles(pathString).Length == 0)
                    {
                        Directory.Delete(pathString);
                    }
                }
                string sql1 = "update reportrecord set status ='4' where  status ='2' AND len(flowid)> 10";
                SqlCommand command1 = new SqlCommand(sql1, connection);
                command1.ExecuteNonQuery();
            }
        }

        public void ClosingClaims()
        {

            DataTable dt = new DataTable("oclaim");
            dt = closeDT();
            string fclmno = "", fclass = "", fctlid_1 = "", ocabk_efctlid = "", ocadj_fctlid = "", oclaim_afctlid = "";
            ArrayList Addsql = new ArrayList();

            for (int i = 0; i < dt.Rows.Count; i++)
            {
                Addsql.Clear();
                fclmno = dt.Rows[i]["fclmno"].ToString().Trim();
                fclass = dt.Rows[i]["fclass"].ToString().Trim();
                fctlid_1 = dt.Rows[i]["fctlid"].ToString().Trim();
                ocabk_efctlid = NewId("ocabk_e");
                ocadj_fctlid = NewId("ocadj");
                oclaim_afctlid = NewId("oclaim_a");
                if (fclass == "EEC")
                {
                    string sql2 = "update oclaim set fmode=4, fcnfuser = 'Admin' , " +
                                  "fcnfdate = '" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "'" +
                                  "where fclmno = '" + fclmno + "'";
                    Addsql.Add(sql2);
                    string sqlclaim = "update oclaim set fosamt =0, fincamt = 0, " +
                        "fosretn = 0, fincretn = 0,fostty = 0, finctty = 0, " +
                        "fosfacb = 0, fincfacb = 0,fosfac = 0, fincfac = 0, " +
                        "fosfacnp = 0, fincfacnp = 0,fosxol = 0, fincxol = 0 " +
                        "where fctlid= (select fctlid from oclaim where fclmno ='" + fclmno + "') ";
                    Addsql.Add(sqlclaim);
                    string sqlclaims = "update oclaim_s set fosamt = 0, fincamt = 0, " +
                        "fosretn = 0, fincretn = 0,fostty = 0, finctty = 0, " +
                        "fosfacb = 0, fincfacb = 0,fosfac = 0, fincfac = 0, " +
                        "fosfacnp =0, fincfacnp =0,fosxol = 0, fincxol = 0 " +
                        "where fctlid_1= (select fctlid from oclaim where fclmno ='" + fclmno + "') ";
                    Addsql.Add(sqlclaims);

                    string sql3 = "update ocabk_oe set fadj01 = 0, fadj02 =0, " +
                                "fadj03 = 0, fadj04 = 0,fadj05 = 0, fadj06 = 0,  " +
                                "fadj07 = 0, fadj08 = 0,fadj09 = 0, fadj10 = 0,  " +
                                "fadjA = 0, fadjB = 0,fadjC = 0, fadj = 0,  " +
                                "fos01 = 0, fos02 = 0,fos03 = 0, fos04 = 0,fos05 = 0,  " +
                                "fos06 = 0 ,fos07 = 0, fos08 = 0 ,fos09 = 0, fos10 = 0,  " +
                                "fosA = 0, fosB = 0,fosC = 0, fos = 0  " +
                                "where fctlid_c = (select fctlid from oclaim where fclmno = '" + fclmno + "') ";
                    Addsql.Add(sql3);
                    string sql1 = "insert into oclaim_a " +
                   "select fctlid_1,'" + oclaim_afctlid + "','A0' + convert(char(5),(flogseq)),flogseq + 1,GETDATE(),frepdate,flosdate,ffrm2date, " +
                   "fuwyr,fsec,fcover,frno,fprdr,fclnt,fsrcclm,fcedeclm,fitem,flosloc, " +
                   "fpartr_t1,fpartr_t2,fnatlos,fadjr1,fadjr2,fsolr1,fsolr2,frcrdate,frcrto, " +
                   "fhlhc,fstatus,4,fdestroy,fdesdate,fctldel,'Admin',GETDATE(),'Admin',GETDATE(),'Admin',GETDATE() " +
                   "from oclaim_a " +
                   "where fctlid_1 = '" + fctlid_1 + "' and fctlid = (select max(fctlid) from oclaim_a " +
                   "where fctlid_1 = '" + fctlid_1 + "')";
                    Addsql.Add(sql1);
                    string sql4 = "INSERT INTO [dbo].[oclaim_log]([fctlid_1],[fclass],[fclmno],[flogseq],[finpdate], " +
                                "[fbringdate],[ftoken],[fcycle],[fnote],[fprogress],[fstatus],[finpuser],[fupduser], " +
                                "[fupddate],[fcnfuser],[fcnfdate]) VALUES(" +
                                "'" + fctlid_1 + "', '" + fclass + "', '" + fclmno + "', '2', " +
                                "'" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "', " +
                                "'" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "', " +
                                "'Close claims', 'Claim unsettled/declined and Closed', '', " +
                                "'', '3', 'Admin', 'Admin', '" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "', " +
                                "'Admin', '" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "') ";
                    Addsql.Add(sql4);
                    string sql5 = "insert into ocadj(fclass,fsclass,fclmno,fpolno,fendtno,fctlid_p,fctlid_e,fctlid_1,fctlid,ftrntype,fnature,flogseq,faccdate,fecamt,fclawamt,famount,fretn,ftty,ffac,ffacb,ffacnp,fxol,fttycode,fttysec,ffacbcode,ffacbsec,ffacnplyr,flyrid,fxolcode,fxollayr,fstatus,finpuser,finpdate,fupduser,fupddate,fcnfuser,fcnfdate) " +
                                "select top 1 fclass, fsclass, fclmno, fpolno, fendtno, fctlid_p, fctlid_e, fctlid_1, " +
                                "'" + ocadj_fctlid + "' as fctlid, 3 as ftrntype,fnature,2 as flogseq,GETDATE(), " +
                                "- fecamt,fclawamt,-famount,-fretn,ftty,ffac,ffacb,ffacnp,fxol,fttycode,fttysec,ffacbcode, " +
                                "ffacbsec,ffacnplyr,flyrid,fxolcode,fxollayr,fstatus,'Admin',GETDATE(),'Admin',GETDATE(),'Admin',GETDATE() " +
                                "from ocadj where fctlid_1 = (select fctlid from oclaim where fclmno = '" + fclmno + "') ";
                    Addsql.Add(sql5);
                    string sql6 = "insert into ocabk_e " +
                                "select top 1 '" + ocabk_efctlid + "' as fctlid,fctlid_c, '" + ocadj_fctlid + "' as fctlid_a,3 as ftrntype,2 as flogseq,fdutype,fttlday, " +
                                "fnilpay1,fnilpay2,ffrm01,fto01,fnofday01,ffrm02,fto02,fnofday02,ffrm03,fto03,fnofday03, " +
                                "ffrm04,fto04,fnofday04,ffrm05,fto05,fnofday05,ffrm06,fto06,fnofday06,ffrm07,fto07,fnofday07, " +
                                "ffrm08,fto08,fnofday08,ffrm09,fto09,fnofday09,ffrm10,fto10,fnofday10,ffrm11,fto11,fnofday11, " +
                                "ffrm12,fto12,fnofday12,ffrm13,fto13,fnofday13,ffrm14,fto14,fnofday14,ffrm15,fto15,fnofday15, " +
                                "fwgtype,ffactor,fwage,fnlength,famt01,fpay01,100000,0,-100000,fincpamt,fnlen2,fincprate, " +
                                "famt02,fpay02,fos02,frvos02,fadj02,fos03,frvos03,fadj03,fos04,frvos04,fadj04, " +
                                "fos05,frvos05,fadj05,fos06,frvos06,fadj06,fos07,frvos07,fadj07,fos08,frvos08,fadj08, " +
                                "fos09,frvos09,fadj09,fos10,frvos10,fadj10,100000,0,-100000,fosB,frvosB,fadjB, " +
                                "fosC,frvosC,fadjC,100000,0,-100000,fremark_t1 " +
                                "from ocabk_e " +
                                "where fctlid_c = (select fctlid from oclaim where fclmno = '" + fclmno + "') ";
                    Addsql.Add(sql6);
                    string sqlfctlid2 = "update live_ilmdata.dbo.xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='OCABK_E'";
                    Addsql.Add(sqlfctlid2);
                    string sqlfctlid = "update live_ilmdata.dbo.xsysparm set fnxtid= RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='ocadj'";
                    Addsql.Add(sqlfctlid);
                    string sqlfctlid1 = "update live_ilmdata.dbo.xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='oclaim_a'";
                    Addsql.Add(sqlfctlid1);

                }

                using (SqlConnection connection = new SqlConnection(Conn))
                {
                    connection.Open();

                    SqlCommand command = connection.CreateCommand();
                    SqlTransaction transaction;

                    // Start a local transaction.
                    transaction = connection.BeginTransaction("SampleTransaction");

                    // Must assign both transaction object and connection 
                    // to Command object for a pending local transaction
                    command.Connection = connection;
                    command.Transaction = transaction;

                    try
                    {
                        string[] sql2 = (string[])Addsql.ToArray(typeof(string));
                        if (sql2 != null)
                        {
                            for (int k = 0; k < sql2.Length; k++)
                            {
                                command.CommandText = sql2[k];
                                command.ExecuteNonQuery();
                            }
                        }
                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                        Console.WriteLine("  Message: {0}", ex.Message);

                        // Attempt to roll back the transaction. 
                        try
                        {
                            transaction.Rollback();
                        }
                        catch (Exception ex2)
                        {
                            // This catch block will handle any errors that may have occurred 
                            // on the server that would cause the rollback to fail, such as 
                            // a closed connection.
                            Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                            Console.WriteLine("  Message: {0}", ex2.Message);
                        }
                    }
                    connection.Close();
                }

            }

        }

        public void Updateflogseq()
        {
            using (SqlConnection connection = new SqlConnection(Conn))
            {
                connection.Open();
                string sql1 = "UPDATE t1 SET t1.flogseq = t2.flogseq " +
                    "FROM(select * from oclaim_log where " +
                    "fcnfdate > CONVERT(varchar(100), GETDATE(), 23) and fcnfuser = 'Admin') t1 " +
                    "INNER JOIN(select count(*) as flogseq, fclmno from oclaim_log where " +
                    "fclmno in (select fclmno from oclaim_log " +
                    "where fcnfdate > CONVERT(varchar(100), GETDATE(), 23) and fcnfuser = 'Admin') " +
                    "group by fclmno) t2 ON t1.fclmno = t2.fclmno";
                SqlCommand command1 = new SqlCommand(sql1, connection);
                command1.ExecuteNonQuery();
                connection.Close();
            }
        }

        public void closedReport()
        {
            using (SqlConnection connection = new SqlConnection(Conn))
            {
                string sql1 = "select CONVERT(varchar(100),a.finpdate, 23) as regdate, " +
                        "a.fclmno,a.fpolno,e.fdesc,CONVERT(varchar(100), a.frepdate, 23) as frepdate, " +
                        "CONVERT(varchar(100), a.flosdate, 23) as flosdate, " +
                        "finsd,flosloc + '/' + fpartr_t1 as flosloc, " +
                        "- c.famount as lastrev,c.famount as revisedrev, 0 as adj, " +
                        "d.fpayamt,d.fosamt,d.fincamt,case when a.fmode = 4 then 'Closed' else 'Open' end as status " +
                        "from oclaim a " +
                        "left join oclaim_log b on a.fclmno = b.fclmno  " +
                        "left join (select fid,fdesc from live_ilmdata.dbo.mprdr where ftype = 'P') e on a.fprdr = e.fid  " +
                        "left join(select* from ocadj where fctlid  " +
                        "in (select max(fctlid) from ocadj group by fclmno)) c on a.fclmno = c.fclmno  " +
                        "left join oclaim_s d on a.fctlid = d.fctlid_1 " +
                        "where a.fcnfuser = 'Admin' ";
                SqlCommand command1 = new SqlCommand(sql1, connection);
                command1.ExecuteNonQuery();
            }
        }

        public DataTable closeDT()
        {
            DataTable dt = new DataTable("oclaim");
            using (SqlConnection connection = new SqlConnection(Conn))
            {
                DateTime dateTime = DateTime.Now.AddMonths(-7);
                if (dateTime.Year != DateTime.Now.Year)
                {
                    dateTime = DateTime.Now.AddMonths(-5);
                }

                string sql = $@"select distinct a.* from oclaim a " +
            "left join ocpay b on a.fctlid = b.fctlid_1 " +
            "left join ocadj c on a.fctlid = c.fctlid_1 " +
            "left join (select * from oclaim_log where findex in " +
            "(select max(findex) as findex from oclaim_log group by fclmno)) d on a.fctlid = d.fctlid_1 " +
            "where b.fclmno is null and a.fclass ='EEC' and fosamt = '100000'  and fmode <> 4 and c.famount = '100000'  " +
            $"and d.fcycle = 'Notification only' and ffrm2date is null and MONTH(a.flosdate)= {dateTime.Month} and year(a.flosdate)={dateTime.Year}";
                //"and ABS(convert(int, MONTH(GETDATE())) - convert(int, MONTH(a.flosdate))) = 5 "; //跨年5个月，不跨年7个月

                SqlCommand command = new SqlCommand(sql, connection);
                command.Connection.Open();
                var reader = command.ExecuteReader();
                dt.Load(reader);
            }
            return dt;
        }
        public string NewId(string fidtype)
        {
            string fctlid = "";
            using (SqlConnection connection = new SqlConnection(Conn))
            {
                DataTable dt = new DataTable("oclaim");
                string str = "select * from live_ilmdata.dbo.xsysparm where fidtype ='" + fidtype + "'";
                SqlCommand command = new SqlCommand(str, connection);
                command.Connection.Open();
                var reader = command.ExecuteReader();
                dt.Load(reader);
                fctlid = dt.Rows[0]["fnxtid"].ToString();

            }
            return fctlid;
        }
        public void CheckCoverNote()
        {
            if (Scan() == "更新记录0条")
            {
                PDFImageTest Signed = new PDFImageTest();
                using (SqlConnection connection = new SqlConnection(Conn))
                {
                    DataTable dt = new DataTable("cover_note");
                    //string sql = "select * from reportrecord where len(flowid)> 10 and flowid LIKE '%Finished%' and flowid NOT LIKE '%Send%'";
                    string sql = "select convert(varchar,BeginDate, 112) as BeginDate, CoverNo from cover_note where len(flowid)> 10 and fconfirm ='4'";
                    SqlCommand command = new SqlCommand(sql, connection);
                    command.Connection.Open();
                    var reader = command.ExecuteReader();
                    dt.Load(reader);
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        folderName = @"\\*********\保險公司\COIL_DATA\COIL_eCoverNote\";

                        string pathString = System.IO.Path.Combine(folderName, dt.Rows[i]["CoverNo"].ToString().Trim() + @"\");
                        if (!System.IO.Directory.Exists(pathString))
                        {
                            System.IO.Directory.CreateDirectory(pathString);
                        }
                        Signed.SignCoverNote(dt.Rows[i]["CoverNo"].ToString().Trim() + ".pdf");
                        if (System.IO.Directory.GetFiles(pathString).Length == 0)
                        {
                            Directory.Delete(pathString);
                        }
                    }
                    string sql1 = "update cover_note set fconfirm ='5' where fconfirm ='4'";
                    SqlCommand command1 = new SqlCommand(sql1, connection);
                    command1.ExecuteNonQuery();
                }
            }
        }

        public void wkhtmltopdfhtmlTopdf()
        {
            var accessToken = FormReference.GetUserRestToken("anna_miao");
            HttpClient client = new HttpClient();
            HttpResponseMessage responseID = client.GetAsync("https://i.3311csci.com/seeyon/rest/flow/FromFinish/Indemnity/2021-02-14/2021-04-21/?token=" + accessToken + "").Result;
            var resultID = responseID.Content.ReadAsStringAsync().Result;
            JArray flowIDs = JArray.Parse(resultID);
            if (flowIDs != null)
            {
                foreach (string flowID in flowIDs)
                {
                    SqlConnection connection = new SqlConnection(Conn);
                    string sqlselect = "select * from CAP4FormApprove where SummaryId = '" + flowID + "'";
                    using (SqlCommand command = new SqlCommand(sqlselect, connection))
                    {
                        command.Connection.Open();
                        var reader = command.ExecuteReader();
                        if (reader.HasRows)
                        {
                            HttpResponseMessage responseData = client.GetAsync("https://i.3311csci.com/seeyon/rest/form/getformdata/" + flowID.Trim() + "?token=" + accessToken + "").Result;
                            var resultData = responseData.Content.ReadAsStringAsync().Result;
                            var s = Newtonsoft.Json.JsonConvert.DeserializeObject(resultData);
                            JavaScriptSerializer js = new JavaScriptSerializer();//实例化一个能够序列化数据的类
                            Application oput = js.Deserialize<Application>(s.ToString()); //将json数据转化为对象类型并赋值给list
                            string ClaimNo = oput.RowData.field0001.ToString();
                            string fileVersion = oput.RowData.field0031.ToString();
                            string PolicyNo = oput.RowData.field0002.ToString();

                            var targetUrl = $"collaboration/collaboration.do?method=summary&summaryId=" + flowID.ToString().Trim() + "&operationId=" + FormReference.ReferenceID("Indemnity") + "&formId=" + FormReference.FormID("Indemnity") + "";
                            var ssoUrl = "https://i.3311csci.com/seeyon/login/sso?from=cdms4&ticket=lsm1&tourl=" + HttpUtility.UrlEncode(targetUrl);
                            // 把 HTML 文本内容转换为 PDF
                            //string htmlStr = WebHandler.GetContent(WebHandler.GetCookie("", "https://i.3311csci.com/seeyon/login/sso?from=cdms4&ticket=lsm1"), ssoUrl);
                            //url.HtmlTextConvertToPdf(htmlStr, @"\\*********\保險公司\COIL_DATA\COIL_eClaim\001.pdf");

                            /// 把 HTML 文件转换为 PDF
                            bool success = url.HtmlConvertToPdf(ssoUrl, @"\\*********\保險公司\COIL_DATA\COIL_eClaim\" + ClaimNo + "_赔款审批表" + fileVersion + ".pdf");
                            if (success)
                            {
                                string insertsql = "INSERT INTO [dbo].[CAP4FormApprove]([SummaryId],[PolicyNo],[ClaimNo],[Finvno],[InputDate]) VALUES ('" + flowID + "', '" + PolicyNo + "', '" + ClaimNo + "', '', '" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "')";
                                using (SqlCommand commandinsert = new SqlCommand(insertsql, connection))
                                {
                                    commandinsert.ExecuteNonQuery();
                                }
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// _赔款审批表下载pdf及附件
        /// </summary>
        [Obsolete]
        public void SeleniumIndemnityPdf()
        {
            var accessToken = FormReference.GetUserRestToken("anna_miao");
            HttpClient client = new HttpClient();
            HttpResponseMessage responseID = client.GetAsync("https://i.3311csci.com/seeyon/rest/flow/FromFinish/Indemnity/2023-07-20/" + DateTime.Now.AddDays(1).ToString("yyyy-MM-dd") + "/?token=" + accessToken + "").Result;
            var resultID = responseID.Content.ReadAsStringAsync().Result;
            JArray flowIDs = JArray.Parse(resultID);
            if (flowIDs != null)
            {
                foreach (string flowID in flowIDs)
                {
                    string sqlselect = "select * from CAP4FormApprove where SummaryId = '" + flowID + "'";
                    using (SqlConnection connection = new SqlConnection(Conn))
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(sqlselect, connection))
                        {
                            command.CommandTimeout = 1;
                            try
                            {
                                var reader = command.ExecuteReader();
                                if (!reader.HasRows)
                                {
                                    HttpResponseMessage responseData = client.GetAsync("https://i.3311csci.com/seeyon/rest/form/getformdata/" + flowID.Trim() + "?token=" + accessToken + "").Result;
                                    var resultData = responseData.Content.ReadAsStringAsync().Result;
                                    var s = Newtonsoft.Json.JsonConvert.DeserializeObject(resultData);
                                    JavaScriptSerializer js = new JavaScriptSerializer();//实例化一个能够序列化数据的类
                                    Application oput = js.Deserialize<Application>(s.ToString()); //将json数据转化为对象类型并赋值给list
                                    string ClaimNo = oput.RowData.field0001.ToString();
                                    string fileVersion = oput.RowData.field0031.ToString();
                                    string PolicyNo = oput.RowData.field0002.ToString();

                                    HttpResponseMessage responseAttachData = client.GetAsync("https://i.3311csci.com/seeyon/rest/coll/attachments/" + flowID.Trim() + "/0?token=" + accessToken + "").Result;
                                    var resultAttachData = responseAttachData.Content.ReadAsStringAsync().Result;
                                    var sAttach = Newtonsoft.Json.JsonConvert.DeserializeObject(resultAttachData);
                                    JArray AttachArrays = JArray.Parse(resultAttachData);
                                    foreach (JObject AttachArray in AttachArrays)
                                    {
                                        JavaScriptSerializer jsAttach = new JavaScriptSerializer();//实例化一个能够序列化数据的类
                                        Root oputAttach = js.Deserialize<Root>(AttachArray.ToString()); //将json数据转化为对象类型并赋值给list
                                        string filename = oputAttach.filename.ToString();
                                        string fileID = oputAttach.fileUrl.ToString();
                                        string v = oputAttach.v.ToString();
                                        string type = oputAttach.extension.ToString().Replace(".", "");
                                        //string atturl = $"fileDownload.do?method=download&fileId=" + fileID + "&v=" + v + "&filename=" + filename + "";
                                        string attopenurl = $"/seeyon/fileDownload.do?method=doDownload4Office&type=" + type + "&isOpenFile=true&fileId=" + fileID + "&filename=" + filename + "&v=" + v + "";
                                        var ssoAttUrl = "https://i.3311csci.com/seeyon/login/sso?from=cdms4&ticket=" + FormReference.RandomString(4, true) + "&tourl=" + HttpUtility.UrlEncode(attopenurl);
                                        var pathAtthUrl = @"\\*********\保險公司\COIL_DATA\COIL_eClaim\" + PolicyNo.Substring(0, 3).ToString().Trim() + @"\" + ClaimNo + @"\赔款审批表" + fileVersion + @"\" + filename;
                                        if (!System.IO.Directory.Exists(pathAtthUrl.Replace(filename, "")))
                                        {
                                            System.IO.Directory.CreateDirectory(pathAtthUrl.Replace(filename, ""));
                                        }
                                        url.SeleniumAttach(ssoAttUrl, filename, pathAtthUrl.Replace(filename, ""));
                                        //url.Downloadfile(ssoAttUrl, pathAtthUrl);
                                    }
                                    //EC190208 没有附件所以不能保存审批表到\赔款审批表3\

                                    var pathUrl = @"\\*********\保險公司\COIL_DATA\COIL_eClaim\" + PolicyNo.Substring(0, 3).ToString().Trim() + @"\" + ClaimNo + @"\赔款审批表" + fileVersion + @"\";
                                    var targetUrl = $"/seeyon/collaboration/collaboration.do?method=summary&summaryId=" + flowID.ToString().Trim() + "&operationId=" + FormReference.ReferenceID("Indemnity") + "&formId=" + FormReference.FormID("Indemnity") + "";
                                    var ssoUrl = "https://i.3311csci.com/seeyon/login/sso?from=cdms4&ticket=" + FormReference.RandomString(4, true) + "&tourl=" + HttpUtility.UrlEncode(targetUrl);
                                    bool success = url.Selenium(ssoUrl, ClaimNo + "_赔款审批表" + fileVersion, pathUrl);

                                    // 把 HTML 文本内容转换为 PDF
                                    //string htmlStr = WebHandler.GetContent(WebHandler.GetCookie("", "https://i.3311csci.com/seeyon/login/sso?from=cdms4&ticket=lsm1"), ssoUrl);
                                    //url.HtmlTextConvertToPdf(htmlStr, @"\\*********\保險公司\COIL_DATA\COIL_eClaim\001.pdf");

                                    ///// 把 HTML 文件转换为 PDF
                                    //bool success = url.HtmlConvertToPdf(ssoUrl, @"\\*********\保險公司\COIL_DATA\COIL_eClaim\" + ClaimNo + "_赔款审批表" + fileVersion + ".pdf");

                                    if (success)
                                    {
                                        string insertsql = "INSERT INTO [dbo].[CAP4FormApprove]([SummaryId],[PolicyNo],[ClaimNo],[Finvno],[InputDate]) VALUES ('" + flowID + "', '" + PolicyNo + "', '" + ClaimNo + "', '', '" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "')";
                                        using (SqlCommand commandinsert = new SqlCommand(insertsql, connection))
                                        {
                                            commandinsert.ExecuteNonQuery();
                                        }
                                    }
                                }
                            }
                            catch (SqlException e)
                            {
                                Console.WriteLine("Got expected SqlException due to command timeout ");
                                Console.WriteLine(e);
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// _保險業務審批表(分判)下载pdf及附件
        /// </summary>
        [Obsolete]
        public void SeleniumSubcontractPdf()
        {
            var accessToken = FormReference.GetUserRestToken("anna_miao");
            HttpClient client = new HttpClient();
            HttpResponseMessage responseID = client.GetAsync("https://i.3311csci.com/seeyon/rest/flow/FromFinish/Subcontract/2021-05-01/" + DateTime.Now.AddDays(1).ToString("yyyy-MM-dd") + "/?token=" + accessToken + "").Result;
            var resultID = responseID.Content.ReadAsStringAsync().Result;
            JArray flowIDs = JArray.Parse(resultID);
            if (flowIDs != null)
            {
                foreach (string flowID in flowIDs)
                {
                    string sqlselect = "select * from CAP4FormApprove where SummaryId = '" + flowID + "'";
                    using (SqlConnection connection = new SqlConnection(Conn))
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(sqlselect, connection))
                        {
                            command.CommandTimeout = 1;
                            try
                            {
                                var reader = command.ExecuteReader();
                                if (!reader.HasRows)
                                {
                                    HttpResponseMessage responseData = client.GetAsync("https://i.3311csci.com/seeyon/rest/form/getformdata/" + flowID.Trim() + "?token=" + accessToken + "").Result;
                                    var resultData = responseData.Content.ReadAsStringAsync().Result;
                                    var s = Newtonsoft.Json.JsonConvert.DeserializeObject(resultData);
                                    JavaScriptSerializer js = new JavaScriptSerializer();//实例化一个能够序列化数据的类
                                    Application oput = js.Deserialize<Application>(s.ToString()); //将json数据转化为对象类型并赋值给list
                                    string Number = oput.RowData.field0001.ToString().Replace(@"/", "-");

                                    HttpResponseMessage responseAttachData = client.GetAsync("https://i.3311csci.com/seeyon/rest/coll/attachments/" + flowID.Trim() + "/0?token=" + accessToken + "").Result;
                                    var resultAttachData = responseAttachData.Content.ReadAsStringAsync().Result;
                                    var sAttach = Newtonsoft.Json.JsonConvert.DeserializeObject(resultAttachData);
                                    JArray AttachArrays = JArray.Parse(resultAttachData);
                                    foreach (JObject AttachArray in AttachArrays)
                                    {
                                        JavaScriptSerializer jsAttach = new JavaScriptSerializer();//实例化一个能够序列化数据的类
                                        Root oputAttach = js.Deserialize<Root>(AttachArray.ToString()); //将json数据转化为对象类型并赋值给list
                                        string filename = oputAttach.filename.ToString();
                                        string fileID = oputAttach.fileUrl.ToString();
                                        string v = oputAttach.v.ToString();
                                        string type = oputAttach.extension.ToString().Replace(".", "");
                                        //string atturl = $"fileDownload.do?method=download&fileId=" + fileID + "&v=" + v + "&filename=" + filename + "";
                                        string attopenurl = $"/seeyon/fileDownload.do?method=doDownload4Office&type=" + type + "&isOpenFile=true&fileId=" + fileID + "&filename=" + filename + "&v=" + v + "";
                                        var ssoAttUrl = "https://i.3311csci.com/seeyon/login/sso?from=cdms4&ticket=" + FormReference.RandomString(4, true) + "&tourl=" + HttpUtility.UrlEncode(attopenurl);
                                        var pathAtthUrl = @"\\*********\保險公司\COIL_DATA\COIL_eApprove\" + Number + @"\" + filename;
                                        if (!System.IO.Directory.Exists(pathAtthUrl.Replace(filename, "")))
                                        {
                                            System.IO.Directory.CreateDirectory(pathAtthUrl.Replace(filename, ""));
                                        }
                                        url.SeleniumAttach(ssoAttUrl, filename, pathAtthUrl.Replace(filename, ""));
                                        //url.Downloadfile(ssoAttUrl, pathAtthUrl);
                                    }

                                    var pathUrl = @"\\*********\保險公司\COIL_DATA\COIL_eApprove\" + Number + @"\";
                                    var targetUrl = $"/seeyon/collaboration/collaboration.do?method=summary&summaryId=" + flowID.ToString().Trim() + "&operationId=" + FormReference.ReferenceID("Subcontract") + "&formId=" + FormReference.FormID("Subcontract") + "";
                                    var ssoUrl = "https://i.3311csci.com/seeyon/login/sso?from=cdms4&ticket=" + FormReference.RandomString(4, true) + "&tourl=" + HttpUtility.UrlEncode(targetUrl);
                                    bool success = url.Selenium(ssoUrl, Number, pathUrl);

                                    // 把 HTML 文本内容转换为 PDF
                                    //string htmlStr = WebHandler.GetContent(WebHandler.GetCookie("", "https://i.3311csci.com/seeyon/login/sso?from=cdms4&ticket="+ FormReference.RandomString(4, true) + ""), ssoUrl);
                                    //url.HtmlTextConvertToPdf(htmlStr, @"\\*********\保險公司\COIL_DATA\COIL_eClaim\001.pdf");

                                    ///// 把 HTML 文件转换为 PDF
                                    //bool success = url.HtmlConvertToPdf(ssoUrl, @"\\*********\保險公司\COIL_DATA\COIL_eClaim\" + ClaimNo + "_赔款审批表" + fileVersion + ".pdf");

                                    if (success)
                                    {
                                        string insertsql = "INSERT INTO [dbo].[CAP4FormApprove]([SummaryId],[PolicyNo],[ClaimNo],[Finvno],[InputDate]) VALUES ('" + flowID + "', '" + Number + "', '', '', '" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "')";
                                        using (SqlCommand commandinsert = new SqlCommand(insertsql, connection))
                                        {
                                            commandinsert.ExecuteNonQuery();
                                        }
                                    }
                                }
                            }
                            catch (SqlException e)
                            {
                                Console.WriteLine("Got expected SqlException due to command timeout ");
                                Console.WriteLine(e);
                            }
                        }
                    }
                }
            }
        }


        /// <summary>
        /// _保險業務審批表(分判)下载pdf及附件
        /// </summary>
        [Obsolete]
        public void SeleniumtenderPdf()
        {
            var accessToken = FormReference.GetUserRestToken("anna_miao");
            HttpClient client = new HttpClient();
            HttpResponseMessage responseID = client.GetAsync("https://i.3311csci.com/seeyon/rest/flow/FromFinish/tender/2021-04-21/" + DateTime.Now.AddDays(1).ToString("yyyy-MM-dd") + "/?token=" + accessToken + "").Result;
            var resultID = responseID.Content.ReadAsStringAsync().Result;
            JArray flowIDs = JArray.Parse(resultID);
            if (flowIDs != null)
            {
                foreach (string flowID in flowIDs)
                {
                    string sqlselect = "select * from CAP4FormApprove where SummaryId = '" + flowID + "'";
                    using (SqlConnection connection = new SqlConnection(Conn))
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(sqlselect, connection))
                        {
                            command.CommandTimeout = 1;
                            try
                            {
                                var reader = command.ExecuteReader();
                                if (!reader.HasRows)
                                {
                                    HttpResponseMessage responseData = client.GetAsync("https://i.3311csci.com/seeyon/rest/form/getformdata/" + flowID.Trim() + "?token=" + accessToken + "").Result;
                                    var resultData = responseData.Content.ReadAsStringAsync().Result;
                                    var s = Newtonsoft.Json.JsonConvert.DeserializeObject(resultData);
                                    JavaScriptSerializer js = new JavaScriptSerializer();//实例化一个能够序列化数据的类
                                    Application oput = js.Deserialize<Application>(s.ToString()); //将json数据转化为对象类型并赋值给list
                                    string Number = oput.RowData.field0001.ToString().Replace(@"/", "-");

                                    HttpResponseMessage responseAttachData = client.GetAsync("https://i.3311csci.com/seeyon/rest/coll/attachments/" + flowID.Trim() + "/0?token=" + accessToken + "").Result;
                                    var resultAttachData = responseAttachData.Content.ReadAsStringAsync().Result;
                                    var sAttach = Newtonsoft.Json.JsonConvert.DeserializeObject(resultAttachData);
                                    JArray AttachArrays = JArray.Parse(resultAttachData);
                                    foreach (JObject AttachArray in AttachArrays)
                                    {
                                        JavaScriptSerializer jsAttach = new JavaScriptSerializer();//实例化一个能够序列化数据的类
                                        Root oputAttach = js.Deserialize<Root>(AttachArray.ToString()); //将json数据转化为对象类型并赋值给list
                                        string filename = oputAttach.filename.ToString();
                                        string fileID = oputAttach.fileUrl.ToString();
                                        string v = oputAttach.v.ToString();
                                        string type = oputAttach.extension.ToString().Replace(".", "");
                                        //string atturl = $"fileDownload.do?method=download&fileId=" + fileID + "&v=" + v + "&filename=" + filename + "";
                                        string attopenurl = $"/seeyon/fileDownload.do?method=doDownload4Office&type=" + type + "&isOpenFile=true&fileId=" + fileID + "&filename=" + HttpUtility.UrlEncode(filename) + "&v=" + v + "";
                                        var ssoAttUrl = "https://i.3311csci.com/seeyon/login/sso?from=cdms4&ticket=" + FormReference.RandomString(4, true) + "&tourl=" + HttpUtility.UrlEncode(attopenurl);
                                        var pathAtthUrl = @"\\*********\保險公司\COIL_DATA\COIL_eApprove\" + Number + @"\" + filename;
                                        if (!System.IO.Directory.Exists(pathAtthUrl.Replace(filename, "")))
                                        {
                                            System.IO.Directory.CreateDirectory(pathAtthUrl.Replace(filename, ""));
                                        }
                                        url.SeleniumAttach(ssoAttUrl, filename, pathAtthUrl.Replace(filename, ""));
                                        //url.Downloadfile(ssoAttUrl, pathAtthUrl);
                                    }

                                    var pathUrl = @"\\*********\保險公司\COIL_DATA\COIL_eApprove\" + Number + @"\";
                                    var targetUrl = $"/seeyon/collaboration/collaboration.do?method=summary&summaryId=" + flowID.ToString().Trim() + "&operationId=" + FormReference.ReferenceID("tender") + "&formId=" + FormReference.FormID("tender") + "";
                                    var ssoUrl = "https://i.3311csci.com/seeyon/login/sso?from=cdms4&ticket=" + FormReference.RandomString(4, true) + "&tourl=" + HttpUtility.UrlEncode(targetUrl);
                                    bool success = url.Selenium(ssoUrl, Number, pathUrl);

                                    // 把 HTML 文本内容转换为 PDF
                                    //string htmlStr = WebHandler.GetContent(WebHandler.GetCookie("", "https://i.3311csci.com/seeyon/login/sso?from=cdms4&ticket="+ FormReference.RandomString(4, true) + ""), ssoUrl);
                                    //url.HtmlTextConvertToPdf(htmlStr, @"\\*********\保險公司\COIL_DATA\COIL_eClaim\001.pdf");

                                    ///// 把 HTML 文件转换为 PDF
                                    //bool success = url.HtmlConvertToPdf(ssoUrl, @"\\*********\保險公司\COIL_DATA\COIL_eClaim\" + ClaimNo + "_赔款审批表" + fileVersion + ".pdf");

                                    if (success)
                                    {
                                        string insertsql = "INSERT INTO [dbo].[CAP4FormApprove]([SummaryId],[PolicyNo],[ClaimNo],[Finvno],[InputDate]) VALUES ('" + flowID + "', '" + Number + "', '', '', '" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "')";
                                        using (SqlCommand commandinsert = new SqlCommand(insertsql, connection))
                                        {
                                            commandinsert.ExecuteNonQuery();
                                        }
                                    }
                                }
                            }
                            catch (SqlException e)
                            {
                                Console.WriteLine("Got expected SqlException due to command timeout ");
                                Console.WriteLine(e);
                            }
                        }
                    }
                }
            }
        }

        private static string ConvertJsonDateToDateString(Match m)
        {
            string result = string.Empty;
            DateTime dt = new DateTime(1970, 1, 1);
            dt = dt.AddMilliseconds(long.Parse(m.Groups[1].Value));
            dt = dt.ToLocalTime();
            result = dt.ToString("yyyy-MM-dd HH:mm:ss");
            return result;
        }


        [Obsolete]
        public void SeleniumOpyinvhPdf()
        {
            var accessToken = FormReference.GetUserRestToken("anna_miao");
            HttpClient client = new HttpClient();
            HttpResponseMessage responseID = client.GetAsync("https://i.3311csci.com/seeyon/rest/flow/FromFinish/opyinvh/2021-05-03/" + DateTime.Now.ToString("yyyy-MM-dd") + "/?token=" + accessToken + "").Result;
            var resultID = responseID.Content.ReadAsStringAsync().Result;
            JArray flowIDs = JArray.Parse(resultID);
            if (flowIDs != null)
            {
                foreach (string flowID in flowIDs)
                {
                    SqlConnection connection = new SqlConnection(Conn);
                    string sqlselect = "select * from CAP4FormApprove where SummaryId = '" + flowID + "'";
                    using (SqlCommand command = new SqlCommand(sqlselect, connection))
                    {
                        command.Connection.Open();
                        var reader = command.ExecuteReader();
                        if (!reader.HasRows)
                        {
                            HttpResponseMessage responseData = client.GetAsync("https://i.3311csci.com/seeyon/rest/form/getformdata/" + flowID.Trim() + "?token=" + accessToken + "").Result;
                            var resultData = responseData.Content.ReadAsStringAsync().Result;
                            var s = Newtonsoft.Json.JsonConvert.DeserializeObject(resultData);
                            JavaScriptSerializer js = new JavaScriptSerializer();//实例化一个能够序列化数据的类
                            Application oput = js.Deserialize<Application>(s.ToString()); //将json数据转化为对象类型并赋值给list
                            string Finvno = oput.RowData.field0016.ToString();
                            string ClaimNo = oput.RowData.field0021.ToString();

                            string PolicyNo = url.GetPolicy(ClaimNo);
                            HttpResponseMessage responseAttachData = client.GetAsync("https://i.3311csci.com/seeyon/rest/coll/attachments/" + flowID.Trim() + "/0?token=" + accessToken + "").Result;
                            var resultAttachData = responseAttachData.Content.ReadAsStringAsync().Result;
                            var sAttach = Newtonsoft.Json.JsonConvert.DeserializeObject(resultAttachData);
                            JArray AttachArrays = JArray.Parse(resultAttachData);
                            foreach (JObject AttachArray in AttachArrays)
                            {
                                JavaScriptSerializer jsAttach = new JavaScriptSerializer();//实例化一个能够序列化数据的类
                                Root oputAttach = js.Deserialize<Root>(AttachArray.ToString()); //将json数据转化为对象类型并赋值给list
                                string filename = oputAttach.filename.ToString();
                                string fileID = oputAttach.fileUrl.ToString();
                                string v = oputAttach.v.ToString();
                                string type = oputAttach.extension.ToString().Replace(".", "");
                                //string atturl = $"fileDownload.do?method=download&fileId=" + fileID + "&v=" + v + "&filename=" + filename + "";
                                string attopenurl = $"fileDownload.do?method=doDownload4Office&type=" + type + "&isOpenFile=true&fileId=" + fileID + "&filename=" + filename + "&v=" + v + "";
                                var ssoAttUrl = "https://i.3311csci.com/seeyon/login/sso?from=cdms4&ticket=" + FormReference.RandomString(4, true) + "&tourl=" + HttpUtility.UrlEncode(attopenurl);
                                var pathAtthUrl = @"\\*********\保險公司\COIL_DATA\COIL_eClaim\" + PolicyNo.Substring(0, 3).ToString().Trim() + @"\" + ClaimNo + @"\" + Finvno + @"\" + filename;
                                if (!System.IO.Directory.Exists(pathAtthUrl.Replace(filename, "")))
                                {
                                    System.IO.Directory.CreateDirectory(pathAtthUrl.Replace(filename, ""));
                                }
                                url.SeleniumAttach(ssoAttUrl, filename, pathAtthUrl.Replace(filename, ""));
                                //url.Downloadfile(ssoAttUrl, pathAtthUrl);
                            }

                            var pathUrl = @"\\*********\保險公司\COIL_DATA\COIL_eClaim\" + PolicyNo.Substring(0, 3).ToString().Trim() + @"\" + ClaimNo + @"\" + Finvno + @"\";
                            var targetUrl = $"collaboration/collaboration.do?method=summary&summaryId=" + flowID.ToString().Trim() + "&operationId=" + FormReference.ReferenceID("Indemnity") + "&formId=" + FormReference.FormID("Indemnity") + "";
                            var ssoUrl = "https://i.3311csci.com/seeyon/login/sso?from=cdms4&ticket=" + FormReference.RandomString(4, true) + "&tourl=" + HttpUtility.UrlEncode(targetUrl);
                            bool success = url.Selenium(ssoUrl, Finvno, pathUrl);

                            // 把 HTML 文本内容转换为 PDF
                            //string htmlStr = WebHandler.GetContent(WebHandler.GetCookie("", "https://i.3311csci.com/seeyon/login/sso?from=cdms4&ticket=lsm1"), ssoUrl);
                            //url.HtmlTextConvertToPdf(htmlStr, @"\\*********\保險公司\COIL_DATA\COIL_eClaim\001.pdf");

                            ///// 把 HTML 文件转换为 PDF
                            //bool success = url.HtmlConvertToPdf(ssoUrl, @"\\*********\保險公司\COIL_DATA\COIL_eClaim\" + ClaimNo + "_赔款审批表" + fileVersion + ".pdf");

                            if (success)
                            {
                                string insertsql = "INSERT INTO [dbo].[CAP4FormApprove]([SummaryId],[PolicyNo],[ClaimNo],[Finvno],[InputDate]) VALUES ('" + flowID + "', '" + PolicyNo + "', '" + ClaimNo + "', '" + Finvno + "', '" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "')";
                                using (SqlCommand commandinsert = new SqlCommand(insertsql, connection))
                                {
                                    commandinsert.ExecuteNonQuery();
                                }
                            }
                        }
                    }
                }
            }
        }


        public void CheckExpire()
        {
            PDFImageTest Signed = new PDFImageTest();
            using (SqlConnection connection = new SqlConnection(Conn))
            {
                DataTable dt = new DataTable("oexdet");
                string sql = "select fautono from oexdet where fautono in ( 'E20-0697')";
                //"len(fctldel) = ''";
                SqlCommand command = new SqlCommand(sql, connection);
                command.Connection.Open();
                var reader = command.ExecuteReader();
                dt.Load(reader);
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    folderName = @"\\*********\保險公司\COIL_DATA\COIL_eExpireNote\";
                    Signed.signExpire(dt.Rows[i]["fautono"].ToString().Trim() + ".pdf");
                }
            }
        }

        public void RenamePDF()
        {
            using (SqlConnection connection = new SqlConnection(Conn))
            {
                DataTable dt = new DataTable("polh");
                string sql = "select a.fpolno, a.fendtno,b.frno, a.fcnfdate,a.fclass from polh a " +
                            "left join oinsint_e b on a.fctlid = b.fctlid_1 " +
                            "where a.fclass in ('PMP','CMP') AND a.fconfirm = 3 " +
                            "and a.fpolno > 'PMP-21-0001' order by  a.fpolno ";
                //"and len(fctldel)> 10 and (fctldel LIKE '%Finished%' and fctldel NOT LIKE '%Send%') ";
                SqlCommand command = new SqlCommand(sql, connection);
                command.Connection.Open();
                var reader = command.ExecuteReader();
                dt.Load(reader);
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    folderName = @"\\*********\保險公司\COIL_DATA\COIL_ePolicy\" + dt.Rows[i]["fclass"].ToString().Trim() + @"\";
                    string pathString = System.IO.Path.Combine(folderName, dt.Rows[i]["fpolno"].ToString().Trim() + @"\");

                    string sourcename = dt.Rows[i]["fpolno"].ToString().Trim() + "_CI.pdf";
                    string destname = dt.Rows[i]["fpolno"].ToString().Trim() + "(" + dt.Rows[i]["frno"].ToString().Trim() + ")" + "_CI.pdf";
                    string sourcename2 = dt.Rows[i]["fpolno"].ToString().Trim() + ".pdf";
                    string destname2 = dt.Rows[i]["fpolno"].ToString().Trim() + "(" + dt.Rows[i]["frno"].ToString().Trim() + ")" + ".pdf";
                    string sourcename4 = dt.Rows[i]["fendtno"].ToString().Trim() + ".pdf";
                    string destname4 = dt.Rows[i]["fendtno"].ToString().Trim() + "(" + dt.Rows[i]["frno"].ToString().Trim() + ")" + ".pdf";

                    //string sourcename1 = dt.Rows[i]["fpolno"].ToString().Trim() + "_CI_Copy.pdf";
                    //string destname1 = dt.Rows[i]["fpolno"].ToString().Trim() + "(" + dt.Rows[i]["frno"].ToString().Trim() + ")" + "_CI_Copy.pdf";
                    //string sourcename3 = dt.Rows[i]["fpolno"].ToString().Trim() + "_Copy.pdf";
                    //string destname3 = dt.Rows[i]["fpolno"].ToString().Trim() + "(" + dt.Rows[i]["frno"].ToString().Trim() + ")" + "_Copy.pdf";

                    if (File.Exists(pathString + sourcename) && File.Exists(pathString + destname))
                    {
                        File.Delete(pathString + destname);
                    }
                    if (File.Exists(pathString + sourcename) && !File.Exists(pathString + destname))
                    {
                        FileSystem.Rename(pathString + sourcename, pathString + destname);
                    }
                    if (File.Exists(pathString + sourcename2) && File.Exists(pathString + destname2))
                    {
                        File.Delete(pathString + destname2);
                    }
                    if (File.Exists(pathString + sourcename2) && !File.Exists(pathString + destname2))
                    {
                        FileSystem.Rename(pathString + sourcename2, pathString + destname2);
                    }
                    if (File.Exists(pathString + sourcename4) && !File.Exists(pathString + destname4))
                    {
                        FileSystem.Rename(pathString + sourcename4, pathString + destname4);
                    }
                    //if (File.Exists(pathString + sourcename1) && !File.Exists(pathString + destname1))
                    //{
                    //    FileSystem.Rename(pathString + sourcename1, pathString + destname1);
                    //}
                    //if (File.Exists(pathString + sourcename3) && !File.Exists(pathString + destname3))
                    //{
                    //    FileSystem.Rename(pathString + sourcename3, pathString + destname3);
                    //}
                }
            }
        }

        public void MovePDF()
        {
            using (SqlConnection connection = new SqlConnection(Conn))
            {
                DataTable dt = new DataTable("polh");
                string sql = "select a.fpolno, a.fendtno,b.frno, a.fcnfdate,a.fclass from polh a " +
                            "left join oinsint_e b on a.fctlid = b.fctlid_1 " +
                            "where a.fclass in ('PMP', 'CMP') AND a.fconfirm = 3 and a.fcnfdate between '2020-09-01' and '2020-09-16' " +
                            "and len(fctldel)> 10 and fctldel LIKE '%Finished%' ";
                SqlCommand command = new SqlCommand(sql, connection);
                command.Connection.Open();
                var reader = command.ExecuteReader();
                dt.Load(reader);
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    string folderNameFrom = @"\\*********\保險公司\COIL_DATA\SendToCustomer\" + dt.Rows[i]["fclass"].ToString().Trim() + @"\";
                    string folderNameTO = @"\\*********\保險公司\COIL_DATA\COIL_ePolicy\" + dt.Rows[i]["fclass"].ToString().Trim() + @"\";
                    string cifile = dt.Rows[i]["fpolno"].ToString().Trim() + "(" + dt.Rows[i]["frno"].ToString().Trim() + ")" + "_CI_Copy.pdf";
                    string polyfile = dt.Rows[i]["fpolno"].ToString().Trim() + "(" + dt.Rows[i]["frno"].ToString().Trim() + ")" + "_Copy.pdf";
                    string pathStringFrom = System.IO.Path.Combine(folderNameFrom, dt.Rows[i]["fpolno"].ToString().Trim() + @"\");
                    string pathStringTo = System.IO.Path.Combine(folderNameTO, dt.Rows[i]["fpolno"].ToString().Trim() + @"\");
                    if (File.Exists(pathStringFrom + cifile) == true)
                    {
                        if (!System.IO.Directory.Exists(pathStringTo))
                        {
                            System.IO.Directory.CreateDirectory(pathStringTo);
                        }
                        File.Copy(pathStringFrom + cifile, pathStringTo + cifile, true);
                    }
                    if (File.Exists(pathStringFrom + polyfile) == true)
                    {
                        if (!System.IO.Directory.Exists(pathStringTo))
                        {
                            System.IO.Directory.CreateDirectory(pathStringTo);
                        }
                        File.Copy(pathStringFrom + polyfile, pathStringTo + polyfile, true);
                    }
                }
            }
        }

        public void CheckSize()
        {
            try
            {
                string folderNameTO = @"\\*********\保險公司\COIL_DATA\COIL_ePolicyCopy\PMP";
                string[] dirs = Directory.GetDirectories(folderNameTO, "*");
                foreach (string dir in dirs)
                {
                    var filecount = Directory.GetFiles(dir, "*");
                    if (filecount.Length < 2)
                    {
                        Trace.WriteLine(dir + ": " + filecount.Length);
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine("The process failed: {0}", e.ToString());
            }

        }

    }
}
