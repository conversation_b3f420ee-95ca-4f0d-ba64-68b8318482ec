using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SignPDF.DTO
{
    public class PJasonOutPut
    {
        // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
        public class RowData
        {
            public long id { get; set; }
            public int state { get; set; }
            public long start_member_id { get; set; }
            public long start_date { get; set; }
            public long approve_member_id { get; set; }
            public object approve_date { get; set; }
            public int finishedflag { get; set; }
            public int ratifyflag { get; set; }
            public long ratify_member_id { get; set; }
            public object ratify_date { get; set; }
            public int sort { get; set; }
            public long modify_member_id { get; set; }
            public long modify_date { get; set; }
            public object field0035 { get; set; }
            public long field0036 { get; set; }
            public object field0001 { get; set; }
            public object field0003 { get; set; }
            public string field0005 { get; set; }
            public long field0017 { get; set; }
            public int field0019 { get; set; }
            public object field0022 { get; set; }
            public long field0024 { get; set; }
            public long field0025 { get; set; }
            public string field0041 { get; set; }
            public string field0042 { get; set; }
            public object field0055 { get; set; }
            public object field0056 { get; set; }
            public string field0057 { get; set; }
            public object field0059 { get; set; }
            public object field0060 { get; set; }
            public long field0061 { get; set; }
            public object field0062 { get; set; }
            public object field0069 { get; set; }
            public long field0070 { get; set; }
            public long field0072 { get; set; }
            public long field0073 { get; set; }
            public long field0076 { get; set; }
            public string field0077 { get; set; }
            public object field0068 { get; set; }
            public string field0074 { get; set; }
            public object field0075 { get; set; }
            public object field0078 { get; set; }
            public object field0079 { get; set; }
            public object field0080 { get; set; }
            public object field0081 { get; set; }
        }

        public class DataMap
        {
            public object field0075 { get; set; }
            public long field0076 { get; set; }
            public List<object> field0032 { get; set; }
            public string field0077 { get; set; }
            public List<object> field0033 { get; set; }
            public object field0078 { get; set; }
            public List<string> field0034 { get; set; }
            public object field0035 { get; set; }
            public object field0079 { get; set; }
            public long field0036 { get; set; }
            public long field0070 { get; set; }
            public long field0072 { get; set; }
            public long field0073 { get; set; }
            public string field0074 { get; set; }
            public List<object> id { get; set; }
            public int state { get; set; }
            public object approve_date { get; set; }
            public object ratify_date { get; set; }
            public string field0042 { get; set; }
            public List<string> field0043 { get; set; }
            public List<string> field0044 { get; set; }
            public int ratifyflag { get; set; }
            public object field0001 { get; set; }
            public List<int> field0045 { get; set; }
            public List<object> field0046 { get; set; }
            public object field0003 { get; set; }
            public List<object> field0047 { get; set; }
            public List<object> field0048 { get; set; }
            public string field0005 { get; set; }
            public object field0080 { get; set; }
            public List<string> sort { get; set; }
            public object field0081 { get; set; }
            public long ratify_member_id { get; set; }
            public string field0041 { get; set; }
            public int finishedflag { get; set; }
            public List<int> field0053 { get; set; }
            public object field0055 { get; set; }
            public object field0056 { get; set; }
            public string field0057 { get; set; }
            public List<long> formmain_id { get; set; }
            public object field0059 { get; set; }
            public List<object> field0050 { get; set; }
            public long modify_member_id { get; set; }
            public long start_date { get; set; }
            public List<int> field0064 { get; set; }
            public List<object> field0065 { get; set; }
            public object field0022 { get; set; }
            public List<object> field0066 { get; set; }
            public long field0024 { get; set; }
            public object field0068 { get; set; }
            public long field0025 { get; set; }
            public object field0069 { get; set; }
            public object field0060 { get; set; }
            public long field0061 { get; set; }
            public object field0062 { get; set; }
            public long start_member_id { get; set; }
            public long approve_member_id { get; set; }
            public long field0017 { get; set; }
            public int field0019 { get; set; }
            public long modify_date { get; set; }
        }

        public class ProjectRoot
        {
            public long StartDate { get; set; }
            public RowData RowData { get; set; }
            public string code { get; set; }
            public object ApproveDate { get; set; }
            public int FinishedFlag { get; set; }
            public int State { get; set; }
            public int Sort { get; set; }
            public string DataJson { get; set; }
            public long id { get; set; }
            public DataMap DataMap { get; set; }
        }


    }
}
