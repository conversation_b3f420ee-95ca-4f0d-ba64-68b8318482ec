using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SignPDF.DTO
{
    public class ExtraMap
    {
    }

    public class Root
    {
        public long id { get; set; }
        public long reference { get; set; }
        public long subReference { get; set; }
        public int category { get; set; }
        public int type { get; set; }
        public string filename { get; set; }
        public string mimeType { get; set; }
        public long createdate { get; set; }
        public long size { get; set; }
        public object description { get; set; }
        public long fileUrl { get; set; }
        public string extension { get; set; }
        public string icon { get; set; }
        public object genesisId { get; set; }
        public int sort { get; set; }
        public string officeTransformEnable { get; set; }
        public string v { get; set; }
        public ExtraMap extraMap { get; set; }
        public bool @new { get; set; }
    }



}
