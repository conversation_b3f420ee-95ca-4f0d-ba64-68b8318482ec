using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;

namespace ScanToSign.DTO
{
    public class SendEmail
    {
        public static string sendtoCus(string policyNo, string mailto,string classtype)
        {
            string folderName = @"\\10.1.8.27\保險公司\COIL_DATA\SendToCustomer\"+ classtype;
            try
            {
                SmtpClient mailServer = new SmtpClient("10.1.8.169", 587);
                mailServer.EnableSsl = false;
                mailServer.Credentials = new System.Net.NetworkCredential("<EMAIL>", "Cce3311#");

                string from = "<EMAIL>";
                string to = "<EMAIL>";
                MailMessage msg = new MailMessage(from, to);
                msg.Subject = "中國海外保險-電子保單";
                msg.Body = "尊敬的客戶，您好：附件中为您的电子保单，保单号:'" + policyNo + "'。如有疑问，请拨打電話28237324进行咨询。感谢您对我司的支持与理解！祝您生活愉快！";
                if (Directory.Exists(folderName + policyNo.Trim() + "\\"))
                {
                    foreach (string SourceFile in Directory.GetFiles(folderName + policyNo.Trim() + "\\"))  //循环取服务器更新路径文件
                    {
                        string FileName = System.IO.Path.GetFileName(SourceFile);//取更新文件名 
                        msg.Attachments.Add(new Attachment(folderName + policyNo.Trim() + "\\" + FileName));
                    }
                }
                        
                mailServer.Send(msg);
                return "發送成功";
            }
            catch (Exception ex)
            {
                return "發送失敗";
            }

        }

        public static string sendtoApplyName(string EmailTo, string HappenedDate, string EmployeeName, string DepartName, string ApplyerName, string Fclmno, string ToComp)
        {
            //{Host:"owahk.cohl.com",Port:587,User:"cscehk_ecc",Password:"Cce3311#",IsHtml:true,From:"<EMAIL>",DisplayName:"中国建筑国际集团信息化管理部",EnableSsl:false}
            try
            {
                SmtpClient mailServer = new SmtpClient("owahk.cohl.com", 25);
                //邮件发送方式  通过网络发送到smtp服务器
                mailServer.DeliveryMethod = SmtpDeliveryMethod.Network;

                //如果服务器支持安全连接，则将安全连接设为true
                mailServer.EnableSsl = true;

                //是否使用默认凭据，若为false，则使用自定义的证书，就是下面的networkCredential实例对象
                mailServer.UseDefaultCredentials = false;

                //指定邮箱账号和密码,需要注意的是授权码
                NetworkCredential networkCredential = new NetworkCredential("<EMAIL>", "Cce3311#");

                //认证
                mailServer.Credentials = networkCredential;

                ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(ValidateServerCertificate);
                //ServicePointManager.ServerCertificateValidationCallback =
                //    delegate (Object o, X509Certificate certificate, X509Chain chain, SslPolicyErrors errors) { return true; };

                string from = "<EMAIL>";
                string to = EmailTo + ",<EMAIL>";

                MailMessage msg = new MailMessage(from, to);
                msg.IsBodyHtml = true;
                if (ToComp == "其他")
                {
                    msg.Subject = "工傷個案重要提示——其他保險理賠號碼：" + Fclmno + "";
                }
                else {
                    msg.Subject = "工傷個案重要提示——中海保險理賠號碼：" + Fclmno + "";
                }
                msg.Subject = "工傷個案重要提示——中海保險理賠號碼：" + Fclmno + "";
                msg.Body = "<font style='color:red'><b>重要提示：</b><br />" +
                           "<b>請務必盡快提交已填報勞工處之表格2副本予“中國海外保險有限公司”，如在<br />" +
                           "發生意外後3個月內仍未收妥該表格2及相關資料（詳見呈報工傷資料清單），<br />" +
                           "有關索償申請將不予受理。</b></font><br /><br /><br />" +
                           "請查詢系統：<br /><br />" +
                           "地盤名稱：" + DepartName + "<br /><br />" +
                           "傷者姓名：" + EmployeeName + "<br /><br />" +
                           "意外日期：" + HappenedDate + "<br /><br />" +
                           "申請人：" + ApplyerName + "<br /><br /><br />" +
                           "<b>如已辦妥，則無需理會此電郵。</b><br />";
                mailServer.Send(msg);
                return "發送成功";
            }
            catch (Exception ex)
            {
                return "發送失敗";
            }

        }

        public static bool ValidateServerCertificate(Object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors)
        {
            return true;
        }
    }
}
