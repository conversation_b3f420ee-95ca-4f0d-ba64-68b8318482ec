using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using OpenQA.Selenium;
using ScanToSign;
using SignPDF.Models;

namespace SignPDF.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;
        private readonly ScanCheck _scanCheck;
        private static bool _running = false;

        public HomeController(ILogger<HomeController> logger, ScanCheck scanCheck)
        {
            _logger = logger;
            _scanCheck = scanCheck;
        }

        [HttpGet]
        public string Test(string id = "0")
        {
            System.DateTime currentTime = new System.DateTime();
            currentTime = System.DateTime.Now;

            string strT = currentTime.ToString("t");

            _scanCheck.Bond();

            return id;
        }

        public IActionResult Index()
        {
            if (true)
            {
                return View();
            }
            try
            {
                _logger.LogInformation("start exec");
                System.DateTime currentTime = new System.DateTime();
                currentTime = System.DateTime.Now;

                string strT = currentTime.ToString("t");
                if (strT.Contains("10:"))
                {
                    _scanCheck.Bond();
                }

                _scanCheck.Check(); // 
                _scanCheck.InsertECO(); // 
                _scanCheck.ECO(); // 
                _scanCheck.Cheque(); // 
                _scanCheck.State();
                _scanCheck.CDMSExpenseCheck(); // 
                _scanCheck.CDMSReceiptCheck(); // 
                                            //string strT = currentTime.ToString("t");
                if (currentTime.Hour == 9)
                {
                    _scanCheck.PayslipAlertA8();
                    _scanCheck.CertAlertA8();
                }

                _scanCheck.Form2A8(); // 
                _scanCheck.CheckCoverNote();
                _scanCheck.CheckExpire();
                _scanCheck.SeleniumIndemnityPdf();
                _scanCheck.SeleniumSubcontractPdf();
                _scanCheck.SeleniumtenderPdf();
                _scanCheck.CheckBusinessReport();
                _scanCheck.CheckClaimsReport();
                //Checked.RenamePDF(); //加车牌号
                int day = currentTime.Day;
                if (day == 1)
                {
                    _scanCheck.ClosingClaims(); // next Dec
                    _scanCheck.Updateflogseq();
                    // Checked.closedReport();
                }
                _logger.LogInformation("end exec");
                return View();
            }
            catch (Exception e)
            {
                _logger.LogError(e, e.Message);
                throw e;
            }
        }

        private static void TestChrome()
        {
            ChromeOptions options = new ChromeOptions();
            options.AddArguments("--enable-print-browser", "--disable-infobars", "--disable-extensions", "--allow-insecure-localhost", "--ignore-certificate-errors", "--ignore-ssl-errors=yes", "--kiosk-printing", "--allow-running-insecure-content");
            options.UnhandledPromptBehavior = UnhandledPromptBehavior.Ignore;
            options.AddUserProfilePreference("plugins.always_open_pdf_externally", true);
            options.AddUserProfilePreference("print.always_print_silent", true);
            //获取当前执行目录
            String path = System.IO.Directory.GetCurrentDirectory();
            options.AddUserProfilePreference("download.default_directory", path);
            options.AddUserProfilePreference("savefile.default_directory", path);
            options.AddUserProfilePreference("download.prompt_for_download", false);
            options.AddUserProfilePreference("printing.default_destination_selection_rules", "{\"kind\": \"local\", \"namePattern\": \"Save as PDF\"}");
            options.AddUserProfilePreference("printing.print_preview_sticky_settings.appState", "{\"recentDestinations\": [{\"id\": \"Save as PDF\", \"origin\": \"local\", \"account\": \"\" }],\"version\":2,\"scalingType\":3,\"scaling\":\"80\",\"marginsType\":2,\"customMargins\":{\"top\":2,\"bottom\":0},\"isHeaderFooterEnabled\":true,\"isGcpPromoDismissed\":false,\"selectedDestinationId\":\"Save as PDF\"}");
            options.BinaryLocation = @"C:\Program Files\Google\Chrome\Application\chrome.exe";

            ChromeDriverService chromeDriverService = ChromeDriverService.CreateDefaultService();
            //chromeDriverService.SuppressInitialDiagnosticInformation = true;
            //chromeDriverService.HideCommandPromptWindow = true;//even we can hide command prompt window (with un comment this line)  

            using (IWebDriver driver = new OpenQA.Selenium.Chrome.ChromeDriver(chromeDriverService, options, TimeSpan.FromSeconds(120)))
            {

                driver.Navigate().GoToUrl("https://www.baidu.com");
                driver.Quit();
            }
        }

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}
