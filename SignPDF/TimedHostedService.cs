using Microsoft.Extensions.Hosting;
using System.Threading;
using System;
using System.Threading.Tasks;
using SignPDF.Controllers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;

namespace SignPDF
{
    public class TimedHostedService : IHostedService, IDisposable
    {
        private readonly Timer _timer;
        private readonly IServiceProvider _serviceProvider;

        public TimedHostedService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _timer = new Timer(DoWork, null, TimeSpan.FromSeconds(2), TimeSpan.FromMinutes(10));
        }

        private void DoWork(object state)
        {
            var currentHour = DateTime.Now.Hour;
            //输出日志
            Console.WriteLine(System.DateTime.Now.ToLongTimeString() + "开始执行任务");
            using (var scope = _serviceProvider.CreateScope())
            {
                var homeController = ActivatorUtilities.CreateInstance<HomeController>(scope.ServiceProvider);
                homeController.Index();
            }
            Console.WriteLine(System.DateTime.Now.ToLongTimeString() + "任务执行完毕");
        }

        public void Dispose()
        {
            _timer?.Dispose();
        }

        Task IHostedService.StartAsync(CancellationToken cancellationToken)
        {
            return Task.CompletedTask;
        }

        Task IHostedService.StopAsync(CancellationToken cancellationToken)
        {
            _timer?.Change(Timeout.Infinite, 0);
            return Task.CompletedTask;
        }
    }

}
