<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="%LAUNCHER_PATH%" stdoutLogEnabled="false" hostingModel="InProcess">
        <environmentVariables>
          <environmentVariable name="COMPLUS_ForceENC" value="1" />
          <environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Development" />
          <environmentVariable name="ASPNETCORE_HTTPS_PORT" value="443" />
          <environmentVariable name="ASPNETCORE_HOSTINGSTARTUPASSEMBLIES" value="Microsoft.AspNetCore.Watch.BrowserRefresh;Microsoft.WebTools.BrowserLink.Net" />
          <environmentVariable name="DOTNET_STARTUP_HOOKS" value="C:\Program Files\dotnet\SDK\7.0.304\DotnetTools\dotnet-watch\7.0.304-servicing.23275.9\tools\net7.0\any\middleware\Microsoft.AspNetCore.Watch.BrowserRefresh.dll;D:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\Extensions\Microsoft\Web Tools\Browser Link\Microsoft.WebTools.BrowserLink.Net.dll" />
          <environmentVariable name="ASPNETCORE_AUTO_RELOAD_WS_ENDPOINT" value="wss://localhost:44329/SignPDF/,ws://localhost:53731/SignPDF/" />
          <environmentVariable name="DOTNET_MODIFIABLE_ASSEMBLIES" value="debug" />
          <environmentVariable name="ASPNETCORE_AUTO_RELOAD_WS_KEY" value="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA29L8TgiFoHMsMd+ryAnqnRixXAP+i8B/fDj0SIqiahkG7qWFVy3gOFcbbZFkvXCTMPXbSvSDnqM9QnptKy6YSAGHp+iRhuKWpMUJxNKEV2wDcuvXTduTGNb8sJT+Vgc6lRCOmDQIRq/CuCQ6gXBWHYDiCIfJcPgyqAEZ8/68yDyjvSJy38thsto+GEEtVfclLBjGpOcTIMHgNsNcfx6Ifj7Q6NOJm9OkgkEM1D5NGTUzI57Wyqi+xCtd+gT7fF4/vOf5cNSWD81QD8qfnSeOvSnL2rrx18KIMeucExpN1riLJkKsoP72BZKFewjtLngBEMKFpg1I7quafaJxE4JmTQIDAQAB" />
          <environmentVariable name="ASPNETCORE_AUTO_RELOAD_VDIR" value="/" />
        </environmentVariables>
      </aspNetCore>
    </system.webServer>
  </location>
  <system.webServer>
    <httpRedirect enabled="false" destination="https://pur.csci.com.hk/" />
  </system.webServer>
</configuration>