using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;

namespace Utils
{
    public static class TokenFetcher
    {
        public static string GetSsoToken(string authKey, string userName)
        {
            var url = "https://wfapi.3311csci.com/auth/sso_encrypt_token" + "?auth_key=" + authKey + "&user_id=" + userName;
            try
            {
                var values = HttpClientHelper.FetchResultsViaHttpGet(url, "encrypt_token");
                return values[0];
            }
            catch (Exception ex)
            {
                throw new Exception("尝试获取用户加密验证授权失败：" + ex.Message);
            }
        }

        public static string GetAccessToken(string authKey)
        {
            var url = "https://wfapi.3311csci.com/auth/access_token" + "?auth_key=" + authKey;
            try
            {
                var values = HttpClientHelper.FetchResultsViaHttpGet(url, "access_token");
                return values[0];
            }
            catch (Exception ex)
            {
                throw new Exception("尝试获取访问令牌失败：" + ex.Message);
            }
        }

        public static string GetRestToken(string ssoToken, string accessToken)
        {
            var json = JsonConvert.SerializeObject(new { sso_encrypt_token = System.Web.HttpUtility.UrlEncode(ssoToken) });
            try
            {
                var values = HttpClientHelper.FetchResultsViaHttpPost("https://wfapi.3311csci.com/systems/a8/gettoken", json, accessToken, "token");
                return values[0];
            }
            catch (Exception ex)
            {
                throw new Exception("尝试获取文件上传令牌失败：" + ex.Message);
            }
        }

        public static string GetUserRestToken(string USERID)
        {
            try
            {
                using (HttpClient client = new HttpClient())
                {
                    var responseMessage = client.GetAsync($@"https://i.3311csci.com/seeyon/rest/token/common-rest-user/2b0cd67b-6ebb-404d-8f86-c8e6e9ffdca8?loginName={USERID}").Result; ;
                    string result = responseMessage.Content.ReadAsStringAsync().Result;
                    if (result.Length < 40)
                    {
                        return result;
                    }
                    JObject jo = JObject.Parse(result);

                    return jo["id"].ToString();
                }
            }
            catch (Exception ex)
            {
                throw new Exception("尝试获取文件上传令牌失败：" + ex.Message);
            }
        }



        //public static string GetRestToken(string userName)
        //{
        //    var ssoToken = TokenFetcher.GetSsoToken(AuthKey4SsoToken, userName);
        //    var accessToken = TokenFetcher.GetAccessToken(AuthKey4AccessToken);
        //    return GetRestToken(ssoToken, accessToken);
        //}

        /* 直接连接A8原生接口获取令牌的代码，已作废
        public static string GetRestToken(string userName)
        {
            var userData = new Dictionary<string, string>
            {
                { "userName", "restuser" },
                { "password", "restuser" },
                { "loginName", userName }
            };
            var url = $"http://a8hktest.cohl.com:80/seeyon/rest/token";
            var json = JsonConvert.SerializeObject(userData);
            try
            {
                var values = FetchResultsViaHttpPost(url, json, "", "id");
                return values[0];
            }
            catch (Exception ex)
            {
                throw new Exception("尝试获取用户加密验证授权失败：" + ex.Message);
            }
        }*/

    }
}
