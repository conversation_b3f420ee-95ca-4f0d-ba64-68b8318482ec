using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace Utils
{
    public static class JsonReaderHelper
    {
        public static string[] ReadJsonPathsValueAsString(string json, params string[] jsonPaths)
        {
            //if (string.IsNullOrWhiteSpace(json))
            //    throw new ArgumentNullException(nameof(json));
            //if (jsonPaths == null || jsonPaths.Length == 0)
            //    throw new ArgumentNullException(nameof(jsonPaths));
            var count = jsonPaths.Length;
            var results = new string[count];
            var paths = new List<string>(jsonPaths);
            using (var stringReader = new StringReader(json))
            {
                using (var jsonReader = new JsonTextReader(stringReader))
                {
                    while (jsonReader.Read() && count > 0)
                    {
                        var i = paths.IndexOf(jsonReader.Path); //Array.IndexOf(jsonPaths, jsonReader.Path);
                        if (i >= 0)
                        {
                            results[i] = jsonReader.ReadAsString();
                            count--;
                        }
                    }
                    if (paths.Count == 3)
                    {
                        var myJObject = JObject.Parse(json);
                        string summaryId = myJObject.SelectToken("$.data.app_bussiness_data").Value<string>();
                        results[2] = summaryId;
                    }
                    
                }
            }
            return results;
        }

    }
}
