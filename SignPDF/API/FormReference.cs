using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Utils;

namespace SignPDF.DTO
{
    public class FormReference
    {
        public static string GetUserRestToken(string USERID)
        {
            try
            {
                using (HttpClient client = new HttpClient())
                {
                    var responseMessage = client.GetAsync($@"https://i.3311csci.com/seeyon/rest/token/common-rest-user/2b0cd67b-6ebb-404d-8f86-c8e6e9ffdca8?loginName={USERID}").Result; ;
                    string result = responseMessage.Content.ReadAsStringAsync().Result;
                    if (result.Length < 40)
                    {
                        return result;
                    }
                    JObject jo = JObject.Parse(result);

                    return jo["id"].ToString();
                }
            }
            catch (Exception ex)
            {
                throw new Exception("尝试获取文件上传令牌失败：" + ex.Message);
            }
        }

        public static string ReferenceID(string flag)
        {
            string str = "";
            if (flag == "tender")
                str = "8046329667756314385.2065764374978708031";
            if (flag == "Subcontract")
                str = "8315591038893445052.-4240820586837554304";
            if (flag == "Indemnity")
                str = "477544368987328316.146230716362801490";
            if (flag == "Hire")
                str = "6990322993005086195.6595547824619538979";
            if (flag == "Appendix")
                str = "3341838730938502026.4372646581545813210";
            if (flag == "Breifing")
                str = "";
            return str;
        }
        private static readonly Random _random = new Random();
        public static string RandomString(int size, bool lowerCase = false)
        {
            var builder = new StringBuilder(size);

            // Unicode/ASCII Letters are divided into two blocks
            // (Letters 65–90 / 97–122):
            // The first group containing the uppercase letters and
            // the second group containing the lowercase.  

            // char is a single Unicode character  
            char offset = lowerCase ? 'a' : 'A';
            const int lettersOffset = 26; // A...Z or a..z: length=26  

            for (var i = 0; i < size; i++)
            {
                var @char = (char)_random.Next(offset, offset + lettersOffset);
                builder.Append(@char);
            }

            return lowerCase ? builder.ToString().ToLower() : builder.ToString();
        }

        public static string FormID(string flag)
        {
            string str = "";
            if (flag == "tender")
                str = "-2517479011583637312";
            if (flag == "Subcontract")
                str = "-7464118781798234419";
            if (flag == "Indemnity")
                str = "2589535429041801728";
            if (flag == "Hire")
                str = "-5754550262799973596";
            if (flag == "Appendix")
                str = "7790753155897116936";
            if (flag == "Breifing")
                str = "";
            return str;
        }

        public static string UserName(string flag)
        {
            string str = "";
            if (flag == "CSCI\\vanessa_lam" || flag == "CSCI\\VANESSA_LAM")
                str = "Vanessa Lam";
            else if (flag == "CSCI\\catt_lam" || flag == "CSCI\\CATT_LAM")
                str = "林桂芳";
            else if (flag == "CSCI\\april_lim" || flag == "CSCI\\APRIL_LIM")
                str = "林惠";
            else if (flag == "CSCI\\carol_chan" || flag == "CSCI\\CAROL_CHAN")
                str = "陳麗茹";
            else 
                str = "";
            return str;
        }

        public static string TempleCode(string flag)
        {
            string str = "";
            if (flag == "保險業務審批表投標")
                str = "tender";
            if (flag == "保險業務審批表分判")
                str = "Subcontract";
            if (flag == "賠款審批表")
                str = "Indemnity";
            if (flag == "聘用審批表")
                str = "Hire";
            if (flag == "賠款審批表附頁")
                str = "Appendix";
            return str;
        }
        
    }
}
