using System.Collections.Generic;
using System.Linq;
using Database.Context;
using Models.DtoConverters;
using Models.EntityDtos;

namespace HandlerMethod
{
    public class JobHandler : BaseHandler
    {
        public List<JobDto> GetList(JobDto con)
        {
            var query = db.T_JOB.Where(m => m.Language == con.language).OrderBy(o => o.CreateTime);
            JobDtoConverter _convert = new JobDtoConverter();
            var list = query.Select(o => _convert.Convert(o)).ToList();

            return list;
        }

        public JobDto GetInfo(JobDto model)
        {
            var query = db.T_JOB.Where(m => 1 == 1);

            if (model.id > 0)
            {
                query = query.Where(m => m.ID == model.id);
            }

            JobDtoConverter _convert = new JobDtoConverter();

            model = _convert.Convert(query.SingleOrDefault());

            return model;
        }
    }
}
