<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
  </configSections>
  <connectionStrings>
    <add name="odata" connectionString="Data Source=cob-server-090;User ID=common;PassWord=**********;Initial Catalog=live_ilodata;MultipleActiveResultSets=True;Connection Timeout=400" providerName="System.Data.SqlClient" />
    <add name="mdata" connectionString="Data Source=cob-server-090;User ID=common;PassWord=**********;Initial Catalog=live_ilmdata;MultipleActiveResultSets=True;Connection Timeout=400" providerName="System.Data.SqlClient" />
    <add name="INS.Properties.Settings.ConnectionString1" connectionString="Dsn=live_ilodata;uid=sa" providerName="System.Data.Odbc" />
  </connectionStrings>
  <startup useLegacyV2RuntimeActivationPolicy="true">
  <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.1" /></startup>
  <appSettings>
    <add key="DefaultCulture" value="Big5" />
    <add key="StylesSheetFilename" value="C:\Users\<USER>\Documents\Visual Studio 2013\Projects\INS\INS\Css\styles1.xml" />
    <add key="ClientSettingsProvider.ServiceUri" value="" />
  </appSettings>
  <system.web>
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
      </providers>
    </membership>
    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
      </providers>
    </roleManager>
  </system.web>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <!--<dependentAssembly>
        <assemblyIdentity name="CrystalDecisions.Shared" publicKeyToken="692fbea5521e1304" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.3500.0" newVersion="13.0.3500.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="CrystalDecisions.CrystalReports.Engine" publicKeyToken="692fbea5521e1304" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.2000.0" newVersion="13.0.2000.0" />
      </dependentAssembly>-->
      <dependentAssembly>
        <assemblyIdentity name="BouncyCastle.Crypto" publicKeyToken="0e99375e54769942" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.8.9.0" newVersion="1.8.9.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
