using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using INS.INSClass;
using System.Data.SqlClient;
using System.Configuration;
using System.Collections;

namespace INS.Ctrl
{
    public partial class EndorDirectInsured_CPM : UserControl
    {
        public string fctlid = "";
        public int insuredlistrow = 0;
        public Boolean Addflag = false;
        public Boolean updateflag = false;
        public string subclass = "";
        public string Class = "";
        public string Classfctlid = "";
        public string ftype = "";
        public string fbus = "";
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        private DataTable insureddataSource = new DataTable();
        public DataTable mmain = new DataTable();
        private TextBox Cur_TextBox = null;
        public Boolean Skip_Validate = false;


        public string EndtType
        {
            get { return fendttype.Text; }
            set { fendttype.Text = value; }
        }

        public EndorDirectInsured_CPM()
        {
            InitializeComponent();
            InitCombobox();
            foreach (Control control in panel4.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);
                if (control is TextBox)
                    control.Enter += new EventHandler(textBox_Enter);    //添加事件//添加事件
            }
            mmain.Columns.Add("fsi", typeof(decimal));
            mmain.Columns.Add("fupdsi", typeof(decimal));
            mmain.Columns.Add("fgpm", typeof(decimal));
            mmain.Columns.Add("ftdamt", typeof(decimal));
            mmain.Columns.Add("fcomamt", typeof(decimal));
            mmain.Columns.Add("fnpm", typeof(decimal));
            mmain.Columns.Add("fliab", typeof(decimal));
            mmain.Columns.Add("fupdliab", typeof(decimal));
            mmain.Columns.Add("flgpm", typeof(decimal));
            mmain.Columns.Add("fltdamt", typeof(decimal));
            mmain.Columns.Add("flcomamt", typeof(decimal));
            mmain.Columns.Add("flnpm", typeof(decimal));
        }

        void textBox_Enter(object sender, EventArgs e)
        {
            Cur_TextBox = (TextBox)sender;
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
            {
                SendKeys.Send("{TAB}");
            }
        }

        public void setControlFocus(string ctrlName)
        {
            if (ctrlName == "addinsur") { Controls["panel1"].Controls["panel4"].Controls["panel5"].Controls[ctrlName].Focus(); }
            else { Controls["panel1"].Controls["panel4"].Controls[ctrlName].Focus(); }
        }

        void tabPage3reload(string fctlid)
        {
            foreach (Control ctrl in panel4.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = true;
                }
            }

            String Sql = "select CONVERT(varchar(20),flogseq) as flogseq, case when fmode=1 then 'New' else case when fmode=2 then 'Revised' else 'Cancelled' end end as fmode, CONVERT(varchar(20),fqty) as fqty, CONVERT(varchar(20),fyr) as fyr,  " +
                        "fsi, fgpm,ftdamt,fcomamt,fnpm,fdesc1 as content, fctlid, fitem, fupdsi,fupdsi as fupdsio, fupdqty, fupdqty as fupdqtyo,fctlid_pk from oinsint_d ";
            if (ftype == "E") { Sql = Sql + "where fctlid_1 ='" + fctlid + "'"; } else { Sql = Sql + "where fctlid_1 ='" + fctlid + "'"; }

            insuredlist.DataSource = DBHelper.GetDataSet(Sql);
            insuredlist.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.insuredlist_CellFormatting);

            if (insuredlist.RowCount == 0)
            {
                foreach (Control ctrl in panel4.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                    }
                }
                this.insuredlist.Columns[9].Visible = false;
                this.insuredlist.Columns[10].Visible = false;
                this.insuredlist.Columns[11].Visible = false;
                this.insuredlist.Columns[12].Visible = false;
                this.insuredlist.Columns[13].Visible = false;
                this.insuredlist.Columns[14].Visible = false;
                this.insuredlist.Columns[15].Visible = false;
                this.insuredlist.Columns[16].Visible = false;
                insuredlist.Columns["flogseq"].HeaderText = "Seq#";
                insuredlist.Columns["fmode"].HeaderText = "Mode";
                insuredlist.Columns["fqty"].HeaderText = "Qty (+/-)";
                insuredlist.Columns["fyr"].HeaderText = "Year";
                insuredlist.Columns["fsi"].HeaderText = "Sum Ins (+/-)";
                insuredlist.Columns["fgpm"].HeaderText = "G Permium";
                insuredlist.Columns["ftdamt"].HeaderText = "Discount";
                insuredlist.Columns["fcomamt"].HeaderText = "Comm";
                insuredlist.Columns["fnpm"].HeaderText = "N Permium";
            }
        }

        private void insuredlist_CellFormatting(object sender,
     System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            this.insuredlist.Columns[9].Visible = false;
            this.insuredlist.Columns[10].Visible = false;
            this.insuredlist.Columns[11].Visible = false;
            this.insuredlist.Columns[12].Visible = false;
            this.insuredlist.Columns[13].Visible = false;
            this.insuredlist.Columns[14].Visible = false;
            this.insuredlist.Columns[15].Visible = false;
            this.insuredlist.Columns[16].Visible = false;
            insuredlist.Columns["flogseq"].HeaderText = "Seq#";
            insuredlist.Columns["fmode"].HeaderText = "Mode";
            insuredlist.Columns["fqty"].HeaderText = "Qty (+/-)";
            insuredlist.Columns["fyr"].HeaderText = "Year";
            insuredlist.Columns["fsi"].HeaderText = "Sum Ins (+/-)";
            insuredlist.Columns["fgpm"].HeaderText = "G Permium";
            insuredlist.Columns["ftdamt"].HeaderText = "Discount";
            insuredlist.Columns["fcomamt"].HeaderText = "Comm";
            insuredlist.Columns["fnpm"].HeaderText = "N Permium";
            if (insuredlist.Columns[e.ColumnIndex].Name.Equals("fsi") ||
                insuredlist.Columns[e.ColumnIndex].Name.Equals("fgpm") ||
                insuredlist.Columns[e.ColumnIndex].Name.Equals("ftdamt") ||
                insuredlist.Columns[e.ColumnIndex].Name.Equals("fcomamt") ||
                insuredlist.Columns[e.ColumnIndex].Name.Equals("fnpm") ||
                insuredlist.Columns[e.ColumnIndex].Name.Equals("fqty"))
            {
                e.CellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                insuredlist.Columns[e.ColumnIndex].AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
            }

            if (insuredlist.Columns[e.ColumnIndex].Name.Equals("fyr"))
            {
                e.CellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                insuredlist.Columns[e.ColumnIndex].Width = 40;
            }
            if (insuredlist.Columns[e.ColumnIndex].Name.Equals("fmode"))
            {
                insuredlist.Columns[e.ColumnIndex].Width = 45;
            }
            if (insuredlist.Columns[e.ColumnIndex].Name.Equals("flogseq"))
            {
                insuredlist.Columns[e.ColumnIndex].Width = 35;
                e.CellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            }
        }

        void insuredchg()
        {
            try
            {
                if (insuredlist.CurrentCell != null)
                {
                    insuredlistrow = insuredlist.CurrentCell.RowIndex;
                }
                else { return; }
                if (insuredlist.Rows[insuredlistrow].Cells["flogseq"].Value != null)
                {
                    flogseq.Text = insuredlist.Rows[insuredlistrow].Cells["flogseq"].Value.ToString().Trim();
                }
                if (insuredlist.Rows[insuredlistrow].Cells["fitem"].Value != null)
                {
                    fitem.Text = insuredlist.Rows[insuredlistrow].Cells["fitem"].Value.ToString().Trim();
                }
                if (insuredlist.Rows[insuredlistrow].Cells["fupdsi"].Value != null)
                {
                    fupdsi_1.Text = insuredlist.Rows[insuredlistrow].Cells["fupdsio"].Value.ToString().Trim();
                    fupdsi.Text = insuredlist.Rows[insuredlistrow].Cells["fupdsi"].Value.ToString().Trim();
                    fupdsi.Text = Convert.ToDecimal(fupdsi.Text).ToString("n2");
                }
                if (insuredlist.Rows[insuredlistrow].Cells["fsi"].Value != null)
                {
                    fsi.Text = insuredlist.Rows[insuredlistrow].Cells["fsi"].Value.ToString().Trim();
                }
                if (insuredlist.Rows[insuredlistrow].Cells["fgpm"].Value != null)
                {
                    fgpm.Text = insuredlist.Rows[insuredlistrow].Cells["fgpm"].Value.ToString().Trim();
                }
                if (insuredlist.Rows[insuredlistrow].Cells["ftdamt"].Value != null)
                {
                    ftdamt.Text = insuredlist.Rows[insuredlistrow].Cells["ftdamt"].Value.ToString().Trim();
                }
                if (insuredlist.Rows[insuredlistrow].Cells["fcomamt"].Value != null)
                {
                    fcomamt.Text = insuredlist.Rows[insuredlistrow].Cells["fcomamt"].Value.ToString().Trim();
                }
                if (insuredlist.Rows[insuredlistrow].Cells["fnpm"].Value != null)
                {
                    fnpm.Text = insuredlist.Rows[insuredlistrow].Cells["fnpm"].Value.ToString().Trim();
                }
                if (insuredlist.Rows[insuredlistrow].Cells["content"].Value != null)
                {
                    fdesc.Text = insuredlist.Rows[insuredlistrow].Cells["content"].Value.ToString().Trim();
                }
                if (insuredlist.Rows[insuredlistrow].Cells["fyr"].Value != null)
                {
                    fyr.Text = insuredlist.Rows[insuredlistrow].Cells["fyr"].Value.ToString().Trim();
                }
                if (insuredlist.Rows[insuredlistrow].Cells["fqty"].Value != null)
                {
                    fqty.Text = insuredlist.Rows[insuredlistrow].Cells["fqty"].Value.ToString().Trim();
                }
                if (insuredlist.Rows[insuredlistrow].Cells["fupdqty"].Value != null)
                {
                    fupdqty_1.Text = insuredlist.Rows[insuredlistrow].Cells["fupdqtyo"].Value.ToString().Trim();
                    fupdqty.Text = insuredlist.Rows[insuredlistrow].Cells["fupdqty"].Value.ToString().Trim();
                }
                if (insuredlist.Rows[insuredlistrow].Cells["fmode"].Value != null)
                {
                    comboMode.Text = insuredlist.Rows[insuredlistrow].Cells["fmode"].Value.ToString().Trim();
                }
            }
            catch { }
        }

        private void insuredlist_SelectionChanged(object sender, EventArgs e)
        {
            insuredchg();
        }

        private void insuredlist_CurrentCellChanged(object sender, EventArgs e)
        {
            insuredchg();
        }

        void btnctrlback(string flag)
        {
            foreach (Control ctrl in panel4.Controls)
            {
                if (ctrl is TextBox)
                {
                    if (flag == "del")
                    {
                        ((TextBox)ctrl).Text = "";
                    }
                    ((TextBox)ctrl).ReadOnly = true;
                    ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
            }
        }

        public void buttoncontrolback()
        {
            Addflag = false;
            updateflag = false;
            addinsur.Visible = false;
            delinsur.Visible = false;
            pick.Visible = false;
            btnctrlback("");
            tabPage3reload(fctlid);
        }

        public void buttoncontroladd(string fendtype)
        {
            Addflag = true;
            addinsur.Visible = true;
            delinsur.Visible = true;
            pick.Visible = true;
            tabpage3add();
        }

        public void buttoncontrolupdate(string fendtype)
        {
            updateflag = true;
            addinsur.Visible = true;
            delinsur.Visible = true;
            pick.Visible = true;
            tabpage3update();
        }

        public void tabpage3add()
        {
            if (fendttype.Text == "2")
            {
                String Sql = "select CONVERT(varchar(20),flogseq) as flogseq, " +
                    "case when '" + fendttype.Text + "'=3 then 'Cancelled' else 'Revised' end as fmode,  " +
                    "CONVERT(varchar(20),case when '" + fendttype.Text + "'=3 then -a.fqty else case when '" + fendttype.Text + "'=2 then 0 else a.fqty end end) as fqty,  CONVERT(varchar(20),fyr) as fyr,  " +
                    "case when '" + fendttype.Text + "'=3 then -a.fupdsi else case when '" + fendttype.Text + "'=2 then 0 else a.fupdsi end end as fsi, 0.0000 as [fgpm],0.0000 as [ftdamt],0.0000 as [fcomamt],0.0000 as [fnpm],  " +
                    "fdesc1 as content, a.fctlid, a.fitem,  " +
                    "case when '" + fendttype.Text + "'=3 then 0 else a.fupdsi end as fupdsi, case when '" + fendttype.Text + "'=1 then 0 else a.fupdsi end as fupdsio, " +
                    "case when '" + fendttype.Text + "'=3 then 0 else a.fupdqty end as fupdqty, case when '" + fendttype.Text + "'=1 then 0 else a.fupdqty end as fupdqtyo, a.fctlid as fctlid_pk " +
                    "from oinsint_d a " +
                    "where fctlid in (select max(a.fctlid) as fctlid from oinsint_d a where fpolno =(select distinct fpolno from polh where fctlid ='" + fctlid + "') group by fitem)";
                insuredlist.DataSource = DBHelper.GetDataSet(Sql);
                insureddataSource = insuredlist.DataSource as DataTable;
                insureddataSource.Clear();
                insuredlist.DataSource = insureddataSource;
                insuredlist.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.insuredlist_CellFormatting);
                foreach (Control ctrl in panel4.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                    }
                }
            }
            else
            {
                String Sql = "select CONVERT(varchar(20),flogseq) as flogseq, "+
                            "case when '" + fendttype.Text + "'=3 then 'Cancelled' else 'Revised' end as fmode,  " +
                            "CONVERT(varchar(20),case when '" + fendttype.Text + "'=3 then -a.fqty else case when '" + fendttype.Text + "'=2 then 0 else a.fqty end end) as fqty,  CONVERT(varchar(20),fyr) as fyr,  " +
                            "case when '" + fendttype.Text + "'=3 then -a.fupdsi else case when '" + fendttype.Text + "'=2 then 0 else a.fupdsi end end as fsi, 0.0000 as [fgpm],0.0000 as [ftdamt],0.0000 as [fcomamt],0.0000 as [fnpm],  " +
                            "fdesc1 as content, a.fctlid, a.fitem,  "+
                            "case when '" + fendttype.Text + "'=3 then 0 else a.fupdsi end as fupdsi, case when '" + fendttype.Text + "'=1 then 0 else a.fupdsi end as fupdsio, "+
                            "case when '" + fendttype.Text + "'=3 then 0 else a.fupdqty end as fupdqty, case when '" + fendttype.Text + "'=1 then 0 else a.fupdqty end as fupdqtyo, a.fctlid as fctlid_pk " +
                            "from oinsint_d a "+
                            "where fctlid in (select max(a.fctlid) as fctlid from oinsint_d a where fpolno =(select distinct fpolno from polh where fctlid ='" + fctlid + "') group by fitem)";
                insuredlist.DataSource = DBHelper.GetDataSet(Sql);
                insuredlist.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.insuredlist_CellFormatting);
                foreach (Control ctrl in panel4.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        if (((TextBox)ctrl).TabStop == true)
                        {
                            ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                            ((TextBox)ctrl).ReadOnly = false;
                        }
                    }
                }
            }
        }

        public void tabpage3update()
        {
            String Sql = "select CONVERT(varchar(20),a.flogseq) as flogseq,case when a.fmode=1 then 'New' else case when a.fmode=2 then 'Revised' else 'Cancelled' end end as fmode, CONVERT(varchar(20),a.fqty) as fqty,CONVERT(varchar(20),a.fyr) as fyr,  " +
                        "a.fsi, a.fgpm,a.ftdamt, a.fcomamt,a.fnpm,a.fdesc1 as content,  " +
                        "a.fctlid, a.fitem, a.fupdsi, a.fupdsi-a.fsi as fupdsio, a.fupdqty, a.fupdqty-a.fqty as fupdqtyo, fctlid_pk   " +
                        "from oinsint_d a  " +
                        "where a.fctlid_1 ='" + fctlid + "'";
            insuredlist.DataSource = DBHelper.GetDataSet(Sql);
            foreach (Control ctrl in panel4.Controls)
            {
                if (ctrl is TextBox)
                {
                    if (((TextBox)ctrl).TabStop == true)
                    {
                        ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                        ((TextBox)ctrl).ReadOnly = false;
                    }
                }
            }
        }

        private void addinsur_Click(object sender, EventArgs e)
        {
            insureddataSource = insuredlist.DataSource as DataTable;
            DataRow newCustomersRow = insureddataSource.NewRow();
            insureddataSource.Rows.InsertAt(newCustomersRow, insureddataSource.Rows.Count);
            insuredlist.DataSource = insureddataSource;
            insuredlist.CurrentCell = insuredlist.Rows[insureddataSource.Rows.Count - 1].Cells["flogseq"];

            foreach (Control ctrl in panel4.Controls)
            {
                if (ctrl is TextBox)
                {
                    if (((TextBox)ctrl).TabStop == true)
                    {
                        ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                        ((TextBox)ctrl).ReadOnly = false;
                    }
                    ((TextBox)ctrl).Text = "";
                }
            }
            
            insuredlist["fmode", insuredlist.CurrentCell.RowIndex].Value = "New";
            comboMode.Text = "New";
        }

        private void pick_Click(object sender, EventArgs e)
        {
            string fctlids = "";
            foreach (DataGridViewRow dr in insuredlist.Rows)
            {
                fctlids = fctlids + "','" + dr.Cells["fitem"].Value.ToString();
            }
            EndtDirectInsured tempform = new EndtDirectInsured(this);
            tempform.flag = "EndorCPM";
            tempform.Fclass = Class;
            tempform.fctlid_p = fctlid;
            tempform.sqladd = "and fitem not in ('" + fctlids + "')";
            tempform.FillData("", "");
            tempform.ShowDialog();
        }

        public void PickList_click(string selfctlid)
        {
            String Sql = "select CONVERT(varchar(20),flogseq) as flogseq, case when '" + fendttype.Text + "'=3 then 'Cancelled' else 'Revised' end as fmode, CONVERT(varchar(20),case when '" + fendttype.Text + "'=3 then -fqty else case when '" + fendttype.Text + "'=2 then 0 else fqty end end) as fqty,  CONVERT(varchar(20),fyr) as fyr,  " +
                         "case when '" + fendttype.Text + "'=3 then -fupdsi else case when '" + fendttype.Text + "'=2 then 0 else fupdsi end end as fsi,0.0000 as fgpm,0.0000 as ftdamt,0.0000 as fcomamt,0.0000 as fnpm,fdesc1 as content, fctlid, fitem,  " +
                         "case when '" + fendttype.Text + "'=3 then 0 else fupdsi end as fupdsi, case when '" + fendttype.Text + "'=1 then 0 else fupdsi end as fupdsio, "+
                         "case when '" + fendttype.Text + "'=3 then 0 else fupdqty end as fupdqty, case when '" + fendttype.Text + "'=1 then 0 else fupdqty end as fupdqtyo, " +
                         "fctlid as fctlid_pk from oinsint_d " +
                         "where fctlid='" + selfctlid + "'";
            DataTable dt = DBHelper.GetDataSet(Sql);
            insureddataSource = insuredlist.DataSource as DataTable;
            string result2 = CheckDuplicate(dt.Rows[0]["content"].ToString(), "Click");
            if (result2 != "")
            {
                MessageBox.Show(result2);
            }
            else
            {
                foreach (Control ctrl in panel4.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        if (((TextBox)ctrl).TabStop == true)
                        {
                            ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                            ((TextBox)ctrl).ReadOnly = false;
                        }
                        ((TextBox)ctrl).Text = "";
                    }
                }
                foreach (DataRow dr in dt.Rows)
                {
                    insureddataSource.Rows.Add(dr.ItemArray);
                }
                insuredlist.DataSource = insureddataSource;
                insuredlist.CurrentCell = insuredlist.Rows[insureddataSource.Rows.Count - 1].Cells["flogseq"];
                insuredchg();
            }
        }


        private void delinsur_Click(object sender, EventArgs e)
        {
            if (insuredlist.RowCount != 0)
            {
                try
                {
                    insureddataSource = insuredlist.DataSource as DataTable;
                    insureddataSource.Rows.RemoveAt(insuredlist.CurrentCell.RowIndex);
                    insuredlist.DataSource = insureddataSource;
                    insuredlist.CurrentCell = insuredlist.Rows[insureddataSource.Rows.Count - 1].Cells["flogseq"];
                }
                catch { }
            }
            if (insuredlist.RowCount == 0)
            {
                btnctrlback("del");
            }
        }

        void u_calinsint()
        {
            decimal ln_fgpm = 0, ln_ftdamt, ln_fcomamt, ln_fnpm = 0, ln_fsi = 0, ln_fupdsio = 0, ln_fupdsi = 0, ln_fqty=0, ln_fupdqty = 0, ln_fupdqtyo = 0;
            ln_fsi = Convert.ToDecimal(fsi.Text);
            ln_fqty = Fct.sdFat(fqty.Text);
            ln_fgpm = Convert.ToDecimal(fgpm.Text);
            ln_ftdamt = Convert.ToDecimal(ftdamt.Text);
            ln_fcomamt = Convert.ToDecimal(fcomamt.Text);
            ln_fupdsio = Fct.sdFat(fupdsi_1.Text);
            ln_fupdqtyo = Fct.sdFat(fupdqty_1.Text);
            ln_fnpm = ln_fgpm - ln_fcomamt - ln_ftdamt;
            ln_fupdsi = ln_fsi + ln_fupdsio;
            ln_fupdqty = ln_fqty + ln_fupdqtyo;
            int row = 0;
            if (insuredlist.CurrentCell != null) { row = insuredlist.CurrentCell.RowIndex; }
            insuredlist["fgpm", row].Value = ln_fgpm;
            insuredlist["fcomamt", row].Value = ln_fcomamt;
            insuredlist["ftdamt", row].Value = ln_ftdamt;
            insuredlist["fnpm", row].Value = ln_fnpm;
            insuredlist["fsi", row].Value = ln_fsi;
            insuredlist["fupdsi", row].Value = ln_fupdsi;
            insuredlist["fqty", row].Value = ln_fqty;
            insuredlist["fupdqty", row].Value = ln_fupdqty;
        }

        public void u_calculation()
        {
            mmain.Clear();
            decimal ln_fgpm = 0, ln_ftdamt = 0, ln_fcomamt = 0, ln_fnpm = 0, ln_fupdsi = 0, ln_ttfupdliab = 0,
            ln_ttfupdsi = 0, ln_ttfgpm = 0, ln_ttftdamt = 0, ln_ttfcomamt = 0, ln_ttfnpm = 0, ln_ttfsi = 0, ln_fsi = 0,
            ln_ttflgpm = 0, ln_ttfltdamt = 0, ln_ttflcomamt = 0, ln_ttflnpm = 0, ln_ttfliab = 0;
            string fkind = "1";
            foreach (DataGridViewRow dr in insuredlist.Rows)
            {
                if (dr.Cells["fsi"].Value.ToString() != "" && dr.Cells["fupdsi"].Value.ToString() != "" && dr.Cells["fgpm"].Value.ToString() != "")
                {
                    ln_fsi = Convert.ToDecimal(dr.Cells["fsi"].Value.ToString());
                    ln_fupdsi = Convert.ToDecimal(dr.Cells["fupdsi"].Value.ToString());
                    ln_fgpm = Convert.ToDecimal(dr.Cells["fgpm"].Value.ToString());
                    ln_ftdamt = Convert.ToDecimal(dr.Cells["ftdamt"].Value.ToString());
                    ln_fcomamt = Convert.ToDecimal(dr.Cells["fcomamt"].Value.ToString());
                    ln_fnpm = ln_fgpm - ln_fcomamt - ln_ftdamt;
                    ln_ttfgpm = ln_ttfgpm + ln_fgpm;
                    ln_ttftdamt = ln_ttftdamt + ln_ftdamt;
                    ln_ttfnpm = ln_ttfnpm + ln_fnpm;
                    ln_ttfcomamt = ln_ttfcomamt + ln_fcomamt;
                    if (fkind == "1")
                    {
                        ln_ttfsi = ln_ttfsi + ln_fsi;
                        ln_ttfupdsi = ln_ttfupdsi + ln_fupdsi;
                    }
                    else
                    {
                        ln_ttflgpm = ln_ttflgpm + ln_fgpm;
                        ln_ttfltdamt = ln_ttfltdamt + ln_ftdamt;
                        ln_ttflcomamt = ln_ttflcomamt + ln_fcomamt;
                        ln_ttflnpm = ln_ttflnpm + ln_fnpm;
                        ln_ttfliab = ln_ttfliab + ln_fsi;
                        ln_ttfupdliab = ln_ttfupdliab + ln_fupdsi;
                    }

                    dr.Cells["fupdsi"].Value = ln_fupdsi;
                    dr.Cells["fgpm"].Value = ln_fgpm;
                    dr.Cells["fcomamt"].Value = ln_fcomamt;
                    dr.Cells["ftdamt"].Value = ln_ftdamt;
                    dr.Cells["fnpm"].Value = ln_fnpm;
                    dr.Cells["fsi"].Value = ln_fsi;
                }
            }
            mmain.Rows.Add(ln_ttfsi, ln_ttfupdsi, ln_ttfgpm, ln_ttftdamt, ln_ttfcomamt, ln_ttfnpm, ln_ttfliab,ln_ttfupdliab,ln_ttflgpm, ln_ttfltdamt, ln_ttflcomamt, ln_ttflnpm);
        }

        private void panel1_MouseClick(object sender, MouseEventArgs e)
        {
            this.panel1.Focus();
        }

        private void panel1_MouseWheel(object sender, MouseEventArgs e)
        {
            if (panel1.VerticalScroll.Value < 100)
            {
                panel1.VerticalScroll.Value += 10;
                panel1.Refresh();
                panel1.Invalidate();
                panel1.Update();
            }
        }

        public enum fmode
        {
            New = 1,
            Revised = 2,
            Cancel = 3
        }

        private void InitCombobox()
        {
            Array arr = System.Enum.GetValues(typeof(fmode));    // 获取枚举的所有值
            DataTable dt = new DataTable();
            dt.Columns.Add("String", Type.GetType("System.String"));
            dt.Columns.Add("Value", typeof(int));
            foreach (var a in arr)
            {
                string strText = EnumTextByDescription.GetEnumDesc((fmode)a);
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = (int)a;
                dt.Rows.Add(aRow);
            }
            comboMode.DataSource = dt;
            comboMode.DisplayMember = "String";
            comboMode.ValueMember = "Value";

        }

        private void comboMode_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (insuredlist.CurrentCell != null)
            {
                insuredlist["fmode", insuredlist.CurrentCell.RowIndex].Value = comboMode.Text;
            }
        }

        public string u_valsec()
        {
            if (insuredlist.RowCount == 0)
            {
                MessageBox.Show("Insured Item is not found!", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return "addinsur";
            }
            foreach (DataGridViewRow row in insuredlist.Rows)
            {
                int SelectedIndex = insuredlist.Rows.IndexOf(row);
                if (row.Cells["content"].Value.ToString().Trim() == "")
                {
                    MessageBox.Show("Empty Value is not allowed!", "Warning",
                 MessageBoxButtons.OK, MessageBoxIcon.Information);
                    fdesc.Focus();
                    insuredlist.CurrentCell = insuredlist.Rows[SelectedIndex].Cells["flogseq"];
                    return "fdesc";
                }
            }
            return "";
        }

        public string tabpage3cofirm()
        {
            string ctrlName = u_valsec();
            return ctrlName;
        }

        public string[] tabpage3save(string newfctlid, string Subclass)
        {
            if (Addflag == true)
            {
                ArrayList addsql = new ArrayList();
                string fctlid_oinsint = Fct.NewId("OINSINTIDD");

                string str1 = "select * from oinsint_d where fctlid_1='" + fctlid + "'";
                DataTable dt2 = DBHelper.GetDataSet(str1);
                string Pfctlid = dt2.Rows[0]["fctlid_p"].ToString();
                string fpolno = dt2.Rows[0]["fpolno"].ToString();
                string sql = "select max(fitem) from oinsint_d where fpolno=(select distinct fpolno from polh where fctlid ='" + fctlid + "') ";
                int ofitem = DBHelper.GetScalar(sql) + 1; int fitem;
                DataTable dt = insuredlist.DataSource as DataTable;
                if (dt != null && dt.Rows.Count > 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        if (i != 0) { fctlid_oinsint = (int.Parse(fctlid_oinsint) + 1).ToString().PadLeft(10, '0'); }
                        decimal fsi = 0, fgpm = 0, ftdamt = 0, fcomamt = 0, fnpm = 0, fupdsi = 0,fupdqty= 0,fqty = 0;
                        string fmode = "";
                        fsi = Fct.sdFat(dt.Rows[i]["fsi"]);
                        fupdsi = Fct.sdFat(dt.Rows[i]["fupdsi"]);
                        fgpm = Fct.sdFat(dt.Rows[i]["fgpm"]);
                        ftdamt = Fct.sdFat(dt.Rows[i]["ftdamt"]);
                        fcomamt = Fct.sdFat(dt.Rows[i]["fcomamt"]);
                        fnpm = Fct.sdFat(dt.Rows[i]["fnpm"]);
                        fqty = Fct.sdFat(dt.Rows[i]["fqty"]);
                        fupdqty = Fct.sdFat(dt.Rows[i]["fupdqty"]);
                        if (Fct.stFat(dt.Rows[i]["fmode"]) == "New") { fmode = "1"; }
                        if (Fct.stFat(dt.Rows[i]["fmode"]) == "Revised") { fmode = "2"; }
                        if (Fct.stFat(dt.Rows[i]["fmode"]) == "Cancelled") { fmode = "3"; }
                        if (Fct.stFat(dt.Rows[i]["fitem"]) == "") { ofitem = ofitem + i; fitem = ofitem; } else { fitem = int.Parse(dt.Rows[i]["fitem"].ToString()); } 
                        string insertsql = "INSERT INTO [dbo].[oinsint_d]([fclass],[fsclass],[fpolno],[fvseq],[fvarno],[fendtno],[ftype],[fkind],[fctlid_p],[fctlid_v],[fctlid_1],[fctlid],[fid_c],[fid],[fdesc1],[fyr],[fqty],[fupdqty],[funit],[flogseq],[forgsi],[fuorgsi],[fsi],[fupdsi],[fcalc],[fprate],[forggp],[fgpm],[ftdamt],[fcomamt],[fnpm],[fiocclmt],[fioccall],[fitem],[fmode],[fctlid_pk]) " +
                                     "VALUES('" + Class + "','" + Subclass.ToString().Trim() + "','" + fpolno + "',0,'','','SEC1','1','" + Pfctlid + "','" + Pfctlid + "','" + newfctlid + "','" + fctlid_oinsint + "','','','" + Fct.stFat(dt.Rows[i]["content"]) + "','" + Fct.stFat(dt.Rows[i]["fyr"]) + "','" + fqty + "','" + fupdqty + "',''," + Fct.stFat(dt.Rows[i]["flogseq"]) + ",'0.0000','0.0000','" + fsi + "','" + fupdsi + "','2','0.0000000','0.0000','" + fgpm + "','" + ftdamt + "','" + fcomamt + "','" + fnpm + "','','0.0000','" + fitem + "','" + fmode + "','" + dt.Rows[i]["fctlid_pk"].ToString().Trim() + "')";
                        addsql.Add(insertsql);
                    }
                    string sqlfctlid = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_oinsint) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='OINSINTIDD'";
                    addsql.Add(sqlfctlid);
                }
                return (string[])addsql.ToArray(typeof(string));
            }
            else if (updateflag == true)
            {
                String Sql = "select CONVERT(varchar(20),a.flogseq) as flogseq,case when a.fmode=1 then 'New' else case when a.fmode=2 then 'Revised' else 'Cancelled' end end as fmode, CONVERT(varchar(20),a.fqty) as fqty,CONVERT(varchar(20),a.fyr) as fyr,  " +
                        "a.fsi, a.fgpm,a.ftdamt, a.fcomamt,a.fnpm,a.fdesc1 as content,  " +
                        "a.fctlid, a.fitem, a.fupdsi, a.fupdsi-a.fsi as fupdsio, a.fupdqty, a.fupdqty-a.fqty as fupdqtyo, fctlid_pk   " +
                        "from oinsint_d a  " +
                        "where a.fctlid_1 ='" + fctlid + "'";
                DataTable dt0 = DBHelper.GetDataSet(Sql);
                DataTable dt = insuredlist.DataSource as DataTable;
                if (dt != null)
                {
                    string[] updatesql = CompareDt(dt0, dt, "fctlid", Subclass);
                    return updatesql;
                }
                else
                {
                    return null;
                }
            }
            else
            {
                ArrayList sql = new ArrayList();
                return (string[])sql.ToArray(typeof(string));
            }
        }

        public string[] CompareDt(DataTable dt1, DataTable dt2, string keyField, string Subclass)
        {
            //为三个表拷贝表结构
            DataTable dtRetDel = dt1.Clone();
            DataTable dtRetAdd = dt1.Clone();
            DataTable dtRetDif = dt1.Clone();

            ArrayList updsql = new ArrayList();
            int colCount = dt1.Columns.Count;

            DataView dv1 = dt1.DefaultView;
            DataView dv2 = dt2.DefaultView;

            //先以第一个表为参照，看第二个表是修改了还是删除了
            foreach (DataRowView dr1 in dv1)
            {
                dv2.RowFilter = keyField + " = '" + dr1[keyField].ToString() + "'";
                if (dv2.Count > 0)
                {
                    if (!CompareUpdate(dr1, dv2[0]))//比较是否有不同的
                    {
                        dtRetDif.Rows.Add(dv2[0].Row.ItemArray);//修改后
                        continue;
                    }
                }
                else
                {
                    //已经被删除的
                    dtRetDel.Rows.Add(dr1.Row.ItemArray);
                }
            }

            //以第一个表为参照，看记录是否是新增的
            dv2.RowFilter = "";//清空条件
            foreach (DataRowView dr2 in dv2)
            {
                dv1.RowFilter = keyField + " = '" + dr2[keyField].ToString() + "'";
                if (dv1.Count == 0)
                {
                    //新增的
                    dtRetAdd.Rows.Add(dr2.Row.ItemArray);
                }
            }

            if (dtRetAdd != null && dtRetAdd.Rows.Count > 0)
            {
                string fctlid_oinsint = Fct.NewId("OINSINTIDD");
                string str1 = "select * from oinsint_d where fctlid_1='" + fctlid + "'";
                DataTable dtpolicy = DBHelper.GetDataSet(str1);
                string Pfctlid = dtpolicy.Rows[0]["fctlid_p"].ToString();
                string fpolno = dtpolicy.Rows[0]["fpolno"].ToString();
                string sql = "select max(fitem) from oinsint_d where fpolno=(select distinct fpolno from polh where fctlid ='" + fctlid + "') ";
                int ofitem = DBHelper.GetScalar(sql) + 1; int fitem;
                for (int i = 0; i < dtRetAdd.Rows.Count; i++)
                {
                    if (i != 0) { fctlid_oinsint = (int.Parse(fctlid_oinsint) + 1).ToString().PadLeft(10, '0'); }
                    decimal fsi = 0, fgpm = 0, ftdamt = 0, fcomamt = 0, fnpm = 0, fupdsi = 0, fupdqty = 0, fqty = 0;
                    string fmode = "";
                    fsi = Fct.sdFat(dtRetAdd.Rows[i]["fsi"]);
                    fupdsi = Fct.sdFat(dtRetAdd.Rows[i]["fupdsi"]);
                    fgpm = Fct.sdFat(dtRetAdd.Rows[i]["fgpm"]);
                    ftdamt = Fct.sdFat(dtRetAdd.Rows[i]["ftdamt"]);
                    fcomamt = Fct.sdFat(dtRetAdd.Rows[i]["fcomamt"]);
                    fnpm = Fct.sdFat(dtRetAdd.Rows[i]["fnpm"]);
                    fqty = Fct.sdFat(dtRetAdd.Rows[i]["fqty"]);
                    fupdqty = Fct.sdFat(dtRetAdd.Rows[i]["fupdqty"]);
                    if (Fct.stFat(dtRetAdd.Rows[i]["fmode"]) == "New") { fmode = "1"; }
                    if (Fct.stFat(dtRetAdd.Rows[i]["fmode"]) == "Revised") { fmode = "2"; }
                    if (Fct.stFat(dtRetAdd.Rows[i]["fmode"]) == "Cancelled") { fmode = "3"; }
                    if (Fct.stFat(dtRetAdd.Rows[i]["fitem"]) == "") { ofitem = ofitem + i; fitem = ofitem; } 
                    else { fitem = int.Parse(dtRetAdd.Rows[i]["fitem"].ToString()); } 
                    string insertsql = "INSERT INTO [dbo].[oinsint_d]([fclass],[fsclass],[fpolno],[fvseq],[fvarno],[fendtno],[ftype],[fkind],[fctlid_p],[fctlid_v],[fctlid_1],[fctlid],[fid_c],[fid],[fdesc1],[fyr],[fqty],[fupdqty],[funit],[flogseq],[forgsi],[fuorgsi],[fsi],[fupdsi],[fcalc],[fprate],[forggp],[fgpm],[ftdamt],[fcomamt],[fnpm],[fiocclmt],[fioccall],[fitem],[fmode],[fctlid_pk]) " +
                                 "VALUES('" + Class + "','" + Subclass.ToString().Trim() + "','" + fpolno + "',0,'','','SEC1','1','" + Pfctlid + "','" + Pfctlid + "','" + fctlid + "','" + fctlid_oinsint + "','','','" + Fct.stFat(dtRetAdd.Rows[i]["content"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fyr"]) + "','" + fqty + "','" + fupdqty + "',''," + Fct.stFat(dtRetAdd.Rows[i]["flogseq"]) + ",'0.0000','0.0000','" + fsi + "','" + fupdsi + "','2','0.0000000','0.0000','" + fgpm + "','" + ftdamt + "','" + fcomamt + "','" + fnpm + "','','0.0000','"+ fitem +"','" + fmode + "','" + dtRetAdd.Rows[i]["fctlid_pk"].ToString().Trim() + "')";
                    updsql.Add(insertsql);
                }
            }
            if (dtRetDif != null && dtRetDif.Rows.Count > 0)
            {
                for (int i = 0; i < dtRetDif.Rows.Count; i++)
                {
                    decimal fsi = 0, fgpm = 0, ftdamt = 0, fcomamt = 0, fnpm = 0, fupdsi = 0, fupdqty = 0, fqty = 0;
                    fsi = Fct.sdFat(dtRetDif.Rows[i]["fsi"]);
                    fupdsi = Fct.sdFat(dtRetDif.Rows[i]["fupdsi"]);
                    fgpm = Fct.sdFat(dtRetDif.Rows[i]["fgpm"]);
                    ftdamt = Fct.sdFat(dtRetDif.Rows[i]["ftdamt"]);
                    fcomamt = Fct.sdFat(dtRetDif.Rows[i]["fcomamt"]);
                    fnpm = Fct.sdFat(dtRetDif.Rows[i]["fnpm"]);
                    fqty = Fct.sdFat(dtRetDif.Rows[i]["fqty"]);
                    fupdqty = Fct.sdFat(dtRetDif.Rows[i]["fupdqty"]);
                    string update = "UPDATE [dbo].[oinsint_d] SET [fdesc1] = '" + Fct.stFat(dtRetDif.Rows[i]["content"]) + "',[fyr] = '" + Fct.stFat(dtRetDif.Rows[i]["fyr"]) + "',[fqty] = '" + fqty + "',[fupdqty] = '" + fupdqty + "',[flogseq] = '" + Fct.stFat(dtRetDif.Rows[i]["flogseq"]) + "',[fsi] = '" + fsi + "',[fupdsi] = '" + fupdsi + "',[fgpm] = '" + fgpm + "',[ftdamt] ='" + ftdamt + "',[fcomamt] ='" + fcomamt + "',[fnpm] = '" + fnpm + "' where fctlid='" + dtRetDif.Rows[i]["fctlid"].ToString().Trim() + "'";
                    updsql.Add(update);
                }
            }

            if (dtRetDel != null && dtRetDel.Rows.Count > 0)
            {
                for (int i = 0; i < dtRetDel.Rows.Count; i++)
                {
                    string del = "delete from oinsint_d where fctlid='" + dtRetDel.Rows[i]["fctlid"].ToString().Trim() + "'";
                    updsql.Add(del);
                }
            }

            return (string[])updsql.ToArray(typeof(string));
        }

        //比较是否有不同的
        private static bool CompareUpdate(DataRowView dr1, DataRowView dr2)
        {
            //行里只要有一项不一样，整个行就不一样,无需比较其它
            object val1;
            object val2;
            for (int i = 0; i < dr1.Row.ItemArray.Length; i++)
            {
                val1 = dr1[i];
                val2 = dr2[i];
                if (!val1.Equals(val2))
                {
                    return false;
                }
            }
            return true;
        }

        public string CheckDuplicate(string fid, string flag)
        {
            if (fid != "")
            {
                if (insureddataSource.Rows.Count > 0)
                {
                    DataTable objectTable = insureddataSource.DefaultView.ToTable();
                    DataRow[] rows = objectTable.Select("[content]='" + Fct.stFat(fid) + "'");
                    if (flag == "ENTER")
                    {
                        if (rows.Length > 1)
                        {
                            return "Duplicate Value";
                        }
                        else { return ""; }
                    }
                    else
                    {
                        if (rows.Length > 0)
                        {
                            return "Duplicate Value";
                        }
                        else { return ""; }
                    }
                }
                else { return ""; }
            }
            else { return ""; }
        }

        private Boolean txtValid(TextBox text_obj)
        {
            Boolean ll_ok;
            if ((!Addflag && !updateflag) || Skip_Validate || text_obj.ReadOnly) { ll_ok = false; }
            else { ll_ok = Validation.CheckEmptyString(text_obj); }
            return ll_ok;
        }

        private void flogseq_TextChanged(object sender, EventArgs e)
        {
            if ((!Addflag && !updateflag) || insuredlist.CurrentCell == null)
                return;
            insuredlist["flogseq", insuredlist.CurrentCell.RowIndex].Value = Fct.snFat(flogseq.Text.ToString());
        }

        private void flogseq_Validated(object sender, EventArgs e)
        {
            if (!Validation.CheckValidInt((TextBox)sender))
            {
                MessageBox.Show("Invalid Value!");
                Cur_TextBox.Focus();
            }
            else
            {
                flogseq.Text = Fct.snFat(flogseq.Text).ToString();
                insuredlist["flogseq", insuredlist.CurrentCell.RowIndex].Value = Fct.snFat(flogseq.Text.ToString());
            }
        }

        private void fqty_Validated(object sender, EventArgs e)
        {
            if (txtValid((TextBox)sender))
            {
                if (!Validation.CheckValidInt((TextBox)sender))
                {
                    MessageBox.Show("Invalid Value!");
                    Cur_TextBox.Focus();
                }
                else {
                    u_calinsint(); insuredchg();
                }
            }
        }

        private void fyr_Validated(object sender, EventArgs e)
        {
            if (txtValid((TextBox)sender))
            {
                if (!Validation.CheckValidYear((TextBox)sender))
                {
                    MessageBox.Show("Invalid Value!");
                    Cur_TextBox.Focus();
                }
                else { insuredlist["fyr", insuredlist.CurrentCell.RowIndex].Value = fyr.Text.ToString(); }
            }
        }

        private void fsi_Validated(object sender, EventArgs e)
        {
            if (Addflag == true || updateflag == true)
            {
                u_calinsint();
                insuredchg();
            }
        }

        private void fgpm_Validated(object sender, EventArgs e)
        {
            if (Addflag == true || updateflag == true)
            {
                u_calinsint(); insuredchg();
            }
        }

        private void fcomamt_Validated(object sender, EventArgs e)
        {
            if (Addflag == true || updateflag == true)
            {
                u_calinsint(); insuredchg();
            }
        }

        private void fdesc_Validated(object sender, EventArgs e)
        {
            if (txtValid((TextBox)sender))
            {
                insuredlist["content", insuredlist.CurrentCell.RowIndex].Value = fdesc.Text.ToString();
            }
        }

        private void EndorDirectInsured_CPM_Enter(object sender, EventArgs e)
        {
            Skip_Validate = false;
        }

        private void EndorDirectInsured_CPM_Leave(object sender, EventArgs e)
        {
            Skip_Validate = true;
        }

        
        
      



    }
}
