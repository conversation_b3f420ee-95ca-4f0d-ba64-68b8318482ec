namespace INS.Ctrl
{
    partial class DirectButton
    {
        /// <summary> 
        /// 設計工具所需的變數。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清除任何使用中的資源。
        /// </summary>
        /// <param name="disposing">如果應該處置 Managed 資源則為 true，否則為 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 元件設計工具產生的程式碼

        /// <summary> 
        /// 此為設計工具支援所需的方法 - 請勿使用程式碼編輯器
        /// 修改這個方法的內容。
        /// </summary>
        private void InitializeComponent()
        {
            this.panel23 = new System.Windows.Forms.Panel();
            this.progressBar1 = new System.Windows.Forms.ProgressBar();
            this.Expiry = new System.Windows.Forms.Button();
            this.push = new System.Windows.Forms.Button();
            this.copy = new System.Windows.Forms.Button();
            this.AddPolicy = new System.Windows.Forms.Button();
            this.cancelpolicy = new System.Windows.Forms.Button();
            this.ModifyPolicy = new System.Windows.Forms.Button();
            this.savepolicy = new System.Windows.Forms.Button();
            this.DelPolicy = new System.Windows.Forms.Button();
            this.renewpolicy = new System.Windows.Forms.Button();
            this.ConfirmPolicy = new System.Windows.Forms.Button();
            this.ExitPolicy = new System.Windows.Forms.Button();
            this.PrintPolicy = new System.Windows.Forms.Button();
            this.Noted = new System.Windows.Forms.Button();
            this.panel23.SuspendLayout();
            this.SuspendLayout();
            // 
            // panel23
            // 
            this.panel23.CausesValidation = false;
            this.panel23.Controls.Add(this.progressBar1);
            this.panel23.Controls.Add(this.Expiry);
            this.panel23.Controls.Add(this.push);
            this.panel23.Controls.Add(this.copy);
            this.panel23.Controls.Add(this.AddPolicy);
            this.panel23.Controls.Add(this.cancelpolicy);
            this.panel23.Controls.Add(this.ModifyPolicy);
            this.panel23.Controls.Add(this.savepolicy);
            this.panel23.Controls.Add(this.DelPolicy);
            this.panel23.Controls.Add(this.renewpolicy);
            this.panel23.Controls.Add(this.ConfirmPolicy);
            this.panel23.Controls.Add(this.ExitPolicy);
            this.panel23.Controls.Add(this.PrintPolicy);
            this.panel23.Location = new System.Drawing.Point(0, 1);
            this.panel23.Name = "panel23";
            this.panel23.Size = new System.Drawing.Size(950, 38);
            this.panel23.TabIndex = 16;
            // 
            // progressBar1
            // 
            this.progressBar1.Location = new System.Drawing.Point(847, 7);
            this.progressBar1.Maximum = 50;
            this.progressBar1.Name = "progressBar1";
            this.progressBar1.Size = new System.Drawing.Size(100, 23);
            this.progressBar1.TabIndex = 311;
            this.progressBar1.Visible = false;
            // 
            // Expiry
            // 
            this.Expiry.CausesValidation = false;
            this.Expiry.Location = new System.Drawing.Point(671, 7);
            this.Expiry.Name = "Expiry";
            this.Expiry.Size = new System.Drawing.Size(75, 21);
            this.Expiry.TabIndex = 16;
            this.Expiry.TabStop = false;
            this.Expiry.Text = "Expiry";
            this.Expiry.UseVisualStyleBackColor = true;
            this.Expiry.Visible = false;
            this.Expiry.Click += new System.EventHandler(this.Expiry_Click);
            // 
            // push
            // 
            this.push.Location = new System.Drawing.Point(374, 8);
            this.push.Name = "push";
            this.push.Size = new System.Drawing.Size(75, 21);
            this.push.TabIndex = 15;
            this.push.TabStop = false;
            this.push.Text = "PushToA8";
            this.push.UseVisualStyleBackColor = true;
            this.push.Click += new System.EventHandler(this.push_Click);
            // 
            // copy
            // 
            this.copy.Location = new System.Drawing.Point(433, 7);
            this.copy.Name = "copy";
            this.copy.Size = new System.Drawing.Size(75, 21);
            this.copy.TabIndex = 14;
            this.copy.TabStop = false;
            this.copy.Text = "Copy";
            this.copy.UseVisualStyleBackColor = true;
            this.copy.Visible = false;
            this.copy.Click += new System.EventHandler(this.copy_Click);
            // 
            // AddPolicy
            // 
            this.AddPolicy.Location = new System.Drawing.Point(99, 8);
            this.AddPolicy.Name = "AddPolicy";
            this.AddPolicy.Size = new System.Drawing.Size(75, 21);
            this.AddPolicy.TabIndex = 4;
            this.AddPolicy.TabStop = false;
            this.AddPolicy.Text = "Add";
            this.AddPolicy.UseVisualStyleBackColor = true;
            this.AddPolicy.Click += new System.EventHandler(this.AddPolicy_Click);
            // 
            // cancelpolicy
            // 
            this.cancelpolicy.CausesValidation = false;
            this.cancelpolicy.Location = new System.Drawing.Point(639, 7);
            this.cancelpolicy.Name = "cancelpolicy";
            this.cancelpolicy.Size = new System.Drawing.Size(75, 21);
            this.cancelpolicy.TabIndex = 13;
            this.cancelpolicy.TabStop = false;
            this.cancelpolicy.Text = "Cancel";
            this.cancelpolicy.UseVisualStyleBackColor = true;
            this.cancelpolicy.Visible = false;
            this.cancelpolicy.Click += new System.EventHandler(this.cancelpolicy_Click);
            // 
            // ModifyPolicy
            // 
            this.ModifyPolicy.Location = new System.Drawing.Point(194, 8);
            this.ModifyPolicy.Name = "ModifyPolicy";
            this.ModifyPolicy.Size = new System.Drawing.Size(75, 21);
            this.ModifyPolicy.TabIndex = 5;
            this.ModifyPolicy.TabStop = false;
            this.ModifyPolicy.Text = "Modify";
            this.ModifyPolicy.UseVisualStyleBackColor = true;
            this.ModifyPolicy.Visible = false;
            this.ModifyPolicy.Click += new System.EventHandler(this.ModifyPolicy_Click);
            // 
            // savepolicy
            // 
            this.savepolicy.CausesValidation = false;
            this.savepolicy.Location = new System.Drawing.Point(536, 8);
            this.savepolicy.Name = "savepolicy";
            this.savepolicy.Size = new System.Drawing.Size(75, 21);
            this.savepolicy.TabIndex = 12;
            this.savepolicy.TabStop = false;
            this.savepolicy.Text = "Save";
            this.savepolicy.UseVisualStyleBackColor = true;
            this.savepolicy.Visible = false;
            this.savepolicy.Click += new System.EventHandler(this.savepolicy_Click);
            // 
            // DelPolicy
            // 
            this.DelPolicy.Location = new System.Drawing.Point(285, 8);
            this.DelPolicy.Name = "DelPolicy";
            this.DelPolicy.Size = new System.Drawing.Size(75, 21);
            this.DelPolicy.TabIndex = 6;
            this.DelPolicy.TabStop = false;
            this.DelPolicy.Text = "Delete";
            this.DelPolicy.UseVisualStyleBackColor = true;
            this.DelPolicy.Visible = false;
            this.DelPolicy.Click += new System.EventHandler(this.DelPolicy_Click);
            // 
            // renewpolicy
            // 
            this.renewpolicy.Location = new System.Drawing.Point(514, 7);
            this.renewpolicy.Name = "renewpolicy";
            this.renewpolicy.Size = new System.Drawing.Size(75, 21);
            this.renewpolicy.TabIndex = 11;
            this.renewpolicy.TabStop = false;
            this.renewpolicy.Text = "Renew";
            this.renewpolicy.UseVisualStyleBackColor = true;
            this.renewpolicy.Click += new System.EventHandler(this.renewpolicy_Click);
            // 
            // ConfirmPolicy
            // 
            this.ConfirmPolicy.Location = new System.Drawing.Point(508, 7);
            this.ConfirmPolicy.Name = "ConfirmPolicy";
            this.ConfirmPolicy.Size = new System.Drawing.Size(75, 21);
            this.ConfirmPolicy.TabIndex = 7;
            this.ConfirmPolicy.TabStop = false;
            this.ConfirmPolicy.Text = "Confirm";
            this.ConfirmPolicy.UseVisualStyleBackColor = true;
            this.ConfirmPolicy.Visible = false;
            this.ConfirmPolicy.Click += new System.EventHandler(this.ConfirmPolicy_Click);
            // 
            // ExitPolicy
            // 
            this.ExitPolicy.Location = new System.Drawing.Point(666, 7);
            this.ExitPolicy.Name = "ExitPolicy";
            this.ExitPolicy.Size = new System.Drawing.Size(75, 21);
            this.ExitPolicy.TabIndex = 9;
            this.ExitPolicy.TabStop = false;
            this.ExitPolicy.Text = "Exit";
            this.ExitPolicy.UseVisualStyleBackColor = true;
            this.ExitPolicy.Click += new System.EventHandler(this.ExitPolicy_Click);
            // 
            // PrintPolicy
            // 
            this.PrintPolicy.Location = new System.Drawing.Point(590, 7);
            this.PrintPolicy.Name = "PrintPolicy";
            this.PrintPolicy.Size = new System.Drawing.Size(75, 21);
            this.PrintPolicy.TabIndex = 8;
            this.PrintPolicy.TabStop = false;
            this.PrintPolicy.Text = "Print";
            this.PrintPolicy.UseVisualStyleBackColor = true;
            this.PrintPolicy.Click += new System.EventHandler(this.PrintPolicy_Click);
            // 
            // Noted
            // 
            this.Noted.Location = new System.Drawing.Point(747, 8);
            this.Noted.Name = "Noted";
            this.Noted.Size = new System.Drawing.Size(75, 21);
            this.Noted.TabIndex = 16;
            this.Noted.TabStop = false;
            this.Noted.Text = "通知客户";
            this.Noted.UseVisualStyleBackColor = true;
            this.Noted.Visible = false;
            this.Noted.Click += new System.EventHandler(this.Noted_Click);
            // 
            // DirectButton
            // 
            this.AccessibleRole = System.Windows.Forms.AccessibleRole.WhiteSpace;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.Noted);
            this.Controls.Add(this.panel23);
            this.Name = "DirectButton";
            this.Size = new System.Drawing.Size(950, 38);
            this.panel23.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel23;
        private System.Windows.Forms.Button AddPolicy;
        private System.Windows.Forms.Button cancelpolicy;
        private System.Windows.Forms.Button ModifyPolicy;
        private System.Windows.Forms.Button savepolicy;
        private System.Windows.Forms.Button DelPolicy;
        private System.Windows.Forms.Button renewpolicy;
        private System.Windows.Forms.Button ConfirmPolicy;
        private System.Windows.Forms.Button ExitPolicy;
        private System.Windows.Forms.Button PrintPolicy;
        private System.Windows.Forms.Button copy;
        private System.Windows.Forms.Button push;
        private System.Windows.Forms.Button Noted;
        private System.Windows.Forms.Button Expiry;
        private System.Windows.Forms.ProgressBar progressBar1;
    }
}
