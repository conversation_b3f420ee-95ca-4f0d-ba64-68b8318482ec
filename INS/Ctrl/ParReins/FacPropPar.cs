using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using INS.INSClass;
using System.Data.SqlClient;
using System.Configuration;
using System.Collections;

namespace INS.Ctrl.Reins
{
    public partial class FacPropPar : UserControl
    {
        GenInfoPar Gen = new GenInfoPar();
        public string fctlid = "", fclass = "", AbsnetTot = "", ComamtTot = "", fsec = "";
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public Boolean updateflag = false;
        public DataTable FacMain = new DataTable();
        public DataTable ComMain = new DataTable();
        private DataTable orild2 = new DataTable();
        public DataSet dataSet = new DataSet("ds");
        public Boolean Skip_Validate = false;

        public String ReinsValue
        {
            set
            { freinsr.Text = value; }
        }
        public String ReinsDesc
        {
            set
            { textBox16.Text = value; }
        }
        public String BrokerValue
        {
            set
            { fbrkr.Text = value; }
        }
        public String BrokerDesc
        {
            set
            { textBox14.Text = value; }
        }

        public FacPropPar()
        {
            InitializeComponent();
            InitCombobox();
            foreach (Control control in panel1.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            FacMain.Columns.Add("fctlid_lc", typeof(String));
            FacMain.Columns.Add("fshare", typeof(decimal));
            FacMain.Columns.Add("fsi", typeof(decimal));
            FacMain.Columns.Add("fgpm", typeof(decimal));
            FacMain.Columns.Add("fnpm", typeof(decimal));
            FacMain.Columns.Add("fcomamt", typeof(decimal));
            ComMain.Columns.Add("fctlid_lc", typeof(String));
            ComMain.Columns.Add("fcomamt", typeof(decimal));
            ComMain.Columns.Add("fabsnet", typeof(decimal));
            ComMain.Clear();
        }

        public void setControlFocus(string ctrlName)
        {
            if (ctrlName == "fsharec" || ctrlName == "fsic")
            {
                Controls["panel2"].Controls[ctrlName].Focus();
            }
            else
            {
               Controls["panel1"].Controls[ctrlName].Focus();
            }
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
            {
                SendKeys.Send("{TAB}");
            }
        }

        public enum Mode
        {
            [Description("Share%")]
            fshare,

            [Description("SI/ Limit")]
            fsi,
        }

        private void InitCombobox()
        {
            Array arr = System.Enum.GetValues(typeof(Mode));    // 获取枚举的所有值
            DataTable dt = new DataTable();
            dt.Columns.Add("String", Type.GetType("System.String"));
            dt.Columns.Add("Value", typeof(int));
            foreach (var a in arr)
            {
                string strText = EnumTextByDescription.GetEnumDesc((Mode)a);
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = (int)a;

                dt.Rows.Add(aRow);
            }

            fcalc.DataSource = dt;
            fcalc.DisplayMember = "String";
            fcalc.ValueMember = "Value";
        }

        void loadLayr()
        {
            string str = "select flogseq,fctlid from oril where fctlid_ri ='" + fctlid + "'";
            DataTable dt = DBHelper.GetDataSet(str);
            loc.DisplayMember = "flogseq";
            loc.ValueMember = "fctlid";
            loc.DataSource = dt;
        }

        void tabPage3reload()
        {
            loadLayr();
            string selorid1 = "select * from orild1 where fctlid_ri ='" + fctlid + "' and fid ='Facultative' ";
            DataTable orild1 = DBHelper.GetDataSet(selorid1);
            if (orild1 != null && orild1.Rows.Count > 0)
            {
                fshare.Text = orild1.Rows[0]["fshare"].ToString().Trim();
                fsi.Text = orild1.Rows[0]["fsi"].ToString().Trim();
                fgpm.Text = orild1.Rows[0]["fgpm"].ToString().Trim();
                fnpm.Text = orild1.Rows[0]["fnpm"].ToString().Trim();
            }

            string selorid2 = "select orild2.*,  b.fdesc as fbrkrD, c.fdesc as ReinsrD, CONVERT(varchar(20),flogseq) as [Seq#],freinsr as [Reinsurer], fshare as [Share%], " +
                "fsi as [RI SI/Limit], fgpm as [G Premium],fabsnet as [Abs Net] from orild2 left join (select fid,fdesc from "+Dbm+"mprdr where ftype='B') b on b.fid = orild2.fbrkr " +
               "left join (select fid,fdesc from "+Dbm+"mprdr where ftype='R') c on c.fid = orild2.freinsr where fctlid_ri ='" + fctlid + "' ";
            facuRein.DataSource = DBHelper.GetDataSet(selorid2);
            orild2 = DBHelper.GetDataSet(selorid2);
            if (facuRein.RowCount > 0) { facuRein.CurrentCell = facuRein.Rows[0].Cells["Seq#"]; }
            foreach (DataGridViewColumn Column in facuRein.Columns)
            {
                Column.Visible = false;
            }
            this.facuRein.Columns["Seq#"].Visible = true;
            this.facuRein.Columns["Reinsurer"].Visible = true;
            this.facuRein.Columns["Share%"].Visible = true;
            this.facuRein.Columns["RI SI/Limit"].Visible = true;
            this.facuRein.Columns["G Premium"].Visible = true;
            this.facuRein.Columns["Abs Net"].Visible = true;
            facuRein.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.facuRein_CellFormatting);

            dataSet.Tables.Clear();
            orild1.TableName = "orild1";
            dataSet.Tables.Add(orild1);
            orild2.TableName = "orild2";
            dataSet.Tables.Add(orild2);
            layrChag();
            RITotal();
        }

        public void FacMainTods()
        {
            dataSet.Tables["orild1"].Clear();
            foreach (DataRow dr in FacMain.Rows)
            {
                DataRow newRow = dataSet.Tables["orild1"].NewRow();
                newRow["fctlid_lc"] = dr["fctlid_lc"];
                newRow["fshare"] = dr["fshare"];
                newRow["fsi"] = dr["fsi"];
                newRow["fgpm"] = dr["fgpm"];
                newRow["fnpm"] = dr["fnpm"];
                newRow["fid"] = "Facultative";
                dataSet.Tables["orild1"].Rows.Add(newRow);
            }
            layrChag();
        }

        public void layrChag()
        {
            if (dataSet.Tables.Count > 0)
            {
                if (loc.SelectedValue != null && dataSet.Tables["orild1"].Rows.Count != 0)
                {
                    DataRow[] layrRow = dataSet.Tables["orild1"].Select("fctlid_lc ='" + loc.SelectedValue.ToString() + "' and fid ='Facultative'");
                    if (layrRow != null && layrRow.Length != 0)
                    {
                        fshare.Text = layrRow[0]["fshare"].ToString().Trim();
                        fsi.Text = layrRow[0]["fsi"].ToString().Trim();
                        fgpm.Text = layrRow[0]["fgpm"].ToString().Trim();
                        fnpm.Text = layrRow[0]["fnpm"].ToString().Trim();
                    }
                }
                if (loc.SelectedValue != null && dataSet.Tables["orild2"].Rows.Count != 0)
                {
                    DataRow[] dr = dataSet.Tables["orild2"].Select("fctlid_lc ='" + loc.SelectedValue.ToString() + "'");
                    if (dr != null && dr.Length != 0)
                    {
                        DataTable dtorild1 = dataSet.Tables["orild2"].Select("fctlid_lc ='" + loc.SelectedValue.ToString() + "'", "flogseq ASC").CopyToDataTable();
                        facuRein.DataSource = null;
                        facuRein.DataSource = dtorild1;
                        foreach (DataGridViewColumn Column in facuRein.Columns)
                        {
                            Column.Visible = false;
                        }
                        this.facuRein.Columns["Seq#"].Visible = true;
                        this.facuRein.Columns["Reinsurer"].Visible = true;
                        this.facuRein.Columns["Share%"].Visible = true;
                        this.facuRein.Columns["RI SI/Limit"].Visible = true;
                        this.facuRein.Columns["G Premium"].Visible = true;
                        this.facuRein.Columns["Abs Net"].Visible = true;
                        facuRein.CurrentCell = facuRein.Rows[0].Cells["Seq#"];
                        if (facuRein.RowCount > 0)
                        {
                            if (updateflag == true)
                            {
                                if (fcalc.SelectedIndex.ToString() == "0")
                                {
                                    share.ReadOnly = false;
                                    share.BackColor = SystemColors.Window;
                                }
                                if (fcalc.SelectedIndex.ToString() == "1")
                                {
                                    sum.ReadOnly = false;
                                    sum.BackColor = SystemColors.Window;
                                }
                                flogseq.ReadOnly = false;
                                flogseq.BackColor = SystemColors.Window;
                                freinsr.ReadOnly = false;
                                freinsr.BackColor = SystemColors.Window;
                                fbrkr.ReadOnly = false;
                                fbrkr.BackColor = SystemColors.Window;
                                fcom.ReadOnly = false;
                                fcom.BackColor = SystemColors.Window;
                                calc.Enabled = true;
                                calc.BackColor = SystemColors.Window;
                                ReinsrBt.Enabled = true;
                                BrokerBt.Enabled = true;
                            }
                        }
                    }
                    else
                    {
                        string sql = "select orild2.*,  b.fdesc as fbrkrD, c.fdesc as ReinsrD, CONVERT(varchar(20),flogseq) as [Seq#],freinsr as [Reinsurer], fshare as [Share%], " +
                                     "fsi as [RI SI/Limit], fgpm as [G Premium],fabsnet as [Abs Net] from orild2 left join (select fid,fdesc from "+Dbm+"mprdr where ftype='B') b on b.fid = orild2.fbrkr " +
                                     "left join (select fid,fdesc from "+Dbm+"mprdr where ftype='R') c on c.fid = orild2.freinsr where fctlid='1'";
                        facuRein.DataSource = DBHelper.GetDataSet(sql);
                        foreach (DataGridViewColumn Column in facuRein.Columns)
                        {
                            Column.Visible = false;
                        }
                        this.facuRein.Columns["Seq#"].Visible = true;
                        this.facuRein.Columns["Reinsurer"].Visible = true;
                        this.facuRein.Columns["Share%"].Visible = true;
                        this.facuRein.Columns["RI SI/Limit"].Visible = true;
                        this.facuRein.Columns["G Premium"].Visible = true;
                        this.facuRein.Columns["Abs Net"].Visible = true;
                        foreach (Control ctrl in panel1.Controls)
                        {
                            if (ctrl is TextBox)
                            {
                                ((TextBox)ctrl).Text = "";
                                ((TextBox)ctrl).ReadOnly = true;
                                ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                            }
                            if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                            if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
                        }
                    }
                }
            }
        }

        void RITotal()
        {
            Decimal Share = 0;
            for (int i = 0; i < facuRein.Rows.Count; i++)
            {
                if (facuRein["Share%", i].Value.ToString() != "")
                {
                    Share = Share + Convert.ToDecimal(facuRein["Share%", i].Value);
                }
            }
            fsharec.Text = Share.ToString("n2");

            Decimal SI = 0;
            for (int i = 0; i < facuRein.Rows.Count; i++)
            {
                if (facuRein["RI SI/Limit", i].Value.ToString() != "")
                {
                    SI = SI + Convert.ToDecimal(facuRein["RI SI/Limit", i].Value);
                }
            }
            fsic.Text = SI.ToString("n2");

            Decimal GP = 0;
            for (int i = 0; i < facuRein.Rows.Count; i++)
            {
                if (facuRein["G Premium", i].Value.ToString() != "")
                {
                    GP = GP + Convert.ToDecimal(facuRein["G Premium", i].Value);
                }
            }
            fgpmc.Text = GP.ToString("n2");

            Decimal NP = 0;
            for (int i = 0; i < facuRein.Rows.Count; i++)
            {
                if (facuRein["fnpm", i].Value.ToString() != "")
                {
                    NP = NP + Convert.ToDecimal(facuRein["fnpm", i].Value);
                }
            }
            fnpmc.Text = NP.ToString("n2");

            Decimal SI2 = 0;
            for (int i = 0; i < facuRein.Rows.Count; i++)
            {
                if (facuRein["fsi2", i].Value.ToString() != "")
                {
                    SI2 = SI2 + Convert.ToDecimal(facuRein["fsi2", i].Value);
                }
            }
            fsi2c.Text = SI2.ToString("n2");

            Decimal com = 0;
            for (int i = 0; i < facuRein.Rows.Count; i++)
            {
                if (facuRein["fcomamt", i].Value.ToString() != "")
                {
                    com = com + Convert.ToDecimal(facuRein["fcomamt", i].Value);
                }
            }
            ComamtTot = com.ToString("n2");

            Decimal net = 0;
            for (int i = 0; i < facuRein.Rows.Count; i++)
            {
                if (facuRein["Abs Net", i].Value.ToString() != "")
                {
                    net = net + Convert.ToDecimal(facuRein["Abs Net", i].Value);
                }
            }
            AbsnetTot = net.ToString("n2");
            ComCaculate();
        }

        public void ComCaculate()
        {
            if (loc.SelectedValue != null && dataSet.Tables.Count > 0)
            {
                foreach (var item in loc.Items)
                {
                    DataRowView row = item as DataRowView;
                    DataRow[] drs = dataSet.Tables["orild2"].Select("fctlid_lc ='" + row["fctlid"].ToString() + "'");
                    Decimal com = 0; Decimal net = 0;
                    foreach (DataRow dr in drs)
                    {
                        com = com + Convert.ToDecimal(dr["fcomamt"]);
                        ComamtTot = com.ToString("n2");

                        net = net + Convert.ToDecimal(dr["Abs Net"]);
                        AbsnetTot = net.ToString("n2");
                    }
                    if (ComMain.Select("fctlid_lc='" + row["fctlid"].ToString().Trim() + "'").Length > 0)
                    {
                        DataRow[] drs1 = ComMain.Select("fctlid_lc ='" + row["fctlid"].ToString() + "'");
                        foreach (DataRow dr1 in drs1)
                        {
                            dr1["fcomamt"] = ComamtTot; dr1["fabsnet"] = AbsnetTot;
                        }
                    }
                    else { ComMain.Rows.Add(row["fctlid"].ToString().Trim(), ComamtTot, AbsnetTot); }

                }
                Gen.ComMain = ComMain;
                Gen.facCaculate();
            }
        }

        private void facuRein_CellFormatting(object sender, System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            if (facuRein.Columns[e.ColumnIndex].Name.Equals("Share%") ||
                facuRein.Columns[e.ColumnIndex].Name.Equals("Abs Net") ||
                facuRein.Columns[e.ColumnIndex].Name.Equals("G Premium") ||
                facuRein.Columns[e.ColumnIndex].Name.Equals("RI SI/Limit"))
            {
                e.CellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                facuRein.Columns[e.ColumnIndex].AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
            }
            if (facuRein.Columns[e.ColumnIndex].Name.Equals("Share%"))
            {
                facuRein.Columns[e.ColumnIndex].DefaultCellStyle.Format = "N4";
            }
            if (facuRein.Columns[e.ColumnIndex].Name.Equals("Reinsurer"))
            {
                facuRein.Columns[e.ColumnIndex].Width = 80;
            }
        }

        void facuReinChange()
        {
            if (facuRein.CurrentCell != null)
            {
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["Seq#"].Value != null)
                {
                    flogseq.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["Seq#"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fcession"].Value != null)
                {
                    fcession.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fcession"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["friapp"].Value != null)
                {
                    friapp.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["friapp"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["Reinsurer"].Value != null)
                {
                    freinsr.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["Reinsurer"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["ReinsrD"].Value != null)
                {
                    textBox16.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["ReinsrD"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fbrkr"].Value != null)
                {
                    fbrkr.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fbrkr"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fbrkrD"].Value != null)
                {
                    textBox14.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fbrkrD"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["frinsrref"].Value != null)
                {
                    frinsrref.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["frinsrref"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["Share%"].Value != null)
                {
                    share.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["Share%"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["RI SI/Limit"].Value != null)
                {
                    sum.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["RI SI/Limit"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fsi2"].Value != null)
                {
                    tppd.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fsi2"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["G Premium"].Value != null)
                {
                    gpm.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["G Premium"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fnpm"].Value != null)
                {
                    npm.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fnpm"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fcomamt"].Value != null)
                {
                    fcomamt.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fcomamt"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fcom"].Value != null)
                {
                    fcom.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fcom"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["Abs Net"].Value != null)
                {
                    fabsnet.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["Abs Net"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fcalc"].Value != null)
                {
                    if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fcalc"].Value.ToString().Trim() == "1") { calc.SelectedItem = "Rate"; }
                    else { calc.SelectedItem = "Flat"; }
                }
                fmethod.SelectedItem = "Gross";
             }

        }

        public string CheckValue(string fid, string flag)
        {
            string str = "";
            if (flag == "Broker")
            {
                str = "select fid,fdesc from mprdr where ftype='B' and fid='" + fid + "'";
            }
            if (flag == "Reinsurer")
            {
                str = "select fid,fdesc from mprdr where ftype='R' and fid='" + fid + "'";
            }
            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["mdata"].ConnectionString);
            SqlCommand cmd = new SqlCommand(str, con);
            con.Open();
            using (SqlDataReader sdr = cmd.ExecuteReader())
            {
                if (sdr.HasRows && sdr.Read())
                {
                    string fdesc = sdr["fdesc"].ToString().Trim();
                    sdr.Close();
                    return fdesc;
                }
                else { return null; }
            }
        }

        void u_calrid2()
        {
            decimal ln_calshare = 0, ln_fshare = 0, ln_fsi = 0, ln_fsi2, ln_fgpm, ln_fnpm, ln_fcom = 0, ln_fcomamt = 0, ln_absnet = 0;
            decimal m_fsi = Math.Round(Convert.ToDecimal(fsi.Text) / Math.Round(Convert.ToDecimal(fshare.Text) / 100, 4, MidpointRounding.AwayFromZero), 2, MidpointRounding.AwayFromZero);
            decimal m_fgpm = Math.Round(Convert.ToDecimal(fgpm.Text) / Math.Round(Convert.ToDecimal(fshare.Text) / 100, 4, MidpointRounding.AwayFromZero), 2, MidpointRounding.AwayFromZero);
            decimal m_fnpm = Math.Round(Convert.ToDecimal(fnpm.Text) / Math.Round(Convert.ToDecimal(fshare.Text) / 100, 4, MidpointRounding.AwayFromZero), 2, MidpointRounding.AwayFromZero);
            if (fcalc.Text.ToString() == "Share%")
            {
                ln_fshare = Convert.ToDecimal(share.Text);
                ln_calshare = Convert.ToDecimal(share.Text);
                ln_fsi = Math.Round(m_fsi * ln_fshare / 100, 2, MidpointRounding.AwayFromZero);
            }
            if (fcalc.Text.ToString() == "SI/ Limit")
            {
                ln_fsi = Convert.ToDecimal(sum.Text);
                ln_fshare = Math.Round(ln_fsi / m_fsi, 4, MidpointRounding.AwayFromZero);
                ln_calshare = ln_fsi / m_fsi * 100;
            }
            ln_fgpm = Math.Round(m_fgpm * ln_calshare / 100, 2, MidpointRounding.AwayFromZero);
            ln_fnpm = Math.Round(m_fnpm * ln_calshare / 100, 2, MidpointRounding.AwayFromZero);

            if (calc.SelectedItem.ToString() == "Rate")
            {
                ln_fcom = Convert.ToDecimal(fcom.Text);
                ln_fcomamt = Math.Round(ln_fcom * ln_fgpm / 100, 2, MidpointRounding.AwayFromZero);
            }
            if (calc.SelectedItem.ToString() == "Flat")
            {
                ln_fcomamt = Convert.ToDecimal(fcomamt.Text);
                ln_fcom = Math.Round(ln_fcomamt / ln_fgpm * 100, 4, MidpointRounding.AwayFromZero);
            }
            ln_absnet = ln_fgpm - ln_fcomamt;
            int row = 0;
            if (facuRein.CurrentCell != null) { row = facuRein.CurrentCell.RowIndex; }
            facuRein["Share%", row].Value = ln_fshare;
            facuRein["RI SI/Limit", row].Value = ln_fsi;
            facuRein["G Premium", row].Value = ln_fgpm;
            facuRein["fnpm", row].Value = ln_fnpm;
            facuRein["Abs Net", row].Value = ln_absnet;
            facuRein["fcomamt", row].Value = ln_fcomamt;
            facuRein["fcom", row].Value = ln_fcom;
            DataRow[] dtRow = dataSet.Tables["orild2"].Select("fctlid_lc = '" + loc.SelectedValue.ToString() + "'");
            if (dtRow.Length > 0)
            {
                dtRow[row]["Share%"] = ln_fshare;
                dtRow[row]["RI SI/Limit"] = ln_fsi;
                dtRow[row]["G Premium"] = ln_fgpm;
                dtRow[row]["Abs Net"] = ln_absnet;
                dtRow[row]["fcomamt"] = ln_fcomamt;
                dtRow[row]["fcom"] = ln_fcom;
                dtRow[row]["fnpm"] = ln_fnpm;
            }
        }

        private void share_Validated(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && !Skip_Validate)
            {
                u_calrid2();
                facuReinChange();
                RITotal();
            }
        }

        private void fcom_Validated(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && !Skip_Validate)
            {
                u_calrid2();
                facuReinChange();
                RITotal();
            }
        }

        private void fcomamt_Validated(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && !Skip_Validate)
            {
                u_calrid2();
                facuReinChange();
                RITotal();
            }
        }

        private void freinsr_Validated(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && !Skip_Validate)
            {
                facuRein["Reinsurer", facuRein.CurrentCell.RowIndex].Value = freinsr.Text;
                DataRow[] dtRow = dataSet.Tables["orild2"].Select("fctlid_lc = '" + loc.SelectedValue.ToString() + "' and flogseq ='"+flogseq.Text +"'");
                if (dtRow.Length > 0)
                {
                    dtRow[0]["Reinsurer"] = freinsr.Text.ToString();
                }
                string desc = CheckValue(freinsr.Text.ToString(), "Reinsurer");
                if (desc == null && freinsr.Text != "")
                {
                    freinsr.Focus();
                    MessageBox.Show("Invalid Value");
                }
                else
                {
                    textBox16.Text = desc;
                    facuRein["ReinsrD", facuRein.CurrentCell.RowIndex].Value = textBox16.Text;
                    if (dtRow.Length > 0)
                    {
                        dtRow[0]["ReinsrD"] = textBox16.Text.ToString();
                    }
                }
            }
        }

        private void fbrkr_Validated(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && !Skip_Validate)
            {
                facuRein["fbrkr", facuRein.CurrentCell.RowIndex].Value = fbrkr.Text;
                DataRow[] dtRow = dataSet.Tables["orild2"].Select("fctlid_lc = '" + loc.SelectedValue.ToString() + "' and flogseq ='" + flogseq.Text + "'");
                if (dtRow.Length > 0)
                {
                    dtRow[0]["fbrkr"] = fbrkr.Text.ToString();
                }
                string desc = CheckValue(fbrkr.Text.ToString(), "Broker");
                if (desc == null && fbrkr.Text != "")
                {
                    fbrkr.Focus();
                    MessageBox.Show("Invalid Value");
                }
                else
                {
                    textBox14.Text = desc;
                    facuRein["fbrkrD", facuRein.CurrentCell.RowIndex].Value = textBox14.Text;
                    if (dtRow.Length > 0)
                    {
                        dtRow[0]["fbrkrD"] = textBox14.Text.ToString();
                    }
                }
            }
        }

        private void flogseq_Validated(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && !Skip_Validate)
            {
                if (flogseq.Text == "") { flogseq.Focus(); }
                else
                {
                    facuRein["Seq#", facuRein.CurrentCell.RowIndex].Value = flogseq.Text;
                    DataRow[] dtRow = dataSet.Tables["orild2"].Select("fctlid_lc = '" + loc.SelectedValue.ToString() + "'");
                    if (dtRow.Length > 0)
                    {
                        dtRow[facuRein.CurrentCell.RowIndex]["Seq#"] = flogseq.Text.ToString();
                    }
                }
            }
        }

        private void freinsr_TextChanged(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && !Skip_Validate)
            {
                facuRein["Reinsurer", facuRein.CurrentCell.RowIndex].Value = freinsr.Text;
                DataRow[] dtRow = dataSet.Tables["orild2"].Select("fctlid_lc = '" + loc.SelectedValue.ToString() + "' and flogseq ='"+flogseq.Text +"'");
                if (dtRow.Length > 0)
                {
                    dtRow[0]["Reinsurer"] = freinsr.Text.ToString();
                }
            }
        }

        private void fbrkr_TextChanged(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && !Skip_Validate)
            {
                facuRein["fbrkr", facuRein.CurrentCell.RowIndex].Value = fbrkr.Text;
                DataRow[] dtRow = dataSet.Tables["orild2"].Select("fctlid_lc = '" + loc.SelectedValue.ToString() + "' and flogseq ='" + flogseq.Text + "'");
                if (dtRow.Length > 0)
                {
                    dtRow[0]["fbrkr"] = fbrkr.Text.ToString();
                }
            }
        }

        private void textBox16_TextChanged(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && !Skip_Validate)
            {
                facuRein["ReinsrD", facuRein.CurrentCell.RowIndex].Value = textBox16.Text;
                DataRow[] dtRow = dataSet.Tables["orild2"].Select("fctlid_lc = '" + loc.SelectedValue.ToString() + "'");
                if (dtRow.Length > 0)
                {
                    dtRow[facuRein.CurrentCell.RowIndex]["ReinsrD"] = textBox16.Text.ToString();
                }
            }
        }

        private void textBox14_TextChanged(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && !Skip_Validate)
            {
                facuRein["fbrkrD", facuRein.CurrentCell.RowIndex].Value = textBox14.Text;
                DataRow[] dtRow = dataSet.Tables["orild2"].Select("fctlid_lc = '" + loc.SelectedValue.ToString() + "'");
                if (dtRow.Length > 0)
                {
                    dtRow[facuRein.CurrentCell.RowIndex]["fbrkrD"] = textBox14.Text.ToString();
                }
            }
        }


        private void BrokerBt_Click(object sender, EventArgs e)
        {
            frmBrokerSearch tempform = new frmBrokerSearch(this);
            tempform.flag = "BrokerPar";
            tempform.FillData("", "");
            tempform.ShowDialog();
        }

        private void ReinsrBt_Click(object sender, EventArgs e)
        {
            frmReinsurerSearch tempform = new frmReinsurerSearch(this);
            tempform.flag = "ReinsPar";
            tempform.FillData("", "");
            tempform.ShowDialog();
        }

        public void buttoncontrolback()
        {
            updateflag = false;
            add.Visible = false;
            del.Visible = false;
            appri.Visible = true;
            foreach (Control ctrl in panel1.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = true;
                    ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
            }
            tabPage3reload();
        }

        public void buttoncontrolupdate()
        {
            updateflag = true;
            InitCombobox();
            tabPage3update();
        }

        void tabPage3update()
        {
            tabPage3reload();
            add.Visible = true;
            del.Visible = true;
            appri.Visible = false;
            if (facuRein.DataSource != null && facuRein.RowCount > 0)
            { facuRein.CurrentCell = facuRein.Rows[0].Cells["Seq#"]; }
            if (facuRein.RowCount > 0)
            {
                if (fcalc.SelectedIndex.ToString() == "0")
                {
                    share.ReadOnly = false;
                    share.BackColor = SystemColors.Window;
                }
                if (fcalc.SelectedIndex.ToString() == "1")
                {
                    sum.ReadOnly = false;
                    sum.BackColor = SystemColors.Window;
                }
                flogseq.ReadOnly = false;
                flogseq.BackColor = SystemColors.Window;
                freinsr.ReadOnly = false;
                freinsr.BackColor = SystemColors.Window;
                fbrkr.ReadOnly = false;
                fbrkr.BackColor = SystemColors.Window;
                fcom.ReadOnly = false;
                fcom.BackColor = SystemColors.Window;
                calc.Enabled = true;
                calc.BackColor = SystemColors.Window;
                ReinsrBt.Enabled = true;
                BrokerBt.Enabled = true;
            }
        }

        private void calc_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && !Skip_Validate)
            {
                if (calc.SelectedItem.ToString() == "Rate")
                {
                    facuRein["fcalc", facuRein.CurrentCell.RowIndex].Value = "1";
                    DataRow[] dtRow = dataSet.Tables["orild2"].Select("fctlid_lc = '" + loc.SelectedValue.ToString() + "'");
                    if (dtRow.Length > 0)
                    {
                        dtRow[facuRein.CurrentCell.RowIndex]["fcalc"] = "1";
                    }
                    fcom.ReadOnly = false;
                    fcom.BackColor = SystemColors.Window;
                    fcomamt.ReadOnly = true;
                    fcomamt.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (calc.SelectedItem.ToString() == "Flat")
                {
                    facuRein["fcalc", facuRein.CurrentCell.RowIndex].Value = "2";
                    DataRow[] dtRow = dataSet.Tables["orild2"].Select("fctlid_lc = '" + loc.SelectedValue.ToString() + "'");
                    if (dtRow.Length > 0)
                    {
                        dtRow[facuRein.CurrentCell.RowIndex]["fcalc"] = "2";
                    }
                    fcomamt.ReadOnly = false;
                    fcomamt.BackColor = SystemColors.Window;
                    fcom.ReadOnly = true;
                    fcom.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
            }
        }

        private void fcalc_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && !Skip_Validate)
            {
                if (fcalc.Text.ToString() == "Share%")
                {
                    share.ReadOnly = false;
                    share.BackColor = SystemColors.Window;
                    sum.ReadOnly = true;
                    sum.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (fcalc.Text.ToString() == "SI/ Limit")
                {
                    sum.ReadOnly = false;
                    sum.BackColor = SystemColors.Window;
                    share.ReadOnly = true;
                    share.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
            }
        }

        private void add_Click(object sender, EventArgs e)
        {
            //string[] row1 = new string[] { "", fctlid, loc.SelectedValue.ToString()};
            //row1["flogseq"] = (facuRein.Rows.Count +1).ToString();
            //dataSet.Tables["orild2"].Rows.Add(row1);

            DataRow newlayrRow = dataSet.Tables["orild2"].NewRow();
            newlayrRow["flogseq"] = (facuRein.Rows.Count + 1).ToString();
            newlayrRow["fctlid_lc"] = loc.SelectedValue.ToString();
            newlayrRow["fctlid_ri"] = fctlid;
            newlayrRow["fbrkr"] = fbrkr.Text;
            newlayrRow["fbrkrD"] = textBox14.Text;
            dataSet.Tables["orild2"].Rows.Add(newlayrRow);
            facuRein.DataSource = dataSet.Tables["orild2"].Select("fctlid_lc ='" + loc.SelectedValue + "'").CopyToDataTable();
            facuRein.CurrentCell = facuRein.Rows[facuRein.RowCount - 1].Cells["Seq#"];

            if (fcalc.Text.ToString() == "Share%")
            {
                share.ReadOnly = false;
                share.BackColor = SystemColors.Window;
            }
            if (fcalc.Text.ToString() == "SI/ Limit")
            {
                sum.ReadOnly = false;
                sum.BackColor = SystemColors.Window;
            }
            flogseq.Text = facuRein.Rows.Count.ToString();
            facuRein["Seq#", facuRein.CurrentCell.RowIndex].Value = flogseq.Text;
            if (facuRein.CurrentCell.RowIndex != 0)
            {
                facuRein["fbrkr", facuRein.CurrentCell.RowIndex].Value = facuRein["fbrkr", facuRein.CurrentCell.RowIndex - 1].Value;
                facuRein["fbrkrD", facuRein.CurrentCell.RowIndex].Value = facuRein["fbrkrD", facuRein.CurrentCell.RowIndex - 1].Value;
                fbrkr.Text = facuRein["fbrkr", facuRein.CurrentCell.RowIndex - 1].Value.ToString();
            }
            flogseq.ReadOnly = false;
            flogseq.BackColor = SystemColors.Window;
            freinsr.ReadOnly = false;
            freinsr.BackColor = SystemColors.Window;
            fbrkr.ReadOnly = false;
            fbrkr.BackColor = SystemColors.Window;
            fcom.ReadOnly = false;
            fcom.BackColor = SystemColors.Window;
            calc.Enabled = true;
            calc.BackColor = SystemColors.Window;
            ReinsrBt.Enabled = true;
            BrokerBt.Enabled = true;
            facuReinChange();
            calc.SelectedItem = "Rate";
            setControlFocus("flogseq");
        }


        private void del_Click(object sender, EventArgs e)
        {
            if (facuRein.RowCount != 0)
            {
                try
                {
                    DataRow[] drr = dataSet.Tables["orild2"].Select("fctlid_lc ='" + loc.SelectedValue.ToString() + "'");
                    dataSet.Tables["orild2"].Rows.Remove(drr[facuRein.CurrentCell.RowIndex]);
                    facuRein.DataSource = dataSet.Tables["orild2"];
                    layrChag();
                }
                catch { }
            }
            if (facuRein.RowCount == 0)
            {
                foreach (Control ctrl in panel1.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                    if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                    if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
                }
            }
        }

        public string u_valrinsr()
        {
            if (fcalc.Text.ToString() == "Share%" && Convert.ToDecimal(fsharec.Text) != Convert.ToDecimal(fshare.Text))
            {
                MessageBox.Show("Invalid Total Share(Facultative))!", "Warning",
                     MessageBoxButtons.OK, MessageBoxIcon.Information);
                return "fsharec";
            }
            if (fcalc.Text.ToString() == "SI/ Limit" && Convert.ToDecimal(fsic.Text) != Convert.ToDecimal(fsi.Text))
            {
                MessageBox.Show("Invalid Total SI(Facultative)!", "Warning",
                     MessageBoxButtons.OK, MessageBoxIcon.Information);
                return "fsic";
            }
            foreach (var item in loc.Items)
            {
                decimal m_fsi = 0, m_fshare = 0;
                DataRowView row = item as DataRowView;
                if (dataSet.Tables.Count > 0) { 
                DataRow[] drs = dataSet.Tables["orild2"].Select("fctlid_lc ='" + row["fctlid"].ToString() + "'");
                DataRow[] orild1drs = dataSet.Tables["orild1"].Select("fctlid_lc ='" + row["fctlid"].ToString() + "'");
                foreach (DataRow dr in orild1drs)
                {
                    m_fsi = Convert.ToDecimal(dr["fsi"].ToString());
                    m_fshare = Convert.ToDecimal(dr["fshare"].ToString());
                }
                foreach (DataRow dr in drs)
                {
                    if (dr["Seq#"].ToString() == "")
                    {
                        MessageBox.Show("Empty Value is not allowed!", "Warning",
                         MessageBoxButtons.OK, MessageBoxIcon.Information);
                        flogseq.Focus();
                        return "flogseq";
                    }
                    string desc = CheckValue(dr["freinsr"].ToString(), "Reinsurer");
                    if (desc == null && dr["freinsr"].ToString() != "")
                    {
                        MessageBox.Show("Invalid Value");
                        freinsr.Focus();
                        return "freinsr";
                    }
                    if (Convert.ToDecimal(dr["fcom"].ToString()) < 0)
                    {
                        MessageBox.Show("Invalid Value");
                        fcom.Focus();
                        return "fcom";
                    }
                    string desc1 = CheckValue(dr["fbrkr"].ToString(), "Broker");
                    if (desc1 == null && dr["fbrkr"].ToString() != "")
                    {
                        MessageBox.Show("Invalid Value");
                        fbrkr.Focus(); return "fbrkr";
                    }
                    if (fcalc.Text.ToString() == "Share%" && (Convert.ToDecimal(dr["Share%"].ToString()) < 0 || Convert.ToDecimal(dr["Share%"].ToString()) > m_fshare))
                    {
                        MessageBox.Show("Invalid Value!", "Warning",
                         MessageBoxButtons.OK, MessageBoxIcon.Information);
                        share.Focus();
                        return "share";
                    }
                    int ln_result = 0;
                    if (fcalc.Text.ToString() == "SI/ Limit")
                    {
                        if (Convert.ToDecimal(fsi.Text) >= 0 && Convert.ToDecimal(dr["RI SI/Limit"].ToString()) >= 0 && Convert.ToDecimal(dr["RI SI/Limit"].ToString()) <= m_fsi)
                        {
                            ln_result = 1;
                        }
                        if (Convert.ToDecimal(fsi.Text) <= 0 && Convert.ToDecimal(dr["RI SI/Limit"].ToString()) <= 0 && Convert.ToDecimal(dr["RI SI/Limit"].ToString()) >= m_fsi)
                        {
                            ln_result = 1;
                        }
                        if (ln_result == 0)
                        {
                            MessageBox.Show("Invalid Value!", "Warning",
                                 MessageBoxButtons.OK, MessageBoxIcon.Information);
                            sum.Focus();
                            return "sum";
                        }
                    }

                    decimal ln_base = Convert.ToDecimal(dr["G Premium"].ToString());
                    decimal ln_fcomamt = Convert.ToDecimal(dr["fcomamt"].ToString());
                    if ((ln_base > 0 && (ln_fcomamt < 0 || ln_fcomamt > ln_base)) || (ln_base < 0 && (ln_fcomamt > 0 || ln_fcomamt < ln_base)))
                    {
                        MessageBox.Show("Invalid Value!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        fcomamt.Focus();
                        return "fcomamt";
                    }
                }
                }
            }
            return "";
        }

        public void u_calallrid2()
        {
            if (fshare.Text != "0.0000")
            {
                decimal ln_calshare = 0, ln_fshare = 0, ln_fsi = 0, ln_fsi2 = 0, ln_fgpm = 0, ln_fnpm = 0, ln_fcomamt = 0, ln_fabsnet = 0;
                foreach (var item in loc.Items)
                {
                    DataRowView row = item as DataRowView;
                    DataRow[] layrRow = dataSet.Tables["orild1"].Select("fctlid_lc ='" + row["fctlid"].ToString() + "'");
                    if (layrRow != null && layrRow.Length != 0)
                    {
                        fshare.Text = layrRow[0]["fshare"].ToString().Trim();
                        fsi.Text = layrRow[0]["fsi"].ToString().Trim();
                        fgpm.Text = layrRow[0]["fgpm"].ToString().Trim();
                        fnpm.Text = layrRow[0]["fnpm"].ToString().Trim();
                    }
                    decimal m_fsi = Math.Round(Convert.ToDecimal(fsi.Text) / Math.Round(Convert.ToDecimal(fshare.Text) / 100, 4, MidpointRounding.AwayFromZero), 2, MidpointRounding.AwayFromZero);
                    decimal m_fgpm = Math.Round(Convert.ToDecimal(fgpm.Text) / Math.Round(Convert.ToDecimal(fshare.Text) / 100, 4, MidpointRounding.AwayFromZero), 2, MidpointRounding.AwayFromZero);
                    decimal m_fnpm = Math.Round(Convert.ToDecimal(fnpm.Text) / Math.Round(Convert.ToDecimal(fshare.Text) / 100, 4, MidpointRounding.AwayFromZero), 2, MidpointRounding.AwayFromZero);
                    decimal trid1_fshare = Convert.ToDecimal(fshare.Text);
                    decimal trid1_fsi = Convert.ToDecimal(fsi.Text);
                    decimal trid1_fgpm = Convert.ToDecimal(fgpm.Text);
                    decimal trid1_fnpm = Convert.ToDecimal(fnpm.Text);
                    decimal ln_ttfshare = 0, ln_ttfsi = 0, ln_ttfsi2 = 0, ln_ttfgpm = 0, ln_ttfnpm = 0, ln_ttfcomamt = 0, ln_ttfabsnet = 0;
                    decimal ln_otfshare = 0, ln_otfsi = 0, ln_otfsi2 = 0, ln_otfgpm = 0, ln_otfnpm = 0;
                    DataRow[] drs = dataSet.Tables["orild2"].Select("fctlid_lc ='" + row["fctlid"].ToString() + "'");
                    foreach (DataRow dr in drs)
                    {
                        string ln_fgrnet = dr["fgrnet"].ToString();
                        decimal ln_fcom = Convert.ToDecimal(dr["fcom"]);
                        if (fcalc.Text.ToString() == "Share%")
                        {
                            ln_fshare = Convert.ToDecimal(dr["Share%"]);
                            ln_calshare = Convert.ToDecimal(dr["Share%"]);
                            ln_fsi = Math.Round(m_fsi * ln_fshare / 100, 2, MidpointRounding.AwayFromZero);
                        }
                        else
                        {
                            ln_fsi = Convert.ToDecimal(dr["RI SI/Limit"]);
                            ln_fshare = Math.Round(ln_fsi / m_fsi * 100, 4, MidpointRounding.AwayFromZero);
                            ln_calshare = ln_fsi / m_fsi * 100;
                        }
                        ln_fgpm = Math.Round(m_fgpm * ln_calshare / 100, 2, MidpointRounding.AwayFromZero);
                        ln_fnpm = Math.Round(m_fnpm * ln_calshare / 100, 2, MidpointRounding.AwayFromZero);
                        if (calc.SelectedItem.ToString() == "Rate")
                        {
                            ln_fcom = Convert.ToDecimal(dr["fcom"]);
                            ln_fcomamt = Math.Round(ln_fcom * ln_fgpm / 100, 2, MidpointRounding.AwayFromZero);
                        }
                        if (calc.SelectedItem.ToString() == "Flat")
                        {
                            ln_fcomamt = Convert.ToDecimal(dr["fcomamt"]);
                            ln_fcom = Math.Round(ln_fcomamt / ln_fgpm * 100, 4, MidpointRounding.AwayFromZero);
                        }
                        ln_fabsnet = ln_fgpm - ln_fcomamt;

                        ln_ttfshare = ln_ttfshare + ln_fshare;
                        ln_ttfsi = ln_ttfsi + ln_fsi;
                        ln_ttfsi2 = ln_ttfsi2 + ln_ttfsi2;
                        ln_ttfgpm = ln_ttfgpm + ln_fgpm;
                        ln_ttfnpm = ln_ttfnpm + ln_fnpm;
                        ln_ttfcomamt = ln_ttfcomamt + ln_fcomamt;
                        ln_ttfabsnet = ln_ttfabsnet + ln_fabsnet;

                        if (dr["Seq#"].ToString() != "1")
                        {
                            ln_otfshare = ln_otfshare + ln_fshare;
                            ln_otfsi = ln_otfsi + ln_fsi;
                            ln_ttfsi2 = ln_ttfsi2 + ln_fsi2;
                            ln_otfgpm = ln_otfgpm + ln_fgpm;
                            ln_otfnpm = ln_otfnpm + ln_fnpm;
                        }
                        dr["Share%"] = ln_fshare;
                        dr["RI SI/Limit"] = ln_fsi;
                        dr["fsi2"] = ln_fsi2;
                        dr["G Premium"] = ln_fgpm;
                        dr["fnpm"] = ln_fnpm;
                        dr["Abs Net"] = ln_fabsnet;
                        dr["fcomamt"] = ln_fcomamt;
                        dr["fcom"] = ln_fcom;
                        dr["fupdsi"] = ln_fsi;
                        dr["fupdsi2"] = ln_fsi2;
                        dr["fupdshare"] = ln_fshare;
                    }
                    if ((fcalc.Text.ToString() == "Share%" && ln_ttfshare == trid1_fshare) || (fcalc.Text.ToString() == "SI/ Limit" && ln_ttfsi == trid1_fsi))
                    {
                        foreach (DataRow dr in drs)
                        {
                            if (dr["Seq#"].ToString() == "1")
                            {
                                ln_fcomamt = Convert.ToDecimal(dr["fcomamt"]);
                                dr["Share%"] = trid1_fshare - ln_otfshare;
                                dr["RI SI/Limit"] = trid1_fsi - ln_otfsi;
                                dr["G Premium"] = trid1_fgpm - ln_otfgpm;
                                dr["fnpm"] = trid1_fnpm - ln_otfnpm;
                                dr["fupdsi"] = trid1_fsi - ln_otfsi;
                                dr["fupdshare"] = trid1_fshare - ln_otfshare;
                                dr["Abs Net"] = trid1_fgpm - ln_otfgpm - ln_fcomamt;
                            }
                        }
                    }
                }
            }
        }

        public string[] tabpage3save()
        {
            string sql = "";
            ArrayList addsql = new ArrayList();
            DataTable dt = dataSet.Tables["orild2"];
            if (dt != null)
            {
                string fctlid_orid2 = Fct.NewId("orild2");
                string fkind = "", fbus = "", fctlid_e = "", fctlid_ri = "";
                string selorid1 = "select * from orih where fctlid ='" + fctlid + "'";
                DataTable dtorid1 = DBHelper.GetDataSet(selorid1);
                if (dtorid1 != null)
                {
                    if (dtorid1.Rows.Count != 0)
                    {
                        fbus = dtorid1.Rows[0]["fbus"].ToString();
                        fkind = dtorid1.Rows[0]["fkind"].ToString();
                        fclass = dtorid1.Rows[0]["fclass"].ToString();
                        fctlid_e = dtorid1.Rows[0]["fctlid_1"].ToString();
                        fctlid_ri = dtorid1.Rows[0]["fctlid"].ToString();
                    }
                }
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    decimal ln_fcom, ln_fcomamt, ln_fabsnet, ln_fshare, ln_fsi, ln_fsi2, ln_fgpm, ln_fnpm, ln_fupdshare, ln_fupdsi, ln_fupdsi2;
                    ln_fcom = Fct.Refat(dt.Rows[i]["fcom"].ToString());
                    ln_fcomamt = Fct.Refat(dt.Rows[i]["fcomamt"].ToString());
                    ln_fabsnet = Fct.Refat(dt.Rows[i]["Abs Net"].ToString());
                    ln_fshare = Fct.Refat(dt.Rows[i]["Share%"].ToString());
                    ln_fsi = Fct.Refat(dt.Rows[i]["RI SI/Limit"].ToString());
                    ln_fsi2 = Fct.Refat(dt.Rows[i]["fsi2"].ToString());
                    ln_fgpm = Fct.Refat(dt.Rows[i]["G Premium"].ToString());
                    ln_fnpm = Fct.Refat(dt.Rows[i]["fnpm"].ToString());

                    ln_fupdshare = Fct.Refat(dt.Rows[i]["fupdshare"].ToString());
                    ln_fupdsi = Fct.Refat(dt.Rows[i]["fupdsi"].ToString());
                    ln_fupdsi2 = Fct.Refat(dt.Rows[i]["fupdsi2"].ToString());
                    string fctlid_lc = dt.Rows[i]["fctlid_lc"].ToString();
                    string ln_freinsr = dt.Rows[i]["Reinsurer"].ToString();
                    string ln_fbrkr = dt.Rows[i]["fbrkr"].ToString();
                    string ln_frinsrref = "", ln_fcession = "", ln_friapp = "", ln_fmethod = "1", ln_fcalc = "";
                    if (dt.Rows[i]["fcalc"].ToString() == "1") { ln_fcalc = "1"; } else { ln_fcalc = "2"; }
                    int ln_flogseq = int.Parse(dt.Rows[i]["Seq#"].ToString());

                    if (dt.Rows[i]["fctlid"].ToString().Trim().Length < 9)
                    {
                        if (i != 0) { fctlid_orid2 = (int.Parse(fctlid_orid2) + 1).ToString().PadLeft(10, '0'); }
                        sql = "insert into orild2 ([fctlid_e],[fctlid_ri],[fctlid_lc],[fbus],[fitem],[fctlid],[fkind],[fritype],[fsec],[flogseq],[freinsr],[fbrkr],[fcom],[fgrnet],[fbrkge],[fgrnet2],[fcomamt],[fbrkgeamt],[fmiscamt1],[fmiscamt2],[fmiscamt3],[fmiscamt],[fabsnet],[fsi],[fsi2],[fupdsi],[fupdsi2],[fosi],[fosi2],[fgpm],[fnpm],[fshare],[fupdshare],[foshare],[fcession],[friapp],[frinsrref],[fcalc],[fmethod],[fclass],[floscode],[fppl]) VALUES " +
                          " ('" + fctlid_e + "','" + fctlid_ri + "','" + fctlid_lc + "','" + fbus + "',0,'" + fctlid_orid2 + "','" + fkind + "','04','" + fsec + "','" + ln_flogseq + "','" + ln_freinsr + "','" + ln_fbrkr + "','" + ln_fcom + "','1',0,'1','" + ln_fcomamt + "',0,0,0,0,0,'" + ln_fabsnet + "','" + ln_fsi + "','" + ln_fsi2 + "','" + ln_fupdsi + "','" + ln_fupdsi2 + "','" + ln_fupdsi + "','" + ln_fupdsi2 + "', " +
                        "'" + ln_fgpm + "','" + ln_fnpm + "','" + ln_fshare + "','" + ln_fupdshare + "','" + ln_fupdshare + "','" + ln_fcession + "','" + ln_friapp + "','" + ln_frinsrref + "','" + ln_fcalc + "','" + ln_fmethod + "','" + fclass + "','','1')";

                        addsql.Add(sql);
                    }
                    else
                    {
                        sql = "update orild2 SET [fcom] = '" + ln_fcom + "',[fcomamt] = '" + ln_fcomamt + "',[fabsnet] = '" + ln_fabsnet + "',[fshare] = '" + ln_fshare + "',[fsi] = '" + ln_fsi + "',[fsi2] = '" + ln_fsi2 + "',[fupdshare] = '" + ln_fupdshare + "',[fupdsi] = '" + ln_fupdsi + "',[fupdsi2] = '" + ln_fupdsi2 + "',[foshare] = '" + ln_fupdshare + "',[fosi] ='" + ln_fupdsi + "',[fosi2] = '" + ln_fupdsi2 + "',[fgpm] = '" + ln_fgpm + "',[fnpm] = '" + ln_fnpm + "',[freinsr] = '" + ln_freinsr + "' ,[fbrkr] = '" + ln_fbrkr + "' WHERE fctlid = '" + dt.Rows[i]["fctlid"].ToString() + "'";
                        addsql.Add(sql);
                    }
                    //check delete
                    string selorid2 = "select orild2.*,  b.fdesc as fbrkrD, c.fdesc as ReinsrD, CONVERT(varchar(20),flogseq) as [Seq#],freinsr as [Reinsurer], fshare as [Share%], " +
                    "fsi as [RI SI/Limit], fgpm as [G Premium],fabsnet as [Abs Net] from orild2 left join (select fid,fdesc from "+Dbm+"mprdr where ftype='B') b on b.fid = orild2.fbrkr " +
                    "left join (select fid,fdesc from "+Dbm+"mprdr where ftype='R') c on c.fid = orild2.freinsr where fctlid_ri ='" + fctlid + "'";
                    DataTable orid2Org = DBHelper.GetDataSet(selorid2);
                    if (orid2Org != null && orid2Org.Rows.Count > 0)
                    {
                        string[] sqlorid2 = CompareDt(orid2Org, dt, "fctlid", "orild2");
                        if (sqlorid2 != null)
                        {
                            for (int p = 0; p < sqlorid2.Length; p++)
                            { addsql.Add(sqlorid2[p]); }
                        }
                    }
                }
                if (fctlid_orid2 != "")
                {
                    string updfctlid = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_orid2) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='orild2'";
                    addsql.Add(updfctlid);
                }
            }
            return (string[])addsql.ToArray(typeof(string));
        }

        public string[] CompareDt(DataTable dt1, DataTable dt2, string keyField, string flag)
        {
            //为三个表拷贝表结构
            DataTable dtRetDel = dt1.Clone();
            DataTable dtRetAdd = dt1.Clone();
            DataTable dtRetDif = dt1.Clone();

            ArrayList updsql = new ArrayList();
            int colCount = dt1.Columns.Count;

            DataView dv1 = dt1.DefaultView;
            DataView dv2 = dt2.DefaultView;

            //先以第一个表为参照，看第二个表是修改了还是删除了
            foreach (DataRowView dr1 in dv1)
            {
                dv2.RowFilter = keyField + " = '" + dr1[keyField].ToString() + "'";
                if (dv2.Count > 0)
                {
                    if (!CompareUpdate(dr1, dv2[0]))//比较是否有不同的
                    {
                        dtRetDif.Rows.Add(dv2[0].Row.ItemArray);//修改后
                        continue;
                    }
                }
                else
                {
                    //已经被删除的
                    dtRetDel.Rows.Add(dr1.Row.ItemArray);
                }
            }

            //以第一个表为参照，看记录是否是新增的
            dv2.RowFilter = "";//清空条件
            foreach (DataRowView dr2 in dv2)
            {
                dv1.RowFilter = keyField + " = '" + dr2[keyField].ToString() + "'";
                if (dv1.Count == 0)
                {
                    //新增的
                    dtRetAdd.Rows.Add(dr2.Row.ItemArray);
                }
            }

            if (dtRetDel != null && dtRetDel.Rows.Count > 0)
            {
                for (int i = 0; i < dtRetDel.Rows.Count; i++)
                {
                    string del = "delete from orild2 where fctlid='" + dtRetDel.Rows[i]["fctlid"].ToString().Trim() + "'";
                    updsql.Add(del);
                }
            }

            return (string[])updsql.ToArray(typeof(string));
        }

        //比较是否有不同的
        private static bool CompareUpdate(DataRowView dr1, DataRowView dr2)
        {
            //行里只要有一项不一样，整个行就不一样,无需比较其它
            object val1;
            object val2;
            for (int i = 0; i < dr1.Row.ItemArray.Length; i++)
            {
                val1 = dr1[i];
                val2 = dr2[i];
                if (!val1.Equals(val2))
                {
                    return false;
                }
            }
            return true;
        }

        private void appri_Click(object sender, EventArgs e)
        {
            RIApp tempform = new RIApp(this);
            tempform.fctlid_1 = fctlid;
            tempform.fritype = "04";
            tempform.FillData("", "", "");
            tempform.ShowDialog();
        }

        private void loc_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (updateflag == true)
            {
                if (fcalc.Text.ToString() == "Share%" && Convert.ToDecimal(fsharec.Text) != Convert.ToDecimal(fshare.Text))
                {
                    MessageBox.Show("Invalid Total Share(Facultative))!", "Warning",
                         MessageBoxButtons.OK, MessageBoxIcon.Information);
                    fsharec.Focus();
                }
                else if (fcalc.Text.ToString() == "SI/ Limit" && Convert.ToDecimal(fsic.Text) != Convert.ToDecimal(fsi.Text))
                {
                    MessageBox.Show("Invalid Total SI(Facultative)!", "Warning",
                         MessageBoxButtons.OK, MessageBoxIcon.Information);
                    fsic.Focus();
                }
                else { if (loc.DataSource != null) { layrChag(); RITotal(); } }
            }
            else { if (loc.DataSource != null) { layrChag(); RITotal(); } }
        }

        private void FacPropPar_Leave(object sender, EventArgs e)
        {
            Skip_Validate = true;
        }

        private void FacPropPar_Enter(object sender, EventArgs e)
        {
            Skip_Validate = false;
        }


        private void facuRein_SelectionChanged(object sender, EventArgs e)
        {
            facuReinChange();
        }



    }
}
