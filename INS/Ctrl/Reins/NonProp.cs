using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using INS.INSClass;
using System.Data.SqlClient;
using System.Configuration;
using System.Collections;

namespace INS.Ctrl.Reins
{
    public partial class NonProp : UserControl
    {
        private DataTable orid2 = new DataTable();
        private DataTable orilayr = new DataTable();
        public DataSet dataSet = new DataSet("ds");
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public string fctlid = "", ltrid_fgpm = "0",fsec="";
        public Boolean updateflag = false; public Boolean Skip_Validate = false;
        public String ReinsValue
        {
            set
            { freinsr.Text = value; }
        }
        public String ReinsDesc
        {
            set
            { textBox16.Text = value; }
        }
        public String BrokerValue
        {
            set
            { fbrkr.Text = value; }
        }
        public String BrokerDesc
        {
            set
            { textBox14.Text = value; }
        }

        public NonProp()
        {
            InitializeComponent();
            loadLayr();
            foreach (Control control in panel1.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
        }

        public void setControlFocus(string ctrlName)
        {
            Controls["NonProp"].Controls["panel4"].Controls[ctrlName].Focus();
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
            {
                SendKeys.Send("{TAB}");
            }
        }

        void loadLayr()
        {
            string str = "select flyrid,fctlid from orilayr where fctlid_2 ='" + fctlid + "'";
            DataTable dt = DBHelper.GetDataSet(str);
            flyr.DisplayMember = "flyrid";
            flyr.ValueMember = "fctlid";
            flyr.DataSource = dt;
        }

        void tabPage4reload()
        {
            string selorilayr = "select * from orilayr where fctlid_2 ='" + fctlid + "'";
            orilayr = DBHelper.GetDataSet(selorilayr);
            if (orilayr != null)
            {
                if (orilayr.Rows.Count != 0)
                {
                    fgpm.Text = orilayr.Rows[0]["fgpm"].ToString().Trim();
                    fretnsh.Text = orilayr.Rows[0]["fretnsh"].ToString().Trim();
                    fretnamt.Text = orilayr.Rows[0]["fretnamt"].ToString().Trim();
                    ffacsh.Text = orilayr.Rows[0]["ffacsh"].ToString().Trim();
                    ffacamt.Text = orilayr.Rows[0]["ffacamt"].ToString().Trim();
                    fexcess.Text = orilayr.Rows[0]["fexcess"].ToString().Trim();
                    flimit.Text = orilayr.Rows[0]["flimit"].ToString().Trim();
                }
            }

            string selorid2 = "select orid2.*, b.fdesc as fbrkrD, c.fdesc as ReinsrD, CONVERT(varchar(20),flogseq) as [Seq#],freinsr as [Reinsurer], fshare as [Share%], " +
                "fgpm as [G Premium],fcomamt as [Comm Amt],fabsnet as [Abs Net] from orid2 left join (select fid,fdesc from "+Dbm+"mprdr where ftype='B') b on b.fid = orid2.fbrkr " +
               "left join (select fid,fdesc from "+Dbm+"mprdr where ftype='R') c on c.fid = orid2.freinsr where fctlid_2 ='" + fctlid + "' and fctlid_3 <>'' ";
            facuRein.DataSource = DBHelper.GetDataSet(selorid2);
            orid2 = DBHelper.GetDataSet(selorid2);
            if (facuRein.RowCount > 0) { facuRein.CurrentCell = facuRein.Rows[0].Cells["Seq#"]; }
            foreach (DataGridViewColumn Column in facuRein.Columns)
            {
                Column.Visible = false;
            }
            this.facuRein.Columns["Seq#"].Visible = true;
            this.facuRein.Columns["Reinsurer"].Visible = true;
            this.facuRein.Columns["Share%"].Visible = true;
            this.facuRein.Columns["Comm Amt"].Visible = true;
            this.facuRein.Columns["G Premium"].Visible = true;
            this.facuRein.Columns["Abs Net"].Visible = true;
            facuRein.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.facuRein_CellFormatting);

            dataSet.Tables.Clear();
            orilayr.TableName = "orilayr";
            dataSet.Tables.Add(orilayr);
            orid2.TableName = "orid2";
            dataSet.Tables.Add(orid2);
            layrChag();
            RITotal();
        }

        void RITotal()
        {
            Decimal Share = 0;
            for (int i = 0; i < facuRein.Rows.Count; i++)
            {
                if (facuRein["Share%", i].Value.ToString() != "")
                {
                    Share = Share + Convert.ToDecimal(facuRein["Share%", i].Value);
                }
            }
            fsharec.Text = Share.ToString("n2");

            Decimal GP = 0;
            for (int i = 0; i < facuRein.Rows.Count; i++)
            {
                if (facuRein["G Premium", i].Value.ToString() != "")
                {
                    GP = GP + Convert.ToDecimal(facuRein["G Premium", i].Value);
                }
            }
            ffacamtc.Text = GP.ToString("n2");

            Decimal com = 0;
            for (int i = 0; i < facuRein.Rows.Count; i++)
            {
                if (facuRein["Comm Amt", i].Value.ToString() != "")
                {
                    com = com + Convert.ToDecimal(facuRein["Comm Amt", i].Value);
                }
            }
            fcomamtc.Text = com.ToString("n2");

            Decimal net = 0;
            for (int i = 0; i < facuRein.Rows.Count; i++)
            {
                if (facuRein["Abs Net", i].Value.ToString() != "")
                {
                    net = net + Convert.ToDecimal(facuRein["Abs Net", i].Value);
                }
            }
            ffacnetc.Text = net.ToString("n2");
        }

        private void facuRein_CellFormatting(object sender, System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            if (facuRein.Columns[e.ColumnIndex].Name.Equals("Share%") ||
                facuRein.Columns[e.ColumnIndex].Name.Equals("Abs Net") ||
                facuRein.Columns[e.ColumnIndex].Name.Equals("G Premium") ||
                facuRein.Columns[e.ColumnIndex].Name.Equals("Comm Amt"))
            {
                e.CellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                facuRein.Columns[e.ColumnIndex].AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
            }
            if (facuRein.Columns[e.ColumnIndex].Name.Equals("Share%"))
            {
                facuRein.Columns[e.ColumnIndex].DefaultCellStyle.Format = "N4";
            }
            if (facuRein.Columns[e.ColumnIndex].Name.Equals("Seq#"))
            {
                facuRein.Columns[e.ColumnIndex].DefaultCellStyle.Format = "N0";
            }
        }

        public void layrChag()
        {
            if (dataSet.Tables.Count > 0)
            {
                if (flyr.SelectedValue != null && dataSet.Tables["orilayr"].Rows.Count != 0)
                {
                    DataRow[] layrRow = dataSet.Tables["orilayr"].Select("fctlid ='" + flyr.SelectedValue.ToString() + "'");
                    if (layrRow != null && layrRow.Length != 0)
                    {
                        fgpm.Text = layrRow[0]["fgpm"].ToString().Trim();
                        fretnsh.Text = layrRow[0]["fretnsh"].ToString().Trim();
                        fretnamt.Text = Convert.ToDecimal(layrRow[0]["fretnamt"].ToString().Trim()).ToString("N2");
                        ffacsh.Text = layrRow[0]["ffacsh"].ToString().Trim();
                        ffacamt.Text = Convert.ToDecimal(layrRow[0]["ffacamt"].ToString().Trim()).ToString("N2");
                        fexcess.Text = layrRow[0]["fexcess"].ToString().Trim();
                        flimit.Text = layrRow[0]["flimit"].ToString().Trim();
                    }
                }
                if (flyr.SelectedValue != null && dataSet.Tables["orid2"].Rows.Count != 0)
                {
                    DataRow[] dr = dataSet.Tables["orid2"].Select("fctlid_3 ='" + flyr.SelectedValue.ToString() + "'");
                    if (dr != null && dr.Length != 0)
                    {
                        DataTable dtGrid2 = dataSet.Tables["orid2"].Select("fctlid_3 ='" + flyr.SelectedValue.ToString() + "'" ,"flogseq ASC").CopyToDataTable();
                        facuRein.DataSource = null;
                        facuRein.DataSource = dtGrid2;

                        foreach (DataGridViewColumn Column in facuRein.Columns)
                        {
                            Column.Visible = false;
                        }
                        this.facuRein.Columns["Seq#"].Visible = true;
                        this.facuRein.Columns["Reinsurer"].Visible = true;
                        this.facuRein.Columns["Share%"].Visible = true;
                        this.facuRein.Columns["Comm Amt"].Visible = true;
                        this.facuRein.Columns["G Premium"].Visible = true;
                        this.facuRein.Columns["Abs Net"].Visible = true;
                        facuRein.CurrentCell = facuRein.Rows[facuRein.RowCount - 1].Cells["Seq#"];
                    }
                    else
                    {
                        string sql = "select orid2.*,  '' fbrkrD, '' as ReinsrD, flogseq as [Seq#],freinsr as [Reinsurer], fshare as [Share%],  fgpm as [G Premium],fcomamt as [Comm Amt],fabsnet as [Abs Net] from orid2 where fctlid='1'";
                        facuRein.DataSource = DBHelper.GetDataSet(sql);
                        foreach (DataGridViewColumn Column in facuRein.Columns)
                        {
                            Column.Visible = false;
                        }
                        this.facuRein.Columns["Seq#"].Visible = true;
                        this.facuRein.Columns["Reinsurer"].Visible = true;
                        this.facuRein.Columns["Share%"].Visible = true;
                        this.facuRein.Columns["Comm Amt"].Visible = true;
                        this.facuRein.Columns["G Premium"].Visible = true;
                        this.facuRein.Columns["Abs Net"].Visible = true;
                    }
                }
            }
        }

        private void facuRein_SelectionChanged(object sender, EventArgs e)
        {
            facuReinChange();
        }

        void facuReinChange()
        {
            if (facuRein.CurrentCell != null)
            {
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["Seq#"].Value != null)
                {
                    flogseq.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["Seq#"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fcession"].Value != null)
                {
                    fcession.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fcession"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["friapp"].Value != null)
                {
                    friapp.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["friapp"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["Reinsurer"].Value != null)
                {
                    freinsr.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["Reinsurer"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["ReinsrD"].Value != null)
                {
                    textBox16.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["ReinsrD"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fbrkr"].Value != null)
                {
                    fbrkr.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fbrkr"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fbrkrD"].Value != null)
                {
                    textBox14.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fbrkrD"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["frinsrref"].Value != null)
                {
                    frinsrref.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["frinsrref"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["Share%"].Value != null)
                {
                    fshare.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["Share%"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["G Premium"].Value != null)
                {
                    gpm.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["G Premium"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["Comm Amt"].Value != null)
                {
                    fcomamt.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["Comm Amt"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fcom"].Value != null)
                {
                    fcom.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fcom"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["Abs Net"].Value != null)
                {
                    fabsnet.Text = facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["Abs Net"].Value.ToString().Trim();
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fcalc"].Value != null)
                {
                    if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fcalc"].Value.ToString().Trim() == "1") { calc.SelectedItem = "Rate"; }
                    else { calc.SelectedItem = "Flat"; }
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fpnetonly"].Value != null)
                {
                    if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fpnetonly"].Value.ToString().Trim() == "1") { fpnetonly.SelectedItem = "Yes"; }
                    else { fpnetonly.SelectedItem = "No"; }
                }
                if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fsepdrcr"].Value != null)
                {
                    if (facuRein.Rows[facuRein.CurrentCell.RowIndex].Cells["fsepdrcr"].Value.ToString().Trim() == "1") { fsepdrcr.SelectedItem = "Yes"; }
                    else { fsepdrcr.SelectedItem = "No"; }
                }
            }
        }

        private void flyr_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (flyr.DataSource != null) { layrChag(); RITotal(); }
        }

        private void layAdd_Click(object sender, EventArgs e)
        {
            if (Convert.ToDecimal(ffacsh.Text) != Convert.ToDecimal(fsharec.Text))
            {
                MessageBox.Show("Invalid Fac Layer Share!", "Warning",
                       MessageBoxButtons.OK, MessageBoxIcon.Information);
                ffacsh.Focus();
            }
            else { 
            DataRow newlayrRow = dataSet.Tables["orilayr"].NewRow();
            if (dataSet.Tables["orilayr"].Rows.Count != 0)
            {
                newlayrRow["flyrid"] = int.Parse(dataSet.Tables["orilayr"].Rows[dataSet.Tables["orilayr"].Rows.Count - 1]["flyrid"].ToString()) + 1;
                newlayrRow["fctlid"] = int.Parse(dataSet.Tables["orilayr"].Rows[dataSet.Tables["orilayr"].Rows.Count - 1]["flyrid"].ToString()) + 1;
            }
            else
            {
                newlayrRow["flyrid"] = 1;
                newlayrRow["fctlid"] = 1;
            }
            dataSet.Tables["orilayr"].Rows.Add(newlayrRow);
            newlayrRow["fgpm"] = 0;
            newlayrRow["fretnsh"] = 0;
            newlayrRow["fretnamt"] = 0;
            newlayrRow["fretnsh"] = 0;
            newlayrRow["ffacamt"] = 0;
            newlayrRow["fexcess"] = 0;
            newlayrRow["flimit"] = 0;

            flyr.DataSource = orilayr;
            flyr.DisplayMember = "flyrid";
            flyr.ValueMember = "fctlid";
            flyr.SelectedValue = newlayrRow["fctlid"];
            foreach (Control ctrl in panel1.Controls)                //循环窗体的控件
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    ((TextBox)ctrl).ReadOnly = true;
                    ((TextBox)ctrl).Text = "";
                }
            }
            foreach (Control ctrl in panel2.Controls)                //循环窗体的控件
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                }
            }
            foreach (Control ctrl in panel4.Controls)                //循环窗体的控件
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                    ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                    ((TextBox)ctrl).ReadOnly = false;
                }
            }
            fretnsh.ReadOnly = false;
            fretnsh.BackColor = SystemColors.Window;
            fgpm.ReadOnly = false;
            fgpm.BackColor = SystemColors.Window;
            fexcess.ReadOnly = false;
            fexcess.BackColor = SystemColors.Window;
            flimit.ReadOnly = false;
            flimit.BackColor = SystemColors.Window;
            ffacsh.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            ffacsh.ReadOnly = true;
            fretnamt.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fretnamt.ReadOnly = true;
            ffacamt.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            ffacamt.ReadOnly = true;
            }
        }

        private void layDel_Click(object sender, EventArgs e)
        {
            DataRow[] drr = dataSet.Tables["orilayr"].Select("fctlid ='" + flyr.SelectedValue.ToString() + "'");
            for (int i = 0; i < drr.Length; i++)
            {
                dataSet.Tables["orilayr"].Rows.Remove(drr[i]);
            }
            flyr.DisplayMember = "flyrid";
            flyr.ValueMember = "fctlid";
            flyr.DataSource = dataSet.Tables["orilayr"];
        }

        private void add_Click(object sender, EventArgs e)
        {
            if (flyr.SelectedValue != null)
            {
                string[] row1 = new string[] { "", "", flyr.SelectedValue.ToString(), "", "", "", "", "" };
                dataSet.Tables["orid2"].Rows.Add(row1);
                facuRein.DataSource = dataSet.Tables["orid2"].Select("fctlid_3 ='" + flyr.SelectedValue.ToString() + "'").CopyToDataTable();
                facuRein.CurrentCell = facuRein.Rows[facuRein.RowCount - 1].Cells["Seq#"];
               
                flogseq.Text = facuRein.Rows.Count.ToString();
                facuRein["Seq#", facuRein.CurrentCell.RowIndex].Value = flogseq.Text;
                if (facuRein.CurrentCell.RowIndex != 0)
                {
                    facuRein["fbrkr", facuRein.CurrentCell.RowIndex].Value = facuRein["fbrkr", facuRein.CurrentCell.RowIndex - 1].Value;
                    facuRein["fbrkrD", facuRein.CurrentCell.RowIndex].Value = facuRein["fbrkrD", facuRein.CurrentCell.RowIndex - 1].Value;
                    fbrkr.Text = facuRein["fbrkr", facuRein.CurrentCell.RowIndex - 1].Value.ToString();
                }
                flogseq.ReadOnly = false;
                flogseq.BackColor = SystemColors.Window;
                freinsr.ReadOnly = false;
                freinsr.BackColor = SystemColors.Window;
                fbrkr.ReadOnly = false;
                fbrkr.BackColor = SystemColors.Window;
                fshare.ReadOnly = false;
                fshare.BackColor = SystemColors.Window;
                calc.Enabled = true;
                calc.BackColor = SystemColors.Window;
                fpnetonly.Enabled = true;
                fpnetonly.BackColor = SystemColors.Window;
                fsepdrcr.Enabled = true;
                fsepdrcr.BackColor = SystemColors.Window;
                ReinsrBt.Enabled = true;
                BrokerBt.Enabled = true;
                facuReinChange();
                calc.SelectedItem = "Rate";
            }
        }

        private void del_Click(object sender, EventArgs e)
        {
            if (facuRein.RowCount != 0)
            {
                try
                {
                    DataRow[] drr = dataSet.Tables["orid2"].Select("fctlid_3 ='" + flyr.SelectedValue.ToString() + "' and [Seq#] ='" + flogseq.Text.ToString() + "'");
                    for (int i = 0; i < drr.Length; i++)
                    {
                        dataSet.Tables["orid2"].Rows.Remove(drr[i]);
                    }
                    facuRein.DataSource = dataSet.Tables["orid2"];
                    layrChag();
                }
                catch { }
            }
            if (facuRein.RowCount == 0)
            {
                foreach (Control ctrl in panel1.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                    if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                    if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
                }
            }
        }

        private void ReinsrBt_Click(object sender, EventArgs e)
        {
            frmReinsurerSearch tempform = new frmReinsurerSearch(this);
            tempform.flag = "NonReins";
            tempform.FillData("", "");
            tempform.ShowDialog();
        }

        private void BrokerBt_Click(object sender, EventArgs e)
        {
            frmBrokerSearch tempform = new frmBrokerSearch(this);
            tempform.flag = "NonBroker";
            tempform.FillData("", "");
            tempform.ShowDialog();
        }

        private void calc_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (updateflag == true && dataSet.Tables.Count > 0)
            {
                DataRow[] dtRow = dataSet.Tables["orid2"].Select("fctlid_3 = '" + flyr.SelectedValue.ToString() + "'");
                if (calc.SelectedItem.ToString() == "Rate")
                {
                    facuRein["fcalc", facuRein.CurrentCell.RowIndex].Value = "1";
                    if (dtRow.Length > 0)
                    {
                        dtRow[facuRein.CurrentCell.RowIndex]["fcalc"] = "1";
                    }
                    fcom.ReadOnly = false;
                    fcom.BackColor = SystemColors.Window;
                    fcomamt.ReadOnly = true;
                    fcomamt.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (calc.SelectedItem.ToString() == "Flat")
                {
                    facuRein["fcalc", facuRein.CurrentCell.RowIndex].Value = "2";
                    if (dtRow.Length > 0)
                    {
                        dtRow[facuRein.CurrentCell.RowIndex]["fcalc"] = "2";
                    }
                    fcomamt.ReadOnly = false;
                    fcomamt.BackColor = SystemColors.Window;
                    fcom.ReadOnly = true;
                    fcom.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
            }
        }

        private void flogseq_Validated(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && dataSet.Tables.Count > 0 && !Skip_Validate)
            {
                if (flogseq.Text == "")
                {
                    flogseq.Focus();
                }
                else
                {
                    if (facuRein.CurrentCell == null) { facuRein.CurrentCell = facuRein.Rows[facuRein.RowCount - 1].Cells["Seq#"]; }
                    DataRow[] dtRow = dataSet.Tables["orid2"].Select("fctlid_3 = '" + flyr.SelectedValue.ToString() + "'");
                    if (dtRow.Length > 0)
                    {
                        dtRow[facuRein.CurrentCell.RowIndex]["Seq#"] = flogseq.Text;
                    }
                    facuRein["Seq#", facuRein.CurrentCell.RowIndex].Value = flogseq.Text;
                }
            }
        }

        private void fshare_Validated(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && dataSet.Tables.Count > 0 && !Skip_Validate)
            {
                if ((Convert.ToDecimal(fshare.Text) <= 0 || Convert.ToDecimal(fshare.Text) > 100))
                {
                    MessageBox.Show("Invalid Value!", "Warning",
                     MessageBoxButtons.OK, MessageBoxIcon.Information);
                    fshare.Focus();
                }
                else
                {
                    u_calrid2();
                    facuReinChange();
                    RITotal();
                }
            }
        }

        private void fcom_Validated(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && dataSet.Tables.Count > 0 && !Skip_Validate)
            {
                u_calrid2();
                facuReinChange();
                RITotal();
            }
        }

        private void fcomamt_Validated(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && dataSet.Tables.Count > 0 && !Skip_Validate)
            {
                u_calrid2();
                facuReinChange();
                RITotal();
            }
        }

        private void fpnetonly_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && dataSet.Tables.Count > 0 && !Skip_Validate)
            {
                DataRow[] dtRow = dataSet.Tables["orid2"].Select("fctlid_3 = '" + flyr.SelectedValue.ToString() + "'");

                if (fpnetonly.SelectedItem.ToString().Trim() == "Yes")
                {
                    facuRein["fpnetonly", facuRein.CurrentCell.RowIndex].Value = "1";
                    if (dtRow.Length > 0)
                    {
                        dtRow[facuRein.CurrentCell.RowIndex]["fpnetonly"] = "1";
                    }
                }
                else
                {
                    facuRein["fpnetonly", facuRein.CurrentCell.RowIndex].Value = "2";
                    if (dtRow.Length > 0)
                    {
                        dtRow[facuRein.CurrentCell.RowIndex]["fpnetonly"] = "2";
                    }
                }

            }
        }

        private void fsepdrcr_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && dataSet.Tables.Count > 0 && !Skip_Validate)
            {
                DataRow[] dtRow = dataSet.Tables["orid2"].Select("fctlid_3 = '" + flyr.SelectedValue.ToString() + "'");
                if (fsepdrcr.SelectedItem.ToString().Trim() == "Yes")
                {
                    facuRein["fsepdrcr", facuRein.CurrentCell.RowIndex].Value = "1";
                    if (dtRow.Length > 0)
                    {
                        dtRow[facuRein.CurrentCell.RowIndex]["fsepdrcr"] = "1";
                    }
                }
                else
                {
                    facuRein["fsepdrcr", facuRein.CurrentCell.RowIndex].Value = "2";
                    if (dtRow.Length > 0)
                    {
                        dtRow[facuRein.CurrentCell.RowIndex]["fsepdrcr"] = "2";
                    }
                }
            }
        }

        private void textBox16_TextChanged(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && dataSet.Tables.Count > 0 && !Skip_Validate)
            {
                facuRein["ReinsrD", facuRein.CurrentCell.RowIndex].Value = textBox16.Text;
                DataRow[] dtRow = dataSet.Tables["orid2"].Select("fctlid_3 = '" + flyr.SelectedValue.ToString() + "'");
                if (dtRow.Length > 0)
                {
                    dtRow[facuRein.CurrentCell.RowIndex]["ReinsrD"] = textBox16.Text;
                }
            }
        }

        private void textBox14_TextChanged(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && dataSet.Tables.Count > 0 && !Skip_Validate)
            {
                facuRein["fbrkrD", facuRein.CurrentCell.RowIndex].Value = textBox14.Text;
                DataRow[] dtRow = dataSet.Tables["orid2"].Select("fctlid_3 = '" + flyr.SelectedValue.ToString() + "'");
                if (dtRow.Length > 0)
                {
                    dtRow[facuRein.CurrentCell.RowIndex]["fbrkrD"] = textBox14.Text;
                }
            }
        }

        private void freinsr_Validated(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && dataSet.Tables.Count > 0 && !Skip_Validate)
            {
                facuRein["Reinsurer", facuRein.CurrentCell.RowIndex].Value = freinsr.Text;
                DataRow[] dtRow = dataSet.Tables["orid2"].Select("fctlid_3 = '" + flyr.SelectedValue.ToString() + "'");
                if (dtRow.Length > 0)
                {
                    dtRow[facuRein.CurrentCell.RowIndex]["Reinsurer"] = freinsr.Text;
                }
                string desc = CheckValue(freinsr.Text.ToString(), "Reinsurer");
                if (desc == null && freinsr.Text != "")
                {
                    freinsr.Focus();
                    MessageBox.Show("Invalid Value");
                }
                else
                {
                    textBox16.Text = desc;
                    facuRein["ReinsrD", facuRein.CurrentCell.RowIndex].Value = textBox16.Text;
                    if (dtRow.Length > 0)
                    {
                        dtRow[facuRein.CurrentCell.RowIndex]["ReinsrD"] = textBox16.Text;
                    }
                }
            }
        }

        private void fbrkr_Validated(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && dataSet.Tables.Count > 0 && !Skip_Validate)
            {
                facuRein["fbrkr", facuRein.CurrentCell.RowIndex].Value = fbrkr.Text;
                DataRow[] dtRow = dataSet.Tables["orid2"].Select("fctlid_3 = '" + flyr.SelectedValue.ToString() + "'");
                if (dtRow.Length > 0)
                {
                    dtRow[facuRein.CurrentCell.RowIndex]["fbrkr"] = fbrkr.Text;
                }
                string desc = CheckValue(fbrkr.Text.ToString(), "Broker");
                if (desc == null && fbrkr.Text != "")
                {
                    fbrkr.Focus();
                    MessageBox.Show("Invalid Value");
                }
                else
                {
                    textBox14.Text = desc;
                    facuRein["fbrkrD", facuRein.CurrentCell.RowIndex].Value = textBox14.Text;
                    if (dtRow.Length > 0)
                    {
                        dtRow[facuRein.CurrentCell.RowIndex]["fbrkrD"] = textBox14.Text;
                    }
                }
            }
        }

        private void fbrkr_TextChanged(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && dataSet.Tables.Count > 0 && !Skip_Validate)
            {
                facuRein["fbrkr", facuRein.CurrentCell.RowIndex].Value = fbrkr.Text;
                DataRow[] dtRow = dataSet.Tables["orid2"].Select("fctlid_3 = '" + flyr.SelectedValue.ToString() + "'");
                if (dtRow.Length > 0)
                {
                    dtRow[facuRein.CurrentCell.RowIndex]["fbrkr"] = fbrkr.Text;
                }
            }
        }

        private void freinsr_TextChanged(object sender, EventArgs e)
        {
            if (updateflag == true && facuRein.CurrentCell != null && dataSet.Tables.Count > 0 && !Skip_Validate)
            {
                facuRein["Reinsurer", facuRein.CurrentCell.RowIndex].Value = freinsr.Text;
                DataRow[] dtRow = dataSet.Tables["orid2"].Select("fctlid_3 = '" + flyr.SelectedValue.ToString() + "'");
                if (dtRow.Length > 0)
                {
                    dtRow[facuRein.CurrentCell.RowIndex]["Reinsurer"] = freinsr.Text;
                }
            }
        }

        public string CheckValue(string flyrid, string flag)
        {
            string str = "";
            if (flag == "Broker")
            {
                str = "select fid,fdesc from mprdr where ftype='B' and fid='" + flyrid + "'";
            }
            if (flag == "Reinsurer")
            {
                str = "select fid,fdesc from mprdr where ftype='R' and fid='" + flyrid + "'";
            }
            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["mdata"].ConnectionString);
            SqlCommand cmd = new SqlCommand(str, con);
            con.Open();
            using (SqlDataReader sdr = cmd.ExecuteReader())
            {
                if (sdr.HasRows && sdr.Read())
                {
                    string fdesc = sdr["fdesc"].ToString().Trim();
                    sdr.Close();
                    return fdesc;
                }
                else { return null; }
            }
        }

        private void fgpm_Validated(object sender, EventArgs e)
        {
            if (updateflag == true && flyr.SelectedValue != null && dataSet.Tables["orilayr"].Rows.Count != 0 && !Skip_Validate)
            {
                DataRow[] layrRow = dataSet.Tables["orilayr"].Select("fctlid ='" + flyr.SelectedValue.ToString() + "'");
                if (layrRow != null && layrRow.Length != 0)
                {
                    layrRow[0]["fgpm"] = fgpm.Text;
                }
            }
            u_layr();
        }

        private void fretnsh_Validated(object sender, EventArgs e)
        {
            if (updateflag == true && flyr.SelectedValue != null && dataSet.Tables["orilayr"].Rows.Count != 0 && !Skip_Validate)
            {
                DataRow[] layrRow = dataSet.Tables["orilayr"].Select("fctlid ='" + flyr.SelectedValue.ToString() + "'");
                if (layrRow != null && layrRow.Length != 0)
                {
                    layrRow[0]["fretnsh"] = fretnsh.Text;
                }
            }
            u_layr();
        }

        private void fexcess_Validated(object sender, EventArgs e)
        {
            if (updateflag == true && flyr.SelectedValue != null && dataSet.Tables["orilayr"].Rows.Count != 0 && !Skip_Validate)
            {
                DataRow[] layrRow = dataSet.Tables["orilayr"].Select("fctlid ='" + flyr.SelectedValue.ToString() + "'");
                if (layrRow != null && layrRow.Length != 0)
                {
                    layrRow[0]["fexcess"] = fexcess.Text;
                }
            }
        }

        private void flimit_Validated(object sender, EventArgs e)
        {
            if (updateflag == true && flyr.SelectedValue != null && dataSet.Tables["orilayr"].Rows.Count != 0 && !Skip_Validate)
            {
                DataRow[] layrRow = dataSet.Tables["orilayr"].Select("fctlid ='" + flyr.SelectedValue.ToString() + "'");
                if (layrRow != null && layrRow.Length != 0)
                {
                    layrRow[0]["flimit"] = flimit.Text;
                }
            }
        }

        void u_calrid2()
        {
            decimal ln_layrgpm, ln_fcom = 0;
            decimal ln_fshare, ln_fgpm, ln_fnpm, ln_fcomamt = 0, ln_fabsnet;
            ln_fshare = Convert.ToDecimal(fshare.Text);
            ln_layrgpm = Convert.ToDecimal(fgpm.Text);
            ln_fgpm = Math.Round(ln_layrgpm * ln_fshare / 100, 2, MidpointRounding.AwayFromZero);

            if (calc.Text.ToString() == "Rate")
            {
                ln_fcom = Convert.ToDecimal(fcom.Text);
                ln_fcomamt = Math.Round(ln_fcom * ln_fgpm / 100, 2, MidpointRounding.AwayFromZero);
            }
            if (calc.Text.ToString() == "Flat" && ln_fgpm!=0)
            {
                ln_fcomamt = Convert.ToDecimal(fcomamt.Text);
                ln_fcom = Math.Round(ln_fcomamt / ln_fgpm * 100, 4, MidpointRounding.AwayFromZero);
            }
            ln_fnpm = ln_fgpm - ln_fcomamt;
            ln_fabsnet = ln_fgpm - ln_fcomamt;

            int row = 0;
            if (facuRein.CurrentCell != null) { row = facuRein.CurrentCell.RowIndex; }
            facuRein["Share%", row].Value = ln_fshare;
            facuRein["G Premium", row].Value = ln_fgpm;
            facuRein["Abs Net", row].Value = ln_fabsnet;
            facuRein["Comm Amt", row].Value = ln_fcomamt;
            facuRein["fcom", row].Value = ln_fcom;
            facuRein["fnpm", row].Value = ln_fnpm;
            DataRow[] dtRow = dataSet.Tables["orid2"].Select("fctlid_3 = '" + flyr.SelectedValue.ToString() + "'");
            if (dtRow.Length > 0)
            {
                dtRow[row]["Share%"] = ln_fshare;
                dtRow[row]["G Premium"] = ln_fgpm;
                dtRow[row]["Abs Net"] = ln_fabsnet;
                dtRow[row]["Comm Amt"] = ln_fcomamt;
                dtRow[row]["fcom"] = ln_fcom;
                dtRow[row]["fnpm"] = ln_fnpm;
            }
        }

        void u_layr()
        {
            decimal ln_fretnamt, ln_ffacamt;
            decimal ln_fretnsh = Convert.ToDecimal(fretnsh.Text);
            decimal ln_fgpm = Convert.ToDecimal(fgpm.Text);
            ln_fretnamt = Math.Round(ln_fgpm * ln_fretnsh / 100, 2, MidpointRounding.AwayFromZero);
            ln_ffacamt = ln_fgpm - ln_fretnamt;
            fretnamt.Text = ln_fretnamt.ToString("N2");
            ffacsh.Text = (100 - ln_fretnsh).ToString();
            ffacamt.Text = ln_ffacamt.ToString("N2");
        }


        public void buttoncontrolback()
        {
            loadLayr();
            updateflag = false;
            add.Visible = false;
            appri.Visible = true;
            del.Visible = false;
            layAdd.Visible = false;
            layDel.Visible = false;

            foreach (Control ctrl in panel1.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                    ((TextBox)ctrl).ReadOnly = true;
                    ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
            }
            foreach (Control ctrl in panel4.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                    ((TextBox)ctrl).ReadOnly = true;
                    ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
            }
            tabPage4reload();
            flyr.Enabled = true;
        }

        public void buttoncontrolupdate()
        {
            loadLayr();
            updateflag = true;
            tabPage4update();
        }

        public void pick()
        {
            DataTable dt = dataSet.Tables["orilayr"].Copy() ;
            flyr.DisplayMember = "flyrid";
            flyr.ValueMember = "fctlid";
            flyr.DataSource = dt;

            layrChag();

            if (facuRein.RowCount > 0)
            {
                flogseq.ReadOnly = false;
                flogseq.BackColor = SystemColors.Window;
                freinsr.ReadOnly = false;
                freinsr.BackColor = SystemColors.Window;
                fbrkr.ReadOnly = false;
                fbrkr.BackColor = SystemColors.Window;
                fshare.ReadOnly = false;
                fshare.BackColor = SystemColors.Window;
                calc.Enabled = true;
                calc.BackColor = SystemColors.Window;
                fpnetonly.Enabled = true;
                fpnetonly.BackColor = SystemColors.Window;
                fsepdrcr.Enabled = true;
                fsepdrcr.BackColor = SystemColors.Window;
                ReinsrBt.Enabled = true;
                BrokerBt.Enabled = true;

                if (updateflag == true && dataSet.Tables.Count > 0)
                {
                    DataRow[] dtRow = dataSet.Tables["orid2"].Select("fctlid_3 = '" + flyr.SelectedValue.ToString() + "'");
                    if (calc.SelectedItem.ToString() == "Rate")
                    {
                        facuRein["fcalc", facuRein.CurrentCell.RowIndex].Value = "1";
                        if (dtRow.Length > 0)
                        {
                            dtRow[facuRein.CurrentCell.RowIndex]["fcalc"] = "1";
                        }
                        fcom.ReadOnly = false;
                        fcom.BackColor = SystemColors.Window;
                        fcomamt.ReadOnly = true;
                        fcomamt.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                    if (calc.SelectedItem.ToString() == "Flat")
                    {
                        facuRein["fcalc", facuRein.CurrentCell.RowIndex].Value = "2";
                        if (dtRow.Length > 0)
                        {
                            dtRow[facuRein.CurrentCell.RowIndex]["fcalc"] = "2";
                        }
                        fcomamt.ReadOnly = false;
                        fcomamt.BackColor = SystemColors.Window;
                        fcom.ReadOnly = true;
                        fcom.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                }
            }
        }

        void tabPage4update()
        {
            tabPage4reload();
            add.Visible = true;
            del.Visible = true;
            appri.Visible = false;
            layAdd.Visible = true;
            layDel.Visible = true;
            layrChag();
            if (facuRein.RowCount > 0)
            {
                flogseq.ReadOnly = false;
                flogseq.BackColor = SystemColors.Window;
                freinsr.ReadOnly = false;
                freinsr.BackColor = SystemColors.Window;
                fbrkr.ReadOnly = false;
                fbrkr.BackColor = SystemColors.Window;
                fcom.ReadOnly = false;
                fcom.BackColor = SystemColors.Window;
                fshare.ReadOnly = false;
                fshare.BackColor = SystemColors.Window;
                calc.Enabled = true;
                calc.BackColor = SystemColors.Window;
                fpnetonly.Enabled = true;
                fpnetonly.BackColor = SystemColors.Window;
                ReinsrBt.Enabled = true;
                BrokerBt.Enabled = true;
            }
        }

        private void ffacsh_TextChanged(object sender, EventArgs e)
        {
            if (updateflag == true && dataSet.Tables.Count > 0)
            {
                if (flyr.SelectedValue != null && dataSet.Tables["orilayr"].Rows.Count != 0)
                {
                    DataRow[] layrRow = dataSet.Tables["orilayr"].Select("fctlid ='" + flyr.SelectedValue.ToString() + "'");
                    if (layrRow != null && layrRow.Length != 0)
                    {
                        layrRow[0]["ffacsh"] = ffacsh.Text;
                    }
                }
            }
        }

        private void fretnamt_TextChanged(object sender, EventArgs e)
        {
            if (updateflag == true && dataSet.Tables.Count >0)
            {
                if (flyr.SelectedValue != null && dataSet.Tables["orilayr"].Rows.Count != 0)
                {
                    DataRow[] layrRow = dataSet.Tables["orilayr"].Select("fctlid ='" + flyr.SelectedValue.ToString() + "'");
                    if (layrRow != null && layrRow.Length != 0)
                    {
                        layrRow[0]["fretnamt"] = fretnamt.Text;
                    }
                }
                //fretnamt.Text = Convert.ToDecimal(fretnamt.Text).ToString("N2");
            }
        }

        private void ffacamt_TextChanged(object sender, EventArgs e)
        {
            if (updateflag == true && dataSet.Tables.Count > 0)
            {
                if (flyr.SelectedValue != null && dataSet.Tables["orilayr"].Rows.Count != 0)
                {
                    DataRow[] layrRow = dataSet.Tables["orilayr"].Select("fctlid ='" + flyr.SelectedValue.ToString() + "'");
                    if (layrRow != null && layrRow.Length != 0)
                    {
                        layrRow[0]["ffacamt"] = ffacamt.Text;
                    }
                }
            }
        }

        public string u_valrinsr2()
        {
            decimal trid1_fgpm = Convert.ToDecimal(ltrid_fgpm);
            decimal ln_fgpm = Convert.ToDecimal(fgpm.Text);
            if ((trid1_fgpm > 0 && ln_fgpm > trid1_fgpm) || (trid1_fgpm < 0 && ln_fgpm < trid1_fgpm))
            {
                if (trid1_fgpm > 0)
                {
                    MessageBox.Show("Sum of Fac Layer Premium is greater than +ve Retention Premium!", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("Sum of Fac Layer Premium is less than +ve Retention Premium!", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                fgpm.Focus();
                return "fgpm";
            }
            if (Convert.ToDecimal(ffacsh.Text) != Convert.ToDecimal(fsharec.Text))
            {
                MessageBox.Show("Invalid Fac Layer Share!", "Warning",
                       MessageBoxButtons.OK, MessageBoxIcon.Information);
                ffacsh.Focus();
                return "ffacsh";
            }
            foreach (var item in flyr.Items)
            {
                DataRowView row = item as DataRowView;
                DataRow[] dr = dataSet.Tables["orid2"].Select("fctlid_3 ='" + row["fctlid"].ToString() + "'");
                if (dr.Length == 0)
                {
                    MessageBox.Show("Fac Layer Reinsurer does not exist!", "Warning",
                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    gpm.Focus();
                    return "gpm";
                }
            }
            foreach (DataGridViewRow row in facuRein.Rows)
            {
                facuRein.CurrentCell = row.Cells["Share%"];
                facuReinChange();
                if (row.Cells["Seq#"].Value.ToString() == "")
                {
                    MessageBox.Show("Empty Value is not allowed!", "Warning",
                     MessageBoxButtons.OK, MessageBoxIcon.Information);
                    flogseq.Focus();
                    return "flogseq";
                }
                string desc = CheckValue(row.Cells["freinsr"].Value.ToString(), "Reinsurer");
                if (desc == null && row.Cells["freinsr"].Value.ToString() != "")
                {
                    MessageBox.Show("Invalid Value");
                    freinsr.Focus();
                    return "freinsr";
                }
                if (Convert.ToDecimal(row.Cells["fcom"].Value.ToString()) < 0)
                {
                    MessageBox.Show("Invalid Value");
                    fcom.Focus();
                    return "fcom";
                }
                string desc1 = CheckValue(row.Cells["fbrkr"].Value.ToString(), "Broker");
                if (desc1 == null && row.Cells["fbrkr"].Value.ToString() != "")
                {
                    MessageBox.Show("Invalid Value");
                    fbrkr.Focus(); 
                    return "fbrkr";
                }
                if ((Convert.ToDecimal(row.Cells["Share%"].Value.ToString()) <= 0 || Convert.ToDecimal(row.Cells["Share%"].Value.ToString()) > 100))
                {
                    MessageBox.Show("Invalid Value!", "Warning",
                     MessageBoxButtons.OK, MessageBoxIcon.Information);
                    fshare.Focus();
                    return "fshare";
                }

                decimal ln_base = Convert.ToDecimal(row.Cells["G Premium"].Value.ToString());
                int ln_result = 0;
                decimal ln_retpm = Convert.ToDecimal(fgpm.Text);
                if (ln_retpm >= 0 && ln_base >= 0 && ln_base <= ln_retpm) { ln_result = 1; }
                if (ln_retpm < 0 && ln_base < 0 && ln_base >= ln_retpm) { ln_result = 1; }
                if (ln_result == 0)
                {
                    MessageBox.Show("Invalid Value!", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
                    gpm.Focus();
                    return "gpm";
                }

                decimal ln_fcomamt = Convert.ToDecimal(row.Cells["Comm Amt"].Value.ToString());
                if ((ln_base > 0 && (ln_fcomamt < 0 || ln_fcomamt > ln_base)) || (ln_base < 0 && (ln_fcomamt > 0 || ln_fcomamt < ln_base)))
                {
                    MessageBox.Show("Invalid Value!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    fcomamt.Focus();
                    return "fcomamt";
                }
            }
            return "";
        }

        public void u_calallrid2b()
        {
            foreach (var item in flyr.Items)
            {
                decimal ln_fshare = 0, ln_fgpm = 0, ln_fnpm = 0, ln_fcomamt = 0, ln_fabsnet = 0;
                decimal ln_layrgpm = 0, ln_fcom = 0, ln_ttfgpm = 0, ln_ttfabsnet = 0, ln_ttfcomamt = 0, ln_layrfacamt = 0;
                DataRowView row = item as DataRowView;
                DataRow[] layrRow = dataSet.Tables["orilayr"].Select("fctlid ='" + row["fctlid"].ToString() + "'");
                if (layrRow != null && layrRow.Length != 0)
                {
                    ln_layrgpm = Convert.ToDecimal(layrRow[0]["fgpm"]);
                    ln_layrfacamt = Convert.ToDecimal(layrRow[0]["ffacamt"]);
                }
                DataRow[] drs = dataSet.Tables["orid2"].Select("fctlid_3 ='" + row["fctlid"].ToString() + "'");
                foreach (DataRow dr in drs)
                {
                    ln_fshare = Convert.ToDecimal(dr["Share%"]);
                    ln_fgpm = Math.Round(ln_layrgpm * ln_fshare / 100, 2, MidpointRounding.AwayFromZero);
                    if (dr["fcalc"].ToString() == "1")
                    {
                        ln_fcom = Convert.ToDecimal(dr["fcom"]);
                        ln_fcomamt = Math.Round(ln_fgpm * ln_fcom / 100, 2, MidpointRounding.AwayFromZero);
                    }
                    else
                    {
                        ln_fcomamt = Convert.ToDecimal(dr["Comm Amt"]);
                        ln_fcom = Math.Round(ln_fcomamt / ln_fgpm * 100, 4, MidpointRounding.AwayFromZero);
                    }
                    ln_fnpm = ln_fgpm - ln_fcomamt;
                    ln_fabsnet = ln_fgpm - ln_fcomamt;

                    ln_ttfgpm = ln_ttfgpm + ln_fgpm;
                    ln_ttfcomamt = ln_ttfcomamt + ln_fcomamt;
                    ln_ttfabsnet = ln_ttfabsnet + ln_fabsnet;

                    dr["G Premium"] = ln_fgpm;
                    dr["Abs Net"] = ln_fabsnet;
                    dr["Comm Amt"] = ln_fcomamt;
                    dr["fcom"] = ln_fcom;
                    dr["fnpm"] = ln_fnpm;
                } 
                foreach (DataRow dr in drs)
                {
                    if (dr["Seq#"].ToString() == "1")
                    {
                        dr["G Premium"] = Convert.ToDecimal(dr["G Premium"].ToString()) + ln_layrfacamt - ln_ttfgpm;
                        dr["fnpm"] = Convert.ToDecimal(dr["G Premium"].ToString()) - Convert.ToDecimal(dr["Comm Amt"].ToString());
                        dr["Abs Net"] = dr["fnpm"];
                    }
                }
            }
        }

        public string[] tabpage4save()
        {
            string sql = "", fctlid_layr = "";
            ArrayList addsql = new ArrayList();
            string selorilayr = "select * from orilayr where fctlid_2 ='" + fctlid + "'";
            DataTable orilayrOrg = DBHelper.GetDataSet(selorilayr);

            string selorid2 = "select orid2.*, b.fdesc as fbrkrD, c.fdesc as ReinsrD, CONVERT(varchar(20),flogseq) as [Seq#],freinsr as [Reinsurer], fshare as [Share%], " +
                "fgpm as [G Premium],fcomamt as [Comm Amt], fabsnet as [Abs Net] from orid2 left join (select fid,fdesc from "+Dbm+"mprdr where ftype='B') b on b.fid = orid2.fbrkr " +
               "left join (select fid,fdesc from "+Dbm+"mprdr where ftype='R') c on c.fid = orid2.freinsr where fctlid_2 ='" + fctlid + "' and fctlid_3 <>'' ";
            DataTable orid2Org = DBHelper.GetDataSet(selorid2);

            if (orilayrOrg != null && orilayrOrg.Rows.Count > 0)
            {
                string[] sqlorilayr = CompareDt(orilayrOrg, dataSet.Tables["orilayr"].Select("LEN(fctlid)=10").CopyToDataTable(), "fctlid", "orilayr");
                if (sqlorilayr != null)
                {
                    for (int p = 0; p < sqlorilayr.Length; p++)
                    { addsql.Add(sqlorilayr[p]); }
                }
            }
            if (orid2Org != null && orid2Org.Rows.Count > 0)
            {
                string[] sqlorid2 = CompareDt(orid2Org, dataSet.Tables["orid2"].Select("LEN(fctlid_3)=10").CopyToDataTable(), "fctlid", "orid2");
                if (sqlorid2 != null)
                {
                    for (int p = 0; p < sqlorid2.Length; p++)
                    { addsql.Add(sqlorid2[p]); }
                }
            }
            string fclass = "", fkind = "", fbus = "", fctlid_1 = "", fctlid_2 = "";
            string selorid1 = "select * from orih where fctlid ='" + fctlid + "'";
            DataTable dtorid1 = DBHelper.GetDataSet(selorid1);
            if (dtorid1 != null)
            {
                if (dtorid1.Rows.Count != 0)
                {
                    fbus = dtorid1.Rows[0]["fbus"].ToString();
                    fkind = dtorid1.Rows[0]["fkind"].ToString();
                    fclass = dtorid1.Rows[0]["fclass"].ToString();
                    fctlid_1 = dtorid1.Rows[0]["fctlid_1"].ToString();
                    fctlid_2 = dtorid1.Rows[0]["fctlid"].ToString();
                }
            }
            if (dataSet.Tables["orilayr"].Select("LEN(fctlid)<10").Length > 0 || (dataSet.Tables["orilayr"].Select("LEN(fctlid)=10").Length > 0 && orid2Org.Rows.Count == 0))
            {
                DataTable dtlayr=new DataTable ();
                if (dataSet.Tables["orilayr"].Select("LEN(fctlid)<10").Length > 0) { dtlayr = dataSet.Tables["orilayr"].Select("LEN(fctlid)<10").CopyToDataTable(); }
                else { dtlayr = dataSet.Tables["orilayr"].Select("LEN(fctlid)=10").CopyToDataTable(); }
               
                if (dtlayr.Rows.Count > 0)
                {
                    string fctlid_orid2 = Fct.NewId("orid2");
                    fctlid_layr = Fct.NewId("orilayr");
                    for (int j = 0; j < dtlayr.Rows.Count; j++)
                    {
                        string flyrid = dtlayr.Rows[j]["flyrid"].ToString();
                        decimal lc_fgpm = Fct.Refat(dtlayr.Rows[j]["fgpm"].ToString());
                        decimal lc_fretnsh = Fct.Refat(dtlayr.Rows[j]["fretnsh"].ToString());
                        decimal lc_fretnamt = Fct.Refat(dtlayr.Rows[j]["fretnamt"].ToString());
                        decimal lc_ffacsh = Fct.Refat(dtlayr.Rows[j]["ffacsh"].ToString());
                        decimal lc_ffacamt = Fct.Refat(dtlayr.Rows[j]["ffacamt"].ToString());
                        decimal lc_fexcess = Fct.Refat(dtlayr.Rows[j]["fexcess"].ToString());
                        decimal lc_flimit = Fct.Refat(dtlayr.Rows[j]["flimit"].ToString());

                        if (dtlayr.Rows[j]["fctlid"].ToString().Trim().Length < 10 || orilayrOrg.Rows.Count ==0)
                        {
                            if (j != 0) { fctlid_layr = (int.Parse(fctlid_layr) + 1).ToString().PadLeft(10, '0'); }
                            string Addlayr = "INSERT INTO [dbo].[orilayr]([fctlid],[fctlid_1],[fctlid_2],[flyrid],[fgpm],[fretnsh],[fretnamt],[ffacsh],[ffacamt],[fexcess],[flimit],[fshare_c],[ffacamt_c],[fcomamt_c],[ffacnet_c]) VALUES( " +
                            "'" + fctlid_layr + "','" + fctlid_1 + "','" + fctlid_2 + "','" + flyrid + "','" + lc_fgpm + "','" + lc_fretnsh + "','" + lc_fretnamt + "','" + lc_ffacsh + "','" + lc_ffacamt + "','" + lc_fexcess + "','" + lc_flimit + "',0,0,0,0)";
                            addsql.Add(Addlayr);
                        }

                        DataTable dt = dataSet.Tables["orid2"].Select("fctlid_3 ='" + dtlayr.Rows[j]["fctlid"].ToString() + "'").CopyToDataTable();
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            decimal ln_fcom, ln_fcomamt, ln_fabsnet, ln_fshare, ln_fsi = 0, ln_fsi2 = 0, ln_fgpm, ln_fnpm, ln_fupdshare = 0, ln_fupdsi = 0, ln_fupdsi2 = 0;
                            ln_fcom = Fct.Refat(dt.Rows[i]["fcom"].ToString());
                            ln_fcomamt = Fct.Refat(dt.Rows[i]["Comm Amt"].ToString());
                            ln_fabsnet = Fct.Refat(dt.Rows[i]["Abs Net"].ToString());
                            ln_fshare = Fct.Refat(dt.Rows[i]["Share%"].ToString());
                            ln_fgpm = Fct.Refat(dt.Rows[i]["G Premium"].ToString());
                            ln_fnpm = Fct.Refat(dt.Rows[i]["fnpm"].ToString());
                            ln_fupdshare = Fct.Refat(dt.Rows[i]["Share%"].ToString());
                            string ln_freinsr = dt.Rows[i]["Reinsurer"].ToString();
                            string ln_fbrkr = dt.Rows[i]["fbrkr"].ToString();
                            string ln_fsepdrcr = "", ln_fpnetonly = "", ln_frinsrref = "", ln_fcession = "", ln_friapp = "", ln_fmethod = "1", ln_fcalc = "";
                            if (dt.Rows[i]["fcalc"].ToString() == "1") { ln_fcalc = "1"; } else { ln_fcalc = "2"; }
                            if (dt.Rows[i]["fpnetonly"].ToString() == "1") { ln_fpnetonly = "1"; } else { ln_fpnetonly = "2"; }
                            if (dt.Rows[i]["fsepdrcr"].ToString() == "1") { ln_fsepdrcr = "1"; } else { ln_fsepdrcr = "2"; }
                            int ln_flogseq = int.Parse(dt.Rows[i]["Seq#"].ToString());
                            decimal ln_fexcess = 0, ln_floslmt = 0, ln_fprate = 0;
                            fctlid_orid2 = (int.Parse(fctlid_orid2) + 1).ToString().PadLeft(10, '0');
                            sql = "insert into orid2 ([fctlid_1],[fctlid_2],[fctlid_3],[fctlid],[fbus],[fkind],[fritype],[fsec],[flogseq],[freinsr],[fbrkr],[fcom],[fgrnet],[fpnetonly],[fsepdrcr],[fbrkge],[fgrnet2],[fcomamt],[fbrkgeamt],[fmiscamt1],[fmiscamt2],[fmiscamt3],[fmiscamt],[fabsnet],[fsi],[fsi2],[fupdsi],[fupdsi2],[fosi],[fosi2],[fexcess],[floslmt],[fprate],[fgpm],[fnpm],[fshare],[fupdshare],[foshare],[fcession],[friapp],[frinsrref],[fcalc],[fmethod],[fclass],[floscode],[fppl]) VALUES " +
                              " ('" + fctlid_1 + "','" + fctlid_2 + "','" + fctlid_layr + "','" + fctlid_orid2 + "','" + fbus + "','" + fkind + "','06','" + fsec + "','" + ln_flogseq + "','" + ln_freinsr + "','" + ln_fbrkr + "','" + ln_fcom + "','1','" + ln_fpnetonly + "','" + ln_fsepdrcr + "',0,'','" + ln_fcomamt + "',0,0,0,0,0,'" + ln_fabsnet + "','" + ln_fsi + "','" + ln_fsi2 + "','" + ln_fupdsi + "','" + ln_fupdsi2 + "','" + ln_fupdsi + "','" + ln_fupdsi2 + "','" + ln_fexcess + "','" + ln_floslmt + "','" + ln_fprate + "'," +
                            "'" + ln_fgpm + "','" + ln_fnpm + "','" + ln_fshare + "','" + ln_fupdshare + "','" + ln_fupdshare + "','" + ln_fcession + "','" + ln_friapp + "','" + ln_frinsrref + "','" + ln_fcalc + "','" + ln_fmethod + "','" + fclass + "','','2')";
                            addsql.Add(sql);
                        }
                    }
                    string layrfctlid = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_layr) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='orilayr'";
                    addsql.Add(layrfctlid);
                    if (fctlid_orid2 != "")
                    {
                        string orih2fctlid = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_orid2) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='orid2'";
                        addsql.Add(orih2fctlid);
                    }
                }
            }
            return (string[])addsql.ToArray(typeof(string));
        }

        public string[] CompareDt(DataTable dt1, DataTable dt2, string keyField, string flag)
        {
            //为三个表拷贝表结构
            DataTable dtRetDel = dt1.Clone();
            DataTable dtRetAdd = dt1.Clone();
            DataTable dtRetDif = dt1.Clone();

            ArrayList updsql = new ArrayList();
            int colCount = dt1.Columns.Count;

            DataView dv1 = dt1.DefaultView;
            DataView dv2 = dt2.DefaultView;

            //先以第一个表为参照，看第二个表是修改了还是删除了
            foreach (DataRowView dr1 in dv1)
            {
                dv2.RowFilter = keyField + " = '" + dr1[keyField].ToString() + "'";
                if (dv2.Count > 0)
                {
                    if (!CompareUpdate(dr1, dv2[0]))//比较是否有不同的
                    {
                        dtRetDif.Rows.Add(dv2[0].Row.ItemArray);//修改后
                        continue;
                    }
                }
                else
                {
                    //已经被删除的
                    dtRetDel.Rows.Add(dr1.Row.ItemArray);
                }
            }

            //以第一个表为参照，看记录是否是新增的
            dv2.RowFilter = "";//清空条件
            foreach (DataRowView dr2 in dv2)
            {
                dv1.RowFilter = keyField + " = '" + dr2[keyField].ToString() + "'";
                if (dv1.Count == 0)
                {
                    //新增的
                    dtRetAdd.Rows.Add(dr2.Row.ItemArray);
                }
            }

            if (flag == "orilayr")
            {

                if (dtRetDif != null && dtRetDif.Rows.Count > 0)
                {
                    for (int i = 0; i < dtRetDif.Rows.Count; i++)
                    {
                        decimal lc_fgpm = Fct.Refat(dtRetDif.Rows[i]["fgpm"].ToString());
                        decimal lc_fretnsh = Fct.Refat(dtRetDif.Rows[i]["fretnsh"].ToString());
                        decimal lc_fretnamt = Fct.Refat(dtRetDif.Rows[i]["fretnamt"].ToString());
                        decimal lc_ffacsh = Fct.Refat(dtRetDif.Rows[i]["ffacsh"].ToString());
                        decimal lc_ffacamt = Fct.Refat(dtRetDif.Rows[i]["ffacamt"].ToString());
                        decimal lc_fexcess = Fct.Refat(dtRetDif.Rows[i]["fexcess"].ToString());
                        decimal lc_flimit = Fct.Refat(dtRetDif.Rows[i]["flimit"].ToString());
                        string update = "update orilayr set [fgpm] = '" + lc_fgpm + "',[fretnsh] = '" + lc_fretnsh + "',[fretnamt] = '" + lc_fretnamt + "',[ffacsh] = '" + lc_ffacsh + "',[ffacamt] = '" + lc_ffacamt + "',[fexcess] ='" + lc_fexcess + "',[flimit] = '" + lc_flimit + "' where fctlid='" + dtRetDif.Rows[i]["fctlid"].ToString().Trim() + "'";
                        updsql.Add(update);
                    }
                }

                if (dtRetDel != null && dtRetDel.Rows.Count > 0)
                {
                    for (int i = 0; i < dtRetDel.Rows.Count; i++)
                    {
                        string del = "delete from orilayr where fctlid='" + dtRetDel.Rows[i]["fctlid"].ToString().Trim() + "'";
                        updsql.Add(del);
                    }
                }
            }
            if (flag == "orid2")
            {
                if (dtRetAdd != null && dtRetAdd.Rows.Count > 0)
                {    //MXLINSR
                    string fclass = "", fkind = "", fbus = "", fsec = "", fctlid_1 = "", fctlid_2 = "";
                    string selorid1 = "select * from orih where fctlid ='" + fctlid + "'";
                    DataTable dtorid1 = DBHelper.GetDataSet(selorid1);
                    if (dtorid1 != null)
                    {
                        if (dtorid1.Rows.Count != 0)
                        {
                            fbus = dtorid1.Rows[0]["fbus"].ToString();
                            fkind = dtorid1.Rows[0]["fkind"].ToString();
                            fsec = "";
                            fclass = dtorid1.Rows[0]["fclass"].ToString();
                            fctlid_1 = dtorid1.Rows[0]["fctlid_1"].ToString();
                            fctlid_2 = dtorid1.Rows[0]["fctlid"].ToString();
                        }
                    }
                    string fctlid_orid2 = Fct.NewId("orid2");
                    for (int i = 0; i < dtRetAdd.Rows.Count; i++)
                    {
                        decimal ln_fcom, ln_fcomamt, ln_fabsnet, ln_fshare, ln_fsi=0, ln_fsi2=0, ln_fgpm, ln_fnpm, ln_fupdshare, ln_fupdsi=0, ln_fupdsi2=0;
                        ln_fcom = Fct.Refat(dtRetAdd.Rows[i]["fcom"].ToString());
                        ln_fcomamt = Fct.Refat(dtRetAdd.Rows[i]["Comm Amt"].ToString());
                        ln_fabsnet = Fct.Refat(dtRetAdd.Rows[i]["Abs Net"].ToString());
                        ln_fshare = Fct.Refat(dtRetAdd.Rows[i]["Share%"].ToString());
                        ln_fgpm = Fct.Refat(dtRetAdd.Rows[i]["G Premium"].ToString());
                        ln_fnpm = Fct.Refat(dtRetAdd.Rows[i]["fnpm"].ToString());
                        ln_fupdshare = Fct.Refat(dtRetAdd.Rows[i]["Share%"].ToString());
                        string ln_freinsr = dtRetAdd.Rows[i]["Reinsurer"].ToString();
                        string ln_fbrkr = dtRetAdd.Rows[i]["fbrkr"].ToString();
                        string ln_fsepdrcr = "", ln_fpnetonly = "", ln_frinsrref = "", ln_fcession = "", ln_friapp = "", ln_fmethod = "1", ln_fcalc = "";
                        if (dtRetAdd.Rows[i]["fcalc"].ToString() == "1") { ln_fcalc = "1"; } else { ln_fcalc = "2"; }
                        if (dtRetAdd.Rows[i]["fpnetonly"].ToString() == "1") { ln_fpnetonly = "1"; } else { ln_fpnetonly = "2"; }
                        if (dtRetAdd.Rows[i]["fsepdrcr"].ToString() == "1") { ln_fsepdrcr = "1"; } else { ln_fsepdrcr = "2"; }
                        int ln_flogseq = int.Parse(dtRetAdd.Rows[i]["Seq#"].ToString());
                        decimal ln_fexcess = 0, ln_floslmt = 0, ln_fprate = 0;

                        if (i != 0) { fctlid_orid2 = (int.Parse(fctlid_orid2) + 1).ToString().PadLeft(10, '0'); }
                        string sql = "insert into orid2 ([fctlid_1],[fctlid_2],[fctlid_3],[fctlid],[fbus],[fkind],[fritype],[fsec],[flogseq],[freinsr],[fbrkr],[fcom],[fgrnet],[fpnetonly],[fsepdrcr],[fbrkge],[fgrnet2],[fcomamt],[fbrkgeamt],[fmiscamt1],[fmiscamt2],[fmiscamt3],[fmiscamt],[fabsnet],[fsi],[fsi2],[fupdsi],[fupdsi2],[fosi],[fosi2],[fexcess],[floslmt],[fprate],[fgpm],[fnpm],[fshare],[fupdshare],[foshare],[fcession],[friapp],[frinsrref],[fcalc],[fmethod],[fclass],[floscode],[fppl]) VALUES " +
                          " ('" + fctlid_1 + "','" + fctlid_2 + "','" + dtRetAdd.Rows[i]["fctlid_3"].ToString() + "','" + fctlid_orid2 + "','" + fbus + "','" + fkind + "','06','" + fsec + "','" + ln_flogseq + "','" + ln_freinsr + "','" + ln_fbrkr + "','" + ln_fcom + "','1','" + ln_fpnetonly + "','" + ln_fsepdrcr + "',0,'','" + ln_fcomamt + "',0,0,0,0,0,'" + ln_fabsnet + "','" + ln_fsi + "','" + ln_fsi2 + "','" + ln_fupdsi + "','" + ln_fupdsi2 + "','" + ln_fupdsi + "','" + ln_fupdsi2 + "','" + ln_fexcess + "','" + ln_floslmt + "','" + ln_fprate + "'," +
                        "'" + ln_fgpm + "','" + ln_fnpm + "','" + ln_fshare + "','" + ln_fupdshare + "','" + ln_fupdshare + "','" + ln_fcession + "','" + ln_friapp + "','" + ln_frinsrref + "','" + ln_fcalc + "','" + ln_fmethod + "','" + fclass + "','','2')";

                        updsql.Add(sql);

                    }
                    if (fctlid_orid2 != "")
                    {
                        string updfctlid = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_orid2) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='orid2'";
                        updsql.Add(updfctlid);
                    }
                }

                if (dtRetDif != null && dtRetDif.Rows.Count > 0)
                {
                    for (int i = 0; i < dtRetDif.Rows.Count; i++)
                    {
                        decimal ln_fcom, ln_fcomamt, ln_fabsnet, ln_fshare, ln_fsi=0, ln_fsi2=0, ln_fgpm, ln_fnpm, ln_fupdshare, ln_fupdsi=0, ln_fupdsi2=0;
                        ln_fcom = Fct.Refat(dtRetDif.Rows[i]["fcom"].ToString());
                        ln_fcomamt = Fct.Refat(dtRetDif.Rows[i]["Comm Amt"].ToString());
                        ln_fabsnet = Fct.Refat(dtRetDif.Rows[i]["Abs Net"].ToString());
                        ln_fshare = Fct.Refat(dtRetDif.Rows[i]["Share%"].ToString());
                        ln_fgpm = Fct.Refat(dtRetDif.Rows[i]["G Premium"].ToString());
                        ln_fnpm = Fct.Refat(dtRetDif.Rows[i]["fnpm"].ToString());
                        ln_fupdshare = Fct.Refat(dtRetDif.Rows[i]["Share%"].ToString());
                        string ln_freinsr = dtRetDif.Rows[i]["Reinsurer"].ToString();
                        string ln_fbrkr = dtRetDif.Rows[i]["fbrkr"].ToString();
                        string ln_fsepdrcr = "", ln_fpnetonly = "", ln_frinsrref = "", ln_fcession = "", ln_friapp = "", ln_fmethod = "1", ln_fcalc = "";
                        if (dtRetDif.Rows[i]["fcalc"].ToString() == "1") { ln_fcalc = "1"; } else { ln_fcalc = "2"; }
                        if (dtRetDif.Rows[i]["fpnetonly"].ToString() == "1") { ln_fpnetonly = "1"; } else { ln_fpnetonly = "2"; }
                        if (dtRetDif.Rows[i]["fsepdrcr"].ToString() == "1") { ln_fsepdrcr = "1"; } else { ln_fsepdrcr = "2"; }
                        int ln_flogseq = int.Parse(dtRetDif.Rows[i]["Seq#"].ToString());
                        decimal ln_fexcess = 0, ln_floslmt = 0, ln_fprate = 0;

                        string sql = "update orid2 SET [fcom] = '" + ln_fcom + "',[fcomamt] = '" + ln_fcomamt + "',[fabsnet] = '" + ln_fabsnet + "',[fshare] = '" + ln_fshare + "',[fsi] = '" + ln_fsi + "',[fsi2] = '" + ln_fsi2 + "',[fupdshare] = '" + ln_fupdshare + "',[fupdsi] = '" + ln_fupdsi + "',[fupdsi2] = '" + ln_fupdsi2 + "',[foshare] = '" + ln_fupdshare + "',[fosi] ='" + ln_fupdsi + "',[fosi2] = '" + ln_fupdsi2 + "',[fgpm] = '" + ln_fgpm + "',[fnpm] = '" + ln_fnpm + "',[freinsr] = '" + ln_freinsr + "' ,[fbrkr] = '" + ln_fbrkr + "',[fpnetonly] = '" + ln_fpnetonly + "',[fmethod] = '" + ln_fmethod + "',[fcalc] = '" + ln_fcalc + "',[fexcess] = '" + ln_fexcess + "',[floslmt] = '" + ln_floslmt + "',[fprate] = '" + ln_fprate + "',[fsepdrcr] = '" + ln_fsepdrcr + "' WHERE fctlid = '" + dtRetDif.Rows[i]["fctlid"].ToString() + "'";
                        updsql.Add(sql);
                    }
                }

                if (dtRetDel != null && dtRetDel.Rows.Count > 0)
                {
                    for (int i = 0; i < dtRetDel.Rows.Count; i++)
                    {
                        string del = "delete from orid2 where fctlid='" + dtRetDel.Rows[i]["fctlid"].ToString().Trim() + "'";
                        updsql.Add(del);
                    }
                }
            }

            return (string[])updsql.ToArray(typeof(string));
        }

        //比较是否有不同的
        private static bool CompareUpdate(DataRowView dr1, DataRowView dr2)
        {
            //行里只要有一项不一样，整个行就不一样,无需比较其它
            object val1;
            object val2;
            for (int i = 0; i < dr1.Row.ItemArray.Length; i++)
            {
                val1 = dr1[i];
                val2 = dr2[i];
                if (!val1.Equals(val2))
                {
                    return false;
                }
            }
            return true;
        }

        private void appri_Click(object sender, EventArgs e)
        {
            RIApp tempform = new RIApp(this);
            tempform.fctlid_1 = fctlid;
            tempform.fritype = "06";
            tempform.FillData("", "", "");
            tempform.ShowDialog();
        }

        private void NonProp_Leave(object sender, EventArgs e)
        {
            Skip_Validate = true;
        }

        private void NonProp_Enter(object sender, EventArgs e)
        {
            Skip_Validate = false;
        }

    }
}
