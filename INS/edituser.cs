using INS.INSClass;
using InsEnvironment;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace INS
{
    public partial class edituser : Form
    {
        DES des = new DES();
        DBConnect operate = new DBConnect();
        private Boolean addflag = false, updateflag = false;
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public string UserName = InsEnvironment.LoginUser.GetUserCode();
        public string right1 = "";
        public string right2 = "";
        public string right3 = "";
        public string right4 = "";
        public string right5 = "";
        public string right6 = "";
        public string right7 = "";
        public string right8 = "";
        public string right9 = "";
        public string right10 = "";
        public string right11 = "";
        public string right12 = "";
        public string right13 = "";
        public string right14 = "";
        public string right15 = "";
        public string cyle = "";
        public int life;
        public string len = "";
        private int minPasslen;
        private int maxPasslen;
        private int passLife;
        private int passCycle;
        public string oldrow;

        public edituser()
        {
            InitializeComponent();
            pswControl();
            foreach (Control control in panel2.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件             //添加事件
            }
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
            {
                SendKeys.Send("{TAB}");
            }
        }
        private void edituser_Load(object sender, EventArgs e)
        {
            muser.DataSource = DBHelper.GetDataSet("select * from " + Dbm + "muser");
            int row = Fct.snFat(label7.Text);
            muser.CurrentCell = muser.Rows[row].Cells["fctlid"];
            btnctrlback("");
            buttonload("");
        }

        void pswControl()
        {
            string str = "select * from xsyscomp";
            DataTable dt = new DataTable();
            dt = operate.GetTable(str);
            len = dt.Rows[0]["Fpasslen"].ToString();
            life = int.Parse(dt.Rows[0]["Fpasslife"].ToString());
            cyle = dt.Rows[0]["Fpasscyle"].ToString();
            minPasslen = CompInfo.GetPassLen();
            maxPasslen = 10;
            passLife = CompInfo.GetPasslife();
            passCycle = CompInfo.GetPassCyle();
        }

        private void muser_SelectionChanged(object sender, EventArgs e)
        {
            muserchg();
        }

        void muserchg() {
            int row;
            if (muser.CurrentRow == null) { row = 0; }
            else { row = muser.CurrentRow.Index; }
            try
            {
                fname.Text = muser.Rows[row].Cells["fname"].Value.ToString();
                fcode.Text = muser.Rows[row].Cells["fcode"].Value.ToString();
                fcname.Text = muser.Rows[row].Cells["fcname"].Value.ToString();
                fpassword.Text = muser.Rows[row].Cells["fpassword"].Value.ToString();
                fpassword.Text = UtilityFunc.EnCode_Str(fpassword.Text.Trim(), false).Trim();
                fenable.Checked = Fct.sbFat(muser.Rows[row].Cells["fenable"].Value.ToString());
                failure.Checked = Fct.sbFat(muser.Rows[row].Cells["ffailure"].Value.ToString());
                xsuper.Checked = Fct.sbFat(muser.Rows[row].Cells["xsuper"].Value.ToString());
                label7.Text = row.ToString();
                comboBox1.SelectedItem = "COIL";
                try
                {
                    DateTime b = Convert.ToDateTime(muser.Rows[row].Cells["fvdate"].Value.ToString().Trim());
                    fvdate.Text = b.ToString("yyyy.MM.dd");
                }
                catch { }
                if (comboBox1.SelectedItem.Equals("COIL"))
                {
                    string s = muser.Rows[row].Cells[9].Value.ToString();
                    if (s != "")
                    {
                        string[] a = new string[s.Length];
                        for (int i = 0; i < s.Length; i++)
                        {
                            a[i] = s[i].ToString();
                        }
                        coil1.Checked = Fct.sbFat(a[0]);
                        coil2.Checked = Fct.sbFat(a[1]);
                        coil3.Checked = Fct.sbFat(a[2]);
                        coil4.Checked = Fct.sbFat(a[3]);
                        coil5.Checked = Fct.sbFat(a[4]);
                        coil6.Checked = Fct.sbFat(a[5]);
                        coil7.Checked = Fct.sbFat(a[6]);
                        coil8.Checked = Fct.sbFat(a[7]);
                        coil9.Checked = Fct.sbFat(a[8]);
                        coil10.Checked = Fct.sbFat(a[9]);
                    }
                }
            }
            catch { }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            buttonload("add");
            addflag = true;
            DataTable dtmuser = muser.DataSource as DataTable;
            DataRow newCustomersRow = dtmuser.NewRow();
            dtmuser.Rows.InsertAt(newCustomersRow, dtmuser.Rows.Count);
            muser.DataSource = dtmuser;
            muser.CurrentCell = muser.Rows[dtmuser.Rows.Count - 1].Cells[1];

            foreach (Control ctrl in panel1.Controls)
            {
                if (ctrl is CheckBox)
                {
                    ((CheckBox)ctrl).Checked = false;
                    ((CheckBox)ctrl).Enabled = true;
                }
            }
            foreach (Control ctrl in panel2.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                    ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                    ((TextBox)ctrl).ReadOnly = false;
                }
                if (ctrl is MaskedTextBox)
                {
                    ((MaskedTextBox)ctrl).Text = "";
                    ((MaskedTextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                    ((MaskedTextBox)ctrl).ReadOnly = false;
                }
                if (ctrl is CheckBox)
                {
                    ((CheckBox)ctrl).Enabled = true;
                }
            }
            DateTime newExpiry = Convert.ToDateTime(DateTime.Now.ToShortDateString());
            newExpiry = newExpiry.AddDays(passLife - 1);
            fvdate.Text = newExpiry.ToString("yyyy.MM.dd");
            fpassword.Enabled = true;
            oldrow = label7.Text;
        }

        //对修改该内容进行保存
        private void btnUpdate_Click(object sender, EventArgs e)
        {
            buttonload("update");
            updateflag = true;
            if (muser.DataSource != null && muser.RowCount > 0)
            {
                foreach (Control ctrl in panel1.Controls)
                {
                    if (ctrl is CheckBox)
                    {
                        ((CheckBox)ctrl).Enabled = true;
                    }
                }
                foreach (Control ctrl in panel2.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                        ((TextBox)ctrl).ReadOnly = false;
                    }
                    if (ctrl is CheckBox)
                    {
                        ((CheckBox)ctrl).Enabled = true;
                    }
                }
            }
            fpassword.Enabled = false;
            oldrow = label7.Text;
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            buttonload("delete");
            if (muser.RowCount != 0)
            {
                try
                {
                    DataSet dsmuser = muser.DataSource as DataSet;
                    DataTable dtmuser = muser.DataSource as DataTable;
                    dtmuser.Rows.RemoveAt(muser.CurrentCell.RowIndex);
                    muser.DataSource = dtmuser;
                    muser.CurrentCell = muser.Rows[dtmuser.Rows.Count - 1].Cells["fctlid"];
                }
                catch { }
            }
            if (muser.RowCount == 0)
            {
                btnctrlback("del");
            }
        }

        void btnctrlback(string flag)
        {
            addflag = false; updateflag = false;
            foreach (Control ctrl in panel1.Controls)
            {
                if (ctrl is CheckBox)
                {
                    ((CheckBox)ctrl).Enabled = false;
                }
            }
            foreach (Control ctrl in panel2.Controls)
            {
                if (ctrl is TextBox)
                {
                    if (flag == "del") { ((TextBox)ctrl).Text = ""; }
                    ((TextBox)ctrl).BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    ((TextBox)ctrl).ReadOnly = true;
                }
                if (ctrl is MaskedTextBox)
                {
                    if (flag == "del") { ((MaskedTextBox)ctrl).Text = ""; }
                    ((MaskedTextBox)ctrl).BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    ((MaskedTextBox)ctrl).ReadOnly = true;
                }
                if (ctrl is CheckBox)
                {
                    ((CheckBox)ctrl).Enabled = false;
                }
            }
        }

        private void BtnRight_Click(object sender, EventArgs e)
        {
            if (comboBox1.SelectedItem.Equals("COIL"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right1 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("COIS"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right2 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Business"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right3 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("DR/CR"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "2";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right4 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Outward Reinsurance"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right5 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Renewal Notice"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right6 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Claims"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "2";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right7 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("DR/CR Claims"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right8 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Claims Payment Approval"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right9 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Claims Cover letter"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right10 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("A/Cs"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right11 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Master (Business)"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right12 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Master (Claims)"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right13 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Master (Treaty)"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right14 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Management Report I"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "2";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right15 = label5.Text.ToString();
            }
            muser["fright01", muser.CurrentCell.RowIndex].Value = right1;
            muser["fright02", muser.CurrentCell.RowIndex].Value = right2;
            muser["fright03", muser.CurrentCell.RowIndex].Value = right3;
            muser["fright3a", muser.CurrentCell.RowIndex].Value = right4;
            muser["fright3b", muser.CurrentCell.RowIndex].Value = right5;
            muser["fright3c", muser.CurrentCell.RowIndex].Value = right6;
            muser["fright04", muser.CurrentCell.RowIndex].Value = right7;
            muser["fright4a", muser.CurrentCell.RowIndex].Value = right8;
            muser["fright4b", muser.CurrentCell.RowIndex].Value = right9;
            muser["fright4c", muser.CurrentCell.RowIndex].Value = right10;
            muser["fright05", muser.CurrentCell.RowIndex].Value = right11;
            muser["fright06", muser.CurrentCell.RowIndex].Value = right12;
            muser["fright07", muser.CurrentCell.RowIndex].Value = right13;
            muser["fright08", muser.CurrentCell.RowIndex].Value = right14;
            muser["fright09", muser.CurrentCell.RowIndex].Value = right15;
        }

        private Boolean txtValid(TextBox text_obj)
        {
            Boolean ll_ok;
            if ((!addflag && !updateflag) || text_obj.ReadOnly) { ll_ok = false; }
            else { ll_ok = Validation.CheckEmptyString(text_obj); }
            return ll_ok;
        }

        private string CheckConfirm()
        {
            DataTable dt2 = muser.DataSource as DataTable;
            foreach (DataRow row in dt2.Rows)
            {
                if (row["fcode"].ToString().Trim() == "")
                {
                    MessageBox.Show("User ID Can't be Empty", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return "fcode";
                }
                else if (row["fname"].ToString().Trim() == "")
                {
                    MessageBox.Show("User Name Can't be Empty", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return "fname";
                }
                else if (row["fcname"].ToString().Trim() == "")
                {
                    MessageBox.Show("User CName Can't be Empty", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return "fcname";
                }
                else if (row["fpassword"].ToString().Trim() == "")
                {
                    MessageBox.Show("User Psd Can't be Empty", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return "fpassword";
                }
                else if (row["fpassword"].ToString().Trim().Length < 6)
                {
                    MessageBox.Show("User Psd Can't be less 6 char", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return "fpassword";
                }
                else if (row["fright01"].ToString() == "")
                {
                    MessageBox.Show("COIL Can't be Empty", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return "right1";
                }
                else if (row["fright02"].ToString() == "")
                {
                    MessageBox.Show("COIS Can't be Empty", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return "right2";
                }
                else if (row["fright03"].ToString() == "")
                {
                    MessageBox.Show("Business Can't be Empty", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return "right3";
                }
                else if (row["fright3a"].ToString() == "")
                {
                    MessageBox.Show("DR/CR Can't be Empty", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return "right4";
                }
                else if (row["fright3b"].ToString() == "")
                {
                    MessageBox.Show("Outward Reinsurance Can't be Empty", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return "right5";
                }
                else if (row["fright3c"].ToString() == "")
                {
                    MessageBox.Show("Renewal Notice Can't be Empty", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return "right6";
                }
                else if (row["fright04"].ToString() == "")
                {
                    MessageBox.Show("Claims Can't be Empty", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return "right7";
                }
                else if (row["fright4a"].ToString() == "")
                {
                    MessageBox.Show("DR/CR Claims Can't be Empty", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return "right8";
                }
                else if (row["fright4b"].ToString() == "")
                {
                    MessageBox.Show("Claims Payment Approval Can't be Empty", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return "right9";
                }
                else if (row["fright4c"].ToString() == "")
                {
                    MessageBox.Show("Claims Cover Letter Can't be Empty", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return "right10";
                }
                else if (row["fright05"].ToString() == "")
                {
                    MessageBox.Show("A/Cs Can't be Empty", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return "right11";
                }
                else if (row["fright06"].ToString() == "")
                {
                    MessageBox.Show("Master (Business) Can't be Empty", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return "right12";
                }
                else if (row["fright07"].ToString() == "")
                {
                    MessageBox.Show("Master (Claims) Can't be Empty", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return "right13";
                }
                else if (row["fright08"].ToString() == "")
                {
                    MessageBox.Show("Master (Treaty) Can't be Empty", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return "right14";
                }
                else if (row["fright09"].ToString() == "")
                {
                    MessageBox.Show("Management report Can't be Empty", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return "right15";
                }
                else return "";
            }

            return "";
        }

        private void btnConfirm_Click(object sender, EventArgs e)
        {
            string[] sql = confirm();
            string connectionString = ConfigurationManager.ConnectionStrings["mdata"].ConnectionString;
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                SqlCommand command = connection.CreateCommand();
                SqlTransaction transaction;
                transaction = connection.BeginTransaction("SampleTransaction");
                command.Connection = connection;
                command.Transaction = transaction;

                try
                {
                    if (sql != null)
                    {
                        for (int i = 0; i < sql.Length; i++)
                        {
                            if (sql[i] != "" && sql[i] != null)
                            {
                                command.CommandText = sql[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    transaction.Commit();
                    muser.DataSource = DBHelper.GetDataSet("select * from " + Dbm + "muser");
                    muser.CurrentCell = muser.Rows[Fct.snFat(oldrow)].Cells["fctlid"];
                    muserchg();
                    btnctrlback("");
                    buttonload("");
                }
                catch (Exception ex)
                {
                    MessageBox.Show("Haven't Been Edited", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                    Console.WriteLine("  Message: {0}", ex.Message);
                    try
                    {
                        transaction.Rollback();
                    }
                    catch (Exception ex2)
                    {
                        Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                        Console.WriteLine("  Message: {0}", ex2.Message);
                    }
                }
            }
        }

        void pswd()
        {
            int row;
            if (muser.CurrentRow == null) { row = 0; }
            else { row = muser.CurrentRow.Index; }

            string newpwd = fpassword.Text.Trim();
            string newpwd2 = fpassword.Text.Trim();
            if (newpwd.Contains(" "))
            {
                MessageBox.Show("Invalid new password!", "Warning",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (newpwd.Length < minPasslen || newpwd.Length > maxPasslen)
            {
                MessageBox.Show("Invalid new password length! Must be between " + minPasslen.ToString("G") + " to "
                                + maxPasslen.ToString("G") + " characters", "Warning",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                fpassword.Text = "";
                fpassword.Focus();

                return;
            }

            if (newpwd.Length < maxPasslen)
                newpwd = newpwd.PadRight(maxPasslen, ' ');
            else
                newpwd = newpwd.Substring(0, maxPasslen);

            int strLength = newpwd2.Length;
            Boolean contains_alpha = false, contains_num = false, legal_str = false;

            Int32 ascii_no;

            for (int i = 0; i != strLength; i++)
            {
                ascii_no = (int)newpwd2[i];

                if (!contains_alpha &&
                    ((ascii_no >= (int)'a' && ascii_no <= (int)'z') || (ascii_no >= (int)'A' && ascii_no <= (int)'Z')))
                    contains_alpha = true;

                if (!contains_num && (ascii_no >= (int)'0' && ascii_no <= (int)'9'))
                    contains_num = true;

                if ((ascii_no >= (int)'0' && ascii_no <= (int)'9') ||
                    (ascii_no >= (int)'a' && ascii_no <= (int)'z') ||
                    (ascii_no >= (int)'A' && ascii_no <= (int)'Z'))
                    legal_str = true;
                else
                    legal_str = false;

                if (!legal_str)
                    break;
            }

            if (!contains_alpha || !contains_num || !legal_str)
            {
                MessageBox.Show("Password must consists of alpha and numeric characters!", "Warning",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);

                fpassword.Text = "";
                fpassword.Focus();

                return;
            }


            string passhis = "";
            if (muser.Rows[row].Cells["fpasshis"].Value != null && muser.Rows[row].Cells["fpasshis"].Value.ToString().Trim() != "")
            {
                passhis = muser.Rows[row].Cells["fpasshis"].Value.ToString().Substring(0, maxPasslen * passCycle - 1);
            }
            if (passhis != null)
            {
                newpwd = UtilityFunc.EnCode_Str(newpwd, true);
                if (passhis.Contains(newpwd))
                {
                    MessageBox.Show("Recurrent password is not allowed!", "Warning",
                                     MessageBoxButtons.OK, MessageBoxIcon.Information);
                    fpassword.Text = "";
                    fpassword.Focus();

                    return;
                }
            }
        }

        private string[] confirm()
        {
            DateTime time = DateTime.Now;
            if (CheckConfirm() != "") { }
            else
            {
                String Sql = "select * from " + Dbm + "muser";
                DataTable dt0 = DBHelper.GetDataSet(Sql);
                DataTable dt = muser.DataSource as DataTable;
                if (dt != null)
                {
                    string[] updatesql = CompareDt(dt0, dt, "fctlid");
                    return updatesql;
                }
            }
            return null;
        }

        public string[] CompareDt(DataTable dt1, DataTable dt2, string keyField)
        {
            DateTime time = DateTime.Now;
            //为三个表拷贝表结构
            DataTable dtRetDel = dt1.Clone();
            DataTable dtRetAdd = dt1.Clone();
            DataTable dtRetDif = dt1.Clone();

            ArrayList updsql = new ArrayList();
            int colCount = dt1.Columns.Count;

            DataView dv1 = dt1.DefaultView;
            DataView dv2 = dt2.DefaultView;

            //先以第一个表为参照，看第二个表是修改了还是删除了
            foreach (DataRowView dr1 in dv1)
            {
                dv2.RowFilter = keyField + " = '" + dr1[keyField].ToString() + "'";
                if (dv2.Count > 0)
                {
                    if (!CompareUpdate(dr1, dv2[0]))//比较是否有不同的
                    {
                        dtRetDif.Rows.Add(dv2[0].Row.ItemArray);//修改后
                        continue;
                    }
                }
                else
                {
                    //已经被删除的
                    dtRetDel.Rows.Add(dr1.Row.ItemArray);
                }
            }

            //以第一个表为参照，看记录是否是新增的
            dv2.RowFilter = "";//清空条件
            foreach (DataRowView dr2 in dv2)
            {
                dv1.RowFilter = keyField + " = '" + dr2[keyField].ToString() + "'";
                if (dv1.Count == 0)
                {
                    //新增的
                    dtRetAdd.Rows.Add(dr2.Row.ItemArray);
                }
            }

            if (dtRetAdd != null && dtRetAdd.Rows.Count > 0)
            {
                string fctlid_muser = Fct.NewId("USRID");
                for (int i = 0; i < dtRetAdd.Rows.Count; i++)
                {
                    string newpwd = Fct.stFat(dtRetAdd.Rows[i]["fpassword"]);
                    if (newpwd.Length < maxPasslen)
                        newpwd = newpwd.PadRight(maxPasslen, ' ');
                    else
                        newpwd = newpwd.Substring(0, maxPasslen);
                    newpwd = UtilityFunc.EnCode_Str(newpwd, true);

                    Boolean ll_ok = false;
                    string sqlstr = "select * from muser where fcode ='" + Fct.stFat(dtRetAdd.Rows[i]["fcode"]) + "'";
                    SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["mdata"].ConnectionString);
                    SqlCommand cmd = new SqlCommand(sqlstr, con);
                    con.Open();
                    using (SqlDataReader sdr = cmd.ExecuteReader())
                    {
                        if (sdr.HasRows && sdr.Read())
                        {
                            ll_ok = true;
                            sdr.Close();
                            sdr.Dispose();
                        }
                    }
                    if (ll_ok) { MessageBox.Show("Duplicate User"); fcode.Focus(); }
                    else
                    {
                        if (i != 0) { fctlid_muser = (int.Parse(fctlid_muser) + 1).ToString().PadLeft(10, '0'); }
                        string insertsql = "INSERT INTO [dbo].[muser]([fctlid],[fcode],[fname],[fcname],[fpassword],[fpasshis],[fapcode],[fapparm], " +
                                   "[fappas],[fright01],[fright02],[fright03],[fright3a],[fright3b],[fright3c],[fright04],[fright4a],[fright4b], " +
                                   "[fright4c],[fright05],[fright06],[fright07],[fright08],[fright09],[bright03],[bright04],[bright05],[bright06], " +
                                   "[bright07],[bright08],[fenable],[fvdate],[ffailure],[xsuper],[finpuser],[finpdate],[fupduser],[fupddate],[fctldel]) " +
                                   "VALUES('" + fctlid_muser + "','" + Fct.stFat(dtRetAdd.Rows[i]["fcode"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fname"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fcname"]) + "', " +
                                   "'" + newpwd + "','" + newpwd + "','','','','" + Fct.stFat(dtRetAdd.Rows[i]["fright01"]) + "', " +
                                   "'" + Fct.stFat(dtRetAdd.Rows[i]["fright02"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fright03"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fright3a"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fright3b"]) + "', " +
                                   "'" + Fct.stFat(dtRetAdd.Rows[i]["fright3c"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fright04"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fright4a"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fright4b"]) + "', " +
                                   "'" + Fct.stFat(dtRetAdd.Rows[i]["fright4c"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fright05"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fright06"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fright07"]) + "', " +
                                   "'" + Fct.stFat(dtRetAdd.Rows[i]["fright08"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fright09"]) + "','','','','','','','1','" + DateTime.Now.AddDays(life).ToString("yyyy-MM-dd HH:mm:ss") + "', " +
                                   "'" + dtRetAdd.Rows[i]["ffailure"] + "','" + dtRetAdd.Rows[i]["xsuper"] + "','" + UserName + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','" + UserName + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','')"; ;
                        updsql.Add(insertsql);
                    }
                    string sqlfctlid = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_muser) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='USRID'";
                    updsql.Add(sqlfctlid);
                }
            }
            if (dtRetDif != null && dtRetDif.Rows.Count > 0)
            {
                for (int i = 0; i < dtRetDif.Rows.Count; i++)
                {
                    string update = "UPDATE [dbo].[muser] SET [fcode] ='" + Fct.stFat(dtRetDif.Rows[i]["fcode"].ToString().Trim()) + "',[fname] = '" + Fct.stFat(dtRetDif.Rows[i]["fname"].ToString().Trim()) + "', " +
                                    "[fcname] = '" + Fct.stFat(dtRetDif.Rows[i]["fcname"].ToString().Trim()) + "', " +
                                    "[fright01] = '" + Fct.stFat(dtRetDif.Rows[i]["fright01"]) + "',[fright02] = '" + Fct.stFat(dtRetDif.Rows[i]["fright02"]) + "',[fright03] = '" + Fct.stFat(dtRetDif.Rows[i]["fright03"]) + "', " +
                                    "[fright3a] = '" + Fct.stFat(dtRetDif.Rows[i]["fright3a"]) + "',[fright3b] = '" + Fct.stFat(dtRetDif.Rows[i]["fright3b"]) + "',[fright3c] = '" + Fct.stFat(dtRetDif.Rows[i]["fright3c"]) + "', " +
                                    "[fright04] = '" + Fct.stFat(dtRetDif.Rows[i]["fright04"]) + "',[fright4a] = '" + Fct.stFat(dtRetDif.Rows[i]["fright4a"]) + "',[fright4b] = '" + Fct.stFat(dtRetDif.Rows[i]["fright4b"]) + "', " +
                                    "[fright4c] = '" + Fct.stFat(dtRetDif.Rows[i]["fright4c"]) + "',[fright05] = '" + Fct.stFat(dtRetDif.Rows[i]["fright05"]) + "',[fright06] = '" + Fct.stFat(dtRetDif.Rows[i]["fright06"]) + "', " +
                                    "[fright07] = '" + Fct.stFat(dtRetDif.Rows[i]["fright07"]) + "',[fright08] = '" + Fct.stFat(dtRetDif.Rows[i]["fright08"]) + "',[fright09] = '" + Fct.stFat(dtRetDif.Rows[i]["fright09"]) + "', " +
                                    "[fenable] = '" + dtRetDif.Rows[i]["fenable"] + "',[fvdate] = '" + DateTime.Now.AddDays(life).ToString("yyyy-MM-dd HH:mm:ss") + "',[ffailure] = '" + dtRetDif.Rows[i]["ffailure"] + "', " +
                                    "[xsuper] = '" + dtRetDif.Rows[i]["xsuper"] + "',[fupduser] = '" + UserName + "',[fupddate] = '" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' " +
                                    "where fctlid='" + dtRetDif.Rows[i]["fctlid"].ToString().Trim() + "'";
                    updsql.Add(update);
                }
            }

            if (dtRetDel != null && dtRetDel.Rows.Count > 0)
            {
                for (int i = 0; i < dtRetDel.Rows.Count; i++)
                {
                    string del = "delete from muser where fctlid='" + dtRetDel.Rows[i]["fctlid"].ToString().Trim() + "'";
                    updsql.Add(del);
                }
            }

            return (string[])updsql.ToArray(typeof(string));
        }

        //比较是否有不同的
        private static bool CompareUpdate(DataRowView dr1, DataRowView dr2)
        {
            //行里只要有一项不一样，整个行就不一样,无需比较其它
            object val1;
            object val2;
            for (int i = 0; i < dr1.Row.ItemArray.Length; i++)
            {
                val1 = dr1[i];
                val2 = dr2[i];
                if (!val1.Equals(val2))
                {
                    return false;
                }
            }
            return true;
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            muser.DataSource = DBHelper.GetDataSet("select * from " + Dbm + "muser");
            muser.CurrentCell = muser.Rows[Fct.snFat(oldrow)].Cells["fctlid"];
            muserchg();
            btnctrlback("");
            buttonload("");
        }

        private void fcode_Validated(object sender, EventArgs e)
        {
            if (txtValid((TextBox)sender))
            {
                muser["fcode", muser.CurrentCell.RowIndex].Value = fcode.Text;
            }
        }

        private void fname_Validated(object sender, EventArgs e)
        {
            if (txtValid((TextBox)sender))
            {
                muser["fname", muser.CurrentCell.RowIndex].Value = fname.Text;
            }
        }

        private void fcname_Validated(object sender, EventArgs e)
        {
            if (txtValid((TextBox)sender))
            {
                muser["fcname", muser.CurrentCell.RowIndex].Value = fcname.Text;
            }
        }

        private void fpassword_Validated(object sender, EventArgs e)
        {
            if (addflag == true || updateflag == true)
            {
                pswd();
                if (txtValid((TextBox)sender))
                {
                    muser["fpassword", muser.CurrentCell.RowIndex].Value = fpassword.Text;
                }
            }
        }

        private void fvdate_Validated(object sender, EventArgs e)
        {
            DateTime dateTime;
            if (!DateTime.TryParseExact(fvdate.Text, "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out dateTime))
            {
                MessageBox.Show("Invalid Value");
                fvdate.Focus();
                fvdate.SelectAll();
                return;
            }
            else { muser["fvdate", muser.CurrentCell.RowIndex].Value = fvdate.Text; }

        }

        private void xsuper_Validated(object sender, EventArgs e)
        {
            muser["xsuper", muser.CurrentCell.RowIndex].Value = Fct.sbtFat(xsuper.Checked);
        }

        private void failure_Validated(object sender, EventArgs e)
        {
            muser["ffailure", muser.CurrentCell.RowIndex].Value = Fct.sbtFat(failure.Checked);
        }

        private void fenable_Validated(object sender, EventArgs e)
        {
            muser["fenable", muser.CurrentCell.RowIndex].Value = Fct.sbtFat(fenable.Checked);
        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            int row = muser.CurrentRow.Index;
            string s = "";
            if (comboBox1.SelectedItem.Equals("COIL"))
            {
                s = muser.Rows[row].Cells["fright01"].Value.ToString();
            }
            if (comboBox1.SelectedItem.Equals("COIS"))
            {
                s = muser.Rows[row].Cells["fright02"].Value.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Business"))
            {
                s = muser.Rows[row].Cells["fright03"].Value.ToString();
            }
            if (comboBox1.SelectedItem.Equals("DR/CR"))
            {
                s = muser.Rows[row].Cells["fright3a"].Value.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Outward Reinsurance"))
            {
                s = muser.Rows[row].Cells["fright3b"].Value.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Renewal Notice"))
            {
                s = muser.Rows[row].Cells["fright3c"].Value.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Claims"))
            {
                s = muser.Rows[row].Cells["fright04"].Value.ToString();
            }
            if (comboBox1.SelectedItem.Equals("DR/CR Claims"))
            {
                s = muser.Rows[row].Cells["fright4a"].Value.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Claims Payment Approval"))
            {
                s = muser.Rows[row].Cells["fright4b"].Value.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Claims Cover letter"))
            {
                s = muser.Rows[row].Cells["fright4c"].Value.ToString();
            }
            if (comboBox1.SelectedItem.Equals("A/Cs"))
            {
                s = muser.Rows[row].Cells["fright05"].Value.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Master (Business)"))
            {
                s = muser.Rows[row].Cells["fright06"].Value.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Master (Claims)"))
            {
                s = muser.Rows[row].Cells["fright07"].Value.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Master (Treaty)"))
            {
                s = muser.Rows[row].Cells["fright08"].Value.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Master (Treaty)"))
            {
                s = muser.Rows[row].Cells["fright08"].Value.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Management Report I"))
            {
                s = muser.Rows[row].Cells["fright09"].Value.ToString();
            }
            if (s != "")
            {
                string[] a = new string[s.Length];
                for (int i = 0; i < s.Length; i++)
                {
                    a[i] = s[i].ToString();
                }
                coil1.Checked = Fct.sbFat(a[0]);
                coil2.Checked = Fct.sbFat(a[1]);
                coil3.Checked = Fct.sbFat(a[2]);
                coil4.Checked = Fct.sbFat(a[3]);
                coil5.Checked = Fct.sbFat(a[4]);
                coil6.Checked = Fct.sbFat(a[5]);
                coil7.Checked = Fct.sbFat(a[6]);
                coil8.Checked = Fct.sbFat(a[7]);
                coil9.Checked = Fct.sbFat(a[8]);
                coil10.Checked = Fct.sbFat(a[9]);
            }

        }

        public void buttonload(string flag)
        {
            if (flag == "add")
            {
                btnAdd.Visible = false;
                btnUpdate.Visible = false;
                btnDelete.Visible = false;
                btnConfirm.Visible = true;
                BtnCancel.Visible = true;
                Btnexit.Visible = false;
            }
            else if (flag == "update")
            {
                btnAdd.Visible = false;
                btnUpdate.Visible = false;
                btnDelete.Visible = false;
                btnConfirm.Visible = true;
                BtnCancel.Visible = true;
                Btnexit.Visible = false;
            }
            else if (flag == "delete")
            {
                btnAdd.Visible = false;
                btnUpdate.Visible = false;
                btnDelete.Visible = false;
                btnConfirm.Visible = true;
                BtnCancel.Visible = true;
                Btnexit.Visible = false;
            }
            else
            {
                btnAdd.Visible = true;
                btnUpdate.Visible = true;
                btnDelete.Visible = true;
                btnConfirm.Visible = false;
                BtnCancel.Visible = false;
                Btnexit.Visible = true;
            }
        }

        private void Btnexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void fenable_CheckedChanged(object sender, EventArgs e)
        {
            muser["fenable", muser.CurrentCell.RowIndex].Value = Fct.sbtFat(fenable.Checked);
        }

        private void failure_CheckedChanged(object sender, EventArgs e)
        {
            muser["ffailure", muser.CurrentCell.RowIndex].Value = Fct.sbtFat(failure.Checked);
        }

        private void xsuper_CheckedChanged(object sender, EventArgs e)
        {
            muser["xsuper", muser.CurrentCell.RowIndex].Value = Fct.sbtFat(xsuper.Checked);
        }



    }
}
