using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS
{
    public partial class Menu : UserControl
    {

        public Menu()
        {
            InitializeComponent();
        }

        private void treeView1_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (e.Node.Text == "Attachment")
            {
                frmAtt frm2 = new frmAtt();
                frm2.Show();
            }
        }

    }
}
