using CrystalDecisions.CrystalReports.Engine;
using CrystalDecisions.Shared;
using INS.Business.objRpt;
using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business.Report
{
    public partial class pendt : Form
    {
        //private string rpDirectory = Application.StartupPath.Replace("\\bin\\Debug", "\\objRpt");
        private string rpDirectory = "M:\\Software II\\New INS Runtime\\Setup\\objRpt";
        public String vfbus = "", p_type = "", rpOptid = "", fctlid = "", fctlids = "",fmntlabel="";
        private DirectPolicy directPolicy;
        public DataTable dt = new DataTable();
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        private RIWard rIWard;
        private Endorsement endorsement;
        public pendt()
        {
            InitializeComponent();
        }

        public pendt(Endorsement endorsement)
        {
            // TODO: Complete member initialization
            this.endorsement = endorsement;
            InitializeComponent();
        }

        public void ctrlbtn()
        {
            if (rpOptid == "EEC" || rpOptid == "CMP" || rpOptid == "PMP")
            {
                CI.Visible = true;
            }
            else { CI.Visible = false; }
        }



        void callCrPrmReg(DataSet cryDs, string optid)
        {
            ReportDocument cryRpt = new ReportDocument();
            if (fmntlabel == "4")
            {
                if (optid == "CAR")
                { cryRpt.Load(rpDirectory + "\\pendtcar_diy.rpt"); }
                else
                { cryRpt.Load(rpDirectory + "\\pendt_diy.rpt"); }
            }
            else {
                if (optid == "CAR")
                { cryRpt.Load(rpDirectory + "\\pendtcar.rpt"); }
                else
                { cryRpt.Load(rpDirectory + "\\pendt.rpt"); }
            }

            cryRpt.SetDataSource(cryDs.Tables[0]);
            cryRpt.Subreports["polh"].SetDataSource(cryDs.Tables[0]);
            cryRpt.Subreports["endrmk"].SetDataSource(cryDs.Tables[1]);
            cryRpt.DataDefinition.FormulaFields["Z_classdesc"].Text = String.Format("'{0}'", "" + Fct.stFat(InsEnvironment.Minsc.GetFdesc()) + "");
            cryDocViewer temp_form = new cryDocViewer(cryRpt);
            temp_form.ShowDialog();
        }

        public DataSet CreateViewDs(string rpOptid)
        {
            DataSet ds = new DataSet();

            DataTable dtpolh = DBHelper.GetDataSet("select * from polh where fctlid in ( '" + fctlids + "')");
            DataTable dtoinsex1 = DBHelper.GetDataSet("select * from oinsex where fctlid_1 in ( '" + fctlids + "') and ftype ='SEC1' order by flogseq");
            DataTable dtoinsex2 = DBHelper.GetDataSet("select * from oinsex where fctlid_1 in ( '" + fctlids + "') and ftype ='SEC2' order by flogseq");
            DataTable dtoinsatt = DBHelper.GetDataSet("select * from oinsatt where fctlid_1 in ( '" + fctlids + "')  order by flogseq");
            DataTable dtoremark = DBHelper.GetDataSet("select fcontent, fctlid_1 from oremark where fctlid_1 in ( '" + fctlids + "')");
            dtpolh = dtpolh_n(dtpolh, dtoinsex1.Rows.Count, dtoinsex2.Rows.Count, dtoinsatt.Rows.Count);

            if (rpOptid == "CAR")
            {
                ds.Tables.Add(dtpolh); 
                ds.Tables.Add(dtoremark);
            }
            else
            {
                ds.Tables.Add(dtpolh); ;
                ds.Tables.Add(dtoremark);
            }
            return ds;
        }

        public DataTable dtpolh_n(DataTable dtpolh, int dtoinsex1Row, int dtoinsex2Row, int dtoinsattRow)
        {
            DataColumn pmntlabel = dtpolh.Columns.Add("pmntlabel", typeof(String)),
            pcontrt = dtpolh.Columns.Add("pcontrt", typeof(Boolean)),
            pcontrt_t1 = dtpolh.Columns.Add("pcontrt_t1", typeof(Boolean)),
            psite = dtpolh.Columns.Add("psite", typeof(Boolean)),
            psite_t1 = dtpolh.Columns.Add("psite_t1", typeof(Boolean)),
            peffpd_t1 = dtpolh.Columns.Add("peffpd_t1", typeof(Boolean)),
            pmntpd_t1 = dtpolh.Columns.Add("pmntpd_t1", typeof(Boolean)),
            pretrodate = dtpolh.Columns.Add("pretrodate", typeof(Boolean)),
            ppropdate = dtpolh.Columns.Add("ppropdate", typeof(Boolean)),
            ptrdesc = dtpolh.Columns.Add("ptrdesc", typeof(Boolean)),
            pprfdesc = dtpolh.Columns.Add("pprfdesc", typeof(Boolean)),
            pex1 = dtpolh.Columns.Add("pex1", typeof(Boolean)),
            pex2 = dtpolh.Columns.Add("pex2", typeof(Boolean)),
            pattch = dtpolh.Columns.Add("pattch", typeof(Boolean)),
            pmainten = dtpolh.Columns.Add("pmainten", typeof(String));

            foreach (DataRow dr in dtpolh.Rows)
            {
                if (dr["fendttype"].ToString().Trim() == "1")
                {
                    dr["fincfr"] = dr["fincfr"];
                }
                else { dr["fincfr"] = dr["fincfr_p"]; }
                if (dr["fendttype"].ToString().Trim() == "1")
                {
                    dr["fincto"] = dr["fincto"];
                }
                else { dr["fincto"] = dr["fincto_p"]; }
                if (dr["fendttype"].ToString().Trim() == "1")
                {
                    dr["feffpd_t1"] = dr["feffpd_t1"];
                }
                else { //dr["feffpd_p1"] = dr["feffpd_t1"]; 
                    dr["feffpd_t1"] = dr["feffpd_p1"]; 
                }

                if (dr["fendttype"].ToString().Trim() == "1")
                {
                    dr["fmntfr"] = dr["fmntfr"];
                }
                else { dr["fmntfr"] = dr["fmntfr_p"]; }
                if (dr["fendttype"].ToString().Trim() == "1")
                {
                    dr["fmntto"] = dr["fmntto"];
                }
                else { dr["fmntto"] = dr["fmntto_p"]; }
                if (dr["fendttype"].ToString().Trim() == "1")
                {
                    dr["fmntpd_t1"] = dr["fmntpd_t1"];
                }
                else { dr["fmntpd_t1"] = dr["fmntpd_p1"]; }

                if (dr["fendttype"].ToString().Trim() == "1")
                {
                    if (dr["feffpd_t1"].ToString().Trim() != "")
                    {
                        dr["peffpd_t1"] = true;
                    }
                    else { dr["peffpd_t1"] = false; }
                }
                else
                {
                    if (dr["feffpd_p1"].ToString().Trim() != "")
                    {
                        dr["peffpd_t1"] = true;
                    }
                    else { dr["peffpd_t1"] = false; }
                }
                if (dr["fendttype"].ToString().Trim() == "1")
                {
                    if (dr["fmntpd_t1"].ToString().Trim() != "")
                    {
                        dr["pmntpd_t1"] = true;
                    }
                    else { dr["pmntpd_t1"] = false; }
                }
                else
                {
                    if (dr["fmntpd_p1"].ToString().Trim() != "")
                    {
                        dr["pmntpd_t1"] = true;
                    }
                    else { dr["pmntpd_t1"] = false; }
                }
                if (dr["fendttype"].ToString().Trim() == "1")
                {
                    if (dr["fmainten"].ToString().Trim() == "1")
                    {
                        dr["pmainten"] = "1";
                    }
                    else { dr["pmainten"] = "2"; }
                }
                else
                {
                    if (dr["fmntfr_p"].ToString().Trim() != "")
                    {
                        dr["pmainten"] = "1";
                    }
                    else { dr["pmainten"] = "2"; }
                }
                if (dr["fmntlabel"].ToString().Trim() == "")
                {
                    dr["pmntlabel"] = "1";
                }
                else { dr["pmntlabel"] = dr["fmntlabel"]; }
                if (dr["fcontrt"].ToString().Trim() != "")
                {
                    dr["pcontrt"] = true;
                }
                else { dr["pcontrt"] = false; }
                if (dr["fcontrt_t1"].ToString().Trim() != "")
                {
                    dr["pcontrt_t1"] = true;
                }
                else { dr["pcontrt_t1"] = false; }
                if (dr["fsite"].ToString().Trim() != "")
                {
                    dr["psite"] = true;
                }
                else { dr["psite"] = false; }
                if (dr["fsite_t1"].ToString().Trim() != "")
                {
                    dr["psite_t1"] = true;
                }
                else { dr["psite_t1"] = false; }
                if (dr["ftrdesc"].ToString().Trim() != "")
                {
                    dr["ptrdesc"] = true;
                }
                else { dr["ptrdesc"] = false; }
                if (dr["fprfdesc"].ToString().Trim() != "")
                {
                    dr["pprfdesc"] = true;
                }
                else { dr["pprfdesc"] = false; }
                if (dr["fretrodate"] != null)
                {
                    dr["pretrodate"] = true;
                }
                else { dr["pretrodate"] = false; }
                if (dr["fpropdate"] != null)
                {
                    dr["ppropdate"] = true;
                }
                else { dr["ppropdate"] = false; }
                if (dtoinsex1Row > 0)
                {
                    dr["pex1"] = true;
                }
                else { dr["pex1"] = false; }
                if (dtoinsex2Row > 0)
                {
                    dr["pex2"] = true;
                }
                else { dr["pex2"] = false; }
                if (dtoinsattRow > 0)
                {
                    dr["pattch"] = true;
                }
                else { dr["pattch"] = false; }

            }
            return dtpolh;
        }

        private void CI_Click(object sender, EventArgs e)
        {
            if (checkBox1.Checked)
            {
                fctlids = fctlid;
            }
            if (checkBox3.Checked)
            {
                DataRow[] rows = dt.Select("fctlid <= '" + fctlid + "'", "fctlid desc");
                for (int i = 0; i < Fct.snFat(domainUpDown1.Text); i++)
                {
                    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                    else { fctlids = fctlids + "','" + Fct.stFat(rows[i]["fctlid"]); }
                }
            } 
            if (rpOptid == "EEC")
                callCrPrmCI(CreateViewEecCI(), rpOptid);

            if (rpOptid == "PMP" || rpOptid == "CMP")
                callCrPrmCI(CreateViewPmpCI(), rpOptid);
        }

        public DataTable CreateViewEecCI()
        {
            DataTable dtpolh = DBHelper.GetDataSet("select * from polh where fctlid in ( '" + fctlids + "')");
            DataTable dtoinsint_b = DBHelper.GetDataSet("select sum(fnum1) as fnum1,sum(fupdnum1) as fupdnum1 from oinsint_b where fctlid_1 in ( '" + fctlids + "')");
            DataColumn pmntlabel = dtpolh.Columns.Add("pmntlabel", typeof(String)),
               pcontrt = dtpolh.Columns.Add("pcontrt", typeof(Boolean)),
               pcontrt_t1 = dtpolh.Columns.Add("pcontrt_t1", typeof(Boolean)),
               psite = dtpolh.Columns.Add("psite", typeof(Boolean)),
               psite_t1 = dtpolh.Columns.Add("psite_t1", typeof(Boolean)),
               peffpd_t1 = dtpolh.Columns.Add("peffpd_t1", typeof(Boolean)),
               pmntpd_t1 = dtpolh.Columns.Add("pmntpd_t1", typeof(Boolean)),
               ptrdesc = dtpolh.Columns.Add("ptrdesc", typeof(Boolean)),
               pprfdesc = dtpolh.Columns.Add("pprfdesc", typeof(Boolean)),
               flmt = dtpolh.Columns.Add("flmt", typeof(Decimal)),
               fupdlmt = dtpolh.Columns.Add("fupdlmt", typeof(Decimal)),
               fnum1 = dtpolh.Columns.Add("fnum1", typeof(Decimal)),
               fupdnum1 = dtpolh.Columns.Add("fupdnum1", typeof(Decimal));

            foreach (DataRow dr in dtpolh.Rows)
            {
                dr["fsclass"] = dr["fsclass"].ToString().Trim();
                if (dr["fmntlabel"].ToString().Trim() == "")
                {
                    dr["pmntlabel"] = "1";
                }
                else { dr["pmntlabel"] = dr["fmntlabel"]; }
                if (dr["fcontrt"].ToString().Trim() != "")
                {
                    dr["pcontrt"] = true;
                }
                else { dr["pcontrt"] = false; }
                if (dr["fcontrt_t1"].ToString().Trim() != "")
                {
                    dr["pcontrt_t1"] = true;
                }
                else { dr["pcontrt_t1"] = false; }
                if (dr["fsite"].ToString().Trim() != "")
                {
                    dr["psite"] = true;
                }
                else { dr["psite"] = false; }
                if (dr["fsite_t1"].ToString().Trim() != "")
                {
                    dr["psite_t1"] = true;
                }
                else { dr["psite_t1"] = false; }

                if (dr["fendttype"].ToString().Trim() == "1")
                {
                    if (dr["feffpd_t1"].ToString().Trim() != "")
                    {
                        dr["peffpd_t1"] = true;
                    }
                    else { dr["peffpd_t1"] = false; }
                }
                else
                {
                    if (dr["feffpd_p1"].ToString().Trim() != "")
                    {
                        dr["peffpd_t1"] = true;
                    }
                    else { dr["peffpd_t1"] = false; }
                }

                if (dr["fendttype"].ToString().Trim() == "1")
                {
                    if (dr["fmntpd_t1"].ToString().Trim() != "")
                    {
                        dr["pmntpd_t1"] = true;
                    }
                    else { dr["pmntpd_t1"] = false; }
                }
                else
                {
                    if (dr["fmntpd_p1"].ToString().Trim() != "")
                    {
                        dr["pmntpd_t1"] = true;
                    }
                    else { dr["pmntpd_t1"] = false; }
                }

                if (dr["ftrdesc"].ToString().Trim() != "")
                {
                    dr["ptrdesc"] = true;
                }
                else { dr["ptrdesc"] = false; }
                if (dr["fprfdesc"].ToString().Trim() != "")
                {
                    dr["pprfdesc"] = true;
                }
                else { dr["pprfdesc"] = false; }
                foreach (DataRow dr1 in dtoinsint_b.Rows)
                {
                    dr["fnum1"] = dr1["fnum1"];
                    dr["fupdnum1"] = dr1["fupdnum1"];
                }
            }

            return dtpolh;
        }

        public DataTable CreateViewPmpCI()
        {
            string sql = "select a.*,case when a.fendtno ='' then 'N' else 'Y' end as prnendt, " +
                        "b.fupdlmt1,b.fupdlmt2,b.fsicur,b.fciform,b.finsd,b.fissdate,b.fefffr,b.feffto " +
                        "from [dbo].[oinsint_e] a " +
                        "left join polh b on a.fctlid_1=b.fctlid where b.fctlid in ( '" + fctlids + "')";
            DataTable dt = DBHelper.GetDataSet(sql);
            return dt;
        }

        void callCrPrmCI(DataTable cryDT, string optid)
        {
            ReportDocument cryRpt = new ReportDocument();
            if (optid == "PMP" || optid == "CMP")
                cryRpt.Load(rpDirectory + "\\pmpci.rpt");
            else if (optid == "EEC")
                cryRpt.Load(rpDirectory + "\\eecni.rpt");

            cryRpt.SetDataSource(cryDT);
            cryDocViewer temp_form = new cryDocViewer(cryRpt);
            temp_form.ShowDialog();
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void pendt_Resize(object sender, EventArgs e)
        {
            if (WindowState == FormWindowState.Maximized)
            {
                //最大化时所需的操作 
                this.ClientSize = new System.Drawing.Size(431, 272);
                this.WindowState = FormWindowState.Normal;
            }
            else if (WindowState == FormWindowState.Minimized)
            {
                //最小化时所需的操作

                this.ClientSize = new System.Drawing.Size(50, 50);
                this.WindowState = FormWindowState.Normal;
            }
        }

        private void PS_Click(object sender, EventArgs e)
        {
            progressBar1.Visible = true;
            progressBar1.Maximum = 50;//设置最大长度值 
            progressBar1.Value = 0;//设置当前值 
            progressBar1.Step = 10;//设置没次增长多少 
            for (int i = 0; i < 5; i++)//循环 
            {
                System.Threading.Thread.Sleep(100);//暂停1秒 
                progressBar1.Value += progressBar1.Step;
            }
            if (checkBox1.Checked)
            {
                fctlids = fctlid;
            }
            if (checkBox3.Checked)
            {
                DataRow[] rows = dt.Select("fctlid <= '" + fctlid + "'", "fctlid desc");
                for (int i = 0; i < Fct.snFat(domainUpDown1.Text); i++)
                {
                    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                    else { fctlids = fctlids + "','" + Fct.stFat(rows[i]["fctlid"]); }
                }
            }
            if (vfbus == "R")
            { }
            else
            {
                if (rpOptid == "CAR")
                {
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);
                }
                else
                {
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);
                }
            }
            if (rpOptid != "EEC" && rpOptid != "PMP" && rpOptid != "CMP") { this.Close(); }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            if (checkBox1.Checked)
            {
                fctlids = fctlid;
            }
            if (checkBox3.Checked)
            {
                DataRow[] rows = dt.Select("fctlid <= '" + fctlid + "'", "fctlid desc");
                for (int i = 0; i < Fct.snFat(domainUpDown1.Text); i++)
                {
                    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                    else { fctlids = fctlids + "','" + Fct.stFat(rows[i]["fctlid"]); }
                }
            } 
            //使用ExportToStream方式匯出
            Export(CrystalDecisions.Shared.ExportFormatType.WordForWindows, "test.doc");
            MessageBox.Show("Exported Successful");
        }

        public void Export(CrystalDecisions.Shared.ExportFormatType FileType, string FileName)//使用ExportToStream方式匯出
        {
            //create CrystalReport object
            CrystalDecisions.CrystalReports.Engine.ReportDocument report = new CrystalDecisions.CrystalReports.Engine.ReportDocument();

            if (fmntlabel == "4")
            {
                //load report
                if (rpOptid == "CAR")
                { report.Load(rpDirectory + "\\pendtcar_diy.rpt"); }
                else
                { report.Load(rpDirectory + "\\pendt_diy.rpt"); }
            }
            else {
                if (rpOptid == "CAR")
                { report.Load(rpDirectory + "\\pendtcar.rpt"); }
                else
                { report.Load(rpDirectory + "\\pendt.rpt"); }
            }

            DataSet cryDs = CreateViewDs(rpOptid);
            report.SetDataSource(cryDs.Tables[0]);
            report.Subreports["polh"].SetDataSource(cryDs.Tables[0]);
            report.Subreports["endrmk"].SetDataSource(cryDs.Tables[1]);
            report.DataDefinition.FormulaFields["Z_classdesc"].Text = String.Format("'{0}'", "" + Fct.stFat(InsEnvironment.Minsc.GetFdesc()) + "");

            cryDocViewer temp_form = new cryDocViewer(report);

            try
            {
                ExportOptions CrExportOptions;
                DiskFileDestinationOptions CrDiskFileDestinationOptions = new DiskFileDestinationOptions();
                PdfRtfWordFormatOptions CrFormatTypeOptions = new PdfRtfWordFormatOptions();
                CrDiskFileDestinationOptions.DiskFileName = "M:\\test.doc";
                CrExportOptions = report.ExportOptions;
                {
                    CrExportOptions.ExportDestinationType = ExportDestinationType.DiskFile;
                    CrExportOptions.ExportFormatType = ExportFormatType.WordForWindows;
                    CrExportOptions.DestinationOptions = CrDiskFileDestinationOptions;
                    CrExportOptions.FormatOptions = CrFormatTypeOptions;
                }
                report.Export();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
        }

        private void domainUpDown1_TextChanged(object sender, EventArgs e)
        {
            checkBox3.Checked = true;
            checkBox1.Checked = false;
        }

        private void checkBox3_CheckedChanged(object sender, EventArgs e)
        {
            if (checkBox3.Checked == true) {
                checkBox1.Checked = false;
            }
        }

    }
}
