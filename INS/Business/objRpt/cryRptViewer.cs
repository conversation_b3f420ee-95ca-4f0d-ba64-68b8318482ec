using CrystalDecisions.CrystalReports.Engine;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business.objRpt
{
    public partial class cryRptViewer : Form
    {
        public cryRptViewer(ReportDocument cryRpt)
        {
            InitializeComponent();
            cryRptViewer1.ReportSource = cryRpt;
            cryRptViewer1.Refresh();
        }

        private TextObject CType(object p, TextObject textObject)
        {
            throw new NotImplementedException();
        }
    }
}
