using CrystalDecisions.CrystalReports.Engine;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business.objRpt
{
    public partial class cryDocViewer : Form
    {
        public cryDocViewer(ReportDocument cryRpt)
        {
            InitializeComponent();
            cryDocViewer1.ReportSource = cryRpt;
            cryDocViewer1.Refresh();
        }

        private TextObject CType(object p, TextObject textObject)
        {
            throw new NotImplementedException();
        }
    }
}
