using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Configuration;
using INS.Ctrl;
using System.Collections;
using INS.Business.SearchForm;
using INS.Business.Report;
using System.Net;
using System.IO;
using RestSharp;
using Newtonsoft.Json;
using System.Collections.Specialized;
using System.Net.Http;
using System.Web;
using System.Net.Mail;
using System.Web.Script.Serialization;
using System.ComponentModel.Design;

namespace INS.Business
{
    public partial class DirectPolicy : Form
    {
        public string Class = InsEnvironment.Minsc.GetFid();
        public string Acclass = InsEnvironment.Minsc.GetFacclass();
        public string Classfctlid = InsEnvironment.Minsc.GetFctlid();
        public string com = "0", disc = "", Fbus = "", policyNo = "", fctlid = "", fconfirm = "", fctldel = "",
            finpuser = "", ConfirmRlt = "", reopt = "";
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        DBConnect operate = new DBConnect();
        public Boolean Addflag = false, TabCancel = false, updateflag = false;
        public int Policylistrow = 0;

        DirectGen Gen = new DirectGen();
        DirectExcess Excess = new DirectExcess();
        DirectAttach Attach = new DirectAttach();
        DirectInstall Install = new DirectInstall();
        DirectInsured_CAR Insured = new DirectInsured_CAR();
        DirectInsured_CGL InsuredC = new DirectInsured_CGL();
        DirectInsured_CPM InsuredD = new DirectInsured_CPM();
        DirectInsured_PMP InsuredE = new DirectInsured_PMP();
        DirectInsured_EEC InsuredB = new DirectInsured_EEC();
        DirectInsured_PAR Insured2 = new DirectInsured_PAR();
        private Inquiry.DirectInquiry directInquiry;
        private Inquiry.COInquiry cOInquiry;

        private dynamic InsdInst = null;
        private string InsdFocus;
        private renewopt renewopt;

        //public Boolean ll_getno, ll_con;

        public DirectPolicy()
        {
            InitializeComponent();
            InitCombobox();
            FillData("");
            DetermineInsdInst(Class);
        }

        public DirectPolicy(Inquiry.DirectInquiry directInquiry)
        {
            // TODO: Complete member initialization
            this.directInquiry = directInquiry;
            InitializeComponent();
            InitCombobox();
            DetermineInsdInst(Class);
        }

        public DirectPolicy(Inquiry.COInquiry cOInquiry)
        {
            // TODO: Complete member initialization
            this.cOInquiry = cOInquiry;
            InitializeComponent();
            InitCombobox();
            DetermineInsdInst(Class);
        }

        public DirectPolicy(renewopt renewopt)
        {
            // TODO: Complete member initialization
            this.renewopt = renewopt;
            InitializeComponent();
            InitCombobox();
            DetermineInsdInst(Class);
        }



        private void DetermineInsdInst(string clsstr)
        {

            if (Class == "CAR")
            {
                InsdInst = Insured;
                InsdFocus = "afid";
            }

            if (Class == "CSB")
            {
                InsdInst = Insured;
                InsdFocus = "fcalc2";
            }

            if (Class == "MYP" || Class == "PAR")
            {
                InsdInst = Insured2;
                InsdFocus = "seql";
            }

            if (Class == "CLL" || Class == "PII")
            {
                InsdInst = InsuredC;
                InsdFocus = "fsi";
            }

            if (Class == "CGL" || Class == "FGP")
            {
                InsdInst = InsuredC;
                InsdFocus = "flogseq";
            }

            if (Class == "CPM")
            {
                InsdInst = InsuredD;
                InsdFocus = "flogseq";
            }

            if (Class == "PMP" || Class == "CMP")
            {
                InsdInst = InsuredE;
                InsdFocus = "fkind";
            }

            if (Class == "EEC")
            {
                InsdInst = InsuredB;
                InsdFocus = "fid";
            }

        }

        public void FillData(string order)
        {
            String Sql = "select fctlid, fcom, ftd, finpuser, fpolno as Policy#,CONVERT(varchar(10),fissdate,102) as [Issue Date],CONVERT(varchar(10),fefffr,102) as [Effect Fr],CONVERT(varchar(10),feffto,102) as [Effect To], " +
                        "fprdr as Producer#,finsd as Contractors,fexno as [Expiry No.],frnno as [Renew No.], " +
                        "case when fconfirm='1' then 'Pending' else  " +
                        "case when fconfirm='2' then 'Hold' else " +
                        "case when fconfirm='3' then 'Confirmed' else 'Cancelled' end end end as Status, " +
                        "case when CHARINDEX('Send',rtrim(fctldel)) > 0 then '已發給客戶'  else   " +
                        "case when CHARINDEX('Finished',rtrim(fctldel)) > 0  then '審批完成' " +
                        "else case when len(rTRIM(fctldel))>10 then '已傳A8' else '' end end end as PushToA8 " +
                        "from polh a where fbus='" + Fbus + "' and ftype = 'P' AND fclass ='" + Class + "' ";
            if (Class == "PMP" || Class == "CMP")
            {
                Sql = "select a.fctlid, a.fcom, a.ftd, a.finpuser, a.fpolno as Policy#,CONVERT(varchar(10),a.fissdate,102) as [Issue Date],CONVERT(varchar(10),a.fefffr,102) as [Effect Fr],CONVERT(varchar(10),a.feffto,102) as [Effect To], " +
                        "a.fprdr as Producer#,a.finsd as Contractors,a.fexno as [Expiry No.],a.frnno as [Renew No.],b.frno, " +
                        "case when a.fconfirm='1' then 'Pending' else  " +
                        "case when a.fconfirm='2' then 'Hold' else " +
                        "case when a.fconfirm='3' then 'Confirmed' else 'Cancelled' end end end as Status, " +
                        "case when CHARINDEX('Send',rtrim(fctldel)) > 0 then '已發給客戶'  else   " +
                        "case when CHARINDEX('Finished',rtrim(fctldel)) > 0  then '審批完成' " +
                        "else case when len(rTRIM(fctldel))>10 then '已傳A8' else '' end end end as PushToA8 " +
                        "from polh a left join oinsint_e b on a.fctlid = b.fctlid_1 where a.fbus='" + Fbus + "' and a.ftype = 'P' AND a.fclass ='" + Class + "' ";
            }
            if (order == "order by System.Data.DataRowView#") { Sql = Sql + "order by case when fconfirm=1 then '1' when fconfirm='2' then '2' when fconfirm='4' then '4' else '3' END,a.fpolno desc,a.finpdate desc"; }
            else
            {
                if (order.Contains("order"))
                {
                    Sql = Sql + order;
                }
                else
                {
                    Sql = Sql + order;
                    Sql = Sql + "order by case when fconfirm=1 then '1' when fconfirm='2' then '2' when fconfirm='4' then '4' else '3' END,a.fpolno desc,a.finpdate desc ";
                }
            }
            Policylist.DataSource = DBHelper.GetDataSet(Sql);
            this.Policylist.Columns[0].Visible = false;
            this.Policylist.Columns[1].Visible = false;
            this.Policylist.Columns[2].Visible = false;
            this.Policylist.Columns[3].Visible = false;

            if (Policylist.Rows.Count == 0)
            {
                panel10.Controls.Clear();
                panel10.Controls.Add(buttonload("Load"));
            }
            else
            {
                Policylist.CellFormatting +=
            new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.Policylist_CellFormatting);
            }

        }

        private void Policylist_CellFormatting(object sender,
        System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            if (Addflag == true || updateflag == true)
            {
                    try
                    {
                        if (Policylist.Columns[e.ColumnIndex].Name.Equals("Policy#"))
                        {
                            Policylist.Columns[e.ColumnIndex].Width = 150;
                        }
                        if (Policylist.Columns[e.ColumnIndex].Name.Equals("Status"))
                        {
                            Policylist.Columns[e.ColumnIndex].Width = 60;
                        }
                        if (Policylist.Columns[e.ColumnIndex].Name.Equals("Expiry No."))
                        {
                            Policylist.Columns[e.ColumnIndex].Width = 65;
                        }
                        if (Policylist.Columns[e.ColumnIndex].Name.Equals("Renew No."))
                        {
                            Policylist.Columns[e.ColumnIndex].Width = 65;
                        }
                        if (Class == "PMP" || Class == "CMP")
                        {
                            if (Policylist.Columns[e.ColumnIndex].Name.Equals("frno"))
                            {
                                Policylist.Columns[e.ColumnIndex].Width = 65;
                            }
                        }
                        if (Policylist.Columns[e.ColumnIndex].Name.Equals("Contractors"))
                        {
                            Policylist.Columns[e.ColumnIndex].Width = 180;
                        }
                        if (Policylist.Columns[e.ColumnIndex].Name.Equals("PushToA8"))
                        {
                            Policylist.Columns[e.ColumnIndex].Width = 100;
                        }
                    }
                    catch { }
            }
        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            if (Addflag == true || updateflag == true)
            {
                if (e.TabPage == tabPage1)
                {
                    e.Cancel = true;
                }
            }
        }

        private void tabControl1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            if (Addflag == true || updateflag == true)
            {
                if (tabControl1.SelectedIndex == 1)
                {
                    if (TabCancel == false)
                    {
                        InsdInst.u_calculation();
                        Gen.mmain = InsdInst.mmain;
                        Gen.getMmain();
                        Gen.installTotal = Install.installTotal;
                    }
                }
                if (tabControl1.SelectedIndex == 2)
                {
                    if (Gen.GenEffrDate == "    .  ." || Gen.GenEftoDate == "    .  .")
                    {
                        TabCancel = true;
                        tabControl1.SelectedIndex = 1;
                        MessageBox.Show("Invalid Value!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        Gen.setControlFocus("fefffr");
                    }
                    else
                    {
                        TabCancel = false;
                        if (Class == "EEC")
                        {
                            if (Fbus == "K")
                            {
                                InsdInst.fcomtype = Gen.GenComtype;
                                InsdInst.fleader = Gen.Fleader;
                            }
                            InsdInst.EffrDate = Gen.GenEffrDate;
                            InsdInst.EftoDate = Gen.GenEftoDate;
                            InsdInst.InsureComm = Gen.GenComm;
                            InsdInst.InsureDisc = Gen.GenDisc;
                            if (Gen.GenSubClass == "CONTRACTOR")
                            {
                                InsdInst.InsureSum2 = Gen.GenSum;
                            }
                            if (InsdInst.fsclass != Gen.GenSubClass)
                            {
                                panel4.Controls.Clear();
                                InsdInst.fctlid = fctlid;
                                InsdInst.Class = Class;
                                InsdInst.Classfctlid = Classfctlid;
                                InsdInst.fsclass = Gen.GenSubClass;
                                InsdInst.buttoncontroladd("change");
                                panel4.Controls.Add(InsdInst);
                                if (Gen.GenSubClass == "CONTRACTOR")
                                {
                                    InsdInst.InsureSum2 = Gen.GenSum;
                                }
                            }
                            InsdInst.u_calculation();
                        }
                        else
                        {
                            InsdInst.EffrDate = Gen.GenEffrDate;
                            InsdInst.EftoDate = Gen.GenEftoDate;
                            InsdInst.InsureComm = Gen.GenComm;
                            InsdInst.InsureDisc = Gen.GenDisc;
                            if (Class == "CSB")
                            {
                                InsdInst.InsureSum2 = Gen.GenSum;
                            }
                            if (Class == "CLL")
                            {
                                InsdInst.fsum = Gen.GenConSum;
                            }
                            InsdInst.u_calculation();
                            //InsdInst.calculation();
                        }
                    }
                }

                if (tabControl1.SelectedIndex == 5)
                {
                    if (Class == "EEC")
                    {
                        InsdInst.InsureComm = Gen.GenComm;
                        InsdInst.u_calculation();
                        Install.mmain = InsdInst.mmain;
                        if (Gen.GenSubClass == "CONTRACTOR")
                        {
                            InsdInst.InsureSum2 = Gen.GenSum;
                        }
                        Gen.mmain = InsdInst.mmain;
                        Gen.getMmain();
                        Gen.flvyChange();
                        Install.InstallEC1 = Gen.GenEC1;
                        Install.InstallEC2 = Gen.GenEC2;
                        Install.InstallEC3 = Gen.GenEC3;
                        Install.InstallEC1amt = Gen.GenEC1amt;
                        Install.InstallEC2amt = Gen.GenEC2amt;
                        Install.InstallEC3amt = Gen.GenEC3amt;
                        Install.Installalevyamt = Gen.Genflvyamta;
                        Install.InstalldiscAdj = Gen.GendiscAdj;
                        Install.InstallcommAdj = Gen.GencommAdj;
                        Install.Installec1Adj = Gen.Genec1Adj;
                        Install.checkdata();
                        Install.Control();
                    }
                    else if (Class == "PMP" || Class == "CMP")
                    {
                        InsdInst.InsureComm = Gen.GenComm;
                        InsdInst.u_calculation();
                        Install.mmain = InsdInst.mmain;
                        Gen.mmain = InsdInst.mmain;
                        Gen.getMmain();
                        Gen.flvyChange();
                        Install.InstallEC1 = Gen.GenEC1;
                        Install.InstallEC2 = Gen.GenEC2;
                        Install.InstallEC1amt = Gen.GenEC1amt;
                        Install.InstallEC2amt = Gen.GenEC2amt;
                        Install.Installalevyamt = Gen.Genflvyamta;
                    }
                    else
                    {
                        InsdInst.InsureComm = Gen.GenComm;
                        InsdInst.u_calculation();
                        Install.mmain = InsdInst.mmain;
                        Gen.mmain = InsdInst.mmain;
                        Gen.getMmain();
                        Install.Installalevyamt = Gen.Genflvyamta;
                    }
                    Gen.installTotal = Install.installTotal;
                    Install.InstallMmain();
                    Install.tabpage6addData();
                    Install.InstallComm = Gen.GenComm;
                    Install.InstallDisc = Gen.GenDisc;
                    Install.InstallEffrDate = Gen.GenEffrDate;
                    Install.InstallEftoDate = Gen.GenEftoDate;
                    Install.InstallIsDate = Gen.GenIsDate;
                    if (Fbus == "K")
                    {
                        Install.fcomtype = Gen.GenComtype;
                    }
                }
            }
        }

        public Control buttonload(string flag)
        {
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fclass = Class;
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.finpuser = finpuser.Trim();
            temp_ctrlbutton.sourceListRowCount = Policylist.Rows.Count;
            if (flag == "Load") { temp_ctrlbutton.buttonload("", fctldel); }
            if (flag == "Add") { temp_ctrlbutton.buttoncontroladd(); }
            if (flag == "Back") { temp_ctrlbutton.buttoncontrolback(); }
            if (flag == "Mod") { temp_ctrlbutton.buttoncontrolupdate(); }
            if (flag == "Save") { temp_ctrlbutton.buttoncontrolsaveback(); }
            if (flag == "Push") { temp_ctrlbutton.buttoncontrolsaveback(); }
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonRenewClicked += new EventHandler(renewattch_Click);
            temp_ctrlbutton.UserControlButtoncopyClicked += new EventHandler(copy_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            temp_ctrlbutton.UserControlButtonPrintClicked += new EventHandler(printattch_Click);
            temp_ctrlbutton.UserControlButtonPushClicked += new EventHandler(pushattch_Click);
            temp_ctrlbutton.UserControlButtonNotedClicked += new EventHandler(noteattch_Click);
            temp_ctrlbutton.UserControlButtonExpiryClicked += new EventHandler(Expiry_Click);
            //ll_getno = temp_ctrlbutton.ll_getno;
            //ll_con = temp_ctrlbutton.ll_con;

            return temp_ctrlbutton;
        }

        public void tabpage1reload()
        {
            panel10.Controls.Clear();
            panel10.Controls.Add(buttonload("Load"));
            tabPage2reload(fctlid);
            tabPage3reload(fctlid, com, disc);
            tabPage4reload(fctlid);
            tabPage5reload(fctlid);
            tabPage6reload(fctlid);
        }

        void tabPage2reload(string fctlid)
        {
            panel22.Controls.Clear();
            panel22.Controls.Add(buttonload("Load"));
            panel1.Controls.Clear();
            Gen.fctlid = fctlid;
            Gen.Class = Class;
            Gen.Classfctlid = Classfctlid;
            Gen.fbus = Fbus;
            Gen.ftype = "P";
            Gen.buttoncontrolback();
            panel1.Controls.Add(Gen);
        }

        void tabPage3reload(string fctlid, string com, string disc)
        {
            panel24.Controls.Clear();
            panel24.Controls.Add(buttonload("Load"));
            panel4.Controls.Clear();
            InsdInst.fctlid = fctlid;
            InsdInst.InsureComm = com;
            InsdInst.InsureDisc = disc;
            InsdInst.Class = Class;
            InsdInst.Classfctlid = Classfctlid;
            InsdInst.fbus = Fbus;
            InsdInst.ftype = "P";
            if (Class == "EEC")
            {
                InsdInst.fsclass = Gen.GenSubClass;
                if (Fbus == "K")
                {
                    InsdInst.fcomtype = Gen.GenComtype;
                }
            }
            InsdInst.buttoncontrolback();
            panel4.Controls.Add(InsdInst);
        }

        void tabPage4reload(string fctlid)
        {
            panel13.Controls.Clear();
            panel13.Controls.Add(buttonload("Load"));
            panel11.Controls.Clear();
            Excess.fctlid = fctlid;
            if (Class == "CAR" || Class == "PMP" || Class == "CMP")
            {
                Excess.panel12.Visible = true;
                Excess.panel13.Visible = true;
            }
            Excess.Class = Class;
            Excess.Classfctlid = Classfctlid;
            Excess.fbus = Fbus;
            Excess.ftype = "P";
            Excess.buttoncontrolback();
            panel11.Controls.Add(Excess);
        }

        void tabPage5reload(string fctlid)
        {
            panel28.Controls.Clear();
            panel28.Controls.Add(buttonload("Load"));
            panel12.Controls.Clear();
            Attach.fctlid = fctlid;
            Attach.Class = Class;
            Attach.Classfctlid = Classfctlid;
            Attach.fbus = Fbus;
            Attach.ftype = "P";
            Attach.buttoncontrolback();
            panel12.Controls.Add(Attach);
        }

        void tabPage6reload(string fctlid)
        {
            panel27.Controls.Clear();
            panel27.Controls.Add(buttonload("Load"));
            panel2.Controls.Clear();
            Install.fctlid = fctlid;
            Install.Class = Class;
            Install.Classfctlid = Classfctlid;
            Install.fbus = Fbus;
            Install.ftype = "P";
            Install.InstallTotComm = Gen.GenTotComm;
            Install.InstallTotDisc = Gen.GenTotDisc;
            Install.InstallGP = Gen.GenGP;
            Install.InstallNP = Gen.GenNP;
            Install.InstallComm = Gen.GenComm;
            Install.InstallDisc = Gen.GenDisc;
            Install.InstallEffrDate = Gen.GenEffrDate;
            Install.InstallEftoDate = Gen.GenEftoDate;
            Install.InstallIsDate = Gen.GenIsDate;
            Install.Installalevyamt = Gen.Genflvyamta;
            if (Fbus == "K")
            {
                Install.fcomtype = Gen.GenComtype;
            }
            Gen.installTotal = Install.installTotal;
            if (Class == "EEC" || Class == "PMP" || Class == "CMP")
            {
                Install.InstallEC1 = Gen.GenEC1;
                Install.InstallEC2 = Gen.GenEC2;
                Install.InstallEC3 = Gen.GenEC3;
                Install.InstallEC1amt = Gen.GenEC1amt;
                Install.InstallEC2amt = Gen.GenEC2amt;
                Install.InstallEC3amt = Gen.GenEC3amt;
                Install.Installalevyamt = Gen.Genflvyamta;
                Install.InstalldiscAdj = Gen.GendiscAdj;
                Install.InstallcommAdj = Gen.GencommAdj;
                Install.Installec1Adj = Gen.Genec1Adj;
            }
            Install.buttoncontrolback();
            panel2.Controls.Add(Install);

        }

        private void Policylist_SelectionChanged(object sender, EventArgs e)
        {
            fctldel = "";
            policylistchg();
        }

        void policylistchg()
        {
            if (Policylist.CurrentCell != null)
            {
                Policylistrow = Policylist.CurrentCell.RowIndex;
            }
            else { return; }

            if (Policylist.Rows[Policylistrow].Cells["Policy#"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["Policy#"].Value.ToString().Length != 0)
                {
                    policyNo = Policylist.Rows[Policylistrow].Cells["Policy#"].Value.ToString();
                }
            }

            if (Policylist.Rows[Policylistrow].Cells["PushToA8"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["PushToA8"].Value.ToString().Length != 0)
                {
                    fctldel = Policylist.Rows[Policylistrow].Cells["PushToA8"].Value.ToString().Trim();
                }
            }

            if (Policylist.Rows[Policylistrow].Cells["fctlid"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["fctlid"].Value.ToString().Length != 0)
                {
                    fctlid = Policylist.Rows[Policylistrow].Cells["fctlid"].Value.ToString();
                }
            }

            if (Policylist.Rows[Policylistrow].Cells["Status"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["Status"].Value.ToString().Length != 0)
                {
                    fconfirm = Policylist.Rows[Policylistrow].Cells["Status"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["fcom"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["fcom"].Value.ToString().Length != 0)
                {
                    com = Policylist.Rows[Policylistrow].Cells["fcom"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["ftd"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["ftd"].Value.ToString().Length != 0)
                {
                    disc = Policylist.Rows[Policylistrow].Cells["ftd"].Value.ToString();
                }
            }

            if (Policylist.Rows[Policylistrow].Cells["finpuser"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["finpuser"].Value.ToString().Length != 0)
                {
                    finpuser = Policylist.Rows[Policylistrow].Cells["finpuser"].Value.ToString();
                }
            }

            tabpage1reload();
        }

        public void buttoncontrolback()
        {
            Addflag = false;
            updateflag = false;
        }

        public void buttoncontroladd()
        {
            Addflag = true;
            tabControl1.SelectedTab = tabPage2;
            tabpage2add();
            tabpage3add();
            tabpage4add();
            tabpage5add();
            tabpage6add();

            if (tabControl1.SelectedTab == tabPage2)
                Gen.setControlFocus(Fbus == "D" ? "fuwyr" : "fcoins");

        }

        public void buttoncontrolupdate()
        {
            updateflag = true;
            if (tabControl1.SelectedTab == tabPage1)
            { tabControl1.SelectedTab = tabPage2; }

            tabpage2update();
            tabpage3update("update");
            tabpage4update("update");
            tabpage5update("update");
            tabpage6update("update");

            if (tabControl1.SelectedTab == tabPage2)
                Gen.setControlFocus(Fbus == "D" ? "fuwyr" : "fcoins");

            if (tabControl1.SelectedTab == tabPage3)
                InsdInst.setControlFocus(InsdFocus);

            if (tabControl1.SelectedTab == tabPage4)
                Excess.setControlFocus("fid1");

            if (tabControl1.SelectedTab == tabPage5)
                Attach.setControlFocus("fid");

            if (tabControl1.SelectedTab == tabPage6)
                Install.setControlFocus("installno");

        }

        public void buttoncontrolrenew(string flag)
        {
            Addflag = true;
            tabControl1.SelectedTab = tabPage2;
            tabpage2renew(flag);
            tabpage3update("renew");
            tabpage4update("renew");
            tabpage5update("renew");
            tabpage6update("renew");

            if (tabControl1.SelectedTab == tabPage2)
                Gen.setControlFocus(Fbus == "D" ? "fuwyr" : "fcoins");

            if (tabControl1.SelectedTab == tabPage3)
                InsdInst.setControlFocus(InsdFocus);

            if (tabControl1.SelectedTab == tabPage4)
                Excess.setControlFocus("fid1");

            if (tabControl1.SelectedTab == tabPage5)
                Attach.setControlFocus("fid");

            if (tabControl1.SelectedTab == tabPage6)
                Install.setControlFocus("installno");

        }

        void tabpage1Saveload(string newfctlid, string newcom, string newdisc)
        {
            panel10.Controls.Clear();
            panel10.Controls.Add(buttonload("Load"));
            tabPage2reload(newfctlid);
            tabPage3reload(newfctlid, newcom, newdisc);
            tabPage4reload(newfctlid);
            tabPage5reload(newfctlid);
            tabPage6reload(newfctlid);
        }

        void tabpage2add()
        {
            panel22.Controls.Clear();
            panel22.Controls.Add(buttonload("Add"));
            panel1.Controls.Clear();
            Gen.fctlid = fctlid;
            Gen.Class = Class;
            Gen.Classfctlid = Classfctlid;
            Gen.fbus = Fbus;
            Gen.ftype = "P";
            Gen.buttoncontroladd();
            panel1.Controls.Add(Gen);
        }

        void tabpage3add()
        {
            panel24.Controls.Clear();
            panel24.Controls.Add(buttonload("Add"));
            panel4.Controls.Clear();
            InsdInst.fctlid = fctlid;
            InsdInst.Class = Class;
            InsdInst.Classfctlid = Classfctlid;
            if (Class == "EEC") { InsdInst.fsclass = Gen.GenSubClass; }
            InsdInst.fbus = Fbus;
            InsdInst.ftype = "P";
            if (Class == "EEC")
            {
                InsdInst.buttoncontroladd("");
            }
            else { InsdInst.buttoncontroladd(); }
            panel4.Controls.Add(InsdInst);
        }

        void tabpage4add()
        {
            panel13.Controls.Clear();
            panel13.Controls.Add(buttonload("Add"));
            panel11.Controls.Clear();
            Excess.fctlid = fctlid;
            if (Class == "CAR" || Class == "PMP" || Class == "CMP")
            {
                Excess.panel12.Visible = true;
            }
            Excess.Class = Class;
            Excess.Classfctlid = Classfctlid;
            Excess.fbus = Fbus;
            Excess.ftype = "P";
            Excess.buttoncontroladd();
            panel11.Controls.Add(Excess);
        }

        void tabpage5add()
        {
            panel28.Controls.Clear();
            panel28.Controls.Add(buttonload("Add"));
            panel12.Controls.Clear();
            Attach.fctlid = fctlid;
            Attach.Class = Class;
            Attach.Classfctlid = Classfctlid;
            Attach.fbus = Fbus;
            Attach.ftype = "P";
            Attach.buttoncontroladd();
            panel12.Controls.Add(Attach);
        }

        void tabpage6add()
        {
            panel27.Controls.Clear();
            panel27.Controls.Add(buttonload("Add"));
            panel2.Controls.Clear();
            Install.fctlid = fctlid;
            Install.Class = Class;
            Install.Classfctlid = Classfctlid;
            Gen.installTotal = Install.installTotal;
            Install.fbus = Fbus;
            Install.ftype = "P";
            Install.buttoncontroladd();
            panel2.Controls.Add(Install);
        }

        void tabpage2update()
        {
            panel22.Controls.Clear();
            panel22.Controls.Add(buttonload("Mod"));
            panel1.Controls.Clear();
            Gen.fctlid = fctlid;
            Gen.Class = Class;
            Gen.Classfctlid = Classfctlid;
            Gen.fbus = Fbus;
            Gen.ftype = "P";
            Gen.buttoncontrolupdate();
            panel1.Controls.Add(Gen);
        }

        void tabpage3update(string flag)
        {
            panel24.Controls.Clear();
            panel24.Controls.Add(buttonload("Mod"));
            panel4.Controls.Clear();
            InsdInst.fctlid = fctlid;
            InsdInst.Class = Class;
            InsdInst.Classfctlid = Classfctlid;
            if (Class == "EEC") { InsdInst.fsclass = Gen.GenSubClass; }
            InsdInst.fbus = Fbus;
            InsdInst.ftype = "P";
            InsdInst.EffrDate = Gen.GenEffrDate;
            InsdInst.EftoDate = Gen.GenEftoDate;
            InsdInst.InsureComm = Gen.GenComm;
            InsdInst.InsureDisc = Gen.GenDisc;
            InsdInst.buttoncontrolupdate(flag);
            panel4.Controls.Add(InsdInst);
        }

        void tabpage4update(string flag)
        {
            panel13.Controls.Clear();
            panel13.Controls.Add(buttonload("Mod"));
            panel11.Controls.Clear();
            Excess.fctlid = fctlid;
            if (Class == "CAR" || Class == "PMP" || Class == "CMP")
            {
                Excess.panel12.Visible = true;
                Excess.panel13.Visible = true;
            }
            Excess.Class = Class;
            Excess.Classfctlid = Classfctlid;
            Excess.fbus = Fbus;
            Excess.ftype = "P";
            Excess.buttoncontrolupdate(flag);
            panel11.Controls.Add(Excess);
        }

        void tabpage5update(string flag)
        {
            panel28.Controls.Clear();
            panel28.Controls.Add(buttonload("Mod"));
            panel12.Controls.Clear();
            Attach.fctlid = fctlid;
            Attach.Class = Class;
            Attach.Classfctlid = Classfctlid;
            Attach.fbus = Fbus;
            Attach.ftype = "P";
            Attach.buttoncontrolupdate(flag);
            panel12.Controls.Add(Attach);
        }

        void tabpage6update(string flag)
        {
            panel27.Controls.Clear();
            panel27.Controls.Add(buttonload("Mod"));
            panel2.Controls.Clear();
            Install.fctlid = fctlid;
            Install.Class = Class;
            Install.Classfctlid = Classfctlid;
            Gen.installTotal = Install.installTotal;
            Install.fbus = Fbus;
            Install.ftype = "P";
            Install.buttoncontrolupdate(flag);
            panel2.Controls.Add(Install);

        }

        void tabpage2renew(string flag)
        {
            panel22.Controls.Clear();
            panel22.Controls.Add(buttonload("Mod"));
            panel1.Controls.Clear();
            Gen.fctlid = fctlid;
            Gen.Class = Class;
            Gen.Classfctlid = Classfctlid;
            Gen.fbus = Fbus;
            Gen.ftype = "P";
            Gen.buttoncontrolrenew(flag);
            panel1.Controls.Add(Gen);
        }

        private void renewattch_Click(object sender, EventArgs e)
        {
            renewopt tempform = new renewopt(this);
            tempform.fctlid = fctlid;
            tempform.ShowDialog();
            if (reopt == "2") { buttoncontrolrenew("renew"); }
            if (reopt != "2")
            {
                buttoncontrolback();
                tabpage1reload();
            }
        }

        private void modifyattch_Click(object sender, EventArgs e)
        {
            buttoncontrolupdate();
        }

        private void addattch_Click(object sender, EventArgs e)
        {
            buttoncontroladd();
        }

        private void cancelattch_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = tabPage1;
            buttoncontrolback();
            tabpage1reload();
        }

        private void exitattch_Click(object sender, EventArgs e)
        {
            exit();
        }

        private void delattch_Click(object sender, EventArgs e)
        {
            del();
        }

        private void printattch_Click(object sender, EventArgs e)
        {

            psche tempform = new psche(this);
            tempform.rpOptid = Class;
            tempform.fctlid = policyNo;
            tempform.fctlids = fctlid;
            tempform.dt = Policylist.DataSource as DataTable;
            tempform.vfbus = Fbus;
            tempform.ctrlbtn();
            tempform.ShowDialog();
            //pushA8 ToolsPush = new pushA8();
            //string EndtNo = "", fmntlabel="", policyNo="", Class="", fctlid="";
            //string sql = "select * from polh where fclass='PMP' AND finpdate > '2021-01-01' and fendtno <>'' order by fpolno";
            //DataTable dt = DBHelper.GetDataSet(sql);
            //if (dt.Rows.Count > 0)
            //{
            //    for (int i = 0; i < dt.Rows.Count; i++)
            //    {
            //        policyNo = dt.Rows[i]["fpolno"].ToString().Trim();
            //        Class = dt.Rows[i]["fclass"].ToString().Trim();
            //        fctlid = dt.Rows[i]["fctlid"].ToString().Trim();
            //        EndtNo = dt.Rows[i]["fendtno"].ToString().Trim();
            //        fmntlabel = dt.Rows[i]["fmntlabel"].ToString();
            //        string fileCI = "M:\\PushToA8\\" + policyNo.Trim() + "_CI.pdf";
            //        string fileCIEndtNo = "M:\\PushToA8\\" + EndtNo.Trim() + "_CI.pdf";
            //        if (System.IO.File.Exists(Path.GetFullPath(fileCI)))
            //        {
            //            File.Delete(Path.GetFullPath(fileCI));
            //            ToolsPush.wl_fpolno = policyNo.Trim();
            //            ToolsPush.pscheExport(CrystalDecisions.Shared.ExportFormatType.WordForWindows, "test.doc", Class, fctlid, "ToA8");
            //        }
            //        else {
            //            ToolsPush.wl_fpolno = policyNo.Trim();
            //            ToolsPush.pscheExport(CrystalDecisions.Shared.ExportFormatType.WordForWindows, "test.doc", Class, fctlid, "ToA8");
            //        }
            //        if (EndtNo != "")
            //        {
            //            if (System.IO.File.Exists(Path.GetFullPath(fileCIEndtNo)))
            //            {
            //                File.Delete(Path.GetFullPath(fileCIEndtNo));
            //                ToolsPush.wl_fpolno = EndtNo;
            //                ToolsPush.pendtExport(CrystalDecisions.Shared.ExportFormatType.WordForWindows, "test.doc", Class, fctlid, fmntlabel, "ToA8");
            //            }
            //            else
            //            {
            //                ToolsPush.wl_fpolno = EndtNo;
            //                ToolsPush.pendtExport(CrystalDecisions.Shared.ExportFormatType.WordForWindows, "test.doc", Class, fctlid, fmntlabel, "ToA8");

            //            }
            //        }
            //    }
            //}

        }



        private void pushattch_Click(object sender, EventArgs e)
        {
            pushA8 ToolsPush = new pushA8();
            if (policyNo.Trim() != "")
            {
                if (policyNo == "1")
                {
                    CopyFile cf = new CopyFile();
                    if (cf.WordToPdfSechedule(policyNo.Trim()) != "")
                    {// ToA8process(); }     
                    } 
                }
                else
                {
                    if ((Class == "PMP" || Class == "CMP") && fconfirm == "Hold")
                    {
                        //string sql = "select * from polh where fconfirm = 2 and (fclass ='PMP' or fclass ='CMP') and fctldel = ''";
                        //DataTable dt = DBHelper.GetDataSet(sql);
                        //for (int i = 0; i < dt.Rows.Count; i++)
                        //{
                        //    if (dt.Rows[i]["fconfirm"].ToString().Trim() == "2" && (dt.Rows[i]["fclass"].ToString().Trim() == "PMP" || dt.Rows[i]["fclass"].ToString().Trim() == "CMP") && dt.Rows[i]["fctldel"].ToString().Trim() == "")
                        //    {
                        //        string file = "M:\\PushToA8\\" + dt.Rows[i]["fpolno"].ToString().Trim() + ".pdf";

                        //        if (System.IO.File.Exists(Path.GetFullPath(file)))
                        //        {
                        //            File.Delete(Path.GetFullPath(file));
                        //            ToolsPush.wl_fpolno = policyNo.Trim();
                        //            ToolsPush.pscheExport(CrystalDecisions.Shared.ExportFormatType.WordForWindows, "test.doc", Class, dt.Rows[i]["fctlid"].ToString().Trim(), "ToA8");
                        //        }
                        //        else
                        //        {
                        //            ToolsPush.wl_fpolno = policyNo.Trim();
                        //            ToolsPush.pscheExport(CrystalDecisions.Shared.ExportFormatType.WordForWindows, "test.doc", Class, dt.Rows[i]["fctlid"].ToString().Trim(), "ToA8");
                        //        }
                        //        ToA8process(dt.Rows[i]["fclass"].ToString().Trim(), dt.Rows[i]["fconfirm"].ToString().Trim(), dt.Rows[i]["fpolno"].ToString().Trim(), dt.Rows[i]["fctlid"].ToString().Trim());
                        //    }
                        //}
                    }
                    else if((Class == "PMP" || Class == "CMP") && fconfirm == "Confirmed")
                    {
                        string file = "M:\\PushToA8\\" + policyNo.Trim() + ".pdf";
                        string fileCI = "M:\\PushToA8\\" + policyNo.Trim() + "_CI.pdf";
                        if (System.IO.File.Exists(Path.GetFullPath(file)) || System.IO.File.Exists(Path.GetFullPath(fileCI)))
                        {
                            File.Delete(Path.GetFullPath(file));
                            File.Delete(Path.GetFullPath(fileCI));
                            ToolsPush.wl_fpolno = policyNo.Trim();
                            ToolsPush.pscheExport(CrystalDecisions.Shared.ExportFormatType.WordForWindows, "test.doc", Class, fctlid, "ToA8");
                        }
                        else
                        {
                            ToolsPush.wl_fpolno = policyNo.Trim();
                            ToolsPush.pscheExport(CrystalDecisions.Shared.ExportFormatType.WordForWindows, "test.doc", Class, fctlid, "ToA8");
                        }
                        ToA8process(Class, fconfirm, policyNo, fctlid);
                    }
                    else
                    {
                        string file = "M:\\PushToA8\\" + policyNo.Trim() + ".pdf";
                        if (System.IO.File.Exists(Path.GetFullPath(file)))
                        {
                            File.Delete(Path.GetFullPath(file));
                            ToolsPush.wl_fpolno = policyNo.Trim();
                            ToolsPush.pscheExport(CrystalDecisions.Shared.ExportFormatType.WordForWindows, "test.doc", Class, fctlid, "ToA8");
                        }
                        else
                        {
                            ToolsPush.wl_fpolno = policyNo.Trim();
                            ToolsPush.pscheExport(CrystalDecisions.Shared.ExportFormatType.WordForWindows, "test.doc", Class, fctlid, "ToA8");
                        }
                       ToA8process(Class, fconfirm, policyNo, fctlid);
                    }
                }
            }
            else
            {
                MessageBox.Show("保单状态不能推送！", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void ToA8process(string fclass, string fconfirm, string policyNo, string fctlid)
        {
            try
            {

                //byte[] fileBytes = null;
                string str = ToA8.ToA8process(fclass, fconfirm, policyNo, fctlid);
                progressBar1.Value = 0;//设置当前值 
                string oldfctlid = fctlid;
                FillData("");
                DataTable dt3 = Policylist.DataSource as DataTable;
                foreach (DataRow row in dt3.Rows)
                {
                    int SelectedIndex = dt3.Rows.IndexOf(row);
                    if (oldfctlid == row["fctlid"].ToString().Trim())
                    {
                        Policylist.CurrentCell = Policylist.Rows[SelectedIndex].Cells["Policy#"];
                        policylistchg();
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString(), "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }


        private void noteattch_Click(object sender, EventArgs e)
        {
            
        }

        void exit()
        {
            buttoncontrolback();
            this.Close();
        }

        private void confirmattch_Click(object sender, EventArgs e)
        {
            confpol tempform = new confpol(this);
            tempform.fctlid = fctlid;
            tempform.tranStatus = fconfirm;
            tempform.autori = Gen.autori;
            tempform.Control();
            tempform.ShowDialog();

            Int32 nResult = tempform.optionNo;

            if (nResult == 0)
            {
                tempform.Dispose();
                return;
            }

            if (nResult == 2)
            {
                Gen.Skip_Validate = false;
                InsdInst.Skip_Validate = false;
                Excess.Skip_Validate = false;

                if (!Validation())
                {
                    tempform.Dispose();
                    buttoncontrolupdate();
                    return;
                }
            }

            tempform.confirmPolicy(nResult);

            if (ConfirmRlt != "No")
            {
                FillData("");
                tabControl1.SelectedTab = tabPage2;
                DataTable dt3 = Policylist.DataSource as DataTable;
                foreach (DataRow row in dt3.Rows)
                {
                    int SelectedIndex = dt3.Rows.IndexOf(row);
                    if (ConfirmRlt == row["fctlid"].ToString().Trim())
                    {
                        Policylist.CurrentCell = Policylist.Rows[SelectedIndex].Cells["Policy#"];
                        policylistchg();
                        break;
                    }
                }
                tabpage1reload();
            }

            tempform.Dispose();
            tabpage1reload();
            return;
        }

        private void saveattch_Click(object sender, EventArgs e)
        {
            Gen.Skip_Validate = false;
            InsdInst.Skip_Validate = false;
            Excess.Skip_Validate = false;
            string result = save();

            if (Addflag == true)
            {
                if (result == "OK")
                {
                    MessageBox.Show("Have Been Inserted!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                //else
                //{
                //    MessageBox.Show("Can't Been Inserted!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                //}
            }
            if (updateflag == true)
            {
                if (result == "OK")
                {
                    MessageBox.Show("Have Been Updated!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                //else
                //{
                //    MessageBox.Show("Can't Been Updated!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                //}
            }
            tabpage1reload();
        }

        public string rollback(string fctlid)
        {
            ArrayList delstring = new ArrayList();
            string deldatestr = "delete from polh where fctlid='" + fctlid + "'";
            delstring.Add(deldatestr);
            string deldatestr1 = "";
            if (Class == "CAR" || Class == "CSB" || Class == "MYP" || Class == "PAR")
            {
                deldatestr1 = "delete from oinsint where fctlid_p='" + fctlid + "'";
            }
            if (Class == "EEC")
            {
                deldatestr1 = "delete from oinsint_b where fctlid_p='" + fctlid + "'";
            }
            if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII" || Class == "GLF")
            {
                deldatestr1 = "delete from oinsint_c where fctlid_p='" + fctlid + "'";
            }
            if (Class == "CPM")
            {
                deldatestr1 = "delete from oinsint_d where fctlid_p='" + fctlid + "'";
            }
            if (Class == "PMP" || Class == "CMP")
            {
                deldatestr1 = "delete from oinsint_e where fctlid_p='" + fctlid + "'";
            }
            delstring.Add(deldatestr1);
            string deldatestr2 =
                "delete from oinsex where fctlid_p='" + fctlid + "'";
            delstring.Add(deldatestr2);
            string deldatestr3 =
                "delete from oinsatt where fctlid_p='" + fctlid + "'";
            delstring.Add(deldatestr3);
            string deldatestr4 =
                 "delete from oinvdet where fctlid_1='" + fctlid + "'";
            delstring.Add(deldatestr4);
            string deldatestr5 = "", deldatestr6 = "";
            if (Class == "CGL" || Class == "FGP" || Class == "GLF" || Class == "MYP" || Class == "PAR")
            {
                deldatestr5 = "delete from oinsloc where fctlid_p ='" + fctlid + "'";
                delstring.Add(deldatestr5);
            }
            if (Class == "FGP")
            {
                deldatestr6 = "delete from oinsper where fctlid_p ='" + fctlid + "'";
                delstring.Add(deldatestr6);
            }
            string deldatestr8 = "delete from orih where fctlid_1 ='" + fctlid + "'";
            delstring.Add(deldatestr8);
            string deldatestr9 = "";
            if (Class == "CAR")
            {
                deldatestr9 = "delete from orihc where fctlid_1 ='" + fctlid + "'";
            }
            delstring.Add(deldatestr9);
            string deldatestr10 = "";
            if (Class == "PAR" || Class == "CPM")
            {
                deldatestr10 = "delete from oril where fctlid_e ='" + fctlid + "'";
            }
            delstring.Add(deldatestr10);
            string deldatestr11 = "";
            if (Class == "PAR" || Class == "CPM")
            {
                deldatestr11 = "delete from orild1 where fctlid_e ='" + fctlid + "'";
            }
            else { deldatestr11 = "delete from orid1 where fctlid_1 ='" + fctlid + "'"; }
            delstring.Add(deldatestr11);
            string deldatestr12 = "";
            if (Class == "PAR" || Class == "CPM")
            {
                deldatestr12 = "delete from orild2 where fctlid_e ='" + fctlid + "'";
            }
            else { deldatestr12 = "delete from orid2 where fctlid_1 ='" + fctlid + "'"; }
            delstring.Add(deldatestr12);
            string deldatestr13 = "delete from orilayr where fctlid_1 ='" + fctlid + "'";
            delstring.Add(deldatestr13);
            string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
            string[] sql1 = (string[])delstring.ToArray(typeof(string));
            string result = ExecuteSqlTransaction(connectionString, sql1);
            if (result == "ok")
            {
                result = "RollBock";
            }
            else
            {
                result = "RollBock Error!";
            }
            return result;
        }

        private string ExecuteSqlTransaction(string connectionString, string[] sql1)
        {
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                SqlCommand command = connection.CreateCommand();
                SqlTransaction transaction;
                transaction = connection.BeginTransaction("SampleTransaction");

                command.Connection = connection;
                command.Transaction = transaction;

                try
                {
                    if (sql1 != null)
                    {
                        for (int i = 0; i < sql1.Length; i++)
                        {
                            if (sql1[i] != "" && sql1[i] != null)
                            {
                                command.CommandText = sql1[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    transaction.Commit();
                    return "OK";
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                    Console.WriteLine("  Message: {0}", ex.Message);

                    try
                    {
                        transaction.Rollback();
                        return "RollBack";
                    }
                    catch (Exception ex2)
                    {
                        Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                        Console.WriteLine("  Message: {0}", ex2.Message);
                        return ex2.Message;
                    }
                }
            }
        }

        string SaveCode()
        {
            Gen.totInvfgpm = Install.totInvfgpm;
            Gen.totInvftdamt = Install.totInvftdamt;
            Gen.totInvfcomamt = Install.totInvfcomamt;
            Gen.totInvfnpm = Install.totInvfnpm;
            Gen.totInvfeci1 = Install.totInvfeci1;
            Gen.totInvfeci2 = Install.totInvfeci2;
            Gen.totInvfeci3 = Install.totInvfeci3;
            Gen.totIAlevy = Install.totInvflvya;
            Gen.fprnexsec2 = Excess.fprnexsec2;
            Gen.installTotal = Install.installTotal;

            string result = "";
            string[] sql1 = Gen.tabpage2save();
            string newfctlid = Gen.newfctlid;
            string newcom = Gen.newcom;
            string newdisc = Gen.newdisc;
            string Subclass = Gen.GenSubClass;
            string[] sql2 = InsdInst.tabpage3save(newfctlid, Subclass);
            string[] sql3 = Excess.tabpage4save(newfctlid, Subclass);
            string[] sql4 = Attach.tabpage5save(newfctlid, Subclass);
            string[] sql5 = Install.tabpage6save(newfctlid);

            string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
            result = ExecuteSqlTransaction(connectionString, sql1, sql2, sql3, sql4, sql5);
            if (result == "OK")
            {
                if (Class == "PAR")
                {
                    if (updateflag == false) { fctlid = newfctlid; }
                    string sql = "update oinsint set fitem_1= b.fitem from oinsloc b where b.fclass='PAR' and b.fpolno =(select fpolno from polh where fctlid= '" + fctlid + "') and oinsint.fctlid_2= b.fctlid";
                    DBHelper.ExecuteCommand(sql);
                }

                if (newfctlid == "" || updateflag == true)
                {
                    if (Class == "CMP" || Class == "PMP" || Class == "CSB" || (Class == "EEC" && Gen.autori == "Yes")) { }
                    else
                    {
                        result = saveOrih(fctlid, "update");
                    }
                }
                else
                {
                    if (Class == "CMP" || Class == "PMP" || Class == "CSB" || (Class == "EEC" && Gen.autori == "Yes")) { }
                    else { result = saveOrih(newfctlid, "new"); }
                }

                if (result == "RollBack")
                {
                    result = rollback(newfctlid);
                    MessageBox.Show("RollBack", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    string oldfctlid = fctlid;
                    FillData("");
                    if ((newfctlid == "" || updateflag == true))
                    {
                        tabpage1Saveload(oldfctlid, newcom, newdisc);
                        DataTable dt3 = Policylist.DataSource as DataTable;
                        foreach (DataRow row in dt3.Rows)
                        {
                            int SelectedIndex = dt3.Rows.IndexOf(row);
                            if (oldfctlid == row["fctlid"].ToString().Trim())
                            {
                                Policylist.CurrentCell = Policylist.Rows[SelectedIndex].Cells["Policy#"];
                                policylistchg();
                                break;
                            }
                        }
                    }
                    else
                    {
                        tabpage1Saveload(newfctlid, newcom, newdisc);
                        DataTable dt3 = Policylist.DataSource as DataTable;
                        foreach (DataRow row in dt3.Rows)
                        {
                            int SelectedIndex = dt3.Rows.IndexOf(row);
                            if (newfctlid == row["fctlid"].ToString().Trim())
                            {
                                Policylist.CurrentCell = Policylist.Rows[SelectedIndex].Cells["Policy#"];
                                policylistchg();
                                break;
                            }
                        }
                    }
                    buttoncontrolback();
                }
            }
            else
            {
                MessageBox.Show(result, "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
                if (tabControl1.SelectedTab == tabPage2)
                    Gen.setControlFocus(Fbus == "D" ? "fuwyr" : "fcoins");
            }
            return result;
        }

        public string save()
        {
            string result = "";
            tabControl1.SelectedTab = tabPage6;
            tabControl1.SelectedTab = tabPage2;
            Gen.mmain = InsdInst.mmain;
            Gen.getMmain();
            if (!Validation())
            {
                DialogResult myResult = MessageBox.Show("Continue to Save?", "Save Warning", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
                if (myResult == DialogResult.OK)
                {
                    result = SaveCode();
                }
            }
            else { result = SaveCode(); }
            return result;
        }

        public Boolean Validation()
        {
            string ctrlName = "";
            ctrlName = Gen.tabpage2cofirm();
            if (ctrlName != "")
            {
                tabControl1.SelectedIndex = 1;
                Gen.setControlFocus(ctrlName);
                return false;
            }
            ctrlName = InsdInst.tabpage3cofirm();
            if (ctrlName != "")
            {
                tabControl1.SelectedIndex = 2;
                InsdInst.setControlFocus(ctrlName);
                return false;
            }
            InsdInst.u_calculation();
            ctrlName = Excess.tabpage4cofirm(InsdInst.mmain.Rows[0]["fliab"].ToString());
            if (ctrlName != "")
            {
                tabControl1.SelectedIndex = 3;
                Excess.setControlFocus(ctrlName);
                return false;
            }
            ctrlName = Install.tabpage6cofirm();
            if (ctrlName != "")
            {
                if (ctrlName == "installDgpm")
                {
                    tabControl1.SelectedIndex = 5;
                    Install.setControlFocus(ctrlName);
                }
                else
                {
                    tabControl1.SelectedIndex = 1;
                    Gen.setControlFocus(ctrlName);
                }
                return false;
            }
            return true;
        }



        void print()
        {

        }
        void del()
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the Policy All information?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string deldatestr =
                    "delete from polh where fctlid='" + fctlid + "'";
                string deldatestr1 = "";
                if (Class == "CAR" || Class == "CSB" || Class == "MYP" || Class == "PAR")
                {
                    deldatestr1 = "delete from oinsint where fctlid_p='" + fctlid + "'";
                }
                if (Class == "EEC")
                {
                    deldatestr1 = "delete from oinsint_b where fctlid_p='" + fctlid + "'";
                }
                if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII" || Class == "GLF")
                {
                    deldatestr1 = "delete from oinsint_c where fctlid_p='" + fctlid + "'";
                }
                if (Class == "CPM")
                {
                    deldatestr1 = "delete from oinsint_d where fctlid_p='" + fctlid + "'";
                }
                if (Class == "PMP" || Class == "CMP")
                {
                    deldatestr1 = "delete from oinsint_e where fctlid_p='" + fctlid + "'";
                }
                string deldatestr2 =
                    "delete from oinsex where fctlid_p='" + fctlid + "'";
                string deldatestr3 =
                    "delete from oinsatt where fctlid_p='" + fctlid + "'";
                string deldatestr4 =
                     "delete from oinvdet where fctlid_1='" + fctlid + "'";
                string deldatestr5 = "", deldatestr6 = "";
                if (Class == "CGL" || Class == "FGP" || Class == "GLF" || Class == "MYP" || Class == "PAR")
                {
                    deldatestr5 = "delete from oinsloc where fctlid_p ='" + fctlid + "'";
                }
                if (Class == "FGP")
                {
                    deldatestr6 = "delete from oinsper where fctlid_p ='" + fctlid + "'";
                }
                string deldatestr7 = "delete from orih where fctlid_1 ='" + fctlid + "'";
                string deldatestr8 = "";
                if (Class == "CAR")
                {
                    deldatestr8 = "delete from orihc where fctlid_1 ='" + fctlid + "'";
                }
                string deldatestr9 = "";
                if (Class == "PAR" || Class == "CPM")
                {
                    deldatestr9 = "delete from oril where fctlid_e ='" + fctlid + "'";
                }
                string deldatestr11 = "";
                if (Class == "PAR" || Class == "CPM")
                {
                    deldatestr11 = "delete from orild1 where fctlid_e ='" + fctlid + "'";
                }
                else { deldatestr11 = "delete from orid1 where fctlid_1 ='" + fctlid + "'"; }
                string deldatestr12 = "";
                if (Class == "PAR" || Class == "CPM")
                {
                    deldatestr12 = "delete from orild2 where fctlid_e ='" + fctlid + "'";
                }
                else { deldatestr12 = "delete from orid2 where fctlid_1 ='" + fctlid + "'"; }
                string deldatestr13 = "delete from orilayr where fctlid_1 ='" + fctlid + "'";
                string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();

                    SqlCommand command = connection.CreateCommand();
                    SqlTransaction transaction;

                    // Start a local transaction.
                    transaction = connection.BeginTransaction("SampleTransaction");

                    // Must assign both transaction object and connection 
                    // to Command object for a pending local transaction
                    command.Connection = connection;
                    command.Transaction = transaction;

                    try
                    {
                        command.CommandText = deldatestr;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr1;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr2;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr3;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr4;
                        command.ExecuteNonQuery();
                        if (deldatestr5 != "")
                        {
                            command.CommandText = deldatestr5;
                            command.ExecuteNonQuery();
                        }
                        if (deldatestr6 != "")
                        {
                            command.CommandText = deldatestr6;
                            command.ExecuteNonQuery();
                        }
                        if (deldatestr7 != "")
                        {
                            command.CommandText = deldatestr7;
                            command.ExecuteNonQuery();
                        }
                        if (deldatestr8 != "")
                        {
                            command.CommandText = deldatestr8;
                            command.ExecuteNonQuery();
                        }
                        if (deldatestr9 != "")
                        {
                            command.CommandText = deldatestr9;
                            command.ExecuteNonQuery();
                        }
                        if (deldatestr11 != "")
                        {
                            command.CommandText = deldatestr11;
                            command.ExecuteNonQuery();
                        }
                        if (deldatestr12 != "")
                        {
                            command.CommandText = deldatestr12;
                            command.ExecuteNonQuery();
                        }
                        if (deldatestr13 != "")
                        {
                            command.CommandText = deldatestr13;
                            command.ExecuteNonQuery();
                        }
                        transaction.Commit();
                        //MessageBox.Show("Have Been Deleted", "Warning",
                        //   MessageBoxButtons.OK, MessageBoxIcon.Information);
                        FillData("");
                        tabControl1.SelectedTab = tabPage1;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("Haven't Been Deleted", "Warning",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);

                        Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                        Console.WriteLine("  Message: {0}", ex.Message);

                        // Attempt to roll back the transaction. 
                        try
                        {
                            transaction.Rollback();
                        }
                        catch (Exception ex2)
                        {
                            // This catch block will handle any errors that may have occurred 
                            // on the server that would cause the rollback to fail, such as 
                            // a closed connection.
                            Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                            Console.WriteLine("  Message: {0}", ex2.Message);
                        }
                    }
                }
            }
        }

        private void Expiry_Click(object sender, EventArgs e)
        {
            //expnote tempform = new expnote(this);
            //tempform.fctlid_p = fctlid;
            //tempform.FillData();
            //tempform.ShowDialog();
        }

        public string saveOrih(string newfctlid, string flag)
        {
            ArrayList updarr = new ArrayList();
            string fkind = UtilityFunc.GetKindFrmClass(Class);
            string fxol = "", fcalc = "";
            if (Class == "EEC" || Class == "CMP" || Class == "CGL" || Class == "PMP")
            { fxol = "1"; }
            else { fxol = "2"; }
            if (Class == "CAR" || Class == "CPM" || Class == "FGP" || Class == "MYP" || Class == "PAR")
            { fcalc = "2"; }
            else { fcalc = "1"; }
            if (flag == "new")
            {
                string orih = Fct.NewId("orih");
                string orihc = Fct.NewId("orihc");
                string orih_fctlid = "";
                bool isSuccuss = false;
                string sql = "update " + Dbm + "[xsysparm] set fnxtid =RIGHT('000000000'+ LTRIM(STR(CAST((select max(fctlid) from orih) as int)+1)), 10) where fidtype ='orih'";
                DBHelper.GetScalar(sql);
                DataTable dt = new DataTable();
                if (Class == "CAR")
                {
                    string selsql = "select fctlid as fctlid_1,fctlid_p,fctlid_v,fbus,ftype,fendttype,fpolno,fendtno,fvseq,fvarno,fextend, " +
                                      "fsrcref,fcedepol,fcedeendt,fuwyr,fclass,fsclass,'01' as frino,isnull(b.fkind,'1') as fkind, " +
                                      "'" + orih + "' as fctlid,fissdate,fefffr,fefffr,feffto,'" + fcalc + "' as fcalc,fsicur,isnull(b.fsi,0) as fsi,'0.0000' as fliab, " +
                                      "isnull(b.fsi,0) as fupdsi,'0.0000' as fupdliab,'0.0000' as forgsi,'0.0000' as forgliab,fpmcur, " +
                                      "isnull(b.fgpm,0),isnull(b.fnpm,0), '" + fxol + "' as fxol,'0.0000' as fsi_c,'0.0000' as fsi2_c,'0.0000' as fgpm_c, " +
                                      "'0.0000' as fnpm_c,'0.0000' as fshare_c,finpuser,finpdate,fupduser,fupddate,fcnfuser,fcnfdate, " +
                                      "fconfirm,'2' as fposted from polh a  " +
                                      "left join (select sum(fgpm) as fgpm,sum(fnpm) as fnpm,sum(fsi) as fsi,fkind,fctlid_1 from oinsint  " +
                                      "where fctlid_1='" + newfctlid + "' and (fgpm+fnpm+fsi > 0) group by fkind,fctlid_1) b on a.fctlid = b.fctlid_1 " +
                                      "where fctlid='" + newfctlid + "'";
                    dt = DBHelper.GetDataSet(selsql);
                    if (dt.Rows.Count > 0)
                    {
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            if (i != 0) { orih = (int.Parse(orih) + 1).ToString().PadLeft(10, '0'); }
                            dt.Rows[i]["fctlid"] = orih;
                            orih_fctlid = dt.Rows[i]["fctlid"].ToString();
                        }
                        isSuccuss = DBHelper.BulkInsertDataTable("orih", dt);
                    }
                }
                else
                {
                    string selsql = "select fctlid as fctlid_1,fctlid_p,fctlid_v,fbus,ftype,fendttype,fpolno,fendtno,fvseq,fvarno,fextend,  " +
                                    "fsrcref,fcedepol,fcedeendt,fuwyr,fclass,fsclass,'01' as frino,'" + fkind + "' as fkind,  " +
                                    "'" + orih + "' as fctlid,fissdate,fefffr,fefffr,feffto,'" + fcalc + "' as fcalc,fsicur, " +
                                    "case when fclass='EEC' OR fclass='CMP' OR fclass='PMP' then flmt1 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then fliab else fsi end end as fsi, " +
                                    "case when fclass='CMP' OR fclass='PMP' then flmt2 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then 0 else fliab end end as fsi2, " +
                                    "case when fclass='EEC' OR fclass='CMP' OR fclass='PMP' then fupdlmt1 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then fupdliab else fupdsi end end as fupdsi, " +
                                    "case when fclass='CMP' OR fclass='PMP' then fupdlmt2 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then 0 else fupdliab end end as fupdsi2, " +
                                    "'0.0000' as forgsi,'0.0000' as forgliab,fpmcur, fgpm,fnpm,'" + fxol + "' as fxol, " +
                                    "case when fclass='EEC' OR fclass='CMP' OR fclass='PMP' then flmt1 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then fliab else fsi end end as fsi_c, " +
                                    "case when fclass='CMP' OR fclass='PMP' then flmt2 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then 0 else fliab end end as fsi2_c, " +
                                    "fgpm as fgpm_c, fnpm as fnpm_c,'100.0000' as fshare_c, " +
                                    "finpuser,finpdate,fupduser,fupddate,fcnfuser,fcnfdate,fconfirm,'2' as fposted from polh " +
                                    "where fctlid='" + newfctlid + "'";
                    dt = DBHelper.GetDataSet(selsql);
                    if (dt.Rows.Count > 0)
                    {
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            if (i != 0) { orih = (int.Parse(orih) + 1).ToString().PadLeft(10, '0'); }
                            dt.Rows[i]["fctlid"] = orih;
                            orih_fctlid = dt.Rows[i]["fctlid"].ToString();
                        }
                        isSuccuss = DBHelper.BulkInsertDataTable("orih", dt);
                    }
                }
                if (isSuccuss == true)
                {
                    if (orih_fctlid != "")
                    {
                        string updorih = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(orih_fctlid) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='orih'";
                        updarr.Add(updorih);
                    }
                    if (Class == "CAR" && dt.Rows.Count > 0)
                    {
                        string sqlorihc = "insert into orihc " +
                            "select fctlid,fbus,ftype,fpolno,fvseq,fvarno,fendtno,fsrcref,fcedepol,fcedeendt,fuwyr,fclass, " +
                            "fsclass,'01' as frino,'" + orihc + "' as fctlid,finpuser,finpdate,fupduser,fupddate,fcnfuser,fcnfdate,fconfirm, " +
                            "'1' as fstatus1,'2' as fposted " +
                            "from polh where fctlid='" + newfctlid + "'";
                        string updorihc = "update " + Dbm + "xsysparm set fnxtid=RIGHT('000000000'+ LTRIM(STR(CAST('" + orihc + "' as int)+1)), 10) where fidtype ='orihc'";
                        updarr.Add(sqlorihc);
                        updarr.Add(updorihc);
                    }
                    if (Class == "PAR" || Class == "CPM")
                    {
                        string oril = Fct.NewId("oril"); string oril_fctlid = "", selsql = "";
                        if (Class == "PAR")
                        {
                            selsql = "select a.fctlid as fctlid_1,a.fctlid_p,a.fctlid_v,b.fctlid as fctlid_ri,a.fbus,a.ftype,a.fpolno,a.fvseq,a.fvarno,a.fendtno,a.fendttype, a.fextend,  " +
                                            "a.fsrcref,a.fcedepol,a.fcedeendt,a.fuwyr,a.fclass,a.fsclass,'01' as frino,'" + fkind + "' as fkind,  " +
                                            "'" + oril + "' as fctlid,'' as flocid,c.flogseq,c.fitem,c.fmode,a.fissdate,a.fefffr,a.fefffr,a.feffto, " +
                                            "c.frm,c.fflr,c.fblk,c.flot,c.fbldg,c.fstno,c.fstno2,c.fst,c.fdist,0 as fqty,0 as fupdqty,'' as funit,'' as fyr,'' as fdesc1, " +
                                            "'" + fcalc + "' as fcalc,fsicur,c.fsi,'0.0000' as fsi2,c.fupdsi,'0.0000' as fupdsi2,'0.0000' as fosi, " +
                                            "'0.0000' as fosi2,a.fpmcur, c.fgpm,c.fnpm,'" + fxol + "' as fxol, " +
                                            "c.fsi as fsi_c,'0.0000' as fsi2_c,c.fgpm as fgpm_c, c.fnpm as fnpm_c,'100.0000' as fshare_c " +
                                            "from polh a left join oinsloc c on a.fctlid = c.fctlid_1 left join orih b on b.fctlid_1 = a.fctlid " +
                                            "where a.fctlid='" + newfctlid + "'";
                        }
                        if (Class == "CPM")
                        {
                            selsql = "select a.fctlid as fctlid_1,a.fctlid_p,a.fctlid_v,b.fctlid as fctlid_ri,a.fbus,a.ftype,a.fpolno,a.fvseq,a.fvarno,a.fendtno,a.fendttype, a.fextend,  " +
                                    "a.fsrcref,a.fcedepol,a.fcedeendt,a.fuwyr,a.fclass,a.fsclass,'01' as frino,'" + fkind + "' as fkind,  " +
                                    "'" + oril + "' as fctlid,'' as flocid,c.flogseq,c.fitem,c.fmode,a.fissdate,a.fefffr,a.fefffr,a.feffto, " +
                                    "'' as frm,'' as fflr,'' as fblk,'' as flot,'' as fbldg,'' as fstno,'' as fstno2,'' as fst,'' as fdist, " +
                                    "c.fqty as fqty,c.fupdqty as fupdqty,c.funit as funit,c.fyr as fyr,c.fdesc1 as fdesc1, " +
                                    "'" + fcalc + "' as fcalc,fsicur,c.fsi,'0.0000' as fsi2,c.fupdsi,'0.0000' as fupdsi2,'0.0000' as fosi, " +
                                    "'0.0000' as fosi2,a.fpmcur, c.fgpm,c.fnpm,'" + fxol + "' as fxol, " +
                                    "c.fsi as fsi_c,'0.0000' as fsi2_c,c.fgpm as fgpm_c, c.fnpm as fnpm_c,'100.0000' as fshare_c " +
                                    "from polh a left join oinsint_d c on a.fctlid = c.fctlid_1 left join orih b on b.fctlid_1 = a.fctlid " +
                                    "where a.fctlid='" + newfctlid + "'";
                        }
                        dt = DBHelper.GetDataSet(selsql);
                        if (dt.Rows.Count > 0)
                        {
                            for (int i = 0; i < dt.Rows.Count; i++)
                            {
                                if (i != 0) { oril = (int.Parse(oril) + 1).ToString().PadLeft(10, '0'); }
                                dt.Rows[i]["fctlid"] = oril;
                                oril_fctlid = dt.Rows[i]["fctlid"].ToString();
                            }
                            isSuccuss = DBHelper.BulkInsertDataTable("oril", dt);
                            if (isSuccuss == true && oril_fctlid != "")
                            {
                                string updoril = "update " + Dbm + "xsysparm set fnxtid=RIGHT('000000000'+ LTRIM(STR(CAST('" + oril_fctlid + "' as int)+1)), 10) where fidtype ='oril'";
                                updarr.Add(updoril);
                            }
                        }
                    }
                    isSuccuss = DBHelper.ExecuteSqlTransaction((string[])updarr.ToArray(typeof(string)));
                }
                if (isSuccuss == true) { return "OK"; }
                else { return "RollBack"; }
            }
            else
            {
                bool isSuccuss = false;
                if (Class == "CAR")
                {
                    string sqlupdorih = "UPDATE orih SET orih.fpolno = b.fpolno,orih.fendtno = b.fendtno,orih.[fcedepol] = b.fcedepol, " +
                    "orih.[fcedeendt] = b.fcedeendt,orih.[fuwyr] = b.fuwyr,orih.[fclass] = b.fclass,orih.[fsclass] = b.fsclass, " +
                    "orih.[fissdate] = b.fissdate,orih.[fincpdate] = b.fincpdate,orih.[fefffr] = b.fefffr, orih.[feffto] = b.feffto, " +
                    "orih.[fsi] = b.fsi,orih.[fsi2] = b.fliab,orih.[fupdsi] = b.fupdsi,orih.[fupdsi2] = b.fupdliab, " +
                    "orih.[fosi] = b.forgsi,orih.[fosi2] = b.forgliab,orih.[fgpm] = b.fgpm,orih.[fnpm] = b.fnpm, " +
                    "orih.[finpuser] = b.finpuser,orih.[finpdate] = b.finpdate, orih.[fupduser] = b.fupduser, " +
                    "orih.[fupddate] = b.fupddate, orih.[fcnfuser] = b.fcnfuser,orih.[fcnfdate] = b.fcnfdate,  " +
                    "orih.[fstatus] = b.fconfirm " +
                    "FROM orih INNER JOIN " +
                    "(select fctlid,b.fkind,a.fpolno,fendtno,fcedepol,fcedeendt,fuwyr,fclass,fsclass, " +
                    "fissdate,fefffr as fincpdate,fefffr,feffto,b.fsi,'0.0000' as fliab,  " +
                    "b.fsi as fupdsi,'0.0000' as fupdliab,'0.0000' as forgsi,'0.0000' as forgliab,fpmcur,  " +
                    "b.fgpm,b.fnpm,finpuser,finpdate,fupduser,fupddate,fcnfuser,fcnfdate,  " +
                    "fconfirm from polh a   " +
                    "left join (select sum(fgpm) as fgpm,sum(fnpm) as fnpm,sum(fsi) as fsi,fkind,fctlid_1 from oinsint   " +
                    "where fctlid_1='" + newfctlid + "' group by fkind,fctlid_1) b on a.fctlid = b.fctlid_1  " +
                    "where fctlid='" + newfctlid + "') as b " +
                    "ON orih.fctlid_1 = b.fctlid and orih.fkind = b.fkind";
                    updarr.Add(sqlupdorih);
                }
                else
                {
                    string sqlupdorih = "UPDATE orih SET orih.fpolno = b.fpolno,orih.fendtno = b.fendtno,orih.[fcedepol] = b.fcedepol, " +
                   "orih.[fcedeendt] = b.fcedeendt,orih.[fuwyr] = b.fuwyr,orih.[fclass] = b.fclass,orih.[fsclass] = b.fsclass, " +
                   "orih.[fissdate] = b.fissdate,orih.[fincpdate] = b.fincpdate,orih.[fefffr] = b.fefffr, orih.[feffto] = b.feffto, " +
                   "orih.[fsi] = b.fsi,orih.[fsi2] = b.fsi2,orih.[fupdsi] = b.fupdsi,orih.[fupdsi2] = b.fupdsi2, " +
                   "orih.[fosi] = b.forgsi,orih.[fosi2] = b.forgliab,orih.[fgpm] = b.fgpm,orih.[fnpm] = b.fnpm, " +
                   "orih.[finpuser] = b.finpuser,orih.[finpdate] = b.finpdate, orih.[fupduser] = b.fupduser, " +
                   "orih.[fupddate] = b.fupddate, orih.[fcnfuser] = b.fcnfuser,orih.[fcnfdate] = b.fcnfdate,  " +
                   "orih.[fstatus] = b.fconfirm,orih.[fnpm_c] = b.fnpm,orih.[fgpm_c] = b.fgpm " +
                   "FROM orih INNER JOIN " +
                   "(select fctlid,'" + fkind + "' as fkind,fpolno,fendtno,fcedepol,fcedeendt,fuwyr,fclass,fsclass, " +
                   "fissdate,fefffr as fincpdate,fefffr,feffto,'" + fcalc + "' as fcalc,fsicur, " +
                    //"case when fclass='EEC' OR fclass='CMP' OR fclass='PMP' then flmt1 else fsi end as fsi," +
                    //"case when fclass='CMP' OR fclass='PMP' then flmt2 else fliab end as fsi2, " +
                    //"case when fclass='EEC' OR fclass='CMP' OR fclass='PMP' then fupdlmt1 else fupdsi end as fupdsi, " +
                    //"case when fclass='CMP' OR fclass='PMP' then fupdlmt2 else fupdliab end as fupdsi2, " +
                    "case when fclass='EEC' OR fclass='CMP' OR fclass='PMP' then flmt1 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then fliab else fsi end end as fsi, " +
                    "case when fclass='CMP' OR fclass='PMP' then flmt2 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then 0 else fliab end end as fsi2, " +
                    "case when fclass='EEC' OR fclass='CMP' OR fclass='PMP' then fupdlmt1 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then fupdliab else fupdsi end end as fupdsi, " +
                    "case when fclass='CMP' OR fclass='PMP' then fupdlmt2 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then 0 else fupdliab end end as fupdsi2, " +
                    "'0.0000' as forgsi,'0.0000' as forgliab,fpmcur, " +
                   "fgpm,fnpm,finpuser,finpdate,fupduser,fupddate,fcnfuser,fcnfdate, " +
                   "fconfirm,'2' as fposted from polh " +
                   "where fctlid='" + newfctlid + "') as b " +
                   "ON orih.fctlid_1 = b.fctlid and orih.fkind = b.fkind";
                    updarr.Add(sqlupdorih);
                }

                if (Class == "CAR")
                {
                    string sqlupdorihc = "UPDATE orihc SET orihc.fpolno = b.fpolno,orihc.fendtno = b.fendtno,orihc.[fuwyr] = b.fuwyr,orihc.[fclass] = b.fclass,orihc.[fsclass] = b.fsclass, " +
                    "orihc.[finpuser] = b.finpuser,orihc.[finpdate] = b.finpdate, orihc.[fupduser] = b.fupduser, " +
                    "orihc.[fupddate] = b.fupddate, orihc.[fcnfuser] = b.fcnfuser,orihc.[fcnfdate] = b.fcnfdate,  " +
                    "orihc.[fstatus] = b.fconfirm " +
                    "FROM orihc INNER JOIN " +
                    "(select fctlid,fpolno,fendtno,fuwyr,fclass, " +
                    "fsclass,finpuser,finpdate,fupduser,fupddate,fcnfuser,fcnfdate,fconfirm " +
                    "from polh where fctlid='" + newfctlid + "') as b " +
                    "ON orihc.fctlid_1 = b.fctlid ";
                    updarr.Add(sqlupdorihc);
                }
                if (Class == "PAR" || Class == "CPM")
                {
                    string deloril = "delete from oril where fctlid_e='" + newfctlid + "'";
                    isSuccuss = DBHelper.ExecuteCommand(deloril);
                    if (isSuccuss == true)
                    {
                        string oril = Fct.NewId("oril"); string oril_fctlid = "", selsql = "";
                        if (Class == "PAR")
                        {
                            selsql = "select a.fctlid as fctlid_1,a.fctlid_p,a.fctlid_v,b.fctlid as fctlid_ri,a.fbus,a.ftype,a.fpolno,a.fvseq,a.fvarno,a.fendtno,a.fendttype, a.fextend,  " +
                                            "a.fsrcref,a.fcedepol,a.fcedeendt,a.fuwyr,a.fclass,a.fsclass,'01' as frino,'" + fkind + "' as fkind,  " +
                                            "'" + oril + "' as fctlid,'' as flocid,c.flogseq,c.fitem,c.fmode,a.fissdate,a.fefffr,a.fefffr,a.feffto, " +
                                            "c.frm,c.fflr,c.fblk,c.flot,c.fbldg,c.fstno,c.fstno2,c.fst,c.fdist,0 as fqty,0 as fupdqty,'' as funit,'' as fyr,'' as fdesc1, " +
                                            "'" + fcalc + "' as fcalc,fsicur,c.fsi,'0.0000' as fsi2,c.fupdsi,'0.0000' as fupdsi2,'0.0000' as fosi, " +
                                            "'0.0000' as fosi2,a.fpmcur, c.fgpm,c.fnpm,'" + fxol + "' as fxol, " +
                                            "c.fsi as fsi_c,'0.0000' as fsi2_c,c.fgpm as fgpm_c, c.fnpm as fnpm_c,'100.0000' as fshare_c " +
                                            "from polh a left join oinsloc c on a.fctlid = c.fctlid_1 left join orih b on b.fctlid_1 = a.fctlid " +
                                            "where a.fctlid='" + newfctlid + "'";
                        }
                        if (Class == "CPM")
                        {
                            selsql = "select a.fctlid as fctlid_1,a.fctlid_p,a.fctlid_v,b.fctlid as fctlid_ri,a.fbus,a.ftype,a.fpolno,a.fvseq,a.fvarno,a.fendtno,a.fendttype, a.fextend,  " +
                                    "a.fsrcref,a.fcedepol,a.fcedeendt,a.fuwyr,a.fclass,a.fsclass,'01' as frino,'" + fkind + "' as fkind,  " +
                                    "'" + oril + "' as fctlid,'' as flocid,c.flogseq,c.fitem,c.fmode,a.fissdate,a.fefffr,a.fefffr,a.feffto, " +
                                    "'' as frm,'' as fflr,'' as fblk,'' as flot,'' as fbldg,'' as fstno,'' as fstno2,'' as fst,'' as fdist, " +
                                    "c.fqty as fqty,c.fupdqty as fupdqty,c.funit as funit,c.fyr as fyr,c.fdesc1 as fdesc1, " +
                                    "'" + fcalc + "' as fcalc,fsicur,c.fsi,'0.0000' as fsi2,c.fupdsi,'0.0000' as fupdsi2,'0.0000' as fosi, " +
                                    "'0.0000' as fosi2,a.fpmcur, c.fgpm,c.fnpm,'" + fxol + "' as fxol, " +
                                    "c.fsi as fsi_c,'0.0000' as fsi2_c,c.fgpm as fgpm_c, c.fnpm as fnpm_c,'100.0000' as fshare_c " +
                                    "from polh a left join oinsint_d c on a.fctlid = c.fctlid_1 left join orih b on b.fctlid_1 = a.fctlid " +
                                     "where a.fctlid='" + newfctlid + "'";
                        }
                        DataTable dt = DBHelper.GetDataSet(selsql);
                        if (dt.Rows.Count > 0)
                        {
                            for (int i = 0; i < dt.Rows.Count; i++)
                            {
                                if (i != 0) { oril = (int.Parse(oril) + 1).ToString().PadLeft(10, '0'); }
                                dt.Rows[i]["fctlid"] = oril;
                                oril_fctlid = dt.Rows[i]["fctlid"].ToString();
                            }
                            isSuccuss = DBHelper.BulkInsertDataTable("oril", dt);
                            if (isSuccuss == true)
                            {
                                string updoril = "update " + Dbm + "xsysparm set fnxtid=RIGHT('000000000'+ LTRIM(STR(CAST('" + oril_fctlid + "' as int)+1)), 10) where fidtype ='oril'";
                                updarr.Add(updoril);
                            }
                        }
                    }
                }
                DBHelper.ExecuteSqlTransaction((string[])updarr.ToArray(typeof(string)));
                return "";
            }
        }

        private string ExecuteSqlTransaction(string connectionString, string[] sql1, string[] sql2, string[] sql3, string[] sql4, string[] sql5)
        {
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                SqlCommand command = connection.CreateCommand();
                SqlTransaction transaction;
                transaction = connection.BeginTransaction("SampleTransaction");
                command.Connection = connection;
                command.Transaction = transaction;

                try
                {
                    if (sql1 != null)
                    {
                        for (int i = 0; i < sql1.Length; i++)
                        {
                            if (sql1[i] != "" && sql1[i] != null)
                            {
                                command.CommandText = sql1[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (sql2 != null)
                    {
                        for (int i = 0; i < sql2.Length; i++)
                        {
                            if (sql2[i] != "" && sql2[i] != null && sql2[i].Length != 10 && sql2[i].Length > 1)
                            {
                                command.CommandText = sql2[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }

                    if (sql3 != null)
                    {
                        for (int i = 0; i < sql3.Length; i++)
                        {
                            if (sql3[i] != "" && sql3[i] != null)
                            {
                                command.CommandText = sql3[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (sql4 != null)
                    {
                        for (int i = 0; i < sql4.Length; i++)
                        {
                            if (sql4[i] != "" && sql4[i] != null)
                            {
                                command.CommandText = sql4[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (sql5 != null)
                    {
                        for (int i = 0; i < sql5.Length; i++)
                        {
                            if (sql5[i] != "" && sql5[i] != null)
                            {
                                command.CommandText = sql5[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    transaction.Commit();
                    return "OK";
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                    Console.WriteLine("  Message: {0}", ex.Message);

                    try
                    {
                        transaction.Rollback();
                        return "RollBack";
                    }
                    catch (Exception ex2)
                    {
                        Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                        Console.WriteLine("  Message: {0}", ex2.Message);
                        return ex2.Message;
                    }
                }
            }
        }

        private void tabPage6_MouseClick(object sender, MouseEventArgs e)
        {
            this.tabPage6.Focus();
        }

        private void tabPage6_MouseWheel(object sender, MouseEventArgs e)
        {
            tabPage6.VerticalScroll.Value += 10;
            tabPage6.Refresh();
            tabPage6.Invalidate();
            tabPage6.Update();
        }

        private void ReQuery_Click(object sender, EventArgs e)
        {
            inqpol tempform = new inqpol(this);
            tempform.Class = Class;
            tempform.Classfctlid = Classfctlid;
            tempform.Text = "Inquiry - " + ClassType(Class);
            tempform.flag = "Direct";
            tempform.InitializeSubclassName();
            tempform.ShowDialog();
        }

        static string ClassType(string value)
        {
            string Name = "";
            switch (value)
            {
                case "CAR":
                    Name = "Contractors' All Risks";
                    break;
                case "EEC":
                    Name = "Employees' Compensation";
                    break;
                case "CGL":
                    Name = "Public Liability / General Liability";
                    break;
                case "CPM":
                    Name = "Contractors' Plant and Machinery";
                    break;
                case "MYP":
                    Name = "Money Insurance";
                    break;
                case "PAR":
                    Name = "Property All Risks";
                    break;
                case "FGP":
                    Name = "Fidelity Guarantee";
                    break;
                case "PMP":
                    Name = "Private Car Insurance";
                    break;
                case "CMP":
                    Name = "Commercial Vehicle Insurance";
                    break;
                case "CSB":
                    Name = "Bond Insurance";
                    break;
                case "PII":
                    Name = "Professional Indemnity Insurance";
                    break;
                case "GLF":
                    Name = "Golf Insurance";
                    break;
                case "CLL":
                    Name = "Contractor Liability";
                    break;
            }
            return Name;
        }

        private void OrderBy_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (OrderBy.Text.ToString() == "Contractors")
            {
                FillData("order by " + OrderBy.Text.ToString());
            }
            else
            {
                FillData("order by " + OrderBy.Text.ToString() + "#");
            }
        }

        public enum Mode
        {
            Policy = 1,
            Producer = 2,
            Contractors = 3,
        }

        private void InitCombobox()
        {
            Array arr = System.Enum.GetValues(typeof(Mode));    // 获取枚举的所有值
            DataTable dt = new DataTable();
            dt.Columns.Add("String", Type.GetType("System.String"));
            dt.Columns.Add("Value", typeof(int));
            foreach (var a in arr)
            {
                string strText = EnumTextByDescription.GetEnumDesc((Mode)a);
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = (int)a;

                dt.Rows.Add(aRow);
            }

            OrderBy.DataSource = dt;
            OrderBy.DisplayMember = "String";
            OrderBy.ValueMember = "Value";
        }

        private void DirectPolicy_Resize(object sender, EventArgs e)
        {
            if (WindowState == FormWindowState.Maximized)
            {
                //最大化时所需的操作 
                // this.ClientSize = new System.Drawing.Size(916, 676);
                // this.WindowState = FormWindowState.Normal;
            }
            else if (WindowState == FormWindowState.Minimized)
            {
                //最小化时所需的操作

                this.ClientSize = new System.Drawing.Size(50, 50);
                this.WindowState = FormWindowState.Normal;
            }
        }

        private void copy_Click(object sender, EventArgs e)
        {
            buttoncontrolrenew("Copy");
        }
    }

}
