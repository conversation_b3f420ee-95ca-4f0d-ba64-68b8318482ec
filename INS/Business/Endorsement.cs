using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Configuration;
using INS.Ctrl;
using INS.Ctrl.Endor;
using INS.Business.SearchForm;
using System.Collections;
using INS.Business.Report;
using System.IO;
using System.Net.Http;
using System.Web.Script.Serialization;

namespace INS.Business
{
    public partial class Endorsement : Form
    {
        DBConnect operate = new DBConnect();
        public string Class = InsEnvironment.Minsc.GetFid();
        public string Classfctlid = InsEnvironment.Minsc.GetFctlid();
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public string SubClass = "", fendtype = "", fexten = "", policyNo = "", EndtNo = "";
        public string fctlid = "", fctlid_v = "", fctlid_p = "",fctldel= "", fmntlabel ="";
        public string fconfirm = "", fconfirmCheck = "", ConfirmRlt = "";
        public string com = "", disc = "", fbus = "";
        public Boolean Addflag = false;
        public Boolean updateflag = false;
        public int Policylistrow = 0;

        EndorDirectGen Gen = new EndorDirectGen();
        EndorExcess Excess = new EndorExcess();
        EndorAttach Attach = new EndorAttach();
        EndorInstall Install = new EndorInstall();
        EndorPrint Print = new EndorPrint();
        EndorDirectInsured_CAR Insured = new EndorDirectInsured_CAR();
        EndorDirectInsured_CGL InsuredC = new EndorDirectInsured_CGL();
        EndorDirectInsured_CPM InsuredD = new EndorDirectInsured_CPM();
        EndorDirectInsured_PMP InsuredE = new EndorDirectInsured_PMP();
        EndorDirectInsured_EEC InsuredB = new EndorDirectInsured_EEC();
        EndorDirectInsured_PAR Insured2 = new EndorDirectInsured_PAR();

        private DirectGen directGen;
        private RIGen rIGen;
        private EndorRIGen endorRIGen;

        private dynamic InsdInst = null;
        private string InsdFocus;

        public Endorsement()
        {
            InitializeComponent();
            DetermineInsdInst(Class);
        }

        public Endorsement(DirectGen directGen)
        {
            // TODO: Complete member initialization
            this.directGen = directGen;
            InitializeComponent();
            DetermineInsdInst(Class);
        }

        public Endorsement(RIGen rIGen)
        {
            // TODO: Complete member initialization
            this.rIGen = rIGen;
            InitializeComponent();
            DetermineInsdInst(Class);
        }

        public Endorsement(EndorRIGen endorRIGen)
        {
            // TODO: Complete member initialization
            this.endorRIGen = endorRIGen;
            InitializeComponent();
            DetermineInsdInst(Class);
        }

        private void DetermineInsdInst(string clsstr)
        {
            if (fbus == "K")
            {
                panel1.Location = new System.Drawing.Point(7, 4);
                panel22.Location = new System.Drawing.Point(49, 737);
                panel1.Size = new System.Drawing.Size(812, 701);
            }
            else
            {
                panel1.Location = new System.Drawing.Point(7, -40);
            }

            if (Class == "CAR" || Class == "CSB")
            {
                InsdInst = Insured;
                InsdFocus = "afid";
            }

            if (Class == "MYP" || Class == "PAR")
            {
                InsdInst = Insured2;
                InsdFocus = "seql";
            }

            if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII")
            {
                InsdInst = InsuredC;
                InsdFocus = "fseq";
            }

            if (Class == "CPM")
            {
                InsdInst = InsuredD;
                InsdFocus = "seq";
            }

            if (Class == "PMP" || Class == "CMP")
            {
                InsdInst = InsuredE;
                InsdFocus = "seq";
            }

            if (Class == "EEC")
            {
                InsdInst = InsuredB;
                InsdFocus = "fid";
            }

        }

        public void FillData(string order)
        {
            String Sql = "select fmntlabel,fctlid_p, fvseq, fctlid, fcom, ftd, fsclass, fpolno as Policy#,case when fvseq=0 then null else fvseq end as V#, fvarno as VO, fendtno as [Endt No#],CONVERT(varchar(10),fissdate,102) as [Issue Date],CONVERT(varchar(10),fefffr,102) as [Effect Fr],CONVERT(varchar(10),feffto,102) as [Effect To], " +
                        "fprdr as Producer#,finsd as Insured, " +
                        "case when fconfirm='1' then 'Pending' else  " +
                        "case when fconfirm='2' then 'Hold' else " +
                        "case when fconfirm='3' then 'Confirmed' else 'Cancelled' end end end as Status, " +
                        "case when CHARINDEX('Send',rtrim(fctldel)) > 0 then '已發給客戶'  else   " +
                        "case when CHARINDEX('Finished',rtrim(fctldel)) > 0  then '審批完成' " +
                        "else case when len(rTRIM(fctldel))>10 then '已傳A8' else '' end end end as PushToA8 " +
                        "from polh where fbus='" + fbus + "' and ftype = 'E' AND fclass ='" + Class + "' AND fctlid_p ='" + fctlid_p + "'  order by case when fconfirm=1 then '1' when fconfirm='2' then '2' when fconfirm='4' then'4' else '3' END,V#,fendtno desc";
            Policylist.DataSource = DBHelper.GetDataSet(Sql);
            this.Policylist.Columns[0].Visible = false;
            this.Policylist.Columns[1].Visible = false;
            this.Policylist.Columns[2].Visible = false;
            this.Policylist.Columns[3].Visible = false;
            this.Policylist.Columns[4].Visible = false;
            this.Policylist.Columns[5].Visible = false;
            this.Policylist.Columns[6].Visible = false;
            Policylist.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.Policylist_CellFormatting);

            if (Policylist.Rows.Count == 0)
            {
                Sql = "select fctlid_p, fvseq, fctlid, fcom, ftd, fsclass, fpolno as Policy#,case when fvseq=0 then null else fvseq end as V#, fvarno as VO, fendtno as [Endt No#],CONVERT(varchar(10),fissdate,102) as [Issue Date],CONVERT(varchar(10),fefffr,102) as [Effect Fr],CONVERT(varchar(10),feffto,102) as [Effect To], " +
                        "fprdr as Producer#,finsd as Insured, " +
                        "case when fconfirm='1' then 'Pending' else  " +
                        "case when fconfirm='2' then 'Hold' else " +
                        "case when fconfirm='3' then 'Confirmed' else 'Cancelled' end end end as Status, " +
                        "case when CHARINDEX('Send',rtrim(fctldel)) > 0 then '已發給客戶'  else   " +
                        "case when CHARINDEX('Finished',rtrim(fctldel)) > 0  then '審批完成' " +
                        "else case when len(rTRIM(fctldel))>10 then '已傳A8' else '' end end end as PushToA8 " +
                        "from polh where fbus='" + fbus + "' AND fclass ='" + Class + "' AND fctlid_p ='" + fctlid_p + "' order by case when fconfirm=1 then '1' when fconfirm='2' then '2' when fconfirm='4' then'4' else '3' END,V#,fendtno desc";
                DataTable dt = DBHelper.GetDataSet(Sql);
                if (dt != null && dt.Rows.Count > 0)
                {
                    fctlid = dt.Rows[0]["fctlid"].ToString().Trim();
                    policyNo = dt.Rows[0]["Policy#"].ToString().Trim();
                    EndtNo = dt.Rows[0]["Endt No#"].ToString().Trim();
                    fconfirm = dt.Rows[0]["Status"].ToString().Trim();
                    com = dt.Rows[0]["fcom"].ToString().Trim();
                    disc = dt.Rows[0]["ftd"].ToString().Trim();
                    SubClass = dt.Rows[0]["fsclass"].ToString().Trim();
                }
                panel10.Controls.Clear();
                panel10.Controls.Add(buttonload("Load"));
            }
            else
            {
                panel10.Controls.Clear();
                panel10.Controls.Add(buttonload("Load"));
            }

        }

        public Control buttonload(string flag)
        {
            DirectButton temp_ctrlbutton = new DirectButton();
            ArrayList result = new ArrayList();
            for (int i = 0; i < Policylist.RowCount; i++)
            {
                result.Add(Policylist["Status", i].Value.ToString().Trim());
            }
            if (result.Contains("Hold") || result.Contains("Pending"))
            { fconfirmCheck = "NotYet"; }
            else { fconfirmCheck = ""; }
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.fconfirmCheck = fconfirmCheck;
            temp_ctrlbutton.sourceListRowCount = Policylist.Rows.Count;
            if (flag == "Load") { temp_ctrlbutton.buttonload("Endor", fctldel); }
            if (flag == "Add") { temp_ctrlbutton.buttoncontroladd(); }
            if (flag == "Back") { temp_ctrlbutton.buttoncontrolback(); }
            if (flag == "Mod") { temp_ctrlbutton.buttoncontrolupdate(); }
            if (flag == "Save") { temp_ctrlbutton.buttoncontrolsaveback(); }
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            temp_ctrlbutton.UserControlButtonPrintClicked += new EventHandler(printattch_Click);
            temp_ctrlbutton.UserControlButtonPushClicked += new EventHandler(pushattch_Click);
            temp_ctrlbutton.UserControlButtonNotedClicked += new EventHandler(noteattch_Click);

            return temp_ctrlbutton;
        }

        private void Policylist_CellFormatting(object sender, System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            if (Policylist.Columns[e.ColumnIndex].Name.Equals("Policy#"))
            {
                Policylist.Columns[e.ColumnIndex].Width = 150;
            }
            if (Policylist.Columns[e.ColumnIndex].Name.Equals("Status"))
            {
                Policylist.Columns[e.ColumnIndex].Width = 60;
            }
            if (Policylist.Columns[e.ColumnIndex].Name.Equals("Endt No#"))
            {
                Policylist.Columns[e.ColumnIndex].Width = 90;
            }
            if (Policylist.Columns[e.ColumnIndex].Name.Equals("V#") || Policylist.Columns[e.ColumnIndex].Name.Equals("VO"))
            {
                Policylist.Columns[e.ColumnIndex].Width = 30;
            }
            if (Policylist.Columns[e.ColumnIndex].Name.Equals("Insured"))
            {
                Policylist.Columns[e.ColumnIndex].Width = 190;
            }
        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            if (Addflag == true || updateflag == true)
            {
                if (e.TabPage == tabPage1)
                { e.Cancel = true; }
                if (e.TabPage != tabPage1)
                { e.Cancel = false; }
            }
            if (Addflag != true && updateflag != true)
            {
                if (Policylist.Rows.Count == 0)
                {
                    if (e.TabPage != tabPage1)
                        e.Cancel = true;
                }
            }
        }

        private void tabControl1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            if (Addflag == true || updateflag == true)
            {
                if (tabControl1.SelectedIndex == 1)
                {
                    InsdInst.u_calculation();
                    InsdInst.EndtType = Gen.Genfextend;
                    if (Class == "EEC")
                    {
                        InsdInst.fsclass = SubClass;
                    }
                    //if (Gen.Genfextend.ToString() == "2" && )
                    //{ }
                    //else
                    //{
                    Gen.mmain = InsdInst.mmain;
                    Gen.getMmain();
                    //}
                }
                if (tabControl1.SelectedIndex == 2)
                {
                    if ((Gen.GenEffrDate == "    .  .") || (Gen.GenEftoDate == "    .  ."))
                    {
                        tabControl1.SelectedIndex = 1;
                        MessageBox.Show("Invalid Value!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        Gen.setControlFocus("fefffr");
                    }
                    else
                    {
                        if (Class == "CAR" || Class == "CSB")
                        {
                            if (Class == "CSB")
                            {
                                InsdInst.InsureSum2 = Gen.GenSum;
                                InsdInst.InsureSumUpd2 = Gen.GenSumUpd;
                            }
                        }
                        if (Class == "EEC")
                        {
                            if (Gen.GenSubClass == "CONTRACTOR")
                            {
                                InsdInst.InsureSum2 = Gen.GenSum;
                                InsdInst.InsureSumUpd = Gen.GenSumUpd;
                            }
                            if (InsdInst.fsclass != Gen.GenSubClass)
                            {
                                panel4.Controls.Clear();
                                InsdInst.fctlid = fctlid;
                                InsdInst.Class = Class;
                                InsdInst.Classfctlid = Classfctlid;
                                InsdInst.fsclass = Gen.GenSubClass;
                                InsdInst.buttoncontroladd(fendtype);
                                panel4.Controls.Add(InsdInst);
                            } 
                        }
                        InsdInst.u_calculation();
                    }
                }
                if (tabControl1.SelectedIndex == 5)
                {
                    InsdInst.u_calculation();
                    Install.mmain = InsdInst.mmain;
                    if (Class == "EEC" || Class == "PMP" || Class == "CMP")
                    {
                        if (Gen.GenSubClass == "CONTRACTOR")
                        {
                            InsdInst.InsureSum2 = Gen.GenSum;
                        }
                        Install.InstallEC1amt = Gen.GenEC1amt;
                        Install.InstallEC2amt = Gen.GenEC2amt;
                        Install.InstallEC3amt = Gen.GenEC3amt;
                    }
                    if (Class == "PMP" || Class == "CMP")
                    {
                        Gen.mmain = InsdInst.mmain;
                        Gen.getMmain();
                    }
                    if (Class == "EEC")
                    {
                        if (Gen.GenSubClass == "CONTRACTOR")
                        {
                            InsdInst.InsureSum2 = Gen.GenSum;
                            Gen.mmain = InsdInst.mmain;
                            Gen.getMmain();
                        }
                    }
                    //if (Gen.Genfextend.ToString() == "2" &&)
                    //{ }
                    //else
                    //{
                    Gen.mmain = InsdInst.mmain;
                    Gen.getMmain();
                    //}
                    Gen.installTotal = Install.installTotal;
                    Install.InstallMmain();
                    Install.tabpage6addData();
                    Install.Installalevyamt = Gen.Genflvyamta;
                    Install.InstallEffrDate = Gen.GenEffrDate;
                    Install.InstallEftoDate = Gen.GenEftoDate;
                    Install.InstallIsDate = Gen.GenIsDate;
                }
            }
        }

        void tabpage1reload()
        {
            panel10.Controls.Clear();
            panel10.Controls.Add(buttonload("Load"));
            if (Addflag != true && updateflag != true)
            {
                if (Policylist.Rows.Count == 0)
                {
                    tabControl1.SelectedTab = tabPage1;
                }
            }
            tabPage2reload(fctlid);
            tabPage3reload(fctlid, com, disc);
            tabPage4reload(fctlid);
            tabPage5reload(fctlid);
            tabPage6reload(fctlid);
            tabPage7reload(fctlid);
        }

        void tabPage2reload(string fctlid)
        {
            panel22.Controls.Clear();
            panel22.Controls.Add(buttonload("Load"));

            panel1.Controls.Clear();
            Gen.fctlid = fctlid;
            Gen.Class = Class;
            Gen.Classfctlid = Classfctlid;
            Gen.ftype = "E";
            Gen.fbus = fbus;
            Gen.buttoncontrolback();
            panel1.Controls.Add(Gen);
        }

        void tabPage3reload(string fctlid, string com, string disc)
        {
            panel24.Controls.Clear();
            panel24.Controls.Add(buttonload("Load"));

            panel4.Controls.Clear();
            InsdInst.fctlid = fctlid;
            InsdInst.Class = Class;
            InsdInst.Classfctlid = Classfctlid;
            InsdInst.ftype = "E";
            InsdInst.fbus = fbus;
            InsdInst.EndtType = Gen.Genfextend;
            if (Class == "EEC") { InsdInst.fsclass = Gen.GenSubClass; }
            InsdInst.buttoncontrolback();
            panel4.Controls.Add(InsdInst);
        }

        void tabPage4reload(string fctlid)
        {
            panel13.Controls.Clear();
            panel13.Controls.Add(buttonload("Load"));

            panel11.Controls.Clear();
            Excess.fctlid = fctlid;
            if (Class == "CAR" || Class == "PMP" || Class == "CMP")
            {
                Excess.panel12.Visible = true;
            }
            Excess.Class = Class;
            Excess.Classfctlid = Classfctlid;
            Excess.ftype = "E";
            Excess.fbus = fbus;
            Excess.buttoncontrolback();
            panel11.Controls.Add(Excess);
        }

        void tabPage5reload(string fctlid)
        {
            panel26.Controls.Clear();
            panel26.Controls.Add(buttonload("Load"));

            panel12.Controls.Clear();
            Attach.fctlid = fctlid;
            Attach.Class = Class;
            Attach.Classfctlid = Classfctlid;
            Attach.ftype = "E";
            Attach.fbus = fbus;
            Attach.buttoncontrolback();
            panel12.Controls.Add(Attach);
        }

        void tabPage6reload(string fctlid)
        {
            panel27.Controls.Clear();
            panel27.Controls.Add(buttonload("Load"));

            panel2.Controls.Clear();
            Install.fctlid = fctlid;
            Install.Class = Class;
            Install.Classfctlid = Classfctlid;
            Install.ftype = "E";
            Install.fbus = fbus;
            Install.InstallTotComm = Gen.GenTotComm;
            Install.InstallTotDisc = Gen.GenTotDisc;
            Install.InstallGP = Gen.GenGP;
            Install.InstallNP = Gen.GenNP;
            Install.InstallEffrDate = Gen.GenEffrDate;
            Install.InstallEftoDate = Gen.GenEftoDate;
            Install.InstallIsDate = Gen.GenIsDate;
            Install.Installalevyamt = Gen.Genflvyamta;
            if (Class == "EEC" || Class == "PMP" || Class == "CMP")
            {
                Install.InstallEC1amt = Gen.GenEC1amt;
                Install.InstallEC2amt = Gen.GenEC2amt;
                Install.InstallEC3amt = Gen.GenEC3amt;
            }
            Gen.installTotal = Install.installTotal;
            Install.buttoncontrolback();
            panel2.Controls.Add(Install);

        }

        void tabPage7reload(string fctlid)
        {
            panel6.Controls.Clear();
            panel6.Controls.Add(buttonload("Load"));

            panel3.Controls.Clear();
            Print.fctlid = fctlid;
            Print.Class = Class;
            Print.buttoncontrolback();
            panel3.Controls.Add(Print);

        }

        void policylistchg()
        {
            if (Policylist.CurrentCell != null)
            {
                Policylistrow = Policylist.CurrentCell.RowIndex;
            }
            else { return; }

            if (Policylist.Rows[Policylistrow].Cells["Policy#"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["Policy#"].Value.ToString().Length != 0)
                {
                    policyNo = Policylist.Rows[Policylistrow].Cells["Policy#"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["Endt No#"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["Endt No#"].Value.ToString().Length != 0)
                {
                    EndtNo = Policylist.Rows[Policylistrow].Cells["Endt No#"].Value.ToString();
                }
            }

            if (Policylist.Rows[Policylistrow].Cells["fctlid"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["fctlid"].Value.ToString().Length != 0)
                {
                    fctlid = Policylist.Rows[Policylistrow].Cells["fctlid"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["PushToA8"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["PushToA8"].Value.ToString().Length != 0)
                {
                    fctldel = Policylist.Rows[Policylistrow].Cells["PushToA8"].Value.ToString().Trim();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["Status"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["Status"].Value.ToString().Length != 0)
                {
                    fconfirm = Policylist.Rows[Policylistrow].Cells["Status"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["fcom"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["fcom"].Value.ToString().Length != 0)
                {
                    com = Policylist.Rows[Policylistrow].Cells["fcom"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["ftd"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["ftd"].Value.ToString().Length != 0)
                {
                    disc = Policylist.Rows[Policylistrow].Cells["ftd"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["fsclass"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["fsclass"].Value.ToString().Length != 0)
                {
                    SubClass = Policylist.Rows[Policylistrow].Cells["fsclass"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["fmntlabel"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["fmntlabel"].Value.ToString().Length != 0)
                {
                    fmntlabel = Policylist.Rows[Policylistrow].Cells["fmntlabel"].Value.ToString();
                }
            }
            tabpage1reload();
        }

        private void Policylist_SelectionChanged(object sender, EventArgs e)
        {
            fctldel = "";
            policylistchg();
        }

        void print() { }

        void del()
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the Endorsement All information?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string deldatestr =
                    "delete from polh where fctlid='" + fctlid + "'";
                string deldatestr1 = "";
                if (Class == "CAR" || Class == "CSB" || Class == "MYP" || Class == "PAR")
                {
                    deldatestr1 = "delete from oinsint where fctlid_1='" + fctlid + "'";
                }
                if (Class == "EEC")
                {
                    deldatestr1 = "delete from oinsint_b where fctlid_1='" + fctlid + "'";
                }
                if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII" || Class == "GLF")
                {
                    deldatestr1 = "delete from oinsint_c where fctlid_1='" + fctlid + "'";
                }
                if (Class == "CPM")
                {
                    deldatestr1 = "delete from oinsint_d where fctlid_1='" + fctlid + "'";
                }
                if (Class == "PMP" || Class == "CMP")
                {
                    deldatestr1 = "delete from oinsint_e where fctlid_1='" + fctlid + "'";
                }
                string deldatestr2 =
                    "delete from oinsex where fctlid_1='" + fctlid + "'";
                string deldatestr3 =
                    "delete from oinsatt where fctlid_1='" + fctlid + "'";
                string deldatestr4 =
                     "delete from oinvdet where fctlid_1='" + fctlid + "'";
                string deldatestr5 = "", deldatestr6 = "";
                if (Class == "CGL" || Class == "FGP" || Class == "GLF" || Class == "MYP" || Class == "PAR")
                {
                    deldatestr5 = "delete from oinsloc where fctlid_1 ='" + fctlid + "'";
                }
                if (Class == "FGP")
                {
                    deldatestr6 = "delete from oinsper where fctlid_1 ='" + fctlid + "'";
                }
                string deldatestr7 = "delete from oremark where fctlid_1='" + fctlid + "'";
                string deldatestr8 = "delete from orih where fctlid_1 ='" + fctlid + "'";
                string deldatestr9 = "";
                if (Class == "CAR")
                {
                    deldatestr9 = "delete from orihc where fctlid_1 ='" + fctlid + "'";
                }
                string deldatestr10 = "";
                if (Class == "PAR" || Class == "CPM")
                {
                    deldatestr10 = "delete from oril where fctlid_e ='" + fctlid + "'";
                }
                string deldatestr11 = "";
                if (Class == "PAR" || Class == "CPM")
                {
                    deldatestr11 = "delete from orild1 where fctlid_e ='" + fctlid + "'";
                }
                else { deldatestr11 = "delete from orid1 where fctlid_1 ='" + fctlid + "'"; }
                string deldatestr12 = "";
                if (Class == "PAR" || Class == "CPM")
                {
                    deldatestr12 = "delete from orild2 where fctlid_e ='" + fctlid + "'";
                }
                else { deldatestr12 = "delete from orid2 where fctlid_1 ='" + fctlid + "'"; }
                string deldatestr13 = "delete from orilayr where fctlid_1 ='" + fctlid + "'";
                string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();

                    SqlCommand command = connection.CreateCommand();
                    SqlTransaction transaction;

                    // Start a local transaction.
                    transaction = connection.BeginTransaction("SampleTransaction");

                    // Must assign both transaction object and connection 
                    // to Command object for a pending local transaction
                    command.Connection = connection;
                    command.Transaction = transaction;

                    try
                    {
                        command.CommandText = deldatestr;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr1;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr2;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr3;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr4;
                        command.ExecuteNonQuery();
                        if (deldatestr5 != "")
                        {
                            command.CommandText = deldatestr5;
                            command.ExecuteNonQuery();
                        }
                        if (deldatestr6 != "")
                        {
                            command.CommandText = deldatestr6;
                            command.ExecuteNonQuery();
                        }
                        command.CommandText = deldatestr7;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr8;
                        command.ExecuteNonQuery();
                        if (deldatestr9 != "")
                        {
                            command.CommandText = deldatestr9;
                            command.ExecuteNonQuery();
                        }
                        if (deldatestr10 != "")
                        {
                            command.CommandText = deldatestr10;
                            command.ExecuteNonQuery();
                        }
                        if (deldatestr11 != "")
                        {
                            command.CommandText = deldatestr11;
                            command.ExecuteNonQuery();
                        }
                        if (deldatestr12 != "")
                        {
                            command.CommandText = deldatestr12;
                            command.ExecuteNonQuery();
                        }
                        if (deldatestr13 != "")
                        {
                            command.CommandText = deldatestr13;
                            command.ExecuteNonQuery();
                        }
                        transaction.Commit();
                        MessageBox.Show("Have Been Deleted", "Warning",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        FillData("");
                        tabpage1reload();
                        tabControl1.SelectedTab = tabPage1;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("Haven't Been Deleted", "Warning",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);

                        Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                        Console.WriteLine("  Message: {0}", ex.Message);

                        // Attempt to roll back the transaction. 
                        try
                        {
                            transaction.Rollback();
                        }
                        catch (Exception ex2)
                        {
                            // This catch block will handle any errors that may have occurred 
                            // on the server that would cause the rollback to fail, such as 
                            // a closed connection.
                            Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                            Console.WriteLine("  Message: {0}", ex2.Message);
                        }
                    }
                }
            }
        }


        public void buttoncontrolback()
        {
            Addflag = false;
            updateflag = false;
        }

        public void buttoncontroladd()
        {
            addendt tempform = new addendt(this);
            tempform.Class = Class;
            tempform.flag = "Endor";
            tempform.SubClass = SubClass;
            tempform.fbus = fbus;
            tempform.Policy = policyNo;
            tempform.Control();
            tempform.ShowDialog();
            if ((fendtype != "" && fexten != "") && (fendtype != "2"))
            {
                Addflag = true;
                DirectButton temp_ctrlbutton = new DirectButton();
                temp_ctrlbutton.buttoncontroladd();
                tabControl1.SelectedTab = tabPage2;
                tabpage2add(fendtype, fexten);
                tabpage3add(fendtype, fexten);
                tabpage4add();
                tabpage5add();
                tabpage6add();
                tabpage7add();
            }
            if (fendtype == "2")
            {
                selvo tempformvo = new selvo(this);
                tempformvo.flag = "Endor";
                tempformvo.Class = Class;
                tempformvo.SubClass = SubClass;
                tempformvo.fbus = fbus;
                tempformvo.Policy = policyNo.Trim();
                tempformvo.FillData();
                tempformvo.ShowDialog();
                if ((fendtype != "" && fexten != ""))
                {
                    Addflag = true;
                    DirectButton temp_ctrlbutton = new DirectButton();
                    temp_ctrlbutton.buttoncontroladd();
                    tabControl1.SelectedTab = tabPage2;
                    tabpage2add(fendtype, fexten);
                    tabpage3add(fendtype, fexten);
                    tabpage4add();
                    tabpage5add();
                    tabpage6add();
                    tabpage7add();
                }
            }
            Gen.setControlFocus(fbus == "D" ? "fuwyr" : "fcoins");
        }

        public void buttoncontrolsaveback()
        {
            buttoncontrolback();
        }

        public void buttoncontrolupdate()
        {
            updateflag = true;
            tabControl1.SelectedTab = tabPage2;
            tabpage2update();
            tabpage3update();
            tabpage4update();
            tabpage5update();
            tabpage6update();
            tabpage7update();

            if (tabControl1.SelectedTab == tabPage2)
                Gen.setControlFocus(fbus == "D" ? "fuwyr" : "fcoins");

            if (tabControl1.SelectedTab == tabPage3)
                InsdInst.setControlFocus(InsdFocus);

            if (tabControl1.SelectedTab == tabPage4)
                Excess.setControlFocus("fid1");

            if (tabControl1.SelectedTab == tabPage5)
                Attach.setControlFocus("fid");

            if (tabControl1.SelectedTab == tabPage6)
                Install.setControlFocus("installno");

        }

        void tabpage2add(string fendtype, string fexten)
        {
            panel22.Controls.Clear();
            panel22.Controls.Add(buttonload("Add"));

            panel1.Controls.Clear();
            if (fendtype == "2") { Gen.fctlid = fctlid_v; } else { Gen.fctlid = fctlid; }
            Gen.Class = Class;
            Gen.Classfctlid = Classfctlid;
            Gen.ftype = "E";
            Gen.GenSubClass = SubClass;
            Gen.fbus = fbus;
            Gen.buttoncontroladd(fendtype, fexten);
            panel1.Controls.Add(Gen);
        }

        void tabpage3add(string fendtype, string fexten)
        {
            panel24.Controls.Clear();
            panel24.Controls.Add(buttonload("Add"));

            panel4.Controls.Clear();
            if (fendtype == "2") { InsdInst.fctlid = fctlid_v; }
            else { InsdInst.fctlid = fctlid; }
            InsdInst.Class = Class;
            InsdInst.Classfctlid = Classfctlid;
            if (Class == "EEC")
            {
                InsdInst.fsclass = Gen.GenSubClass;
                InsdInst.InsureSumUpd = Gen.GenSumUpd;
            }
            InsdInst.ftype = "E";
            InsdInst.EndtType = Gen.Genfextend;
            InsdInst.fbus = fbus;
            InsdInst.buttoncontroladd(fendtype);
            panel4.Controls.Add(InsdInst);
        }

        void tabpage4add()
        {
            panel13.Controls.Clear();
            panel13.Controls.Add(buttonload("Add"));

            panel11.Controls.Clear();
            Excess.fctlid = fctlid;
            if (Class == "CAR" || Class == "PMP" || Class == "CMP")
            {
                Excess.panel12.Visible = true;
            }
            Excess.Class = Class;
            Excess.Classfctlid = Classfctlid;
            Excess.ftype = "E";
            Excess.fbus = fbus;
            Excess.buttoncontroladd();
            panel11.Controls.Add(Excess);
        }

        void tabpage5add()
        {
            panel26.Controls.Clear();
            panel26.Controls.Add(buttonload("Add"));

            panel12.Controls.Clear();
            Attach.fctlid = fctlid;
            Attach.Class = Class;
            Attach.Classfctlid = Classfctlid;
            Attach.ftype = "E";
            Attach.fbus = fbus;
            Attach.buttoncontroladd();
            panel12.Controls.Add(Attach);
        }

        void tabpage6add()
        {
            panel27.Controls.Clear();
            panel27.Controls.Add(buttonload("Add"));

            panel2.Controls.Clear();
            Install.fctlid = fctlid;
            Install.Class = Class;
            Install.Classfctlid = Classfctlid;
            Gen.installTotal = Install.installTotal;
            Install.ftype = "E";
            Install.fbus = fbus;
            Install.buttoncontroladd();
            panel2.Controls.Add(Install);
        }

        void tabpage7add()
        {
            panel6.Controls.Clear();
            panel6.Controls.Add(buttonload("Add"));

            panel3.Controls.Clear();
            Print.fctlid = fctlid;
            Print.Class = Class;
            Print.buttoncontroladd();
            panel3.Controls.Add(Print);

        }

        void tabpage2update()
        {
            panel22.Controls.Clear();
            panel22.Controls.Add(buttonload("Mod"));

            panel1.Controls.Clear();
            Gen.fctlid = fctlid;
            Gen.Class = Class;
            Gen.Classfctlid = Classfctlid;
            Gen.ftype = "E";
            Gen.fbus = fbus;
            Gen.buttoncontrolupdate();
            panel1.Controls.Add(Gen);
        }

        void tabpage3update()
        {
            panel24.Controls.Clear();
            panel24.Controls.Add(buttonload("Mod"));

            panel4.Controls.Clear();
            InsdInst.fctlid = fctlid;
            InsdInst.Class = Class;
            InsdInst.Classfctlid = Classfctlid;
            if (Class == "EEC")
            {
                InsdInst.fsclass = Gen.GenSubClass;
                InsdInst.InsureSumUpd = Gen.GenSumUpd;
            }
            InsdInst.ftype = "E";
            InsdInst.EndtType = Gen.Genfextend;
            InsdInst.fbus = fbus;
            InsdInst.buttoncontrolupdate(fendtype);
            panel4.Controls.Add(InsdInst);
        }

        void tabpage4update()
        {
            panel13.Controls.Clear();
            panel13.Controls.Add(buttonload("Mod"));

            panel11.Controls.Clear();
            Excess.fctlid = fctlid;
            if (Class == "CAR" || Class == "PMP" || Class == "CMP")
            {
                Excess.panel12.Visible = true;
            }
            Excess.Class = Class;
            Excess.Classfctlid = Classfctlid;
            Excess.ftype = "E";
            Excess.fbus = fbus;
            Excess.buttoncontrolupdate();
            panel11.Controls.Add(Excess);
        }

        void tabpage5update()
        {
            panel26.Controls.Clear();
            panel26.Controls.Add(buttonload("Mod"));

            panel12.Controls.Clear();
            Attach.fctlid = fctlid;
            Attach.Class = Class;
            Attach.Classfctlid = Classfctlid;
            Attach.ftype = "E";
            Attach.fbus = fbus;
            Attach.buttoncontrolupdate();
            panel12.Controls.Add(Attach);
        }

        void tabpage6update()
        {
            panel27.Controls.Clear();
            panel27.Controls.Add(buttonload("Mod"));

            panel2.Controls.Clear();
            Install.fctlid = fctlid;
            Install.Class = Class;
            Install.Classfctlid = Classfctlid;
            Gen.installTotal = Install.installTotal;
            Install.ftype = "E";
            Install.fbus = fbus;
            Install.buttoncontrolupdate();
            panel2.Controls.Add(Install);

        }

        void tabpage7update()
        {
            panel6.Controls.Clear();
            panel6.Controls.Add(buttonload("Mod"));

            panel3.Controls.Clear();
            Print.fctlid = fctlid;
            Print.Class = Class;
            Print.buttoncontrolupdate();
            panel3.Controls.Add(Print);

        }

        private void modifyattch_Click(object sender, EventArgs e)
        {
            buttoncontrolupdate();
        }

        private void addattch_Click(object sender, EventArgs e)
        {
            buttoncontroladd();
        }

        private void cancelattch_Click(object sender, EventArgs e)
        {
            buttoncontrolback();
            tabpage1reload();
        }

        private void exitattch_Click(object sender, EventArgs e)
        {
            exit();
        }

        private void delattch_Click(object sender, EventArgs e)
        {
            del();
        }

        private void printattch_Click(object sender, EventArgs e)
        {
            pendt tempform = new pendt(this);
            tempform.rpOptid = Class;
            tempform.fctlid = fctlid;
            tempform.fmntlabel = fmntlabel;
            //tempform.fctlids = fctlid;
            tempform.dt = Policylist.DataSource as DataTable;
            tempform.vfbus = fbus;
            tempform.ctrlbtn();
            tempform.ShowDialog();
        }
        public string PostFileToServer(string url, byte[] filedata, byte[] extraFileData,Encoding encoder)
        {
            HttpClient client = new HttpClient();
            var json = Newtonsoft.Json.JsonConvert.SerializeObject(new { Fctlid = fctlid, Polno = EndtNo, UserId = InsEnvironment.LoginUser.GetUserADCode(), FileContent = filedata, ExtraFileContent = extraFileData });
            HttpContent httpContent = new StringContent(json, Encoding.UTF8, "application/json");
            HttpResponseMessage response = client.PostAsync(url, httpContent).Result;
            var result = response.Content.ReadAsStringAsync().Result;
            var s = Newtonsoft.Json.JsonConvert.DeserializeObject(result);
            JavaScriptSerializer js = new JavaScriptSerializer();//实例化一个能够序列化数据的类
            JsonOutput oput = js.Deserialize<JsonOutput>(s.ToString()); //将json数据转化为对象类型并赋值给list
            return oput.Message;
        }
        public string Scan()
        {
            HttpClient client = new HttpClient();
            HttpResponseMessage response = client.GetAsync("https://pur.csci.com.hk/push/api/push?UserId=anna_miao").Result;
            var result = response.Content.ReadAsStringAsync().Result;
            var s = Newtonsoft.Json.JsonConvert.DeserializeObject(result);
            JavaScriptSerializer js = new JavaScriptSerializer();//实例化一个能够序列化数据的类
            JsonOutput oput = js.Deserialize<JsonOutput>(s.ToString()); //将json数据转化为对象类型并赋值给list
            return oput.Result.ToString();
        }
        private void pushattch_Click(object sender, EventArgs e)
        {
            pushA8 ToolsPush = new pushA8();
            if (EndtNo.Trim() != "")
            {
                ToolsPush.wl_fpolno = EndtNo;
                ToolsPush.pendtExport(CrystalDecisions.Shared.ExportFormatType.WordForWindows, "test.doc", Class, fctlid, fmntlabel, "ToA8");
                if (File.Exists(@"M:\\PushToA8\\" + EndtNo.Trim() + ".pdf"))
                {
                    try
                    {
                        progressBar1.Visible = true;
                        progressBar1.Maximum = 50;//设置最大长度值 
                        progressBar1.Value = 0;//设置当前值 
                        progressBar1.Step = 5;//设置没次增长多少 
                        for (int i = 0; i < 10; i++)//循环 
                        {
                            System.Threading.Thread.Sleep(1000);//暂停1秒 
                            progressBar1.Value += progressBar1.Step;
                        }
                        byte[] fileBytes = ReadfileToByte.ReadFileToByte("M:\\PushToA8\\" + EndtNo.Trim() + ".pdf");
                        byte[] extraFileBytes = ReadfileToByte.ReadFileToByte("M:\\PushToA8\\" + EndtNo.Trim() + "_RI" + ".pdf");
                        String res = PostFileToServer("https://pur.csci.com.hk/push/api/push", fileBytes, extraFileBytes, Encoding.UTF8);
                        progressBar1.Value = 0;
                        MessageBox.Show(res.ToString(), "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        string oldfctlid = fctlid;
                        FillData("");
                        DataTable dt3 = Policylist.DataSource as DataTable;
                        foreach (DataRow row in dt3.Rows)
                        {
                            int SelectedIndex = dt3.Rows.IndexOf(row);
                            if (oldfctlid == row["fctlid"].ToString().Trim())
                            {
                                Policylist.CurrentCell = Policylist.Rows[SelectedIndex].Cells["Policy#"];
                                policylistchg();
                                break;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show(ex.ToString(), "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                else
                {
                    MessageBox.Show("保单生成错误！", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            else
            {
                MessageBox.Show("保单状态不能推送！", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void noteattch_Click(object sender, EventArgs e)
        {
            if (fctldel.Contains("審批完成"))
            {
                if (File.Exists(@"M:\\SendToCustomer\\" + EndtNo.Trim() + ".pdf"))
                {
                    string result = SendEmail.sendtoCus(EndtNo.Trim(), "");
                    if (result == "發送成功")
                    {
                        MessageBox.Show("已發送郵件！", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        string sql = "update polh set fctldel =  concat(isnull(rtrim(fctldel),''),' Send') where fctlid = '" + fctlid + "'";
                        DBHelper.ExecuteCommand(sql);
                        string oldfctlid = fctlid;
                        FillData("");
                        DataTable dt3 = Policylist.DataSource as DataTable;
                        foreach (DataRow row in dt3.Rows)
                        {
                            int SelectedIndex = dt3.Rows.IndexOf(row);
                            if (oldfctlid == row["fctlid"].ToString().Trim())
                            {
                                Policylist.CurrentCell = Policylist.Rows[SelectedIndex].Cells["Policy#"];
                                policylistchg();
                                break;
                            }
                        }
                    }
                    else
                    {
                        MessageBox.Show("發送郵件失败！", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                else
                {
                    MessageBox.Show("保單沒準備好！", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        void exit()
        {
            this.Close();
        }

        private void confirmattch_Click(object sender, EventArgs e)
        {
            string content = "";
            string sqlselect = "select * from oremark where fctlid_1 ='" + fctlid + "' ";
            DataTable dt = DBHelper.GetDataSet(sqlselect);
            if (dt.Rows.Count > 0) {
              content =  dt.Rows[0]["fcontent"].ToString().Trim();
            }
            if (content != "")
            {
                confpol tempform = new confpol(this);
                tempform.fctlid = fctlid;
                tempform.tranStatus = fconfirm;
                tempform.autori = Gen.autori;
                tempform.Control();
                tempform.ShowDialog();

                Int32 nResult = tempform.optionNo;

                if (nResult == 0)
                {
                    tempform.Dispose();
                    return;
                }

                if (nResult == 2)
                {
                    Gen.Skip_Validate = false;
                    InsdInst.Skip_Validate = false;
                    Excess.Skip_Validate = false;

                    if (!Validation())
                    {
                        tempform.Dispose();
                        buttoncontrolupdate();
                        return;
                    }
                }

                tempform.confirmPolicy(nResult);

                if (ConfirmRlt != "No")
                {
                    FillData("");
                    tabControl1.SelectedTab = tabPage2;
                    DataTable dt3 = Policylist.DataSource as DataTable;
                    foreach (DataRow row in dt3.Rows)
                    {
                        int SelectedIndex = dt3.Rows.IndexOf(row);
                        if (ConfirmRlt == row["fctlid"].ToString().Trim())
                        {
                            Policylist.CurrentCell = Policylist.Rows[SelectedIndex].Cells["Policy#"];
                            policylistchg();
                            break;
                        }
                    }
                    tabpage1reload();
                }

                tempform.Dispose();
                tabpage1reload();
                return;
            }
            else {
                MessageBox.Show("Content is Empty!");
            }
            
        }

        private void tabPage6_MouseClick(object sender, MouseEventArgs e)
        {
            this.tabPage6.Focus();
        }

        private void tabPage6_MouseWheel(object sender, MouseEventArgs e)
        {
            tabPage6.VerticalScroll.Value += 10;
            tabPage6.Refresh();
            tabPage6.Invalidate();
            tabPage6.Update();
        }

        private void saveattch_Click(object sender, EventArgs e)
        {
            string result = save();

            if (Addflag == true)
            {
                if (result == "OK")
                {
                    MessageBox.Show("Have Been Inserted!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            if (updateflag == true)
            {
                if (result == "OK")
                {
                    MessageBox.Show("Have Been Updated!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }

        }

        void tabpage1Saveload(string newfctlid, string newcom, string newdisc)
        {
            panel10.Controls.Clear();
            panel10.Controls.Add(buttonload("Load"));

            tabPage2reload(newfctlid);
            tabPage3reload(newfctlid, newcom, newdisc);
            tabPage4reload(newfctlid);
            tabPage5reload(newfctlid);
            tabPage6reload(newfctlid);
        }

        public string rollback(string fctlid)
        {
            ArrayList delstring = new ArrayList();
            string deldatestr = "delete from polh where fctlid='" + fctlid + "'";
            delstring.Add(deldatestr);
            string deldatestr1 = "";
            if (Class == "CAR" || Class == "CSB" || Class == "MYP" || Class == "PAR")
            {
                deldatestr1 = "delete from oinsint where fctlid_1='" + fctlid + "'";
            }
            if (Class == "EEC")
            {
                deldatestr1 = "delete from oinsint_b where fctlid_1='" + fctlid + "'";
            }
            if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII" || Class == "GLF")
            {
                deldatestr1 = "delete from oinsint_c where fctlid_1='" + fctlid + "'";
            }
            if (Class == "CPM")
            {
                deldatestr1 = "delete from oinsint_d where fctlid_1='" + fctlid + "'";
            }
            if (Class == "PMP" || Class == "CMP")
            {
                deldatestr1 = "delete from oinsint_e where fctlid_1='" + fctlid + "'";
            }
            delstring.Add(deldatestr1);
            string deldatestr2 =
                "delete from oinsex where fctlid_1='" + fctlid + "'";
            delstring.Add(deldatestr2);
            string deldatestr3 =
                "delete from oinsatt where fctlid_1='" + fctlid + "'";
            delstring.Add(deldatestr3);
            string deldatestr4 =
                 "delete from oinvdet where fctlid_1='" + fctlid + "'";
            delstring.Add(deldatestr4);
            string deldatestr5 = "", deldatestr6 = "";
            if (Class == "CGL" || Class == "FGP" || Class == "GLF" || Class == "MYP" || Class == "PAR")
            {
                deldatestr5 = "delete from oinsloc where fctlid_1 ='" + fctlid + "'";
                delstring.Add(deldatestr5);
            }
            if (Class == "FGP")
            {
                deldatestr6 = "delete from oinsper where fctlid_1 ='" + fctlid + "'";
                delstring.Add(deldatestr6);
            }
            string deldatestr7 = "delete from oremark where fctlid_1='" + fctlid + "'";
            delstring.Add(deldatestr7);
            string deldatestr8 = "delete from orih where fctlid_1 ='" + fctlid + "'";
            delstring.Add(deldatestr8);
            string deldatestr9 = "";
            if (Class == "CAR")
            {
                deldatestr9 = "delete from orihc where fctlid_1 ='" + fctlid + "'";
            }
            delstring.Add(deldatestr9);
            string deldatestr10 = "";
            if (Class == "PAR" || Class == "CPM")
            {
                deldatestr10 = "delete from oril where fctlid_e ='" + fctlid + "'";
            }
            delstring.Add(deldatestr10);
            string deldatestr11 = "";
            if (Class == "PAR" || Class == "CPM")
            {
                deldatestr11 = "delete from orild1 where fctlid_e ='" + fctlid + "'";
            }
            else { deldatestr11 = "delete from orid1 where fctlid_1 ='" + fctlid + "'"; }
            delstring.Add(deldatestr11);
            string deldatestr12 = "";
            if (Class == "PAR" || Class == "CPM")
            {
                deldatestr12 = "delete from orild2 where fctlid_e ='" + fctlid + "'";
            }
            else { deldatestr12 = "delete from orid2 where fctlid_1 ='" + fctlid + "'"; }
            delstring.Add(deldatestr12);
            string deldatestr13 = "delete from orilayr where fctlid_1 ='" + fctlid + "'";
            delstring.Add(deldatestr13);
            string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
            string[] sql1 = (string[])delstring.ToArray(typeof(string));
            string result = ExecuteSqlTransaction(connectionString, sql1);
            if (result == "ok")
            {
                result = "RollBock";
            }
            else
            {
                result = "RollBock Error!";
            }
            return result;
        }

        private string ExecuteSqlTransaction(string connectionString, string[] sql1)
        {
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                SqlCommand command = connection.CreateCommand();
                SqlTransaction transaction;
                transaction = connection.BeginTransaction("SampleTransaction");

                command.Connection = connection;
                command.Transaction = transaction;

                try
                {
                    if (sql1 != null)
                    {
                        for (int i = 0; i < sql1.Length; i++)
                        {
                            if (sql1[i] != "" && sql1[i] != null)
                            {
                                command.CommandText = sql1[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    transaction.Commit();
                    return "OK";
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                    Console.WriteLine("  Message: {0}", ex.Message);

                    try
                    {
                        transaction.Rollback();
                        return "RollBack";
                    }
                    catch (Exception ex2)
                    {
                        Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                        Console.WriteLine("  Message: {0}", ex2.Message);
                        return ex2.Message;
                    }
                }
            }
        }

        public string save()
        {
            string result = "";
            tabControl1.SelectedTab = tabPage6;
            tabControl1.SelectedTab = tabPage2;
            Gen.mmain = InsdInst.mmain;
            Gen.getMmain();
            if (!Validation())
            {
                DialogResult myResult = MessageBox.Show("Continue to Save?", "Save Warning", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
                if (myResult == DialogResult.OK)
                {
                    result = SaveCode();
                }
            }
            else { result = SaveCode(); }
            return result;
        }

        public Boolean Validation()
        {
            string ctrlName = "";
            ctrlName = Gen.tabpage2cofirm();
            if (ctrlName != "")
            {
                tabControl1.SelectedIndex = 1;
                Gen.setControlFocus(ctrlName);
                return false;
            }
            ctrlName = InsdInst.tabpage3cofirm();
            if (ctrlName != "")
            {
                tabControl1.SelectedIndex = 2;
                InsdInst.setControlFocus(ctrlName);
                return false;
            }
            InsdInst.u_calculation();

            ctrlName = Install.tabpage6cofirm();
            if (ctrlName != "")
            {
                if (ctrlName == "installDgpm")
                {
                    tabControl1.SelectedIndex = 5;
                    Install.setControlFocus(ctrlName);
                }
                else
                {
                    tabControl1.SelectedIndex = 1;
                    Gen.setControlFocus(ctrlName);
                }
                return false;
            }
            return true;
        }

        public string SaveCode()
        {
            Gen.totInvfgpm = Install.totInvfgpm;
            Gen.totInvftdamt = Install.totInvftdamt;
            Gen.totInvfcomamt = Install.totInvfcomamt;
            Gen.totInvfnpm = Install.totInvfnpm;
            Gen.totInvfeci1 = Install.totInvfeci1;
            Gen.totInvfeci2 = Install.totInvfeci2;
            Gen.totInvfeci3 = Install.totInvfeci3;
            Gen.fprnexsec2 = Excess.fprnexsec2;
            Gen.installTotal = Install.installTotal;

            string result = "";
            string[] sql1 = Gen.tabpage2save();
            string newfctlid = Gen.newfctlid;
            string newcom = Gen.newcom;
            string newdisc = Gen.newdisc;
            string Subclass = Gen.GenSubClass;
            string[] sql2;
            if (Class == "CAR" || Class == "CSB" || Class == "EEC")
            {
                sql2 = InsdInst.tabpage3save(newfctlid, Subclass, Gen.GenEndtType1);
            }
            else
            {
                sql2 = InsdInst.tabpage3save(newfctlid, Subclass);
            }
            string[] sql3 = Excess.tabpage4save(newfctlid, Subclass);
            string[] sql4 = Attach.tabpage5save(newfctlid, Subclass);
            string[] sql5 = Install.tabpage6save(newfctlid);
            string[] sql6 = Print.tabpage7save(newfctlid, Subclass);
            if (sql6[0] == "Empty")
            { sql6 = null; }
            //if (sql6.Length < 0)
            //{
            //    if (sql6[0] == "Empty")
            //    {
            //        MessageBox.Show("Content can't be Empty!", "Warning",
            //                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            //    }
            //    else
            //    {
            string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
            result = ExecuteSqlTransaction(connectionString, sql1, sql2, sql3, sql4, sql5, sql6);
            if (result == "OK")
            {
                if (Class == "PAR")
                {
                    if (updateflag == false) { fctlid = newfctlid; }
                    string sql = "update oinsint set fitem_1= b.fitem from oinsloc b where b.fclass='PAR' and b.fpolno =(select fpolno from polh where fctlid= '" + fctlid + "') and oinsint.fctlid_2= b.fctlid";
                    DBHelper.ExecuteCommand(sql);
                }
                if ((newfctlid == "" || updateflag == true))
                {
                    if (Class == "CMP" || Class == "PMP" || Class == "CSB" || (Class == "EEC" && Gen.autori == "Yes")) { }
                    else { result = saveOrih(fctlid, "update"); }
                }
                else
                {
                    if (Class == "CMP" || Class == "PMP" || Class == "CSB" || (Class == "EEC" && Gen.autori == "Yes")) { }
                    else { result = saveOrih(newfctlid, "new"); }
                }
                if (result == "RollBack")
                {
                    result = rollback(newfctlid);
                    MessageBox.Show("RollBack", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    string oldfctlid = fctlid;
                    FillData("");
                    if ((newfctlid == "" || updateflag == true))
                    {
                        tabpage1Saveload(fctlid, newcom, newdisc);
                        DataTable dt3 = Policylist.DataSource as DataTable;
                        foreach (DataRow row in dt3.Rows)
                        {
                            int SelectedIndex = dt3.Rows.IndexOf(row);
                            if (oldfctlid == row["fctlid"].ToString().Trim())
                            {
                                Policylist.CurrentCell = Policylist.Rows[SelectedIndex].Cells["Policy#"];
                                policylistchg();
                                break;
                            }
                        }
                    }
                    else
                    {
                        tabpage1Saveload(newfctlid, newcom, newdisc);
                        DataTable dt3 = Policylist.DataSource as DataTable;
                        foreach (DataRow row in dt3.Rows)
                        {
                            int SelectedIndex = dt3.Rows.IndexOf(row);
                            if (newfctlid == row["fctlid"].ToString().Trim())
                            {
                                Policylist.CurrentCell = Policylist.Rows[SelectedIndex].Cells["Policy#"];
                                policylistchg();
                                break;
                            }
                        }
                    }
                    buttoncontrolback();
                }
            }
            else
            {
                MessageBox.Show(result, "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            //    }
            //}
            return result;
        }

        public string saveOrih(string newfctlid, string flag)
        {
            ArrayList updarr = new ArrayList();
            string fkind = UtilityFunc.GetKindFrmClass(Class);
            string fxol = "", fcalc = "";
            if (Class == "EEC" || Class == "CMP" || Class == "CGL" || Class == "PMP")
            { fxol = "1"; }
            else { fxol = "2"; }
            if (Class == "CAR" || Class == "CPM" || Class == "FGP" || Class == "MYP" || Class == "PAR")
            { fcalc = "2"; }
            else { fcalc = "1"; }
            if (flag == "new")
            {
                string orih = Fct.NewId("orih");
                string orihc = Fct.NewId("orihc");
                string orih_fctlid = "";
                bool isSuccuss = true;
                string sql = "update " + Dbm + "[xsysparm] set fnxtid =RIGHT('000000000'+ LTRIM(STR(CAST( (select max(fctlid) from orih) as int)+1)), 10) where fidtype ='orih'";
                DBHelper.GetScalar(sql);
                DataTable dt = new DataTable();
                if (Class == "CAR")
                {
                    string selsql = "select a.fctlid  as fctlid_1,fctlid_p,fctlid_v,fbus,ftype,fendttype,fpolno,fendtno,fvseq,fvarno,fextend,  " +
                                    "fsrcref,fcedepol,fcedeendt,fuwyr,fclass,fsclass,'01' as frino,b.fkind as fkind,  " +
                                    "'" + orih + "' as fctlid,fissdate,case when '" + Gen.GenEndtType1 + "'=1 then c.fincfr else c.fincfr_p end,fefffr,feffto,'" + fcalc + "' as fcalc,fsicur,b.fsi,'0.0000' as fliab,  " +
                                    "b.fupdsi as fupdsi,'0.0000' as fupdliab, " +
                                    "case when '" + Gen.Genfextend + "'=3 then 0 else case when fkind ='1' then  c.fupdsi else c.fupdliab end end as fosi, 0 as fosi2,fpmcur,  " +
                                    "b.fgpm,b.fnpm,'" + fxol + "' as fxol,'0.0000' as fsi_c,'0.0000' as fsi2_c,'0.0000' as fgpm_c,  " +
                                    "'0.0000' as fnpm_c,'0.0000' as fshare_c,finpuser,finpdate,fupduser,fupddate,fcnfuser,fcnfdate,  " +
                                    "fconfirm,'2' as fposted from polh a   " +
                                    "join (select sum(fgpm) as fgpm,sum(fnpm) as fnpm,sum(fsi) as fsi,sum(fupdsi) as fupdsi,fkind,fctlid_1 from oinsint   " +
                                    "where fctlid_1='" + newfctlid + "' and (fgpm+fnpm+fsi <> 0) group by fkind,fctlid_1) b on a.fctlid = b.fctlid_1  " +
                                    "left join (select fupdsi, fupdliab,fctlid,fincfr,fincfr_p from polh) c on c.fctlid = b.fctlid_1  " +
                                    "where a.fctlid='" + newfctlid + "' ";
                    dt = DBHelper.GetDataSet(selsql);
                    if (dt.Rows.Count > 0)
                    {
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            if (i != 0) { orih = (int.Parse(orih) + 1).ToString().PadLeft(10, '0'); }
                            dt.Rows[i]["fctlid"] = orih;
                            orih_fctlid = dt.Rows[i]["fctlid"].ToString();
                        }
                        isSuccuss = DBHelper.BulkInsertDataTable("orih", dt);
                    }
                }
                else
                {
                    string selsql = "select polh.fctlid as fctlid_1,fctlid_p,fctlid_v,fbus,ftype,fendttype,fpolno,fendtno,fvseq,fvarno,fextend,  " +
                                    "fsrcref,fcedepol,fcedeendt,fuwyr,fclass,fsclass,'01' as frino,'" + fkind + "' as fkind,  " +
                                    "'" + orih + "' as fctlid,fissdate,case when fclass ='EEC' and '" + Gen.GenEndtType1 + "' <> 1 then fincfr_p else fincfr end,fefffr,feffto,'" + fcalc + "' as fcalc,fsicur, " +
                                    "case when fclass='EEC' OR fclass='CMP' OR fclass='PMP' then flmt1 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then fliab else fsi end end as fsi, " +
                                    "case when fclass='CMP' OR fclass='PMP' then flmt2 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then 0 else fliab end end as fsi2, " +
                                    "case when fclass='EEC' OR fclass='CMP' OR fclass='PMP' then fupdlmt1 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then fupdliab else fupdsi end end as fupdsi, " +
                                    "case when fclass='CMP' OR fclass='PMP' then fupdlmt2 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then 0 else fupdliab end end as fupdsi2, " +
                                    "case when (fclass='EEC' and fendttype=3) then 0 else case when (fclass='EEC' and fendttype<>3) OR fclass='CMP' OR fclass='PMP' then b.fupdlmt1o else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then b.fosi2 else b.fosi end end end as forgsi, " +
                                    "case when fclass='CMP' OR fclass='PMP' then b.fupdlmt2o else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then 0 else b.fosi2 end end as forgliab,fpmcur, fgpm,fnpm,'" + fxol + "' as fxol, " +
                                    "case when fclass='EEC' OR fclass='CMP' OR fclass='PMP' then flmt1 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then fliab else fsi end end as fsi_c, " +
                                    "case when fclass='CMP' OR fclass='PMP' then flmt2 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then 0 else fliab end end as fsi2_c, " +
                                    "fgpm as fgpm_c, fnpm as fnpm_c,'100.0000' as fshare_c, " +
                                    "finpuser,finpdate,fupduser,fupddate,fcnfuser,fcnfdate,fconfirm,'2' as fposted from polh " +
                                    "left join (select fupdsi as fosi , fupdliab as fosi2 , fupdlmt1 as fupdlmt1o, fupdlmt2 as fupdlmt2o, fctlid from polh) b on b.fctlid = polh.fctlid_pk " +
                                    "where polh.fctlid='" + newfctlid + "'";
                    dt = DBHelper.GetDataSet(selsql);
                    if (dt.Rows.Count > 0)
                    {
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            if (i != 0) { orih = (int.Parse(orih) + 1).ToString().PadLeft(10, '0'); }
                            dt.Rows[i]["fctlid"] = orih;
                            orih_fctlid = dt.Rows[i]["fctlid"].ToString();
                        }
                        isSuccuss = DBHelper.BulkInsertDataTable("orih", dt);
                    }
                }
                if (isSuccuss == true)
                {
                    if (orih_fctlid != "")
                    {
                        string updorih = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(orih_fctlid) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='orih'";
                        updarr.Add(updorih);
                    }
                    if (Class == "CAR" && dt.Rows.Count > 0)
                    {
                        string sqlorihc = "insert into orihc " +
                            "select fctlid,fbus,ftype,fpolno,fvseq,fvarno,fendtno,fsrcref,fcedepol,fcedeendt,fuwyr,fclass, " +
                            "fsclass,'01' as frino,'" + orihc + "' as fctlid,finpuser,finpdate,fupduser,fupddate,fcnfuser,fcnfdate,fconfirm, " +
                            "'1' as fstatus1,'2' as fposted " +
                            "from polh where fctlid='" + newfctlid + "'";
                        string updorihc = "update " + Dbm + "xsysparm set fnxtid=RIGHT('000000000'+ LTRIM(STR(CAST('" + orihc + "' as int)+1)), 10) where fidtype ='orihc'";
                        updarr.Add(sqlorihc);
                        updarr.Add(updorihc);
                    }
                    if (Class == "PAR" || Class == "CPM")
                    {
                        string oril = Fct.NewId("oril"); string oril_fctlid = "", selsql = "";
                        string sqlloc = "select * from oinsloc where fctlid_1 ='" + newfctlid + "'";
                        SqlDataReader sdr = DBHelper.GetReader(sqlloc);
                        if (sdr.HasRows)
                        {
                            if (Class == "PAR")
                            {
                                selsql = "select a.fctlid as fctlid_1,a.fctlid_p,a.fctlid_v,b.fctlid as fctlid_ri,a.fbus,a.ftype,a.fpolno,a.fvseq,a.fvarno,a.fendtno,a.fendttype, a.fextend,  " +
                                                "a.fsrcref,a.fcedepol,a.fcedeendt,a.fuwyr,a.fclass,a.fsclass,'01' as frino,'" + fkind + "' as fkind,  " +
                                                "'" + oril + "' as fctlid,'' as flocid,isnull(c.flogseq,1) as flogseq,c.fitem,c.fmode,a.fissdate,a.fincfr,a.fefffr,a.feffto, " +
                                                "c.frm,c.fflr,c.fblk,c.flot,c.fbldg,c.fstno,c.fstno2,c.fst,c.fdist,0 as fqty,0 as fupdqty,'' as funit,'' as fyr,'' as fdesc1, " +
                                                "'" + fcalc + "' as fcalc,fsicur,c.fsi,'0.0000' as fsi2,c.fupdsi,'0.0000' as fupdsi2,c.fupdsi as fosi, " +
                                                "'0.0000' as fosi2,a.fpmcur, c.fgpm,c.fnpm,'" + fxol + "' as fxol, " +
                                                "c.fsi as fsi_c,'0.0000' as fsi2_c,c.fgpm as fgpm_c, c.fnpm as fnpm_c,'100.0000' as fshare_c " +
                                                "from polh a left join oinsloc c on a.fctlid = c.fctlid_1 left join orih b on b.fctlid_1 = a.fctlid ";
                                selsql = selsql + "where a.fctlid='" + newfctlid + "'";
                            }
                            if (Class == "CPM")
                            {
                                selsql = "select a.fctlid as fctlid_1,a.fctlid_p,a.fctlid_v,b.fctlid as fctlid_ri,a.fbus,a.ftype,a.fpolno,a.fvseq,a.fvarno,a.fendtno,a.fendttype, a.fextend,  " +
                                        "a.fsrcref,a.fcedepol,a.fcedeendt,a.fuwyr,a.fclass,a.fsclass,'01' as frino,'" + fkind + "' as fkind,  " +
                                        "'" + oril + "' as fctlid,'' as flocid,isnull(c.flogseq,1) as flogseq,c.fitem,c.fmode,a.fissdate,a.fincfr,a.fefffr,a.feffto, " +
                                        "'' as frm,'' as fflr,'' as fblk,'' as flot,'' as fbldg,'' as fstno,'' as fstno2,'' as fst,'' as fdist, " +
                                        "c.fqty as fqty,c.fupdqty as fupdqty,c.funit as funit,c.fyr as fyr,c.fdesc1 as fdesc1, " +
                                        "'" + fcalc + "' as fcalc,fsicur,c.fsi,'0.0000' as fsi2,c.fupdsi,'0.0000' as fupdsi2,c.fupdsi as fosi, " +
                                        "'0.0000' as fosi2,a.fpmcur, c.fgpm,c.fnpm,'" + fxol + "' as fxol, " +
                                        "c.fsi as fsi_c,'0.0000' as fsi2_c,c.fgpm as fgpm_c, c.fnpm as fnpm_c,'100.0000' as fshare_c " +
                                        "from polh a left join oinsint_d c on a.fctlid = c.fctlid_1 left join orih b on b.fctlid_1 = a.fctlid ";
                                selsql = selsql + "where a.fctlid='" + newfctlid + "'";
                            }
                            dt = DBHelper.GetDataSet(selsql);
                            if (dt.Rows.Count > 0)
                            {
                                for (int i = 0; i < dt.Rows.Count; i++)
                                {
                                    if (i != 0) { oril = (int.Parse(oril) + 1).ToString().PadLeft(10, '0'); }
                                    dt.Rows[i]["fctlid"] = oril;
                                    oril_fctlid = dt.Rows[i]["fctlid"].ToString();
                                }
                                isSuccuss = DBHelper.BulkInsertDataTable("oril", dt);
                                if (isSuccuss == true && oril_fctlid != "")
                                {
                                    string updoril = "update " + Dbm + "xsysparm set fnxtid=RIGHT('000000000'+ LTRIM(STR(CAST('" + oril_fctlid + "' as int)+1)), 10) where fidtype ='oril'";
                                    updarr.Add(updoril);
                                }
                            }
                        }
                    }
                    DBHelper.ExecuteSqlTransaction((string[])updarr.ToArray(typeof(string)));
                }
                if (isSuccuss == true) { return "OK"; }
                else { return "RollBack"; }
            }
            else
            {
                bool isSuccuss = false;
                if (Class == "CAR")
                {
                    string sqlupdorih = "UPDATE orih SET orih.fpolno = b.fpolno,orih.fendtno = b.fendtno,orih.[fcedepol] = b.fcedepol, " +
                    "orih.[fcedeendt] = b.fcedeendt,orih.[fuwyr] = b.fuwyr,orih.[fclass] = b.fclass,orih.[fsclass] = b.fsclass, " +
                    "orih.[fissdate] = b.fissdate,orih.[fincpdate] = b.fincpdate,orih.[fefffr] = b.fefffr, orih.[feffto] = b.feffto, " +
                    "orih.[fsi] = b.fsi,orih.[fsi2] = b.fliab,orih.[fupdsi] = b.fupdsi,orih.[fupdsi2] = b.fupdliab, " +
                    "orih.[fosi] = b.fosi,orih.[fosi2] = b.forgliab,orih.[fgpm] = b.fgpm,orih.[fnpm] = b.fnpm, " +
                    "orih.[finpuser] = b.finpuser,orih.[finpdate] = b.finpdate, orih.[fupduser] = b.fupduser, " +
                    "orih.[fupddate] = b.fupddate, orih.[fcnfuser] = b.fcnfuser,orih.[fcnfdate] = b.fcnfdate,  " +
                    "orih.[fstatus] = b.fconfirm " +
                    "FROM orih INNER JOIN " +
                    "(select a.fctlid,b.fkind,a.fpolno,fendtno,fcedepol,fcedeendt,fuwyr,fclass,fsclass,  " +
                        "fissdate,case when '" + Gen.GenEndtType1 + "'=1 then c.fincfr else c.fincfr_p end as fincpdate,fefffr,feffto,b.fsi,'0.0000' as fliab,   " +
                        "b.fupdsi as fupdsi,'0.0000' as fupdliab,case when '" + Gen.Genfextend + "'=3 then 0 else case when fkind ='1' then  c.fupdsi else c.fupdliab end end as fosi, " +
                        "'0.0000' as forgliab,fpmcur,   " +
                        "b.fgpm,b.fnpm,finpuser,finpdate,fupduser,fupddate,fcnfuser,fcnfdate,   " +
                        "fconfirm from polh a    " +
                        "left join (select sum(fgpm) as fgpm,sum(fnpm) as fnpm,sum(fsi) as fsi,sum(fupdsi) as fupdsi,fkind,fctlid_1 from oinsint    " +
                        "where fctlid_1='" + newfctlid + "' group by fkind,fctlid_1) b on a.fctlid = b.fctlid_1   " +
                        "left join (select fupdsi, fupdliab,fctlid,fincfr,fincfr_p from polh) c on c.fctlid = b.fctlid_1  " +
                        "where a.fctlid='" + newfctlid + "') as b " +
                    "ON orih.fctlid_1 = b.fctlid and orih.fkind = b.fkind";
                    updarr.Add(sqlupdorih);
                }
                else
                {
                    string sqlupdorih = "UPDATE orih SET orih.fpolno = b.fpolno,orih.fendtno = b.fendtno,orih.[fcedepol] = b.fcedepol, " +
                   "orih.[fcedeendt] = b.fcedeendt,orih.[fuwyr] = b.fuwyr,orih.[fclass] = b.fclass,orih.[fsclass] = b.fsclass, " +
                   "orih.[fissdate] = b.fissdate,orih.[fincpdate] = b.fincpdate,orih.[fefffr] = b.fefffr, orih.[feffto] = b.feffto, " +
                   "orih.[fsi] = b.fsi,orih.[fsi2] = b.fsi2,orih.[fupdsi] = b.fupdsi,orih.[fupdsi2] = b.fupdsi2, " +
                   "orih.[fosi] = isnull(b.fosi,0),orih.[fosi2] = isnull(b.fosi2,0),orih.[fgpm] = b.fgpm,orih.[fnpm] = b.fnpm, " +
                   "orih.[finpuser] = b.finpuser,orih.[finpdate] = b.finpdate, orih.[fupduser] = b.fupduser, " +
                   "orih.[fupddate] = b.fupddate, orih.[fcnfuser] = b.fcnfuser,orih.[fcnfdate] = b.fcnfdate,  " +
                   "orih.[fstatus] = b.fconfirm " +
                   "FROM orih INNER JOIN " +
                   "(select polh.fctlid,'" + fkind + "' as fkind,fpolno,fendtno,fcedepol,fcedeendt,fuwyr,fclass,fsclass, " +
                   "fissdate,case when fclass ='EEC' and '" + Gen.GenEndtType1 + "' <> 1 then fincfr_p else fincfr end as fincpdate,fefffr,feffto,'" + fcalc + "' as fcalc,fsicur, " +
                    "case when fclass='EEC' OR fclass='CMP' OR fclass='PMP' then flmt1 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then fliab else polh.fsi end end as fsi," +
                    "case when fclass='CMP' OR fclass='PMP' then flmt2 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then 0 else fliab end end as fsi2, " +
                    "case when fclass='EEC' OR fclass='CMP' OR fclass='PMP' then fupdlmt1 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then fupdliab else polh.fupdsi end end as fupdsi, " +
                    "case when fclass='CMP' OR fclass='PMP' then fupdlmt2 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then 0 else fupdliab end end as fupdsi2, " +
                    "case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then fupdliab else case when fclass='EEC' OR fclass='CAR' then case when fendttype='3' then 0 else b.fupdsi end end end as fosi, " +
                    "case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then 0 else case when fclass='EEC' OR fclass='CAR' then case when fendttype='3' then 0 else b.fupdsi2 end end end as fosi2,fpmcur, " +
                   "fgpm,fnpm,finpuser,finpdate,fupduser,fupddate,fcnfuser,fcnfdate, " +
                   "fconfirm,'2' as fposted from polh " +
                   "left join (select " +
                   "case when fclass='EEC' OR fclass='CMP' OR fclass='PMP' then fupdlmt1 else fupdsi end as fupdsi, " +
                   "case when fclass='CMP' OR fclass='PMP' then fupdlmt2 else fupdliab end as fupdsi2,fctlid from polh) b on polh.fctlid_pk = b.fctlid " +
                   "where polh.fctlid='" + newfctlid + "') as b " +
                   "ON orih.fctlid_1 = b.fctlid and orih.fkind = b.fkind";
                    updarr.Add(sqlupdorih);
                }

                if (Class == "CAR")
                {
                    string sqlupdorihc = "UPDATE orihc SET orihc.fpolno = b.fpolno,orihc.fendtno = b.fendtno,orihc.[fuwyr] = b.fuwyr,orihc.[fclass] = b.fclass,orihc.[fsclass] = b.fsclass, " +
                    "orihc.[finpuser] = b.finpuser,orihc.[finpdate] = b.finpdate, orihc.[fupduser] = b.fupduser, " +
                    "orihc.[fupddate] = b.fupddate, orihc.[fcnfuser] = b.fcnfuser,orihc.[fcnfdate] = b.fcnfdate,  " +
                    "orihc.[fstatus] = b.fconfirm " +
                    "FROM orihc INNER JOIN " +
                    "(select fctlid,fpolno,fendtno,fuwyr,fclass, " +
                    "fsclass,finpuser,finpdate,fupduser,fupddate,fcnfuser,fcnfdate,fconfirm " +
                    "from polh where fctlid='" + newfctlid + "') as b " +
                    "ON orihc.fctlid_1 = b.fctlid ";
                    updarr.Add(sqlupdorihc);
                }
                if (Class == "PAR" || Class == "CPM")
                {
                    string deloril = "delete from oril where fctlid_e='" + newfctlid + "'";
                    isSuccuss = DBHelper.ExecuteCommandPAR(deloril);
                    if (isSuccuss == true)
                    {
                        string oril = Fct.NewId("oril"); string oril_fctlid = "", selsql = "";
                        if (Class == "PAR")
                        {
                            selsql = "select a.fctlid as fctlid_1,a.fctlid_p,a.fctlid_v,b.fctlid as fctlid_ri,a.fbus,a.ftype,a.fpolno,a.fvseq,a.fvarno,a.fendtno,a.fendttype, a.fextend,  " +
                                            "a.fsrcref,a.fcedepol,a.fcedeendt,a.fuwyr,a.fclass,a.fsclass,'01' as frino,'" + fkind + "' as fkind,  " +
                                            "'" + oril + "' as fctlid,'' as flocid,c.flogseq,c.fitem,c.fmode,a.fissdate,a.fincfr,a.fefffr,a.feffto, " +
                                            "c.frm,c.fflr,c.fblk,c.flot,c.fbldg,c.fstno,c.fstno2,c.fst,c.fdist,0 as fqty,0 as fupdqty,'' as funit,'' as fyr,'' as fdesc1, " +
                                            "'" + fcalc + "' as fcalc,fsicur,c.fsi,'0.0000' as fsi2,c.fupdsi,'0.0000' as fupdsi2,c.fupdsi as fosi, " +
                                            "'0.0000' as fosi2,a.fpmcur, c.fgpm,c.fnpm,'" + fxol + "' as fxol, " +
                                            "c.fsi as fsi_c,'0.0000' as fsi2_c,c.fgpm as fgpm_c, c.fnpm as fnpm_c,'100.0000' as fshare_c " +
                                            "from polh a left join oinsloc c on a.fendtno = c.fendtno left join orih b on b.fctlid_1 = a.fctlid " +
                                            "where a.fctlid='" + newfctlid + "'";
                        }
                        if (Class == "CPM")
                        {
                            selsql = "select a.fctlid as fctlid_1,a.fctlid_p,a.fctlid_v,b.fctlid as fctlid_ri,a.fbus,a.ftype,a.fpolno,a.fvseq,a.fvarno,a.fendtno,a.fendttype, a.fextend,  " +
                                    "a.fsrcref,a.fcedepol,a.fcedeendt,a.fuwyr,a.fclass,a.fsclass,'01' as frino,'" + fkind + "' as fkind,  " +
                                    "'" + oril + "' as fctlid,'' as flocid,c.flogseq,c.fitem,c.fmode,a.fissdate,a.fincfr,a.fefffr,a.feffto, " +
                                    "'' as frm,'' as fflr,'' as fblk,'' as flot,'' as fbldg,'' as fstno,'' as fstno2,'' as fst,'' as fdist, " +
                                    "c.fqty as fqty,c.fupdqty as fupdqty,c.funit as funit,c.fyr as fyr,c.fdesc1 as fdesc1, " +
                                    "'" + fcalc + "' as fcalc,fsicur,c.fsi,'0.0000' as fsi2,c.fupdsi,'0.0000' as fupdsi2,c.fupdsi as fosi, " +
                                    "'0.0000' as fosi2,a.fpmcur, c.fgpm,c.fnpm,'" + fxol + "' as fxol, " +
                                    "c.fsi as fsi_c,'0.0000' as fsi2_c,c.fgpm as fgpm_c, c.fnpm as fnpm_c,'100.0000' as fshare_c " +
                                    "from polh a left join oinsint_d c on a.fendtno = c.fendtno left join orih b on b.fctlid_1 = a.fctlid " +
                                     "where a.fctlid='" + newfctlid + "'";
                        }
                        DataTable dt = DBHelper.GetDataSet(selsql);
                        if (dt.Rows.Count > 0)
                        {
                            for (int i = 0; i < dt.Rows.Count; i++)
                            {
                                if (i != 0) { oril = (int.Parse(oril) + 1).ToString().PadLeft(10, '0'); }
                                dt.Rows[i]["fctlid"] = oril;
                                oril_fctlid = dt.Rows[i]["fctlid"].ToString();
                            }
                            isSuccuss = DBHelper.BulkInsertDataTable("oril", dt);
                            if (isSuccuss == true)
                            {
                                string updoril = "update " + Dbm + "xsysparm set fnxtid=RIGHT('000000000'+ LTRIM(STR(CAST('" + oril_fctlid + "' as int)+1)), 10) where fidtype ='oril'";
                                updarr.Add(updoril);
                            }
                        }
                    }
                }
                DBHelper.ExecuteSqlTransaction((string[])updarr.ToArray(typeof(string)));
                return "";
            }
        }

        private string ExecuteSqlTransaction(string connectionString, string[] sql1, string[] sql2, string[] sql3, string[] sql4, string[] sql5, string[] sql6)
        {
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                SqlCommand command = connection.CreateCommand();
                SqlTransaction transaction;

                // Start a local transaction.
                transaction = connection.BeginTransaction("SampleTransaction");

                // Must assign both transaction object and connection 
                // to Command object for a pending local transaction
                command.Connection = connection;
                command.Transaction = transaction;

                try
                {
                    if (sql1 != null)
                    {
                        for (int i = 0; i < sql1.Length; i++)
                        {
                            command.CommandText = sql1[i];
                            command.ExecuteNonQuery();
                        }
                    }
                    if (sql2 != null)
                    {
                        for (int i = 0; i < sql2.Length; i++)
                        {
                            command.CommandText = sql2[i];
                            command.ExecuteNonQuery();
                        }
                    }
                    if (sql3 != null)
                    {
                        for (int i = 0; i < sql3.Length; i++)
                        {
                            if (sql3[i] != "" && sql3[i] != null)
                            {
                                command.CommandText = sql3[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (sql4 != null)
                    {
                        for (int i = 0; i < sql4.Length; i++)
                        {
                            if (sql4[i] != "" && sql4[i] != null)
                            {
                                command.CommandText = sql4[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (sql5 != null)
                    {
                        for (int i = 0; i < sql5.Length; i++)
                        {
                            if (sql5[i] != "" && sql5[i] != null)
                            {
                                command.CommandText = sql5[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (sql6 != null)
                    {
                        for (int i = 0; i < sql6.Length; i++)
                        {
                            if (sql6[i] != "" && sql6[i] != null)
                            {
                                command.CommandText = sql6[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    // Attempt to commit the transaction.
                    transaction.Commit();
                    return "OK";
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                    Console.WriteLine("  Message: {0}", ex.Message);

                    // Attempt to roll back the transaction. 
                    try
                    {
                        transaction.Rollback();
                        return "RollBack";
                    }
                    catch (Exception ex2)
                    {
                        // This catch block will handle any errors that may have occurred 
                        // on the server that would cause the rollback to fail, such as 
                        // a closed connection.
                        Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                        Console.WriteLine("  Message: {0}", ex2.Message);
                        return ex2.Message;
                    }
                }
            }
        }

        private void Endorsement_Click(object sender, EventArgs e)
        {
            this.ClientSize = new System.Drawing.Size(20, 20);
        }

        private void Endorsement_Resize(object sender, EventArgs e)
        {
            if (WindowState == FormWindowState.Maximized)
            {
                //最大化时所需的操作 
              //  this.ClientSize = new System.Drawing.Size(948, 618);
               // this.WindowState = FormWindowState.Normal;
            }
            else if (WindowState == FormWindowState.Minimized)
            {
                //最小化时所需的操作

                this.ClientSize = new System.Drawing.Size(50, 50);
                this.WindowState = FormWindowState.Normal;
            }
        }



    }
}
