using CrystalDecisions.CrystalReports.Engine;
using CrystalDecisions.Shared;
using INS.Business.objRpt;
using INS.INSClass;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Web.Script.Serialization;
using System.Windows.Forms;

namespace INS.Business.Report
{
    public partial class pfacinv : Form
    {
        BindData[] u_fdoctype = new BindData[]  {
                                 new BindData("1", "DR/CR Note" ),
                                 new BindData("2", "憑 單")};
        //private string rpDirectory = Application.StartupPath.Replace("\\bin\\Debug", "\\objRpt");
        private string rpDirectory = "M:\\Software II\\New INS Runtime\\Setup\\objRpt";
        public string fctlid_1 = "", p_calling = "", finvno = "", flag = "";
        public string fclmno = "", fbilldate = "", fpayee = "", fcremark = "", yccamount = "", yamount = "", fcinpname = "", fclass = "", fpolno = "", fctlid_py="", ld_fnature="", ln_flogseq="";
        public DateTime ld_faccdate;
        public string Dbo = InsEnvironment.DataBase.GetDbo();
        public DataTable dt = new DataTable();
        private DrCrNote drCrNote;
        private DrCrNoteFacIn drCrNoteFacIn;
        private DrCrNoteFacOut drCrNoteFacOut;
        private tpyinvh tpyinvh;
        private tpyrinvh tpyrinvh;
        string[,] u_paycatarr_desc = {
                                     {"0",""},
                                     {"1","僱員補償賠款"},
                                     {"2","普通法責任賠款"},
                                     {"3","索償人之訴訟費用(僱員補償)"},
                                     {"4","索償人之訴訟費用(普通法責任)"}
                                };
        string[,] u_pynatarr_desc ={
                                    {"0","","0"},
                                    {"01","Claim Amount","01"},
                                    {"02","Own Solicitor Fee","02"},
                                    {"03","Adjuster Fee","07"},
                                    {"04","Other","09"},
                                    {"05","Salvage","06"},
                                    {"06","Claim Amount/TP","05"},
                                    {"07","Clmnt's Solicitor Fee","03"},
                                    {"08","Healthcare Fee","08"},
                                    {"09","Claim Amount/Insd","04"},
                                    {"10","Internal Adjustment","10"}
                                };
        public pfacinv()
        {
            InitializeComponent();
            fdoctype.DataSource = u_fdoctype;
            fdoctype.ValueMember = "ID";
            fdoctype.DisplayMember = "Item";
        }

        public pfacinv(DrCrNoteFacOut drCrNoteFacOut)
        {
            // TODO: Complete member initialization
            this.drCrNoteFacOut = drCrNoteFacOut;
            InitializeComponent();
            fdoctype.DataSource = u_fdoctype;
            fdoctype.ValueMember = "ID";
            fdoctype.DisplayMember = "Item";
        }

        public pfacinv(tpyinvh tpyinvh)
        {
            // TODO: Complete member initialization
            this.tpyinvh = tpyinvh;
            InitializeComponent();
            ccpy.Visible = false;
            ccpy.Checked = false;
            pcpy.Text = "Origianl Copy";
            fdoctype.DataSource = u_fdoctype;
            fdoctype.ValueMember = "ID";
            fdoctype.DisplayMember = "Item";
            fdoctype.SelectedIndex = 1;
        }

        public pfacinv(tpyrinvh tpyrinvh)
        {
            // TODO: Complete member initialization
            this.tpyrinvh = tpyrinvh;
            InitializeComponent();
            ccpy.Visible = false;
            ccpy.Checked = false;
            pcpy.Text = "Origianl Copy";
            fdoctype.DataSource = u_fdoctype;
            fdoctype.ValueMember = "ID";
            fdoctype.DisplayMember = "Item";
            fdoctype.SelectedIndex = 1;
        }

        private void button3_Click(object sender, EventArgs e)
        {
            if (fdoctype.SelectedValue == "1")
            {
                progressBar1.Visible = true;
                progressBar1.Maximum = 50;//设置最大长度值 
                progressBar1.Value = 0;//设置当前值 
                progressBar1.Step = 5;//设置没次增长多少 
                for (int i = 0; i < 10; i++)//循环 
                {
                    System.Threading.Thread.Sleep(1000);//暂停1秒 
                    progressBar1.Value += progressBar1.Step;
                }

                DataTable dtpolh = new DataTable();
                if (p_calling == "RIOF")
                {
                    dtpolh = DBHelper.GetDataSet("select * from oriinvh where fctlid in ('" + fctlid_1.Replace(",", "','") + "')");
                }
                if (p_calling == "PYRI")
                {
                    dtpolh = DBHelper.GetDataSet("select * from opyrinvh where fctlid_py = (select fctlid_py from opyrinvh where fctlid ='" + fctlid_1 + "')");
                }
                if (p_calling == "PYIV")
                {
                    dtpolh = DBHelper.GetDataSet("select * from opyinvh where fctlid_PY = '" + fctlid_1 + "'");
                }

                foreach (DataRow dr in dtpolh.Rows)
                {
                    if (p_calling == "PYRI" && dr["fctlid"].ToString() == fctlid_1)
                    {
                        finvno = dr["finvno"].ToString().Trim();
                    }
                    if (p_calling != "PYRI")
                    {
                        finvno = dr["finvno"].ToString().Trim();
                    }
                    fpolno = dr["fpolno"].ToString().Trim();
                    fclass = dr["fclass"].ToString().Trim();
                    if (p_calling != "RIOF")
                    {
                        fclmno = dr["fclmno"].ToString().Trim();
                    }
                }
                GenerDrNote(fclass, "PCA");
                MessageBox.Show("請查看M盤", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                progressBar1.Value = 0;
            }
            else
            {
                MessageBox.Show("請選擇CR/DR Note", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void PS_Click(object sender, EventArgs e)
        {
            //progressBar1.Visible = true;
            //progressBar1.Maximum = 50;//设置最大长度值 
            //progressBar1.Value = 0;//设置当前值 
            //progressBar1.Step = 5;//设置没次增长多少 
            //for (int i = 0; i < 10; i++)//循环 
            //{
            //    System.Threading.Thread.Sleep(1000);//暂停1秒 
            //    progressBar1.Value += progressBar1.Step;
            //}

            string fctlids = "";
            if (checkBox1.Checked)
            {
                callCrPrmReg(fctlid_1);
            }
            if (checkBox2.Checked)
            {
                string sql = "";
                if (p_calling == "PYRI")
                {
                    sql = "select fctlid from opyrinvh where fctlid_py = (select fctlid_py from opyrinvh where fctlid ='" + fctlid_1 + "') ";
                }
                if (p_calling == "RIOF")
                {
                    sql = "select fctlid from oriinvh where fctlid_i in (select fctlid_i from oriinvh  where fctlid = '" + fctlid_1 + "') and fposted =1 ";
                }
                if (p_calling == "PYIV")
                {
                    sql = "select fctlid_PY as fctlid from opyinvh where fctlid_PY = '" + fctlid_1 + "'";
                }
                DataTable dt4 = DBHelper.GetDataSet(sql);
                foreach (DataRow row in dt4.Rows)
                {
                    if (fctlids == "") { fctlids = Fct.stFat(row["fctlid"]); }
                    else { fctlids = fctlids + "," + Fct.stFat(row["fctlid"]); }
                }
                callCrPrmReg(fctlids);
            }
            if (checkBox3.Checked)
            {
                string sql = "";
                if (p_calling == "RIOF")
                {
                    sql = "select fctlid from oriinvh where fctlid >= '" + fctlid_1 + "' and fposted =1 ";
                }
                if (p_calling == "PYRI")
                {
                    sql = "select fctlid from opyrinvh where fctlid_py = (select fctlid_py from opyrinvh where fctlid ='" + fctlid_1 + "') ";
                }
                if (p_calling == "PYIV")
                {
                    sql = "select fctlid_PY as fctlid from opyinvh where fctlid_PY = '" + fctlid_1 + "'";
                }
                if (p_calling == "RIOF")
                {
                    //DataRow[] rows = dt.Select("fctlid >= '" + fctlid_1 + "' and Posted = 'Yes' ");
                    //for (int i = 0; i < Fct.snFat(domainUpDown1.Text); i++)
                    //{
                    //    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                    //    else { fctlids = fctlids + "," + Fct.stFat(rows[i]["fctlid"]); }
                    //}
                    DataRow[] rows = dt.Select("[Dr/Cr No.] >= '" + finvno + "'");
                    for (int i = 0; i < Fct.snFat(domainUpDown1.Text); i++)
                    {
                        if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                        else { fctlids = fctlids + "," + Fct.stFat(rows[i]["fctlid"]); }
                    }
                    callCrPrmReg(fctlids);
                }
                else
                {
                    DataTable dt4 = DBHelper.GetDataSet(sql);
                    for (int i = 0; i < Fct.snFat(domainUpDown1.Text); i++)
                    {
                        if (fctlids == "") { fctlids = Fct.stFat(dt4.Rows[i]["fctlid"]); }
                        else { fctlids = fctlids + "," + Fct.stFat(dt4.Rows[i]["fctlid"]); }
                    }
                }

                callCrPrmReg(fctlids);
            }

            //this.Close();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (fdoctype.SelectedValue == "1")
            {
                progressBar1.Visible = true;
                progressBar1.Maximum = 50;//设置最大长度值 
                progressBar1.Value = 0;//设置当前值 
                progressBar1.Step = 5;//设置没次增长多少 
                for (int i = 0; i < 10; i++)//循环 
                {
                    System.Threading.Thread.Sleep(1000);//暂停1秒 
                    progressBar1.Value += progressBar1.Step;
                }

                DataTable dtpolh = new DataTable();
                if (p_calling == "RIOF")
                {
                    dtpolh = DBHelper.GetDataSet("select * from oriinvh where fctlid in ('" + fctlid_1.Replace(",", "','") + "')");
                }
                if (p_calling == "PYRI")
                {
                    dtpolh = DBHelper.GetDataSet("select * from opyrinvh where fctlid_py = (select fctlid_py from opyrinvh where fctlid ='" + fctlid_1 + "')");
                }
                if (p_calling == "PYIV")
                {
                    dtpolh = DBHelper.GetDataSet("select * from opyinvh where fctlid_PY = '" + fctlid_1 + "'");
                }

                foreach (DataRow dr in dtpolh.Rows)
                {
                    if (p_calling == "PYRI" && dr["fctlid"].ToString() == fctlid_1)
                    {
                        finvno = dr["finvno"].ToString().Trim();
                    }
                    if (p_calling != "PYRI") 
                    {
                        finvno = dr["finvno"].ToString().Trim();
                    }
                    fpolno = dr["fpolno"].ToString().Trim();
                    fclass = dr["fclass"].ToString().Trim();
                    if (p_calling != "RIOF")
                    {
                        fclmno = dr["fclmno"].ToString().Trim();
                    }
                }
                GenerDrNote(fclass, "PC");
                MessageBox.Show("請查看M盤", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                progressBar1.Value = 0;
            }
            else {
                MessageBox.Show("請選擇CR/DR Note", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        void callCrPrmReg(string fctlid_1)
        {
            string fcpytype = "1";
            string Dbo = InsEnvironment.DataBase.GetDbo();
            DataTable dtall = new DataTable();
            DataTable dt = new DataTable();
            DataTable dt1 = new DataTable();
            DataTable dt2 = new DataTable();
            DataTable dt3 = new DataTable();
            SqlParameter[] paramall = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };
            if (p_calling == "RIOF") { dtall = DBHelper.GetDataSetProd("RIOF", Dbo, paramall); }
            if (p_calling == "PYIV") { dtall = DBHelper.GetDataSetProd("PYIV", Dbo, paramall); }
            if (p_calling == "PYRI") { dtall = DBHelper.GetDataSetProd("PYRI", Dbo, paramall); }
            dtall.Clear();

            if (pcpy.Checked)
            {
                fcpytype = "P";
                SqlParameter[] param = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };

                if (p_calling == "RIOF") { dt = DBHelper.GetDataSetProd("RIOF", Dbo, param); }
                if (p_calling == "PYIV") { dt = DBHelper.GetDataSetProd("PYIV", Dbo, param); }
                if (p_calling == "PYRI") { dt = DBHelper.GetDataSetProd("PYRI", Dbo, param); }
            }
            if (acpy.Checked)
            {
                fcpytype = "A";
                SqlParameter[] param = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };

                if (p_calling == "RIOF") { dt1 = DBHelper.GetDataSetProd("RIOF", Dbo, param); }
                if (p_calling == "PYIV") { dt1 = DBHelper.GetDataSetProd("PYIV", Dbo, param); }
                if (p_calling == "PYRI") { dt1 = DBHelper.GetDataSetProd("PYRI", Dbo, param); }
            }
            if (icpy.Checked)
            {
                fcpytype = "I";
                SqlParameter[] param = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };

                if (p_calling == "RIOF") { dt2 = DBHelper.GetDataSetProd("RIOF", Dbo, param); }
                if (p_calling == "PYIV") { dt2 = DBHelper.GetDataSetProd("PYIV", Dbo, param); }
                if (p_calling == "PYRI") { dt2 = DBHelper.GetDataSetProd("PYRI", Dbo, param); }
            }
            //if (ccpy.Checked)
            //{
            //    fcpytype = "C";
            //    SqlParameter[] param = new SqlParameter[] {
            //            new SqlParameter("@fctlid_1", fctlid_1),
            //            new SqlParameter("@fcpytype", fcpytype)
            //    };

            //    if (p_calling == "RIOF") { dt3 = DBHelper.GetDataSetProd("RIOF", Dbo, param); }
            //    if (p_calling == "PYIV") { dt3 = DBHelper.GetDataSetProd("PYIV", Dbo, param); }
            //    if (p_calling == "PYRI") { dt3 = DBHelper.GetDataSetProd("PYRI", Dbo, param); }
            //}

            ReportDocument cryRpt = new ReportDocument();

            if (fdoctype.SelectedValue == "2")
            {
                if (p_calling == "RIOF") { cryRpt.Load(rpDirectory + "\\drac2.rpt"); }
                else
                {
                    cryRpt.Load(rpDirectory + "\\drac.rpt");
                }
            }
            else
            {
                if (p_calling == "RIOF") { cryRpt.Load(rpDirectory + "\\drnote2.rpt"); }
                if (p_calling == "PYIV") { cryRpt.Load(rpDirectory + "\\drnote3.rpt"); }
                if (p_calling == "PYRI") { cryRpt.Load(rpDirectory + "\\drnote4.rpt"); }
            }

            if (checkBox1.Checked)
            {
                object[] obj = new object[dtall.Columns.Count];
                if (dt.Rows.Count > 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        dt.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt1.Rows.Count > 0)
                {
                    for (int i = 0; i < dt1.Rows.Count; i++)
                    {
                        dt1.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt2.Rows.Count > 0)
                {
                    for (int i = 0; i < dt2.Rows.Count; i++)
                    {
                        dt2.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt3.Rows.Count > 0)
                {
                    for (int i = 0; i < dt3.Rows.Count; i++)
                    {
                        dt3.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                cryRpt.SetDataSource(dtall);
            }
            else
            {
                object[] obj = new object[dtall.Columns.Count];
                if (dt.Rows.Count > 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        dt.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt1.Rows.Count > 0)
                {
                    for (int i = 0; i < dt1.Rows.Count; i++)
                    {
                        dt1.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt2.Rows.Count > 0)
                {
                    for (int i = 0; i < dt2.Rows.Count; i++)
                    {
                        dt2.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt3.Rows.Count > 0)
                {
                    for (int i = 0; i < dt3.Rows.Count; i++)
                    {
                        dt3.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                DataView dv = dtall.DefaultView;
                dv.Sort = "finvno desc";
                dtall = dv.ToTable();
                cryRpt.SetDataSource(dtall);
            }


            if (fdoctype.SelectedValue == "1")
            {
                if (p_calling == "RIOF")
                {
                    DataTable dtpolh = DBHelper.GetDataSet("select * from polh where fctlid in (select fctlid_e from oriinvh WHERE fctlid in ('" + fctlid_1.Replace(",", "','") + "'))");
                    cryRpt.Subreports["polh"].SetDataSource(dtpolh);
                }
            }
            else
            {
                cryRpt.DataDefinition.FormulaFields["z_remark"].Text = "'請於備妥支票後通知業務部林桂芳(內線7324)'";
                if (p_calling == "PYIV")
                {
                    string numStr = "";
                    foreach (DataRow dr in dtall.Rows)
                    {
                        numStr = NumGetString.NumGetStr(Convert.ToDouble(dr["yamount"]));
                        dr["yccamount"] = numStr;
                    }
                    // cryRpt.DataDefinition.FormulaFields["z_yccamount"].Text = String.Format("'{0}'", numStr);
                    //cryRpt.DataDefinition.FormulaFields["z_remark"].Text = "'請於備妥支票後通知理賠部鄭素婷(內線5015)'";
                    //cryRpt.DataDefinition.FormulaFields["z_remark"].Text = "'請於備妥支票後通知理賠部蕭碧英(內線5016)'";
                    cryRpt.DataDefinition.FormulaFields["z_remark"].Text = "'請於備妥支票後通知理賠部陳美珍(內線5016)'";
                }
                if (p_calling == "RIOF")
                {
                    string numStr = "";
                    foreach (DataRow dr in dtall.Rows)
                    {
                        numStr = NumGetString.NumGetStr(Convert.ToDouble(dr["yamount"]));
                        dr["yccamount"] = numStr;
                    }
                    // cryRpt.DataDefinition.FormulaFields["z_yccamount"].Text = String.Format("'{0}'", numStr);
                }
                cryRpt.SetDataSource(dtall);
            }
            cryDocViewer temp_form = new cryDocViewer(cryRpt);
            temp_form.ShowDialog();
            if (cryRpt != null)
            {
                cryRpt.Close();
                cryRpt.Dispose();
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            progressBar1.Visible = true;
            progressBar1.Maximum = 50;//设置最大长度值 
            progressBar1.Value = 0;//设置当前值 
            progressBar1.Step = 5;//设置没次增长多少 
            for (int i = 0; i < 10; i++)//循环 
            {
                System.Threading.Thread.Sleep(1000);//暂停1秒 
                progressBar1.Value += progressBar1.Step;
            }
            string fcpytype = "1";
            string Dbo = InsEnvironment.DataBase.GetDbo();
            DataTable dtall = new DataTable();
            DataTable dt = new DataTable();
            DataTable dt1 = new DataTable();
            DataTable dt2 = new DataTable();
            DataTable dt3 = new DataTable();
            SqlParameter[] paramall = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };
            if (p_calling == "RIOF") { dtall = DBHelper.GetDataSetProd("RIOF", Dbo, paramall); }
            if (p_calling == "PYIV") { dtall = DBHelper.GetDataSetProd("PYIV", Dbo, paramall); }
            if (p_calling == "PYRI") { dtall = DBHelper.GetDataSetProd("PYRI", Dbo, paramall); }
            dtall.Clear();

            if (pcpy.Checked)
            {
                fcpytype = "P";
                SqlParameter[] param = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };

                if (p_calling == "RIOF") { dt = DBHelper.GetDataSetProd("RIOF", Dbo, param); }
                if (p_calling == "PYIV") { dt = DBHelper.GetDataSetProd("PYIV", Dbo, param); }
                if (p_calling == "PYRI") { dt = DBHelper.GetDataSetProd("PYRI", Dbo, param); }
            }
            if (acpy.Checked)
            {
                fcpytype = "A";
                SqlParameter[] param = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };

                if (p_calling == "RIOF") { dt1 = DBHelper.GetDataSetProd("RIOF", Dbo, param); }
                if (p_calling == "PYIV") { dt1 = DBHelper.GetDataSetProd("PYIV", Dbo, param); }
                if (p_calling == "PYRI") { dt1 = DBHelper.GetDataSetProd("PYRI", Dbo, param); }
            }
            if (icpy.Checked)
            {
                fcpytype = "I";
                SqlParameter[] param = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };

                if (p_calling == "RIOF") { dt2 = DBHelper.GetDataSetProd("RIOF", Dbo, param); }
                if (p_calling == "PYIV") { dt2 = DBHelper.GetDataSetProd("PYIV", Dbo, param); }
                if (p_calling == "PYRI") { dt2 = DBHelper.GetDataSetProd("PYRI", Dbo, param); }
            }

            ReportDocument cryRpt = new ReportDocument();

            if (fdoctype.SelectedValue == "2")
            {
                if (p_calling == "RIOF") { cryRpt.Load(rpDirectory + "\\drac2.rpt"); }
                else
                {
                    cryRpt.Load(rpDirectory + "\\drac.rpt");
                }
            }
            else
            {
                if (p_calling == "RIOF") { cryRpt.Load(rpDirectory + "\\drnote2.rpt"); }
                if (p_calling == "PYIV") { cryRpt.Load(rpDirectory + "\\drnote3.rpt"); }
                if (p_calling == "PYRI") { cryRpt.Load(rpDirectory + "\\drnote4.rpt"); }
            }

            if (checkBox1.Checked)
            {
                object[] obj = new object[dtall.Columns.Count];
                if (dt.Rows.Count > 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        dt.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt1.Rows.Count > 0)
                {
                    for (int i = 0; i < dt1.Rows.Count; i++)
                    {
                        dt1.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt2.Rows.Count > 0)
                {
                    for (int i = 0; i < dt2.Rows.Count; i++)
                    {
                        dt2.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt3.Rows.Count > 0)
                {
                    for (int i = 0; i < dt3.Rows.Count; i++)
                    {
                        dt3.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                cryRpt.SetDataSource(dtall);
            }
            else
            {
                object[] obj = new object[dtall.Columns.Count];
                if (dt.Rows.Count > 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        dt.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt1.Rows.Count > 0)
                {
                    for (int i = 0; i < dt1.Rows.Count; i++)
                    {
                        dt1.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt2.Rows.Count > 0)
                {
                    for (int i = 0; i < dt2.Rows.Count; i++)
                    {
                        dt2.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt3.Rows.Count > 0)
                {
                    for (int i = 0; i < dt3.Rows.Count; i++)
                    {
                        dt3.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                DataView dv = dtall.DefaultView;
                dv.Sort = "finvno desc";
                dtall = dv.ToTable();
                cryRpt.SetDataSource(dtall);
            }

            cryRpt.DataDefinition.FormulaFields["z_remark"].Text = "'請於備妥支票後通知業務部林桂芳(內線7324)'";
            if (p_calling == "PYIV")
            {
                string numStr = "";
                foreach (DataRow dr in dtall.Rows)
                {
                    numStr = NumGetString.NumGetStr(Convert.ToDouble(dr["yamount"]));
                    dr["yccamount"] = numStr;
                }
                // cryRpt.DataDefinition.FormulaFields["z_yccamount"].Text = String.Format("'{0}'", numStr);
                //cryRpt.DataDefinition.FormulaFields["z_remark"].Text = "'請於備妥支票後通知理賠部鄭素婷(內線5015)'";
                //cryRpt.DataDefinition.FormulaFields["z_remark"].Text = "'請於備妥支票後通知理賠部蕭碧英(內線5016)'";
                cryRpt.DataDefinition.FormulaFields["z_remark"].Text = "'請於備妥支票後通知理賠部陳美珍(內線5016)'";
            }
            if (p_calling == "RIOF")
            {
                string numStr = "";
                foreach (DataRow dr in dtall.Rows)
                {
                    numStr = NumGetString.NumGetStr(Convert.ToDouble(dr["yamount"]));
                    dr["yccamount"] = numStr;
                }
                // cryRpt.DataDefinition.FormulaFields["z_yccamount"].Text = String.Format("'{0}'", numStr);
            }
            cryRpt.SetDataSource(dtall);

            foreach (DataRow dr in dtall.Rows)
            {
                finvno = dr["finvno"].ToString();
                fbilldate = dr["fbilldate"].ToString();
                fpayee = dr["fpayee"].ToString();
                fcremark = dr["fcremark"].ToString();
                fclass = dr["fclass"].ToString().Trim();
                if (p_calling != "PYRI")
                {
                    yccamount = dr["yccamount"].ToString();
                    yamount = dr["yamount"].ToString();
                }
                if (p_calling == "PYIV" || p_calling == "PYRI")
                {
                    fclmno = dr["fclmno"].ToString();
                    fctlid_py = dr["fctlid_py"].ToString();
                    DataTable dtclaim = DBHelper.GetDataSet("select * from ocpay where fctlid = '"+ fctlid_py + "'");
                    DataTable dtpolh = new DataTable();
                    if (dtclaim.Rows.Count > 0)
                    {
                        for (int i = 0; i < dtclaim.Rows.Count; i++)
                        {
                            ld_fnature =  dtclaim.Rows[i]["fnature"].ToString();
                            ln_flogseq = dtclaim.Rows[i]["flogseq"].ToString();
                            ld_faccdate = Convert.ToDateTime(dtclaim.Rows[i]["finvdate"].ToString());
                        }   
                    }
                    if (p_calling == "PYRI")
                    {
                        dtpolh = DBHelper.GetDataSet("select fctlid_c as fctlid from opyrinvh where finvno = '" + finvno.Trim() + "'");
                    }
                    if (p_calling == "PYIV")
                    {
                        dtpolh = DBHelper.GetDataSet("select fctlid_c as fctlid from opyinvh where fctlid_PY = '" + fctlid_py + "'");
                    }
                    pclmappr(dtpolh.Rows[0]["fctlid"].ToString());
                }
                fcinpname = InsEnvironment.LoginUser.GetChineseName();
                fpolno = dr["fpolno"].ToString().Trim();
                GenerDrNote(fclass, "AC");
            }
            byte[] fileBytes = null , extrafileBytes = null;
            if (p_calling == "RIOF")
            {
                fileBytes = ReadfileToByte.ReadFileToByte("M:\\COIL_ePolicy\\" + fclass + "\\" + fpolno + "\\" + finvno.Trim() + "AC.pdf");
            }
            else{
                fileBytes = ReadfileToByte.ReadFileToByte("M:\\COIL_eClaim\\" + fclass + "\\" + fclmno + "\\" + finvno.Trim() + "AC.pdf");
                extrafileBytes = ReadfileToByte.ReadFileToByte("M:\\COIL_eClaim\\" + fclass + "\\" + fclmno + "\\" + finvno.Trim() + "_appl.pdf");
            }
            String res = PostFileToServer("https://pur.csci.com.hk/push/api/push", fileBytes, extrafileBytes, Encoding.UTF8);
            MessageBox.Show(res.ToString(), "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
            progressBar1.Value = 0;
        }

        void pclmappr(string fctlid_p)
        {
            SqlParameter[] param = new SqlParameter[] {
                    new SqlParameter("@fctlid", fctlid_p),
                    new SqlParameter("@lc_fnature", ld_fnature),
                    new SqlParameter("@ln_flogseq", ln_flogseq),
                    new SqlParameter("@ld_faccdate", ld_faccdate),
                    new SqlParameter("@p_calling", fclass)
            };
            DataSet ds = DBHelper.GetDtSetProd("pclmappr2", Dbo, param);
            if (ds.Tables[0].Rows.Count > 0)
            {
                foreach (DataRow dr in ds.Tables[1].Rows)
                {
                    dr["fcatdesc"] = u_paycatarr_desc[int.Parse(Fct.stFat(dr["fpaycat"])), 1];
                }
                foreach (DataRow dr in ds.Tables[2].Rows)
                {
                    if (dr["fnatdesc"].ToString().Trim().Length == 2)
                    {
                        dr["fnatdesc"] = u_pynatarr_desc[int.Parse(Fct.stFat(dr["fpaynat"])), 1];
                    }
                }

                ReportDocument cryRpt = new ReportDocument();
                cryRpt.Load(rpDirectory + "\\pclmappr.rpt");
                cryRpt.DataDefinition.FormulaFields["z_title"].Text = String.Format("'{0}'", "批核/賠款記錄");
                cryRpt.DataDefinition.FormulaFields["z_class"].Text = String.Format("'{0}'", Fct.stFat(InsEnvironment.Minsc.GetFdesc()));
                cryRpt.DataDefinition.FormulaFields["z_payno"].Text = String.Format("'{0}'", Fct.snFat(ln_flogseq).ToString()); ;
                cryRpt.SetDataSource(ds.Tables[0]);
                cryRpt.Subreports["payl"].SetDataSource(ds.Tables[2]);

                try
                {
                    ExportOptions CrExportOptions;
                    DiskFileDestinationOptions CrDiskFileDestinationOptions = new DiskFileDestinationOptions();
                    PdfRtfWordFormatOptions CrFormatTypeOptions = new PdfRtfWordFormatOptions();
                    CrDiskFileDestinationOptions.DiskFileName = "M:\\PushToA8\\" + finvno.Trim() + "_appl.doc";

                    CrExportOptions = cryRpt.ExportOptions;
                    {
                        CrExportOptions.ExportDestinationType = ExportDestinationType.DiskFile;
                        CrExportOptions.ExportFormatType = ExportFormatType.WordForWindows;
                        CrExportOptions.DestinationOptions = CrDiskFileDestinationOptions;
                        CrExportOptions.FormatOptions = CrFormatTypeOptions;
                    }
                    cryRpt.Export();
                    Word2PDF WP = new Word2PDF();

                    string urlSendToCustomer = @"\\10.1.8.27\保險公司\COIL_DATA\COIL_eClaim\" + fclass.Trim() + @"\" + fclmno + @"\";
                    if (!System.IO.Directory.Exists(urlSendToCustomer))
                    {
                        System.IO.Directory.CreateDirectory(urlSendToCustomer);
                    }

                    WP.word2PDF("M:\\PushToA8\\" + finvno.Trim() + "_appl.doc", "M:\\COIL_eClaim\\" + fclass + @"\" + fclmno.Trim() + @"\" + finvno.Trim() + "_appl.pdf");
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.ToString());
                }
            }
        }

        void GenerDrNote(string fclass, string Type)
        {
            string fcpytype = "1";
            string Dbo = InsEnvironment.DataBase.GetDbo();
            DataTable dtall = new DataTable();
            DataTable dt = new DataTable();
            DataTable dt1 = new DataTable();
            DataTable dt2 = new DataTable();
            DataTable dt3 = new DataTable();
            SqlParameter[] paramall = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };
            if (p_calling == "RIOF") { dtall = DBHelper.GetDataSetProd("RIOF", Dbo, paramall); }
            if (p_calling == "PYIV") { dtall = DBHelper.GetDataSetProd("PYIV", Dbo, paramall); }
            if (p_calling == "PYRI") { dtall = DBHelper.GetDataSetProd("PYRI", Dbo, paramall); }
            dtall.Clear();
            if (Type == "PC")
            {
                if (pcpy.Checked)
                {
                    fcpytype = "P";
                    SqlParameter[] param = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };

                    if (p_calling == "RIOF") { dt = DBHelper.GetDataSetProd("RIOF", Dbo, param); }
                    if (p_calling == "PYIV") { dt = DBHelper.GetDataSetProd("PYIV", Dbo, param); }
                    if (p_calling == "PYRI") { dt = DBHelper.GetDataSetProd("PYRI", Dbo, param); }
                }
            }
            if (Type == "AC" || Type == "PCA")
            {
                if (acpy.Checked)
                {
                    fcpytype = "A";
                    SqlParameter[] param = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };

                    if (p_calling == "RIOF") { dt1 = DBHelper.GetDataSetProd("RIOF", Dbo, param); }
                    if (p_calling == "PYIV") { dt1 = DBHelper.GetDataSetProd("PYIV", Dbo, param); }
                    if (p_calling == "PYRI") { dt1 = DBHelper.GetDataSetProd("PYRI", Dbo, param); }
                }
            }

            ReportDocument cryRpt = new ReportDocument();

            if (p_calling == "RIOF") { cryRpt.Load(rpDirectory + "\\drnote2.rpt"); }
            if (p_calling == "PYIV") { cryRpt.Load(rpDirectory + "\\drnote3.rpt"); }
            if (p_calling == "PYRI") { cryRpt.Load(rpDirectory + "\\drnote4.rpt"); }

            if (checkBox1.Checked)
            {
                object[] obj = new object[dtall.Columns.Count];
                if (dt.Rows.Count > 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        dt.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt1.Rows.Count > 0)
                {
                    for (int i = 0; i < dt1.Rows.Count; i++)
                    {
                        dt1.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt2.Rows.Count > 0)
                {
                    for (int i = 0; i < dt2.Rows.Count; i++)
                    {
                        dt2.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt3.Rows.Count > 0)
                {
                    for (int i = 0; i < dt3.Rows.Count; i++)
                    {
                        dt3.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                cryRpt.SetDataSource(dtall);
            }
            else
            {
                object[] obj = new object[dtall.Columns.Count];
                if (dt.Rows.Count > 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        dt.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt1.Rows.Count > 0)
                {
                    for (int i = 0; i < dt1.Rows.Count; i++)
                    {
                        dt1.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt2.Rows.Count > 0)
                {
                    for (int i = 0; i < dt2.Rows.Count; i++)
                    {
                        dt2.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt3.Rows.Count > 0)
                {
                    for (int i = 0; i < dt3.Rows.Count; i++)
                    {
                        dt3.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                DataView dv = dtall.DefaultView;
                dv.Sort = "finvno desc";
                dtall = dv.ToTable();
                cryRpt.SetDataSource(dtall);
            }

            if (p_calling == "RIOF")
            {
                DataTable dtpolh = DBHelper.GetDataSet("select * from polh where fctlid in (select fctlid_e from oriinvh WHERE fctlid in ('" + fctlid_1.Replace(",", "','") + "'))");
                cryRpt.Subreports["polh"].SetDataSource(dtpolh);
            }

            try
            {
                ExportOptions CrExportOptions;
                DiskFileDestinationOptions CrDiskFileDestinationOptions = new DiskFileDestinationOptions();
                PdfRtfWordFormatOptions CrFormatTypeOptions = new PdfRtfWordFormatOptions();
                CrDiskFileDestinationOptions.DiskFileName = "M:\\PushToA8\\" + finvno.Trim() + ".doc";

                CrExportOptions = cryRpt.ExportOptions;
                {
                    CrExportOptions.ExportDestinationType = ExportDestinationType.DiskFile;
                    CrExportOptions.ExportFormatType = ExportFormatType.WordForWindows;
                    CrExportOptions.DestinationOptions = CrDiskFileDestinationOptions;
                    CrExportOptions.FormatOptions = CrFormatTypeOptions;
                }
                cryRpt.Export();
                Word2PDF WP = new Word2PDF();

                WP.word2PDF("M:\\PushToA8\\" + finvno.Trim() + ".doc", "M:\\PushToA8\\" + finvno.Trim() + Type + ".pdf");
                if (p_calling == "RIOF")
                {
                    ToA8.SignPloNote(finvno.Trim() + Type + ".pdf", fclass, fpolno);
                }
                else {
                    ToA8.SignClmNote(finvno.Trim() + Type + ".pdf", fclass, fclmno);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
        }
        public string PostFileToServer(string url, byte[] filedata, byte[] extraFileData, Encoding encoder)
        {
            string Postype = "";
            if (p_calling == "PYIV") { Postype = "opyinvh"; }
            else if (p_calling == "PYRI") { Postype = "opyrinvh"; }
            else if (p_calling == "RIOF") { Postype = "oriinvh"; }
            HttpClient client = new HttpClient();
            var json = Newtonsoft.Json.JsonConvert.SerializeObject(new { Fctlid = fctlid_1, Fclmno = fclmno, Finvno = finvno, Fbilldate = fbilldate, fpayee = fpayee.Trim(), Fcremark = fcremark, Yccamount = yccamount, Yamount = yamount, Fcinpname = InsEnvironment.LoginUser.GetChineseName().Trim(), UserId = InsEnvironment.LoginUser.GetUserADCode(), Postype = Postype, FileContent = filedata, ExtraFileContent = extraFileData });
            HttpContent httpContent = new StringContent(json, Encoding.UTF8, "application/json");
            HttpResponseMessage response = client.PostAsync(url, httpContent).Result;
            var result = response.Content.ReadAsStringAsync().Result;
            var s = Newtonsoft.Json.JsonConvert.DeserializeObject(result);
            JavaScriptSerializer js = new JavaScriptSerializer();//实例化一个能够序列化数据的类
            JsonOutput oput = js.Deserialize<JsonOutput>(s.ToString()); //将json数据转化为对象类型并赋值给list
            return oput.Message;
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void pfacinv_Resize(object sender, EventArgs e)
        {
            if (WindowState == FormWindowState.Maximized)
            {
                //最大化时所需的操作 
                this.ClientSize = new System.Drawing.Size(687, 363);
                this.WindowState = FormWindowState.Normal;
            }
            else if (WindowState == FormWindowState.Minimized)
            {
                //最小化时所需的操作

                this.ClientSize = new System.Drawing.Size(50, 50);
                this.WindowState = FormWindowState.Normal;
            }
        }

        private void fdoctype_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (fdoctype.SelectedIndex == 1)
            {
                pcpy.Checked = false;
                acpy.Checked = true;
                icpy.Checked = false;
                ccpy.Checked = false;
            }
            else
            {
                pcpy.Checked = true;
                acpy.Checked = true;
                icpy.Checked = true;
                if (p_calling == "PYIV" || p_calling == "PYRI")
                {
                    ccpy.Checked = false;
                }
                else { ccpy.Checked = true; }
            }
        }

        private void checkBox3_CheckedChanged(object sender, EventArgs e)
        {
            checkBox1.Checked = false;
            checkBox2.Checked = false;
        }

        private void domainUpDown1_TextChanged(object sender, EventArgs e)
        {
            checkBox3.Checked = true;
            checkBox1.Checked = false;
        }

        private void checkBox1_CheckedChanged(object sender, EventArgs e)
        {
            checkBox3.Checked = false;
            checkBox2.Checked = false;
        }

        private void checkBox2_CheckedChanged(object sender, EventArgs e)
        {
            checkBox3.Checked = false;
            checkBox1.Checked = false;
        }
    }
}
