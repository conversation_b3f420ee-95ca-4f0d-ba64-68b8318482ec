using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Configuration;
using INS.Ctrl;
using INS.Ctrl.Endor;
using INS.Business.SearchForm;

namespace INS.Business
{
    public partial class RIEndorsement2 : Form
    {
        public string Class = "";
        public string Classfctlid = "";
        public string SubClass = "";
        public string policyNo = "";
        public string fctlid = "";
        public string fctlid_v = "";
        public string fctlid_p = "";
        public string fconfirm = "";
        public string com = "";
        public string disc = "";
        public string fbus = "";
        DBConnect operate = new DBConnect();
        public Boolean Addflag = false;
        public Boolean updateflag = false;
        public int Policylistrow = 0;
        public string db = InsEnvironment.DataBase.GetDbm();
        public string fendtype="";
        public string fexten = "";

        EndorRIGen Gen = new EndorRIGen();
        EndorExcess Excess = new EndorExcess();
        EndorAttach Attach = new EndorAttach();
        EndorInstall Install = new EndorInstall();
        EndorPrint Print = new EndorPrint();
        EndorRIInsured_CAR Insured = new EndorRIInsured_CAR();
        EndorRIInsured_EEC InsuredB = new EndorRIInsured_EEC();
        EndorRIInsured_CGL InsuredC = new EndorRIInsured_CGL();
        EndorDirectInsured_CPM InsuredD = new EndorDirectInsured_CPM();
        EndorDirectInsured_PMP InsuredE = new EndorDirectInsured_PMP();
        EndorRIInsured_PAR Insured2 = new EndorRIInsured_PAR();

        private DirectGen directGen;
        private RIGen rIGen;
        private EndorRIGen endorRIGen;

        public RIEndorsement2()
        {
            InitializeComponent();
        }

        public RIEndorsement2(DirectGen directGen)
        {
            // TODO: Complete member initialization
            this.directGen = directGen;
            InitializeComponent();
        }

        public RIEndorsement2(RIGen rIGen)
        {
            // TODO: Complete member initialization
            this.rIGen = rIGen;
            InitializeComponent();
        }

        public RIEndorsement2(EndorRIGen endorRIGen)
        {
            // TODO: Complete member initialization
            this.endorRIGen = endorRIGen;
            InitializeComponent();
        }

       public void FillData(string order)
        {
            String Sql = "select fctlid_p, fvseq, fctlid, fcom, ftd, fsclass, fpolno as Policy#,case when fvseq=0 then null else fvseq end as V#, case when cast(fvarno as int)=0 then null else  cast(fvarno as int) end as VO, fendtno as [Endt No#],fissdate as [Issue Date],fefffr as [Effect Fr],feffto as [Effect To], " +
                        "fprdr as Producer#,finsd as Insured, "+
                        "case when fconfirm='1' then 'Pending' else  "+
                        "case when fconfirm='2' then 'Hold' else "+
                        "case when fconfirm='3' then 'Confirmed' else 'Cancelled' end end end as Status "+
                        "from polh where fbus='" + fbus + "' and ftype = 'E' AND fclass ='" + Class + "' AND fctlid_p ='" + fctlid_p + "' order by case when fconfirm=1 then '1' when fconfirm='2' then '2' when fconfirm='4' then'4' else '3' END,V#,fendtno desc";
            Policylist.DataSource = DBHelper.GetDataSet(Sql);
            this.Policylist.Columns[0].Visible = false;
            this.Policylist.Columns[1].Visible = false;
            this.Policylist.Columns[2].Visible = false;
            this.Policylist.Columns[3].Visible = false;
            this.Policylist.Columns[4].Visible = false;
            this.Policylist.Columns[5].Visible = false;
            Policylist.CellFormatting +=
new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.Policylist_CellFormatting);

            if (Policylist.Rows.Count == 0)
            {
                Sql = "select fctlid_p, fvseq, fctlid, fcom, ftd, fsclass, fpolno as Policy#,case when fvseq=0 then null else fvseq end as V#, case when cast(fvarno as int)=0 then null else  cast(fvarno as int) end as VO, fendtno as [Endt No#],fissdate as [Issue Date],fefffr as [Effect Fr],feffto as [Effect To], " +
                        "fprdr as Producer#,finsd as Insured, " +
                        "case when fconfirm='1' then 'Pending' else  " +
                        "case when fconfirm='2' then 'Hold' else " +
                        "case when fconfirm='3' then 'Confirmed' else 'Cancelled' end end end as Status " +
                        "from polh where fbus='" + fbus + "' AND fclass ='" + Class + "' AND fctlid_p ='" + fctlid_p + "' order by case when fconfirm=1 then '1' when fconfirm='2' then '2' when fconfirm='4' then'4' else '3' END,V#,fendtno desc";
                DataTable dt = DBHelper.GetDataSet(Sql);
                if (dt != null && dt.Rows.Count > 0) { 
                    fctlid = dt.Rows[0]["fctlid"].ToString().Trim();
                    policyNo = dt.Rows[0]["Policy#"].ToString().Trim();
                    fconfirm = dt.Rows[0]["Status"].ToString().Trim();
                    com = dt.Rows[0]["fcom"].ToString().Trim();
                    disc = dt.Rows[0]["ftd"].ToString().Trim();
                    SubClass = dt.Rows[0]["fsclass"].ToString().Trim();
                }
                panel10.Controls.Clear();
                DirectButton temp_ctrlbutton = new DirectButton();
                temp_ctrlbutton.fconfirm = fconfirm;
                temp_ctrlbutton.buttonload("Endor");
                temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
                temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
                temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
                temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
                temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
                temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
                temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
                panel10.Controls.Add(temp_ctrlbutton);
            }
        }

       private void Policylist_CellFormatting(object sender,
System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
       {
           if (Policylist.Columns[e.ColumnIndex].Name.Equals("Policy#"))
           {
               Policylist.Columns[e.ColumnIndex].Width = 90;
           }
           if (Policylist.Columns[e.ColumnIndex].Name.Equals("Status"))
           {
               Policylist.Columns[e.ColumnIndex].Width = 60;
           }
           if (Policylist.Columns[e.ColumnIndex].Name.Equals("Endt No#"))
           {
               Policylist.Columns[e.ColumnIndex].Width = 90;
           }
           if (Policylist.Columns[e.ColumnIndex].Name.Equals("V#") || Policylist.Columns[e.ColumnIndex].Name.Equals("VO"))
           {
               Policylist.Columns[e.ColumnIndex].Width = 30;
           }
           if (Policylist.Columns[e.ColumnIndex].Name.Equals("Insured"))
           {
               Policylist.Columns[e.ColumnIndex].Width = 190;
           }
       }

       private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
       {
           if (Addflag == true || updateflag == true)
           {
               if (e.TabPage == tabPage1)
               { e.Cancel = true; }
               if (e.TabPage != tabPage1)
               { e.Cancel = false; }
           }
           if (Addflag != true && updateflag != true)
           {
               if (Policylist.Rows.Count == 0)
               {
                   if (e.TabPage != tabPage1)
                       e.Cancel = true;
               }
           }
       }

        private void tabControl1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            if (Policylist.Rows.Count > 0)
            {
                int counter;
                counter = Policylist.CurrentCell.RowIndex;
                if (tabControl1.SelectedIndex == 1)
                {
                    counter = Policylist.CurrentCell.RowIndex;
                    if (Policylist.Rows[counter].Cells["fctlid"].Value != null)
                    {
                        if (Policylist.Rows[counter].Cells["fctlid"].Value.ToString().Length != 0)
                        {
                            fctlid = Policylist.Rows[counter].Cells["fctlid"].Value.ToString();
                            if (Addflag == false && updateflag == false)
                            {
                                tabPage2reload(fctlid);
                            }
                            else
                            {
                                if (Class == "CAR" || Class == "CSB")
                                {
                                    Insured.Caculate();
                                    Gen.GenTPL = Insured.InsureTPL;
                                    Gen.GenTPLupd = Insured.InsureTPLupd;
                                    Gen.GenOTPL = Insured.InsureOTPL;
                                    Gen.GenOTPLupd = Insured.InsureOTPLupd;
                                    Gen.GenTotComm = Insured.InsureTotComm;
                                    Gen.GenTotDisc = Insured.InsureTotDisc;
                                    Gen.GenSum = Insured.InsureSum;
                                    Gen.GenSumUpd = Insured.InsureSumUpd;
                                    Gen.GenOSum = Insured.InsureOSum;
                                    Gen.GenOSumUpd = Insured.InsureOSumUpd;
                                    Gen.GenOGP = Insured.InsureOGP;
                                    Gen.GenGP = Insured.InsureGP;
                                    Gen.GenNP = Insured.InsureNP;
                                    if (Class == "CAR")
                                    {
                                        Gen.Genflorggp = Insured.Insureflorggpm;
                                        Gen.Genflgpm = Insured.Insureflgpm;
                                        Gen.Genfltdamt = Insured.Insurefltdamt;
                                        Gen.Genflcomamt = Insured.Insureflcomamt;
                                        Gen.Genflnpm = Insured.Insureflnpm;
                                    }
                                }
                                if (Class == "MYP" || Class == "PAR")
                                {
                                    Insured2.Caculate();
                                    Gen.GenTPL = Insured2.InsureTPL;
                                    Gen.GenTotComm = Insured2.InsureTotComm;
                                    Gen.GenTotDisc = Insured2.InsureTotDisc;
                                    Gen.GenSum = Insured2.InsureSum;
                                    Gen.GenGP = Insured2.InsureGP;
                                    Gen.GenSumUpd = Insured2.InsureSumUpd;
                                    Gen.GenOSum = Insured2.InsureOSum;
                                    Gen.GenOSumUpd = Insured2.InsureOSumUpd;
                                    Gen.GenOGP = Insured2.InsureOGP;
                                    Gen.GenNP = Insured2.InsureNP;
                                }
                                if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII")
                                {
                                    InsuredC.Caculate();
                                    Gen.GenTPL = InsuredC.InsureTPL;
                                    Gen.GenTPLupd = InsuredC.InsureTPLupd;
                                    Gen.GenOTPL = InsuredC.InsureOTPL;
                                    Gen.GenOTPLupd = InsuredC.InsureOTPLupd;
                                    Gen.GenTotComm = InsuredC.InsureTotComm;
                                    Gen.GenTotDisc = InsuredC.InsureTotDisc;
                                    Gen.GenSum = InsuredC.InsureSum;
                                    Gen.GenGP = InsuredC.InsureGP;
                                    Gen.GenOGP = InsuredC.InsureOGP;
                                    Gen.GenNP = InsuredC.InsureNP;
                                }
                                if (Class == "CPM")
                                {
                                    InsuredD.u_calculation();
                                    Gen.GenTotComm = InsuredD.InsureTotComm;
                                    Gen.GenTotDisc = InsuredD.InsureTotDisc;
                                    Gen.GenSum = InsuredD.InsureSum;
                                    Gen.GenGP = InsuredD.InsureGP;
                                    Gen.GenNP = InsuredD.InsureNP;
                                }
                                if (Class == "PMP" || Class == "CMP")
                                {
                                    InsuredE.u_calculation();
                                    Gen.GenTotComm = InsuredE.InsureTotComm;
                                    Gen.GenTotDisc = InsuredE.InsureTotDisc;
                                    Gen.GenSum = InsuredE.InsureSum;
                                    Gen.GenGP = InsuredE.InsureGP;
                                    Gen.GenNP = InsuredE.InsureNP;
                                }
                                if (Class == "EEC")
                                {
                                    InsuredB.Caculate();
                                    Gen.GenTotComm = InsuredB.InsureTotComm;
                                    Gen.GenTotDisc = InsuredB.InsureTotDisc;
                                    Gen.GenOSum = InsuredB.InsureSum;
                                    Gen.GenGP = InsuredB.InsureGP;
                                    Gen.GenOGP = InsuredB.InsureOGP;
                                    Gen.GenNP = InsuredB.InsureNP;
                                }
                            }
                        }
                    }
                }
                if (tabControl1.SelectedIndex == 2)
                {
                    counter = Policylist.CurrentCell.RowIndex;
                    if (Policylist.Rows[counter].Cells["fctlid"].Value != null)
                    {
                        if (Policylist.Rows[counter].Cells["fctlid"].Value.ToString().Length != 0)
                        {
                            fctlid = Policylist.Rows[counter].Cells["fctlid"].Value.ToString();
                        }
                    }
                    if (Addflag == false && updateflag == false)
                    {
                        tabPage3reload(fctlid, com, disc);
                    }
                    else
                    {
                        if (Class == "CAR" || Class == "CSB")
                        {
                            if (Class == "CSB")
                            {
                                Insured.InsureSum2 = Gen.GenSum;
                                Insured.InsureSumUpd2 = Gen.GenSumUpd;
                            }
                            Insured.Caculate();
                        }
                        if (Class == "MYP" || Class == "PAR")
                        {
                            Insured2.Caculate();
                        }
                        if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII")
                        {
                            InsuredC.Caculate();
                        }
                        if (Class == "CPM")
                        {
                            InsuredD.u_calculation();
                        }
                        if (Class == "PMP" || Class == "CMP")
                        {
                            InsuredE.u_calculation();
                        }
                        if (Class == "EEC")
                        {
                            if (Gen.GenSubClass == "CONTRACTOR")
                            {
                                InsuredB.InsureSum2 = Gen.GenOSum;
                                InsuredB.InsureSumUpd = Gen.GenOSumUpd;
                            }
                            if (InsuredB.fsclass != Gen.GenSubClass)
                            {
                                panel4.Controls.Clear();
                                InsuredB.fctlid = fctlid;
                                InsuredB.Class = Class;
                                InsuredB.Classfctlid = Classfctlid;
                                InsuredB.fsclass = Gen.GenSubClass;
                                InsuredB.buttoncontroladd(fendtype);
                                panel4.Controls.Add(InsuredB);
                            }
                            InsuredB.Caculate();
                        }
                    }

                }
                if (tabControl1.SelectedIndex == 3)
                {
                    counter = Policylist.CurrentCell.RowIndex;
                    if (Policylist.Rows[counter].Cells["fctlid"].Value != null)
                    {
                        if (Policylist.Rows[counter].Cells["fctlid"].Value.ToString().Length != 0)
                        {
                            fctlid = Policylist.Rows[counter].Cells["fctlid"].Value.ToString();
                            if (Addflag == false && updateflag == false)
                            {
                                tabPage4reload(fctlid);
                            }
                        }
                    }
                }
                if (tabControl1.SelectedIndex == 4)
                {
                    counter = Policylist.CurrentCell.RowIndex;
                    if (Policylist.Rows[counter].Cells["fctlid"].Value != null)
                    {
                        if (Policylist.Rows[counter].Cells["fctlid"].Value.ToString().Length != 0)
                        {
                            fctlid = Policylist.Rows[counter].Cells["fctlid"].Value.ToString();
                            if (Addflag == false && updateflag == false)
                            {
                                tabPage5reload(fctlid);
                            }
                        }
                    }
                }

                if (tabControl1.SelectedIndex == 5)
                {
                    counter = Policylist.CurrentCell.RowIndex;
                    if (Policylist.Rows[counter].Cells["fctlid"].Value != null)
                    {
                        if (Policylist.Rows[counter].Cells["fctlid"].Value.ToString().Length != 0)
                        {
                            fctlid = Policylist.Rows[counter].Cells["fctlid"].Value.ToString();
                        }
                    }
                    if (Addflag == false && updateflag == false)
                    {
                        tabPage6reload(fctlid);
                        Install.InstallTotComm = Gen.GenTotComm;
                        Install.InstallTotDisc = Gen.GenTotDisc;
                        Install.InstallGP = Gen.GenGP;
                        Install.InstallNP = Gen.GenNP;
                        Install.InstallEffrDate = Gen.GenEffrDate;
                        Install.InstallEftoDate = Gen.GenEftoDate;
                        Install.InstallIsDate = Gen.GenIsDate;
                    }
                    else
                    {
                        if (Class == "CAR" || Class == "CSB")
                        {
                            Insured.Caculate();
                            Install.InstallTotComm = Insured.InsureTotComm;
                            Install.InstallTotDisc = Insured.InsureTotDisc;
                            Install.InstallGP = Insured.InsureGP;
                            Install.InstallNP = Insured.InsureNP;
                        }
                        if (Class == "MYP" || Class == "PAR")
                        {
                            Insured2.Caculate();
                            Install.InstallTotComm = Insured2.InsureTotComm;
                            Install.InstallTotDisc = Insured2.InsureTotDisc;
                            Install.InstallGP = Insured2.InsureGP;
                            Install.InstallNP = Insured2.InsureNP;
                        }
                        if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII")
                        {
                            InsuredC.Caculate();
                            Install.InstallTotComm = InsuredC.InsureTotComm;
                            Install.InstallTotDisc = InsuredC.InsureTotDisc;
                            Install.InstallGP = InsuredC.InsureGP;
                            Install.InstallNP = InsuredC.InsureNP;
                        }
                        if (Class == "CPM")
                        {
                            InsuredD.u_calculation();
                            Install.InstallTotComm = InsuredD.InsureTotComm;
                            Install.InstallTotDisc = InsuredD.InsureTotDisc;
                            Install.InstallGP = InsuredD.InsureGP;
                            Install.InstallNP = InsuredD.InsureNP;
                        }
                        if (Class == "PMP" || Class == "CMP")
                        {
                            InsuredE.u_calculation();
                            Install.InstallTotComm = InsuredE.InsureTotComm;
                            Install.InstallTotDisc = InsuredE.InsureTotDisc;
                            Install.InstallGP = InsuredE.InsureGP;
                            Install.InstallNP = InsuredE.InsureNP;
                        }
                        if (Class == "EEC")
                        {
                            InsuredB.Caculate();
                            Install.InstallTotComm = InsuredB.InsureTotComm;
                            Install.InstallTotDisc = InsuredB.InsureTotDisc;
                            Install.InstallGP = InsuredB.InsureGP;
                            Install.InstallNP = InsuredB.InsureNP;
                        }
                        Install.InstallEffrDate = Gen.GenEffrDate;
                        Install.InstallEftoDate = Gen.GenEftoDate;
                        Install.InstallIsDate = Gen.GenIsDate;
                    }
                }
                if (tabControl1.SelectedIndex == 6)
                {
                    counter = Policylist.CurrentCell.RowIndex;
                    if (Policylist.Rows[counter].Cells["fctlid"].Value != null)
                    {
                        if (Policylist.Rows[counter].Cells["fctlid"].Value.ToString().Length != 0)
                        {
                            fctlid = Policylist.Rows[counter].Cells["fctlid"].Value.ToString();
                            if (Addflag == false && updateflag == false)
                            {
                                tabPage7reload(fctlid);
                            }
                        }
                    }
                }
                if (Addflag == true || updateflag == true)
                {
                    if (tabControl1.SelectedIndex == 1)
                    {
                        if (Class == "CAR" || Class == "CSB")
                        {
                            Insured.Caculate();
                            Gen.GenTPL = Insured.InsureTPL;
                            Gen.GenTPLupd = Insured.InsureTPLupd;
                            Gen.GenOTPL = Insured.InsureOTPL;
                            Gen.GenOTPLupd = Insured.InsureOTPLupd;
                            Gen.GenTotComm = Insured.InsureTotComm;
                            Gen.GenTotDisc = Insured.InsureTotDisc;
                            Gen.GenSumUpd = Insured.InsureSumUpd;
                            Gen.GenSum = Insured.InsureSum;
                            Gen.GenOSum = Insured.InsureOSum;
                            Gen.GenOSumUpd = Insured.InsureOSumUpd;
                            Gen.GenGP = Insured.InsureGP;
                            Gen.GenOGP = Insured.InsureOGP;
                            Gen.GenNP = Insured.InsureNP;
                            Insured.EndtType = Gen.GenEndtType;
                            if (Class == "CAR")
                            {
                                Gen.Genflorggp = Insured.Insureflorggpm;
                                Gen.Genflgpm = Insured.Insureflgpm;
                                Gen.Genfltdamt = Insured.Insurefltdamt;
                                Gen.Genflcomamt = Insured.Insureflcomamt;
                                Gen.Genflnpm = Insured.Insureflnpm;
                            }
                        }
                        if (Class == "MYP" || Class == "PAR")
                        {
                            Insured2.Caculate();
                            Gen.GenTPL = Insured2.InsureTPL;
                            Gen.GenTotComm = Insured2.InsureTotComm;
                            Gen.GenTotDisc = Insured2.InsureTotDisc;
                            Gen.GenSum = Insured2.InsureSum;
                            Gen.GenSumUpd = Insured2.InsureSumUpd;
                            Gen.GenOSum = Insured2.InsureOSum;
                            Gen.GenOSumUpd = Insured2.InsureOSumUpd;
                            Gen.GenOGP = Insured2.InsureOGP;
                            Gen.GenGP = Insured2.InsureGP;
                            Gen.GenNP = Insured2.InsureNP;
                            Insured2.EndtType = Gen.GenEndtType;
                        }
                        if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII")
                        {
                            InsuredC.Caculate();
                            Gen.GenTPL = InsuredC.InsureTPL;
                            Gen.GenTPLupd = InsuredC.InsureTPLupd;
                            Gen.GenOTPL = InsuredC.InsureOTPL;
                            Gen.GenOTPLupd = InsuredC.InsureOTPLupd;
                            Gen.GenTotComm = InsuredC.InsureTotComm;
                            Gen.GenTotDisc = InsuredC.InsureTotDisc;
                            Gen.GenSum = InsuredC.InsureSum;
                            Gen.GenGP = InsuredC.InsureGP;
                            Gen.GenOGP = InsuredC.InsureOGP;
                            Gen.GenNP = InsuredC.InsureNP;
                            InsuredC.EndtType = Gen.GenEndtType;
                        }
                        if (Class == "CPM")
                        {
                            InsuredD.u_calculation();
                            Gen.GenTotComm = InsuredD.InsureTotComm;
                            Gen.GenTotDisc = InsuredD.InsureTotDisc;
                            Gen.GenSum = InsuredD.InsureSum;
                            Gen.GenGP = InsuredD.InsureGP;
                            Gen.GenNP = InsuredD.InsureNP;
                            InsuredD.EndtType = Gen.GenEndtType;
                        }
                        if (Class == "PMP" || Class == "CMP")
                        {
                            InsuredE.u_calculation();
                            Gen.GenTotComm = InsuredE.InsureTotComm;
                            Gen.GenTotDisc = InsuredE.InsureTotDisc;
                            Gen.GenSum = InsuredE.InsureSum;
                            Gen.GenGP = InsuredE.InsureGP;
                            Gen.GenNP = InsuredE.InsureNP;
                            InsuredE.EndtType = Gen.GenEndtType;
                        }
                        if (Class == "EEC")
                        {
                            InsuredB.Caculate();
                            Gen.GenTotComm = InsuredB.InsureTotComm;
                            Gen.GenTotDisc = InsuredB.InsureTotDisc;
                            Gen.GenOSum = InsuredB.InsureSum;
                            Gen.GenGP = InsuredB.InsureGP;
                            Gen.GenOGP = InsuredB.InsureOGP;
                            Gen.GenNP = InsuredB.InsureNP;
                            InsuredB.fsclass = Gen.GenSubClass;
                            InsuredB.EndtType = Gen.GenEndtType;
                        }
                    }
                    if (tabControl1.SelectedIndex == 2)
                    {
                        if (Class == "CAR" || Class == "CSB")
                        {
                            if (Class == "CSB")
                            {
                                Insured.InsureSum2 = Gen.GenSum;
                                Insured.InsureSumUpd2 = Gen.GenSumUpd;
                            }
                            Insured.Caculate();
                        }
                        if (Class == "MYP" || Class == "PAR")
                        {
                            Insured2.Caculate();
                        }
                        if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII")
                        {
                            InsuredC.Caculate();
                        }
                        if (Class == "CPM")
                        {
                            InsuredD.u_calculation();
                        }
                        if (Class == "PMP" || Class == "CMP")
                        {
                            InsuredE.u_calculation();
                        }
                        if (Class == "EEC")
                        {
                            if (Gen.GenSubClass == "CONTRACTOR")
                            {
                                InsuredB.InsureSum2 = Gen.GenOSum;
                                InsuredB.InsureSumUpd = Gen.GenOSumUpd;
                            }
                            if (InsuredB.fsclass != Gen.GenSubClass)
                            {
                                panel4.Controls.Clear();
                                InsuredB.fctlid = fctlid;
                                InsuredB.Class = Class;
                                InsuredB.Classfctlid = Classfctlid;
                                InsuredB.fsclass = Gen.GenSubClass;
                                InsuredB.buttoncontroladd(fendtype);
                                panel4.Controls.Add(InsuredB);
                            }
                            InsuredB.Caculate();
                        }
                    }
                    if (tabControl1.SelectedIndex == 5)
                    {
                        if (Class == "CAR" || Class == "CSB")
                        {
                            Insured.Caculate();
                            Install.InstallTotComm = Insured.InsureTotComm;
                            Install.InstallTotDisc = Insured.InsureTotDisc;
                            Install.InstallGP = Insured.InsureGP;
                            Install.InstallNP = Insured.InsureNP;
                        }
                        if (Class == "MYP" || Class == "PAR")
                        {
                            Insured2.Caculate();
                            Install.InstallTotComm = Insured2.InsureTotComm;
                            Install.InstallTotDisc = Insured2.InsureTotDisc;
                            Install.InstallGP = Insured2.InsureGP;
                            Install.InstallNP = Insured2.InsureNP;
                        }
                        if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII")
                        {
                            InsuredC.Caculate();
                            Install.InstallTotComm = InsuredC.InsureTotComm;
                            Install.InstallTotDisc = InsuredC.InsureTotDisc;
                            Install.InstallGP = InsuredC.InsureGP;
                            Install.InstallNP = InsuredC.InsureNP;
                        }
                        if (Class == "CPM")
                        {
                            InsuredD.u_calculation();
                            Install.InstallTotComm = InsuredD.InsureTotComm;
                            Install.InstallTotDisc = InsuredD.InsureTotDisc;
                            Install.InstallGP = InsuredD.InsureGP;
                            Install.InstallNP = InsuredD.InsureNP;
                        }
                        if (Class == "PMP" || Class == "CMP")
                        {
                            InsuredE.u_calculation();
                            Install.InstallTotComm = InsuredE.InsureTotComm;
                            Install.InstallTotDisc = InsuredE.InsureTotDisc;
                            Install.InstallGP = InsuredE.InsureGP;
                            Install.InstallNP = InsuredE.InsureNP;
                        }
                        if (Class == "EEC")
                        {
                            InsuredB.Caculate();
                            Install.InstallTotComm = InsuredB.InsureTotComm;
                            Install.InstallTotDisc = InsuredB.InsureTotDisc;
                            Install.InstallGP = InsuredB.InsureGP;
                            Install.InstallNP = InsuredB.InsureNP;
                        }
                        Install.InstallEffrDate = Gen.GenEffrDate;
                        Install.InstallEftoDate = Gen.GenEftoDate;
                        Install.InstallIsDate = Gen.GenIsDate;
                    }
                }
            }
            else
            {
                if (Addflag == true || updateflag == true) {
                    if (tabControl1.SelectedIndex == 1) {
                        if (Class == "CAR" || Class == "CSB")
                        {
                            Insured.Caculate();
                            Gen.GenTPL = Insured.InsureTPL;
                            Gen.GenTPLupd = Insured.InsureTPLupd;
                            Gen.GenOTPL = Insured.InsureOTPL;
                            Gen.GenOTPLupd = Insured.InsureOTPLupd;
                            Gen.GenTotComm = Insured.InsureTotComm;
                            Gen.GenTotDisc = Insured.InsureTotDisc;
                            Gen.GenSumUpd = Insured.InsureSumUpd;
                            Gen.GenSum = Insured.InsureSum;
                            Gen.GenOSum = Insured.InsureOSum;
                            Gen.GenOSumUpd = Insured.InsureOSumUpd;
                            Gen.GenGP = Insured.InsureGP;
                            Gen.GenOGP = Insured.InsureOGP;
                            Gen.GenNP = Insured.InsureNP;
                            Insured.EndtType = Gen.GenEndtType;
                        }
                        if (Class == "MYP" || Class == "PAR")
                        {
                            Insured2.Caculate();
                            Gen.GenTPL = Insured2.InsureTPL;
                            Gen.GenTotComm = Insured2.InsureTotComm;
                            Gen.GenTotDisc = Insured2.InsureTotDisc;
                            Gen.GenSum = Insured2.InsureSum;
                            Gen.GenSumUpd = Insured2.InsureSumUpd;
                            Gen.GenOSum = Insured2.InsureOSum;
                            Gen.GenOSumUpd = Insured2.InsureOSumUpd;
                            Gen.GenOGP = Insured2.InsureOGP;
                            Gen.GenGP = Insured2.InsureGP;
                            Gen.GenNP = Insured2.InsureNP;
                            Insured2.EndtType = Gen.GenEndtType;
                        }
                        if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII")
                        {
                            InsuredC.Caculate();
                            Gen.GenTPL = InsuredC.InsureTPL;
                            Gen.GenOTPL = InsuredC.InsureOTPL;
                            Gen.GenTotComm = InsuredC.InsureTotComm;
                            Gen.GenTotDisc = InsuredC.InsureTotDisc;
                            Gen.GenSum = InsuredC.InsureSum;
                            Gen.GenGP = InsuredC.InsureGP;
                            Gen.GenOGP = InsuredC.InsureOGP;
                            Gen.GenNP = InsuredC.InsureNP;
                            InsuredC.EndtType = Gen.GenEndtType;
                        }
                        if (Class == "CPM")
                        {
                            InsuredD.u_calculation();
                            Gen.GenTotComm = InsuredD.InsureTotComm;
                            Gen.GenTotDisc = InsuredD.InsureTotDisc;
                            Gen.GenSum = InsuredD.InsureSum;
                            Gen.GenGP = InsuredD.InsureGP;
                            Gen.GenNP = InsuredD.InsureNP;
                            InsuredD.EndtType = Gen.GenEndtType;
                        }
                        if (Class == "PMP" || Class == "CMP")
                        {
                            InsuredE.u_calculation();
                            Gen.GenTotComm = InsuredE.InsureTotComm;
                            Gen.GenTotDisc = InsuredE.InsureTotDisc;
                            Gen.GenSum = InsuredE.InsureSum;
                            Gen.GenGP = InsuredE.InsureGP;
                            Gen.GenNP = InsuredE.InsureNP;
                            InsuredE.EndtType = Gen.GenEndtType;
                        }
                        if (Class == "EEC")
                        {
                            InsuredB.Caculate();
                            Gen.GenTotComm = InsuredB.InsureTotComm;
                            Gen.GenTotDisc = InsuredB.InsureTotDisc;
                            Gen.GenOSum = InsuredB.InsureSum;
                            Gen.GenGP = InsuredB.InsureGP;
                            Gen.GenOGP = InsuredB.InsureOGP;
                            Gen.GenNP = InsuredB.InsureNP;
                            InsuredB.EndtType = Gen.GenEndtType;
                            InsuredB.fsclass = SubClass;
                        }
                    }
                    if (tabControl1.SelectedIndex == 2)
                    {
                        if (Class == "CAR" || Class == "CSB")
                        {
                            if (Class == "CSB")
                            {
                                Insured.InsureSum2 = Gen.GenSum;
                                Insured.InsureSumUpd2 = Gen.GenSumUpd;
                            }
                            Insured.Caculate();
                        }
                        if (Class == "MYP" || Class == "PAR")
                        {
                            Insured2.Caculate();
                        }
                        if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII")
                        {
                            InsuredC.Caculate();
                        }
                        if (Class == "CPM")
                        {
                            InsuredD.u_calculation();
                        }
                        if (Class == "PMP" || Class == "CMP")
                        {
                            InsuredE.u_calculation();
                        }
                        if (Class == "EEC")
                        {
                            if (Gen.GenSubClass == "CONTRACTOR")
                            {
                                InsuredB.InsureSum2 = Gen.GenOSum;
                                InsuredB.InsureSumUpd = Gen.GenOSumUpd;
                            }
                            if (InsuredB.fsclass != Gen.GenSubClass)
                            {
                                panel4.Controls.Clear();
                                InsuredB.fctlid = fctlid;
                                InsuredB.Class = Class;
                                InsuredB.Classfctlid = Classfctlid;
                                InsuredB.fsclass = Gen.GenSubClass;
                                InsuredB.buttoncontroladd(fendtype);
                                panel4.Controls.Add(InsuredB);
                            }
                            InsuredB.Caculate();
                        }
                    }
                    if (tabControl1.SelectedIndex == 5)
                    {
                        if (Class == "CAR" || Class == "CSB")
                        {
                            Insured.Caculate();
                            Install.InstallTotComm = Insured.InsureTotComm;
                            Install.InstallTotDisc = Insured.InsureTotDisc;
                            Install.InstallGP = Insured.InsureGP;
                            Install.InstallNP = Insured.InsureNP;
                        }
                        if (Class == "MYP" || Class == "PAR")
                        {
                            Insured2.Caculate();
                            Install.InstallTotComm = Insured2.InsureTotComm;
                            Install.InstallTotDisc = Insured2.InsureTotDisc;
                            Install.InstallGP = Insured2.InsureGP;
                            Install.InstallNP = Insured2.InsureNP;
                        }
                        if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII")
                        {
                            InsuredC.Caculate();
                            Install.InstallTotComm = InsuredC.InsureTotComm;
                            Install.InstallTotDisc = InsuredC.InsureTotDisc;
                            Install.InstallGP = InsuredC.InsureGP;
                            Install.InstallNP = InsuredC.InsureNP;
                        }
                        if (Class == "CPM")
                        {
                            InsuredD.u_calculation();
                            Install.InstallTotComm = InsuredD.InsureTotComm;
                            Install.InstallTotDisc = InsuredD.InsureTotDisc;
                            Install.InstallGP = InsuredD.InsureGP;
                            Install.InstallNP = InsuredD.InsureNP;
                        }
                        if (Class == "PMP" || Class == "CMP")
                        {
                            InsuredE.u_calculation();
                            Install.InstallTotComm = InsuredE.InsureTotComm;
                            Install.InstallTotDisc = InsuredE.InsureTotDisc;
                            Install.InstallGP = InsuredE.InsureGP;
                            Install.InstallNP = InsuredE.InsureNP;
                        }
                        if (Class == "EEC")
                        {
                            InsuredB.Caculate();
                            Install.InstallTotComm = InsuredB.InsureTotComm;
                            Install.InstallTotDisc = InsuredB.InsureTotDisc;
                            Install.InstallGP = InsuredB.InsureGP;
                            Install.InstallNP = InsuredB.InsureNP;
                        }
                        Install.InstallEffrDate = Gen.GenEffrDate;
                        Install.InstallEftoDate = Gen.GenEftoDate;
                        Install.InstallIsDate = Gen.GenIsDate;
                    }
                }
            }
        }

        void tabpage1reload()
        {
            panel10.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttonload("Endor");
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            panel10.Controls.Add(temp_ctrlbutton);

            if (Addflag != true && updateflag != true)
            {
                if (Policylist.Rows.Count == 0)
                {
                    tabControl1.SelectedTab = tabPage1;
                }
            }
            tabPage2reload(fctlid);
            tabPage3reload(fctlid,com,disc);
            tabPage4reload(fctlid);
            tabPage5reload(fctlid);
            tabPage6reload(fctlid);
            tabPage7reload(fctlid);
        }

        void tabPage2reload(string fctlid)
        {
            panel22.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttonload("Endor");
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            panel22.Controls.Add(temp_ctrlbutton);

            panel1.Controls.Clear();
            Gen.fctlid = fctlid;
            Gen.Class = Class;
            Gen.Classfctlid = Classfctlid;
            Gen.ftype = "E";
            Gen.fbus = fbus;
            Gen.buttoncontrolback();
            panel1.Controls.Add(Gen);
        }

        void tabPage3reload(string fctlid,string com, string disc) {
            panel24.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttonload("Endor");
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            panel24.Controls.Add(temp_ctrlbutton);

            panel4.Controls.Clear();
            if (Class == "CAR" || Class == "CSB")
            {
                Insured.fctlid = fctlid;
                Insured.Class = Class;
                Insured.Classfctlid = Classfctlid;
                Insured.ftype = "E";
                Insured.fbus = fbus;
                Insured.EndtType = Gen.GenEndtType;
                Insured.buttoncontrolback();
                panel4.Controls.Add(Insured);
            }
            if (Class == "MYP" || Class == "PAR")
            {
                Insured2.fctlid = fctlid;
                Insured2.Class = Class;
                Insured2.Classfctlid = Classfctlid;
                Insured2.ftype = "E";
                Insured2.fbus = fbus;
                Insured2.EndtType = Gen.GenEndtType;
                Insured2.buttoncontrolback();
                panel4.Controls.Add(Insured2);
            }
            if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII")
            {
                InsuredC.fctlid = fctlid;
                InsuredC.Class = Class;
                InsuredC.Classfctlid = Classfctlid;
                InsuredC.ftype = "E";
                InsuredC.fbus = fbus;
                InsuredC.EndtType = Gen.GenEndtType;
                InsuredC.buttoncontrolback();
                panel4.Controls.Add(InsuredC);
            }
            if (Class == "CPM")
            {
                InsuredD.fctlid = fctlid;
                InsuredD.Class = Class;
                InsuredD.Classfctlid = Classfctlid;
                InsuredD.ftype = "E";
                InsuredD.fbus = fbus;
                InsuredD.EndtType = Gen.GenEndtType;
                InsuredD.buttoncontrolback();
                panel4.Controls.Add(InsuredD);
            }
            if (Class == "PMP" || Class == "CMP")
            {
                InsuredE.fctlid = fctlid;
                InsuredE.Class = Class;
                InsuredE.Classfctlid = Classfctlid;
                InsuredE.EndtType = Gen.GenEndtType;
                InsuredE.ftype = "E";
                InsuredE.fbus = fbus;
                InsuredE.buttoncontrolback();
                panel4.Controls.Add(InsuredE); 
            }
              if (Class == "EEC")
              {
                  InsuredB.fctlid = fctlid;
                  InsuredB.Class = Class;
                  InsuredB.Classfctlid = Classfctlid;
                  InsuredB.ftype = "E";
                  InsuredB.fbus = fbus;
                  InsuredB.EndtType = Gen.GenEndtType;
                  InsuredB.fsclass = Gen.GenSubClass;
                  InsuredB.buttoncontrolback();
                  panel4.Controls.Add(InsuredB);
              }
        }

        void tabPage4reload(string fctlid)
        {
            panel13.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttonload("Endor");
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            panel13.Controls.Add(temp_ctrlbutton);

            panel11.Controls.Clear();
            Excess.fctlid = fctlid;
            if (Class == "CAR" || Class == "PMP" || Class == "CMP")
            {
                Excess.panel12.Visible = true;
            }
            Excess.Class = Class;
            Excess.Classfctlid = Classfctlid;
            Excess.ftype = "E";
            Excess.fbus = fbus;
            Excess.buttoncontrolback();
            panel11.Controls.Add(Excess);
        }

        void tabPage5reload(string fctlid)
        {
            panel26.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttonload("Endor");
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            panel26.Controls.Add(temp_ctrlbutton);

            panel12.Controls.Clear();
            Attach.fctlid = fctlid;
            Attach.Class = Class;
            Attach.Classfctlid = Classfctlid;
            Attach.ftype = "E";
            Attach.fbus = fbus;
            Attach.buttoncontrolback();
            panel12.Controls.Add(Attach);
        }

        void tabPage6reload(string fctlid)
        {
            panel27.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttonload("Endor");
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            panel27.Controls.Add(temp_ctrlbutton);

            panel2.Controls.Clear();
            Install.fctlid = fctlid;
            Install.Class = Class;
            Install.Classfctlid = Classfctlid;
            Install.ftype = "E";
            Install.fbus = fbus;
            Install.InstallTotComm = Gen.GenTotComm;
            Install.InstallTotDisc = Gen.GenTotDisc;
            Install.InstallGP = Gen.GenGP;
            Install.InstallNP = Gen.GenNP;
            Install.InstallEffrDate = Gen.GenEffrDate;
            Install.InstallEftoDate = Gen.GenEftoDate;
            Install.InstallIsDate = Gen.GenIsDate;
            Install.buttoncontrolback();
            panel2.Controls.Add(Install);
            
        }

        void tabPage7reload(string fctlid)
        {
            panel5.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttonload("Endor");
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            panel5.Controls.Add(temp_ctrlbutton);

            panel3.Controls.Clear();
            Print.fctlid = fctlid;
            Print.Class = Class;
            Print.buttoncontrolback();
            panel3.Controls.Add(Print);

        }

        private void Policylist_SelectionChanged(object sender, EventArgs e) {
            if (Policylist.CurrentCell != null)
            {
                Policylistrow = Policylist.CurrentCell.RowIndex;
            }
            else { Policylist.CurrentCell = Policylist.Rows[0].Cells["Policy#"]; }
            
            if (Policylist.Rows[Policylistrow].Cells["Policy#"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["Policy#"].Value.ToString().Length != 0)
                { 
                    policyNo = Policylist.Rows[Policylistrow].Cells["Policy#"].Value.ToString(); 
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["fctlid"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["fctlid"].Value.ToString().Length != 0)
                {
                    fctlid = Policylist.Rows[Policylistrow].Cells["fctlid"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["Status"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["Status"].Value.ToString().Length != 0)
                {
                    fconfirm = Policylist.Rows[Policylistrow].Cells["Status"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["fcom"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["fcom"].Value.ToString().Length != 0)
                {
                    com = Policylist.Rows[Policylistrow].Cells["fcom"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["ftd"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["ftd"].Value.ToString().Length != 0)
                {
                    disc = Policylist.Rows[Policylistrow].Cells["ftd"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["fsclass"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["fsclass"].Value.ToString().Length != 0)
                {
                    SubClass = Policylist.Rows[Policylistrow].Cells["fsclass"].Value.ToString();
                }
            }
            tabpage1reload();
        }


        void exit() { this.Close();}
        void print() { }
        void confirm() { }

        void del() {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the Endorsement All information?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string deldatestr =
                    "delete from polh where fctlid='" + fctlid + "'";
                string deldatestr1 = "";
                if (Class == "CAR" || Class == "CSB" || Class == "MYP" || Class == "PAR")
                {
                    deldatestr1 = "delete from oinsint where fctlid_1='" + fctlid + "'";
                }
                if (Class == "EEC")
                {
                    deldatestr1 = "delete from oinsint_b where fctlid_1='" + fctlid + "'";
                }
                if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII" || Class == "GLF")
                {
                    deldatestr1 = "delete from oinsint_c where fctlid_1='" + fctlid + "'";
                }
                if (Class == "CPM")
                {
                    deldatestr1 = "delete from oinsint_d where fctlid_1='" + fctlid + "'";
                }
                if (Class == "PMP" || Class == "CMP")
                {
                    deldatestr1 = "delete from oinsint_e where fctlid_1='" + fctlid + "'";
                }
                string deldatestr2 =
                    "delete from oinsex where fctlid_1='" + fctlid + "'";
                string deldatestr3 =
                    "delete from oinsatt where fctlid_1='" + fctlid + "'";
                string deldatestr4 =
                     "delete from oinvdet where fctlid_1='" + fctlid + "'";
                string deldatestr5 = "", deldatestr6 = "";
                if (Class == "CGL" || Class == "FGP" || Class == "GLF" || Class == "MYP" || Class == "PAR")
                {
                    deldatestr5 = "delete from oinsloc where fctlid_1 ='" + fctlid + "'";
                }
                if (Class == "FGP")
                {
                    deldatestr6 = "delete from oinsper where fctlid_1 ='" + fctlid + "'";
                }
                string deldatestr7 = "delete from oremark where fctlid_1='" + fctlid + "'";
                string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();

                    SqlCommand command = connection.CreateCommand();
                    SqlTransaction transaction;

                    // Start a local transaction.
                    transaction = connection.BeginTransaction("SampleTransaction");

                    // Must assign both transaction object and connection 
                    // to Command object for a pending local transaction
                    command.Connection = connection;
                    command.Transaction = transaction;

                    try
                    {
                        command.CommandText = deldatestr;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr1;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr2;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr3;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr4;
                        command.ExecuteNonQuery();
                        if (deldatestr5 != "")
                        {
                            command.CommandText = deldatestr5;
                            command.ExecuteNonQuery();
                        }
                        if (deldatestr6 != "")
                        {
                            command.CommandText = deldatestr6;
                            command.ExecuteNonQuery();
                        }
                        command.CommandText = deldatestr7;
                        command.ExecuteNonQuery();
                        transaction.Commit();
                        MessageBox.Show("Have Been Deleted", "Warning",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        FillData("");
                        tabControl1.SelectedTab = tabPage1;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("Haven't Been Deleted", "Warning",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);

                        Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                        Console.WriteLine("  Message: {0}", ex.Message);

                        // Attempt to roll back the transaction. 
                        try
                        {
                            transaction.Rollback();
                        }
                        catch (Exception ex2)
                        {
                            // This catch block will handle any errors that may have occurred 
                            // on the server that would cause the rollback to fail, such as 
                            // a closed connection.
                            Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                            Console.WriteLine("  Message: {0}", ex2.Message);
                        }
                    }
                }
            }
        }

       public void buttoncontrolback()
        {
            Addflag = false;
            updateflag = false;
        }

       public void buttoncontroladd()
       {
           addendt tempform = new addendt(this);
           tempform.Class = Class;
           tempform.flag = "EndorRI";
           tempform.SubClass = SubClass;
           tempform.fbus = fbus;
           tempform.Policy = policyNo;
           tempform.Control();
           tempform.ShowDialog();
           if ((fendtype != "" && fexten != "") && (fendtype != "2"))
           {
               Addflag = true;
               DirectButton temp_ctrlbutton = new DirectButton();
               temp_ctrlbutton.buttoncontroladd();
               tabControl1.SelectedTab = tabPage2;
               tabpage2add(fendtype, fexten);
               tabpage3add(fendtype, fexten);
               tabpage4add();
               tabpage5add();
               tabpage6add();
               tabpage7add();
           }
           if (fendtype == "2")
           {
               selvo tempformvo = new selvo(this);
               tempformvo.flag = "EndorRI";
               tempformvo.Class = Class;
               tempformvo.SubClass = SubClass;
               tempformvo.fbus = fbus;
               tempformvo.Policy = policyNo.Trim();
               tempformvo.FillData();
               tempformvo.ShowDialog();
               if ((fendtype != "" && fexten != ""))
               {
                   Addflag = true;
                   DirectButton temp_ctrlbutton = new DirectButton();
                   temp_ctrlbutton.buttoncontroladd();
                   tabControl1.SelectedTab = tabPage2;
                   tabpage2add(fendtype, fexten);
                   tabpage3add(fendtype, fexten);
                   tabpage4add();
                   tabpage5add();
                   tabpage6add();
                   tabpage7add();
               }
           }
       }

       public void buttoncontrolsaveback()
        {
            buttoncontrolback();
        }

       public void buttoncontrolupdate()
        {
            updateflag = true;
            tabControl1.SelectedTab = tabPage2;
            tabpage2update();
            tabpage3update();
            tabpage4update();
            tabpage5update();
            tabpage6update();
            tabpage7update();
        }

        void tabpage2add(string fendtype, string fexten)
       {
           panel22.Controls.Clear();
           DirectButton temp_ctrlbutton = new DirectButton();
           temp_ctrlbutton.fconfirm = fconfirm;
           temp_ctrlbutton.buttoncontroladd();
           temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
           temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
           temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
           temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
           temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
           temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
           temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
           panel22.Controls.Add(temp_ctrlbutton);

           panel1.Controls.Clear();
           if (fendtype == "2") { Gen.fctlid = fctlid_v; } else { Gen.fctlid = fctlid; }
           Gen.Class = Class;
           Gen.Classfctlid = Classfctlid;
           Gen.ftype = "E";
           Gen.GenSubClass = SubClass;
           Gen.fbus = fbus;
           Gen.buttoncontroladd(fendtype, fexten);
           panel1.Controls.Add(Gen);
        }

        void tabpage3add(string fendtype, string fexten)
        {
            panel24.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontroladd();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            panel24.Controls.Add(temp_ctrlbutton);

            panel4.Controls.Clear();
            if (Class == "CAR" || Class == "CSB")
            {
                if (fendtype == "2") { Insured.fctlid = fctlid_v; } else { Insured.fctlid = fctlid; }
                Insured.Class = Class;
                Insured.Classfctlid = Classfctlid;
                Insured.ftype = "E";
                Insured.fbus = fbus;
                Insured.EndtType = Gen.GenEndtType;
                Insured.buttoncontroladd(fendtype);
                panel4.Controls.Add(Insured);
            }
            if (Class == "MYP" || Class == "PAR")
            {
                if (fendtype == "2") { Insured2.fctlid = fctlid_v; } else { Insured2.fctlid = fctlid; }
                Insured2.Class = Class;
                Insured2.Classfctlid = Classfctlid;
                Insured2.ftype = "E";
                Insured2.fbus = fbus;
                Insured2.EndtType = Gen.GenEndtType;
                Insured2.buttoncontroladd();
                panel4.Controls.Add(Insured2);
            }
            if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII")
            {
                if (fendtype == "2") { InsuredC.fctlid = fctlid_v; } else { InsuredC.fctlid = fctlid; }
                InsuredC.Class = Class;
                InsuredC.Classfctlid = Classfctlid;
                InsuredC.ftype = "E";
                InsuredC.fbus = fbus;
                InsuredC.EndtType = Gen.GenEndtType;
                InsuredC.buttoncontroladd(fendtype);
                panel4.Controls.Add(InsuredC);
            }
            if (Class == "CPM")
            {
                if (fendtype == "2") { InsuredD.fctlid = fctlid_v; } else { InsuredD.fctlid = fctlid; }
                InsuredD.Class = Class;
                InsuredD.Classfctlid = Classfctlid;
                InsuredD.ftype = "E";
                InsuredD.fbus = fbus;
                InsuredD.EndtType = Gen.GenEndtType;
                InsuredD.buttoncontroladd();
                panel4.Controls.Add(InsuredD);
            }
            if (Class == "PMP" || Class == "CMP")
            {
                if (fendtype == "2") { InsuredE.fctlid = fctlid_v; } else { InsuredE.fctlid = fctlid; }
                InsuredE.Class = Class;
                InsuredE.Classfctlid = Classfctlid;
                InsuredE.ftype = "E";
                InsuredE.fbus = fbus;
                InsuredE.EndtType = Gen.GenEndtType;
                InsuredE.buttoncontroladd();
                panel4.Controls.Add(InsuredE);
            }
              if (Class == "EEC")
              {
                  if (fendtype == "2") { InsuredB.fctlid = fctlid_v; } else { InsuredB.fctlid = fctlid; }
                  InsuredB.Class = Class;
                  InsuredB.Classfctlid = Classfctlid;
                  InsuredB.fsclass = Gen.GenSubClass;
                  InsuredB.InsureSumUpd = Gen.GenOSumUpd;
                  InsuredB.ftype = "E";
                  InsuredB.EndtType = Gen.GenEndtType;
                  InsuredB.fbus = fbus;
                  InsuredB.buttoncontroladd(fendtype);
                  panel4.Controls.Add(InsuredB);
              }
        }

        void tabpage4add() {
            panel13.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontroladd ();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            panel13.Controls.Add(temp_ctrlbutton);

            panel11.Controls.Clear();
            Excess.fctlid = fctlid;
            if (Class == "CAR" || Class == "PMP" || Class == "CMP")
            {
                Excess.panel12.Visible = true;
            }
            Excess.Class = Class;
            Excess.Classfctlid = Classfctlid;
            Excess.ftype = "E";
            Excess.fbus = fbus;
            Excess.buttoncontroladd();
            panel11.Controls.Add(Excess);
        }

        void tabpage5add() {
            panel26.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontroladd();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            panel26.Controls.Add(temp_ctrlbutton);

            panel12.Controls.Clear();
            Attach.fctlid = fctlid;
            Attach.Class = Class;
            Attach.Classfctlid = Classfctlid;
            Attach.ftype = "E";
            Attach.fbus = fbus;
            Attach.buttoncontroladd();
            panel12.Controls.Add(Attach);
        }

        void tabpage6add()
        {
            panel27.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontroladd();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            panel27.Controls.Add(temp_ctrlbutton);

            panel2.Controls.Clear();
            Install.fctlid = fctlid;
            Install.Class = Class;
            Install.Classfctlid = Classfctlid;
            Install.ftype = "E";
            Install.fbus = fbus;
            Install.buttoncontroladd();
            panel2.Controls.Add(Install);
        }

        void tabpage7add()
        {
            panel5.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontroladd();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            panel5.Controls.Add(temp_ctrlbutton);

            panel3.Controls.Clear();
            Print.fctlid = fctlid;
            Print.Class = Class;
            Print.buttoncontroladd();
            panel3.Controls.Add(Print);

        }

        void tabpage2update()
        {
            panel22.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontrolupdate();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            panel22.Controls.Add(temp_ctrlbutton);

            panel1.Controls.Clear();
            Gen.fctlid = fctlid;
            Gen.Class = Class;
            Gen.Classfctlid = Classfctlid;
            Gen.ftype = "E";
            Gen.fbus = fbus;
            Gen.buttoncontrolupdate();
            panel1.Controls.Add(Gen);
        }

        void tabpage3update()
        {
            panel24.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontrolupdate ();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            panel24.Controls.Add(temp_ctrlbutton);

            panel4.Controls.Clear();
            if (Class == "CAR" || Class == "CSB")
            {
                Insured.fctlid = fctlid;
                Insured.Class = Class;
                Insured.Classfctlid = Classfctlid;
                Insured.buttoncontrolupdate(fendtype);
                Insured.ftype = "E";
                Insured.fbus = fbus;
                panel4.Controls.Add(Insured);
            }
            if (Class == "MYP" || Class == "PAR")
            {
                Insured2.fctlid = fctlid;
                Insured2.Class = Class;
                Insured2.Classfctlid = Classfctlid;
                Insured2.ftype = "E";
                Insured2.fbus = fbus;
                Insured2.EndtType = Gen.GenEndtType;
                Insured2.buttoncontrolupdate();
                panel4.Controls.Add(Insured2);
            }
            if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII")
            {
                InsuredC.fctlid = fctlid;
                InsuredC.Class = Class;
                InsuredC.Classfctlid = Classfctlid;
                InsuredC.ftype = "E";
                InsuredC.fbus = fbus;
                InsuredC.buttoncontrolupdate();
                panel4.Controls.Add(InsuredC);
            }
            if (Class == "CPM")
            {
                InsuredD.fctlid = fctlid;
                InsuredD.Class = Class;
                InsuredD.Classfctlid = Classfctlid;
                InsuredD.ftype = "E";
                InsuredD.fbus = fbus;
                InsuredD.buttoncontrolupdate();
                panel4.Controls.Add(InsuredD);
            }
            if (Class == "PMP" || Class == "CMP")
            {
                InsuredE.fctlid = fctlid;
                InsuredE.Class = Class;
                InsuredE.Classfctlid = Classfctlid;
                InsuredE.ftype = "E";
                InsuredE.fbus = fbus;
                InsuredE.buttoncontrolupdate();
                panel4.Controls.Add(InsuredE);
            }
            if (Class == "EEC")
            {
                InsuredB.fctlid = fctlid;
                InsuredB.Class = Class;
                InsuredB.Classfctlid = Classfctlid;
                InsuredB.fsclass = Gen.GenSubClass;
                InsuredB.InsureSumUpd = Gen.GenOSumUpd;
                InsuredB.ftype = "E";
                InsuredB.fbus = fbus;
                InsuredB.buttoncontrolupdate();
                panel4.Controls.Add(InsuredB);
            }
        }

        void tabpage4update()
        {
            panel13.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontrolupdate();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            panel13.Controls.Add(temp_ctrlbutton);

            panel11.Controls.Clear();
            Excess.fctlid = fctlid;
            if (Class == "CAR" || Class == "PMP" || Class == "CMP")
            {
                Excess.panel12.Visible = true;
            }
            Excess.Class = Class;
            Excess.Classfctlid = Classfctlid;
            Excess.ftype = "E";
            Excess.fbus = fbus;
            Excess.buttoncontrolupdate();
            panel11.Controls.Add(Excess);
        }

        void tabpage5update()
        {
            panel26.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontrolupdate ();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            panel26.Controls.Add(temp_ctrlbutton);

            panel12.Controls.Clear();
            Attach.fctlid = fctlid;
            Attach.Class = Class;
            Attach.Classfctlid = Classfctlid;
            Attach.ftype = "E";
            Attach.fbus = fbus;
            Attach.buttoncontrolupdate();
            panel12.Controls.Add(Attach);
        }

        void tabpage6update()
        {
            panel27.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontrolupdate();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            panel27.Controls.Add(temp_ctrlbutton);

            panel2.Controls.Clear();
            Install.fctlid = fctlid;
            Install.Class = Class;
            Install.Classfctlid = Classfctlid;
            Install.ftype = "E";
            Install.fbus = fbus;
            Install.buttoncontrolupdate();
            panel2.Controls.Add(Install);
           
        }

        void tabpage7update()
        {
            panel5.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontrolupdate();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            panel5.Controls.Add(temp_ctrlbutton);

            panel3.Controls.Clear();
            Print.fctlid = fctlid;
            Print.Class = Class;
            Print.buttoncontrolupdate();
            panel3.Controls.Add(Print);

        }

        private void modifyattch_Click(object sender, EventArgs e)
        {
            buttoncontrolupdate();
        }

        private void addattch_Click(object sender, EventArgs e)
        {
            buttoncontroladd();
        }

        private void cancelattch_Click(object sender, EventArgs e)
        {
                buttoncontrolback();
                tabpage1reload();
        }

        private void exitattch_Click(object sender, EventArgs e)
        {
            exit();
        }

        private void delattch_Click(object sender, EventArgs e)
        {
            del();
        }

        private void confirmattch_Click(object sender, EventArgs e)
        {
            confpol tempform = new confpol(this);
            tempform.fctlid = fctlid;
            tempform.ShowDialog();
            FillData("");
            tabControl1.SelectedTab = tabPage1;
        }
        

        private void tabPage6_MouseClick(object sender, MouseEventArgs e)
        {
            this.tabPage6.Focus();
        }

        private void tabPage6_MouseWheel(object sender, MouseEventArgs e)
        {
            tabPage6.VerticalScroll.Value += 10;
            tabPage6.Refresh();
            tabPage6.Invalidate();
            tabPage6.Update();
        }

        private void saveattch_Click(object sender, EventArgs e)
        {
            string result = save();

            if (Addflag == true)
            {
                if (result == "OK")
                {
                    MessageBox.Show("Have Been Inserted!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("Haven't Been Inserted!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            if (updateflag == true)
            {
                if (result == "OK")
                {
                    MessageBox.Show("Have Been Updated!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("Haven't Been Updated!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        void tabpage1Saveload(string newfctlid, string newcom, string newdisc)
        {
            panel10.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = "Pending";
            temp_ctrlbutton.buttonload("");
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirmattch_Click);
            panel10.Controls.Add(temp_ctrlbutton);

            tabPage2reload(newfctlid);
            tabPage3reload(newfctlid, newcom, newdisc);
            tabPage4reload(newfctlid);
            tabPage5reload(newfctlid);
            tabPage6reload(newfctlid);
        }

        public string save()
        {
            string result = "";
            tabControl1.SelectedTab = tabPage2;
            string sql1 = Gen.tabpage2save();
            string newfctlid = Gen.newfctlid;
            string newcom = Gen.newcom;
            string newdisc = Gen.newdisc;
            string Subclass = Gen.GenSubClass;
            string[] sql2 = new string[10];
            if (Class == "CAR" || Class == "CSB")
            {
                sql2 = Insured.tabpage3save(newfctlid, Subclass, Gen.GenEndtType1);
            }
            else if (Class == "MYP" || Class == "PAR")
            {
                sql2 = Insured2.tabpage3save(newfctlid, Subclass);
            }
            else if (Class == "EEC")
            {
                sql2 = InsuredB.tabpage3save(newfctlid, Subclass, Gen.GenEndtType1);
            }
            else if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII")
            {
                sql2 = InsuredC.tabpage3save(newfctlid, Subclass);
            }
            else if (Class == "CPM")
            {
                sql2 = InsuredD.tabpage3save(newfctlid, Subclass);
            }
            else if (Class == "PMP" || Class == "CMP")
            {
                sql2 = InsuredE.tabpage3save(newfctlid, Subclass);
            }
            else { sql2 = null; }

            string[] sql3 = Excess.tabpage4save(newfctlid, Subclass);
            string[] sql4 = Attach.tabpage5save(newfctlid, Subclass);
            string[] sql5 = Install.tabpage6save(newfctlid);
            string[] sql6 = Print.tabpage7save(newfctlid, Subclass);

            string updatepolicy = "";
            string[] updateinsure = new string[4];
            string[] updateexcess = new string[2];
            string[] updateattch = new string[2];
            string[] updateinstall = new string[2];
            string[] updateprint = new string[2];

            if (sql1 != "" && Addflag == true)
            {
                updatepolicy = "update " + db + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='POLID'";
            }

            if (sql2 != null && sql2.Length > 0)
            {
                if (Class == "PAR" || Class == "MYP")
                {
                    updateinsure[1] = "update " + db + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql2[0] + "')), 10) where fidtype ='OINSLOCID'";
                    for (int i = 1; i < sql2.Length; i++)
                    {
                        if (sql2[i].Length == 10)
                        {
                            updateinsure[2] = "update " + db + "xsysparm set fnxtid='" + sql2[i] + "' where fidtype ='OINSINTID'";
                        }
                    }
                }
                if (Class == "EEC")
                {
                    updateinsure[1] = "update " + db + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql2[0] + "')), 10) where fidtype ='OINSINTIDB'";
                }
                if (Class == "CPM")
                {
                    updateinsure[1] = "update " + db + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql2[0] + "')), 10) where fidtype ='OINSINTIDD'";
                }
                if (Class == "PMP" || Class == "CMP")
                {
                    updateinsure[1] = "update " + db + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql2[0] + "')), 10) where fidtype ='OINSINTIDE'";
                }
                if (Class == "CAR" || Class == "CSB")
                {
                    updateinsure[1] = "update " + db + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql2[0] + "')), 10) where fidtype ='OINSINTID'";
                }
                if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII")
                {
                    if (Class == "CGL" || Class == "FGP")
                    {
                        updateinsure[1] = "update " + db + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql2[0] + "')), 10) where fidtype ='OINSLOCID'";
                        if (Class == "FGP" && sql2.Length > 2)
                        {
                            for (int i = 1; i < sql2.Length; i++)
                            {
                                if (sql2[i].Length == 1)
                                {
                                    updateinsure[2] = "update " + db + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql2[i] + "')), 10) where fidtype ='OINSPERID'";
                                }
                            }
                        }
                    }
                    if (Addflag == true)
                    {
                        updateinsure[3] = "update " + db + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='OINSINTIDC'";
                    }
                }
            }
            if (sql3 != null && sql3.Length > 0)
            {
                updateexcess[1] = "update " + db + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql3[0] + "')), 10) where fidtype ='OINSEXID'";
            }
            if (sql4 != null && sql4.Length > 0)
            {
                updateattch[1] = "update " + db + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql4[0] + "')), 10) where fidtype ='OINSATTID'";
            }
            if (sql5 != null && sql5.Length > 0)
            {
                updateinstall[1] = "update " + db + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql5[0] + "')), 10) where fidtype ='OINVDET'";
            }
            if (sql6 != null && sql6.Length > 0)
            {
                updateprint[1] = "update " + db + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql6[0] + "')), 10) where fidtype ='OREMARK'";
            }
          
            if (sql6.Length > 0)
            {
                if (sql6[0] == "Empty")
                {
                    MessageBox.Show("Content can't be Empty!", "Warning",
                               MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
                    result = ExecuteSqlTransaction(connectionString, sql1, sql2, sql3, sql4, sql5, sql6, updatepolicy, updateinsure, updateexcess, updateattch, updateinstall, updateprint);
                    if (result == "OK")
                    {
                        buttoncontrolback();
                        tabpage1Saveload(newfctlid, newcom, newdisc);
                        FillData("");
                    }
                    else
                    {
                        MessageBox.Show(result, "Warning",
                           MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }

            return result;
        }

        private string ExecuteSqlTransaction(string connectionString, string sql1, string[] sql2, string[] sql3, string[] sql4, string[] sql5, string[] sql6, string updatepolicy, string[] updateinsure, string[] updateexcess, string[] updateattch, string[] updateinstall, string[] updateprint)
        {
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                SqlCommand command = connection.CreateCommand();
                SqlTransaction transaction;

                // Start a local transaction.
                transaction = connection.BeginTransaction("SampleTransaction");

                // Must assign both transaction object and connection 
                // to Command object for a pending local transaction
                command.Connection = connection;
                command.Transaction = transaction;

                try
                {
                    command.CommandText = sql1;
                    command.ExecuteNonQuery();
                    if (updatepolicy != "")
                    {
                        command.CommandText = updatepolicy;
                        command.ExecuteNonQuery();
                    }
                    if (sql2 != null)
                    {
                        for (int i = 1; i < sql2.Length; i++)
                        {
                            if (sql2[i] != "" && sql2[i] != null && sql2[i].Length != 10 && sql2[i].Length > 1)
                            {
                                command.CommandText = sql2[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (updateinsure != null)
                    {
                        for (int i = 1; i < updateinsure.Length; i++)
                        {
                            if (updateinsure[i] != "" && updateinsure[i] != null)
                            {
                                command.CommandText = updateinsure[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (sql3 != null)
                    {
                        for (int i = 1; i < sql3.Length; i++)
                        {
                            if (sql3[i] != "" && sql3[i] != null)
                            {
                                command.CommandText = sql3[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (updateexcess != null)
                    {
                        for (int i = 1; i < updateexcess.Length; i++)
                        {
                            if (updateexcess[i] != "" && updateexcess[i] != null)
                            {
                                command.CommandText = updateexcess[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (sql4 != null)
                    {
                        for (int i = 1; i < sql4.Length; i++)
                        {
                            if (sql4[i] != "" && sql4[i] != null)
                            {
                                command.CommandText = sql4[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (updateattch != null)
                    {
                        for (int i = 1; i < updateattch.Length; i++)
                        {
                            if (updateattch[i] != "" && updateattch[i] != null)
                            {
                                command.CommandText = updateattch[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (sql5 != null)
                    {
                        for (int i = 1; i < sql5.Length; i++)
                        {
                            if (sql5[i] != "" && sql5[i] != null)
                            {
                                command.CommandText = sql5[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (updateinstall != null)
                    {
                        for (int i = 1; i < updateinstall.Length; i++)
                        {
                            if (updateinstall[i] != "" && updateinstall[i] != null)
                            {
                                command.CommandText = updateinstall[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (sql6 != null)
                    {
                        for (int i = 1; i < sql6.Length; i++)
                        {
                            if (sql6[i] != "" && sql6[i] != null)
                            {
                                command.CommandText = sql6[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (updateprint != null)
                    {
                        for (int i = 1; i < updateprint.Length; i++)
                        {
                            if (updateprint[i] != "" && updateprint[i] != null)
                            {
                                command.CommandText = updateprint[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    // Attempt to commit the transaction.
                    transaction.Commit();
                    return "OK";
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                    Console.WriteLine("  Message: {0}", ex.Message);

                    // Attempt to roll back the transaction. 
                    try
                    {
                        transaction.Rollback();
                        return "RollBack";
                    }
                    catch (Exception ex2)
                    {
                        // This catch block will handle any errors that may have occurred 
                        // on the server that would cause the rollback to fail, such as 
                        // a closed connection.
                        Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                        Console.WriteLine("  Message: {0}", ex2.Message);
                        return ex2.Message;
                    }
                }
            }
        }
    }
}
