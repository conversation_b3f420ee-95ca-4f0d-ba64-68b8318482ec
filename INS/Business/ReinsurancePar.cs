using INS.Ctrl;
using INS.Ctrl.Reins;
using INS.INSClass;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business
{
    public partial class ReinsurancePar : Form
    {
        GenInfoPar Gen = new GenInfoPar();
        FacPropPar Prop = new FacPropPar();
        NonProp nonProp = new NonProp();
        TeatyInstallPar trInstall = new TeatyInstallPar();
        FacInstallPar facInstall = new FacInstallPar();
        DBConnect operate = new DBConnect();
        public string fstatus1 = "", fctlid_1 = "", fkind = "", fctlid = "", fposted = "", fsec = "", fclass = "", Policy_post = "";
        public Boolean updateflag = false, p_combined = false;

        private Ctrl.DirectInstall directInstall;
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public string Dbo = InsEnvironment.DataBase.GetDbo();
        DataTable DToriinstl = new DataTable();
        DataTable DToriinvd = new DataTable();
        DataTable DToriivnf = new DataTable();
        private RIInstall rIInstall;

        public ReinsurancePar()
        {
            InitializeComponent();
        }

        public ReinsurancePar(Ctrl.DirectInstall directInstall)
        {
            // TODO: Complete member initialization
            this.directInstall = directInstall;
            InitializeComponent();
        }

        public ReinsurancePar(RIInstall rIInstall)
        {
            // TODO: Complete member initialization
            this.rIInstall = rIInstall;
            InitializeComponent();
        }

        public void FillData(string order)
        {
            string sql = "select fpolno as [Policy#], fendtno as [Endr No.],frino as [RI No.],CONVERT(varchar(10),fissdate,102) as [Issue Date], " +
            "CONVERT(varchar(10),fefffr,102) as [Effect Fr],CONVERT(varchar(10),feffto,102) as [Effect To],fgpm as [G Premium], fnpm as [N Premium],case when fposted='1' then 'Yes' else 'No' end as [Posted],fctlid,fstatus " +
            "from orih where fctlid_1 ='" + fctlid_1 + "' and fkind ='" + fkind + "'";
            Policylist.DataSource = DBHelper.GetDataSet(sql);
            Policylist.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.Policylist_CellFormatting);
            this.Policylist.Columns["fctlid"].Visible = false;
            this.Policylist.Columns["fstatus"].Visible = false;
            if (fclass == "CAR" && fkind == "1") { fsec = "SEC1"; }
            if (fclass == "CAR" && fkind == "2") { fsec = "SEC2"; }
            if (Policylist.RowCount > 0)
            {
                fctlid = Policylist.Rows[0].Cells["fctlid"].Value.ToString();
            }
        }

        private void Policylist_CellFormatting(object sender, System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            if (Policylist.Columns[e.ColumnIndex].Name.Equals("Posted"))
            {
                Policylist.Columns[e.ColumnIndex].Width = 45;
            }
            if (Policylist.Columns[e.ColumnIndex].Name.Equals("RI No."))
            {
                Policylist.Columns[e.ColumnIndex].Width = 35;
            }
            if (Policylist.Columns[e.ColumnIndex].Name.Equals("G Premium"))
            {
                Policylist.Columns[e.ColumnIndex].Width = 90;
                Policylist.Columns[e.ColumnIndex].DefaultCellStyle.Format = "N2";
                Policylist.Columns[e.ColumnIndex].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            }
            if (Policylist.Columns[e.ColumnIndex].Name.Equals("N Premium"))
            {
                Policylist.Columns[e.ColumnIndex].Width = 90;
                Policylist.Columns[e.ColumnIndex].DefaultCellStyle.Format = "N2";
                Policylist.Columns[e.ColumnIndex].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            }
        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            if (updateflag == true)
            {
                if (e.TabPage == tabPage1)
                    e.Cancel = true;
            }
        }

        private void tabControl1_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (updateflag == false)
            {
                if (tabControl1.SelectedIndex == 1)
                {
                    tabPage2reload(fctlid);
                }
                if (tabControl1.SelectedIndex == 2)
                {
                    tabPage3reload(fctlid);
                }
                if (tabControl1.SelectedIndex == 3)
                {
                    tabPage5reload(fctlid);
                }
                if (tabControl1.SelectedIndex == 4)
                {
                    tabPage6reload(fctlid);
                }
            }
            else
            {
                if (tabControl1.SelectedIndex == 1)
                {
                    Prop.ComCaculate();
                    Gen.ComMain = Prop.ComMain;
                    Gen.facCaculate();
                    Gen.fsec = fsec;
                }
                if (tabControl1.SelectedIndex == 2)
                {
                    Gen.facCaculate();
                    Prop.FacMain = Gen.FacMain;
                    Prop.fsec = fsec;
                    Prop.FacMainTods();
                }
            }
        }

        void policylistchg() {
            int Policylistrow = 0;
            if (Policylist.CurrentCell != null)
            {
                Policylistrow = Policylist.CurrentCell.RowIndex;
            }
            else { return; }
            if (Policylist.Rows[Policylistrow].Cells["fctlid"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["fctlid"].Value.ToString().Length != 0)
                {
                    fctlid = Policylist.Rows[Policylistrow].Cells["fctlid"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["Posted"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["Posted"].Value.ToString().Length != 0)
                {
                    fposted = Policylist.Rows[Policylistrow].Cells["Posted"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["fstatus"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["fstatus"].Value.ToString().Length != 0)
                {
                    Policy_post = Policylist.Rows[Policylistrow].Cells["fstatus"].Value.ToString();
                }
            }
            panel1.Controls.Clear();
            panel1.Controls.Add(buttonload("Load"));
        }

        private void Policylist_SelectionChanged(object sender, EventArgs e)
        {
            policylistchg();
        }

        public void buttoncontrolupdate()
        {
            updateflag = true;
            tabControl1.SelectedTab = tabPage2;
            tabpage2update();
            tabpage3update();
            tabpage5update();
            tabpage6update();
        }

        void tabpage2update()
        {
            panel3.Controls.Clear();
            panel3.Controls.Add(buttonload("Mod"));
            panel2.Controls.Clear();
            Gen.fctlid = fctlid;
            Gen.buttoncontrolupdate();
            panel2.Controls.Add(Gen);
        }

        void tabpage3update()
        {
            panel5.Controls.Clear();
            panel5.Controls.Add(buttonload("Mod"));
            panel4.Controls.Clear();
            Prop.fctlid = fctlid;
            Prop.buttoncontrolupdate();
            panel4.Controls.Add(Prop);
        }

        void tabpage5update()
        {
            panel9.Controls.Clear();
            panel9.Controls.Add(buttonload("Mod"));
            panel8.Controls.Clear();
            trInstall.fctlid = fctlid;
            trInstall.fkind = fkind;
            trInstall.buttoncontrolload();
            panel8.Controls.Add(trInstall);
        }

        void tabpage6update()
        {
            panel11.Controls.Clear();
            panel11.Controls.Add(buttonload("Mod"));
            panel10.Controls.Clear();
            facInstall.fctlid = fctlid;
            facInstall.fkind = fkind;
            facInstall.buttoncontrolload();
            panel10.Controls.Add(facInstall);
        }

        private void modifyattch_Click(object sender, EventArgs e)
        {
            buttoncontrolupdate();
        }

        public void buttoncontrolback()
        {
            updateflag = false;
            tabPage2reload(fctlid);
            tabPage3reload(fctlid);
            tabPage5reload(fctlid);
            tabPage6reload(fctlid);
        }

        public Control buttonload(string flag)
        {
            ReinsButton temp_ctrlbutton = new ReinsButton();
            temp_ctrlbutton.fposted = fposted;
            temp_ctrlbutton.Policy_post = Policy_post;
            if (flag == "Load") { temp_ctrlbutton.buttonload(""); }
            if (flag == "Back") { temp_ctrlbutton.buttoncontrolback(); }
            if (flag == "Mod") { temp_ctrlbutton.buttoncontrolupdate(); }
            if (flag == "Save") { temp_ctrlbutton.buttoncontrolsaveback(); }
            if (flag == "Con") { temp_ctrlbutton.buttoncontrolconfirmback(); }
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(conattch_Click);
            return temp_ctrlbutton;
        }

        void tabPage2reload(string fctlid)
        {
            panel3.Controls.Clear();
            panel3.Controls.Add(buttonload("Load"));
            panel2.Controls.Clear();
            Gen.fctlid = fctlid;
            Gen.buttoncontrolback();
            panel2.Controls.Add(Gen);
        }

        void tabPage3reload(string fctlid)
        {
            panel5.Controls.Clear();
            panel5.Controls.Add(buttonload("Load"));
            panel4.Controls.Clear();
            Prop.fctlid = fctlid;
            Prop.buttoncontrolback();
            panel4.Controls.Add(Prop);
        }

        void tabPage5reload(string fctlid)
        {
            panel9.Controls.Clear();
            panel9.Controls.Add(buttonload("Load"));
            panel8.Controls.Clear();
            trInstall.fctlid = fctlid;
            trInstall.fkind = fkind;
            trInstall.buttoncontrolload();
            panel8.Controls.Add(trInstall);
        }

        void tabPage6reload(string fctlid)
        {
            panel11.Controls.Clear();
            panel11.Controls.Add(buttonload("Load"));
            panel10.Controls.Clear();
            facInstall.fctlid = fctlid;
            facInstall.fkind = fkind;
            facInstall.buttoncontrolload();
            panel10.Controls.Add(facInstall);
        }

        private void cancelattch_Click(object sender, EventArgs e)
        {
            buttoncontrolback();
        }

        private void conattch_Click(object sender, EventArgs e)
        {
            string result = confirm();
            MessageBox.Show(result, "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void saveattch_Click(object sender, EventArgs e)
        {
            string result = save();

            if (result == "OK")
            {
                MessageBox.Show("Have Been Inserted!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("Haven't Been Inserted!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        public Boolean Validation()
        {
            string ctrlName = "";
            ctrlName = Gen.u_valrine();
            if (ctrlName != "")
            {
                tabControl1.SelectedIndex = 1;
                Gen.setControlFocus(ctrlName);
                return false;
            }
            ctrlName = Prop.u_valrinsr();
            if (ctrlName != "")
            {
                tabControl1.SelectedIndex = 2;
                Prop.setControlFocus(ctrlName);
                return false;
            }
            return true;
        }

        public string save()
        {
            string result = "";
            if (Validation())
            {
                Gen.u_calallrid1();
                Prop.u_calallrid2();
                Prop.ComCaculate();
                Gen.ComMain = Prop.ComMain;
                Gen.facCaculate();
                string[] sql1 = Gen.tabpage2save();
                string[] sql2 = Prop.tabpage3save();
                string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
                result = ExecuteSqlTransaction(connectionString, sql1, sql2);
                if (result == "OK")
                {
                    string oldfctlid = fctlid;
                    buttoncontrolback();
                    //tabpage1Saveload(newfctlid, newcom, newdisc);
                    FillData("");
                    DataTable dt3 = Policylist.DataSource as DataTable;
                    foreach (DataRow row in dt3.Rows)
                    {
                        int SelectedIndex = dt3.Rows.IndexOf(row);
                        if (oldfctlid == row["fctlid"].ToString().Trim())
                        {
                            Policylist.CurrentCell = Policylist.Rows[SelectedIndex].Cells["Policy#"];
                            policylistchg();
                            break;
                        }
                    }
                }
                else
                {
                    MessageBox.Show(result, "Warning",
                       MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            return result;
        }

        public string confirm()
        {
            string result = "";
            bool is_ok = false;
            if (Validation())
            {
                Gen.u_calallrid1();
                Prop.u_calallrid2();
                DToriinstl.Clear();
                DToriinvd.Clear();
                DToriivnf.Clear();
                is_ok = u_genttyinst();//02
                if (is_ok) { is_ok = u_genfacbinst(); }//05
                if (is_ok) { is_ok = u_genfacinst(); }//04
                if (is_ok) { is_ok = u_genrtninst(); }//01

                if (is_ok) { is_ok = u_allttyinst(); }
                if (is_ok) { is_ok = u_allfacbinst(); }
                if (is_ok) { is_ok = u_allfacinst(); }
                if (is_ok) { is_ok = u_genriapp("04"); }
                if (is_ok) { is_ok = u_genfacinv(); }
                if (is_ok) { is_ok = u_allrtninst(); }
                if (is_ok) { is_ok = u_updrih(); }

                if (is_ok)
                {
                    DateTime time = DateTime.Now;
                    string updst = "update orih set fposted ='1',fissdate = case when fissdate=null then getdate() else fissdate end,fefffr = case when fefffr=null then getdate() else fefffr end,feffto = case when feffto=null then getdate() else feffto end,fincpdate = case when fincpdate=null then getdate() else fincpdate end, fcnfuser='" + InsEnvironment.LoginUser.GetUserCode() + "', fcnfdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid = '" + fctlid + "'";
                    is_ok = DBHelper.ExecuteCommand(updst);
                }
                if (is_ok == false)
                {
                    DateTime time = DateTime.Now;
                    ArrayList updarr = new ArrayList();
                    string updsql = "delete from oriinvld where fctlid in " +
                            "(select a.fctlid from oriinvld a " +
                            "join (select * from oriinsll where fctlid_ri='" + fctlid + "' and fupduser ='" + InsEnvironment.LoginUser.GetUserCode() + "') b on a.fctlid_is= b.fctlid)";
                    updarr.Add(updsql);

                    updsql = "delete from oriinvlf where fctlid in " +
                            "(select a.fctlid from oriinvlf a " +
                            "join (select * from oriinsll where fctlid_ri='" + fctlid + "' and fupduser ='" + InsEnvironment.LoginUser.GetUserCode() + "') b on a.fctlid_is= b.fctlid)";
                    updarr.Add(updsql);

                    updsql = "delete from oriinsll where fctlid_ri='" + fctlid + "' and fupduser ='" + InsEnvironment.LoginUser.GetUserCode() + "'";
                    updarr.Add(updsql);

                    updsql = "delete from oriinvd where fctlid in " +
                    "(select a.fctlid from oriinvd a " +
                    "join (select * from oriinstl where fctlid_1='" + fctlid + "' and fupduser ='" + InsEnvironment.LoginUser.GetUserCode() + "') b on a.fctlid_2= b.fctlid)";
                    updarr.Add(updsql);

                    updsql = "delete from oriinvf where fctlid in " +
                            "(select a.fctlid from oriinvf a " +
                            "join (select * from oriinstl where fctlid_1='" + fctlid + "' and fupduser ='" + InsEnvironment.LoginUser.GetUserCode() + "') b on a.fctlid_2= b.fctlid)";
                    updarr.Add(updsql);

                    updsql = "delete from oriinstl where fctlid_1='" + fctlid + "' and fupduser ='" + InsEnvironment.LoginUser.GetUserCode() + "'";
                    updarr.Add(updsql);

                    updsql = "delete from oriinvh where fctlid >= ( " +
                            "select min(a.fctlid) from oriinvh a " +
                            "join (select * from oriinsll where fctlid_e='" + fctlid + "' and fupduser ='" + InsEnvironment.LoginUser.GetUserCode() + "') b on a.fctlid_2= b.fctlid)";
                    updarr.Add(updsql);

                    updsql = "delete from oriapp where fctlid_e='" + fctlid_1 + "' and  finpuser ='" + InsEnvironment.LoginUser.GetUserCode() + "'";
                    updarr.Add(updsql);

                    updsql = "update orih set fposted ='2',fupduser='" + InsEnvironment.LoginUser.GetUserCode() + "', fupddate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid = '" + fctlid + "'";
                    updarr.Add(updsql);

                    string[] sql1 = (string[])updarr.ToArray(typeof(string));

                    string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
                    result = ExecuteSqlTransaction(connectionString, sql1, sql1);
                    if (result == "OK")
                    {
                        result = "RollBock";
                    }
                    else
                    {
                        result = "RollBock Error!";
                    }
                }
                else
                {
                    FillData("");
                    buttoncontrolback();
                    result = "Have Been Confirmed!!";
                }
            }
            return result;
        }

        bool u_genttyinst()
        {
            bool isSuccuss = false;
            string cheSql = "select fgpm+fnpm+fcomamt+fbrkgeamt as total from orild1 where fctlid_ri ='" + fctlid + "' and fritype ='02'";
            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["odata"].ConnectionString);
            SqlCommand cmd = new SqlCommand(cheSql, con);
            con.Open();
            using (SqlDataReader sdr = cmd.ExecuteReader())
            {
                if (sdr.HasRows && sdr.Read())
                {
                    if (Convert.ToDecimal(sdr["total"].ToString()) != 0)
                    {
                        DataTable dtoriinsll = new DataTable();
                        DateTime time = DateTime.Now;
                        ArrayList updarr = new ArrayList();
                        string oriinstl = Fct.NewId("oriinsll");
                        string oriinstl_fctlid = "";
                        string oriinvd = Fct.NewId("oriinvld");
                        string oriinvd_fctlid = "";

                        string selsql = "select a.fctlid_ri as fctlid_ri, a.fctlid as fctlid_lc, a.fctlid_p as fctlid_p, a.fctlid_e as fctlid_e, b.fctlid as fctlid_i, " +
                                "a.fbus as fbus,'D01'+ c.fritype as fmodule, a.fpolno, a.fendtno, a.fuwyr, a.fclass, a.fsclass, " +
                                "b.facclass,a.fkind,c.fritype,c.fsec,c.fttycode,c.fttysec,c.fttyclass,b.finstall,b.ftotinstal,'" + oriinstl + "' as fctlid, " +
                                "a.fissdate,b.finvdate,b.fbilldate,null as fbkdate,a.fefffr,a.feffto,c.fgrnet,b.fpmcur, " +
                                "'' as fgpm,'' as fnpm, '' as fcomamt,'' as fbrkgeamt, 0,0,0,0,'' as fpayable, " +
                                "0,0,0,0,3,2,'" + InsEnvironment.LoginUser.GetUserCode() + "' as fupduser, '" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' as fupddate,  " +
                                "c.fgpm as trid1_fgpm, c.fnpm as trid1_fnpm, c.fcomamt as trid1_fcomamt,c.fbrkgeamt as trid1_fbrkgeamt, e.fgpm as ln_tfgpm, b.fgpm as oinvh_fgpm, e.fnpm as ln_tfnpm, b.fnpm as oinvh_fnpm " +
                                "from oril a  " +
                                "left join oinvh b on a.fctlid_e = b.fctlid_1 " +
                                "left join orild1 c on c.fctlid_lc = a.fctlid  " +
                                "left join (select sum(fgpm) as fgpm,sum(fnpm) as fnpm, fctlid_1 from oinvh group by fctlid_1) e on e.fctlid_1=a.fctlid_e " +
                                "where a.fctlid_ri ='" + fctlid + "' and c.fritype='02' " +
                                "order by b.finstall";
                        DataTable dt = DBHelper.GetDataSet(selsql);
                        if (dt.Rows.Count > 0)
                        {
                            for (int i = 0; i < dt.Rows.Count; i++)
                            {
                                oriinstl = (int.Parse(oriinstl) + 1).ToString().PadLeft(10, '0');
                                dt.Rows[i]["fctlid"] = oriinstl;
                                if (Convert.ToDecimal(dt.Rows[i]["ln_tfgpm"]) == 0)
                                {
                                    dt.Rows[i]["fgpm"] = 0;
                                }
                                else
                                {
                                    dt.Rows[i]["fgpm"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid1_fgpm"]) * Convert.ToDecimal(dt.Rows[i]["oinvh_fgpm"]) / Convert.ToDecimal(dt.Rows[i]["ln_tfgpm"]), 2, MidpointRounding.AwayFromZero);
                                }
                                if (Convert.ToDecimal(dt.Rows[i]["ln_tfnpm"]) == 0)
                                {
                                    dt.Rows[i]["fnpm"] = 0;
                                }
                                else
                                {
                                    dt.Rows[i]["fnpm"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid1_fnpm"]) * Convert.ToDecimal(dt.Rows[i]["oinvh_fnpm"]) / Convert.ToDecimal(dt.Rows[i]["ln_tfnpm"]), 2, MidpointRounding.AwayFromZero);
                                }
                                if ((dt.Rows[i]["fgrnet"].ToString() == "1" && Convert.ToDecimal(dt.Rows[i]["ln_tfgpm"]) == 0) || (dt.Rows[i]["fgrnet"].ToString() == "2" && Convert.ToDecimal(dt.Rows[i]["ln_tfnpm"]) == 0))
                                {
                                    dt.Rows[i]["fcomamt"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid1_fcomamt"]) / Convert.ToDecimal(dt.Rows[i]["ftotinstal"]), 2, MidpointRounding.AwayFromZero);
                                    dt.Rows[i]["fbrkgeamt"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid1_fbrkgeamt"]) / Convert.ToDecimal(dt.Rows[i]["ftotinstal"]), 2, MidpointRounding.AwayFromZero);
                                }
                                if (dt.Rows[i]["fgrnet"].ToString() == "1" && Convert.ToDecimal(dt.Rows[i]["ln_tfgpm"]) != 0)
                                {
                                    dt.Rows[i]["fcomamt"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid1_fcomamt"]) * Convert.ToDecimal(dt.Rows[i]["oinvh_fgpm"]) / Convert.ToDecimal(dt.Rows[i]["ln_tfgpm"]), 2, MidpointRounding.AwayFromZero);
                                    dt.Rows[i]["fbrkgeamt"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid1_fbrkgeamt"]) * Convert.ToDecimal(dt.Rows[i]["oinvh_fgpm"]) / Convert.ToDecimal(dt.Rows[i]["ln_tfgpm"]), 2, MidpointRounding.AwayFromZero);
                                }
                                if (dt.Rows[i]["fgrnet"].ToString() == "2" && Convert.ToDecimal(dt.Rows[i]["ln_tfnpm"]) != 0)
                                {
                                    dt.Rows[i]["fcomamt"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid1_fcomamt"]) * Convert.ToDecimal(dt.Rows[i]["oinvh_fnpm"]) / Convert.ToDecimal(dt.Rows[i]["ln_tfnpm"]), 2, MidpointRounding.AwayFromZero);
                                    dt.Rows[i]["fbrkgeamt"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid1_fbrkgeamt"]) * Convert.ToDecimal(dt.Rows[i]["oinvh_fnpm"]) / Convert.ToDecimal(dt.Rows[i]["ln_tfnpm"]), 2, MidpointRounding.AwayFromZero);
                                }
                                if (dt.Rows[i]["fgrnet"].ToString() == "1")
                                {
                                    dt.Rows[i]["fpayable"] = Convert.ToDecimal(dt.Rows[i]["fgpm"]) - Convert.ToDecimal(dt.Rows[i]["fcomamt"]);
                                }
                                else
                                {
                                    dt.Rows[i]["fpayable"] = Convert.ToDecimal(dt.Rows[i]["fnpm"]) - Convert.ToDecimal(dt.Rows[i]["fcomamt"]);
                                }
                                oriinstl_fctlid = dt.Rows[i]["fctlid"].ToString();
                            }
                            string loc = "select fctlid from oril where fctlid_ri ='" + fctlid + "'";
                            DataTable locDT = DBHelper.GetDataSet(loc);
                            for (int k = 0; k < locDT.Rows.Count; k++)
                            {
                                DataTable grpLoc = dt.Select("fctlid_lc='" + locDT.Rows[k]["fctlid"] + "'").CopyToDataTable();
                                if (grpLoc.Rows.Count > 0)
                                {
                                    decimal ln_tfgpm = 0, ln_tfnpm = 0, ln_tfcomamt = 0, ln_tfbrkgeamt = 0;
                                    for (int j = 0; j < grpLoc.Rows.Count; j++)
                                    {
                                        ln_tfgpm += Convert.ToDecimal(grpLoc.Rows[j]["fgpm"]);
                                        ln_tfnpm += Convert.ToDecimal(grpLoc.Rows[j]["fnpm"]);
                                        ln_tfcomamt += Convert.ToDecimal(grpLoc.Rows[j]["fcomamt"]);
                                        ln_tfbrkgeamt += Convert.ToDecimal(grpLoc.Rows[j]["fbrkgeamt"]);
                                    }
                                    grpLoc.Rows[0]["fgpm"] = Convert.ToDecimal(grpLoc.Rows[0]["fgpm"]) - Convert.ToDecimal(grpLoc.Rows[0]["trid1_fgpm"]) - ln_tfgpm;
                                    grpLoc.Rows[0]["fnpm"] = Convert.ToDecimal(grpLoc.Rows[0]["fnpm"]) - Convert.ToDecimal(grpLoc.Rows[0]["trid1_fnpm"]) - ln_tfnpm;
                                    grpLoc.Rows[0]["fcomamt"] = Convert.ToDecimal(grpLoc.Rows[0]["fcomamt"]) - Convert.ToDecimal(grpLoc.Rows[0]["trid1_fcomamt"]) - ln_tfcomamt;
                                    grpLoc.Rows[0]["fbrkgeamt"] = Convert.ToDecimal(grpLoc.Rows[0]["fbrkgeamt"]) - Convert.ToDecimal(grpLoc.Rows[0]["trid1_fbrkgeamt"]) - ln_tfbrkgeamt;
                                    if (grpLoc.Rows[0]["fgrnet"].ToString() == "1") { grpLoc.Rows[0]["fpayable"] = Convert.ToDecimal(grpLoc.Rows[0]["fgpm"]) - Convert.ToDecimal(grpLoc.Rows[0]["fcomamt"]); }
                                    else { grpLoc.Rows[0]["fpayable"] = Convert.ToDecimal(grpLoc.Rows[0]["fnpm"]) - Convert.ToDecimal(grpLoc.Rows[0]["fcomamt"]); }
                                }
                                if (dtoriinsll.Rows.Count > 0)
                                {
                                    foreach (DataRow dr in grpLoc.Rows)
                                    {
                                        dtoriinsll.ImportRow(dr);
                                    }
                                }
                                else { dtoriinsll = grpLoc.Copy(); }
                            }
                            dtoriinsll.Columns.Remove("trid1_fgpm"); dtoriinsll.Columns.Remove("trid1_fnpm"); dtoriinsll.Columns.Remove("trid1_fcomamt"); dtoriinsll.Columns.Remove("trid1_fbrkgeamt");
                            dtoriinsll.Columns.Remove("oinvh_fgpm"); dtoriinsll.Columns.Remove("ln_tfgpm"); dtoriinsll.Columns.Remove("oinvh_fnpm"); dtoriinsll.Columns.Remove("ln_tfnpm");
                            if (DToriinstl.Rows.Count > 0)
                            {
                                foreach (DataRow dr in dtoriinsll.Rows)
                                {
                                    DToriinstl.ImportRow(dr);
                                }
                            }
                            else { DToriinstl = dtoriinsll.Copy(); }

                            string seloriinvd = "select distinct a.fctlid_ri as fctlid_ri,a.fctlid as fctlid_lc, '" + oriinstl + "' as fctlid_is, a.fctlid_p as fctlid_p, a.fctlid_e as fctlid_e, '' as fctlid_iv,b.fctlid as fctlid_i, " +
                                                "a.fbus as fbus,'D01'+ c.fritype as fmodule,a.frino, a.fpolno, a.fendtno, a.fuwyr, a.fclass, a.fsclass, " +
                                                "b.facclass,a.fkind,c.fritype,c.fsec,c.fttycode,c.fttysec,c.fttyclass,b.finstall,b.ftotinstal,'" + oriinvd + "'  as fctlid, " +
                                                "a.fissdate,b.finvdate,b.fbilldate,null as fbkdate,a.fefffr,a.feffto,c.fgrnet,'' as finvno, " +
                                                "case when e.fbrkr1='PRD001' then 'R' else 'B' end as fdbtrtype,case when e.fbrkr1='PRD001' then e.freinsr else e.fbrkr1 end as fdbtr, e.fbrkr1,'' as fbdesc, " +
                                                "e.freinsr,'' as frdesc,b.fpmcur, " +
                                                "'' as fgpm, '' as fnpm, '' as fcomamt, '' as fbrkgeamt,0,0,0,0, '' as fpayable,  " +
                                                "round(c.fsi * e.fshare/100,2) as fsi, round(c.fsi2 * e.fshare/100,2) as fsi2, round(c.fupdsi * e.fshare/100,2) as fupdsi, round(c.fupdsi2 * e.fshare/100,2) as fupdsi2,e.fshare, " +
                                                "c.fsi as trid1_fsi, c.fsi2 as trid1_fsi2, c.fupdsi as trid1_fupdsi,c.fupdsi2 as trid1_fupdsi2,e.flogseq  " +
                                                "from oril a left join oinvh b on a.fctlid_e = b.fctlid_1 " +
                                                "left join orild1 c on c.fctlid_lc = a.fctlid  " +
                                                "left join polh d on d.fctlid = a.fctlid_e  " +
                                                "left join (select a.fid, b.freinsr,b.fbrkr1,fshare,flogseq  " +
                                                "from " + Dbm + "mty a left join " + Dbm + "mtyinsr b on a.fctlid =b.fctlid_1) e on e.fid = c.fttycode " +
                                                "where a.fctlid_ri ='" + fctlid + "' and c.fritype='02' " +
                                                "order by b.finstall,fctlid_lc,e.flogseq";
                            DataTable dt1 = DBHelper.GetDataSet(seloriinvd);
                            DataTable dtoriinvd = new DataTable();
                            if (dt1.Rows.Count > 0)
                            {
                                for (int k = 0; k < locDT.Rows.Count; k++)
                                {
                                    DataTable grpLoc = dt1.Select("fctlid_lc='" + locDT.Rows[k]["fctlid"] + "'").CopyToDataTable();
                                    if (grpLoc.Rows.Count > 0)
                                    {
                                        DataView dataView = grpLoc.DefaultView;
                                        DataTable dataTableDistinct = dataView.ToTable(true, "finstall");
                                        for (int i = 1; i < dataTableDistinct.Rows.Count + 1; i++)
                                        {
                                            DataTable dt2 = grpLoc.Select("finstall = " + i + "").CopyToDataTable();
                                            if (dt2.Rows.Count > 0)
                                            {
                                                decimal lc_tfgpm = 0, lc_tfnpm = 0, lc_tfcomamt = 0, lc_tfbrkgeamt = 0, lc_tfsi = 0, lc_tfsi2 = 0, lc_tfupdsi = 0, lc_tfupdsi2 = 0;
                                                DataTable dt3 = dtoriinsll.Select("finstall = " + i + " and fctlid_lc='" + locDT.Rows[k]["fctlid"] + "'").CopyToDataTable();
                                                for (int j = 0; j < dt2.Rows.Count; j++)
                                                {
                                                    dt2.Rows[j]["fctlid_is"] = dt3.Rows[0]["fctlid"].ToString();
                                                    oriinvd = (int.Parse(oriinvd) + 1).ToString().PadLeft(10, '0');
                                                    dt2.Rows[j]["fctlid"] = oriinvd;
                                                    dt2.Rows[j]["fgpm"] = Math.Round(Convert.ToDecimal(dt3.Rows[0]["fgpm"]) * Convert.ToDecimal(dt2.Rows[j]["fshare"]) / 100, 2, MidpointRounding.AwayFromZero);
                                                    dt2.Rows[j]["fnpm"] = Math.Round(Convert.ToDecimal(dt3.Rows[0]["fnpm"]) * Convert.ToDecimal(dt2.Rows[j]["fshare"]) / 100, 2, MidpointRounding.AwayFromZero);
                                                    dt2.Rows[j]["fcomamt"] = Math.Round(Convert.ToDecimal(dt3.Rows[0]["fcomamt"]) * Convert.ToDecimal(dt2.Rows[j]["fshare"]) / 100, 2, MidpointRounding.AwayFromZero);
                                                    dt2.Rows[j]["fbrkgeamt"] = Math.Round(Convert.ToDecimal(dt3.Rows[0]["fbrkgeamt"]) * Convert.ToDecimal(dt2.Rows[j]["fshare"]) / 100, 2, MidpointRounding.AwayFromZero);
                                                    if (dt2.Rows[j]["fgrnet"].ToString() == "1")
                                                    {
                                                        dt2.Rows[j]["fpayable"] = Convert.ToDecimal(dt2.Rows[j]["fgpm"]) - Convert.ToDecimal(dt2.Rows[j]["fcomamt"]);
                                                    }
                                                    else
                                                    {
                                                        dt2.Rows[j]["fpayable"] = Convert.ToDecimal(dt2.Rows[j]["fnpm"]) - Convert.ToDecimal(dt2.Rows[j]["fcomamt"]);
                                                    }
                                                    oriinvd_fctlid = dt2.Rows[j]["fctlid"].ToString();
                                                    lc_tfgpm += Convert.ToDecimal(dt2.Rows[j]["fgpm"]);
                                                    lc_tfnpm += Convert.ToDecimal(dt2.Rows[j]["fnpm"]);
                                                    lc_tfcomamt += Convert.ToDecimal(dt2.Rows[j]["fcomamt"]);
                                                    lc_tfbrkgeamt += Convert.ToDecimal(dt2.Rows[j]["fbrkgeamt"]);
                                                    lc_tfsi += Convert.ToDecimal(dt2.Rows[j]["fsi"]);
                                                    lc_tfsi2 += Convert.ToDecimal(dt2.Rows[j]["fsi2"]);
                                                    lc_tfupdsi += Convert.ToDecimal(dt2.Rows[j]["fupdsi"]);
                                                    lc_tfupdsi2 += Convert.ToDecimal(dt2.Rows[j]["fupdsi2"]);
                                                }

                                                dt2.Rows[0]["fgpm"] = Convert.ToDecimal(dt2.Rows[0]["fgpm"]) + Convert.ToDecimal(dt3.Rows[0]["fgpm"]) - lc_tfgpm;
                                                dt2.Rows[0]["fnpm"] = Convert.ToDecimal(dt2.Rows[0]["fnpm"]) + Convert.ToDecimal(dt3.Rows[0]["fnpm"]) - lc_tfnpm;
                                                dt2.Rows[0]["fcomamt"] = Convert.ToDecimal(dt2.Rows[0]["fcomamt"]) + Convert.ToDecimal(dt3.Rows[0]["fcomamt"]) - lc_tfcomamt;
                                                dt2.Rows[0]["fbrkgeamt"] = Convert.ToDecimal(dt2.Rows[0]["fbrkgeamt"]) + Convert.ToDecimal(dt3.Rows[0]["fbrkgeamt"]) - lc_tfbrkgeamt;
                                                dt2.Rows[0]["fsi"] = Convert.ToDecimal(dt2.Rows[0]["fsi"]) + Convert.ToDecimal(dt2.Rows[0]["trid1_fsi"]) - lc_tfsi;
                                                dt2.Rows[0]["fsi2"] = Convert.ToDecimal(dt2.Rows[0]["fsi2"]) + Convert.ToDecimal(dt2.Rows[0]["trid1_fsi2"]) - lc_tfsi2;
                                                dt2.Rows[0]["fupdsi"] = Convert.ToDecimal(dt2.Rows[0]["fupdsi"]) + Convert.ToDecimal(dt2.Rows[0]["trid1_fupdsi"]) - lc_tfupdsi;
                                                dt2.Rows[0]["fupdsi2"] = Convert.ToDecimal(dt2.Rows[0]["fupdsi2"]) + Convert.ToDecimal(dt2.Rows[0]["trid1_fupdsi2"]) - lc_tfupdsi2;
                                                if (dt2.Rows[0]["fgrnet"].ToString() == "1") { dt2.Rows[0]["fpayable"] = Convert.ToDecimal(dt2.Rows[0]["fgpm"]) - Convert.ToDecimal(dt2.Rows[0]["fcomamt"]); }
                                                else { dt2.Rows[0]["fpayable"] = Convert.ToDecimal(dt2.Rows[0]["fnpm"]) - Convert.ToDecimal(dt2.Rows[0]["fcomamt"]); }

                                                if (dtoriinvd.Rows.Count > 0)
                                                {
                                                    foreach (DataRow dr in dt2.Rows)
                                                    {
                                                        dtoriinvd.ImportRow(dr);
                                                    }
                                                }
                                                else { dtoriinvd = dt2.Copy(); }
                                            }
                                        }
                                    }
                                }
                                dtoriinvd.Columns.Remove("trid1_fsi");
                                dtoriinvd.Columns.Remove("trid1_fsi2");
                                dtoriinvd.Columns.Remove("trid1_fupdsi");
                                dtoriinvd.Columns.Remove("trid1_fupdsi2");
                                dtoriinvd.Columns.Remove("flogseq");
                            }

                            isSuccuss = DBHelper.BulkInsertDataTable("oriinsll", dtoriinsll);
                            if (isSuccuss == true)
                            {
                                string updoriinstl = "update " + Dbm + "xsysparm set fnxtid=RIGHT('*********'+ LTRIM(STR(CAST('" + oriinstl_fctlid + "' as int)+1)), 10) where fidtype ='oriinsll'";
                                isSuccuss = DBHelper.ExecuteCommand(updoriinstl);

                                if (isSuccuss == true)
                                { isSuccuss = DBHelper.BulkInsertDataTable("oriinvld", dtoriinvd); }
                                if (isSuccuss == true)
                                {
                                    string updoriinvd = "update " + Dbm + "xsysparm set fnxtid=RIGHT('*********'+ LTRIM(STR(CAST('" + oriinvd_fctlid + "' as int)+1)), 10) where fidtype ='oriinvld'";
                                    isSuccuss = DBHelper.ExecuteCommand(updoriinvd);
                                }
                            }
                        }
                    }
                    else { isSuccuss = true; }
                }
                else { isSuccuss = true; }
                sdr.Close();
            }
            return isSuccuss;
        }

        bool u_genfacbinst()
        {
            bool isSuccuss = false;
            string cheSql = "select fgpm+fnpm+fcomamt+fbrkgeamt as total from orild1 where fctlid_ri ='" + fctlid + "' and fritype ='05'";
            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["odata"].ConnectionString);
            SqlCommand cmd = new SqlCommand(cheSql, con);
            con.Open();
            using (SqlDataReader sdr = cmd.ExecuteReader())
            {
                if (sdr.HasRows && sdr.Read())
                {
                    if (Convert.ToDecimal(sdr["total"].ToString()) != 0)
                    {
                        DataTable dtoriinsll = new DataTable();
                        DateTime time = DateTime.Now;
                        ArrayList updarr = new ArrayList();
                        string oriinstl = Fct.NewId("oriinsll");
                        string oriinstl_fctlid = "";
                        string oriinvd = Fct.NewId("oriinvld");
                        string oriinvd_fctlid = "";

                        string selsql = "select a.fctlid_ri as fctlid_ri, a.fctlid as fctlid_lc, a.fctlid_p as fctlid_p, a.fctlid_e as fctlid_e, b.fctlid as fctlid_i, " +
                                "a.fbus as fbus,'D01'+ c.fritype as fmodule, a.fpolno, a.fendtno, a.fuwyr, a.fclass, a.fsclass, " +
                                "b.facclass,a.fkind,c.fritype,c.fsec,c.fttycode,c.fttysec,c.fttyclass,b.finstall,b.ftotinstal,'" + oriinstl + "' as fctlid, " +
                                "a.fissdate,b.finvdate,b.fbilldate,null as fbkdate,a.fefffr,a.feffto,c.fgrnet,b.fpmcur, " +
                                "'' as fgpm,'' as fnpm, '' as fcomamt,'' as fbrkgeamt, 0,0,0,0,'' as fpayable, " +
                                "0,0,0,0,3,2,'" + InsEnvironment.LoginUser.GetUserCode() + "' as fupduser, '" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' as fupddate,  " +
                                "c.fgpm as trid1_fgpm, c.fnpm as trid1_fnpm, c.fcomamt as trid1_fcomamt,c.fbrkgeamt as trid1_fbrkgeamt, e.fgpm as ln_tfgpm, b.fgpm as oinvh_fgpm, e.fnpm as ln_tfnpm, b.fnpm as oinvh_fnpm " +
                                "from oril a  " +
                                "left join oinvh b on a.fctlid_e = b.fctlid_1 " +
                                "left join orild1 c on c.fctlid_lc = a.fctlid  " +
                                "where a.fctlid_ri ='" + fctlid + "' and c.fritype='05' " +
                                "order by b.finstall";
                        DataTable dt = DBHelper.GetDataSet(selsql);
                        if (dt.Rows.Count > 0)
                        {
                            for (int i = 0; i < dt.Rows.Count; i++)
                            {
                                oriinstl = (int.Parse(oriinstl) + 1).ToString().PadLeft(10, '0');
                                dt.Rows[i]["fctlid"] = oriinstl;
                                if (Convert.ToDecimal(dt.Rows[i]["ln_tfgpm"]) == 0)
                                {
                                    dt.Rows[i]["fgpm"] = 0;
                                }
                                else
                                {
                                    dt.Rows[i]["fgpm"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid1_fgpm"]) * Convert.ToDecimal(dt.Rows[i]["oinvh_fgpm"]) / Convert.ToDecimal(dt.Rows[i]["ln_tfgpm"]), 2, MidpointRounding.AwayFromZero);
                                }
                                if (Convert.ToDecimal(dt.Rows[i]["ln_tfnpm"]) == 0)
                                {
                                    dt.Rows[i]["fnpm"] = 0;
                                }
                                else
                                {
                                    dt.Rows[i]["fnpm"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid1_fnpm"]) * Convert.ToDecimal(dt.Rows[i]["oinvh_fnpm"]) / Convert.ToDecimal(dt.Rows[i]["ln_tfnpm"]), 2, MidpointRounding.AwayFromZero);
                                }
                                if ((dt.Rows[i]["fgrnet"].ToString() == "1" && Convert.ToDecimal(dt.Rows[i]["ln_tfgpm"]) == 0) || (dt.Rows[i]["fgrnet"].ToString() == "2" && Convert.ToDecimal(dt.Rows[i]["ln_tfnpm"]) == 0))
                                {
                                    dt.Rows[i]["fcomamt"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid1_fcomamt"]) / Convert.ToDecimal(dt.Rows[i]["ftotinstal"]), 2, MidpointRounding.AwayFromZero);
                                    dt.Rows[i]["fbrkgeamt"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid1_fbrkgeamt"]) / Convert.ToDecimal(dt.Rows[i]["ftotinstal"]), 2, MidpointRounding.AwayFromZero);
                                }
                                if (dt.Rows[i]["fgrnet"].ToString() == "1" && Convert.ToDecimal(dt.Rows[i]["ln_tfgpm"]) != 0)
                                {
                                    dt.Rows[i]["fcomamt"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid1_fcomamt"]) * Convert.ToDecimal(dt.Rows[i]["oinvh_fgpm"]) / Convert.ToDecimal(dt.Rows[i]["ln_tfgpm"]), 2, MidpointRounding.AwayFromZero);
                                    dt.Rows[i]["fbrkgeamt"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid1_fbrkgeamt"]) * Convert.ToDecimal(dt.Rows[i]["oinvh_fgpm"]) / Convert.ToDecimal(dt.Rows[i]["ln_tfgpm"]), 2, MidpointRounding.AwayFromZero);
                                }
                                if (dt.Rows[i]["fgrnet"].ToString() == "2" && Convert.ToDecimal(dt.Rows[i]["ln_tfnpm"]) != 0)
                                {
                                    dt.Rows[i]["fcomamt"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid1_fcomamt"]) * Convert.ToDecimal(dt.Rows[i]["oinvh_fnpm"]) / Convert.ToDecimal(dt.Rows[i]["ln_tfnpm"]), 2, MidpointRounding.AwayFromZero);
                                    dt.Rows[i]["fbrkgeamt"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid1_fbrkgeamt"]) * Convert.ToDecimal(dt.Rows[i]["oinvh_fnpm"]) / Convert.ToDecimal(dt.Rows[i]["ln_tfnpm"]), 2, MidpointRounding.AwayFromZero);
                                }
                                if (dt.Rows[i]["fgrnet"].ToString() == "1")
                                {
                                    dt.Rows[i]["fpayable"] = Convert.ToDecimal(dt.Rows[i]["fgpm"]) - Convert.ToDecimal(dt.Rows[i]["fcomamt"]);
                                }
                                else
                                {
                                    dt.Rows[i]["fpayable"] = Convert.ToDecimal(dt.Rows[i]["fnpm"]) - Convert.ToDecimal(dt.Rows[i]["fcomamt"]);
                                }
                                oriinstl_fctlid = dt.Rows[i]["fctlid"].ToString();
                            }
                            string loc = "select fctlid from oril where fctlid_ri ='" + fctlid + "'";
                            DataTable locDT = DBHelper.GetDataSet(loc);
                            for (int k = 0; k < locDT.Rows.Count; k++)
                            {
                                DataTable grpLoc = dt.Select("fctlid_lc='" + locDT.Rows[k]["fctlid"] + "'").CopyToDataTable();
                                if (grpLoc.Rows.Count > 0)
                                {
                                    decimal ln_tfgpm = 0, ln_tfnpm = 0, ln_tfcomamt = 0, ln_tfbrkgeamt = 0;
                                    for (int j = 0; j < grpLoc.Rows.Count; j++)
                                    {
                                        ln_tfgpm += Convert.ToDecimal(grpLoc.Rows[j]["fgpm"]);
                                        ln_tfnpm += Convert.ToDecimal(grpLoc.Rows[j]["fnpm"]);
                                        ln_tfcomamt += Convert.ToDecimal(grpLoc.Rows[j]["fcomamt"]);
                                        ln_tfbrkgeamt += Convert.ToDecimal(grpLoc.Rows[j]["fbrkgeamt"]);
                                    }
                                    grpLoc.Rows[0]["fgpm"] = Convert.ToDecimal(grpLoc.Rows[0]["fgpm"]) - Convert.ToDecimal(grpLoc.Rows[0]["trid1_fgpm"]) - ln_tfgpm;
                                    grpLoc.Rows[0]["fnpm"] = Convert.ToDecimal(grpLoc.Rows[0]["fnpm"]) - Convert.ToDecimal(grpLoc.Rows[0]["trid1_fnpm"]) - ln_tfnpm;
                                    grpLoc.Rows[0]["fcomamt"] = Convert.ToDecimal(grpLoc.Rows[0]["fcomamt"]) - Convert.ToDecimal(grpLoc.Rows[0]["trid1_fcomamt"]) - ln_tfcomamt;
                                    grpLoc.Rows[0]["fbrkgeamt"] = Convert.ToDecimal(grpLoc.Rows[0]["fbrkgeamt"]) - Convert.ToDecimal(grpLoc.Rows[0]["trid1_fbrkgeamt"]) - ln_tfbrkgeamt;
                                    if (grpLoc.Rows[0]["fgrnet"].ToString() == "1") { grpLoc.Rows[0]["fpayable"] = Convert.ToDecimal(grpLoc.Rows[0]["fgpm"]) - Convert.ToDecimal(grpLoc.Rows[0]["fcomamt"]); }
                                    else { grpLoc.Rows[0]["fpayable"] = Convert.ToDecimal(grpLoc.Rows[0]["fnpm"]) - Convert.ToDecimal(grpLoc.Rows[0]["fcomamt"]); }
                                }
                                if (dtoriinsll.Rows.Count > 0)
                                {
                                    foreach (DataRow dr in grpLoc.Rows)
                                    {
                                        dtoriinsll.ImportRow(dr);
                                    }
                                }
                                else { dtoriinsll = grpLoc.Copy(); }
                            }
                            dtoriinsll.Columns.Remove("trid1_fgpm"); dtoriinsll.Columns.Remove("trid1_fnpm"); dtoriinsll.Columns.Remove("trid1_fcomamt"); dtoriinsll.Columns.Remove("trid1_fbrkgeamt");
                            dtoriinsll.Columns.Remove("oinvh_fgpm"); dtoriinsll.Columns.Remove("ln_tfgpm"); dtoriinsll.Columns.Remove("oinvh_fnpm"); dtoriinsll.Columns.Remove("ln_tfnpm");
                            if (DToriinstl.Rows.Count > 0)
                            {
                                foreach (DataRow dr in dtoriinsll.Rows)
                                {
                                    DToriinstl.ImportRow(dr);
                                }
                            }
                            else { DToriinstl = dtoriinsll.Copy(); }

                            string seloriinvd = "select a.fctlid_ri as fctlid_ri,a.fctlid as fctlid_lc, '" + oriinstl + "' as fctlid_is, a.fctlid_p as fctlid_p, a.fctlid_e as fctlid_e, '' as fctlid_iv,b.fctlid as fctlid_i, " +
                                                "a.fbus as fbus,'D01'+ c.fritype as fmodule,a.frino, a.fpolno, a.fendtno, a.fuwyr, a.fclass, a.fsclass, " +
                                                "b.facclass,a.fkind,c.fritype,c.fsec,c.fttycode,c.fttysec,c.fttyclass,b.finstall,b.ftotinstal,'" + oriinvd + "'  as fctlid, " +
                                                "a.fissdate,b.finvdate,b.fbilldate,null as fbkdate,a.fefffr,a.feffto,c.fgrnet,'' as finvno, " +
                                                "case when e.fbrkr1='PRD001' then 'R' else 'B' end as fdbtrtype,case when e.fbrkr1='PRD001' then e.freinsr else e.fbrkr1 end as fdbtr, e.fbrkr1,'' as fbdesc, " +
                                                "e.freinsr,'' as frdesc,b.fpmcur, " +
                                                "'' as fgpm, '' as fnpm, '' as fcomamt, '' as fbrkgeamt,0,0,0,0, '' as fpayable,  " +
                                                "round(c.fsi * e.fshare/100,2) as fsi, round(c.fsi2 * e.fshare/100,2) as fsi2, round(c.fupdsi * e.fshare/100,2) as fupdsi, round(c.fupdsi2 * e.fshare/100,2) as fupdsi2,e.fshare, " +
                                                "c.fsi as trid1_fsi, c.fsi2 as trid1_fsi2, c.fupdsi as trid1_fupdsi,c.fupdsi2 as trid1_fupdsi2 " +
                                                "from oril a left join oinvh b on a.fctlid_e = b.fctlid_1 " +
                                                "left join orild1 c on c.fctlid_lc = a.fctlid  " +
                                                "left join polh d on d.fctlid = a.fctlid_e  " +
                                                "left join (select a.fid, b.freinsr,b.fbrkr1,fshare " +
                                                "from " + Dbm + "mfb a left join " + Dbm + "mfbinsr b on a.fctlid =b.fctlid_1) e on e.fid = c.fttycode " +
                                                "where a.fctlid_ri ='" + fctlid + "' and c.fritype='05' " +
                                                "order by b.finstall";
                            DataTable dt1 = DBHelper.GetDataSet(seloriinvd);
                            DataTable dtoriinvd = new DataTable();
                            if (dt1.Rows.Count > 0)
                            {
                                for (int k = 0; k < locDT.Rows.Count; k++)
                                {
                                    DataTable grpLoc = dt1.Select("fctlid_lc='" + locDT.Rows[k]["fctlid"] + "'").CopyToDataTable();
                                    if (grpLoc.Rows.Count > 0)
                                    {
                                        DataView dataView = grpLoc.DefaultView;
                                        DataTable dataTableDistinct = dataView.ToTable(true, "finstall");
                                        for (int i = 1; i < dataTableDistinct.Rows.Count + 1; i++)
                                        {
                                            DataTable dt2 = grpLoc.Select("finstall = " + i + "").CopyToDataTable();
                                            if (dt2.Rows.Count > 0)
                                            {
                                                decimal lc_tfgpm = 0, lc_tfnpm = 0, lc_tfcomamt = 0, lc_tfbrkgeamt = 0, lc_tfsi = 0, lc_tfsi2 = 0, lc_tfupdsi = 0, lc_tfupdsi2 = 0;
                                                DataTable dt3 = dtoriinsll.Select("finstall = " + i + " and fctlid_lc='" + locDT.Rows[k]["fctlid"] + "'").CopyToDataTable();
                                                for (int j = 0; j < dt2.Rows.Count; j++)
                                                {
                                                    dt2.Rows[j]["fctlid_is"] = dt3.Rows[0]["fctlid"].ToString();
                                                    oriinvd = (int.Parse(oriinvd) + 1).ToString().PadLeft(10, '0');
                                                    dt2.Rows[j]["fctlid"] = oriinvd;
                                                    dt2.Rows[j]["fgpm"] = Math.Round(Convert.ToDecimal(dt3.Rows[0]["fgpm"]) * Convert.ToDecimal(dt2.Rows[j]["fshare"]) / 100, 2, MidpointRounding.AwayFromZero);
                                                    dt2.Rows[j]["fnpm"] = Math.Round(Convert.ToDecimal(dt3.Rows[0]["fnpm"]) * Convert.ToDecimal(dt2.Rows[j]["fshare"]) / 100, 2, MidpointRounding.AwayFromZero);
                                                    dt2.Rows[j]["fcomamt"] = Math.Round(Convert.ToDecimal(dt3.Rows[0]["fcomamt"]) * Convert.ToDecimal(dt2.Rows[j]["fshare"]) / 100, 2, MidpointRounding.AwayFromZero);
                                                    dt2.Rows[j]["fbrkgeamt"] = Math.Round(Convert.ToDecimal(dt3.Rows[0]["fbrkgeamt"]) * Convert.ToDecimal(dt2.Rows[j]["fshare"]) / 100, 2, MidpointRounding.AwayFromZero);
                                                    if (dt2.Rows[j]["fgrnet"].ToString() == "1")
                                                    {
                                                        dt2.Rows[j]["fpayable"] = Convert.ToDecimal(dt2.Rows[j]["fgpm"]) - Convert.ToDecimal(dt2.Rows[j]["fcomamt"]);
                                                    }
                                                    else
                                                    {
                                                        dt2.Rows[j]["fpayable"] = Convert.ToDecimal(dt2.Rows[j]["fnpm"]) - Convert.ToDecimal(dt2.Rows[j]["fcomamt"]);
                                                    }
                                                    oriinvd_fctlid = dt2.Rows[j]["fctlid"].ToString();
                                                    lc_tfgpm += Convert.ToDecimal(dt2.Rows[j]["fgpm"]);
                                                    lc_tfnpm += Convert.ToDecimal(dt2.Rows[j]["fnpm"]);
                                                    lc_tfcomamt += Convert.ToDecimal(dt2.Rows[j]["fcomamt"]);
                                                    lc_tfbrkgeamt += Convert.ToDecimal(dt2.Rows[j]["fbrkgeamt"]);
                                                    lc_tfsi += Convert.ToDecimal(dt2.Rows[j]["fsi"]);
                                                    lc_tfsi2 += Convert.ToDecimal(dt2.Rows[j]["fsi2"]);
                                                    lc_tfupdsi += Convert.ToDecimal(dt2.Rows[j]["fupdsi"]);
                                                    lc_tfupdsi2 += Convert.ToDecimal(dt2.Rows[j]["fupdsi2"]);
                                                }
                                                dt2.Rows[0]["fgpm"] = Convert.ToDecimal(dt2.Rows[0]["fgpm"]) + Convert.ToDecimal(dt3.Rows[0]["fgpm"]) - lc_tfgpm;
                                                dt2.Rows[0]["fnpm"] = Convert.ToDecimal(dt2.Rows[0]["fnpm"]) + Convert.ToDecimal(dt3.Rows[0]["fnpm"]) - lc_tfnpm;
                                                dt2.Rows[0]["fcomamt"] = Convert.ToDecimal(dt2.Rows[0]["fcomamt"]) + Convert.ToDecimal(dt3.Rows[0]["fcomamt"]) - lc_tfcomamt;
                                                dt2.Rows[0]["fbrkgeamt"] = Convert.ToDecimal(dt2.Rows[0]["fbrkgeamt"]) + Convert.ToDecimal(dt3.Rows[0]["fbrkgeamt"]) - lc_tfbrkgeamt;
                                                dt2.Rows[0]["fsi"] = Convert.ToDecimal(dt2.Rows[0]["fsi"]) + Convert.ToDecimal(dt2.Rows[0]["trid1_fsi"]) - lc_tfsi;
                                                dt2.Rows[0]["fsi2"] = Convert.ToDecimal(dt2.Rows[0]["fsi2"]) + Convert.ToDecimal(dt2.Rows[0]["trid1_fsi2"]) - lc_tfsi2;
                                                dt2.Rows[0]["fupdsi"] = Convert.ToDecimal(dt2.Rows[0]["fupdsi"]) + Convert.ToDecimal(dt2.Rows[0]["trid1_fupdsi"]) - lc_tfupdsi;
                                                dt2.Rows[0]["fupdsi2"] = Convert.ToDecimal(dt2.Rows[0]["fupdsi2"]) + Convert.ToDecimal(dt2.Rows[0]["trid1_fupdsi2"]) - lc_tfupdsi2;
                                                if (dt2.Rows[0]["fgrnet"].ToString() == "1") { dt2.Rows[0]["fpayable"] = Convert.ToDecimal(dt2.Rows[0]["fgpm"]) - Convert.ToDecimal(dt2.Rows[0]["fcomamt"]); }
                                                else { dt2.Rows[0]["fpayable"] = Convert.ToDecimal(dt2.Rows[0]["fnpm"]) - Convert.ToDecimal(dt2.Rows[0]["fcomamt"]); }

                                                if (dtoriinvd.Rows.Count > 0)
                                                {
                                                    foreach (DataRow dr in dt2.Rows)
                                                    {
                                                        dtoriinvd.ImportRow(dr);
                                                    }
                                                }
                                                else { dtoriinvd = dt2.Copy(); }
                                            }
                                        }
                                    }
                                }
                                dtoriinvd.Columns.Remove("trid1_fsi");
                                dtoriinvd.Columns.Remove("trid1_fsi2");
                                dtoriinvd.Columns.Remove("trid1_fupdsi");
                                dtoriinvd.Columns.Remove("trid1_fupdsi2");
                            }
                            isSuccuss = DBHelper.BulkInsertDataTable("oriinstl", dt);
                            if (isSuccuss == true)
                            {
                                string updoriinstl = "update " + Dbm + "xsysparm set fnxtid=RIGHT('*********'+ LTRIM(STR(CAST('" + oriinstl_fctlid + "' as int)+1)), 10) where fidtype ='oriinsll'";
                                isSuccuss = DBHelper.ExecuteCommand(updoriinstl);

                                if (isSuccuss == true)
                                { isSuccuss = DBHelper.BulkInsertDataTable("oriinvd", dtoriinvd); }
                                if (isSuccuss == true)
                                {
                                    string updoriinvd = "update " + Dbm + "xsysparm set fnxtid=RIGHT('*********'+ LTRIM(STR(CAST('" + oriinvd_fctlid + "' as int)+1)), 10) where fidtype ='oriinvld'";
                                    isSuccuss = DBHelper.ExecuteCommand(updoriinvd);
                                }
                            }
                        }
                    }
                    else { isSuccuss = true; }
                }
                else { isSuccuss = true; }
                sdr.Close();
            }
            return isSuccuss;
        }

        bool u_genfacinst()
        {
            bool isSuccuss = false;
            string cheSql = "select fgpm+fnpm+fcomamt+fbrkgeamt as total from orild1 where fctlid_ri ='" + fctlid + "' and fritype ='04'";
            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["odata"].ConnectionString);
            SqlCommand cmd = new SqlCommand(cheSql, con);
            con.Open();
            using (SqlDataReader sdr = cmd.ExecuteReader())
            {
                if (sdr.HasRows && sdr.Read())
                {
                    if (Convert.ToDecimal(sdr["total"].ToString()) != 0)
                    {
                        DataTable dtoriinsll = new DataTable();
                        DataTable dtoriinvf = new DataTable();
                        DateTime time = DateTime.Now;
                        ArrayList updarr = new ArrayList();
                        string oriinstl = Fct.NewId("oriinsll");
                        string oriinstl_fctlid = "";
                        string oriinvf = Fct.NewId("oriinvlf");
                        string oriinvf_fctlid = "";

                        string selsql = "select a.fctlid_ri as fctlid_ri,a.fctlid as fctlid_lc, '" + oriinstl + "' as fctlid_is, a.fctlid_p as fctlid_p, a.fctlid_e as fctlid_e,b.fctlid as fctlid_i, '' as fctlid_iv, " +
                        "a.fbus as fbus,'D01'+ c.fritype as fmodule, a.frino, a.fkind,c.fritype,c.fsec, a.fpolno, a.fendtno, a.fuwyr, a.fclass, a.fsclass,b.facclass, " +
                        "c.fttycode,c.fttysec,c.fttyclass,b.finstall,b.ftotinstal,'' as fctlid, CONVERT(VARCHAR(19), a.fissdate, 120) as fissdate,CONVERT(VARCHAR(19),b.finvdate, 120) as finvdate,CONVERT(VARCHAR(19),b.fbilldate, 120) as fbilldate,null as fbkdate,CONVERT(VARCHAR(19),a.fefffr, 120) as fefffr,CONVERT(VARCHAR(19),a.feffto, 120) as feffto, " +
                        "d.fgrnet,d.fmethod,'' as finvno, case when d.fbrkr='PRD001' then 'R' else 'B' end as fdbtrtype, " +
                        "case when d.fbrkr='PRD001' then d.freinsr else d.fbrkr end as fdbtr,case when d.fbrkr='PRD001' then g.frdesc else h.fbdesc end as fdbdesc,  " +
                        "d.fbrkr,h.fbdesc, d.freinsr, g.frdesc, f.fclnt as fclnt,f.fsclnt as fsclnt, b.fpmcur, " +
                        "'' as fgpm, '' as fnpm, d.fcom, '' as fcomamt, d.fbrkge, '' as fbrkgeamt,0,0,0,0, '' as fpayable,0,'' as fremark,'' as fdoctype,3,2,'" + InsEnvironment.LoginUser.GetUserCode() + "' as fupduser, '" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' as fupddate,  " +
                        "d.fbrkgeamt as trid2_fbrkgeamt, d.fcomamt as trid2_fcomamt, d.fgpm as trid2_fgpm, b.fgpm as invh_fgpm,d.fnpm as trid2_fnpm, b.fnpm as invh_fnpm,ln_tinvgpm,ln_tinvnpm, d.fctlid as orid2_fctlid " +
                        "from oril a  " +
                        "left join oinvh b on a.fctlid_e = b.fctlid_1 " +
                        "left join (select fctlid_1,sum(fgpm) as ln_tinvgpm,sum(fnpm) as ln_tinvnpm from oinvh group by fctlid_1) e on e.fctlid_1 = a.fctlid_e " +
                        "left join orild1 c on c.fctlid_ri = a.fctlid_ri and c.fctlid_lc = a.fctlid " +
                        "left join orild2 d on d.fctlid_ri = a.fctlid_ri and c.fctlid_lc = d.fctlid_lc " +
                        "left join polh f on f.fctlid = a.fctlid_e " +
                        "left join (select fid,rtrim(fdesc) as frdesc from " + Dbm + "[mprdr] where ftype='R') g on g.fid = d.freinsr " +
                        "left join (select fid,rtrim(fdesc) as fbdesc from " + Dbm + "[mprdr] where ftype='B') h on h.fid = d.fbrkr " +
                        "where a.fctlid_ri ='" + fctlid + "' and c.fritype='04' " +
                        "order by d.fctlid,b.finstall";
                        DataTable dt = DBHelper.GetDataSet(selsql);
                        if (dt.Rows.Count > 0)
                        {
                            for (int i = 0; i < dt.Rows.Count; i++)
                            {
                                oriinvf = (int.Parse(oriinvf) + 1).ToString().PadLeft(10, '0');
                                dt.Rows[i]["fctlid"] = oriinvf;
                                if (Convert.ToDecimal(dt.Rows[i]["ln_tinvgpm"]) == 0)
                                {
                                    dt.Rows[i]["fgpm"] = 0;
                                }
                                else
                                {
                                    dt.Rows[i]["fgpm"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid2_fgpm"]) * Convert.ToDecimal(dt.Rows[i]["invh_fgpm"]) / Convert.ToDecimal(dt.Rows[i]["ln_tinvgpm"]), 2, MidpointRounding.AwayFromZero);
                                }
                                if (Convert.ToDecimal(dt.Rows[i]["ln_tinvnpm"]) == 0)
                                {
                                    dt.Rows[i]["fnpm"] = 0;
                                }
                                else
                                {
                                    dt.Rows[i]["fnpm"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid2_fnpm"]) * Convert.ToDecimal(dt.Rows[i]["invh_fnpm"]) / Convert.ToDecimal(dt.Rows[i]["ln_tinvnpm"]), 2, MidpointRounding.AwayFromZero);
                                }
                                if ((dt.Rows[i]["fgrnet"].ToString() == "1" && Convert.ToDecimal(dt.Rows[i]["ln_tinvgpm"]) == 0) || (dt.Rows[i]["fgrnet"].ToString() == "2" && Convert.ToDecimal(dt.Rows[i]["ln_tinvnpm"]) == 0))
                                {
                                    dt.Rows[i]["fcomamt"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid2_fcomamt"]) / Convert.ToDecimal(dt.Rows[i]["ftotinstal"]), 2);
                                    dt.Rows[i]["fbrkgeamt"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid2_fbrkgeamt"]) / Convert.ToDecimal(dt.Rows[i]["ftotinstal"]), 2);
                                }
                                if (dt.Rows[i]["fgrnet"].ToString() == "1" && Convert.ToDecimal(dt.Rows[i]["ln_tinvgpm"]) != 0)
                                {
                                    dt.Rows[i]["fcomamt"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid2_fcomamt"]) * Convert.ToDecimal(dt.Rows[i]["invh_fgpm"]) / Convert.ToDecimal(dt.Rows[i]["ln_tinvgpm"]), 2, MidpointRounding.AwayFromZero);
                                    dt.Rows[i]["fbrkgeamt"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid2_fbrkgeamt"]) * Convert.ToDecimal(dt.Rows[i]["invh_fgpm"]) / Convert.ToDecimal(dt.Rows[i]["ln_tinvgpm"]), 2, MidpointRounding.AwayFromZero);
                                }
                                if (dt.Rows[i]["fgrnet"].ToString() == "2" && Convert.ToDecimal(dt.Rows[i]["ln_tfnpm"]) != 0)
                                {
                                    dt.Rows[i]["fcomamt"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid2_fcomamt"]) * Convert.ToDecimal(dt.Rows[i]["invh_fnpm"]) / Convert.ToDecimal(dt.Rows[i]["ln_tinvnpm"]), 2, MidpointRounding.AwayFromZero);
                                    dt.Rows[i]["fbrkgeamt"] = -Math.Round(Convert.ToDecimal(dt.Rows[i]["trid2_fbrkgeamt"]) * Convert.ToDecimal(dt.Rows[i]["invh_fnpm"]) / Convert.ToDecimal(dt.Rows[i]["ln_tinvnpm"]), 2, MidpointRounding.AwayFromZero);
                                }
                                if (dt.Rows[i]["fgrnet"].ToString() == "1")
                                {
                                    dt.Rows[i]["fpayable"] = Convert.ToDecimal(dt.Rows[i]["fgpm"]) - Convert.ToDecimal(dt.Rows[i]["fcomamt"]);
                                }
                                else
                                {
                                    dt.Rows[i]["fpayable"] = Convert.ToDecimal(dt.Rows[i]["fnpm"]) - Convert.ToDecimal(dt.Rows[i]["fcomamt"]);
                                }
                                if (Convert.ToDecimal(dt.Rows[i]["fpayable"].ToString()) >= 0)
                                {
                                    dt.Rows[i]["fdoctype"] = "1";
                                }
                                else
                                {
                                    dt.Rows[i]["fdoctype"] = "2";
                                }
                                oriinvf_fctlid = dt.Rows[i]["fctlid"].ToString();
                            }
                            string loc = "select fctlid from oril where fctlid_ri ='" + fctlid + "'";
                            DataTable locDT = DBHelper.GetDataSet(loc);
                            DataTable grpReinsr = new DataTable();
                            for (int n = 0; n < locDT.Rows.Count; n++)
                            {
                                string reinsr = "select freinsr from orild2 where fctlid_ri ='" + fctlid + "' and fritype='04' and fctlid_lc ='" + locDT.Rows[n]["fctlid"] + "'";
                                DataTable reinsrDT = DBHelper.GetDataSet(reinsr);
                                for (int k = 0; k < reinsrDT.Rows.Count; k++)
                                {
                                    grpReinsr = dt.Select("freinsr='" + reinsrDT.Rows[k]["freinsr"] + "' and fctlid_lc ='" + locDT.Rows[n]["fctlid"] + "'").CopyToDataTable();
                                    if (grpReinsr.Rows.Count > 0)
                                    {
                                        decimal ln_tfgpm = 0, ln_tfnpm = 0, ln_tfcomamt = 0, ln_tfbrkgeamt = 0;
                                        for (int j = 0; j < grpReinsr.Rows.Count; j++)
                                        {
                                            ln_tfgpm += Convert.ToDecimal(grpReinsr.Rows[j]["fgpm"]);
                                            ln_tfnpm += Convert.ToDecimal(grpReinsr.Rows[j]["fnpm"]);
                                            ln_tfcomamt += Convert.ToDecimal(grpReinsr.Rows[j]["fcomamt"]);
                                            ln_tfbrkgeamt += Convert.ToDecimal(grpReinsr.Rows[j]["fbrkgeamt"]);
                                        }
                                        grpReinsr.Rows[0]["fgpm"] = Convert.ToDecimal(grpReinsr.Rows[0]["fgpm"]) - Convert.ToDecimal(grpReinsr.Rows[0]["trid2_fgpm"]) - ln_tfgpm;
                                        grpReinsr.Rows[0]["fnpm"] = Convert.ToDecimal(grpReinsr.Rows[0]["fnpm"]) - Convert.ToDecimal(grpReinsr.Rows[0]["trid2_fnpm"]) - ln_tfnpm;
                                        grpReinsr.Rows[0]["fcomamt"] = Convert.ToDecimal(grpReinsr.Rows[0]["fcomamt"]) - Convert.ToDecimal(grpReinsr.Rows[0]["trid2_fcomamt"]) - ln_tfcomamt;
                                        grpReinsr.Rows[0]["fbrkgeamt"] = Convert.ToDecimal(grpReinsr.Rows[0]["fbrkgeamt"]) - Convert.ToDecimal(grpReinsr.Rows[0]["trid2_fbrkgeamt"]) - ln_tfbrkgeamt;
                                        if (grpReinsr.Rows[0]["fgrnet"].ToString() == "1") { grpReinsr.Rows[0]["fpayable"] = Convert.ToDecimal(grpReinsr.Rows[0]["fgpm"]) - Convert.ToDecimal(grpReinsr.Rows[0]["fcomamt"]); }
                                        else { grpReinsr.Rows[0]["fpayable"] = Convert.ToDecimal(grpReinsr.Rows[0]["fnpm"]) - Convert.ToDecimal(grpReinsr.Rows[0]["fcomamt"]); }
                                    }
                                    if (dtoriinvf.Rows.Count > 0)
                                    {
                                        foreach (DataRow dr in grpReinsr.Rows)
                                        {
                                            dtoriinvf.ImportRow(dr);
                                        }
                                    }
                                    else { dtoriinvf = grpReinsr.Copy(); }
                                }
                            }

                            dtoriinvf.Columns.Remove("orid2_fctlid"); dtoriinvf.Columns.Remove("trid2_fgpm");
                            dtoriinvf.Columns.Remove("trid2_fnpm"); dtoriinvf.Columns.Remove("trid2_fcomamt"); dtoriinvf.Columns.Remove("trid2_fbrkgeamt");
                            dtoriinvf.Columns.Remove("invh_fgpm"); dtoriinvf.Columns.Remove("ln_tinvgpm"); dtoriinvf.Columns.Remove("invh_fnpm"); dtoriinvf.Columns.Remove("ln_tinvnpm");

                            string sql = "select a.fctlid_ri as fctlid_ri, a.fctlid as fctlid_lc, a.fctlid_p as fctlid_p, a.fctlid_e as fctlid_e, b.fctlid as fctlid_i, " +
                                "a.fbus as fbus,'D01'+ c.fritype as fmodule, a.fpolno, a.fendtno, a.fuwyr, a.fclass, a.fsclass, " +
                                "b.facclass,a.fkind,c.fritype,c.fsec,c.fttycode,c.fttysec,c.fttyclass,b.finstall,b.ftotinstal,'" + oriinstl + "' as fctlid, " +
                                "a.fissdate,b.finvdate,b.fbilldate,null as fbkdate,a.fefffr,a.feffto,c.fgrnet,b.fpmcur, " +
                                "'' as fgpm,'' as fnpm, '' as fcomamt,'' as fbrkgeamt, 0,0,0,0,'' as fpayable, " +
                                "0,0,0,0,3,2,'" + InsEnvironment.LoginUser.GetUserCode() + "' as fupduser, '" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' as fupddate  " +
                                "from oril a  " +
                                "left join oinvh b on a.fctlid_e = b.fctlid_1 " +
                                "left join orild1 c on c.fctlid_lc = a.fctlid  " +
                                "where a.fctlid_ri ='" + fctlid + "' and c.fritype='04' " +
                                "order by b.finstall";
                            DataTable dtriinstl = DBHelper.GetDataSet(sql);
                            if (dtriinstl.Rows.Count > 0)
                            {
                                for (int k = 0; k < locDT.Rows.Count; k++)
                                {
                                    DataTable grpLoc = dtriinstl.Select("fctlid_lc='" + locDT.Rows[k]["fctlid"] + "'").CopyToDataTable();
                                    if (grpLoc.Rows.Count > 0)
                                    {
                                        for (int i = 0; i < grpLoc.Rows.Count; i++)
                                        {
                                            oriinstl = (int.Parse(oriinstl) + 1).ToString().PadLeft(10, '0');
                                            DataTable dt2 = dtoriinvf.Select("fctlid_lc='" + locDT.Rows[k]["fctlid"] + "'").CopyToDataTable();
                                            decimal ln_tfpayable = 0, ln_tfgpm = 0, ln_tfnpm = 0, ln_tfcomamt = 0, ln_tfbrkgeamt = 0;
                                            for (int j = 0; j < dt2.Rows.Count; j++)
                                            {
                                                ln_tfgpm += Convert.ToDecimal(dt2.Rows[j]["fgpm"]);
                                                ln_tfnpm += Convert.ToDecimal(dt2.Rows[j]["fnpm"]);
                                                ln_tfcomamt += Convert.ToDecimal(dt2.Rows[j]["fcomamt"]);
                                                ln_tfbrkgeamt += Convert.ToDecimal(dt2.Rows[j]["fbrkgeamt"]);
                                                ln_tfpayable += Convert.ToDecimal(dt2.Rows[j]["fpayable"]);
                                            }
                                            foreach (DataRow dr in dtoriinvf.Rows)
                                            {
                                                if (dr["finstall"].ToString() == (i + 1).ToString() && dr["fctlid_lc"].ToString() == locDT.Rows[k]["fctlid"].ToString())
                                                {
                                                    dr["fctlid_is"] = oriinstl;
                                                }
                                            }
                                            grpLoc.Rows[i]["fctlid"] = oriinstl;
                                            grpLoc.Rows[i]["fgpm"] = ln_tfgpm;
                                            grpLoc.Rows[i]["fnpm"] = ln_tfnpm;
                                            grpLoc.Rows[i]["fcomamt"] = ln_tfcomamt;
                                            grpLoc.Rows[i]["fbrkgeamt"] = ln_tfbrkgeamt;
                                            grpLoc.Rows[i]["fpayable"] = ln_tfpayable;
                                            oriinstl_fctlid = grpLoc.Rows[i]["fctlid"].ToString();
                                        }
                                    }
                                    if (dtoriinsll.Rows.Count > 0)
                                    {
                                        foreach (DataRow dr in grpLoc.Rows)
                                        {
                                            dtoriinsll.ImportRow(dr);
                                        }
                                    }
                                    else { dtoriinsll = grpLoc.Copy(); }
                                }
                                if (DToriinstl.Rows.Count > 0)
                                {
                                    foreach (DataRow dr in dtoriinsll.Rows)
                                    {
                                        DToriinstl.ImportRow(dr);
                                    }
                                }
                                else { DToriinstl = dtoriinsll.Copy(); }
                            }

                            isSuccuss = DBHelper.BulkInsertDataTable("oriinsll", dtoriinsll);
                            if (isSuccuss == true)
                            {
                                string updoriinstl = "update " + Dbm + "xsysparm set fnxtid=RIGHT('*********'+ LTRIM(STR(CAST('" + oriinstl_fctlid + "' as int)+1)), 10) where fidtype ='oriinsll'";
                                isSuccuss = DBHelper.ExecuteCommand(updoriinstl);

                                if (isSuccuss == true)
                                { isSuccuss = DBHelper.BulkInsertDataTable("oriinvlf", dtoriinvf); }
                                if (isSuccuss == true)
                                {
                                    string updoriinvf = "update " + Dbm + "xsysparm set fnxtid=RIGHT('*********'+ LTRIM(STR(CAST('" + oriinvf_fctlid + "' as int)+1)), 10) where fidtype ='oriinvlf'";
                                    isSuccuss = DBHelper.ExecuteCommand(updoriinvf);
                                }
                            }
                        }
                        sdr.Close();
                    }
                    else { isSuccuss = true; }
                }
                else { isSuccuss = true; }
            }
            return isSuccuss;
        }

        bool u_genrtninst()
        {
            bool isSuccuss = false;
            string cheSql = "select fgpm+fnpm+fcomamt+fbrkgeamt as total from orild1 where fctlid_ri ='" + fctlid + "' and fritype ='01'";
            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["odata"].ConnectionString);
            SqlCommand cmd = new SqlCommand(cheSql, con);
            con.Open();
            using (SqlDataReader sdr = cmd.ExecuteReader())
            {
                if (sdr.HasRows && sdr.Read())
                {
                    if (Convert.ToDecimal(sdr["total"].ToString()) != 0)
                    {
                        DataTable dtoriinsll = new DataTable();
                        DateTime time = DateTime.Now;
                        ArrayList updarr = new ArrayList();
                        string oriinstl = Fct.NewId("oriinsll");
                        string oriinstl_fctlid = "";

                        string selsql = "select a.fctlid_ri as fctlid_ri, a.fctlid as fctlid_lc, a.fctlid_p as fctlid_p, a.fctlid_e as fctlid_e, b.fctlid as fctlid_i, " +
                                "a.fbus as fbus,'D01'+ c.fritype as fmodule, a.fpolno, a.fendtno, a.fuwyr, a.fclass, a.fsclass, " +
                                "b.facclass,a.fkind,c.fritype,c.fsec,c.fttycode,c.fttysec,c.fttyclass,b.finstall,b.ftotinstal,'" + oriinstl + "' as fctlid, " +
                                "a.fissdate,b.finvdate,b.fbilldate,null as fbkdate,a.fefffr,a.feffto,c.fgrnet,b.fpmcur, " +
                                "'' as fgpm,'' as fnpm, '0' as fcomamt,'0' as fbrkgeamt, 0,0,0,0,'0' as fpayable, " +
                                "0,0,0,0,3,2,'" + InsEnvironment.LoginUser.GetUserCode() + "' as fupduser, '" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' as fupddate,  " +
                                "c.fgpm as trid1_fgpm, c.fnpm as trid1_fnpm, b.fgpm as oinvh_fgpm,b.fnpm as oinvh_fnpm, ln_tfnpm,ln_tfgpm, a.fgpm as m_fgpm,a.fnpm as m_fnpm " +
                                "from oril a  " +
                                "left join oinvh b on a.fctlid_e = b.fctlid_1 " +
                                "left join orild1 c on c.fctlid_lc = a.fctlid  " +
                                "left join (select fctlid_1,sum(fgpm) as ln_tfgpm,sum(fnpm) as ln_tfnpm from oinvh group by fctlid_1) e on e.fctlid_1=a.fctlid_e " +
                                "where a.fctlid_ri ='" + fctlid + "' and c.fritype='01' " +
                                "order by b.finstall";
                        DataTable dt = DBHelper.GetDataSet(selsql);
                        if (dt.Rows.Count > 0)
                        {
                            string loc = "select fctlid from oril where fctlid_ri ='" + fctlid + "'";
                            DataTable locDT = DBHelper.GetDataSet(loc);
                            for (int k = 0; k < locDT.Rows.Count; k++)
                            {
                                DataTable grpLoc = dt.Select("fctlid_lc='" + locDT.Rows[k]["fctlid"] + "'").CopyToDataTable();
                                if (grpLoc.Rows.Count > 0)
                                {
                                    decimal ln_tfgpm = 0, ln_tfnpm = 0, ln_fgpm = 0, ln_fnpm = 0;
                                    decimal ln_ttyfgpm = 0, ln_facbfgpm = 0, fac_fgpm = 0, ln_ttyfnpm = 0, ln_facbfnpm = 0, fac_fnpm = 0;
                                    for (int j = 0; j < grpLoc.Rows.Count; j++)
                                    {
                                        oriinstl = (int.Parse(oriinstl) + 1).ToString().PadLeft(10, '0');
                                        grpLoc.Rows[j]["fctlid"] = oriinstl;
                                        if (Convert.ToDecimal(grpLoc.Rows[j]["ln_tfgpm"]) == 0)
                                        {
                                            ln_fgpm = 0;
                                        }
                                        else
                                        {
                                            ln_fgpm = Math.Round(Convert.ToDecimal(grpLoc.Rows[j]["oinvh_fgpm"]) * Convert.ToDecimal(grpLoc.Rows[j]["m_fgpm"]) / Convert.ToDecimal(grpLoc.Rows[j]["ln_tfgpm"]), 2, MidpointRounding.AwayFromZero);
                                        }
                                        if (Convert.ToDecimal(grpLoc.Rows[j]["ln_tfnpm"]) == 0)
                                        {
                                            ln_fnpm = 0;
                                        }
                                        else
                                        {
                                            ln_fnpm = Math.Round(Convert.ToDecimal(grpLoc.Rows[j]["oinvh_fnpm"]) * Convert.ToDecimal(grpLoc.Rows[j]["m_fnpm"]) / Convert.ToDecimal(grpLoc.Rows[j]["ln_tfnpm"]), 2, MidpointRounding.AwayFromZero);
                                        }
                                        if (DToriinstl.Rows.Count > 0) {
                                        if (DToriinstl.Select("fritype ='02'").Length > 0)
                                        {
                                            DataTable dt02 = DToriinstl.Select("fritype ='02' and fctlid_lc='" + locDT.Rows[k]["fctlid"] + "'").CopyToDataTable();
                                            if (dt02.Rows.Count > 0)
                                            {
                                                ln_ttyfgpm = Convert.ToDecimal(dt02.Rows[j]["fgpm"]);
                                                ln_ttyfnpm = Convert.ToDecimal(dt02.Rows[j]["fnpm"]);
                                            }
                                        }
                                        if (DToriinstl.Select("fritype ='05'").Length > 0)
                                        {
                                            DataTable dt05 = DToriinstl.Select("fritype ='05' and fctlid_lc='" + locDT.Rows[k]["fctlid"] + "'").CopyToDataTable();
                                            if (dt05.Rows.Count > 0)
                                            {
                                                ln_facbfgpm = Convert.ToDecimal(dt05.Rows[j]["fgpm"]);
                                                ln_facbfnpm = Convert.ToDecimal(dt05.Rows[j]["fnpm"]);
                                            }
                                        }
                                        if (DToriinstl.Select("fritype ='04'").Length > 0)
                                        {
                                            DataTable dt04 = DToriinstl.Select("fritype ='04' and fctlid_lc='" + locDT.Rows[k]["fctlid"] + "'").CopyToDataTable();
                                            if (dt04.Rows.Count > 0)
                                            {
                                                fac_fgpm = Convert.ToDecimal(dt04.Rows[j]["fgpm"]);
                                                fac_fnpm = Convert.ToDecimal(dt04.Rows[j]["fnpm"]);
                                            }
                                        }
                                        }
                                        grpLoc.Rows[j]["fgpm"] = Math.Round(-ln_fgpm - ln_ttyfgpm - ln_facbfgpm - fac_fgpm, 2, MidpointRounding.AwayFromZero);
                                        grpLoc.Rows[j]["fnpm"] = Math.Round(-ln_fnpm - ln_ttyfnpm - ln_facbfnpm - fac_fnpm, 2, MidpointRounding.AwayFromZero);
                                        oriinstl_fctlid = grpLoc.Rows[j]["fctlid"].ToString();
                                        ln_tfgpm += Convert.ToDecimal(grpLoc.Rows[j]["fgpm"]);
                                        ln_tfnpm += Convert.ToDecimal(grpLoc.Rows[j]["fnpm"]);
                                    }
                                    grpLoc.Rows[0]["fgpm"] = Convert.ToDecimal(grpLoc.Rows[0]["fgpm"]) - Convert.ToDecimal(grpLoc.Rows[0]["trid1_fgpm"]) - ln_tfgpm;
                                    grpLoc.Rows[0]["fnpm"] = Convert.ToDecimal(grpLoc.Rows[0]["fnpm"]) - Convert.ToDecimal(grpLoc.Rows[0]["trid1_fnpm"]) - ln_tfnpm;
                                }
                                if (dtoriinsll.Rows.Count > 0)
                                {
                                    foreach (DataRow dr in grpLoc.Rows)
                                    {
                                        dtoriinsll.ImportRow(dr);
                                    }
                                }
                                else { dtoriinsll = grpLoc.Copy(); }
                            }
                            dtoriinsll.Columns.Remove("trid1_fgpm"); dtoriinsll.Columns.Remove("trid1_fnpm"); dtoriinsll.Columns.Remove("m_fgpm"); dtoriinsll.Columns.Remove("m_fnpm");
                            dtoriinsll.Columns.Remove("oinvh_fgpm"); dtoriinsll.Columns.Remove("ln_tfgpm"); dtoriinsll.Columns.Remove("oinvh_fnpm"); dtoriinsll.Columns.Remove("ln_tfnpm");
                            if (DToriinstl.Rows.Count > 0)
                            {
                                foreach (DataRow dr in dtoriinsll.Rows)
                                {
                                    DToriinstl.ImportRow(dr);
                                }
                            }
                            else { DToriinstl = dtoriinsll.Copy(); }
                        }
                        isSuccuss = DBHelper.BulkInsertDataTable("oriinsll", dtoriinsll);
                        if (isSuccuss == true)
                        {
                            string updoriinstl = "update " + Dbm + "xsysparm set fnxtid=RIGHT('*********'+ LTRIM(STR(CAST('" + oriinstl_fctlid + "' as int)+1)), 10) where fidtype ='oriinsll'";
                            isSuccuss = DBHelper.ExecuteCommand(updoriinstl);
                        }
                    }
                    else { isSuccuss = true; }
                }
                else { isSuccuss = true; }
            }
            return isSuccuss;
        }

        bool u_allttyinst()
        {
            bool isSuccuss = false;
            string oriinstl = Fct.NewId("oriinstl");
            string oriinstl_fctlid = "";
            string oriinvd = Fct.NewId("oriinvd");
            string oriinvd_fctlid = "";
            DateTime time = DateTime.Now;
            SqlParameter[] param = new SqlParameter[] {
                    new SqlParameter("@fctlid", fctlid)};
            DataSet allttyinst = DBHelper.GetDtSetProd("allttyinst",Dbo, param);
            if (allttyinst.Tables.Count > 0 && allttyinst.Tables[0].Rows.Count > 0 && allttyinst.Tables[1].Rows.Count > 0)
            {
                foreach (DataRow dr in allttyinst.Tables[0].Rows)
                {
                    oriinstl = (int.Parse(oriinstl) + 1).ToString().PadLeft(10, '0');
                    dr["fctlid"] = oriinstl;
                    oriinstl_fctlid = dr["fctlid"].ToString();
                    dr["fupduser"] = InsEnvironment.LoginUser.GetUserCode();
                    dr["fupddate"] = time.ToString("yyyy-MM-dd HH:mm:ss");
                    foreach (DataRow dr1 in allttyinst.Tables[1].Rows)
                    {
                        if (dr1["finstall"].ToString() == dr["finstall"].ToString())
                        {
                            oriinvd = (int.Parse(oriinvd) + 1).ToString().PadLeft(10, '0');
                            dr1["fctlid"] = oriinvd;
                            oriinvd_fctlid = dr1["fctlid"].ToString();
                            dr1["fctlid_2"] = oriinstl;
                        }
                    }
                }
                isSuccuss = DBHelper.BulkInsertDataTable("oriinstl", allttyinst.Tables[0]);
                if (isSuccuss == true)
                {
                    string updoriinstl = "update " + Dbm + "xsysparm set fnxtid=RIGHT('*********'+ LTRIM(STR(CAST('" + oriinstl_fctlid + "' as int)+1)), 10) where fidtype ='oriinstl'";
                    isSuccuss = DBHelper.ExecuteCommand(updoriinstl);
                }
                if (isSuccuss == true)
                {
                    isSuccuss = DBHelper.BulkInsertDataTable("oriinvd", allttyinst.Tables[1]);
                }
                if (isSuccuss == true)
                {
                    string updoriinstl = "update " + Dbm + "xsysparm set fnxtid=RIGHT('*********'+ LTRIM(STR(CAST('" + oriinvd_fctlid + "' as int)+1)), 10) where fidtype ='oriinvd'";
                    isSuccuss = DBHelper.ExecuteCommand(updoriinstl);
                }
            }
            else { isSuccuss = true; }
            return isSuccuss;
        }

        bool u_allfacbinst()
        {
            bool isSuccuss = false;
            string oriinstl = Fct.NewId("oriinstl");
            string oriinstl_fctlid = "";
            string oriinvd = Fct.NewId("oriinvd");
            string oriinvd_fctlid = "";
            DateTime time = DateTime.Now;
            SqlParameter[] param = new SqlParameter[] {
                    new SqlParameter("@fctlid", fctlid)};
            DataSet allttyinst = DBHelper.GetDtSetProd("allfacbinst",Dbo, param);
            if (allttyinst.Tables.Count > 0 && allttyinst.Tables[0].Rows.Count > 0 && allttyinst.Tables[1].Rows.Count > 0)
            {
                foreach (DataRow dr in allttyinst.Tables[0].Rows)
                {
                    oriinstl = (int.Parse(oriinstl) + 1).ToString().PadLeft(10, '0');
                    dr["fctlid"] = oriinstl;
                    oriinstl_fctlid = dr["fctlid"].ToString();
                    dr["fupduser"] = InsEnvironment.LoginUser.GetUserCode();
                    dr["fupddate"] = time.ToString("yyyy-MM-dd HH:mm:ss");
                    foreach (DataRow dr1 in allttyinst.Tables[1].Rows)
                    {
                        if (dr1["finstall"].ToString() == dr["finstall"].ToString())
                        {
                            oriinvd = (int.Parse(oriinvd) + 1).ToString().PadLeft(10, '0');
                            dr1["fctlid"] = oriinvd;
                            oriinvd_fctlid = dr1["fctlid"].ToString();
                            dr1["fctlid_2"] = oriinstl;
                        }
                    }
                }
                isSuccuss = DBHelper.BulkInsertDataTable("oriinstl", allttyinst.Tables[0]);
                if (isSuccuss == true)
                {
                    string updoriinstl = "update " + Dbm + "xsysparm set fnxtid=RIGHT('*********'+ LTRIM(STR(CAST('" + oriinstl_fctlid + "' as int)+1)), 10) where fidtype ='oriinstl'";
                    isSuccuss = DBHelper.ExecuteCommand(updoriinstl);
                }
                if (isSuccuss == true)
                {
                    isSuccuss = DBHelper.BulkInsertDataTable("oriinvd", allttyinst.Tables[1]);
                }
                if (isSuccuss == true)
                {
                    string updoriinstl = "update " + Dbm + "xsysparm set fnxtid=RIGHT('*********'+ LTRIM(STR(CAST('" + oriinstl_fctlid + "' as int)+1)), 10) where fidtype ='oriinvd'";
                    isSuccuss = DBHelper.ExecuteCommand(updoriinstl);
                }
            }
            else { isSuccuss = true; }
            return isSuccuss;
        }

        bool u_allrtninst()
        {
            bool isSuccuss = false;
            string oriinstl = Fct.NewId("oriinstl");
            string oriinstl_fctlid = "";
            DateTime time = DateTime.Now;
            SqlParameter[] param = new SqlParameter[] {
                    new SqlParameter("@fctlid", fctlid)};
            DataTable allrtninst = DBHelper.GetDataSetProd("allrtninst",Dbo, param);
            if (allrtninst.Rows.Count > 0)
            {
                foreach (DataRow dr in allrtninst.Rows)
                {
                    oriinstl = (int.Parse(oriinstl) + 1).ToString().PadLeft(10, '0');
                    dr["fctlid"] = oriinstl;
                    oriinstl_fctlid = dr["fctlid"].ToString();
                    dr["fupduser"] = InsEnvironment.LoginUser.GetUserCode();
                    dr["fupddate"] = time.ToString("yyyy-MM-dd HH:mm:ss");
                }
                isSuccuss = DBHelper.BulkInsertDataTable("oriinstl", allrtninst);
                if (isSuccuss == true)
                {
                    string updoriinstl = "update " + Dbm + "xsysparm set fnxtid=RIGHT('*********'+ LTRIM(STR(CAST('" + oriinstl_fctlid + "' as int)+1)), 10) where fidtype ='oriinstl'";
                    isSuccuss = DBHelper.ExecuteCommand(updoriinstl);
                }
            }
            else { isSuccuss = true; }
            return isSuccuss;
        }

        bool u_allfacinst()
        {
            bool isSuccuss = false;
            string oriinstl = Fct.NewId("oriinstl");
            string oriinstl_fctlid = "";
            string oriinvf = Fct.NewId("oriinvf");
            string oriinvf_fctlid = "";
            DateTime time = DateTime.Now;
            SqlParameter[] param = new SqlParameter[] {
                    new SqlParameter("@fctlid", fctlid)};
            DataSet allfacinst = DBHelper.GetDtSetProd("allfacinst",Dbo, param);
            if (allfacinst.Tables.Count > 0 && allfacinst.Tables[0].Rows.Count > 0 && allfacinst.Tables[1].Rows.Count > 0)
            {
                foreach (DataRow dr in allfacinst.Tables[0].Rows)
                {
                    oriinstl = (int.Parse(oriinstl) + 1).ToString().PadLeft(10, '0');
                    dr["fctlid"] = oriinstl;
                    oriinstl_fctlid = dr["fctlid"].ToString();
                    dr["fupduser"] = InsEnvironment.LoginUser.GetUserCode();
                    dr["fupddate"] = time.ToString("yyyy-MM-dd HH:mm:ss");
                    foreach (DataRow dr1 in allfacinst.Tables[1].Rows)
                    {
                        if (dr1["finstall"].ToString() == dr["finstall"].ToString())
                        {
                            oriinvf = (int.Parse(oriinvf) + 1).ToString().PadLeft(10, '0');
                            dr1["fctlid"] = oriinvf;
                            oriinvf_fctlid = dr1["fctlid"].ToString();
                            dr1["fctlid_2"] = oriinstl;
                            dr1["fupduser"] = InsEnvironment.LoginUser.GetUserCode();
                            dr1["fupddate"] = time.ToString("yyyy-MM-dd HH:mm:ss");
                        }
                    }
                }
                isSuccuss = DBHelper.BulkInsertDataTable("oriinstl", allfacinst.Tables[0]);
                if (isSuccuss == true)
                {
                    string updoriinstl = "update " + Dbm + "xsysparm set fnxtid=RIGHT('*********'+ LTRIM(STR(CAST('" + oriinstl_fctlid + "' as int)+1)), 10) where fidtype ='oriinstl'";
                    isSuccuss = DBHelper.ExecuteCommand(updoriinstl);
                }
                if (isSuccuss == true)
                {
                    isSuccuss = DBHelper.BulkInsertDataTable("oriinvf", allfacinst.Tables[1]);
                }
                if (isSuccuss == true)
                {
                    string updoriinstl = "update " + Dbm + "xsysparm set fnxtid=RIGHT('*********'+ LTRIM(STR(CAST('" + oriinvf_fctlid + "' as int)+1)), 10) where fidtype ='oriinvf'";
                    isSuccuss = DBHelper.ExecuteCommand(updoriinstl);
                }
            }
            else { isSuccuss = true; }
            return isSuccuss;
        }

        bool u_updrih() { 
            bool isSuccuss = false;
            string orid1 = Fct.NewId("orid1");
            string orid1_fctlid = "";
            string orid2 = Fct.NewId("orid2");
            string orid2_fctlid = "";

            string orih = "UPDATE orih SET fsi_c = b.fsi_c, fgpm_c = b.fgpm_c, fnpm_c = b.fnpm_c  " +
            "FROM orih a ,(select sum(fsi_c) as fsi_c,sum(fgpm_c) as fgpm_c,sum(fnpm_c) as fnpm_c, " +
            "fctlid_ri from oril where fctlid_ri='" + fctlid + "' group by fctlid_ri) b where a.fctlid = b.fctlid_ri";
            isSuccuss = DBHelper.ExecuteCommand(orih);

            string orid1sql = "select fctlid_e, fctlid_ri,'' as fctlid,fbus,fkind,fritype,fsec,fid,fttycode,fttylayr,fttysec,fttyclass, "+
            "0 as fcom, '' as fgrnet,0 as fbrkge, '' as fgrnet2,sum(fcomamt) as fcomamt,sum(fbrkgeamt) as fbrkgeamt, "+
            "sum(fabsnet) as fabsnet,0 as fshare,sum(fsi) as fsi,0 as fsi2,0 as fupdshare, "+
            "sum(fupdsi) as fupdsi,0 as fupdsi2,0 as foshare,0 as fosi,0 as fosi2,sum(fgpm) as fgpm, "+
            "sum(fnpm) as fnpm,0 as fshare_c,sum(fsi_c) as fsi_c,0 as fsi2_c,sum(fgpm_c) as fgpm_c,sum(fnpm_c) as fnpm_c, "+
            "sum(fmiscamt_c) as fmiscamt_c,fclass,'' as floscode,fppl,'' as fcalc,0 as fexcess,0 as flimit  from orild1 where fctlid_ri='" + fctlid + "'  " +
            "group by fctlid_e, fctlid_ri,fid,fbus,fkind,fritype,fsec,fttycode,fttylayr,fttysec,fttyclass,fclass,fppl order by  "+
            "case when fritype ='01' then 1 else case when fritype ='02' then 2 else case when fritype ='05' then 3 else 4 end end end";
            DataTable dtorid1 = DBHelper.GetDataSet(orid1sql);
            if (dtorid1.Rows.Count > 0)
            {
                for (int j = 0; j < dtorid1.Rows.Count; j++)
                {
                    orid1 = (int.Parse(orid1) + 1).ToString().PadLeft(10, '0');
                    dtorid1.Rows[j]["fctlid"] = orid1;
                    orid1_fctlid = dtorid1.Rows[j]["fctlid"].ToString();
                }
            }
            if (isSuccuss == true)
            {
                isSuccuss = DBHelper.BulkInsertDataTable("orid1", dtorid1);
                if (isSuccuss == true)
                {
                    string updorid1 = "update " + Dbm + "xsysparm set fnxtid=RIGHT('*********'+ LTRIM(STR(CAST('" + orid1_fctlid + "' as int)+1)), 10) where fidtype ='orid1'";
                    isSuccuss = DBHelper.ExecuteCommand(updorid1);
                }
            }
            string orid2sql = "select fctlid_e, fctlid_ri,'' as fctlid_3,'' as fctlid,fbus,fkind,fritype,fsec,0 as flogseq,freinsr,fbrkr, " +
            "0 as fcom,'' as fgrnet,2 as fpnetonly, 2 as fsepdrcr,0 fbrkge,'' as fgrnet2, " +
            "sum(fcomamt) as fcomamt,sum(fbrkgeamt) as fbrkgeamt,0,0,0,0, " +
            "sum(fabsnet) as fabsnet,sum(fsi) as fsi,0 as fsi2,sum(fupdsi) as fupdsi,0,0,0,0,0,0,sum(fgpm) as fgpm, " +
            "sum(fnpm) as fnpm,0,0,0,fcession,friapp,'','','',fclass,floscode,fppl " +
            "from orild2 where fctlid_ri='"+fctlid +"'  " +
            "group by fctlid_e, fctlid_ri,fcession,friapp,fbus,fkind,fritype,fsec,freinsr,fbrkr,fclass,floscode,fppl order by freinsr";
            DataTable dtorid2 = DBHelper.GetDataSet(orid2sql);
            if (dtorid2.Rows.Count > 0)
            {
                if (dtorid2.Rows.Count > 0)
                {
                    for (int j = 0; j < dtorid2.Rows.Count; j++)
                    {
                        orid2 = (int.Parse(orid2) + 1).ToString().PadLeft(10, '0');
                        dtorid2.Rows[j]["fctlid"] = orid2;
                        dtorid2.Rows[j]["flogseq"] = j+1;
                        orid2_fctlid = dtorid2.Rows[j]["fctlid"].ToString();
                    }
                }
            }
            if (isSuccuss == true)
            {
                isSuccuss = DBHelper.BulkInsertDataTable("orid2", dtorid2);
                if (isSuccuss == true && orid2_fctlid != "")
                {
                    string updorid2 = "update " + Dbm + "xsysparm set fnxtid=RIGHT('*********'+ LTRIM(STR(CAST('" + orid2_fctlid + "' as int)+1)), 10) where fidtype ='orid2'";
                    isSuccuss = DBHelper.ExecuteCommand(updorid2);
                }
            }
            return isSuccuss;
        }

        bool u_genfacinv()
        {
            bool isSuccuss = false;
            DateTime time = DateTime.Now;
            ArrayList updarr = new ArrayList();
            string strc = "select * from xsysparm where fidtype ='oriinvh'";
            DataTable dtorihc = operate.GetTable(strc);
            string oriinvh = dtorihc.Rows[0]["fnxtid"].ToString();
            string oriinvh_fctlid = "";

            string sql = "select fctlid_1,fctlid_2,fctlid_p,fctlid_e,fctlid_i,fbus,fmodule,frino,fkind,fpolno,  " +
                        "fendtno,fuwyr,a.fclass,fsclass,a.facclass,fritype,fttycode,fttysec,fttyclass,finstall, " +
                        "ftotinstal, fpnetonly,'' as fctlid,CONVERT(VARCHAR(19),fissdate, 120) as fissdate, " +
                        "CONVERT(VARCHAR(19),finvdate, 120) as finvdate,CONVERT(VARCHAR(19),fbilldate, 120) as fbilldate, " +
                        "CONVERT(VARCHAR(19),fbkdate, 120) as fbkdate,CONVERT(VARCHAR(19),fefffr, 120) as fefffr, " +
                        "CONVERT(VARCHAR(19),feffto, 120) as feffto,fgrnet,fmethod,finvno, fdbtrtype,fdbtr,rtrim(fdbdesc) as fdbdesc, " +
                        "'' as fnoinvf,fbrkr,rtrim(fbdesc) as fbdesc,freinsr,rtrim(frdesc) as frdesc,fclnt,a.fsclnt,fpmcur,fgpm,fnpm, " +
                        "fcom,fcomamt, fbrkge,fbrkgeamt,fmiscamt1,fmiscamt2,fmiscamt3,fmiscamt,fpayable,fcrterm, " +
                        "rtrim(fremark) as fremark,'' as fpartr,'' as fcremark1, fdoctype,'2' as fintadj, " +
                        "fstatus,fposted,'' as finpuser,'' as finpdate,'' as fupduser,'' as fupddate,'' as fcnfuser, " +
                        "'' as fcnfdate, null as fvouchid, '' as fcinpname,rtrim(f.facclass) as item_id, '' as interco_id, " +
                        "'' as cont_id, case when a.fclass<>'EEC' then '000' else case when rtrim(b.fsclass_c) ='CONTRACTOR' then 'CR' else 'OT' end end as dept_id, " +
                        "case when rtrim(a.fsclnt)<>'' then rtrim(a.fsclnt) else '00' end as person_id from oriinvf a  " +
                        "left join (select fctlid, fclass,fsclass_c,fsclnt from polh) b on a.fctlid_e=b.fctlid  " +
                        "left join "+Dbm+"minsc f on rtrim(f.fid)= rtrim(b.fclass)   " +
                        "where fctlid_e='" + fctlid_1 + "'";
            DataTable tmpdbf1 = DBHelper.GetDataSet(sql);
            if (tmpdbf1.Rows.Count > 0)
            {
                DataTable tinvoice = DBHelper.GetDataSet(sql);
                tinvoice.Clear();
                int voiceDr = 0;
                foreach (DataRow dr in tmpdbf1.Rows)
                {
                    if (tinvoice.Rows.Count > 0)
                    {
                        voiceDr = tinvoice.Select(string.Format("fctlid_i = '{0}' and fdbtrtype = '{1}' and fdbtr = '{2}'", dr["fctlid_i"], dr["fdbtrtype"], dr["fdbtr"])).Length;
                    }
                    if (voiceDr == 0)
                    {
                        dr["fbilldate"] = DBNull.Value;
                        dr["fbkdate"] = DBNull.Value;
                        dr["fnoinvf"] = "1";
                        dr["finpuser"] = InsEnvironment.LoginUser.GetUserCode();
                        dr["fupduser"] = InsEnvironment.LoginUser.GetUserCode();
                        dr["fcnfuser"] = InsEnvironment.LoginUser.GetUserCode();
                        dr["finpdate"] = time.ToString("yyyy-MM-dd HH:mm:ss");
                        dr["fupddate"] = time.ToString("yyyy-MM-dd HH:mm:ss");
                        dr["fcnfdate"] = time.ToString("yyyy-MM-dd HH:mm:ss");
                        dr["cont_id"] = map(dr["fbrkr"].ToString(), "B");
                        if (dr["fbrkr"].ToString().Trim() == "PRD001")
                        {
                            dr["interco_id"] = map(dr["freinsr"].ToString(), "R");
                        }
                        else { dr["interco_id"] = map(dr["fclnt"].ToString(), "C"); }
                        dr["cont_id"] = map(dr["fbrkr"].ToString(), "B");
                        if (Convert.ToDecimal(dr["fpayable"]) >= 0) { dr["fdoctype"] = "1"; } else { dr["fdoctype"] = "2"; }
                        tinvoice.ImportRow(dr);
                    }
                    else
                    {
                        foreach (DataRow drv in tinvoice.Rows)
                        {
                            if (drv["fctlid_i"].ToString() == dr["fctlid_i"].ToString() && drv["fdbtrtype"].ToString() == dr["fdbtrtype"].ToString() && drv["fdbtr"].ToString() == dr["fdbtr"].ToString())
                            {
                                drv["fctlid_2"] = dr["fctlid_2"];
                                drv["fmodule"] = dr["fmodule"];
                                drv["fkind"] = dr["fkind"];
                                drv["fritype"] = dr["fritype"];
                                drv["fgpm"] = Convert.ToDecimal(drv["fgpm"]) + Convert.ToDecimal(dr["fgpm"]);
                                drv["fnpm"] = Convert.ToDecimal(drv["fnpm"]) + Convert.ToDecimal(dr["fnpm"]);
                                drv["fcomamt"] = Convert.ToDecimal(drv["fcomamt"]) + Convert.ToDecimal(dr["fcomamt"]);
                                drv["fbrkgeamt"] = Convert.ToDecimal(drv["fbrkgeamt"]) + Convert.ToDecimal(dr["fbrkgeamt"]);
                                drv["fpayable"] = Convert.ToDecimal(drv["fpayable"]) + Convert.ToDecimal(dr["fpayable"]);
                                drv["fnoinvf"] = Convert.ToDecimal(drv["fnoinvf"]) + 1;
                                if (Convert.ToDecimal(drv["fpayable"]) >= 0) { drv["fdoctype"] = "1"; } else { drv["fdoctype"] = "2"; }
                            }

                        }
                    }
                }
                string sqloriinvf = "select * from oriinvf where fctlid_1='" + fctlid + "'";
                string sqloriinvlf = "select * from oriinvlf where fctlid_ri='" + fctlid + "'";
                DataTable dtoriinvf = DBHelper.GetDataSet(sqloriinvf);
                DataTable dtoriinvlf = DBHelper.GetDataSet(sqloriinvlf);

                foreach (DataRow dr in tinvoice.Rows)
                {
                    oriinvh = (int.Parse(oriinvh) + 1).ToString().PadLeft(10, '0');
                    dr["fctlid"] = oriinvh;
                    foreach (DataRow drf in dtoriinvf.Rows)
                    {
                        if (drf["fctlid_i"].ToString() == dr["fctlid_i"].ToString() && drf["fdbtrtype"].ToString() == dr["fdbtrtype"].ToString() && drf["fdbtr"].ToString() == dr["fdbtr"].ToString())
                        {
                            string updoriinvf = "update oriinvf set fctlid_iv = '" + oriinvh + "' where fctlid ='" + drf["fctlid"] + "'";
                            updarr.Add(updoriinvf);
                        }
                    }
                    foreach (DataRow drlf in dtoriinvlf.Rows)
                    {
                        if (drlf["fctlid_i"].ToString().Trim() == dr["fctlid_i"].ToString().Trim() && drlf["fdbtrtype"].ToString().Trim() == dr["fdbtrtype"].ToString().Trim() && drlf["fdbtr"].ToString().Trim() == dr["fdbtr"].ToString().Trim())
                        {
                            string updoriinvlf = "update oriinvlf set fctlid_iv = '" + oriinvh + "' where fctlid ='" + drlf["fctlid"] + "'";
                            updarr.Add(updoriinvlf);
                        }
                    }
                }
                oriinvh_fctlid = tinvoice.Rows[tinvoice.Rows.Count - 1]["fctlid"].ToString();
                isSuccuss = DBHelper.BulkInsertDataTable("oriinvh", tinvoice);
                if (isSuccuss == true)
                {
                    string updoriinvh = "update " + Dbm + "xsysparm set fnxtid=RIGHT('*********'+ LTRIM(STR(CAST('" + oriinvh_fctlid + "' as int)+1)), 10) where fidtype ='oriinvh'";
                    isSuccuss = DBHelper.ExecuteCommand(updoriinvh);
                    if (isSuccuss == true)
                    {
                        string[] sql1 = (string[])updarr.ToArray(typeof(string));
                        string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
                        string result = ExecuteSqlTransaction(connectionString, sql1, sql1);
                        if (result == "OK") {isSuccuss = true;}
                        else { isSuccuss = false; }
                    }
                }
            }
            else { isSuccuss = true; }
            return isSuccuss;
        }

        bool u_genriapp(string p_ritype)
        {
            bool isSuccess = false;
            DateTime time = DateTime.Now;
            ArrayList updarr = new ArrayList();
            string strc = "select * from xsysparm where fidtype ='oriapp'";
            DataTable dtorihc = operate.GetTable(strc);
            string oriapp = Fct.stFat(dtorihc.Rows[0]["fnxtid"]);
            string oriapp_fctlid = "";
            String Year = DateTime.Now.Year.ToString();

            string str = "select fnxtid,rtrim(fprefix) as fprefix from [dbo].[xsysparm] where fidtype='RIAPNO' and fuy ='" + Year + "'";
            DataTable dt = operate.GetTable(str);
            string friapp = Fct.stFat(dt.Rows[0]["fnxtid"]);
            string fprefix = Fct.stFat(dt.Rows[0]["fprefix"]);
            string friapp_fctlid = "";

            SqlParameter param = new SqlParameter("@fctlid_1", SqlDbType.NVarChar, 20);
            param.Value = fctlid_1;
            DataTable otriapp = DBHelper.GetDataSetProd("triappPar",Dbo, param);
            string ln_indexp = "0";
            string updsql = "";
            foreach (DataRow dr in otriapp.Rows)
            {
                oriapp = (int.Parse(oriapp) + 1).ToString().PadLeft(10, '0');
                friapp = (int.Parse(friapp) + 1).ToString().PadLeft(4, '0');
                dr["fctlid"] = oriapp;
                dr["friapp"] = fprefix + friapp;
                dr["finpuser"] = InsEnvironment.LoginUser.GetUserCode();
                dr["fupduser"] = InsEnvironment.LoginUser.GetUserCode();
                dr["fcnfuser"] = InsEnvironment.LoginUser.GetUserCode();
                dr["finpdate"] = time.ToString("yyyy-MM-dd HH:mm:ss");
                dr["fupddate"] = time.ToString("yyyy-MM-dd HH:mm:ss");
                dr["fcnfdate"] = time.ToString("yyyy-MM-dd HH:mm:ss");
                dr["fsite"] = Fct.stFat(dr["fsite"]); 

                if (p_ritype == "04")
                {
                    string lc_factype = "P";
                    ln_indexp = (int.Parse(ln_indexp) + 1).ToString().PadLeft(2, '0');
                    string lc_fcession = dr["fpolno"].ToString().Trim() + "-" + lc_factype + ln_indexp;
                    dr["fcession"] = lc_fcession;
                    updsql = "update orild2 set friapp ='" + dr["friapp"] + "' , fcession ='" + dr["fcession"] + "'  " +
                               "where fritype='04'   " +
                               "and case when fbrkr='PRD001' then 'R' else 'B' end ='" + dr["fdbtrtype"] + "'   " +
                               "and case when fbrkr='PRD001' then freinsr else fbrkr end ='" + dr["fdbtr"] + "' and fctlid_e='" + fctlid_1 + "'";
                    updarr.Add(updsql);
                }
                oriapp_fctlid = Fct.stFat(dr["fctlid"]);
                friapp_fctlid = Fct.stFat(dr["friapp"].ToString().Replace(fprefix, ""));
            }

            isSuccess = DBHelper.BulkInsertDataTable("oriapp", otriapp);
            if (isSuccess == true)
            {
                SqlDataReader sdr = DBHelper.GetReader("select max(fctlid) as fctlid from oriapp");
                if (sdr.HasRows && sdr.Read())
                {
                    oriapp_fctlid = Fct.stFat(sdr["fctlid"]);
                }
                sdr.Close();
                sdr.Dispose();

                SqlDataReader sdr1 = DBHelper.GetReader("select max(friapp) as friapp from oriapp a left join polh b on a.fctlid_e = b.fctlid where fuwyr ='" + Year + "'");
                if (sdr1.HasRows && sdr1.Read())
                {
                    friapp_fctlid = Fct.stFat(sdr1["friapp"].ToString().Replace(fprefix, ""));
                }
                sdr1.Close();
                sdr1.Dispose();

                if (oriapp_fctlid != "")
                {
                    string updoriapp = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(oriapp_fctlid) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='oriapp'";
                    if (isSuccess) { isSuccess = DBHelper.ExecuteCommand(updoriapp); }

                    if (friapp_fctlid != "")
                    {
                        string updoriinvd = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(friapp_fctlid) + 1).ToString().PadLeft(4, '0') + "' where fidtype='RIAPNO' and fuy ='" + Year + "'";
                        if (isSuccess) { isSuccess = DBHelper.ExecuteCommand(updoriinvd); }
                    }
                } 
                for (int i = 0; i < updarr.Count; i++)
                {
                    if (isSuccess)
                    {
                        isSuccess = DBHelper.ExecuteCommand(updarr[i].ToString());
                    }
                }
            }
            return isSuccess;
        }

        public string map(string fid, string type)
        {
            string sql = "select fmap from " + Dbm + "[mprdr] where fid ='" + fid + "' and ftype='" + type + "'";
            DataTable dtmap = DBHelper.GetDataSet(sql);
            string fmap = "";
            if (dtmap.Rows.Count > 0)
            {
                fmap = dtmap.Rows[0]["fmap"].ToString();
            }
            else
            {
                if (type == "B") { fmap = "ZZ"; } else { fmap = "ZZZ"; }
            }
            return fmap.Trim();
        }

        private string ExecuteSqlTransaction(string connectionString, string[] sql1, string[] sql2)
        {
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                SqlCommand command = connection.CreateCommand();
                SqlTransaction transaction;
                transaction = connection.BeginTransaction("SampleTransaction");

                command.Connection = connection;
                command.Transaction = transaction;

                try
                {
                    if (sql1 != null)
                    {
                        for (int i = 0; i < sql1.Length; i++)
                        {
                            if (sql1[i] != "" && sql1[i] != null)
                            {
                                command.CommandText = sql1[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (sql2 != null)
                    {
                        for (int i = 0; i < sql2.Length; i++)
                        {
                            if (sql2[i] != "" && sql2[i] != null)
                            {
                                command.CommandText = sql2[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    transaction.Commit();
                    return "OK";
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                    Console.WriteLine("  Message: {0}", ex.Message);

                    try
                    {
                        transaction.Rollback();
                        return "RollBack";
                    }
                    catch (Exception ex2)
                    {
                        Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                        Console.WriteLine("  Message: {0}", ex2.Message);
                        return ex2.Message;
                    }
                }
            }
        }

        private void exitattch_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void ReinsurancePar_Resize(object sender, EventArgs e)
        {
            if (WindowState == FormWindowState.Maximized)
            {
                //最大化时所需的操作 
              //  this.ClientSize = new System.Drawing.Size(837, 722);
              //  this.WindowState = FormWindowState.Normal;
            }
            else if (WindowState == FormWindowState.Minimized)
            {
                //最小化时所需的操作

                this.ClientSize = new System.Drawing.Size(50, 50);
                this.WindowState = FormWindowState.Normal;
            }
        }



    }
}
