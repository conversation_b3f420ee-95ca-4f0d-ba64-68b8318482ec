using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using CrystalDecisions.CrystalReports.Engine;
using CrystalDecisions.Shared;
using INS.Business.objRpt;
using InsEnvironment;
using Excel = Microsoft.Office.Interop.Excel;
using System.Threading.Tasks;
using System.Configuration;

namespace INS.Business.Report
{
    public partial class PremiumI : Form
    {
        DBConnect Operate = new DBConnect();
        DateTime Time = DateTime.Now;
        private string rpOptid;
        //private string rpDirectory = Application.StartupPath.Replace("\\bin\\Debug", "\\objRpt");
        private string rpDirectory = "M:\\Software II\\New INS Runtime\\Setup\\objRpt";
//        private string rpDirectory = "M:\\Users\\Anna\\Setup\\objRpt";
        // Define Report formulae Variables
        private static string rpBusiness, rpClasssel, rpDatefr, rpDateto;
        private static string rpCompany, rpChiCompany, rpCurrency, rpUndersign, rpChkby, rpPreby;
        private static string rpTitle;
        private static string rpFileName;
        // End of Defining Report formulae Variables

        private static DateTime inpDatefr = Convert.ToDateTime(DateTime.Now.AddDays(-DateTime.Now.Day + 1).ToShortDateString());
        private static DateTime inpDateto = Convert.ToDateTime(DateTime.Now.ToShortDateString());

        // Define Query Variables
        DateTime vfbk_fr, vfbk_to, vthisasat, vlastasat;
        String vfbus, wfbus, vclasssel, vfdbtr, vfnew, vfintadj = "2", vfstatus = "3";
        String vrowfilter = "", vprmtype, vsumdet, vintext, vttytype;
        String vfinter, vfdet, vfsub, vford, vfsumm;
        String vperiodtype = "1";
        // End of Defining Query Variables

        private object rpprogBar,rpwaitlbl;
        
        public PremiumI(string optid, string optname)
        {
            InitializeComponent();
            InitCobBus(optid);
            InitCobCls();
            InitCobAllYesNo(cobNew);
            InitCobAllYesNo(cobInter);
            InitCobYesNoButton(cobDet);
            InitCobYesNoButton(cobSub);
            InitCobYesNoButton(cobSumm);
            InitCobOrd();

            InitCobPrm();
            InitCobRepType(optid);
            InitCobTtyType(optid);
            InitCobPeriod();
            InitDate();

            rpprogBar = progBar;
            rpwaitlbl = waitlbl;

            fbilfr.Mask = "0000.00.00";
            fbilfr.ValidatingType = typeof(System.DateTime);
            fbilfr.TypeValidationCompleted += new TypeValidationEventHandler(date_TypeValidationCompleted);

            fbilto.Mask = "0000.00.00";
            fbilto.ValidatingType = typeof(System.DateTime);
            fbilto.TypeValidationCompleted += new TypeValidationEventHandler(date_TypeValidationCompleted);

            foreach (Control control in this.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }

            this.Text = optname;
            rpOptid = optid;

            if (rpOptid == "effpmp")
            {
                cobCls.SelectedValue = "PMP";

                cobBus.Enabled = false;
                buslbl.Visible = true;
                cobBus.Visible = true;

                periodlbl.Text = "As At";
                periodlbl.Visible = true;

                fbilto.Location = new Point(fbilfr.Location.X, fbilfr.Location.Y);
                fbilto.Visible = true;

                //btnExcel.Visible = false;

                btnExcel.Location = new Point(214, 194);
                btnPrint.Location = new Point(322, 194);
                btnExit.Location = new Point(428, 194);
                                
                //btnPrint.Location = new Point(262, 194);
                //btnExit.Location = new Point(354, 194);
            }

            if (rpOptid == "polprmsm")
            {
                buslbl.Visible = true;
                cobBus.Visible = true;

                clslbl.Visible = true;
                cobCls.Enabled = false;
                cobCls.Visible = true;

                periodlbl.Text = "Year To Date";
                periodlbl.Visible = true;

                fbilto.Location = new Point(fbilfr.Location.X, fbilfr.Location.Y);
                fbilto.Visible = true;

                //btnExcel.Visible = false;
                //btnPrint.Location = new Point(262, 194);
                //btnExit.Location = new Point(354, 194);

                btnExcel.Location = new Point(214, 194);
                btnPrint.Location = new Point(322, 194);
                btnExit.Location = new Point(428, 194);
            }

            if ("prmanly|eecanly".Contains(rpOptid))
            {
                buslbl.Visible = true;
                cobBus.Visible = true;
                
                clslbl.Visible = true;
                cobCls.Enabled = rpOptid == "prmanly";
                cobCls.SelectedValue = rpOptid == "prmanly" ? "A" : "EEC";

                cobCls.Visible = true;

                periodlbl.Visible = true;
                dashlbl.Visible = true;

                fbilfr.Visible = true;
                fbilto.Visible = true;

                buslbl.Location = new Point(227, 16);
                cobBus.Location = new Point(227, 30);

                clslbl.Location = new Point(355, 16);
                cobCls.Location = new Point(355, 30);

                periodlbl.Location = new Point(227, 62);
                dashlbl.Location = new Point(338, 81);

                fbilfr.Location = new Point(227, 76);
                fbilto.Location = new Point(355, 76);

                newlbl.Location = new Point(227, 108);
                newlbl.Visible = true;

                cobNew.Location = new Point(227, 122);
                cobNew.Visible = true;

                //interlbl.Location = new Point(355, 108);
                //interlbl.Visible = true;

                //cobInter.Location = new Point(355, 122);
                //cobInter.Visible = true;

                detlbl.Location = new Point(227, 154);
                detlbl.Visible = true;

                cobDet.Location = new Point(227, 168);
                cobDet.Visible = true;

                sublbl.Location = new Point(291, 154);
                sublbl.Visible = true;

                cobSub.Location = new Point(291, 168);
                cobSub.Visible = true;
                
                ordlbl.Location = new Point(355, 154);
                ordlbl.Visible = true;

                cobOrd.Location = new Point(355, 168);
                cobOrd.Visible = true;

                summlbl.Location = new Point(227, 200);
                summlbl.Visible = true;
                
                cobSumm.Location = new Point(227, 214);
                cobSumm.Visible = true;
            }
                        
            
            if (rpOptid == "prmexpl")
            {
                buslbl.Visible = true;
                cobBus.Visible = true;

                clslbl.Visible = true;
                cobCls.Enabled = false;
                cobCls.Visible = true;

                periodlbl.Text = "Expiry Period";
                periodlbl.Visible = true;
                dashlbl.Visible = true;

                fbilfr.Visible = true;
                fbilto.Visible = true;

                btnExcel.Location = new Point(214,194);
                btnPrint.Location = new Point(322,194);
                btnExit.Location = new Point(428, 194);
            }

            if ("prmfacout".Contains(rpOptid))
            {
                buslbl.Visible = true;
                cobBus.Visible = true;

                clslbl.Visible = true;
                cobCls.Enabled = false;
                cobCls.Visible = true;

                periodlbl.Visible = true;
                dashlbl.Visible = true;

                fbilfr.Visible = true;
                fbilto.Visible = true;

                reptypelbl.Location = new Point(226, 184);
                reptypelbl.Visible = true;

                cobRepType.Location = new Point(227, 199);
                cobRepType.Visible = true;
            }

            if ("prmtty".Contains(rpOptid))
            {
                buslbl.Visible = true;
                cobBus.Visible = true;

                clslbl.Visible = true;
                cobCls.Enabled = false;
                cobCls.Visible = true;

                periodlbl.Visible = true;
                dashlbl.Visible = true;

                fbilfr.Visible = true;
                fbilto.Visible = true;

                reptypelbl.Location = new Point(226, 184);
                reptypelbl.Visible = true;

                cobRepType.Location = new Point(227, 199);
                cobRepType.Visible = true;

                ttylbl.Location = new Point(356, 184);
                ttylbl.Visible = true;

                cobTtyType.Location = new Point(356, 199);
                cobTtyType.Visible = true;
            }

            if ("prmbord".Contains(rpOptid))
            {
                buslbl.Visible = true;
                cobBus.Enabled = false;
                cobBus.Visible = true;

                clslbl.Visible = true;
                cobCls.Enabled = false;
                cobCls.Visible = true;

                periodlbl.Visible = true;
                dashlbl.Visible = true;

                fbilfr.Visible = true;
                fbilto.Visible = true;

                //reptypelbl.Location = new Point(226, 184);
                //reptypelbl.Visible = true;

                //cobRepType.Location = new Point(227, 199);
                //cobRepType.Visible = true;

                ttylbl.Location = new Point(226, 184);
                ttylbl.Visible = true;

                cobTtyType.Location = new Point(227, 199);
                cobTtyType.Visible = true;
            }

            if ("prmreg".Contains(rpOptid))
            {
                buslbl.Visible = true;
                cobBus.Visible = true;

                clslbl.Visible = true;
                cobCls.Visible = true;

                periodlbl.Visible = true;
                dashlbl.Visible = true;

                pdlbl.Visible = true;
                cobPeriod.Visible = true;

                fbilfr.Visible = true;
                fbilto.Visible = true;

                pdlbl.Location = new Point(226, 124);
                cobPeriod.Location = new Point(227, 139);

                periodlbl.Location = new Point(226, 184);
                dashlbl.Location = new Point(338, 204);

                fbilfr.Location = new Point(227, 199);
                fbilto.Location = new Point(357, 199);
                                
                //btnExcel.Location = new Point(214, 194);
                //btnPrint.Location = new Point(322, 194);
                //btnExit.Location = new Point(428, 194);
            }

            if ("polprm".Contains(rpOptid))
            {
                cobPeriod.SelectedValue = "2";
                cobPeriod.Enabled = false;

                buslbl.Visible = true;
                cobBus.Visible = true;

                clslbl.Visible = true;
                cobCls.Visible = true;

                periodlbl.Visible = true;
                dashlbl.Visible = true;

                pdlbl.Visible = true;
                cobPeriod.Visible = true;

                fbilfr.Visible = true;
                fbilto.Visible = true;

                pdlbl.Location = new Point(226, 124);
                cobPeriod.Location = new Point(227, 139);

                periodlbl.Location = new Point(226, 184);
                dashlbl.Location = new Point(338, 204);

                fbilfr.Location = new Point(227, 199);
                fbilto.Location = new Point(357, 199);

                //btnExcel.Location = new Point(214, 194);
                //btnPrint.Location = new Point(322, 194);
                //btnExit.Location = new Point(428, 194);
            }

            if ("prmrel".Contains(rpOptid))
            {
                buslbl.Visible = true;
                cobBus.Enabled = false;
                cobBus.Visible = true;

                clslbl.Visible = true;
                cobCls.Visible = true;

                periodlbl.Visible = true;
                dashlbl.Visible = true;

                fbilfr.Visible = true;
                fbilto.Visible = true;

                btnExcel.Location = new Point(214, 194);
                btnPrint.Location = new Point(322, 194);
                btnExit.Location = new Point(428, 194);
            }

            if (rpOptid == "prmdrcr")
            {
                buslbl.Visible = true;
                cobBus.Visible = true;

                clslbl.Visible = true;
                cobCls.Visible = true;

                pdlbl.Visible = true;
                cobPeriod.Visible = true;

                periodlbl.Visible = true;
                dashlbl.Visible = true;

                fbilfr.Visible = true;
                fbilto.Visible = true;

                acclbl.Visible = true;
                accode.Visible = true;

                buslbl.Location = new Point(226, 42);
                cobBus.Location = new Point(227, 57);

                clslbl.Location = new Point(355, 42);
                cobCls.Location = new Point(356, 57);
                
                pdlbl.Location = new Point(226, 91);
                cobPeriod.Location = new Point(227, 106);

                periodlbl.Location = new Point(226, 140);
                dashlbl.Location = new Point(338, 160);
                fbilfr.Location = new Point(227, 155);
                fbilto.Location = new Point(357, 155);

                acclbl.Location = new Point(226, 189);
                accode.Location = new Point(227, 204); 
            }

            if (rpOptid == "prmuniv")
            {
                buslbl.Visible = true;
                cobBus.Visible = true;

                clslbl.Visible = true;
                cobCls.Enabled = false;
                cobCls.Visible = true;

                periodlbl.Visible = true;
                dashlbl.Visible = true;

                fbilfr.Visible = true;
                fbilto.Visible = true;
                
                btnExcel.Location = new Point(214, 194);
                btnPrint.Location = new Point(322, 194);
                btnExit.Location = new Point(428, 194);
            }

            if ("prmgrnd|daqcost|prmunern".Contains(rpOptid))
            {
                buslbl.Visible = true;
                cobBus.Visible = true;

                clslbl.Visible = true;
                cobCls.Enabled = false;
                cobCls.Visible = true;

                periodlbl.Visible = true;
                dashlbl.Visible = true;

                fbilfr.Visible = true;
                fbilto.Visible = true;
                
                newlbl.Location = new Point(226, 184);
                newlbl.Visible = true;

                cobNew.Location = new Point(227, 199);
                cobNew.Visible = true;
            }

            if ("prmstat".Contains(rpOptid))
            {
                buslbl.Visible = true;
                cobBus.Visible = true;

                clslbl.Visible = true;
                cobCls.Enabled = false;
                cobCls.Visible = true;

                periodlbl.Visible = true;
                dashlbl.Visible = true;

                fbilfr.Visible = true;
                fbilto.Visible = true;

                prmlbl.Location = new Point(226, 184);
                prmlbl.Visible = true;

                cobPrm.Location = new Point(227, 199);
                cobPrm.Visible = true;
            }

            rpCompany = CompInfo.GetEnglishComp();
            rpChiCompany = CompInfo.GetChineseComp();
            rpCurrency = CompInfo.GetBaseCur();
            rpUndersign = CompInfo.GetBusign();
            rpChkby = CompInfo.GetBchkby();
            rpPreby = CompInfo.GetBpreby();
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            TextBox focusTextBox = null;

            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
                if (sender is TextBox)
                {
                    focusTextBox = (TextBox)sender;

                    if (!focusTextBox.AcceptsReturn)
                        SendKeys.Send("{TAB}");

                }
                else SendKeys.Send("{TAB}");
        }

        void date_TypeValidationCompleted(object sender, TypeValidationEventArgs e)
        {
            if (!e.IsValidInput)
            {
                MessageBox.Show("Invalid Date!");
                e.Cancel = true;
            }
        }

        private void InitDate()
        {
            //DateTime b = Convert.ToDateTime(DateTime.Now.AddDays(-DateTime.Now.Day + 1).ToShortDateString());
            fbilfr.Text = inpDatefr.ToString("yyyy.MM.dd");

            //DateTime c = Convert.ToDateTime(DateTime.Now.ToShortDateString());
            fbilto.Text = inpDateto.ToString("yyyy.MM.dd");
        }

        private void InitCobBus(String optid)
        {
            String[,] arr = new String[,] { { "Direct", "D" }, { "Direct Only", "E" },
             { "Co In Only", "K" }, { "Fac In", "R" } };
            DataTable dt = new DataTable();
            dt.Columns.Add("String", typeof(String));
            dt.Columns.Add("Value", typeof(String));
            for (int i = 0; i < arr.GetLength(0); i++)
            {
                string strText = arr[i, 0], strValue = arr[i, 1];
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = strValue;
                dt.Rows.Add(aRow);
            }

            if ("polprmsm|prmstat|prmfacout|prmtty|prmbord".Contains(optid) && optid != "polprm")
            {
                DataRow row = dt.NewRow();

                row["String"] = "All";
                row["Value"] = "A";

                dt.Rows.InsertAt(row, 0);
            }

            cobBus.DataSource = dt;
            cobBus.DisplayMember = "String";
            cobBus.ValueMember = "Value";
        }

        private void InitCobAllYesNo(ComboBox cobButton)
        {
            String[,] arr =new String [,] {{"All","A"},{"Yes","1"},{"No","2"}};
            DataTable dt = new DataTable();
            dt.Columns.Add("String", typeof(String));
            dt.Columns.Add("Value", typeof(String));
            for (int i= 0; i < arr.GetLength(0); i++)
            {
                string strText = arr[i,0], strValue = arr[i, 1];
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = strValue;
                dt.Rows.Add(aRow);
            }
            cobButton.DataSource = dt;
            cobButton.DisplayMember = "String";
            cobButton.ValueMember = "Value";
        }

        private void InitCobYesNoButton(ComboBox cobButton)
        {
            String[,] arr = new String[,] { { "Yes", "1" }, { "No", "2" } };
            DataTable dt = new DataTable();
            dt.Columns.Add("String", typeof(String));
            dt.Columns.Add("Value", typeof(String));
            for (int i = 0; i < arr.GetLength(0); i++)
            {
                string strText = arr[i, 0], strValue = arr[i, 1];
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = strValue;
                dt.Rows.Add(aRow);
            }
            cobButton.DataSource = dt;
            cobButton.DisplayMember = "String";
            cobButton.ValueMember = "Value";
        }

        private void InitCobOrd()
        {
            String[,] arr = new String[,] { { "Class", "1" }, { "Class & Client", "2" },
                                          { "Client", "3" }, { "Client & Class", "4" } };
            DataTable dt = new DataTable();
            dt.Columns.Add("String", typeof(String));
            dt.Columns.Add("Value", typeof(String));
            for (int i = 0; i < arr.GetLength(0); i++)
            {
                string strText = arr[i, 0], strValue = arr[i, 1];
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = strValue;
                dt.Rows.Add(aRow);
            }
            cobOrd.DataSource = dt;
            cobOrd.DisplayMember = "String";
            cobOrd.ValueMember = "Value";
        }

        private void InitCobPrm()
        {
            String[,] arr = new String[,] { { "Gross Premium", "1" }, { "Retained Premium", "2" } };
            DataTable dt = new DataTable();
            dt.Columns.Add("String", typeof(String));
            dt.Columns.Add("Value", typeof(String));
            for (int i = 0; i < arr.GetLength(0); i++)
            {
                string strText = arr[i, 0], strValue = arr[i, 1];
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = strValue;
                dt.Rows.Add(aRow);
            }
            cobPrm.DataSource = dt;
            cobPrm.DisplayMember = "String";
            cobPrm.ValueMember = "Value";
        }
        
        private void InitCobCls()
        {
            DataTable dt = new DataTable();
            string sql = "select RTRIM(fid) as fid, RTRIM(fid) as fidval from minsc";

            SqlDataAdapter da = new SqlDataAdapter(sql, DBConnect.mstdbConn);

            da.Fill(dt);
            da.Dispose();

            DataRow row = dt.NewRow();

            row["Fid"] = "All";
            row["Fidval"] = "A";

            dt.Rows.InsertAt(row, 0);

            this.cobCls.DisplayMember = "fid";
            this.cobCls.ValueMember = "fidval";
            this.cobCls.DataSource = dt;
        }

        private void InitCobRepType(String optid)
        {
            String[,] arr;

            if (optid == "prmbord")
                arr = new String[,] { { "Internal", "1" }, { "External", "2" } };
            else
                arr = new String[,] { { "Summary", "1" }, { "Details", "2" } };
            
            DataTable dt = new DataTable();
            dt.Columns.Add("String", typeof(String));
            dt.Columns.Add("Value", typeof(String));
            for (int i = 0; i < arr.GetLength(0); i++)
            {
                string strText = arr[i, 0], strValue = arr[i, 1];
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = strValue;
                dt.Rows.Add(aRow);
            }
            cobRepType.DataSource = dt;
            cobRepType.DisplayMember = "String";
            cobRepType.ValueMember = "Value";
        }

        private void InitCobTtyType(String optid)
        {
            String[,] arr;
            
            if (optid == "prmbord")
                arr = new String[,] { { "Surplus", "1" }, { "Fac Oblig", "3" } };
            else
                arr = new String[,] { { "Surplus", "1" }, { "XOL", "2" }, { "Fac Oblig", "3" } };

            DataTable dt = new DataTable();
            dt.Columns.Add("String", typeof(String));
            dt.Columns.Add("Value", typeof(String));
            for (int i = 0; i < arr.GetLength(0); i++)
            {
                string strText = arr[i, 0], strValue = arr[i, 1];
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = strValue;
                dt.Rows.Add(aRow);
            }
            cobTtyType.DataSource = dt;
            cobTtyType.DisplayMember = "String";
            cobTtyType.ValueMember = "Value";
        }

        private void InitCobPeriod()
        {
            String[,] arr = new String[,] { { "Billing", "1" }, { "Booking", "2" } };
            DataTable dt = new DataTable();
            dt.Columns.Add("String", typeof(String));
            dt.Columns.Add("Value", typeof(String));
            for (int i = 0; i < arr.GetLength(0); i++)
            {
                string strText = arr[i, 0], strValue = arr[i, 1];
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = strValue;
                dt.Rows.Add(aRow);
            }
            cobPeriod.DataSource = dt;
            cobPeriod.DisplayMember = "String";
            cobPeriod.ValueMember = "Value";
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            if ("effpmp".Contains(rpOptid))
                callCrPrmReg(CreateViewEff_PMP().Tables[0], rpOptid);

            if (rpOptid=="polprmsm")
                callCrPrmReg(CreateViewPolPrmSm().Tables[0], rpOptid);
            
            if ("prmexpl".Contains(rpOptid))
                callCrPrmReg(CreateViewPrmExpl().Tables[0], rpOptid);

            if ("prmreg|prmdrcr|prmrel|polprm".Contains(rpOptid))
                callCrPrmReg(CreateViewPrmReg().Tables[0],rpOptid);

            if ("prmuniv".Contains(rpOptid))
                callCrPrmReg(CreateViewPrmUniv().Tables[0], rpOptid);

            if ("prmgrnd|daqcost|prmunern|prmstat".Contains(rpOptid))
                callCrPrmReg(CreateViewPrmGrnd().Tables[0], rpOptid);

            if ("prmfacout".Contains(rpOptid))
                callCrPrmReg(CreateViewPrmFacOut().Tables[0], rpOptid);

            if ("prmtty".Contains(rpOptid))
                callCrPrmReg(CreateViewPrmTty().Tables[0], rpOptid);

            if ("prmbord".Contains(rpOptid))
                callCrPrmReg(CreateViewPrmBord().Tables[0], rpOptid);

            if ("prmanly|eecanly".Contains(rpOptid))
                callCrPrmReg(CreateViewPrmAnly(), rpOptid);
        }

        public DataSet CreateViewEff_PMP()
        {
            vfbk_to = DateTime.ParseExact(fbilto.Text.Trim(), "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);
            vfbk_fr = new DateTime(vfbk_to.Year, 1, 1);

            vfbus = cobBus.SelectedValue.ToString().Trim();
            vclasssel = cobCls.SelectedValue.ToString().Trim();
            vfdbtr = accode.Text.ToString().Trim();
            vfnew = cobNew.SelectedValue.ToString().Trim();
            vttytype = cobTtyType.SelectedValue.ToString().Trim();

            RptSetFunc.SetQueryParam(ref vfbus, ref wfbus, ref vttytype, ref vrowfilter, vfnew, vclasssel, vfdbtr);

            DataSet ds = InterMedData.IntEff_PMP(vfbk_to, vfbus, wfbus, vfstatus);
            //DataTable dtresult = ds.Tables[0];

            inpDatefr = vfbk_fr;
            inpDateto = vfbk_to;

            return ds;
        }
        
        public DataSet CreateViewPolPrmSm()
        {
            vfbk_to = DateTime.ParseExact(fbilto.Text.Trim(), "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);
            vfbk_fr = new DateTime(vfbk_to.Year,1,1);

            vfbus = cobBus.SelectedValue.ToString().Trim();
            vclasssel = cobCls.SelectedValue.ToString().Trim();
            vfdbtr = accode.Text.ToString().Trim();
            vfnew = cobNew.SelectedValue.ToString().Trim();
            vttytype = cobTtyType.SelectedValue.ToString().Trim();

            RptSetFunc.SetQueryParam(ref vfbus, ref wfbus, ref vttytype, ref vrowfilter, vfnew, vclasssel, vfdbtr);

            DataSet ds = InterMedData.IntPolPrmSm(vfbk_fr, vfbk_to, vfbus, wfbus, vfstatus);
            //DataTable dtresult = ds.Tables[0];

            inpDatefr = vfbk_fr;
            inpDateto = vfbk_to;

            return ds;
        }

        public DataSet CreateViewPrmExpl()
        {
            vfbk_fr = DateTime.ParseExact(fbilfr.Text.Trim(), "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);
            vfbk_to = DateTime.ParseExact(fbilto.Text.Trim(), "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);

            vfbus = cobBus.SelectedValue.ToString().Trim();
            vclasssel = cobCls.SelectedValue.ToString().Trim();
            vfdbtr = accode.Text.ToString().Trim();
            vfnew = cobNew.SelectedValue.ToString().Trim();
            vttytype = cobTtyType.SelectedValue.ToString().Trim();

            RptSetFunc.SetQueryParam(ref vfbus, ref wfbus, ref vttytype, ref vrowfilter, vfnew, vclasssel, vfdbtr);

            DataSet ds = InterMedData.IntPrmExpl(vfbk_fr, vfbk_to, vfbus, wfbus, vfstatus);
            //DataTable dtresult = ds.Tables[0];

            inpDatefr = vfbk_fr;
            inpDateto = vfbk_to;

            return ds;
        }

        public DataSet CreateViewPrmFacOut()
        {
            vfbk_fr = DateTime.ParseExact(fbilfr.Text.Trim(), "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);
            vfbk_to = DateTime.ParseExact(fbilto.Text.Trim(), "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);
            vfbus = cobBus.SelectedValue.ToString().Trim();
            vclasssel = cobCls.SelectedValue.ToString().Trim();
            vfdbtr = accode.Text.ToString().Trim();
            //vfnew = cobNew.SelectedValue.ToString().Trim();
            vsumdet = cobRepType.SelectedValue.ToString().Trim();
            vttytype = cobTtyType.SelectedValue.ToString().Trim();

            RptSetFunc.SetQueryParam(ref vfbus, ref wfbus, ref vttytype, ref vrowfilter, vfnew, vclasssel, vfdbtr);

            DataSet ds = InterMedData.IntPrmFacOut(vfbk_fr, vfbk_to, vfbus, wfbus, vfintadj);

            //DataSet ds = InterMedData.IntPrmReg(vfbk_fr, vfbk_to, vfbus, wfbus, vfintadj, rpOptid, vrowfilter);

            //DataTable dtresult = ds.Tables[0];
           
            return ds;
        }

        public DataSet CreateViewPrmTty()
        {
            vfbk_fr = DateTime.ParseExact(fbilfr.Text.Trim(), "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);
            vfbk_to = DateTime.ParseExact(fbilto.Text.Trim(), "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);

            vfbus = cobBus.SelectedValue.ToString().Trim();
            vclasssel = cobCls.SelectedValue.ToString().Trim();
            vfdbtr = accode.Text.ToString().Trim();
            vsumdet = cobRepType.SelectedValue.ToString().Trim();
            vttytype = cobTtyType.SelectedValue.ToString().Trim();

            RptSetFunc.SetQueryParam(ref vfbus, ref wfbus, ref vttytype, ref vrowfilter, vfnew, vclasssel, vfdbtr);

            DataSet ds = InterMedData.IntPrmTty(vfbk_fr, vfbk_to, vfbus, wfbus, vttytype, vfintadj);

            //DataTable dtresult = ds.Tables[0];

            return ds;
        }

        public DataTable CreateViewPrmAnly()
        {
            vfbk_fr = DateTime.ParseExact(fbilfr.Text.Trim(), "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);
            vfbk_to = DateTime.ParseExact(fbilto.Text.Trim(), "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);
            vfbus = cobBus.SelectedValue.ToString().Trim();
            vclasssel = cobCls.SelectedValue.ToString().Trim();

            vfnew = cobNew.SelectedValue.ToString().Trim();
            vfinter = cobInter.SelectedValue.ToString().Trim();
            vfdet = cobDet.SelectedValue.ToString().Trim();
            vfsub = cobSub.SelectedValue.ToString().Trim();
            vford = cobOrd.SelectedValue.ToString().Trim();
            vfsumm = cobOrd.SelectedValue.ToString().Trim();
            
            vfdbtr = accode.Text.ToString().Trim();
            vttytype = cobTtyType.SelectedValue.ToString().Trim();

            RptSetFunc.SetQueryParam(ref vfbus, ref wfbus, ref vttytype, ref vrowfilter, vfnew, vclasssel, vfdbtr);

            DataSet ds = InterMedData.IntPrmAnly(vfbk_fr, vfbk_to, vfbus, wfbus, vrowfilter,vford,vfsumm);

            DataTable dtresult = ds.Tables[0];

            return dtresult;
        }

        public DataSet CreateViewPrmBord()
        {
            vfbk_fr = DateTime.ParseExact(fbilfr.Text.Trim(), "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);
            vfbk_to = DateTime.ParseExact(fbilto.Text.Trim(), "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);

            vfbus = cobBus.SelectedValue.ToString().Trim();
            vclasssel = cobCls.SelectedValue.ToString().Trim();
            vfdbtr = accode.Text.ToString().Trim();
            vintext = cobRepType.SelectedValue.ToString().Trim();
            vttytype = cobTtyType.SelectedValue.ToString().Trim();

            RptSetFunc.SetQueryParam(ref vfbus, ref wfbus, ref vttytype, ref vrowfilter, vfnew, vclasssel, vfdbtr);

            DataSet ds = InterMedData.IntPrmBord(vfbk_fr, vfbk_to, vfbus, wfbus, vttytype, vfintadj);

            DataTable dtresult = ds.Tables[0];

            return ds;
        }

        public DataSet CreateViewPrmReg()
        {
            vfbk_fr = DateTime.ParseExact(fbilfr.Text.Trim(), "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);
            vfbk_to = DateTime.ParseExact(fbilto.Text.Trim(), "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);

            vfbus = cobBus.SelectedValue.ToString().Trim();
            vclasssel = cobCls.SelectedValue.ToString().Trim();
            vfdbtr = accode.Text.ToString().Trim();
            vfnew = cobNew.SelectedValue.ToString().Trim();
            vttytype = cobTtyType.SelectedValue.ToString().Trim();
            vperiodtype = cobPeriod.SelectedValue.ToString().Trim();

            RptSetFunc.SetQueryParam(ref vfbus, ref wfbus, ref vttytype, ref vrowfilter, vfnew, vclasssel, vfdbtr);

            DataSet ds = InterMedData.IntPrmReg(vfbk_fr, vfbk_to, vfbus, wfbus, vfintadj,vperiodtype,rpOptid, vrowfilter);
            //DataTable dtresult = ds.Tables[0];

            inpDatefr = vfbk_fr;
            inpDateto = vfbk_to;

          return ds;
        }

        public DataSet CreateViewPrmUniv()
        {
            vfbus = cobBus.SelectedValue.ToString().Trim();
            vclasssel = cobCls.SelectedValue.ToString().Trim();
            vfbk_fr = DateTime.ParseExact(fbilfr.Text.Trim(), "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);
            vfbk_to = DateTime.ParseExact(fbilto.Text.Trim(), "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);
            vfdbtr = accode.Text.ToString().Trim();
            vfnew = cobNew.SelectedValue.ToString().Trim();
            vttytype = cobTtyType.SelectedValue.ToString().Trim();

            vthisasat = vfbk_to;
            vlastasat = vfbk_fr.AddDays(-1);

            RptSetFunc.SetQueryParam(ref vfbus, ref wfbus, ref vttytype, ref vrowfilter, vfnew, vclasssel, vfdbtr);
            DataSet ds = InterMedData.IntPrmUnIv(vfbk_fr, vfbk_to, vlastasat, vthisasat,
                                          vfbus, wfbus, vfstatus);
            inpDatefr = vfbk_fr;
            inpDateto = vfbk_to;

            //DataTable dtresult = ds.Tables[0];

            return ds;
        }

        public DataSet CreateViewPrmGrnd()
        {
            vfbus = cobBus.SelectedValue.ToString().Trim();
            vclasssel = cobCls.SelectedValue.ToString().Trim();
            vfbk_fr = DateTime.ParseExact(fbilfr.Text.Trim(), "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);
            vfbk_to = DateTime.ParseExact(fbilto.Text.Trim(), "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);
            vfdbtr = accode.Text.ToString().Trim();
            vfnew = cobNew.SelectedValue.ToString().Trim();
            vprmtype = cobPrm.SelectedValue.ToString().Trim();
            vttytype = cobTtyType.SelectedValue.ToString().Trim();

            vthisasat = vfbk_to;
            vlastasat = vfbk_fr.AddDays(-1);

            progBar.Value = 0;
            waitlbl.Text = "Processing .....";
            progBar.Visible = true;
            waitlblfr.Visible = true;
            waitlblto.Visible = true;
            waitlbl.Visible = true;
            progBar.Update();
            waitlblfr.Update();
            waitlblto.Update();
            waitlbl.Update();

            RptSetFunc.SetQueryParam(ref vfbus, ref wfbus, ref vttytype, ref vrowfilter, vfnew, vclasssel, vfdbtr);
            DataSet ds = InterMedData.IntPrmGrnd(vfbk_fr, vfbk_to, vlastasat, vthisasat, vfbus, wfbus, vfintadj, vfnew, rpprogBar, rpwaitlbl);

            DataTable dtgrand = ds.Tables[0], dtdir = ds.Tables[1];

            inpDatefr = vfbk_fr;
            inpDateto = vfbk_to;

            //ExportDBF.CreateDBF("abctest", dtgrand);

            progBar.Value = 0;
            waitlbl.Text = "Processing .....";
            progBar.Visible = false;
            waitlblfr.Visible = false;
            waitlblto.Visible = false;
            waitlbl.Visible = false;

            if (rpOptid == "prmstat")
            {
                DataSet dspstat = InterMedData.IntPrmStat(dtgrand, vfbk_to);

                if (vprmtype == "1")
                    dspstat.Tables.RemoveAt(1);
                else
                    dspstat.Tables.RemoveAt(0);

                return dspstat;

                //if (vprmtype == "1")
                //    return dspstat.Tables[0];
                //else
                //    return dspstat.Tables[1];
            }

            if (rpOptid != "prmunern")
            {
                //dsresult.Tables.Add(dtgrand);
                return ds;
            }

            DataSet dsunearn = InterMedData.IntPrmUnEarn(dtdir);
            //DataTable dtunernsummry = dsunearn.Tables[0],dtunerndetail = dsunearn.Tables[1];

            //return dtunernsummry;
            return dsunearn;
        }
        
        void callCrPrmReg(DataTable cryDt,string optid)
        {
//            prmreg cryRpt = new prmreg();

            ReportDocument cryRpt = new ReportDocument();

            if (optid == "polprmsm")
                cryRpt.Load(rpDirectory + "\\polprmsm.rpt");
            else if ("prmreg|polprm".Contains(optid))
                cryRpt.Load(rpDirectory + "\\prmreg.rpt");
            else if (optid == "prmdrcr")
                cryRpt.Load(rpDirectory + "\\prmdrcr.rpt");
            else if (optid == "prmrel")
                cryRpt.Load(rpDirectory + "\\prmrel.rpt");
            else if (optid == "prmuniv")
                cryRpt.Load(rpDirectory + "\\prmuniv.rpt");
            else if (optid == "prmgrnd")
                cryRpt.Load(rpDirectory + "\\prmgrnd.rpt");
            else if (optid == "daqcost")
                cryRpt.Load(rpDirectory + "\\daqcost.rpt");
            else if (optid == "prmunern")
                cryRpt.Load(rpDirectory + "\\prmunern.rpt");
            else if (optid == "prmexpl")
                cryRpt.Load(rpDirectory + "\\prmexpl.rpt");
            else if (optid == "effpmp")
                cryRpt.Load(rpDirectory + "\\effpmp.rpt");
            else if (optid == "prmstat")
                cryRpt.Load(rpDirectory + "\\prmstat.rpt");
            else if (optid == "prmfacout")
                cryRpt.Load(rpDirectory + "\\prmfac.rpt");
            else if (optid == "prmtty")
                cryRpt.Load(rpDirectory + "\\prmtty.rpt");
            else if (optid == "prmbord" && vttytype == "02")
                cryRpt.Load(rpDirectory + "\\prmbord02.rpt");
            else if (optid == "prmbord" && vttytype == "05")
                cryRpt.Load(rpDirectory + "\\prmbord05.rpt");
            else if (optid == "prmanly")
                cryRpt.Load(rpDirectory + "\\prmanly.rpt");
            else if (optid == "eecanly")
                cryRpt.Load(rpDirectory + "\\eecanly.rpt");
            
            cryRpt.SetDataSource(cryDt);

            RptSetFunc.SetRptFormulae(ref rpBusiness, ref rpClasssel, ref rpDatefr,ref rpDateto, ref rpTitle,
                                      vfbus, wfbus, vclasssel, vfbk_fr, vfbk_to, vttytype,vperiodtype, optid);

            cryRpt.DataDefinition.FormulaFields["z_company"].Text = String.Format("'{0}'",
                                                    optid == "polprmsm" ? rpChiCompany.Trim() : rpCompany).Trim();
            cryRpt.DataDefinition.FormulaFields["z_title"].Text = String.Format("'{0}'",rpTitle);
            cryRpt.DataDefinition.FormulaFields["z_currency"].Text = String.Format("'{0}'",rpCurrency);
            cryRpt.DataDefinition.FormulaFields["z_undersign"].Text = String.Format("'Approved by: {0}'", rpUndersign);
            cryRpt.DataDefinition.FormulaFields["z_chkby"].Text = String.Format("'Checked by: {0}'", rpChkby);
            cryRpt.DataDefinition.FormulaFields["z_preby"].Text = String.Format("'Prepared by: {0}'", rpPreby);

            if ("prmreg|polprm".Contains(optid))
                cryRpt.DataDefinition.FormulaFields["z_reptype"].Text = optid=="prmreg" ? "'1'" : "'2'";

            if ("prmgrnd|daqcost".Contains(optid))
                if (vfbus == "R")
                    cryRpt.DataDefinition.FormulaFields["z_ecsum"].Text ="'N'";
                else
                    cryRpt.DataDefinition.FormulaFields["z_ecsum"].Text = "'Y'";

            if (optid == "prmunern")
                cryRpt.DataDefinition.FormulaFields["z_period"].Text = String.Format("'Current Period: {0} to {1}'",
                    rpDatefr, rpDateto);

            if (optid == "prmrel")
                cryRpt.DataDefinition.FormulaFields["z_period"].Text = String.Format("'From: {0} to {1}'",
                    rpDatefr, rpDateto);
            
            if (optid == "prmstat")
            {
                rpTitle = (vprmtype.Equals("1") ? "Gross " : "Retained ")+rpTitle;
                cryRpt.DataDefinition.FormulaFields["z_title"].Text = String.Format("'{0}'", rpTitle);
                cryRpt.DataDefinition.FormulaFields["z_period"].Text = String.Format("'From: {0} to {1}'",
                    rpDatefr, rpDateto);

                Int32 refyear = vfbk_to.Year;
                cryRpt.DataDefinition.FormulaFields["z_amthd2"].Text = String.Format("'{0:0000}'", refyear - 6);
                cryRpt.DataDefinition.FormulaFields["z_amthd3"].Text = String.Format("'{0:0000}'", refyear - 5);
                cryRpt.DataDefinition.FormulaFields["z_amthd4"].Text = String.Format("'{0:0000}'", refyear - 4);
                cryRpt.DataDefinition.FormulaFields["z_amthd5"].Text = String.Format("'{0:0000}'", refyear - 3);
                cryRpt.DataDefinition.FormulaFields["z_amthd6"].Text = String.Format("'{0:0000}'", refyear - 2);
                cryRpt.DataDefinition.FormulaFields["z_amthd7"].Text = String.Format("'{0:0000}'", refyear - 1);
                cryRpt.DataDefinition.FormulaFields["z_amthd8"].Text = String.Format("'{0:0000} + after'", refyear);
            }

            if (optid == "prmfacout")
            {
                rpTitle = rpTitle + (vsumdet.Equals("1") ? " - Summary" : " - Details");
                cryRpt.DataDefinition.FormulaFields["z_title"].Text = String.Format("'{0}'", rpTitle);
                cryRpt.DataDefinition.FormulaFields["z_prtdetail"].Text = String.Format("'{0}'", vsumdet.Equals("1") ? "N" : "Y");
            }

            if (optid == "prmtty")
            {
                rpTitle = rpTitle + (vsumdet.Equals("1") ? " - Summary" : " - Details");
                cryRpt.DataDefinition.FormulaFields["z_title"].Text = String.Format("'{0}'", rpTitle);
                cryRpt.DataDefinition.FormulaFields["z_prtdetail"].Text = String.Format("'{0}'", vsumdet.Equals("1") ? "N" : "Y");
            }

            if (optid == "prmbord")
            {
                cryRpt.DataDefinition.FormulaFields["z_title"].Text = String.Format("'{0}'", rpTitle);
                cryRpt.DataDefinition.FormulaFields["z_period"].Text = String.Format("'Premium Bordereaux: {0} to {1}'",
                    rpDatefr, rpDateto);
            }

            if ("prmanly|eecanly".Contains(optid))
            {
                cryRpt.DataDefinition.FormulaFields["z_prdetail"].Text = String.Format("'{0}'", vfdet);
                cryRpt.DataDefinition.FormulaFields["z_prsubtot"].Text = String.Format("'{0}'", vfsub);
                cryRpt.DataDefinition.FormulaFields["z_subtotby"].Text = String.Format("'{0}'", vford);
                cryRpt.DataDefinition.FormulaFields["z_prsum"].Text = String.Format("'{0}'", vfsumm);
                cryRpt.DataDefinition.FormulaFields["z_sumby"].Text = String.Format("'{0}'", vford);

                cryRpt.DataDefinition.FormulaFields["z_prgphd1"].Text = String.Format("'{0}'", 
                    vfdet == "1" || (vfsub == "1" && "24".Contains(vford)) ? "1" :"2");
                cryRpt.DataDefinition.FormulaFields["z_prgphd2"].Text = String.Format("'{0}'", vfdet == "1" ? "1" : "2");
                cryRpt.DataDefinition.FormulaFields["z_prgpft1a"].Text = String.Format("'{0}'", 
                    vfdet == "2" && vfsub == "1" && "13".Contains(vford) ? "1" : "2" );
                cryRpt.DataDefinition.FormulaFields["z_prgpft1b"].Text = String.Format("'{0}'", 
                    (vfdet == "1" && vfsub == "1") || (vfdet == "2" && vfsub == "1" && "24".Contains(vford)) ? "1" : "2");
                cryRpt.DataDefinition.FormulaFields["z_prgpft2a"].Text = String.Format("'{0}'", 
                    vfdet == "2" && vfsub == "1" && "24".Contains(vford) ? "1" : "2");
                cryRpt.DataDefinition.FormulaFields["z_prgpft2b"].Text = String.Format("'{0}'",
                    vfdet == "1" && vfsub == "1" && "24".Contains(vford) ? "1" : "2");
                cryRpt.DataDefinition.FormulaFields["z_new"].Text = String.Format("'{0}'", 
                    vfnew == "A" ? "** New + Old **" : vfnew == "1" ? "** New **" : "** Old **");
            }
            if (rpTitle.Contains("成交額統計表 (直接業務)")) 
            {
                rpFileName = "1";
            }
            if (rpTitle.Contains("成交額統計表 (分入業務)"))
            {
                rpFileName = "2";
            }
            if (rpTitle.Contains("成交額統計表 (直接 + 分入業務)"))
            {
                rpFileName = "3";
            }
            if (rpTitle.Contains("EEC Premium Register I (Direct Business)"))
            {
                rpFileName = "4";
            }
            if (rpTitle.Contains("Motor Premium Register I (Direct Business)"))
            {
                rpFileName = "5";
            }
            if (rpTitle.Contains("EEC Policy Premium Register (Direct Business)"))
            {
                rpFileName = "6";
            }
            if (rpTitle.Contains("Premium Register II (Direct Business)"))
            {
                rpFileName = "7";
            }
            if (rpTitle.Contains("Grand Premium Report (Direct Business)"))
            {
                rpFileName = "8";
            }
            if (rpTitle.Contains("Deferred Acquisition Costs (Direct Business)"))
            {
                rpFileName = "9";
            }
            if (rpTitle.Contains("Facultative Outward Reinsurance (Direct Business)") && rpTitle.Contains("Details"))
            {
                rpFileName = "10";
            }
            if (rpTitle.Contains("Facultative Outward Reinsurance (Direct Business)") && rpTitle.Contains("Summary"))
            {
                rpFileName = "11";
            }
            if (rpTitle.Contains("Surplus Treaty Reinsurance (Direct Business)") && rpTitle.Contains("Details"))
            {
                rpFileName = "12";
            }
            if (rpTitle.Contains("Surplus Treaty Reinsurance (Direct Business)") && rpTitle.Contains("Summary"))
            {
                rpFileName = "13";
            }
            if (rpTitle.Contains("XOL Reinsurance (Direct Business)") && rpTitle.Contains("Details"))
            {
                rpFileName = "14";
            }
            if (rpTitle.Contains("XOL Reinsurance (Direct Business)") && rpTitle.Contains("Summary"))
            {
                rpFileName = "15";
            }
            if (rpTitle.Contains("Facultative Obligatory Reinsurance (Direct Business)") && rpTitle.Contains("Details"))
            {
                rpFileName = "16";
            }
            if (rpTitle.Contains("Facultative Obligatory Reinsurance (Direct Business)") && rpTitle.Contains("Summary"))
            {
                rpFileName = "17";
            }
            if (rpTitle.Contains("Un-Invoiced Premium Analysis"))
            {
                rpFileName = "18";
            }
            if (rpTitle.Contains("Unearned Premium Summary (Direct Business)"))
            {
                rpFileName = "19";
            }
            if (rpTitle.Contains("Grand Premium Report (Facultative Inward)"))
            {
                rpFileName = "20";
            }
            if (rpTitle.Contains("Deferred Acquisition Costs (Facultative Inward)"))
            {
                rpFileName = "21";
            }
            if (rpTitle.Contains("Facultative Outward Reinsurance (Facultative Inward)") && rpTitle.Contains("Details"))
            {
                rpFileName = "22";
            }
            if (rpTitle.Contains("Facultative Outward Reinsurance (Facultative Inward)") && rpTitle.Contains("Summary"))
            {
                rpFileName = "23";
            }
            if (rpTitle.Contains("Surplus Treaty Reinsurance (Facultative Inward)") && rpTitle.Contains("Details"))
            {
                rpFileName = "24";
            }
            if (rpTitle.Contains("Surplus Treaty Reinsurance (Facultative Inward)") && rpTitle.Contains("Summary"))
            {
                rpFileName = "25";
            }
            if (rpTitle.Contains("XOL Reinsurance (Facultative Inward)") && rpTitle.Contains("Details"))
            {
                rpFileName = "26";
            }
            if (rpTitle.Contains("XOL Reinsurance (Facultative Inward)") && rpTitle.Contains("Summary"))
            {
                rpFileName = "27";
            }
            if (rpTitle.Contains("Facultative Obligatory Reinsurance (Facultative Inward)") && rpTitle.Contains("Details"))
            {
                rpFileName = "28";
            }
            if (rpTitle.Contains("Facultative Obligatory Reinsurance (Facultative Inward)") && rpTitle.Contains("Summary"))
            {
                rpFileName = "29";
            }
            if (rpTitle.Contains("Unearned Premium Summary (Facultative Inward)"))
            {
                rpFileName = "30";
            }
            string sql = "INSERT INTO [dbo].[reportrecord]([reportdate],[reportname],[filename],[flowid],[status],[inputdate]) VALUES ('" + inpDateto.ToString("yyyy-MM-dd HH:mm:ss") + "', '" + rpTitle + "', '" + rpFileName + "','', '1', '" + Time.ToString("yyyy-MM-dd HH:mm:ss") + "')";
            if (DBHelper.ExecuteCommand(sql))
            {
                MessageBox.Show("Report Record Inserted!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            cryRptViewer temp_form = new cryRptViewer(cryRpt);
            temp_form.ShowDialog();
        }


        void callExcel(DataSet ds, string optid)
        {
            RptSetFunc.SetRptFormulae(ref rpBusiness, ref rpClasssel, ref rpDatefr, ref rpDateto, ref rpTitle,
                                      vfbus, wfbus, vclasssel, vfbk_fr, vfbk_to, vttytype, vperiodtype, optid);

            String z_company, z_title, z_currency, z_period = "";
            Int16 i;

            z_company = String.Format("{0}",optid == "polprmsm" ? rpChiCompany.Trim() : rpCompany).Trim();
            z_title = String.Format("{0}", rpTitle);
            z_currency = String.Format("{0}", rpCurrency);

            if (optid == "prmunern")
                z_period = String.Format("Current Period: {0} to {1}", rpDatefr, rpDateto);

            if (optid == "prmrel")
                z_period = String.Format("From: {0} to {1}", rpDatefr, rpDateto);

            if (optid == "prmstat")
            {
                rpTitle = (vprmtype.Equals("1") ? "Gross " : "Retained ") + rpTitle;
                z_title = String.Format("{0}", rpTitle);
                z_period = String.Format("From: {0} to {1}", rpDatefr, rpDateto);
            }

            if (optid == "prmfacout")
            {
                rpTitle = rpTitle + (vsumdet.Equals("1") ? " - Summary" : " - Details");
                z_title = String.Format("{0}", rpTitle);
            }

            if (optid == "prmtty")
            {
                rpTitle = rpTitle + (vsumdet.Equals("1") ? " - Summary" : " - Details");
                z_title = String.Format("{0}", rpTitle);
            }

            if (optid == "prmbord")
            {
                //z_title = String.Format("{0}", rpTitle);
                z_period = String.Format("Premium Bordereaux: {0} to {1}", rpDatefr, rpDateto);
            }

            Excel.Application xlApp;
            Excel.Workbook xlWorkBook;
            Excel.Worksheet xlWorkSheet;
            object misValue = System.Reflection.Missing.Value;

            xlApp = new Excel.Application();
            xlWorkBook = xlApp.Workbooks.Add(misValue);
            Excel.Sheets worksheets = xlWorkBook.Worksheets;
            xlWorkSheet = xlWorkBook.Worksheets.get_Item(1);

            int noOfWrksheet = xlWorkBook.Worksheets.Count;

            for (Int32 k = noOfWrksheet; k > 1; k--)
            {
                worksheets[k].Delete();
            }

            Boolean noDetail = false;

            if (optid == "prmgrnd")
            {
                if (ds.Tables[0].Rows.Count == 0)
                    noDetail = true;
                else if (ds.Tables[0].DefaultView[0]["fclass"].ToString().Trim() == "???")
                    noDetail = true;

                i = RptInExcel.WsHeader(xlWorkSheet, optid, z_company, z_title, z_currency, z_period);
                i = RptInExcel.WsColTitlePrmGrnd(xlWorkSheet, vlastasat, vthisasat, noDetail, optid, i);

                if (!noDetail)
                    RptInExcel.WsPrmGrndDetail(xlWorkSheet, ds.Tables[0], optid, i);

            }

            if (optid == "daqcost")
            {
                if (ds.Tables[0].Rows.Count == 0)
                    noDetail = true;
                else if (ds.Tables[0].DefaultView[0]["fclass"].ToString().Trim() == "???")
                    noDetail = true;

                i = RptInExcel.WsHeader(xlWorkSheet, optid, z_company, z_title, z_currency, z_period);
                i = RptInExcel.WsColTitleDaqCost(xlWorkSheet, vlastasat, vthisasat, noDetail, optid, i);

                if (!noDetail)
                    RptInExcel.WsDaqCostDetail(xlWorkSheet, ds.Tables[0], optid, i);
            }

            if (optid == "prmstat")
            {
                if (ds.Tables[0].Rows.Count == 0)
                    noDetail = true;

                i = RptInExcel.WsHeader(xlWorkSheet, optid, z_company, z_title, z_currency, z_period);
                i = RptInExcel.WsColTitlePrmStat(xlWorkSheet, noDetail, optid, vfbk_to.Year, i);

                if (!noDetail)
                    RptInExcel.WsPrmStatDetail(xlWorkSheet, ds.Tables[0], optid, i);
            }

            if ("effpmp".Contains(optid))
            {
                if (ds.Tables[0].Rows.Count == 0)
                    noDetail = true;
                else if (ds.Tables[0].DefaultView[0]["fclass"].ToString().Trim() == "???")
                    noDetail = true;

                i = RptInExcel.WsHeader(xlWorkSheet, optid, z_company, z_title, z_currency, z_period);
                i = RptInExcel.WsColTitleEffPMP(xlWorkSheet, noDetail, optid, i);

                if (!noDetail)
                    RptInExcel.WsEffPMPDetail(xlWorkSheet, ds.Tables[0], optid, i);
            }

            if (rpOptid == "polprmsm")
            {
                if (ds.Tables[0].Rows.Count == 0)
                    noDetail = true;

                i = RptInExcel.WsHeader(xlWorkSheet, optid, z_company, z_title, "單位: 萬元計", z_period);
                i = RptInExcel.WsColTitlePolPrmSm(xlWorkSheet, noDetail, optid, i);

                //if (!noDetail)
                //    RptInExcel.WsPolPrmSmDetail(xlWorkSheet, ds.Tables[0], optid, i);
            }

            if ("prmexpl".Contains(optid))
            {
                if (ds.Tables[0].Rows.Count == 0)
                    noDetail = true;
                else if (ds.Tables[0].DefaultView[0]["fclass"].ToString().Trim() == "???")
                    noDetail = true;

                i = RptInExcel.WsHeader(xlWorkSheet, optid, z_company, z_title, z_currency, z_period);
                i = RptInExcel.WsColTitlePrmExpl(xlWorkSheet, vfbus, noDetail, optid, i);

                if (!noDetail)
                    RptInExcel.WsPrmExplDetail(xlWorkSheet, ds.Tables[0], vfbus, optid, i);
            }
            
            if ("prmreg|prmdrcr|prmrel|polprm".Contains(optid))
            {
                if (ds.Tables[0].Rows.Count == 0)
                    noDetail = true;
                else if (ds.Tables[0].DefaultView[0]["fclass"].ToString().Trim() == "???")
                    noDetail = true;

                i = RptInExcel.WsHeader(xlWorkSheet, optid, z_company, z_title, z_currency, z_period);
                i = RptInExcel.WsColTitlePrmReg(xlWorkSheet, vfbus, noDetail, optid, i);

                if (!noDetail)
                    RptInExcel.WsPrmRegDetail(xlWorkSheet, ds.Tables[0], vfbus, optid, i);
            }

            if (optid == "prmfacout")
            {
                if (ds.Tables[0].Rows.Count == 0)
                    noDetail = true;
                else if (ds.Tables[0].DefaultView[0]["fclass"].ToString().Trim() == "???")
                    noDetail = true;

                i = RptInExcel.WsHeader(xlWorkSheet, optid, z_company, z_title, z_currency, z_period);
                i = RptInExcel.WsColTitlePrmFacOut(xlWorkSheet, noDetail, optid, i);

                if (!noDetail)
                    if (vsumdet == "1")
                        RptInExcel.WsPrmFacOutSumm(xlWorkSheet, ds.Tables[0], vfbus, optid, i);
                    else
                        RptInExcel.WsPrmFacOutDetail(xlWorkSheet, ds.Tables[0], vfbus, optid, i);
            }

            if (optid == "prmtty")
            {
                if (ds.Tables[0].Rows.Count == 0)
                    noDetail = true;
                else if (ds.Tables[0].DefaultView[0]["fclass"].ToString().Trim() == "???")
                    noDetail = true;

                i = RptInExcel.WsHeader(xlWorkSheet, optid, z_company, z_title, z_currency, z_period);
                i = RptInExcel.WsColTitlePrmTty(xlWorkSheet, noDetail, optid, i);

                if (!noDetail)
                    if (vsumdet == "1")
                        RptInExcel.WsPrmTtySumm(xlWorkSheet, ds.Tables[0], vfbus, optid, i);
                    else
                        RptInExcel.WsPrmTtyDetail(xlWorkSheet, ds.Tables[0], vfbus, optid, i);
            }

            if (optid == "prmbord")
            {
                foreach (DataRow dr in ds.Tables[0].Rows)
                {
                    dr["fdbtrtype"] = dr["fdbtrtype"].ToString().Trim();
                    dr["fdbtr"] = dr["fdbtr"].ToString().Trim();
                    dr["fdbdesc"] = dr["fdbdesc"].ToString().Trim();
                }

                DataTable dt = new DataTable();
                dt = ds.Tables[0].DefaultView.ToTable(true,"fdbtrtype","fdbtr","fdbdesc");

                for (Int32 k = 1; k < dt.Rows.Count; k++)
                {
                    xlWorkBook.Worksheets.Add();
                }

                RptInExcel.WbPrmBord(xlWorkBook, ds.Tables[0], dt, optid, vttytype, z_company, z_title, z_currency, z_period);
            }

            if (optid == "prmunern")
            {
                xlWorkBook.Worksheets.Add();
                RptInExcel.WbPrmUnearn(xlWorkBook, ds, vlastasat, vthisasat, optid, z_company, z_title, z_currency, z_period);
            }

            if (optid == "prmuniv")
            {
                for (Int32 k = 1; k < 6; k++)
                {
                    xlWorkBook.Worksheets.Add();
                }

                RptInExcel.WbPrmUnIv(xlWorkBook, ds, vlastasat, vthisasat, vfbk_fr, vfbk_to, optid, z_company, z_title, z_currency, z_period);
            }

            xlApp.Visible = true;

            RptInExcel.releaseObject(xlWorkSheet);
            RptInExcel.releaseObject(worksheets);
            RptInExcel.releaseObject(xlWorkBook);
            RptInExcel.releaseObject(xlApp);
        }
        
        private void btnExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void btnExcel_Click(object sender, EventArgs e)
        {
            if ("prmgrnd|daqcost|prmunern|prmstat".Contains(rpOptid))
                callExcel(CreateViewPrmGrnd(), rpOptid);

            if ("prmuniv".Contains(rpOptid))
                callExcel(CreateViewPrmUniv(), rpOptid);

            if ("prmexpl".Contains(rpOptid))
                callExcel(CreateViewPrmExpl(), rpOptid);

            if ("prmreg|prmdrcr|prmrel|polprm".Contains(rpOptid))
                callExcel(CreateViewPrmReg(), rpOptid);

            if ("prmfacout".Contains(rpOptid))
                callExcel(CreateViewPrmFacOut(), rpOptid);

            if ("prmtty".Contains(rpOptid))
                callExcel(CreateViewPrmTty(), rpOptid);

            if ("prmbord".Contains(rpOptid))
                callExcel(CreateViewPrmBord(), rpOptid);

            if ("effpmp".Contains(rpOptid))
                callExcel(CreateViewEff_PMP(), rpOptid);

            if (rpOptid == "polprmsm")
                callExcel(CreateViewPolPrmSm(), rpOptid);

            //if ("prmreg|prmdrcr|prmrel".Contains(rpOptid))
            //{
            //    string str = ExpExl.DtToExcel(CreateViewPrmReg(), new string[] { "Polno", "Class", "Grepm" },
            //        new string[] { "fpolno", "fclass", "fgpm" }, "test", false);
            //    if (str.Length > 0)
            //    {
            //        MessageBox.Show(str, System.Windows.Forms.Application.ProductName);
            //        return;
            //    }
            //}
        }
    }
}
