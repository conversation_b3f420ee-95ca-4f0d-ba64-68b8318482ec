using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business
{
    public partial class inqpol : Form
    {
        public string Class = "";
        public string Classfctlid = "";
        public string flag = "";
        private DirectPolicy directPolicy;
        private Endorsement endorsement;
        private RIWard rIWard;
        private Inquiry.DirectInquiry directInquiry;
        private Inquiry.RIInquiry rIInquiry;
        private Inquiry.COInquiry cOInquiry;
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public inqpol()
        {
            InitializeComponent();
            InitializeSubclassName();
            InitializeStatusName();
        }

        public inqpol(DirectPolicy directPolicy)
        {
            // TODO: Complete member initialization
            this.directPolicy = directPolicy;
            InitializeComponent();
            InitializeSubclassName();
            InitializeStatusName();
        }

        public inqpol(Endorsement endorsement)
        {
            // TODO: Complete member initialization
            this.endorsement = endorsement;
            InitializeComponent();
            InitializeSubclassName();
            InitializeStatusName();
        }

        public inqpol(RIWard rIWard)
        {
            // TODO: Complete member initialization
            this.rIWard = rIWard;
            InitializeComponent();
            InitializeSubclassName();
            InitializeStatusName();
        }

        public inqpol(Inquiry.DirectInquiry directInquiry)
        {
            // TODO: Complete member initialization
            this.directInquiry = directInquiry;
            InitializeComponent();
            InitializeSubclassName();
            InitializeStatusName();
        }

        public inqpol(Inquiry.RIInquiry rIInquiry)
        {
            // TODO: Complete member initialization
            this.rIInquiry = rIInquiry;
            InitializeComponent();
            InitializeSubclassName();
            InitializeStatusName();
        }

        public inqpol(Inquiry.COInquiry cOInquiry)
        {
            // TODO: Complete member initialization
            this.cOInquiry = cOInquiry;
            InitializeComponent();
            InitializeSubclassName();
            InitializeStatusName();
        }

        public void InitializeSubclassName()
        {
            DataTable dt = new DataTable();
            string sql = "SELECT 'ALL' as fid union select RTRIM(fid) as fid from " + Dbm + "msinsc where fctlid_1 = '" + Classfctlid + "'";
            dt = DBHelper.GetDataSet(sql);
            this.subclass.DisplayMember = "fid";
            this.subclass.ValueMember = "fid";
            this.subclass.DataSource = dt;
        }

        void InitializeStatusName()
        {
            DataTable dt = new DataTable();
            string sql = "SELECT 'ALL' as fid,'0' as value union "+
                        "select distinct case when fconfirm='1' then 'Pending' else  "+
                        "case when fconfirm='2' then 'Hold' else "+
                        "case when fconfirm='3' then 'Confirmed' "+
                        "else 'Cancelled' end end end as fid, fconfirm as value from polh ";
            dt = DBHelper.GetDataSet(sql);
            this.status.DisplayMember = "fid";
            this.status.ValueMember = "value";
            this.status.DataSource = dt;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            string sqladd = "";
            foreach (Control ctrl in panel1.Controls)
            {
                if (ctrl is TextBox)
                {
                    if (((TextBox)ctrl).Text==""){
                        sqladd = "";
                    }
                }
                if (ctrl is MaskedTextBox)
                {
                    if (((MaskedTextBox)ctrl).Text == "")
                    {
                        sqladd = "";
                    }
                }
            }
            if (textBox1.Text != "") {
                sqladd = "And a.fpolno = '" + textBox1.Text.Trim() + "' ";
            }
            if (textBox2.Text != "") {
                sqladd = sqladd + "And a.fpolno = (select fpolno from polh where fendtno ='" + textBox2.Text.Trim() + "') ";
            }
            if (textBox3.Text != "") {
                sqladd = sqladd + "And a.fuwyr = '" + textBox3.Text.Trim() + "' ";
            }
            if (subclass.SelectedValue.ToString ().Trim () != "ALL") {
                sqladd = sqladd + "And a.fsclass = '" + subclass.SelectedValue + "' ";
            }
            if (fefffr.Text != "    .  ." && feffto.Text != "    .  .") {
                sqladd = sqladd + "And a.fissdate between '" + fefffr.Text + "' and  '" + feffto.Text + "' ";
            }
            if (textBox4.Text != "")
            {
                sqladd = sqladd + "And a.fprdr = '" + textBox4.Text.Trim() + "' ";
            }
            if (textBox5.Text != "")
            {
                sqladd = sqladd + "And a.fclnt = '" + textBox5.Text.Trim() + "' ";
            }
            if (textBox6.Text != "" && textBox6.Text.ToString().Contains("%"))
            {
                sqladd = sqladd + "And a.finsd like N'" + textBox6.Text.Trim() + "' ";
            }
            if (textBox6.Text != "" && !textBox6.Text.ToString().Contains("%"))
            {
                sqladd = sqladd + "And a.finsd = '" + textBox6.Text.Trim() + "' ";
            }
            if (textBox7.Text != "" && textBox7.Text.ToString().Contains("%"))
            {
                sqladd = sqladd + "And a.fempyr like N'" + textBox7.Text.Trim() + "' ";
            }
            if (textBox7.Text != "" && !textBox7.Text.ToString().Contains("%"))
            {
                sqladd = sqladd + "And a.fempyr = '" + textBox7.Text.Trim() + "' ";
            }
            if (textBox8.Text != "" && textBox8.Text.ToString().Contains("%"))
            {
                sqladd = sqladd + "And a.fcontrt_t1 like N'" + textBox8.Text.Trim() + "' ";
            }
            if (textBox8.Text != "" && !textBox8.Text.ToString().Contains("%"))
            {
                sqladd = sqladd + "And a.fcontrt = '" + textBox8.Text.Trim() + "' ";
            }
            if (textBox9.Text != "" && textBox9.Text.ToString().Contains("%"))
            {
                sqladd = sqladd + "And a.fsite like N'" + textBox9.Text.Trim() + "' ";
            }
            if (textBox9.Text != "" && !textBox9.Text.ToString().Contains("%"))
            {
                sqladd = sqladd + "And a.fsite = '" + textBox9.Text.Trim() + "' ";
            }
            if (fefffr1.Text != "    .  ." && feffto1.Text != "    .  .")
            {
                sqladd = sqladd + "And a.fefffr between '" + fefffr1.Text + "' and  '" + feffto1.Text + "' ";
            }
            if (fefffr2.Text != "    .  ." && feffto2.Text != "    .  .")
            {
                sqladd = sqladd + "And a.feffto between '" + fefffr2.Text + "' and  '" + feffto2.Text + "' ";
            }
            if (status.SelectedValue.ToString() != "0" && status != null)
            {
                sqladd = sqladd + "And a.fconfirm = '" + status.SelectedValue + "' ";
            }
            if (fregno.Text != "")
            {
                sqladd = sqladd + "And b.frno = '" + fregno.Text.Trim() + "' ";
            }
            if (flag == "Direct")
            {
                directPolicy.FillData(sqladd);
            }
            if (flag == "RIWard")
            {
               rIWard.FillData(sqladd);
            }
            if (flag == "DirectInquiry")
            {
                directInquiry.FillData(sqladd);
            }
            if (flag == "RIInquiry")
            {
                rIInquiry.FillData(sqladd);
            }
            if (flag == "COInquiry")
            {
                cOInquiry.FillData(sqladd);
            }
            
            this.Close();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            this.Close();
        }


    }
}
