using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business
{
    public partial class reqinv : Form
    {
        public string Class = "";
        public string fbus = "";
        public string flag = "";
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        private DrCrNote drCrNote;
        private DrCrNoteFacIn drCrNoteFacIn;
        private DrCrNoteFacOut drCrNoteFacOut;
        public reqinv()
        {
            InitializeComponent();
            InitializeSubclassName();
            InitializeStatusName();
            Initializefdbtrtype();
        }

        public reqinv(DrCrNote drCrNote)
        {
            // TODO: Complete member initialization
            this.drCrNote = drCrNote;
            InitializeComponent();
            InitializeSubclassName();
            InitializeStatusName();
        }

        public reqinv(DrCrNoteFacIn drCrNoteFacIn)
        {
            // TODO: Complete member initialization
            this.drCrNoteFacIn = drCrNoteFacIn;
            InitializeComponent();
            InitializeSubclassName();
            InitializeStatusName();
            Initializefdbtrtype();
        }

        public reqinv(DrCrNoteFacOut drCrNoteFacOut)
        {
            // TODO: Complete member initialization
            this.drCrNoteFacOut = drCrNoteFacOut;
            InitializeComponent();
            InitializeSubclassName();
            InitializeStatusName();
            Initializefdbtrtype();
        }

        public void InitializeSubclassName()
        {
            DataTable dt = new DataTable();
            string sql = "SELECT 'ALL' as fid union select RTRIM(fid) as fid from " + Dbm + "minsc ";
            dt = DBHelper.GetDataSet(sql);
            this.fclass.DisplayMember = "fid";
            this.fclass.ValueMember = "fid";
            this.fclass.DataSource = dt;
        }

        void InitializeStatusName()
        {
            BindData[] u_Statusarr = new BindData[]  {
                                 new BindData("0","ALL"),
                                 new BindData("1","Yes"),
                                 new BindData("2","No")};
            fpost.DataSource = u_Statusarr;
            fpost.ValueMember = "ID";
            fpost.DisplayMember = "Item";
        }

        public void Initializefdbtrtype()
        {
            BindData[] u_dtypearr = new BindData[]  {
                                 new BindData("A","ALL"),
                                 new BindData("C","Client"),
                                 new BindData("P","Producer")};
            BindData[] u_dtypearr2 = new BindData[]  {
                                 new BindData("A","ALL"),
                                 new BindData("B","R/I Broker"),
                                 new BindData("R","Reinsurer")};
            if (flag == "drCrNote" && fbus == "D") { fdbtrtype.DataSource = u_dtypearr; } else { fdbtrtype.DataSource = u_dtypearr2; }
            fdbtrtype.ValueMember = "ID";
            fdbtrtype.DisplayMember = "Item";
        }

        private void button1_Click(object sender, EventArgs e)
        {
            Initializefdbtrtype();
            string sqladd = "";
            foreach (Control ctrl in panel1.Controls)
            {
                if (ctrl is TextBox)
                {
                    if (((TextBox)ctrl).Text==""){
                        sqladd = "";
                    }
                }
                if (ctrl is MaskedTextBox)
                {
                    if (((MaskedTextBox)ctrl).Text == "")
                    {
                        sqladd = "";
                    }
                }
            }
            if (fclass.SelectedValue.ToString().Trim() != "ALL")
            {
                sqladd = sqladd + "And a.fclass = '" + fclass.SelectedValue + "' ";
            }
            if (fdbtrtype.SelectedValue.ToString().Trim() != "A")
            {
                sqladd = sqladd + "And a.fdbtrtype = '" + fdbtrtype.SelectedValue + "' ";
            }
            if (finvno.Text != "") {
                sqladd = "And a.finvno = '" + finvno.Text.Trim() + "' ";
            }
            if (fpolno.Text != "")
            {
                sqladd = sqladd + "And a.fpolno = '" + fpolno.Text.Trim() + "' ";
            }
            if (fdbtr.Text != "")
            {
                sqladd = sqladd + "And a.fdbtr = '" + fdbtr.Text.Trim() + "' ";
            }
            if (fprdr.Text != "")
            {
                sqladd = sqladd + "And a.fprdr = '" + fprdr.Text.Trim() + "' ";
            }
            if (fclnt.Text != "")
            {
                sqladd = sqladd + "And a.fclnt = '" + fclnt.Text.Trim() + "' ";
            }
            if (fremark.Text != "" && fremark.Text.ToString().Contains("%"))
            {
                sqladd = sqladd + "And a.fremark like N'" + fremark.Text.Trim() + "' ";
            }
            if (fremark.Text != "" && !fremark.Text.ToString().Contains("%"))
            {
                sqladd = sqladd + "And a.fremark = '" + fremark.Text.Trim() + "' ";
            }
            if (fissdate.Text != "    .  ." && feffto.Text != "    .  .") {
                sqladd = sqladd + "And a.fissdate between '" + fissdate.Text + "' and  '" + feffto.Text + "' ";
            }
            if (finvdate.Text != "    .  ." && feffto1.Text != "    .  .")
            {
                sqladd = sqladd + "And a.finvdate between '" + finvdate.Text + "' and  '" + feffto1.Text + "' ";
            }
            if (fbilldate.Text != "    .  ." && feffto2.Text != "    .  .")
            {
                sqladd = sqladd + "And a.fbilldate between '" + fbilldate.Text + "' and  '" + feffto2.Text + "' ";
            }
            if (fpost.SelectedValue.ToString() != "0" && fpost != null)
            {
                sqladd = sqladd + "And a.fposted = '" + fpost.SelectedValue + "' ";
            }
            if (sqladd == "") { sqladd = sqladd + "And a.fbus = '" + fbus + "' "; }
            if (flag == "drCrNote")
            {
                drCrNote.FillData("",sqladd,"");
            }
            if (flag == "DrCrNoteFacIn")
            {
                drCrNoteFacIn.FillData("", sqladd, "");
            }
            if (flag == "DrCrNoteFacOut")
            {
                drCrNoteFacOut.FillData("", sqladd, "");
            }
            this.Close();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            this.Close();
        }


    }
}
