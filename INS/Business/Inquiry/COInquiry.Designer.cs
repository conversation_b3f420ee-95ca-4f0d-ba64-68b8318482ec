namespace INS.Business.Inquiry
{
    partial class COInquiry
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.Policylist = new System.Windows.Forms.DataGridView();
            this.ReQuery = new System.Windows.Forms.Button();
            this.OrderBy = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.button1 = new System.Windows.Forms.Button();
            this.Exit = new System.Windows.Forms.Button();
            ((System.ComponentModel.ISupportInitialize)(this.Policylist)).BeginInit();
            this.SuspendLayout();
            // 
            // Policylist
            // 
            this.Policylist.AllowUserToAddRows = false;
            this.Policylist.AllowUserToDeleteRows = false;
            this.Policylist.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.Policylist.BackgroundColor = System.Drawing.SystemColors.Control;
            this.Policylist.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.Policylist.Location = new System.Drawing.Point(19, 66);
            this.Policylist.Name = "Policylist";
            this.Policylist.ReadOnly = true;
            this.Policylist.Size = new System.Drawing.Size(864, 454);
            this.Policylist.TabIndex = 7;
            this.Policylist.SelectionChanged += new System.EventHandler(this.Policylist_SelectionChanged);
            // 
            // ReQuery
            // 
            this.ReQuery.Location = new System.Drawing.Point(693, 24);
            this.ReQuery.Name = "ReQuery";
            this.ReQuery.Size = new System.Drawing.Size(75, 21);
            this.ReQuery.TabIndex = 6;
            this.ReQuery.Text = "ReQuery";
            this.ReQuery.UseVisualStyleBackColor = true;
            this.ReQuery.Click += new System.EventHandler(this.ReQuery_Click);
            // 
            // OrderBy
            // 
            this.OrderBy.FormattingEnabled = true;
            this.OrderBy.Items.AddRange(new object[] {
            "Policy#"});
            this.OrderBy.Location = new System.Drawing.Point(139, 24);
            this.OrderBy.Name = "OrderBy";
            this.OrderBy.Size = new System.Drawing.Size(121, 20);
            this.OrderBy.TabIndex = 5;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(79, 27);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(49, 12);
            this.label1.TabIndex = 4;
            this.label1.Text = "Order By";
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(693, 541);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(75, 23);
            this.button1.TabIndex = 8;
            this.button1.Text = "Policy Info";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // Exit
            // 
            this.Exit.Location = new System.Drawing.Point(774, 541);
            this.Exit.Name = "Exit";
            this.Exit.Size = new System.Drawing.Size(75, 23);
            this.Exit.TabIndex = 9;
            this.Exit.Text = "Exit";
            this.Exit.UseVisualStyleBackColor = true;
            this.Exit.Click += new System.EventHandler(this.Exit_Click);
            // 
            // DirectInquiry
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(888, 578);
            this.Controls.Add(this.Exit);
            this.Controls.Add(this.button1);
            this.Controls.Add(this.Policylist);
            this.Controls.Add(this.ReQuery);
            this.Controls.Add(this.OrderBy);
            this.Controls.Add(this.label1);
            this.Name = "DirectInquiry";
            this.Text = "DirectInquiry";
            ((System.ComponentModel.ISupportInitialize)(this.Policylist)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.DataGridView Policylist;
        private System.Windows.Forms.Button ReQuery;
        private System.Windows.Forms.ComboBox OrderBy;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.Button Exit;
    }
}