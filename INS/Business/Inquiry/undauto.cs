using CrystalDecisions.CrystalReports.Engine;
using INS.Business.objRpt;
using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace INS.Business.Inquiry
{
    public partial class undauto : Form
    {
        public string vfbus, vfpolno, vstatus, lc_business, lc_datefr, lc_dateto, p_ftype;
        public DateTime vfbk_fr, vfbk_to;
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        private string rpDirectory = "M:\\Software II\\New INS Runtime\\Setup\\objRpt";

        public undauto()
        {
            DateTime time = DateTime.Now;
            InitializeComponent();
            InitCobBus();
            InitCobCls();
            fbilfr.Text = time.ToString("yyyy-MM-dd");
            fbilto.Text = time.ToString("yyyy-MM-dd");
        }

        private void InitCobCls()
        {
            DataTable dt = new DataTable();
            string sql = "select RTRIM(fid) as fid, RTRIM(fid) as fidval from minsc";

            SqlDataAdapter da = new SqlDataAdapter(sql, DBConnect.mstdbConn);

            da.Fill(dt);
            da.Dispose();

            DataRow row = dt.NewRow();

            row["Fid"] = "All";
            row["Fidval"] = "A";

            dt.Rows.InsertAt(row, 0);

            this.fclass.DisplayMember = "fid";
            this.fclass.ValueMember = "fidval";
            this.fclass.DataSource = dt;
        }

        private void InitCobBus()
        {
            String[,] arr = new String[,] { { "Direct", "D" }, { "Fac In", "R" } };
            DataTable dt = new DataTable();
            dt.Columns.Add("String", typeof(String));
            dt.Columns.Add("Value", typeof(String));
            for (int i = 0; i < arr.GetLength(0); i++)
            {
                string strText = arr[i, 0], strValue = arr[i, 1];
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = strValue;
                dt.Rows.Add(aRow);
            }

            ftype.DataSource = dt;
            ftype.DisplayMember = "String";
            ftype.ValueMember = "Value";
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            prmexpn();
        }

        void SetFormul() {
            vfbus = ftype.SelectedValue.ToString().Trim();
            vfbk_fr = DateTime.ParseExact(fbilfr.Text.Trim(), "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);
            vfbk_to = DateTime.ParseExact(fbilto.Text.Trim(), "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);
            vstatus = "3";
            p_ftype = "E";
            if (vfbus == "%")
                lc_business = "(Direct + Fac Inward)";
            else if (vfbus == "D")
                lc_business = "(Direct Business)";
            else
                lc_business = "(Facultative Inward)";

            lc_datefr = String.Format("{0:dd MMM yyyy}", vfbk_fr);
            lc_dateto = String.Format("{0:dd MMM yyyy}", vfbk_to);
        }

        void prmexpn() {
            SetFormul();
            string sql ="SELECT a.*, case when '" + p_ftype + "' ='E' then a.fexno else a.frnno end as fautono,  " +
                        "case when a.fincto between '" + vfbk_fr + "' and '" + vfbk_to + "' then a.fincto else case when a.feffto between '" + vfbk_fr + "' and '" + vfbk_to + "' then a.feffto else a.fmntto end end  as fexpire, "+
                        "case when a.fmntfr is null then 'N' else 'Y' end as prmnt,'' as fclsseq,'' as fclsname, c.fdesc as fpdesc,c.fadd1 as fpadd1,c.fadd2 as fpadd2,c.fadd3 as fpadd3,c.fadd4 as fpadd4," +
                        "b.fdesc as fcdesc,b.fadd1 as fcadd1,b.fadd2 as fcadd2,b.fadd3 as fcadd3,b.fadd4 as fcadd4 " +
                        "FROM polh a  "+
                        "left join (select * from " + Dbm + "mprdr where ftype=(case when '"+vfbus+"' ='D' then 'C' else 'R' end)) b on b.fid = a.fclnt   "+
                        "left join (select * from " + Dbm + "mprdr where ftype=(case when '" + vfbus + "' ='D' then 'P' else 'B' end)) c on c.fid = a.fprdr   " +
                        "WHERE fctlid_p in   "+
                        "(select fctlid_p from polh  "+
                        "where ((feffto between '" + vfbk_fr + "' and '" + vfbk_to + "') or (fincto between '" + vfbk_fr + "' and '" + vfbk_to + "') or (fmntto between '" + vfbk_fr + "' and '" + vfbk_to + "'))  " +
                        "and fconfirm = '" + vstatus + "' and fbus = '" + vfbus + "' and fpolno like '%" + fpolno.Text + "%' group by fctlid_p)  ";
            DataTable dt = DBHelper.GetDataSet(sql);

            ReportDocument cryRpt = new ReportDocument();
            cryRpt.Load(rpDirectory + "\\expgenl.rpt");
            cryRpt.SetDataSource(dt);
            cryRpt.DataDefinition.FormulaFields["z_title"].Text ="'Expiry Note Generation List "+lc_business+": from "+lc_datefr+" to "+lc_dateto+"'";
            cryDocViewer temp_form = new cryDocViewer(cryRpt);
            temp_form.ShowDialog();
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

    }
}
