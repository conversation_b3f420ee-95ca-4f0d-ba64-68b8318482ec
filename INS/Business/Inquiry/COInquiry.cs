using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using INS.Business;

namespace INS.Business.Inquiry
{
    public partial class COInquiry : Form
    {
        public int Policylistrow = 0;
        public string Class = "";
        public string policyNo = "";
        public string fctlid = "";
        public string fconfirm = "";
        public string com = "";
        public string disc = "";

        public COInquiry()
        {
            InitializeComponent();
        }

        public void FillData(string order)
        {
            String Sql = "select fctlid, fcom, ftd, fpolno as Policy#,fclass as Class, fissdate as [Issue Date],fefffr as [Effect Fr],feffto as [Effect To], " +
                        "fprdr as Producer#,finsd as Contractors,fexno as [Expiry No.],frnno as [Renew No.], " +
                        "case when fconfirm='1' then 'Pending' else  " +
                        "case when fconfirm='2' then 'Hold' else " +
                        "case when fconfirm='3' then 'Confirmed' else 'Cancelled' end end end as Status " +
                        "from polh where fbus='K' and ftype = 'P' ";
            Sql = Sql + order;
            Sql = Sql + "order by fpolno";

            Policylist.DataSource = DBHelper.GetDataSet(Sql);
            this.Policylist.Columns[0].Visible = false;
            this.Policylist.Columns[1].Visible = false;
            this.Policylist.Columns[2].Visible = false;

        }

        private void Policylist_SelectionChanged(object sender, EventArgs e)
        {
            if (Policylist.CurrentCell != null)
            {
                Policylistrow = Policylist.CurrentCell.RowIndex;
            }
            else { return; }

            if (Policylist.Rows[Policylistrow].Cells["Policy#"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["Policy#"].Value.ToString().Length != 0)
                {
                    policyNo = Policylist.Rows[Policylistrow].Cells["Policy#"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["Class"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["Class"].Value.ToString().Length != 0)
                {
                    Class = Policylist.Rows[Policylistrow].Cells["Class"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["fctlid"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["fctlid"].Value.ToString().Length != 0)
                {
                    fctlid = Policylist.Rows[Policylistrow].Cells["fctlid"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["Status"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["Status"].Value.ToString().Length != 0)
                {
                    fconfirm = Policylist.Rows[Policylistrow].Cells["Status"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["fcom"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["fcom"].Value.ToString().Length != 0)
                {
                    com = Policylist.Rows[Policylistrow].Cells["fcom"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["ftd"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["ftd"].Value.ToString().Length != 0)
                {
                    disc = Policylist.Rows[Policylistrow].Cells["ftd"].Value.ToString();
                }
            }

        }

        private void ReQuery_Click(object sender, EventArgs e)
        {
            inqpol tempform = new inqpol(this);
            tempform.Class = Class;
            tempform.Text = "Inquiry - " + ClassType(Class);
            tempform.flag = "COInquiry";
            tempform.InitializeSubclassName();
            tempform.ShowDialog();
        }

        static string ClassType(string value)
        {
            string Name = "";
            switch (value)
            {
                case "CAR":
                    Name = "Contractors' All Risks";
                    break;
                case "EEC":
                    Name = "Employees' Compensation";
                    break;
                case "CGL":
                    Name = "Public Liability / General Liability";
                    break;
                case "CPM":
                    Name = "Contractors' Plant and Machinery";
                    break;
                case "MYP":
                    Name = "Money Insurance";
                    break;
                case "PAR":
                    Name = "Property All Risks";
                    break;
                case "FGP":
                    Name = "Fidelity Guarantee";
                    break;
                case "PMP":
                    Name = "Private Car Insurance";
                    break;
                case "CMP":
                    Name = "Commercial Vehicle Insurance";
                    break;
                case "CSB":
                    Name = "Bond Insurance";
                    break;
                case "PII":
                    Name = "Professional Indemnity Insurance";
                    break;
                case "GLF":
                    Name = "Golf Insurance";
                    break;
                case "CLL":
                    Name = "Contractor Liability";
                    break;
            }
            return Name;
        }

        static string Classfctlid(string value)
        {
            string Name = "";
            switch (value)
            {
                case "CAR":
                    Name = "0000000077";
                    break;
                case "EEC":
                    Name = "0000000078";
                    break;
                case "CGL":
                    Name = "0000000100";
                    break;
                case "CPM":
                    Name = "0000000101";
                    break;
                case "MYP":
                    Name = "0000000102";
                    break;
                case "PAR":
                    Name = "0000000103";
                    break;
                case "FGP":
                    Name = "0000000104";
                    break;
                case "PMP":
                    Name = "0000000111";
                    break;
                case "CMP":
                    Name = "0000000112";
                    break;
                case "CSB":
                    Name = "0000000115";
                    break;
                case "PII":
                    Name = "0000000116";
                    break;
                case "GLF":
                    Name = "0000000117";
                    break;
                case "CLL":
                    Name = "0000000119";
                    break;
            }
            return Name;
        }

        private void Exit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            string sqladd = "";
            if (policyNo.Trim() != "")
            {
                sqladd = "And fpolno = '" + policyNo.Trim() + "' ";
                DirectPolicy tempform = new DirectPolicy(this);
                tempform.Class = Class.Trim();
                tempform.Fbus = "K";
                tempform.Classfctlid = Classfctlid(Class.Trim());
                tempform.FillData(sqladd);
                tempform.ShowDialog();
            }
            else
            {
                if (fctlid.Trim() != "")
                {
                    sqladd = "And fctlid = '" + fctlid.Trim() + "' ";
                    DirectPolicy tempform = new DirectPolicy(this);
                    tempform.Class = Class.Trim();
                    tempform.Fbus = "K";
                    tempform.Classfctlid = Classfctlid(Class.Trim());
                    tempform.FillData(sqladd);
                    tempform.ShowDialog();
                }
            }
        }

    }
}
