using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using System.Collections;
using System.Configuration;
using System.Text.RegularExpressions;
using System.Globalization;
using INS.Business.Report;
using INS.Business;

namespace INS
{
    public partial class DrCrNoteFacOut : Form
    {
        public DrCrNoteFacOut()
        {
            InitializeComponent();
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            InitCombobox();
            InitCombobox1();
            userRight();
            FillData("", "", "");
        }

        public DrCrNoteFacOut(Ctrl.Reins.FacInstall facInstall)
        {
            // TODO: Complete member initialization
            this.facInstall = facInstall;
            InitializeComponent();
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            InitCombobox();
            userRight();
            FillData("", "", "");
        }

        public DrCrNoteFacOut(Business.FrmBNotes frmBNotes)
        {
            // TODO: Complete member initialization
            this.frmBNotes = frmBNotes;
            InitializeComponent();
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            InitCombobox();
            userRight();
            FillData("", "", "");
        }

        public DrCrNoteFacOut(Ctrl.Reins.FacInstallPar facInstallPar)
        {
            // TODO: Complete member initialization
            this.facInstallPar = facInstallPar;
            InitializeComponent();
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            InitCombobox();
            userRight();
            FillData("", "", "");
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
            {
                SendKeys.Send("{TAB}");
            }
        }
        DES des = new DES();
        DBConnect operate = new DBConnect();
        ExportDBF DBF = new ExportDBF();
        XLS txt = new XLS();
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public string fctlid_1 = "", fbus = "";
        public String oldrow = "", fctlid = "", pfinvno = "", pbill = "";
        private Ctrl.DirectInstall directInstall;
        private Ctrl.RIInstall rIInstall;
        private Ctrl.EndorInstall endorInstall;
        private Business.FrmBNotes frmBNotes;
        private Ctrl.Reins.FacInstall facInstall;
        public Boolean requery = false;

        public void userRight()
        {
            string p_fright = InsEnvironment.LoginUser.GetUserRight3a();
            string g_user = InsEnvironment.LoginUser.GetUserCode();
            string finpuser = "";
            Boolean ll_con = false;
            if ((finpuser == g_user && p_fright.Substring(3, 1) == "1") || (finpuser != g_user && p_fright.Substring(4, 1) == "1"))
            {
                if (dupdate.Visible == true) { dupdate.Visible = true; lupdate.Visible = true; }
            }
            else
            {
                dupdate.Visible = false; lupdate.Visible = false;
            }
            if (p_fright.Substring(1, 1) == "1")
            {
                if (btprint.Visible == true) { btprint.Visible = true; lprint.Visible = true; }
            }
            else
            {
                btprint.Visible = false; lprint.Visible = false;
            }
            if ((finpuser == g_user && p_fright.Substring(5, 1) == "1") || (finpuser != g_user && p_fright.Substring(6, 1) == "1"))
            {
                ll_con = true;
            }
            if (ll_con == true)
            {
                if (Postirng.Visible == true) { Postirng.Visible = true; btPost.Visible = true; }
            }
            else
            {
                Postirng.Visible = false; btPost.Visible = false;
            }
            dupdate.Visible = true;
        }

        public void FillData(string order, string query, string list)
        {
            try
            {
                if (requery) { req.Visible = true; }
                string sql = "select a.finvno as [Dr/Cr No.], a.finstall as [Install#],a.fbilldate as [Bill Date],  " +
                        "a.finvdate as [Doc Date],case when a.fdbtrtype='B' then 'Broker' when a.fdbtrtype='C' then 'Client'  " +
                        "when a.fdbtrtype='P' then 'Producer' else 'Reinsurer' end as [A/C Type],a.fdbtr as [A/C#], " +
                        "a.freinsr as [Reinsr#], a.fpolno as [Policy No.],a.fendtno as [Endt No.],a.fissdate as [Issue Date], " +
                        "case when a.fposted=1 then 'Yes' else 'No' end as [Posted], a.fclass,a.fsclass, " +
                        "a.fgpm,a.fbrkge,a.fbrkgeamt,a.fcomamt,a.fcom, a.fnpm,a.fremark,a.fcremark,a.fpayable, a.fmiscamt, " +
                        "e.fdesc as fclassdesc,d.fdesc as facdesc,a.freinsr,f.fdesc as prdrdesc,a.fbrkr,c.fdesc as fclntdesc, " +
                        "b.finsd,b.faddr_t1,CONVERT(varchar(10),a.fefffr,102) as fefffr,a.finpuser,a.finpdate,a.fupduser,a.fupddate,b.fcedepol,b.fcedeendt, " +
                        "a.fcnfuser,a.fcnfdate, case when fintadj=1 then 'Yes' else 'No' end as fintadj, " +
                        "a.ftotinstal, e.fcdesc,a.fpmcur, a.fctlid, a.fstatus  " +
                        "from oriinvh a left join polh b on a.fctlid_e = b.fctlid  " +
                        "left join " + Dbm + "[mprdr] c on c.fid=a.fbrkr  " +
                        "left join " + Dbm + "[mprdr] d on d.fid=a.fdbtr and d.ftype = a.fdbtrtype " +
                        "left join " + Dbm + "[mprdr] f on f.fid=a.freinsr  " +
                        "left join " + Dbm + "minsc e on e.fid=a.fclass where a.fcrterm =0 ";
                if (query != "") { sql = sql + query; }
                else
                {
                    if (list == "")
                    {
                        sql = sql + "and a.fctlid_e = " +
                            "(select distinct fctlid_1 from orih where fctlid='" + fctlid_1 + "') " +
                            "order by a.fposted, a.finvno";
                    }
                    else
                    {
                        sql = sql + "order by posted desc,a.finvno ";
                    }
                }

                dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
                dataGridView1.DataSource = DBHelper.GetDataSet(sql);
                for (int i = 11; i < dataGridView1.ColumnCount; i++)
                {
                    this.dataGridView1.Columns[i].Visible = false;
                }
                dataGridView1.CellFormatting +=
            new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.dataGridView1_CellFormatting);
            }
            catch { }
        }

        private void dataGridView1_CellFormatting(object sender,
      System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {

            // Set the background to red for negative values in the Balance column.
            if (dataGridView1.Columns[e.ColumnIndex].Name.Equals("Install#"))
            {
                e.CellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                dataGridView1.Columns[e.ColumnIndex].AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
                dataGridView1.Columns[e.ColumnIndex].Width = 35;
            }
            if (dataGridView1.Columns[e.ColumnIndex].Name.Equals("Dr/Cr No."))
            {
                dataGridView1.Columns[e.ColumnIndex].Width = 85;
            }
            if (dataGridView1.Columns[e.ColumnIndex].Name.Equals("Policy No."))
            {
                dataGridView1.Columns[e.ColumnIndex].Width = 90;
            }
            if (dataGridView1.Columns[e.ColumnIndex].Name.Equals("Bill Date"))
            {
                dataGridView1.Columns[e.ColumnIndex].Width = 62;
            }
            if (dataGridView1.Columns[e.ColumnIndex].Name.Equals("Issue Date"))
            {
                dataGridView1.Columns[e.ColumnIndex].Width = 62;
            }
            if (dataGridView1.Columns[e.ColumnIndex].Name.Equals("Doc Date"))
            {
                dataGridView1.Columns[e.ColumnIndex].Width = 62;
            }
            if (dataGridView1.Columns[e.ColumnIndex].Name.Equals("Posted"))
            {
                dataGridView1.Columns[e.ColumnIndex].Width = 35;
            }
        }

        private void dataGridView1_CurrentCellChanged(object sender, EventArgs e)
        {
            GridViewchg();
        }

        void GridViewchg()
        {
            if (dataGridView1.CurrentCell != null)
            {
                int counter;
                counter = dataGridView1.CurrentCell.RowIndex;
                if (dataGridView1.Rows[counter].Cells["Dr/Cr No."].Value != null)
                {
                    finvno.Text = dataGridView1.Rows[counter].Cells["Dr/Cr No."].Value.ToString().Trim();
                    pfinvno = finvno.Text;
                }
                if (dataGridView1.Rows[counter].Cells["Install#"].Value != null)
                {
                    finstall.Text = dataGridView1.Rows[counter].Cells["Install#"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["Bill Date"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Bill Date"].Value.ToString() != "")
                    {
                        try
                        {
                            string a = dataGridView1.Rows[counter].Cells["Bill Date"].Value.ToString().Trim();
                            DateTime b = Convert.ToDateTime(a);
                            fbilldate.Text = b.ToString("yyyy.MM.dd");
                            pbill = fbilldate.Text;
                        }
                        catch { }
                    }
                    else { fbilldate.Text = ".NULL..NULL."; }
                }
                if (dataGridView1.Rows[counter].Cells["Doc Date"].Value != null)
                {
                    try
                    {
                        string a = dataGridView1.Rows[counter].Cells["Doc Date"].Value.ToString().Trim();
                        DateTime b = Convert.ToDateTime(a);
                        finvdate.Text = b.ToString("yyyy.MM.dd");
                    }
                    catch { }
                }
                if (dataGridView1.Rows[counter].Cells["fclass"].Value != null)
                {
                    fclass.Text = dataGridView1.Rows[counter].Cells["fclass"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fclassdesc"].Value != null)
                {
                    fclassdesc.Text = dataGridView1.Rows[counter].Cells["fclassdesc"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fsclass"].Value != null)
                {
                    fsclass.Text = dataGridView1.Rows[counter].Cells["fsclass"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["Policy No."].Value != null)
                {
                    fpolno.Text = dataGridView1.Rows[counter].Cells["Policy No."].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["Endt No."].Value != null)
                {
                    fendtno.Text = dataGridView1.Rows[counter].Cells["Endt No."].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["Issue Date"].Value != null)
                {
                    try
                    {
                        string a = dataGridView1.Rows[counter].Cells["Issue Date"].Value.ToString().Trim();
                        DateTime b = Convert.ToDateTime(a);
                        fissdate.Text = b.ToString("yyyy.MM.dd");
                    }
                    catch { }
                }
                if (dataGridView1.Rows[counter].Cells["A/C#"].Value != null)
                {
                    fdbtr.Text = dataGridView1.Rows[counter].Cells["A/C#"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["facdesc"].Value != null)
                {
                    fdbtrdesc.Text = dataGridView1.Rows[counter].Cells["facdesc"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["freinsr"].Value != null)
                {
                    fprdr.Text = dataGridView1.Rows[counter].Cells["freinsr"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["prdrdesc"].Value != null)
                {
                    fpdesc.Text = dataGridView1.Rows[counter].Cells["prdrdesc"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fbrkr"].Value != null)
                {
                    fclnt.Text = dataGridView1.Rows[counter].Cells["fbrkr"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fclntdesc"].Value != null)
                {
                    fclntdesc.Text = dataGridView1.Rows[counter].Cells["fclntdesc"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["finsd"].Value != null)
                {
                    finsd.Text = dataGridView1.Rows[counter].Cells["finsd"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["faddr_t1"].Value != null)
                {
                    faddr.Text = dataGridView1.Rows[counter].Cells["faddr_t1"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fremark"].Value != null)
                {
                    fremark.Text = dataGridView1.Rows[counter].Cells["fremark"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fcremark"].Value != null)
                {
                    fcremark.Text = dataGridView1.Rows[counter].Cells["fcremark"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fgpm"].Value != null)
                {
                    fgpm.Text = dataGridView1.Rows[counter].Cells["fgpm"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fmiscamt"].Value != null)
                {
                    fmiscamt.Text = dataGridView1.Rows[counter].Cells["fmiscamt"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fcom"].Value != null)
                {
                    fcom.Text = dataGridView1.Rows[counter].Cells["fcom"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fnpm"].Value != null)
                {
                    fnpm.Text = dataGridView1.Rows[counter].Cells["fnpm"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fcomamt"].Value != null)
                {
                    fcomamt.Text = dataGridView1.Rows[counter].Cells["fcomamt"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fpayable"].Value != null)
                {
                    famount.Text = dataGridView1.Rows[counter].Cells["fpayable"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["finpuser"].Value != null)
                {
                    finpuser.Text = dataGridView1.Rows[counter].Cells["finpuser"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["finpdate"].Value != null)
                {
                    finpdate.Text = dataGridView1.Rows[counter].Cells["finpdate"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fupduser"].Value != null)
                {
                    fupduser.Text = dataGridView1.Rows[counter].Cells["fupduser"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fupddate"].Value != null)
                {
                    fupddate.Text = dataGridView1.Rows[counter].Cells["fupddate"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fcnfuser"].Value != null)
                {
                    fcnfuser.Text = dataGridView1.Rows[counter].Cells["fcnfuser"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fcnfdate"].Value != null)
                {
                    fcnfdate.Text = dataGridView1.Rows[counter].Cells["fcnfdate"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["Posted"].Value != null)
                {
                    fposted.SelectedItem = dataGridView1.Rows[counter].Cells["Posted"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["A/C Type"].Value != null)
                {
                    fdbtrtype.Text = dataGridView1.Rows[counter].Cells["A/C Type"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fintadj"].Value != null)
                {
                    fintadj.SelectedItem = dataGridView1.Rows[counter].Cells["fintadj"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fcedepol"].Value != null)
                {
                    fcedepol.Text = dataGridView1.Rows[counter].Cells["fcedepol"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fcdesc"].Value != null)
                {
                    fcdesc.Text = dataGridView1.Rows[counter].Cells["fcdesc"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fefffr"].Value != null)
                {
                    fefffr.Text = dataGridView1.Rows[counter].Cells["fefffr"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["ftotinstal"].Value != null)
                {
                    ftotinstal.Text = dataGridView1.Rows[counter].Cells["ftotinstal"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fcedeendt"].Value != null)
                {
                    fcedeendt.Text = dataGridView1.Rows[counter].Cells["fcedeendt"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fstatus"].Value != null)
                {
                    fstatus.Text = dataGridView1.Rows[counter].Cells["fstatus"].Value.ToString().Trim();
                }
                comms.SelectedItem = "Gross";
                rowlabel.Text = counter.ToString();
                fctlid = dataGridView1.Rows[counter].Cells["fctlid"].Value.ToString().Trim();
            }
            PostbtnControl();
        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            e.Cancel = true;
        }

        private void tabControl1_Selecting2(object sender, TabControlCancelEventArgs e)
        {
            e.Cancel = false;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", fdbtrdesc.Text.ToString().Trim(), "");
        }


        public enum Mode
        {
            [Description("Dr/Cr No.")]
            finvno,

            [Description("Policy#")]
            fpolno,

            [Description("A/C#")]
            fdbtr,

            [Description("Client#")]
            fclnt
        }

        private void InitCombobox()
        {
            Array arr = System.Enum.GetValues(typeof(Mode));    // 获取枚举的所有值
            DataTable dt = new DataTable();
            dt.Columns.Add("String", Type.GetType("System.String"));
            dt.Columns.Add("Value", typeof(int));
            foreach (var a in arr)
            {
                string strText = EnumTextByDescription.GetEnumDesc((Mode)a);
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = (int)a;

                dt.Rows.Add(aRow);
            }

            comboBox1.DataSource = dt;
            comboBox1.DisplayMember = "String";
            comboBox1.ValueMember = "Value";
        }

        public enum Mode1
        {
            [Description("Broker")]
            B,

            [Description("Client")]
            C,

            [Description("Producer")]
            P,

            [Description("Reinsurer")]
            R
        }

        private void InitCombobox1()
        {
            Array arr = System.Enum.GetValues(typeof(Mode1));    // 获取枚举的所有值
            DataTable dt = new DataTable();
            dt.Columns.Add("String", Type.GetType("System.String"));
            dt.Columns.Add("Value", typeof(int));
            foreach (var a in arr)
            {
                string strText = EnumTextByDescription.GetEnumDesc((Mode1)a);
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = (int)a;

                dt.Rows.Add(aRow);
            }

            fdbtrtype.DataSource = dt;
            fdbtrtype.DisplayMember = "String";
            fdbtrtype.ValueMember = "Value";
        }

        void ButtonControl()
        {
            fremark.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            fremark.ReadOnly = true;
            fcremark.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            fcremark.ReadOnly = true;
            fintadj.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            fintadj.Enabled = false;
            Postirng.Visible = true;
            btPost.Visible = true;
            lprint.Visible = true;
            btprint.Visible = true;
            dcancel.Visible = false;
            Gener.Visible = false;
            dsave.Visible = false;
            dexit.Visible = true;
            dupdate.Visible = true;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting2);
        }

        private void button9_Click(object sender, EventArgs e)
        {
            pfinvno = finvno.Text;
            pbill = fbilldate.Text;
            this.Close();
        }

        private void dexit_Click(object sender, EventArgs e)
        {
            pfinvno = finvno.Text;
            pbill = fbilldate.Text;
            this.Close();
        }

        private void lupdate_Click(object sender, EventArgs e)
        {
            oldrow = rowlabel.Text;
            tabControl1.SelectedTab = Clauses;
            fremark.BackColor = System.Drawing.SystemColors.Window;
            fremark.ReadOnly = false;
            fcremark.BackColor = System.Drawing.SystemColors.Window;
            fcremark.ReadOnly = false;
            dcancel.Visible = true;
            Gener.Visible = true;
            dsave.Visible = true;
            Postirng.Visible = false;
            btPost.Visible = false;
            lprint.Visible = false;
            btprint.Visible = false;
            dexit.Visible = false;
            dupdate.Visible = false;
            fintadj.Enabled = true;
            fintadj.BackColor = System.Drawing.SystemColors.Window;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);
        }

        private void dupdate_Click(object sender, EventArgs e)
        {
            oldrow = rowlabel.Text;
            tabControl1.SelectedTab = Clauses;
            fremark.BackColor = System.Drawing.SystemColors.Window;
            fremark.ReadOnly = false;
            fcremark.BackColor = System.Drawing.SystemColors.Window;
            fcremark.ReadOnly = false;
            Postirng.Visible = false;
            btPost.Visible = false;
            lprint.Visible = false;
            btprint.Visible = false;
            dcancel.Visible = true;
            dsave.Visible = true;
            Gener.Visible = true;
            dexit.Visible = false;
            dupdate.Visible = false;
            fintadj.Enabled = true;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);

        }

        private void dsave_Click(object sender, EventArgs e)
        {
            DateTime time = DateTime.Now;
            int fintadjn;
            if (fintadj.SelectedItem.ToString() == "Yes") { fintadjn = 1; } else { fintadjn = 2; }
            string fcremarks = fcremark.Text.ToString().Trim();
            string fcremark1s = "", fcremark2s = "";
            if (fcremarks.Length > 254)
            {
                // fcremark1s = fcremarks.Substring(0, 254);
                // fcremark2s = fcremarks.Substring(255, fcremarks.Length);
            }
            else
            {
                fcremark1s = fcremark.Text;
            }
            string updatedatestr = "update oriinvh set fremark =N'" + Fct.stFat(fremark.Text) + "', fcremark= N'" + Fct.stFat(fcremarks) + "', fintadj='" + fintadjn + "', fcnfuser ='" + InsEnvironment.LoginUser.GetUserCode() + "', fcnfdate= '" + time.ToString("yyyy-MM-dd HH:mm:ss") + "', fcinpname = '" + InsEnvironment.LoginUser.GetChineseName() + "' where fctlid_e=(select fctlid_1 from orih where fctlid = '" + fctlid_1 + "') and finstall= " + finstall.Text + " and fctlid ='" + fctlid + "'";
            DBHelper.ExecuteCommand(updatedatestr);
            ButtonControl();
            string oldfctlid = fctlid;
            FillData("", "", "");
            DataTable dt3 = dataGridView1.DataSource as DataTable;
            foreach (DataRow row in dt3.Rows)
            {
                int SelectedIndex = dt3.Rows.IndexOf(row);
                if (oldfctlid == row["fctlid"].ToString().Trim())
                {
                    dataGridView1.CurrentCell = dataGridView1.Rows[SelectedIndex].Cells["Dr/Cr No."];
                    GridViewchg();
                    break;
                }
            }
            //reload("", "update");
        }

        private void dcancel_Click(object sender, EventArgs e)
        {
            ButtonControl();
            FillData("", "", "");
            //reload(oldrow, "cancel");
        }

        void reload(string fid, string lab)
        {
            string str = "select a.finvno as [Dr/Cr No.], a.finstall as [Install#],a.fbilldate as [Bill Date],  " +
                        "a.finvdate as [Doc Date],case when a.fdbtrtype='B' then 'Broker' when a.fdbtrtype='C' then 'Client'  " +
                        "when a.fdbtrtype='P' then 'Producer' else 'Reinsurer' end as [A/C Type],a.fdbtr as [A/C#], " +
                        "a.freinsr as [Reinsr#], a.fpolno as [Policy No.],a.fendtno as [Endt No.],a.fissdate as [Issue Date], " +
                        "case when a.fposted=1 then 'Yes' else 'No' end as [Posted], a.fclass,a.fsclass, " +
                        "a.fgpm,a.fbrkge,a.fbrkgeamt,a.fcomamt,a.fcom, a.fnpm,a.fremark,a.fcremark,a.fpayable,a.fmiscamt, " +
                        "e.fdesc as fclassdesc,d.fdesc as facdesc,a.freinsr,f.fdesc as prdrdesc,a.fbrkr,c.fdesc as fclntdesc, " +
                        "b.finsd,b.faddr_t1,CONVERT(varchar(10),a.fefffr,102) as fefffr,a.finpuser,a.finpdate,a.fupduser,a.fupddate,b.fcedepol,b.fcedeendt, " +
                        "a.fcnfuser,a.fcnfdate, case when fintadj=1 then 'Yes' else 'No' end as fintadj, " +
                        "a.ftotinstal, e.fcdesc,a.fpmcur, a.fctlid, a.fstatus  " +
                        "from oriinvh a left join polh b on a.fctlid_e = b.fctlid  " +
                        "left join " + Dbm + "[mprdr] c on c.fid=a.fbrkr  " +
                        "left join " + Dbm + "[mprdr] d on d.fid=a.fdbtr and d.ftype = a.fdbtrtype " +
                        "left join " + Dbm + "[mprdr] f on f.fid=a.freinsr  " +
                        "left join " + Dbm + "minsc e on e.fid=a.fclass where a.fctlid_e= " +
                        "(select distinct fctlid_1 from orih where fctlid='" + fctlid_1 + "') " +
                        "order by a.fposted desc, a.fctlid";
            DataTable dt = new DataTable();
            dt = DBHelper.GetDataSet(str);
            if (dt.Rows.Count > 0)
            {
                dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
                dataGridView1.DataSource = dt;
                for (int i = 11; i < dataGridView1.ColumnCount; i++)
                {
                    this.dataGridView1.Columns[i].Visible = false;
                }
                dataGridView1.CellFormatting +=
           new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.dataGridView1_CellFormatting);
                if (lab == "insert" || lab == "update")
                {
                    if (fid != "") //add update
                    {
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            if (dt.Rows[i]["Dr/Cr No."].ToString().Trim() == fid.Trim())
                            {
                                dataGridView1.CurrentCell = dataGridView1.Rows[i].Cells["Dr/Cr No."];
                                break;
                            }
                        }
                    }
                }

                if (lab == "cancel")
                {
                    if (dataGridView1.CurrentCell != null)
                    {
                        dataGridView1.CurrentCell = dataGridView1.Rows[int.Parse(fid)].Cells["Dr/Cr No."];
                    }
                }
            }
            PostbtnControl();
        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", "", "");
        }


        private void Gener_Click(object sender, EventArgs e)
        {
            if ((Convert.ToDecimal(famount.Text) >= 0) && (Convert.ToDecimal(fcomamt.Text) + Convert.ToDecimal(fbrkgeamt.Text) <= 0))
            { return; }
            string lc_desc = ""; fbrkgeamt.Text = "0";
            lc_desc = lc_desc + finsd.Text.ToString().Trim() + "\r\n" + "\r\n";
            if (Convert.ToDecimal(famount.Text) < 0)
            {
                if (fremark.Text != "") { lc_desc = lc_desc + fremark.Text.Replace("Facultative Reinsurance Outward\r\n", "") + "\r\n" + "\r\n"; }
                lc_desc = lc_desc + fcdesc.Text + " 臨分 ";
            }
            if (Convert.ToDecimal(ftotinstal.Text) <= 1)
            {
                lc_desc = lc_desc + "淨保費" + "\r\n";
            }
            else
            {
                lc_desc = lc_desc + "第" + GetChineseString(Convert.ToDecimal(finstall.Text)) + "期淨保費" + "\r\n" + "\r\n";
            }
            if (Convert.ToDecimal(fcomamt.Text) + Convert.ToDecimal(fbrkgeamt.Text) > 0)
            {
                lc_desc = lc_desc + fremark.Text + "\r\n" + "\r\n";
                lc_desc = lc_desc + fcdesc.Text + " 退回佣金";
            }
            lc_desc = lc_desc + "保單號碼： " + fpolno.Text + fcedepol.Text + "\r\n";
            if (fendtno.Text != "")
            {
                lc_desc = lc_desc + "批單號碼: " + fendtno.Text + fcedeendt.Text + "\r\n";
            }
            else { lc_desc = lc_desc + "\r\n"; }
            if (Convert.ToDecimal(famount.Text) < 0)
            {
                DateTime b = DateTime.ParseExact(fissdate.Text, "yyyy.MM.dd", CultureInfo.InvariantCulture);
                b = b.AddDays(90);
                fefffr.Text = b.ToString("yyyy.MM.dd");
                lc_desc = lc_desc + "最後付款日期：" + fefffr.Text.Substring(0, 4) + "年" + fefffr.Text.Substring(5, 2) + "月" + fefffr.Text.Substring(8, 2) + "日";
            }
            fcremark.Text = lc_desc;
        }

        string GetChineseString(decimal number)
        {
            string cStr = "零一二三四五六七八九 十百千";
            string[] unitStr = new string[] { "", "万", "亿", "万亿", "兆" };
            string result = string.Empty;
            for (int i = 0; i < number.ToString().Length; i++)
            {
                int temp = (int)((long)(number / (long)Math.Pow(10, i)) % 10);
                int unit = (int)i / 4;
                if (i % 4 == 0) result = unitStr[unit] + result;
                result = cStr[temp] + cStr[10 + i % 4].ToString().Trim() + result;
            }
            result = Regex.Replace(result, "(零[十百千])+", "零");
            result = Regex.Replace(result, "零{2,}", "零");
            result = Regex.Replace(result, "零([万亿])", "$1").TrimEnd('零');
            return result;
        }


        private void Postirng_Click(object sender, EventArgs e)
        {
            Post();
        }

        private void btPost_Click(object sender, EventArgs e)
        {
            Post();
        }

        void PostbtnControl()
        {
            string sql = "select * from " + Dbm + "xsysperiod where fidtype='BILLMON'";
            DataTable dt = DBHelper.GetDataSet(sql);
            DateTime now = DateTime.Now;
            if (dt.Rows.Count > 0)
            {
                DateTime fdatefr = Convert.ToDateTime(dt.Rows[0]["fdatefr"]);
                DateTime fdateto = Convert.ToDateTime(dt.Rows[0]["fdateto"]);
                if (dt.Rows[0]["fextdto"].ToString().Trim() == "")
                {
                    fdateto = Convert.ToDateTime(dt.Rows[0]["fdateto"]);
                }
                else { fdateto = Convert.ToDateTime(dt.Rows[0]["fextdto"]); }
                DateTime finDate = Convert.ToDateTime(finvdate.Text);

                if (now.Ticks > fdatefr.Ticks && now.Ticks < fdateto.Ticks && fstatus.Text == "3" && fposted.SelectedItem.ToString() == "No" && fdateto >= finDate)
                {
                    Postirng.Visible = true;
                    btPost.Visible = true;
                }
                else
                {
                    Postirng.Visible = false;
                    btPost.Visible = false;
                }
                if (fposted.SelectedItem.ToString() == "Yes")
                { lupdate.Visible = false; dupdate.Visible = false; }
                else { lupdate.Visible = true; dupdate.Visible = true; }
            }
            dupdate.Visible = true;
        }

        void Post()
        {
            DialogResult myResult = MessageBox.Show("Continue to Post?", "Save Warning", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string[] update1 = postPart1();
                string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();

                    SqlCommand command = connection.CreateCommand();
                    SqlTransaction transaction;

                    // Start a local transaction.
                    transaction = connection.BeginTransaction("SampleTransaction");

                    // Must assign both transaction object and connection 
                    // to Command object for a pending local transaction
                    command.Connection = connection;
                    command.Transaction = transaction;

                    try
                    {
                        if (update1 != null)
                        {
                            for (int i = 0; i < update1.Length - 1; i++)
                            {
                                if (update1[i] != "" && update1[i] != null)
                                {
                                    command.CommandText = update1[i];
                                    command.ExecuteNonQuery();
                                }
                            }
                        }
                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("Haven't Been Posted", "Warning",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);

                        Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                        Console.WriteLine("  Message: {0}", ex.Message);

                        // Attempt to roll back the transaction. 
                        try
                        {
                            transaction.Rollback();
                        }
                        catch (Exception ex2)
                        {
                            // This catch block will handle any errors that may have occurred 
                            // on the server that would cause the rollback to fail, such as 
                            // a closed connection.
                            Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                            Console.WriteLine("  Message: {0}", ex2.Message);
                        }
                    }
                    connection.Close();
                    string oldfctlid = fctlid;
                    FillData("", "", "");
                    DataTable dt3 = dataGridView1.DataSource as DataTable;
                    foreach (DataRow row in dt3.Rows)
                    {
                        int SelectedIndex = dt3.Rows.IndexOf(row);
                        if (oldfctlid == row["fctlid"].ToString().Trim())
                        {
                            dataGridView1.CurrentCell = dataGridView1.Rows[SelectedIndex].Cells["Dr/Cr No."];
                            GridViewchg();
                            break;
                        }
                    }
                }
            }
        }

        public string[] postPart1()
        {
            ArrayList PostSql = new ArrayList();
            Decimal ln_payable = 0;
            DateTime ld_billdate = DateTime.Today, ld_bkdate = DateTime.Today;
            String lc_type = "", lc_doctype = "", lc_seqno = "", lc_frno = "", lc_deptailis = "";
            string lc_fdbtrdesc = "", lc_falias = "", lc_fvdesc = "", lc_statid = "";
            string selectsql = "select a.*, rtrim(b.falias) as lc_deptalias, c.fdesc as lc_fdbtrdesc, rtrim(c.falias) as lc_falias, " +
                                "isnull( rtrim(d.frno),'') as frno,isnull( rtrim(e.fsite),'') as fsite " +
                                "from oriinvh a " +
                                "left join ndept b on a.fsclnt= b.fid  " +
                                "left join " + Dbm + "mprdr c on c.ftype = a.fdbtrtype and c.fid = a.fdbtr  " +
                                "left join oinsint_e d on d.fctlid_1=a.fctlid_1 " +
                                "left join polh e on e.fctlid=a.fctlid_1 " +
                                "where a.fctlid='" + fctlid + "'";
            DataTable dt_oinvh = DBHelper.GetDataSet(selectsql);
            if (dt_oinvh.Rows.Count > 0)
            {
                lc_doctype = dt_oinvh.Rows[0]["fdoctype"].ToString();
                string sql = "select * from " + Dbm + "xsysperiod where fidtype='BILLMON'";
                DataTable dt = DBHelper.GetDataSet(sql);
                if (dt.Rows.Count > 0)
                {
                    if (ld_billdate > Convert.ToDateTime(dt.Rows[0]["fdateto"]))
                    { ld_billdate = Convert.ToDateTime(dt.Rows[0]["fnxtdate"]); }
                }
                if (dt_oinvh.Rows[0]["fintadj"].ToString() == "1") { lc_type = "INVADJ"; }
                else
                {
                    if (lc_doctype == "1") { lc_type = "DRNO"; } else { lc_type = "CRNO"; }
                }
                if (Convert.ToDateTime(dt_oinvh.Rows[0]["fefffr"]) > ld_billdate)
                {
                    ld_bkdate = Convert.ToDateTime(dt_oinvh.Rows[0]["fefffr"]);
                }
                else { ld_bkdate = ld_billdate; }
                lc_seqno = NEWID(lc_type, ld_billdate.Year.ToString());
                lc_fdbtrdesc = dt_oinvh.Rows[0]["lc_fdbtrdesc"].ToString();
                lc_falias = dt_oinvh.Rows[0]["lc_falias"].ToString();
                lc_statid = dt_oinvh.Rows[0]["fmodule"].ToString();
                if (lc_statid.Trim() == "D0104" || lc_statid.Trim() == "D0106" || lc_statid.Trim() == "R0104" || lc_statid.Trim() == "R0106")
                { lc_statid = "D0104"; }
                if (dt_oinvh.Rows[0]["fdbtrtype"].ToString() == "C")
                {
                    lc_deptailis = "-" + dt_oinvh.Rows[0]["lc_deptalias"].ToString();
                }
                lc_falias = lc_falias + lc_deptailis;
                if (dt_oinvh.Rows[0]["fclass"].ToString() == "EEC" || dt_oinvh.Rows[0]["fclass"].ToString() == "CAR")
                {
                    lc_fvdesc = dt_oinvh.Rows[0]["fsite"].ToString();
                }
                if (dt_oinvh.Rows[0]["fclass"].ToString() == "CMP" || dt_oinvh.Rows[0]["fclass"].ToString() == "PMP")
                {
                    lc_fvdesc = dt_oinvh.Rows[0]["frno"].ToString();
                    lc_frno = " " + dt_oinvh.Rows[0]["frno"].ToString();
                    lc_falias = lc_falias + lc_frno;
                }
            }
            string updsql = "update oriinvh set finvno='" + lc_seqno.Trim() + "', fbilldate='" + ld_billdate.ToString("yyyy-MM-dd HH:mm:ss") + "',fbkdate='" + ld_bkdate.ToString("yyyy-MM-dd HH:mm:ss") + "',fcnfuser='" + InsEnvironment.LoginUser.GetUserCode() + "',fdoctype='" + lc_doctype + "', " +
            "fcnfdate='" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "',fpartr='Installment: '+'" + dt_oinvh.Rows[0]["finstall"].ToString() + "'+'/'+'" + dt_oinvh.Rows[0]["ftotinstal"].ToString() + "'+char(13)+'" + Fct.stFat(dt_oinvh.Rows[0]["fremark"].ToString().Trim()) + "', " +
            "fposted=1 where fctlid='" + fctlid + "' and fposted=2";
            PostSql.Add(updsql);

            string updsql1 = "update " + Dbm + "xsysparm  set fnxtid=RIGHT('00000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 5) where fidtype='" + lc_type + "' and fuy ='" + ld_billdate.Year.ToString() + "'";
            PostSql.Add(updsql1);

            string updsql3 = "INSERT INTO [dbo].[ostat]([fctlid],[fbus],[fmodule],[fstatid],[fclass],[fctlid_i],[fctlid_oi],[frefdate],[frefno],[fpolno],[fendtno], " +
                            "[fclmno],[fsrcref],[fcedepol],[fcedeendt],[fsrcclm],[fcedeclm],[fpaytype],[fpaynat],[fefffr],[feffto],[flosdate],[finstall],[ftotinstal], " +
                            "[fpnetonly],[fdbtrtype],[fdbtr],[fdbtrdesc],[fclnt],[fsclnt],[fpmcur],[frate],[fgpm],[ftdamt],[fcomamt],[fothamt],[famount],[fcrterm], " +
                            "[finsd],[fremark],[fsetamt],[ftsetdr],[ftsetcr],[fbalance],[famount_b],[fsetamt_b],[ftsetdr_b],[ftsetcr_b],[frnddif_b],[fbalance_b], " +
                            "[ftsetdr_g],[ftsetcr_g],[ftsetdr_c],[ftsetcr_c],[ftsetdr_s],[ftsetcr_s],[fset_g],[fset_c],[fset_s],[fbal_g],[fbal_c],[fbal_s],[fstatus], " +
                            "[ffeerel],[fsetdate],[fdoctype],[item_id],[interco_id],[cont_id],[dept_id],[person_id],[fgldebtor]) " +
                            "select distinct '" + newid("OSTAT") + "',a.fbus,fmodule,'" + lc_statid + "',a.fclass,a.fctlid,fctlid_i as fctlid_oi, " +
                            "fbilldate,finvno,a.fpolno,a.fendtno,'', '','','','','',null,null, " +
                            "a.fefffr,a.feffto,null,a.finstall,a.ftotinstal,a.fpnetonly,fdbtrtype,fdbtr,e.fdesc,'',a.fsclnt,a.fpmcur,'1.0000', " +
                            "a.fgpm,0.0000,-a.fcomamt,0.0000,fpayable,fcrterm, b.finsd,a.fremark,'0.0000','0.0000','0.0000', " +
                            "fpayable,fpayable,0.0000,'0.0000','0.0000','0.0000',fpayable,'0.0000','0.0000','0.0000','0.0000','0.0000','0.0000', " +
                            "'0.0000','0.0000','0.0000',a.fgpm,-a.fcomamt,'0.0000','1','1',null,fdoctype,item_id,interco_id,cont_id,dept_id,person_id,null " +
                            "from oriinvh a " +
                            "left join polh b on a.fctlid_e = b.fctlid " +
                            "left join " + Dbm + "mprdr e on e.fid=a.fdbtr " +
                            "where a.fctlid='" + fctlid + "' ";
            PostSql.Add(updsql3);

            string[] postPart2 = u_glvdet(lc_seqno, ld_billdate.ToString("yyyy-MM-dd HH:mm:ss"), lc_falias);
            if (postPart2 != null)
            {
                for (int i = 0; i < postPart2.Length; i++)
                {
                    if (postPart2[i] != "" && postPart2[i] != null)
                    {
                        PostSql.Add(postPart2[i]);
                    }
                }
            }

            string updsql4 = "update " + Dbm + "xsysparm  set fnxtid=RIGHT('00000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype='OSTAT'";
            PostSql.Add(updsql4);

            string updsql7 = "update oriinvf set finvno='" + lc_seqno + "',fbilldate='" + ld_billdate.ToString("yyyy-MM-dd HH:mm:ss") + "',fbkdate='" + ld_bkdate.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid_iv ='" + fctlid + "'";
            PostSql.Add(updsql7);

            string updsql9 = "update oriinvlf set finvno='" + lc_seqno + "',fbilldate='" + ld_billdate.ToString("yyyy-MM-dd HH:mm:ss") + "',fbkdate='" + ld_bkdate.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid_iv ='" + fctlid + "' ";
            PostSql.Add(updsql9);

            PostSql.Add(lc_seqno.Trim());

            return (string[])PostSql.ToArray(typeof(string));
        }

        string lc_gl_debtor, lc_gl_eclevy1, lc_gl_eclevy2, lc_gl_eclevy3,
                   lc_gl_prem, lc_gl_comres, lc_gl_debcom, lc_gl_discnt, lc_gl_mib,
                   lc_gl_ldrres, lc_gl_ldrpaya, lc_gl_ricost, lc_gl_asp, lc_gl_debasp;

        string lc_ex_debtor, lc_ex_eclevy1, lc_ex_eclevy2, lc_ex_eclevy3,
                lc_ex_prem, lc_ex_comres, lc_ex_debcom, lc_ex_discnt, lc_ex_mib,
                lc_ex_ldrres, lc_ex_ldrpaya, lc_ex_ricost, lc_ex_asp, lc_ex_debasp;

        string lc_ngl_debtor, lc_ngl_eclevy1, lc_ngl_eclevy2, lc_ngl_eclevy3,
                lc_ngl_prem, lc_ngl_comres, lc_ngl_debcom, lc_ngl_discnt, lc_ngl_mib,
                lc_ngl_ldrres, lc_ngl_ldrpaya, lc_ngl_ricost, lc_ngl_asp, lc_ngl_debasp;

        string lc_fremark, lc_finvno, lc_fpolno, lc_fendtno, lc_mon;

        string lc_lvy1, lc_lvy2, lc_lvy3, lc_mib;

        string vfctlid_1, lc_fctlid, lc_f06, lc_fpayee;
        private Ctrl.Reins.FacInstallPar facInstallPar;


        public string[] u_glvdet(string m_finvno, string ld_billdate, string lc_falias)
        {
            DateTime fbilldate = DateTime.Parse(ld_billdate);
            ArrayList PostSql = new ArrayList();
            u_glac();
            string sql = "select * from orih where fctlid='" + fctlid_1 + "'";
            DataTable polh = DBHelper.GetDataSet(sql);
            foreach (DataRow dr in polh.Rows)
            {
                int ln_pos = m_finvno.LastIndexOf("-");
                lc_finvno = m_finvno.Substring(0, ln_pos + 1) + int.Parse(m_finvno.Substring(ln_pos + 1)).ToString();

                lc_fpolno = dr["fpolno"].ToString().Trim();
                ln_pos = lc_fpolno.LastIndexOf("-");
                lc_fpolno = lc_fpolno.Substring(0, ln_pos + 1) + int.Parse(lc_fpolno.Substring(ln_pos + 1)).ToString();

                lc_fendtno = dr["fendtno"].ToString().Trim();
                ln_pos = dr["fendtno"].ToString().LastIndexOf("-");
                lc_mon = fbilldate.Month.ToString();
                if (ln_pos != -1)
                {
                    lc_fendtno = lc_fendtno.Substring(0, ln_pos + 1) + int.Parse(lc_fendtno.Substring(ln_pos + 1)).ToString();
                }
                if (lc_fendtno != "") { lc_fremark = lc_fpolno.Trim() + "/" + lc_fendtno.Trim(); }
                else { lc_fremark = lc_fpolno.Trim(); }

            }
            string selectsql = "select * " +
                              "from oriinvh a " +
                              "where a.fctlid='" + fctlid + "'";
            DataTable dt_oinvh = DBHelper.GetDataSet(selectsql);
            decimal m_fgpm = 0, m_fcomamt = 0; string m_fclass = "";
            if (dt_oinvh.Rows.Count > 0)
            {
                lc_fpayee = dt_oinvh.Rows[0]["fdbdesc"].ToString().Replace("\n", "").Replace("\r", "").Replace("\r\n", "");
                int ln_chr13 = dt_oinvh.Rows[0]["fdbdesc"].ToString().LastIndexOf("\n");
                if (ln_chr13 <= 0) { lc_fpayee = lc_fpayee; }
                else { lc_fpayee = lc_fpayee.Substring(0, ln_chr13 - 1); }

                m_fcomamt = Convert.ToDecimal(dt_oinvh.Rows[0]["fcomamt"].ToString());
                m_fgpm = Convert.ToDecimal(dt_oinvh.Rows[0]["fgpm"].ToString());
                m_fclass = dt_oinvh.Rows[0]["fclass"].ToString();
            }
            lc_fctlid = newid("OFINHD");
            string tfinhd = "insert into ofinhd " +
                            "select '" + m_finvno.Substring(0, 1) + "' as fdoctype, fmodule,'" + lc_fctlid + "' as fctlid,fctlid as fctlid_i,'" + m_finvno + "' as frefno, '" + ld_billdate + "' as fbilldate, " +
                            "'" + Fct.stFat(lc_fpayee) + "' as fpayee,round(cast(fgpm - fcomamt as numeric(18,2)),2) as famount, " +
                            "fcremark as fcontent,'" + lc_ngl_debtor + "' as facctode,null as oacctcode,'90' as fdays, " +
                            "null as f01,NULL as f02,'INB-'+interco_id as f03,cont_id as f04,null as f05,NULL as f06, " +
                            "null as f07, null as f08, null as f09,null as f10,'000' as f11, " +
                            "case when interco_id='Z' OR cont_id ='Z' then 'N' else 'Y' end as fready, 'N' as fimport, " +
                            "'" + lc_gl_debtor + "' as xacctcode,item_id as xitemd_id, interco_id as xinter_id,cont_id as xcont_id, " +
                            "dept_id as xdept_id, person_id as xperson_id, fpolno as xpolno, fendtno as xendtno, " +
                            "null as xclmno,null as xpaytype, null as xpaynat, null as fimport_date " +
                            "from oriinvh where fctlid='" + fctlid + "'";
            PostSql.Add(tfinhd);

            string updsql4 = "update " + Dbm + "xsysparm  set fnxtid=RIGHT('00000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype='OFINHD'";
            PostSql.Add(updsql4);

            int ln_fseqno = 0;
            string ln_fctlid = "";
            ln_fctlid = newid("OFINDET");

            string findet = "insert into ofindet " +
                    "select fmodule,'" + ln_fctlid + "' as fctlid,'" + lc_fctlid + "' as fctlid_hd,'" + ld_billdate + "' as fbilldate,'90' as fdays,'" + m_finvno + "/" + ln_fseqno.ToString("D2") + "' as frefno, " +
                    "round(cast(fgpm as numeric(18,2)),2) as famount, " +
                    "'" + lc_finvno + " " + lc_mon + "月臨分保 (" + lc_fremark + ")' as fremark,'" + lc_ngl_debtor + "' as facctode,null as oacctcode,'" + m_finvno + "' as fsalaryno, " +
                    "null as f01,null as f02,'INB-'+interco_id as f03,cont_id as f04,null as f05,null as f06, " +
                    "null as f07, null as f08, null as f09,null as f10,'000' as f11, " +
                    "'" + lc_gl_debtor + "' as xacctcode,item_id as xitemd_id, interco_id as xinter_id,cont_id as xcont_id, " +
                    "dept_id as xdept_id, person_id as xperson_id,NULL " +
                    "from oriinvh where fctlid ='" + fctlid + "'";
            PostSql.Add(findet);


            if (m_fgpm != 0)
            {
                ln_fseqno = ln_fseqno + 1;
                ln_fctlid = (int.Parse(ln_fctlid) + 1).ToString().PadLeft(10, '0');
                string findet1 = "insert into ofindet " +
                    "select fmodule,'" + ln_fctlid + "' as fctlid,'" + lc_fctlid + "' as fctlid_hd,'" + ld_billdate + "' as fbilldate,'90' as fdays,'" + m_finvno + "/" + ln_fseqno.ToString("D2") + "' as frefno, " +
                    "-round('" + m_fgpm + "',2) as famount, " +
                    "'" + lc_finvno + " " + lc_mon + "月臨分再保保費分攤 (" + lc_fremark + ")' as fremark,'" + lc_ngl_prem + "' as facctode,null as oacctcode,'" + m_finvno + "' as fsalaryno, " +
                    "null as f01,null as f02,null as f03,null as f04,null as f05, null as f06, " +
                    "null as f07, null as f08, null as f09,null as f10,'000' as f11, " +
                    "'" + lc_gl_prem + "' as xacctcode,item_id as xitemd_id, '000' as xinter_id,'00' as xcont_id, " +
                    "dept_id as xdept_id, person_id as xperson_id,NULL " +
                    "from oriinvh where fctlid ='" + fctlid + "'";
                PostSql.Add(findet1);
            }

            if (m_fcomamt != 0)
            {
                ln_fseqno = ln_fseqno + 1;
                ln_fctlid = (int.Parse(ln_fctlid) + 1).ToString().PadLeft(10, '0');
                string findet1 = "insert into ofindet " +
                    "select fmodule,'" + ln_fctlid + "' as fctlid,'" + lc_fctlid + "' as fctlid_hd,'" + ld_billdate + "' as fbilldate,'90' as fdays,'" + m_finvno + "/" + ln_fseqno.ToString("D2") + "' as frefno, " +
                    "round('" + m_fcomamt + "',2) as famount, " +
                    "'" + lc_finvno + " " + lc_mon + "月臨分再保保費佣金分攤 (" + lc_fremark + ")' as fremark,'" + lc_ngl_comres + "' as facctode,null as oacctcode,'" + m_finvno + "' as fsalaryno, " +
                    "null as f01,null as f02,null as f03,null as f04,null as f05,null as f06, " +
                    "null as f07, null as f08, null as f09,'IL02' as f10,'000' as f11, " +
                    "'" + lc_gl_comres + "' as xacctcode,item_id as xitemd_id, 'IL02' as xinter_id,'00' as xcont_id, " +
                    "dept_id as xdept_id, person_id as xperson_id,NULL " +
                    "from oriinvh where fctlid ='" + fctlid + "'";
                PostSql.Add(findet1);

                ln_fseqno = ln_fseqno + 1;
                ln_fctlid = (int.Parse(ln_fctlid) + 1).ToString().PadLeft(10, '0');
                string findet2 = "insert into ofindet " +
                    "select fmodule,'" + ln_fctlid + "' as fctlid,'" + lc_fctlid + "' as fctlid_hd,'" + ld_billdate + "' as fbilldate,'90' as fdays,'" + m_finvno + "/" + ln_fseqno.ToString("D2") + "' as frefno, " +
                    "-round('" + m_fcomamt + "',2) as famount, " +
                    "'" + lc_finvno + " " + lc_mon + "月臨分佣 (" + lc_fremark + ")' as fremark,'" + lc_ngl_debcom + "' as facctode,null as oacctcode,'" + m_finvno + "' as fsalaryno, " +
                    "null as f01,'000' as f02,'INB-'+interco_id as f03,cont_id as f04,null as f05,null as f06, " +
                    "null as f07, null as f08, null as f09,null as f10,'000' as f11, " +
                    "'" + lc_gl_debcom + "' as xacctcode,item_id as xitemd_id, interco_id as xinter_id,cont_id as xcont_id, " +
                    "dept_id as xdept_id, person_id as xperson_id,NULL " +
                    "from oriinvh where fctlid ='" + fctlid + "'";
                PostSql.Add(findet2);
            }

            string updsql5 = "update " + Dbm + "xsysparm  set fnxtid=RIGHT('00000'+ LTRIM(STR(CAST('" + ln_fctlid + "' as int)+1)), 10) where fidtype='ofindet'";
            PostSql.Add(updsql5);

            return (string[])PostSql.ToArray(typeof(string));
        }

        void u_glac()
        {
            DataTable tglac = operate.GetTable("select * from mchgcode");
            foreach (DataRow dr in tglac.Rows)
            {
                if (dr["fid"].ToString().Trim() == "*DEBTOR-02")
                {
                    if (dr["fglcode"].ToString() == "")
                    {
                        lc_gl_debtor = "ZZZZZZZZ";
                    }
                    else
                    {
                        lc_gl_debtor = dr["fglcode"].ToString().Trim();
                    }
                    lc_ex_debtor = dr["fexdesc"].ToString().Trim();
                    if (dr["fnewgl"].ToString() == "")
                    {
                        lc_ngl_debtor = "ZZZZZZZZ";
                    }
                    else
                    {
                        lc_ngl_debtor = dr["fnewgl"].ToString().Trim();
                    }
                }
                if (dr["fid"].ToString().Trim() == "*PREM-03")
                {
                    if (dr["fglcode"].ToString() == "")
                    {
                        lc_gl_prem = "ZZZZZZZZ";
                    }
                    else
                    {
                        lc_gl_prem = dr["fglcode"].ToString().Trim();
                    }
                    lc_ex_prem = dr["fexdesc"].ToString().Trim();
                    if (dr["fnewgl"].ToString() == "")
                    {
                        lc_ngl_prem = "ZZZZZZZZ";
                    }
                    else
                    {
                        lc_ngl_prem = dr["fnewgl"].ToString().Trim();
                    }
                }
                if (dr["fid"].ToString().Trim() == "*COMRES-03")
                {
                    if (dr["fglcode"].ToString() == "")
                    {
                        lc_gl_comres = "ZZZZZZZZ";
                    }
                    else
                    {
                        lc_gl_comres = dr["fglcode"].ToString().Trim();
                    }
                    lc_ex_comres = dr["fexdesc"].ToString().Trim();
                    if (dr["fnewgl"].ToString() == "")
                    {
                        lc_ngl_comres = "ZZZZZZZZ";
                    }
                    else
                    {
                        lc_ngl_comres = dr["fnewgl"].ToString().Trim();
                    }
                }
                if (dr["fid"].ToString().Trim() == "*DEBTOR-11")
                {
                    if (dr["fglcode"].ToString() == "")
                    {
                        lc_gl_debcom = "ZZZZZZZZ";
                    }
                    else
                    {
                        lc_gl_debcom = dr["fglcode"].ToString().Trim();
                    }
                    lc_ex_debcom = dr["fexdesc"].ToString().Trim();
                    if (dr["fnewgl"].ToString() == "")
                    {
                        lc_ngl_debcom = "ZZZZZZZZ";
                    }
                    else
                    {
                        lc_ngl_debcom = dr["fnewgl"].ToString().Trim();
                    }
                }
            }
        }

        public string NEWID(string type, string year)
        {
            string lc_seqno = "";
            string sql = "select rtrim(fprefix)+fnxtid as lc_seqno from " + Dbm + "xsysparm where fidtype='" + type + "' and fuy='" + year + "' ";
            DataTable dt = DBHelper.GetDataSet(sql);
            if (dt.Rows.Count > 0)
            {
                lc_seqno = dt.Rows[0]["lc_seqno"].ToString();
            }
            return lc_seqno.Trim();
        }

        public string newid(string type)
        {
            string lc_seqno = "";
            string sql = "select fnxtid as lc_seqno from " + Dbm + "xsysparm where fidtype='" + type + "' ";
            DataTable dt = DBHelper.GetDataSet(sql);
            if (dt.Rows.Count > 0)
            {
                lc_seqno = dt.Rows[0]["lc_seqno"].ToString();
            }
            return lc_seqno;
        }

        private void lprint_Click(object sender, EventArgs e)
        {
            pfacinv tempform = new pfacinv(this);
            tempform.fctlid_1 = fctlid;
            tempform.finvno = finvno.Text;
            tempform.dt = dataGridView1.DataSource as DataTable;
            tempform.p_calling = "RIOF";
            tempform.ShowDialog();
        }

        private void btprint_Click(object sender, EventArgs e)
        {
            pfacinv tempform = new pfacinv(this);
            tempform.p_calling = "RIOF";
            tempform.flag = "push";
            tempform.fctlid_1 = fctlid;
            tempform.dt = dataGridView1.DataSource as DataTable;
            tempform.ShowDialog();
        }

        private void req_Click(object sender, EventArgs e)
        {
            reqinv tempform = new reqinv(this);
            tempform.fbus = "D";
            tempform.flag = "DrCrNoteFacOut";
            tempform.ShowDialog();
        }

    }
}

