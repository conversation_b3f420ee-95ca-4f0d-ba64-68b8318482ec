namespace INS.Business.Report
{
    partial class pexpnote
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.panel1 = new System.Windows.Forms.Panel();
            this.domainUpDown1 = new System.Windows.Forms.DomainUpDown();
            this.checkBox3 = new System.Windows.Forms.CheckBox();
            this.checkBox2 = new System.Windows.Forms.CheckBox();
            this.checkBox1 = new System.Windows.Forms.CheckBox();
            this.label1 = new System.Windows.Forms.Label();
            this.btnExit = new System.Windows.Forms.Button();
            this.PS = new System.Windows.Forms.Button();
            this.waitlblto = new System.Windows.Forms.Label();
            this.waitlblfr = new System.Windows.Forms.Label();
            this.waitlbl = new System.Windows.Forms.Label();
            this.progBar = new System.Windows.Forms.ProgressBar();
            this.panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.BackColor = System.Drawing.SystemColors.Control;
            this.panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.panel1.Controls.Add(this.domainUpDown1);
            this.panel1.Controls.Add(this.checkBox3);
            this.panel1.Controls.Add(this.checkBox2);
            this.panel1.Controls.Add(this.checkBox1);
            this.panel1.Controls.Add(this.label1);
            this.panel1.Location = new System.Drawing.Point(109, 63);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(235, 147);
            this.panel1.TabIndex = 306;
            // 
            // domainUpDown1
            // 
            this.domainUpDown1.Items.Add("1");
            this.domainUpDown1.Items.Add("2");
            this.domainUpDown1.Items.Add("3");
            this.domainUpDown1.Items.Add("4");
            this.domainUpDown1.Items.Add("5");
            this.domainUpDown1.Items.Add("6");
            this.domainUpDown1.Items.Add("7");
            this.domainUpDown1.Items.Add("8");
            this.domainUpDown1.Items.Add("9");
            this.domainUpDown1.Items.Add("10");
            this.domainUpDown1.Items.Add("11");
            this.domainUpDown1.Items.Add("12");
            this.domainUpDown1.Items.Add("13");
            this.domainUpDown1.Items.Add("14");
            this.domainUpDown1.Items.Add("15");
            this.domainUpDown1.Items.Add("16");
            this.domainUpDown1.Items.Add("17");
            this.domainUpDown1.Items.Add("18");
            this.domainUpDown1.Items.Add("19");
            this.domainUpDown1.Items.Add("20");
            this.domainUpDown1.Location = new System.Drawing.Point(123, 95);
            this.domainUpDown1.Name = "domainUpDown1";
            this.domainUpDown1.Size = new System.Drawing.Size(44, 20);
            this.domainUpDown1.TabIndex = 6;
            this.domainUpDown1.Text = "0";
            this.domainUpDown1.TextChanged += new System.EventHandler(this.domainUpDown1_TextChanged);
            // 
            // checkBox3
            // 
            this.checkBox3.AutoSize = true;
            this.checkBox3.Location = new System.Drawing.Point(71, 96);
            this.checkBox3.Name = "checkBox3";
            this.checkBox3.Size = new System.Drawing.Size(48, 17);
            this.checkBox3.TabIndex = 3;
            this.checkBox3.Text = "Next";
            this.checkBox3.UseVisualStyleBackColor = true;
            // 
            // checkBox2
            // 
            this.checkBox2.AutoSize = true;
            this.checkBox2.Location = new System.Drawing.Point(71, 65);
            this.checkBox2.Name = "checkBox2";
            this.checkBox2.Size = new System.Drawing.Size(80, 17);
            this.checkBox2.TabIndex = 2;
            this.checkBox2.Text = "All Records";
            this.checkBox2.UseVisualStyleBackColor = true;
            // 
            // checkBox1
            // 
            this.checkBox1.AutoSize = true;
            this.checkBox1.Checked = true;
            this.checkBox1.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox1.Location = new System.Drawing.Point(71, 34);
            this.checkBox1.Name = "checkBox1";
            this.checkBox1.Size = new System.Drawing.Size(98, 17);
            this.checkBox1.TabIndex = 1;
            this.checkBox1.Text = "Current Record";
            this.checkBox1.UseVisualStyleBackColor = true;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(16, 13);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(38, 13);
            this.label1.TabIndex = 0;
            this.label1.Text = "Scope";
            // 
            // btnExit
            // 
            this.btnExit.Location = new System.Drawing.Point(276, 239);
            this.btnExit.Name = "btnExit";
            this.btnExit.Size = new System.Drawing.Size(197, 25);
            this.btnExit.TabIndex = 310;
            this.btnExit.Text = "Exit(&E)";
            this.btnExit.UseVisualStyleBackColor = true;
            this.btnExit.Click += new System.EventHandler(this.btnExit_Click);
            // 
            // PS
            // 
            this.PS.Location = new System.Drawing.Point(30, 239);
            this.PS.Name = "PS";
            this.PS.Size = new System.Drawing.Size(197, 25);
            this.PS.TabIndex = 309;
            this.PS.Text = "Confirm(&C)";
            this.PS.UseVisualStyleBackColor = true;
            this.PS.Click += new System.EventHandler(this.PS_Click);
            // 
            // waitlblto
            // 
            this.waitlblto.AutoSize = true;
            this.waitlblto.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.waitlblto.Location = new System.Drawing.Point(448, 285);
            this.waitlblto.Name = "waitlblto";
            this.waitlblto.Size = new System.Drawing.Size(39, 15);
            this.waitlblto.TabIndex = 329;
            this.waitlblto.Text = "100%";
            this.waitlblto.Visible = false;
            // 
            // waitlblfr
            // 
            this.waitlblfr.AutoSize = true;
            this.waitlblfr.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.waitlblfr.Location = new System.Drawing.Point(178, 285);
            this.waitlblfr.Name = "waitlblfr";
            this.waitlblfr.Size = new System.Drawing.Size(25, 15);
            this.waitlblfr.TabIndex = 328;
            this.waitlblfr.Text = "0%";
            this.waitlblfr.Visible = false;
            // 
            // waitlbl
            // 
            this.waitlbl.AutoSize = true;
            this.waitlbl.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.waitlbl.Location = new System.Drawing.Point(209, 305);
            this.waitlbl.Name = "waitlbl";
            this.waitlbl.Size = new System.Drawing.Size(88, 15);
            this.waitlbl.TabIndex = 327;
            this.waitlbl.Text = "Processing .....";
            this.waitlbl.Visible = false;
            // 
            // progBar
            // 
            this.progBar.Location = new System.Drawing.Point(207, 280);
            this.progBar.Name = "progBar";
            this.progBar.Size = new System.Drawing.Size(237, 22);
            this.progBar.Step = 5;
            this.progBar.TabIndex = 326;
            this.progBar.Visible = false;
            // 
            // pexpnote
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(509, 334);
            this.Controls.Add(this.waitlblto);
            this.Controls.Add(this.waitlblfr);
            this.Controls.Add(this.waitlbl);
            this.Controls.Add(this.progBar);
            this.Controls.Add(this.btnExit);
            this.Controls.Add(this.PS);
            this.Controls.Add(this.panel1);
            this.Name = "pexpnote";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Document - Expnote";
            this.Resize += new System.EventHandler(this.prih_Resize);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.CheckBox checkBox3;
        private System.Windows.Forms.CheckBox checkBox2;
        private System.Windows.Forms.CheckBox checkBox1;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnExit;
        private System.Windows.Forms.Button PS;
        private System.Windows.Forms.DomainUpDown domainUpDown1;
        private System.Windows.Forms.Label waitlblto;
        private System.Windows.Forms.Label waitlblfr;
        private System.Windows.Forms.Label waitlbl;
        private System.Windows.Forms.ProgressBar progBar;
    }
}