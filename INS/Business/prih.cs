using CrystalDecisions.CrystalReports.Engine;
using INS.Business.objRpt;
using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business.Report
{
    public partial class prih : Form
    {
        public string Dbm = InsEnvironment.DataBase.GetDbm();
       
        //private string rpDirectory = Application.StartupPath.Replace("\\bin\\Debug", "\\objRpt");
        private string rpDirectory = "M:\\Software II\\New INS Runtime\\Setup\\objRpt";
        public string fctlid_lt, fctlid_1 = "", fctlid = "", fbus = "", p_rpttype = "", lc_amend = "", p_calling = "", fclass = "";
        public int ln_flogseq = 0; public DateTime ld_faccdate;

        public string Dbo = InsEnvironment.DataBase.GetDbo();
        private RIApp rIApp;
        private Ctrl.Reins.FacInstall facInstall;
        private Ctrl.Reins.FacInstallPar facInstallPar;
       
        public prih()
        {
            InitializeComponent();
        }

        public prih(RIApp rIApp)
        {
            // TODO: Complete member initialization
            this.rIApp = rIApp;
            InitializeComponent();
        }

        public prih(Ctrl.Reins.FacInstall facInstall)
        {
            // TODO: Complete member initialization
            this.facInstall = facInstall;
            InitializeComponent();
        }

        public prih(Ctrl.Reins.FacInstallPar facInstallPar)
        {
            // TODO: Complete member initialization
            this.facInstallPar = facInstallPar;
            InitializeComponent();
        }

        public void conctrl()
        {
            if (p_calling == "RIBRKDN") { this.Text = "R/I Installment Break Down"; }
            else if (p_rpttype == "RIAPP")
            { this.Text = "R/I Application"; }
        }

        private void PS_Click(object sender, EventArgs e)
        {
            progressBar1.Visible = true;
            progressBar1.Maximum = 50;//设置最大长度值 
            progressBar1.Value = 0;//设置当前值 
            progressBar1.Step = 5;//设置没次增长多少 
            for (int i = 0; i < 10; i++)//循环 
            {
                System.Threading.Thread.Sleep(1000);//暂停1秒 
                progressBar1.Value += progressBar1.Step;
            }
            optType();
            this.Close();
        }

        void optType()
        {
            if (p_calling == "RIBRKDN" && !fclass.Contains("PAR|CPM"))
            { orgribrkdn(); }
            if (p_calling == "RIBRKDN" && fclass.Contains("PAR|CPM"))
            { orgribrkdn2(); }
            if (p_calling == "RIAPP" )
            { orgriapp(); }
        }

        void orgriapp()
        {
            string fctlids = "";
            if (checkBox1.Checked)
            { fctlids = fctlid; }
            if (checkBox2.Checked)
            {
                string sql = "select fctlid from oriapp where fctlid_ri = (select fctlid_ri from oriapp where fctlid = '" + fctlid + "')"; 
                DataTable dt4 = DBHelper.GetDataSet(sql);
                foreach (DataRow row in dt4.Rows)
                {
                    if (fctlids == "") { fctlids = Fct.stFat(row["fctlid"]); }
                    else { fctlids = fctlids + "','" + Fct.stFat(row["fctlid"]); }
                }
            }
            if (checkBox3.Checked)
            {
                string sql = "select fctlid from oriapp where fctlid >='" + fctlid + "'";
                DataTable dt4 = DBHelper.GetDataSet(sql);
                for (int i = 0; i < Fct.snFat(domainUpDown1.Text); i++)
                {
                    if (fctlids == "") { fctlids = Fct.stFat(dt4.Rows[i]["fctlid"]); }
                    else { fctlids = fctlids + "','" + Fct.stFat(dt4.Rows[i]["fctlid"]); }
                }
            }
//            string sql1 = "select isnull(D.flyrid,'') AS fctlid_e,a.frino as fctlid_p ,[fctlid_ri],a.[fctlid],[frino],[fritype],[flogseq],[fsepdrcr],[fdbtrtype]  " +
//",[fdbtr],[fdbtrdesc],[fcession],[friapp],[foutgo],[frinsrref],[fsia],[fsi2a],[fupdsia] " +
//",[fupdsi2a],[fsharea],[fupdsharea],[fgpma],[fcomamta],[fmiscamta],[fpayablea],[fsib] " +
//",[fsi2b],[fupdsib],[fupdsi2b],a.[fexcess],a.[flimit],[fshareb],[fupdshareb],[fgpmb] " +
//",[fcomamtb],[fmiscamtb],[fpayableb],a.[fissdate],a.[fclass],[fclsdesc],a.[fpolno] " +
//",a.[fendtno],a.[finsd],a.[fefffr],a.[feffto],a.[fmainten],[fprna],[fprnb],[fprnmisc] " +
//",[fprngr],a.[fgpm],a.[fcomamt],[fmiscamt],[fpayable],a.[fmntfr],a.[fmntto],a.[fcur],a.[fpmcur] " +
//",a.[fsi],a.[fupdsi],a.[fliab],a.[fupdliab],a.[fsite],a.[fsite_t1],[falter1],[falter2],[fremark] " +
//",[fposted],a.[finpuser],a.[finpdate],a.[fupduser],a.[fupddate],a.[fcnfuser],a.[fcnfdate],  " +
//"isnull(c.fadd1,'') as fdbtradd1, isnull(c.fadd2,'') as fdbtradd2,isnull(c.fadd3,'') as fdbtradd3,  " +
//"isnull(c.fadd4,'') as fdbtradd4,isnull(b.fcedepol,'') as fcedepol, isnull(b.fcedeendt,'') as fcedeendt from oriapp a " +
//                        "left join polh b on a.fctlid_e = b.fctlid " +
//                        "left join [orilayr] D on a.fctlid_ri = D.fctlid_2 " +
//                        "left join " + Dbm + "[mprdr] c on a.fdbtrtype = c.ftype and a.fdbtr = c.fid " +
//                        "where a.fctlid in ('" + fctlids + "')";

            string sql1 = "select distinct a.*,  " +
            "isnull(c.fadd1,'') as fdbtradd1, isnull(c.fadd2,'') as fdbtradd2,isnull(c.fadd3,'') as fdbtradd3,  " +
            "isnull(c.fadd4,'') as fdbtradd4,isnull(b.fcedepol,'') as fcedepol, isnull(b.fcedeendt,'') as fcedeendt,b.ftype from oriapp a " +
                       "left join polh b on a.fctlid_e = b.fctlid " +
                       "left join [orilayr] D on a.fctlid_ri = D.fctlid_2 " +
                       "left join " + Dbm + "[mprdr] c on a.fdbtrtype = c.ftype and a.fdbtr = c.fid " +
                       "where a.fctlid in ('" + fctlids + "')";
            DataTable dt = DBHelper.GetDataSet(sql1);
            DataTable dtpolh = DBHelper.GetDataSet("select * from polh where fctlid in ( select fctlid_e from oriapp where fctlid = '" + fctlids + "')");
            DataTable dtoinsint_c = DBHelper.GetDataSet("select * from oinsint_c where fctlid_1 in ( select fctlid_e from oriapp where fctlid = '" + fctlids + "')");
            ReportDocument cryRpt = new ReportDocument();
            if (fclass.Contains("CAR"))
            {
                cryRpt.Load(rpDirectory + "\\riapp.rpt");
            }
            if (fclass.Contains("PII"))
            {
                cryRpt.Load(rpDirectory + "\\riapp2.rpt");
            }
            cryRpt.SetDataSource(dt);
            if(cryRpt.Subreports["intsec2"] != null)
                cryRpt.Subreports["intsec2"].SetDataSource(dtoinsint_c);
            if (cryRpt.Subreports["tophl"] != null)
                cryRpt.Subreports["tophl"].SetDataSource(dtpolh);

            cryDocViewer temp_form = new cryDocViewer(cryRpt);
            temp_form.ShowDialog();
            if (cryRpt != null)
            {
                cryRpt.Close();
                cryRpt.Dispose();
            }
        }

        void orgribrkdn() {
            
        }

        void orgribrkdn2() { 
        
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void prih_Resize(object sender, EventArgs e)
        {
            if (WindowState == FormWindowState.Maximized)
            {
                //最大化时所需的操作 
                this.ClientSize = new System.Drawing.Size(687, 363);
                this.WindowState = FormWindowState.Normal;
            }
            else if (WindowState == FormWindowState.Minimized)
            {
                //最小化时所需的操作

                this.ClientSize = new System.Drawing.Size(50, 50);
                this.WindowState = FormWindowState.Normal;
            } 
        }

        private void domainUpDown1_TextChanged(object sender, EventArgs e)
        {
            checkBox3.Checked = true;
            checkBox1.Checked = false;
        }
    }
}
