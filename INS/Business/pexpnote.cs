using CrystalDecisions.CrystalReports.Engine;
using CrystalDecisions.Shared;
using INS.Business.objRpt;
using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business.Report
{
    public partial class pexpnote : Form
    {
        public string Dbm = InsEnvironment.DataBase.GetDbm();
       
        //private string rpDirectory = Application.StartupPath.Replace("\\bin\\Debug", "\\objRpt");
        private string rpDirectory = "M:\\Software II\\New INS Runtime\\Setup\\objRpt";
        public string fctlid_lt, fctlid_1 = "", fctlid = "", fctlids = "", fbus = "", p_rpttype = "", lc_amend = "", p_calling = "", fclass = "";
        public int ln_flogseq = 0; public DateTime ld_faccdate;
        public DataTable dtlist = new DataTable();
        public string Dbo = InsEnvironment.DataBase.GetDbo();
        private expnote expnote;
       
        public pexpnote()
        {
            InitializeComponent();
        }

        public pexpnote(expnote expnote)
        {
            // TODO: Complete member initialization
            this.expnote = expnote;
            InitializeComponent();
        }

        public void ShowMessage(string msg)

        {

            this.Invoke(new MessageBoxShow(MessageBoxShow_F), new object[] { msg });

        }

        delegate void MessageBoxShow(string msg);

        void MessageBoxShow_F(string msg)

        {

            MessageBox.Show(msg, "提示信息", MessageBoxButtons.OK, MessageBoxIcon.Information);

        }

        private void PS_Click(object sender, EventArgs e)
        {
            if (checkBox1.Checked)
            {
                fctlids = fctlid;
            }
            if (checkBox3.Checked)
            {
                DataRow[] rows = dtlist.Select("fctlid <= '" + fctlid + "'", "fctlid desc");
                for (int i = 0; i < Fct.snFat(domainUpDown1.Text); i++)
                {
                    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                    else { fctlids = fctlids + "','" + Fct.stFat(rows[i]["fctlid"]); }
                }
            }
            string sql = "select * from oexdet where fctlid in ('" + fctlids + "')";
            DataTable dt = DBHelper.GetDataSet(sql);

            string sql1 = "select a.*,b.fdesc as fclsdesc from oexdet a left join " + Dbm + "minsc b on rtrim(a.fclass) =rtrim(b.fid) where a.fctlid in ('" + fctlids + "')";
            DataTable dt1 = DBHelper.GetDataSet(sql1);
            string wl_fautono = "";

            if (dt1.Rows.Count > 0 && dt.Rows.Count > 0)
            {
                wl_fautono = dt.Rows[0]["fautono"].ToString().Trim();
                progBar.Value = 0;
                waitlbl.Text = "Processing .....";
                progBar.Visible = true;
                waitlblfr.Visible = true;
                waitlblto.Visible = true;
                waitlbl.Visible = true;
                progBar.Update();
                ReportDocument cryRpt1 = new ReportDocument();
                if (progBar != null) { progBar.Value = 5; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }
                cryRpt1.Load(rpDirectory + "\\expnote.rpt");
                if (progBar != null) { progBar.Value = 50; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }
                cryRpt1.SetDataSource(dt);
                cryRpt1.Subreports["polh"].SetDataSource(dt1);
                cryRpt1.Subreports["endrmk"].SetDataSource(dt);
                if (progBar != null) { progBar.Value = 95; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }
                cryDocViewer temp_form = new cryDocViewer(cryRpt1);

               
                if (progBar != null) { progBar.Value = 100; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }
                temp_form.ShowDialog();
            }
            else
            {
                MessageBox.Show("Invalid Print!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            progBar.Value = 0;
            waitlbl.Text = "Processing .....";
            progBar.Visible = false;
            waitlblfr.Visible = false;
            waitlblto.Visible = false;
            waitlbl.Visible = false;

            this.Close();
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void prih_Resize(object sender, EventArgs e)
        {
            if (WindowState == FormWindowState.Maximized)
            {
                //最大化时所需的操作 
                this.ClientSize = new System.Drawing.Size(687, 363);
                this.WindowState = FormWindowState.Normal;
            }
            else if (WindowState == FormWindowState.Minimized)
            {
                //最小化时所需的操作

                this.ClientSize = new System.Drawing.Size(50, 50);
                this.WindowState = FormWindowState.Normal;
            } 
        }

        private void domainUpDown1_TextChanged(object sender, EventArgs e)
        {
            checkBox3.Checked = true;
            checkBox1.Checked = false;
        }
    }
}
