using CrystalDecisions.CrystalReports.Engine;
using CrystalDecisions.Shared;
using INS.Business.objRpt;
using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business.Report
{
    public partial class psche : Form
    {
       //private string rpDirectory = Application.StartupPath.Replace("\\bin\\Debug", "\\objRpt");
       private string rpDirectory = "M:\\Software II\\New INS Runtime\\Setup\\objRpt";
       //private string rpDirectory1 = "M:\\";
        public String vfbus = "", p_type = "", rpOptid = "", fctlid = "", fctlids = "";
        private DirectPolicy directPolicy;
        public DataTable dt = new DataTable();
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        private RIWard rIWard;
        public psche()
        {
            InitializeComponent();
        }

        public psche(DirectPolicy directPolicy)
        {
            // TODO: Complete member initialization
            this.directPolicy = directPolicy;
            InitializeComponent();
        }

        public psche(RIWard rIWard)
        {
            // TODO: Complete member initialization
            this.rIWard = rIWard;
            InitializeComponent();
        }

        public void ctrlbtn()
        {
            if (rpOptid == "EEC" || rpOptid == "EEC1" || rpOptid == "CMP" || rpOptid == "PMP")
            {
                CI.Visible = true; button2.Visible = true; button6.Visible = true; button5.Visible = true;
            }
            else { CI.Visible = false; button2.Visible = false; button6.Visible = false; button5.Visible = false; }
        }

        void callCrPrmReg(DataSet cryDs, string optid)
        {
            ReportDocument report = new ReportDocument();
            if (optid == "CAR")
                report.Load(rpDirectory + "\\CarSche3.rpt");
            else if (optid == "CAR1")
                report.Load(rpDirectory + "\\CarSche1.rpt");
            else if (optid == "EEC")
                report.Load(rpDirectory + "\\eecsche.rpt");
            else if (optid == "EEC1")
                report.Load(rpDirectory + "\\eecsche1.rpt");
            else if (optid == "PMP")
                report.Load(rpDirectory + "\\pmpsche.rpt");
            else if (optid == "CMP")
                report.Load(rpDirectory + "\\pmpsche.rpt");
            else if (optid == "CPM")
                report.Load(rpDirectory + "\\cpmsche.rpt");
            else if (optid == "CGL")
                report.Load(rpDirectory + "\\cglsche.rpt");
            else if (optid == "MYP")
                report.Load(rpDirectory + "\\mypsche.rpt");
            else if (optid == "FGP")
                report.Load(rpDirectory + "\\fgpsche.rpt");
            else if (optid == "PAR")
                report.Load(rpDirectory + "\\parsche.rpt");
            else if (optid == "CLL") 
                report.Load(rpDirectory + "\\cllsche2.rpt");
            else if (optid == "PII")
                report.Load(rpDirectory + "\\piische2.rpt");
            else if (optid == "EECni")
                report.Load(rpDirectory + "\\eecni.rpt");
            else if (optid == "EECni1")
                report.Load(rpDirectory + "\\eecni.rpt");
            else if (optid == "PMPci" || optid == "CMPci")
                report.Load(rpDirectory + "\\pmpci.rpt");

            report.SetDataSource(cryDs.Tables[0]);
            report.Subreports["polh"].SetDataSource(cryDs.Tables[0]);
            if (optid == "CAR" || optid == "CAR1")
            {
                report.Subreports["intsec1"].SetDataSource(cryDs.Tables[1]);
                report.Subreports["si1"].SetDataSource(cryDs.Tables[2]);
                report.Subreports["intsec2"].SetDataSource(cryDs.Tables[3]);
                report.Subreports["ex1"].SetDataSource(cryDs.Tables[4]);
                report.Subreports["ex2"].SetDataSource(cryDs.Tables[5]);
                report.Subreports["insatt"].SetDataSource(cryDs.Tables[6]);
            }
            if (optid == "CPM")
            {
                report.Subreports["intsec1"].SetDataSource(cryDs.Tables[1]);
                report.Subreports["ex1"].SetDataSource(cryDs.Tables[2]);
                report.Subreports["insatt"].SetDataSource(cryDs.Tables[3]);
                report.Subreports["polh3"].SetDataSource(cryDs.Tables[0]);
            }
            if (optid == "PMP" || optid == "CMP")
            {
                report.Subreports["intsec1"].SetDataSource(cryDs.Tables[1]);
                report.Subreports["liab"].SetDataSource(cryDs.Tables[0]);
                report.Subreports["ex1"].SetDataSource(cryDs.Tables[2]);
                report.Subreports["ex2"].SetDataSource(cryDs.Tables[3]);
                report.Subreports["insatt"].SetDataSource(cryDs.Tables[4]);
                report.Subreports["polh3"].SetDataSource(cryDs.Tables[0]);
            }
            if (optid == "PII")
            {
                report.Subreports["intsec2"].SetDataSource(cryDs.Tables[1]);
                report.Subreports["ex1"].SetDataSource(cryDs.Tables[2]);
                report.Subreports["insatt"].SetDataSource(cryDs.Tables[3]);
                report.Subreports["polh3"].SetDataSource(cryDs.Tables[0]);
            }
            if (optid == "PAR")
            {
                report.Subreports["intsec1"].SetDataSource(cryDs.Tables[1]);
                report.Subreports["intsec2"].SetDataSource(cryDs.Tables[2]);
                report.Subreports["ex1"].SetDataSource(cryDs.Tables[3]);
                report.Subreports["insatt"].SetDataSource(cryDs.Tables[4]);
                report.Subreports["polh3"].SetDataSource(cryDs.Tables[0]);
            }
            if (optid == "CGL")
            {
                report.Subreports["intsec1"].SetDataSource(cryDs.Tables[1]);
                report.Subreports["intsec2"].SetDataSource(cryDs.Tables[2]);
                report.Subreports["ex1"].SetDataSource(cryDs.Tables[3]);
                report.Subreports["insatt"].SetDataSource(cryDs.Tables[4]);
                report.Subreports["polh3"].SetDataSource(cryDs.Tables[0]);
            }
            if (optid == "CLL")
            {
                report.Subreports["intsec2"].SetDataSource(cryDs.Tables[2]);
                report.Subreports["ex1"].SetDataSource(cryDs.Tables[3]);
                report.Subreports["polh3"].SetDataSource(cryDs.Tables[0]);
            }
            if (optid == "MYP")
            {
                report.Subreports["intsec1"].SetDataSource(cryDs.Tables[1]);
                report.Subreports["intsec2"].SetDataSource(cryDs.Tables[2]);
                report.Subreports["ex1"].SetDataSource(cryDs.Tables[3]);
                report.Subreports["insatt"].SetDataSource(cryDs.Tables[4]);
                report.Subreports["polh3"].SetDataSource(cryDs.Tables[0]);
            }
            if (optid == "FGP")
            {
                report.Subreports["intsec1"].SetDataSource(cryDs.Tables[1]);
                report.Subreports["intsec2"].SetDataSource(cryDs.Tables[2]);
                report.Subreports["intsec3"].SetDataSource(cryDs.Tables[3]);
                report.Subreports["ex1"].SetDataSource(cryDs.Tables[4]);
                report.Subreports["insatt"].SetDataSource(cryDs.Tables[5]);
                report.Subreports["polh3"].SetDataSource(cryDs.Tables[0]);
            }
            if (optid == "EEC" || optid == "EEC1")
            {
                report.Subreports["intsec1"].SetDataSource(cryDs.Tables[1]);
                report.Subreports["intsec2"].SetDataSource(cryDs.Tables[1]);
                report.Subreports["liab"].SetDataSource(cryDs.Tables[0]);
                report.Subreports["polh2"].SetDataSource(cryDs.Tables[0]);
                report.Subreports["insatt"].SetDataSource(cryDs.Tables[2]);
                report.Subreports["polh3"].SetDataSource(cryDs.Tables[0]);
            }

            if (isContainChinese(cryDs.Tables[0].Rows[0]["fcontrt_t1"].ToString())) {
                if (optid == "EEC" || optid == "EEC1")
                {
                    CrystalDecisions.CrystalReports.Engine.TextObject xObject;
                    xObject = (CrystalDecisions.CrystalReports.Engine.TextObject)report.Subreports["polh2"].ReportDefinition.Sections[3].ReportObjects["Text4"];
                    xObject.ApplyFont(new Font("細明體_HKSCS", 10));
                }
                //Font f = new Font("細明體_HKSCS", 10);
                //ApplyFontAllText(report.Subreports["intsec1"], f);
            }

            cryDocViewer temp_form = new cryDocViewer(report);
            temp_form.ShowDialog();
        }

        public static void ApplyFontAllText(ReportDocument rapport, Font style)
        {

            foreach (ReportObject obj in rapport.ReportDefinition.ReportObjects)
            {
                if (obj.GetType().Name.Equals("TextObject"))
                {

                    ((TextObject)obj).ApplyFont(style);

                }
                else if (obj.GetType().Name.Equals("FieldObject"))
                {
                    ((FieldObject)obj).ApplyFont(style);
                }
            }
        }

        public static Boolean isContainChinese(String str)
        {

            string r = "[\u4e00-\u9fa5]";
            Regex reg = new Regex(r);
            Match mc = reg.Match(str);
            if (mc.Success)
            {
                return true;
            }
            return false;
        }

        void callCrPrmCI(DataTable cryDT, string optid)
        {
            ReportDocument report = new ReportDocument();
            if (optid == "PMP" || optid == "CMP")
                report.Load(rpDirectory + "\\pmpci.rpt");
            else if (optid == "EEC")
                report.Load(rpDirectory + "\\eecni.rpt");
            else if (optid == "EEC1")
                report.Load(rpDirectory + "\\eecni1.rpt");

            report.SetDataSource(cryDT);
            cryDocViewer temp_form = new cryDocViewer(report);
            temp_form.ShowDialog();
        }

        void callCrPrmR(DataTable cryDT, string optid)
        {
            ReportDocument report = new ReportDocument();
            if (optid == "PAR" || optid == "CAR" || optid == "CGL" || optid == "GLF")
                report.Load(rpDirectory + "\\rparsche.rpt");
            else if (optid == "EEC")
                report.Load(rpDirectory + "\\reecsche.rpt");

            if (optid == "PAR")
            {
                report.DataDefinition.FormulaFields["z_clsname"].Text = String.Format("'{0}'", "Property All Risks");
            }
            if (optid == "CAR")
            {
                report.DataDefinition.FormulaFields["z_clsname"].Text = String.Format("'{0}'", "Contractors' All Risks");
            }
            if (optid == "EEC")
            {
                report.DataDefinition.FormulaFields["z_clsname"].Text = String.Format("'{0}'", "Employees' Compensation");
            }
            if (optid == "CGL")
            {
                report.DataDefinition.FormulaFields["z_clsname"].Text = String.Format("'{0}'", "Public Liability / General Liability");
            }
            if (optid == "GLF")
            {
                report.DataDefinition.FormulaFields["z_clsname"].Text = String.Format("'{0}'", "Golf Insurance");
            }

            report.SetDataSource(cryDT);
            report.Subreports["rliab"].SetDataSource(cryDT);
            cryDocViewer temp_form = new cryDocViewer(report);
            temp_form.ShowDialog();

        }

        private void CI_Click(object sender, EventArgs e)
        {
            if (checkBox1.Checked)
            {
                //fctlids = fctlid;
                //DataRow[] rows = dt.Select("[Policy#] <= '" + fctlid + "'", "[Policy#] desc");
                //for (int i = 0; i < Fct.snFat(1); i++)
                //{
                //    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                //    else { fctlids = fctlids + "','" + Fct.stFat(rows[i]["fctlid"]); }
                //}
            }
            if (checkBox3.Checked)
            {
                DataRow[] rows = dt.Select("[Policy#] <= '" + fctlid + "'", "[Policy#] desc");
                for (int i = 0; i < Fct.snFat(domainUpDown1.Text); i++)
                {
                    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                    else { fctlids = fctlids + "','" + Fct.stFat(rows[i]["fctlid"]); }
                }
            }
            if (rpOptid == "EEC")
                callCrPrmCI(CreateViewEecCI(), rpOptid);

            if (rpOptid == "PMP" || rpOptid == "CMP")
                callCrPrmCI(CreateViewPmpCI(), rpOptid);
        }

        private void PS_Click(object sender, EventArgs e)
        {
            progressBar1.Visible = true;
            progressBar1.Maximum = 50;//设置最大长度值 
            progressBar1.Value = 0;//设置当前值 
            progressBar1.Step = 10;//设置没次增长多少 
            for (int i = 0; i < 5; i++)//循环 
            {
                System.Threading.Thread.Sleep(100);//暂停1秒 
                progressBar1.Value += progressBar1.Step;
            }
            if (checkBox1.Checked)
            {
                //fctlids = fctlid;
                //DataRow[] rows = dt.Select("[Policy#] <= '" + fctlid + "'", "[Policy#] desc");
                //for (int i = 0; i < Fct.snFat(1); i++)
                //{
                //    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                //    else { fctlids = fctlids + "','" + Fct.stFat(rows[i]["fctlid"]); }
                //}
            }
            if (checkBox3.Checked)
            {
                DataRow[] rows = dt.Select("[Policy#] <= '" + fctlid + "'", "[Policy#] desc");
                for (int i = 0; i < Fct.snFat(domainUpDown1.Text); i++)
                {
                    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                    else { fctlids = fctlids + "','" + Fct.stFat(rows[i]["fctlid"]); }
                }
            }
            if (vfbus == "R")
            {
                if (rpOptid == "PAR" || rpOptid == "CAR" || rpOptid == "CGL" || rpOptid == "GLF")
                    callCrPrmR(CreateViewPrmRPar("PAR"), "PAR");

                if (rpOptid == "EEC")
                    callCrPrmR(CreateViewPrmRPar("EEC"), "EEC");
            }
            else
            {
                if (rpOptid == "CAR")
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);

                if (rpOptid == "EEC")
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);

                if (rpOptid == "PMP")
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);

                if (rpOptid == "CMP")
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);

                if (rpOptid == "CPM")
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);

                if (rpOptid == "CGL" || rpOptid == "CLL")
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);

                if (rpOptid == "MYP")
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);

                if (rpOptid == "FGP")
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);

                if (rpOptid == "PAR")
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);

                if (rpOptid == "PII")
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);

            }
            if (rpOptid != "EEC" && rpOptid != "PMP" && rpOptid != "CMP") { this.Close(); }
        }

        public DataTable dtpolh_n(DataTable dtpolh, int dtoinsex1Row, int dtoinsex2Row, int dtoinsattRow)
        {
            DataColumn pmntlabel = dtpolh.Columns.Add("pmntlabel", typeof(String)),
            pcontrt = dtpolh.Columns.Add("pcontrt", typeof(Boolean)),
            pcontrt_t1 = dtpolh.Columns.Add("pcontrt_t1", typeof(Boolean)),
            psite = dtpolh.Columns.Add("psite", typeof(Boolean)),
            psite_t1 = dtpolh.Columns.Add("psite_t1", typeof(Boolean)),
            peffpd_t1 = dtpolh.Columns.Add("peffpd_t1", typeof(Boolean)),
            pmntpd_t1 = dtpolh.Columns.Add("pmntpd_t1", typeof(Boolean)),
            pretrodate = dtpolh.Columns.Add("pretrodate", typeof(Boolean)),
            ppropdate = dtpolh.Columns.Add("ppropdate", typeof(Boolean)),
            ptrdesc = dtpolh.Columns.Add("ptrdesc", typeof(Boolean)),
            pprfdesc = dtpolh.Columns.Add("pprfdesc", typeof(Boolean)),
            pex1 = dtpolh.Columns.Add("pex1", typeof(Boolean)),
            pex2 = dtpolh.Columns.Add("pex2", typeof(Boolean)),
            pattch = dtpolh.Columns.Add("pattch", typeof(Boolean));

            foreach (DataRow dr in dtpolh.Rows)
            {
                dr["filvyamt1"] = (Fct.sdFat(dr["flvyamt1"]) + Fct.sdFat(dr["flvyadj1"])).ToString();
                if (dr["fmntlabel"].ToString().Trim() == "")
                {
                    dr["pmntlabel"] = "1";
                }
                else { dr["pmntlabel"] = dr["fmntlabel"]; }
                if (dr["fcontrt"].ToString().Trim() != "")
                {
                    dr["pcontrt"] = true;
                }
                else { dr["pcontrt"] = false; }
                if (dr["fcontrt_t1"].ToString().Trim() != "")
                {
                    dr["pcontrt_t1"] = true;
                }
                else { dr["pcontrt_t1"] = false; }
                if (dr["fsite"].ToString().Trim() != "")
                {
                    dr["psite"] = true;
                }
                else { dr["psite"] = false; }
                if (dr["fsite_t1"].ToString().Trim() != "")
                {
                    dr["psite_t1"] = true;
                }
                else { dr["psite_t1"] = false; }
                if (dr["feffpd_t1"].ToString().Trim() != "")
                {
                    dr["peffpd_t1"] = true;
                }
                else { dr["peffpd_t1"] = false; }
                if (dr["fmntpd_t1"].ToString().Trim() != "")
                {
                    dr["pmntpd_t1"] = true;
                }
                else { dr["pmntpd_t1"] = false; }
                if (dr["ftrdesc"].ToString().Trim() != "")
                {
                    dr["ptrdesc"] = true;
                }
                else { dr["ptrdesc"] = false; }
                if (dr["fprfdesc"].ToString().Trim() != "")
                {
                    dr["pprfdesc"] = true;
                }
                else { dr["pprfdesc"] = false; }
                if (dr["fretrodate"] != null)
                {
                    dr["pretrodate"] = true;
                }
                else { dr["pretrodate"] = false; }
                if (dr["fpropdate"] != null)
                {
                    dr["ppropdate"] = true;
                }
                else { dr["ppropdate"] = false; }
                if (dtoinsex1Row > 0)
                {
                    dr["pex1"] = true;
                }
                else { dr["pex1"] = false; }
                if (dtoinsex2Row > 0)
                {
                    dr["pex2"] = true;
                }
                else { dr["pex2"] = false; }
                if (dtoinsattRow > 0)
                {
                    dr["pattch"] = true;
                }
                else { dr["pattch"] = false; }
            }
            return dtpolh;
        }

        public DataSet CreateViewDs(string rpOptid)
        {
            DataSet ds = new DataSet();

            DataTable dtpolh = DBHelper.GetDataSet("select * from polh where fctlid in ( '" + fctlids + "')");
            DataTable dtoinsex1 = DBHelper.GetDataSet("select * from oinsex where fctlid_1 in ( '" + fctlids + "') and ftype ='SEC1' order by fsid");
            DataTable dtoinsex2 = DBHelper.GetDataSet("select * from oinsex where fctlid_1 in ( '" + fctlids + "') and ftype ='SEC2' order by fsid");
            DataTable dtoinsatt = DBHelper.GetDataSet("select * from oinsatt where fctlid_1 in ( '" + fctlids + "')  order by flogseq");
            dtpolh = dtpolh_n(dtpolh, dtoinsex1.Rows.Count, dtoinsex2.Rows.Count, dtoinsatt.Rows.Count);

            if (rpOptid == "CAR" || rpOptid == "CAR1")
            {
                DataTable dtoinsint1 = DBHelper.GetDataSet("select * from oinsint where fctlid_1 in ( '" + fctlids + "') and ftype <>'SEC2' order by fsid");
                DataTable dtoinsint2 = DBHelper.GetDataSet("select * from oinsint where fctlid_1 in ( '" + fctlids + "') and ftype <>'SEC2' order by fsid");
                DataTable dtoinsint3 = DBHelper.GetDataSet("select * from oinsint where fctlid_1 in ( '" + fctlids + "') and ftype ='SEC2' order by fsid");
                ds.Tables.Add(dtpolh);
                ds.Tables.Add(dtoinsint1);
                ds.Tables.Add(dtoinsint2);
                ds.Tables.Add(dtoinsint3);
                ds.Tables.Add(dtoinsex1);
                ds.Tables.Add(dtoinsex2);
                ds.Tables.Add(dtoinsatt);
            }

            if (rpOptid == "CPM")
            {
                DataTable dtoinsint_d = DBHelper.GetDataSet("select * from oinsint_d where fctlid_1 in ( '" + fctlids + "')");
                ds.Tables.Add(dtpolh);
                ds.Tables.Add(dtoinsint_d);
                ds.Tables.Add(dtoinsex1);
                ds.Tables.Add(dtoinsatt);
            }

            if (rpOptid == "PMP" || rpOptid == "CMP")
            {
                DataTable dtoinsint_e = DBHelper.GetDataSet("select * from oinsint_e where fctlid_1 in ( '" + fctlids + "')");
                ds.Tables.Add(dtpolh);
                ds.Tables.Add(dtoinsint_e);
                ds.Tables.Add(dtoinsex1);
                ds.Tables.Add(dtoinsex2);
                ds.Tables.Add(dtoinsatt);
            }

            if (rpOptid == "PII")
            {
                DataTable dtoinsint_c = DBHelper.GetDataSet("select * from oinsint_c where fctlid_1 in ( '" + fctlids + "')");
                ds.Tables.Add(dtpolh);
                ds.Tables.Add(dtoinsint_c);
                ds.Tables.Add(dtoinsex1);
                ds.Tables.Add(dtoinsatt);
            }

            if (rpOptid == "PAR")
            {
                DataTable dtoinsloc = DBHelper.GetDataSet("select oinsloc.* ,fitem as 'LSEQ' from oinsloc where fctlid_1 in ( '" + fctlids + "')");
                DataTable dtoinsint = DBHelper.GetDataSet("select a.*,a.fitem as 'ISEQ',b.fitem as 'LSEQ' from oinsint a " +
                                      "left join oinsloc b on a.fctlid_2=b.fctlid where a.fctlid_1 in ( '" + fctlids + "') order by b.fitem,a.fitem");

                for (int i = 0; i < dtoinsloc.Rows.Count; i++)
                {
                    dtoinsloc.Rows[i]["LSEQ"] = (1 + i).ToString();
                    for (int j = 0; j < dtoinsint.Rows.Count; j++)
                    {
                        dtoinsint.Rows[j]["ISEQ"] = (1 + j).ToString();
                        if (dtoinsint.Rows[j]["fctlid_2"].ToString().Trim() == dtoinsloc.Rows[i]["fctlid"].ToString().Trim())
                        { dtoinsint.Rows[j]["LSEQ"] = dtoinsloc.Rows[i]["LSEQ"]; }
                    }
                }

                ds.Tables.Add(dtpolh);
                ds.Tables.Add(dtoinsloc);
                ds.Tables.Add(dtoinsint);
                ds.Tables.Add(dtoinsex1);
                ds.Tables.Add(dtoinsatt);
            }

            if (rpOptid == "CGL" || rpOptid == "CLL")
            {
                DataTable dtoinsint_loc = DBHelper.GetDataSet("select * from oinsloc where fctlid_1 in ( '" + fctlids + "')");
                DataTable dtoinsint_c = DBHelper.GetDataSet("select * from oinsint_c where fctlid_1 in ( '" + fctlids + "')");
                ds.Tables.Add(dtpolh);
                ds.Tables.Add(dtoinsint_loc);
                ds.Tables.Add(dtoinsint_c);
                ds.Tables.Add(dtoinsex1);
                ds.Tables.Add(dtoinsatt);
            }

            if (rpOptid == "MYP")
            {
                DataTable dtoinsloc = DBHelper.GetDataSet("select oinsloc.* ,fitem as 'LSEQ' from oinsloc where fctlid_1 in ( '" + fctlids + "')");
                DataTable dtoinsint = DBHelper.GetDataSet("select a.*,a.fitem as 'ISEQ',b.fitem as 'LSEQ' from oinsint a " +
                                      "left join oinsloc b on a.fctlid_2=b.fctlid where a.fctlid_1 in ( '" + fctlids + "') order by b.fitem,a.fitem");

                for (int i = 0; i < dtoinsloc.Rows.Count; i++)
                {
                    dtoinsloc.Rows[i]["LSEQ"] = (1 + i).ToString();
                    for (int j = 0; j < dtoinsint.Rows.Count; j++)
                    {
                        dtoinsint.Rows[j]["ISEQ"] = (1 + j).ToString();
                        if (dtoinsint.Rows[j]["fctlid_2"].ToString().Trim() == dtoinsloc.Rows[i]["fctlid"].ToString().Trim())
                        { dtoinsint.Rows[j]["LSEQ"] = dtoinsloc.Rows[i]["LSEQ"]; }
                    }
                }

                ds.Tables.Add(dtpolh);
                ds.Tables.Add(dtoinsloc);
                ds.Tables.Add(dtoinsint);
                ds.Tables.Add(dtoinsex1);
                ds.Tables.Add(dtoinsatt);
            }

            if (rpOptid == "FGP")
            {
                DataTable dtoinsint_loc = DBHelper.GetDataSet("select * from oinsloc where fctlid_1 in ( '" + fctlids + "')");
                DataTable dtoinsint_per = DBHelper.GetDataSet("select * from oinsper where fctlid_1 in ( '" + fctlids + "')");
                DataTable dtoinsint_c = DBHelper.GetDataSet("select * from oinsint_c where fctlid_1 in ( '" + fctlids + "')");

                ds.Tables.Add(dtpolh);
                ds.Tables.Add(dtoinsint_loc);
                ds.Tables.Add(dtoinsint_per);
                ds.Tables.Add(dtoinsint_c);
                ds.Tables.Add(dtoinsex1);
                ds.Tables.Add(dtoinsatt);
            }

            if (rpOptid == "EEC" || rpOptid == "EEC1")
            {
                DataTable dtoinsint_b = DBHelper.GetDataSet("select * from oinsint_b where fctlid_1 in ( '" + fctlids + "')");

                ds.Tables.Add(dtpolh);
                ds.Tables.Add(dtoinsint_b);
                ds.Tables.Add(dtoinsatt);
            }

            return ds;
        }

        public DataTable CreateViewEecCI()
        {
            DataTable dtpolh = DBHelper.GetDataSet("select * from polh where fctlid in ( '" + fctlids + "')");
            DataTable dtoinsint_b = DBHelper.GetDataSet("select sum(fnum1) as fnum1,sum(fupdnum1) as fupdnum1 from oinsint_b where fctlid_1 in ( '" + fctlids + "')");
            DataColumn pmntlabel = dtpolh.Columns.Add("pmntlabel", typeof(String)),
               pcontrt = dtpolh.Columns.Add("pcontrt", typeof(Boolean)),
               pcontrt_t1 = dtpolh.Columns.Add("pcontrt_t1", typeof(Boolean)),
               psite = dtpolh.Columns.Add("psite", typeof(Boolean)),
               psite_t1 = dtpolh.Columns.Add("psite_t1", typeof(Boolean)),
               peffpd_t1 = dtpolh.Columns.Add("peffpd_t1", typeof(Boolean)),
               pmntpd_t1 = dtpolh.Columns.Add("pmntpd_t1", typeof(Boolean)),
               ptrdesc = dtpolh.Columns.Add("ptrdesc", typeof(Boolean)),
               pprfdesc = dtpolh.Columns.Add("pprfdesc", typeof(Boolean)),
               flmt = dtpolh.Columns.Add("flmt", typeof(Decimal)),
               fupdlmt = dtpolh.Columns.Add("fupdlmt", typeof(Decimal)),
               fnum1 = dtpolh.Columns.Add("fnum1", typeof(Decimal)),
               fupdnum1 = dtpolh.Columns.Add("fupdnum1", typeof(Decimal));

            foreach (DataRow dr in dtpolh.Rows)
            {
                dr["fsclass"] = dr["fsclass"].ToString().Trim();
                if (dr["fmntlabel"].ToString().Trim() == "")
                {
                    dr["pmntlabel"] = "1";
                }
                else { dr["pmntlabel"] = dr["fmntlabel"]; }
                if (dr["fcontrt"].ToString().Trim() != "")
                {
                    dr["pcontrt"] = true;
                }
                else { dr["pcontrt"] = false; }
                if (dr["fcontrt_t1"].ToString().Trim() != "")
                {
                    dr["pcontrt_t1"] = true;
                }
                else { dr["pcontrt_t1"] = false; }
                if (dr["fsite"].ToString().Trim() != "")
                {
                    dr["psite"] = true;
                }
                else { dr["psite"] = false; }
                if (dr["fsite_t1"].ToString().Trim() != "")
                {
                    dr["psite_t1"] = true;
                }
                else { dr["psite_t1"] = false; }

                if (dr["fendttype"].ToString().Trim() == "1")
                {
                    if (dr["feffpd_t1"].ToString().Trim() != "")
                    {
                        dr["peffpd_t1"] = true;
                    }
                    else { dr["peffpd_t1"] = false; }
                }
                else
                {
                    if (dr["feffpd_p1"].ToString().Trim() != "")
                    {
                        dr["peffpd_t1"] = true;
                    }
                    else { dr["peffpd_t1"] = false; }
                }

                if (dr["fendttype"].ToString().Trim() == "1")
                {
                    if (dr["fmntpd_t1"].ToString().Trim() != "")
                    {
                        dr["pmntpd_t1"] = true;
                    }
                    else { dr["pmntpd_t1"] = false; }
                }
                else
                {
                    if (dr["fmntpd_p1"].ToString().Trim() != "")
                    {
                        dr["pmntpd_t1"] = true;
                    }
                    else { dr["pmntpd_t1"] = false; }
                }

                if (dr["ftrdesc"].ToString().Trim() != "")
                {
                    dr["ptrdesc"] = true;
                }
                else { dr["ptrdesc"] = false; }
                if (dr["fprfdesc"].ToString().Trim() != "")
                {
                    dr["pprfdesc"] = true;
                }
                else { dr["pprfdesc"] = false; }
                foreach (DataRow dr1 in dtoinsint_b.Rows)
                {
                    dr["fnum1"] = dr1["fnum1"];
                    dr["fupdnum1"] = dr1["fupdnum1"];
                }
            }

            return dtpolh;
        }

        public DataTable CreateViewPmpCI()
        {
            string sql = "select a.*,case when a.fendtno ='' then 'N' else 'Y' end as prnendt, " +
                        "b.fupdlmt1,b.fupdlmt2,b.fsicur,b.fciform,b.finsd,b.fissdate,b.fefffr,b.feffto " +
                        "from [dbo].[oinsint_e] a " +
                        "left join polh b on a.fctlid_1=b.fctlid where b.fctlid in ( '" + fctlids + "')";
            DataTable dt = DBHelper.GetDataSet(sql);
            return dt;
        }

        public DataTable CreateViewPrmRPar(string rpOptid)
        {
            string sql = "";
            if (rpOptid == "PAR")
            {
                sql = "select a.*,b.fdesc as fclname from polh a " +
                    "left join (select * from " + Dbm + "mprdr where ftype='C') b  " +
                    "on a.fclnt = b.fid where a.fctlid in ( '" + fctlids + "')";
            }
            else
            {
                sql = "select a.*,b.fdesc as fclname from polh a " +
                 "left join (select * from " + Dbm + "mprdr where ftype='C') b  " +
                 "on a.fclnt = b.fid where a.fctlid in ( '" + fctlids + "')";
            }
            DataTable dt = DBHelper.GetDataSet(sql);
            return dt;
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void psche_Resize(object sender, EventArgs e)
        {
            if (WindowState == FormWindowState.Maximized)
            {
                //最大化时所需的操作 
                this.ClientSize = new System.Drawing.Size(391, 279);
                this.WindowState = FormWindowState.Normal;
            }
            else if (WindowState == FormWindowState.Minimized)
            {
                //最小化时所需的操作

                this.ClientSize = new System.Drawing.Size(50, 50);
                this.WindowState = FormWindowState.Normal;
            }
        }

        private void domainUpDown1_TextChanged(object sender, EventArgs e)
        {
            checkBox3.Checked = true;
            checkBox1.Checked = false;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            if (checkBox1.Checked)
            {
                fctlids = fctlid;
                DataRow[] rows = dt.Select("[Policy#] <= '" + fctlid + "'", "[Policy#] desc");
                for (int i = 0; i < Fct.snFat(1); i++)
                {
                    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                    else { fctlids = fctlids + "','" + Fct.stFat(rows[i]["fctlid"]); }
                }
            }
            if (checkBox3.Checked)
            {
                DataRow[] rows = dt.Select("[Policy#] <= '" + fctlid + "'", "[Policy#] desc");
                for (int i = 0; i < Fct.snFat(domainUpDown1.Text); i++)
                {
                    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                    else { fctlids = fctlids + "','" + Fct.stFat(rows[i]["fctlid"]); }
                }
            }
            Export(CrystalDecisions.Shared.ExportFormatType.WordForWindows, "test.doc");
            MessageBox.Show("Exported Successful");
        }

        public void Export(CrystalDecisions.Shared.ExportFormatType FileType, string FileName)//使用ExportToStream方式匯出
        {
            //create CrystalReport object
            CrystalDecisions.CrystalReports.Engine.ReportDocument report = new CrystalDecisions.CrystalReports.Engine.ReportDocument();

            //load report
            if (rpOptid == "CAR")
                report.Load(rpDirectory + "\\CarSche.rpt");
            else if (rpOptid == "CAR1")
                report.Load(rpDirectory + "\\CarSche1.rpt");
            else if (rpOptid == "EEC")
                report.Load(rpDirectory + "\\eecsche.rpt");
            else if (rpOptid == "EEC1")
                report.Load(rpDirectory + "\\eecsche1.rpt");
            else if (rpOptid == "PMP")
                report.Load(rpDirectory + "\\pmpsche.rpt");
            else if (rpOptid == "CMP")
                report.Load(rpDirectory + "\\pmpsche.rpt");
            else if (rpOptid == "CPM")
                report.Load(rpDirectory + "\\cpmsche.rpt");
            else if (rpOptid == "CGL" || rpOptid == "CLL")
                report.Load(rpDirectory + "\\cglsche.rpt");
            else if (rpOptid == "MYP")
                report.Load(rpDirectory + "\\mypsche.rpt");
            else if (rpOptid == "FGP")
                report.Load(rpDirectory + "\\fgpsche.rpt");
            else if (rpOptid == "PAR")
                report.Load(rpDirectory + "\\parsche.rpt");
            else if (rpOptid == "PII")
                report.Load(rpDirectory + "\\piische2.rpt");
            else if (rpOptid == "CLL")
                report.Load(rpDirectory + "\\cllsche2.rpt");
            DataSet cryDs = CreateViewDs(rpOptid);
            report.SetDataSource(cryDs.Tables[0]);
            report.Subreports["polh"].SetDataSource(cryDs.Tables[0]);
            if (rpOptid == "CAR" || rpOptid == "CAR1")
            {
                report.Subreports["intsec1"].SetDataSource(cryDs.Tables[1]);
                report.Subreports["si1"].SetDataSource(cryDs.Tables[2]);
                report.Subreports["intsec2"].SetDataSource(cryDs.Tables[3]);
                report.Subreports["ex1"].SetDataSource(cryDs.Tables[4]);
                report.Subreports["ex2"].SetDataSource(cryDs.Tables[5]);
                report.Subreports["insatt"].SetDataSource(cryDs.Tables[6]);
            }
            if (rpOptid == "CPM")
            {
                report.Subreports["intsec1"].SetDataSource(cryDs.Tables[1]);
                report.Subreports["ex1"].SetDataSource(cryDs.Tables[2]);
                report.Subreports["insatt"].SetDataSource(cryDs.Tables[3]);
                report.Subreports["polh3"].SetDataSource(cryDs.Tables[0]);
            }
            if (rpOptid == "PMP" || rpOptid == "CMP")
            {
                report.Subreports["intsec1"].SetDataSource(cryDs.Tables[1]);
                report.Subreports["liab"].SetDataSource(cryDs.Tables[0]);
                report.Subreports["ex1"].SetDataSource(cryDs.Tables[2]);
                report.Subreports["ex2"].SetDataSource(cryDs.Tables[3]);
                report.Subreports["insatt"].SetDataSource(cryDs.Tables[4]);
                report.Subreports["polh3"].SetDataSource(cryDs.Tables[0]);
            }
            if (rpOptid == "PII")
            {
                report.Subreports["intsec2"].SetDataSource(cryDs.Tables[1]);
                report.Subreports["ex1"].SetDataSource(cryDs.Tables[2]);
                report.Subreports["insatt"].SetDataSource(cryDs.Tables[3]);
                report.Subreports["polh3"].SetDataSource(cryDs.Tables[0]);
            }
            if (rpOptid == "PAR")
            {
                report.Subreports["intsec1"].SetDataSource(cryDs.Tables[1]);
                report.Subreports["intsec2"].SetDataSource(cryDs.Tables[2]);
                report.Subreports["ex1"].SetDataSource(cryDs.Tables[3]);
                report.Subreports["insatt"].SetDataSource(cryDs.Tables[4]);
                report.Subreports["polh3"].SetDataSource(cryDs.Tables[0]);
            }
            if (rpOptid == "CGL")
            {
                report.Subreports["intsec1"].SetDataSource(cryDs.Tables[1]);
                report.Subreports["intsec2"].SetDataSource(cryDs.Tables[2]);
                report.Subreports["ex1"].SetDataSource(cryDs.Tables[3]);
                report.Subreports["insatt"].SetDataSource(cryDs.Tables[4]);
                report.Subreports["polh3"].SetDataSource(cryDs.Tables[0]);
            }
            if (rpOptid == "MYP")
            {
                report.Subreports["intsec1"].SetDataSource(cryDs.Tables[1]);
                report.Subreports["intsec2"].SetDataSource(cryDs.Tables[2]);
                report.Subreports["ex1"].SetDataSource(cryDs.Tables[3]);
                report.Subreports["insatt"].SetDataSource(cryDs.Tables[4]);
                report.Subreports["polh3"].SetDataSource(cryDs.Tables[0]);
            }
            if (rpOptid == "FGP")
            {
                report.Subreports["intsec1"].SetDataSource(cryDs.Tables[1]);
                report.Subreports["intsec2"].SetDataSource(cryDs.Tables[2]);
                report.Subreports["intsec3"].SetDataSource(cryDs.Tables[3]);
                report.Subreports["ex1"].SetDataSource(cryDs.Tables[4]);
                report.Subreports["insatt"].SetDataSource(cryDs.Tables[5]);
                report.Subreports["polh3"].SetDataSource(cryDs.Tables[0]);
            }
            if (rpOptid == "CLL")
            {
                report.Subreports["intsec2"].SetDataSource(cryDs.Tables[2]);
                report.Subreports["ex1"].SetDataSource(cryDs.Tables[3]);
                report.Subreports["polh3"].SetDataSource(cryDs.Tables[0]);
            }
            if (rpOptid == "EEC" || rpOptid == "EEC1")
            {
                report.Subreports["intsec1"].SetDataSource(cryDs.Tables[1]);
                report.Subreports["intsec2"].SetDataSource(cryDs.Tables[1]);
                report.Subreports["liab"].SetDataSource(cryDs.Tables[0]);
                report.Subreports["polh2"].SetDataSource(cryDs.Tables[0]);
                report.Subreports["insatt"].SetDataSource(cryDs.Tables[2]);
                report.Subreports["polh3"].SetDataSource(cryDs.Tables[0]);
            }
            cryDocViewer temp_form = new cryDocViewer(report);

            try
            {
                ExportOptions CrExportOptions;
                DiskFileDestinationOptions CrDiskFileDestinationOptions = new DiskFileDestinationOptions();
                PdfRtfWordFormatOptions CrFormatTypeOptions = new PdfRtfWordFormatOptions();
                CrDiskFileDestinationOptions.DiskFileName = "M:\\test.doc";
                CrExportOptions = report.ExportOptions;
                {
                    CrExportOptions.ExportDestinationType = ExportDestinationType.DiskFile;
                    CrExportOptions.ExportFormatType = ExportFormatType.WordForWindows;
                    CrExportOptions.DestinationOptions = CrDiskFileDestinationOptions;
                    CrExportOptions.FormatOptions = CrFormatTypeOptions;
                }
                report.Export();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
        }

        private void checkBox3_CheckedChanged(object sender, EventArgs e)
        {
            if (checkBox3.Checked == true)
            {
                checkBox1.Checked = false;
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (checkBox1.Checked)
            {
                //fctlids = fctlid;
                //DataRow[] rows = dt.Select("[Policy#] <= '" + fctlid + "'", "[Policy#] desc");
                //for (int i = 0; i < Fct.snFat(1); i++)
                //{
                //    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                //    else { fctlids = fctlids + "','" + Fct.stFat(rows[i]["fctlid"]); }
                //}
            }
            if (checkBox3.Checked)
            {
                DataRow[] rows = dt.Select("[Policy#] <= '" + fctlid + "'", "[Policy#] desc");
                for (int i = 0; i < Fct.snFat(domainUpDown1.Text); i++)
                {
                    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                    else { fctlids = fctlids + "','" + Fct.stFat(rows[i]["fctlid"]); }
                }
            }

            Export1(CrystalDecisions.Shared.ExportFormatType.WordForWindows, "test.doc");
            MessageBox.Show("Exported Successful");
        }

        protected void Export1(CrystalDecisions.Shared.ExportFormatType FileType, string FileName)//使用ExportToStream方式匯出
        {
            //create CrystalReport object
            CrystalDecisions.CrystalReports.Engine.ReportDocument report = new CrystalDecisions.CrystalReports.Engine.ReportDocument();
            if (rpOptid == "PMP" || rpOptid == "CMP")
                report.Load(rpDirectory + "\\pmpci.rpt");
            else if (rpOptid == "EEC")
                report.Load(rpDirectory + "\\eecni.rpt");
            else if (rpOptid == "EEC1")
                report.Load(rpDirectory + "\\eecni1.rpt");
            DataTable cryDt = new DataTable();

            if (rpOptid == "EEC" || rpOptid == "EEC1")
                cryDt = CreateViewEecCI();

            if (rpOptid == "PMP" || rpOptid == "CMP")
                cryDt = CreateViewPmpCI();

            report.SetDataSource(cryDt);
            cryDocViewer temp_form = new cryDocViewer(report);

            try
            {
                ExportOptions CrExportOptions;
                DiskFileDestinationOptions CrDiskFileDestinationOptions = new DiskFileDestinationOptions();
                PdfRtfWordFormatOptions CrFormatTypeOptions = new PdfRtfWordFormatOptions();
                CrDiskFileDestinationOptions.DiskFileName = "M:\\test.doc";
                
                CrExportOptions = report.ExportOptions;
                {
                    CrExportOptions.ExportDestinationType = ExportDestinationType.DiskFile;
                    CrExportOptions.ExportFormatType = ExportFormatType.WordForWindows;
                    CrExportOptions.DestinationOptions = CrDiskFileDestinationOptions;
                    CrExportOptions.FormatOptions = CrFormatTypeOptions;
                }
                report.Export();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
        }

        private void button4_Click(object sender, EventArgs e)
        {
            if (checkBox1.Checked)
            {
                //fctlids = fctlid;
                //DataRow[] rows = dt.Select("[Policy#] <= '" + fctlid + "'", "[Policy#] desc");
                //for (int i = 0; i < Fct.snFat(1); i++)
                //{
                //    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                //    else { fctlids = fctlids + "','" + Fct.stFat(rows[i]["fctlid"]); }
                //}
            }
            if (checkBox3.Checked)
            {
                DataRow[] rows = dt.Select("[Policy#] <= '" + fctlid + "'", "[Policy#] desc");
                for (int i = 0; i < Fct.snFat(domainUpDown1.Text); i++)
                {
                    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                    else { fctlids = fctlids + "','" + Fct.stFat(rows[i]["fctlid"]); }
                }
            }
            if (vfbus == "R")
            {
                if (rpOptid == "PAR" || rpOptid == "CAR" || rpOptid == "CGL" || rpOptid == "GLF")
                    callCrPrmR(CreateViewPrmRPar("PAR"), "PAR");

                if (rpOptid == "EEC")
                    callCrPrmR(CreateViewPrmRPar("EEC"), "EEC");
            }
            else
            {
                if (rpOptid == "CAR")
                {
                    rpOptid = "CAR1";
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);
                }

                if (rpOptid == "EEC")
                {
                    rpOptid = "EEC1";
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);
                }

                if (rpOptid == "PMP")
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);

                if (rpOptid == "CMP")
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);

                if (rpOptid == "CPM")
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);

                if (rpOptid == "CGL" || rpOptid == "CLL")
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);

                if (rpOptid == "MYP")
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);

                if (rpOptid == "FGP")
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);

                if (rpOptid == "PAR")
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);

                if (rpOptid == "PII")
                    callCrPrmReg(CreateViewDs(rpOptid), rpOptid);

            }
            if (rpOptid != "EEC" && rpOptid != "PMP" && rpOptid != "CMP") { this.Close(); }
        }

        private void button3_Click(object sender, EventArgs e)
        {
            if (checkBox1.Checked)
            {
                //fctlids = fctlid;
                //DataRow[] rows = dt.Select("[Policy#] <= '" + fctlid + "'", "[Policy#] desc");
                //for (int i = 0; i < Fct.snFat(1); i++)
                //{
                //    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                //    else { fctlids = fctlids + "','" + Fct.stFat(rows[i]["fctlid"]); }
                //}
            }
            if (checkBox3.Checked)
            {
                DataRow[] rows = dt.Select("[Policy#] <= '" + fctlid + "'", "[Policy#] desc");
                for (int i = 0; i < Fct.snFat(domainUpDown1.Text); i++)
                {
                    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                    else { fctlids = fctlids + "','" + Fct.stFat(rows[i]["fctlid"]); }
                }
            }
            Export(CrystalDecisions.Shared.ExportFormatType.WordForWindows, "test.doc");
            MessageBox.Show("Exported Successful");
        }

        private void button6_Click(object sender, EventArgs e)
        {
            if (checkBox1.Checked)
            {
                //fctlids = fctlid;
                //DataRow[] rows = dt.Select("[Policy#] <= '" + fctlid + "'", "[Policy#] desc");
                //for (int i = 0; i < Fct.snFat(1); i++)
                //{
                //    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                //    else { fctlids = fctlids + "','" + Fct.stFat(rows[i]["fctlid"]); }
                //}
            }
            if (checkBox3.Checked)
            {
                DataRow[] rows = dt.Select("[Policy#] <= '" + fctlid + "'", "[Policy#] desc");
                for (int i = 0; i < Fct.snFat(domainUpDown1.Text); i++)
                {
                    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                    else { fctlids = fctlids + "','" + Fct.stFat(rows[i]["fctlid"]); }
                }
            }
            if (rpOptid == "EEC") {
                rpOptid = "EEC1";
                callCrPrmCI(CreateViewEecCI(), rpOptid);
            }

            if (rpOptid == "PMP" || rpOptid == "CMP")
                callCrPrmCI(CreateViewPmpCI(), rpOptid);
        }

        private void button5_Click(object sender, EventArgs e)
        {
            if (checkBox1.Checked)
            {
                //fctlids = fctlid;
                //DataRow[] rows = dt.Select("[Policy#] <= '" + fctlid + "'", "[Policy#] desc");
                //for (int i = 0; i < Fct.snFat(1); i++)
                //{
                //    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                //    else { fctlids = fctlids + "','" + Fct.stFat(rows[i]["fctlid"]); }
                //}
            }
            if (checkBox3.Checked)
            {
                DataRow[] rows = dt.Select("[Policy#] <= '" + fctlid + "'", "[Policy#] desc");
                for (int i = 0; i < Fct.snFat(domainUpDown1.Text); i++)
                {
                    if (fctlids == "") { fctlids = Fct.stFat(rows[i]["fctlid"]); }
                    else { fctlids = fctlids + "','" + Fct.stFat(rows[i]["fctlid"]); }
                }
            }

            Export1(CrystalDecisions.Shared.ExportFormatType.WordForWindows, "test.doc");
            MessageBox.Show("Exported Successful");
        }
    }
}
