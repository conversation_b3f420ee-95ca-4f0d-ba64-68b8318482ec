                                        Gen.Genflgpm = Insured.Insureflgpm;
                                        Gen.Genfltdamt = Insured.Insurefltdamt;
                                        Gen.Genflcomamt = Insured.Insureflcomamt;
                                        Gen.Genflnpm = Insured.Insureflnpm;
                                    }using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Configuration;
using INS.Ctrl;
using System.Collections;

namespace INS.Business
{
    public partial class RIWard : Form
    {
        public string user = "";
        public string Class = "";
        public string Classfctlid = "";
        public string policyNo = "";
        public string fctlid = "";
        public string fconfirm = "";
        public string com = "";
        public string disc = "";

        DBConnect operate = new DBConnect();
        public Boolean Addflag = false;
        public Boolean updateflag = false;
        public int Policylistrow = 0;

        
        DirectExcess Excess = new DirectExcess();
        DirectAttach Attach = new DirectAttach();
        DirectInsured_PMP InsuredE = new DirectInsured_PMP();
        RIGen Gen = new RIGen();
        RIInstall Install = new RIInstall();
        RIInsured_CAR Insured = new RIInsured_CAR();
        RIInsured_CGL InsuredC = new RIInsured_CGL();
        RIInsured_CPM InsuredD = new RIInsured_CPM();
        RIInsured_EEC InsuredB = new RIInsured_EEC();
        RIInsured_PAR Insured2 = new RIInsured_PAR();

        public RIWard()
        {
            InitializeComponent();
            FillData("");
        }

       public void FillData(string order)
        {
            String Sql = "select fctlid, fcom, ftd, fpolno as Policy#,fissdate as [Issue Date],fefffr as [Effect Fr],feffto as [Effect To], " +
                        "fprdr as Producer#,finsd as Contractors,fexno as [Expiry No.],frnno as [Renew No.], "+
                        "case when fconfirm='1' then 'Pending' else  "+
                        "case when fconfirm='2' then 'Hold' else "+
                        "case when fconfirm='3' then 'Confirmed' else 'Cancelled' end end end as Status "+
                        "from polh where fbus='R' and ftype = 'P' AND fclass ='" + Class + "' order by fupddate desc";
            Policylist.DataSource = DBHelper.GetDataSet(Sql);
            this.Policylist.Columns[0].Visible = false;
            this.Policylist.Columns[1].Visible = false;
            this.Policylist.Columns[2].Visible = false;

            if (Policylist.Rows.Count == 0) {
                panel10.Controls.Clear();
                DirectButton temp_ctrlbutton = new DirectButton();
                temp_ctrlbutton.fconfirm = fconfirm;
                temp_ctrlbutton.buttonload();
                temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
                temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
                temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
                temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
                temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
                temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
                panel10.Controls.Add(temp_ctrlbutton);
            }
        }

       private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
           if (Addflag == true || updateflag == true)
           {
               if (e.TabPage == tabPage1)
                   e.Cancel = true;
           }
       }

       private void tabControl1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            if (Policylist.Rows.Count > 0)
            {
                int counter;
                counter = Policylist.CurrentCell.RowIndex;
                if (tabControl1.SelectedIndex == 1)
                {
                    counter = Policylist.CurrentCell.RowIndex;
                    if (Policylist.Rows[counter].Cells["fctlid"].Value != null)
                    {
                        if (Policylist.Rows[counter].Cells["fctlid"].Value.ToString().Length != 0)
                        {
                            fctlid = Policylist.Rows[counter].Cells["fctlid"].Value.ToString();
                            if (Addflag == false && updateflag == false)
                            {
                                tabPage2reload(fctlid);
                            }
                            else
                            {
                                if (Class == "CAR" || Class == "CSB")
                                {
                                    Insured.Caculate();
                                    Gen.GenOTPL = Insured.InsureOTPL;
                                    Gen.GenTPL = Insured.InsureTPL;
                                    Gen.GenTotComm = Insured.InsureTotComm;
                                    Gen.GenTotDisc = Insured.InsureTotDisc;
                                    Gen.GenOSum = Insured.InsureOSum;
                                    Gen.GenSum = Insured.InsureSum;
                                    Gen.GenOGP = Insured.InsureOGP;
                                    Gen.GenGP = Insured.InsureGP;
                                    Gen.GenNP = Insured.InsureNP;
                                    Gen.installTotal = Install.installTotal;


                                }
                                if (Class == "MYP" || Class == "PAR")
                                {
                                    Insured2.Caculate();
                                    Gen.GenTPL = Insured2.InsureTPL;
                                    Gen.GenTotComm = Insured2.InsureTotComm;
                                    Gen.GenTotDisc = Insured2.InsureTotDisc;
                                    Gen.GenOSum = Insured2.InsureOSum;
                                    Gen.GenSum = Insured2.InsureSum;
                                    Gen.GenOGP = Insured2.InsureOGP;
                                    Gen.GenGP = Insured2.InsureGP;
                                    Gen.GenNP = Insured2.InsureNP;
                                    Gen.installTotal = Install.installTotal;
                                }
                                if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII" || Class == "GLF")
                                {
                                    InsuredC.Caculate();
                                    Gen.GenTotComm = InsuredC.InsureTotComm;
                                    Gen.GenTotDisc = InsuredC.InsureTotDisc;
                                    Gen.installTotal = Install.installTotal;
                                    if (Class == "CGL")
                                    {
                                        Gen.GenOTPL = InsuredC.InsureOSum;
                                        Gen.GenTPL = InsuredC.InsureSum;
                                    }
                                    else
                                    {
                                        Gen.GenOSum = InsuredC.InsureOSum;
                                        Gen.GenSum = InsuredC.InsureSum;
                                    }
                                    Gen.GenOGP = InsuredC.InsureOGP;
                                    Gen.GenGP = InsuredC.InsureGP;
                                    Gen.GenNP = InsuredC.InsureNP;
                                }
                                if (Class == "CPM")
                                {
                                    InsuredD.Caculate();
                                    Gen.GenTotComm = InsuredD.InsureTotComm;
                                    Gen.GenTotDisc = InsuredD.InsureTotDisc;
                                    Gen.GenOSum = InsuredD.InsureOSum;
                                    Gen.GenSum = InsuredD.InsureSum;
                                    Gen.GenOGP = InsuredD.InsureOGP;
                                    Gen.GenGP = InsuredD.InsureGP;
                                    Gen.GenNP = InsuredD.InsureNP;
                                    Gen.installTotal = Install.installTotal;
                                }
                                if (Class == "PMP" || Class == "CMP")
                                {
                                    InsuredE.Caculate();
                                    Gen.GenTotComm = InsuredE.InsureTotComm;
                                    Gen.GenTotDisc = InsuredE.InsureTotDisc;
                                    Gen.GenSum = InsuredE.InsureSum;
                                    Gen.GenGP = InsuredE.InsureGP;
                                    Gen.GenNP = InsuredE.InsureNP;
                                    Gen.installTotal = Install.installTotal;
                                }
                                if (Class == "EEC")
                                {
                                    InsuredB.Caculate();
                                    Gen.GenTotComm = InsuredB.InsureTotComm;
                                    Gen.GenTotDisc = InsuredB.InsureTotDisc;
                                    Gen.GenOSum = InsuredB.InsureOSum;
                                    Gen.GenSum = InsuredB.InsureSum;
                                    Gen.GenOGP = InsuredB.InsureOGP;
                                    Gen.GenGP = InsuredB.InsureGP;
                                    Gen.GenNP = InsuredB.InsureNP;
                                    InsuredB.fsclass = Gen.GenSubClass;
                                    Gen.installTotal = Install.installTotal;
                                }
                            }
                        }
                    }
                }
                if (tabControl1.SelectedIndex == 2)
                {
                    counter = Policylist.CurrentCell.RowIndex;
                    if (Policylist.Rows[counter].Cells["fctlid"].Value != null)
                    {
                        if (Policylist.Rows[counter].Cells["fctlid"].Value.ToString().Length != 0)
                        {
                            fctlid = Policylist.Rows[counter].Cells["fctlid"].Value.ToString();
                        }
                    }
                    if (Addflag == false && updateflag == false)
                    {
                        tabPage3reload(fctlid, com, disc);
                    }
                    else
                    {
                        if (Class == "CAR" || Class == "CSB")
                        {
                            Insured.InsureComm = Gen.GenComm;
                            Insured.InsureDisc = Gen.GenDisc;
                            Insured.InsureShare1 = Gen.GenShare1;
                            Insured.InsureShare2 = Gen.GenShare2;
                            Insured.Caculate();
                        }
                        if (Class == "MYP" || Class == "PAR")
                        {
                            Insured2.InsureComm = Gen.GenComm;
                            Insured2.InsureDisc = Gen.GenDisc;
                            Insured2.InsureShare = Gen.GenShare1;
                           // Insured2.InsureSpecialType = Gen.GenSpecialType;
                            if (Insured2.InsureSpecialType != Gen.GenSpecialType)
                            {
                                panel4.Controls.Clear();
                                Insured2.fctlid = fctlid;
                                Insured2.Class = Class;
                                Insured2.Classfctlid = Classfctlid;
                                Insured2.InsureSpecialType = Gen.GenSpecialType;
                                Insured2.buttoncontroladd();
                                panel4.Controls.Add(Insured2);
                            }
                            Insured2.Caculate();
                        }
                        if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII" || Class == "GLF")
                        {
                            InsuredC.InsureComm = Gen.GenComm;
                            InsuredC.InsureDisc = Gen.GenDisc;
                            if (Class == "CGL") { InsuredC.InsureShare = Gen.GenShare2; }
                            else { InsuredC.InsureShare = Gen.GenShare1; }
                            InsuredC.Caculate();
                        }
                        if (Class == "CPM")
                        {
                            InsuredD.InsureComm = Gen.GenComm;
                            InsuredD.InsureDisc = Gen.GenDisc;
                            InsuredD.InsureShare = Gen.GenShare1;
                            InsuredD.Caculate();
                        }
                        if (Class == "PMP" || Class == "CMP")
                        {
                            InsuredE.InsureComm = Gen.GenComm;
                            InsuredE.InsureDisc = Gen.GenDisc;
                            InsuredE.Caculate();
                        }
                        if (Class == "EEC")
                        {
                            InsuredB.InsureComm = Gen.GenComm;
                            InsuredB.InsureDisc = Gen.GenDisc;
                            InsuredB.InsureShare = Gen.GenShare1;
                            if (Gen.GenSubClass == "CONTRACTOR")
                            {
                                InsuredB.InsureSum2 = Gen.GenOSum;
                            }
                            if (InsuredB.fsclass != Gen.GenSubClass)
                            {
                                panel4.Controls.Clear();
                                InsuredB.fctlid = fctlid;
                                InsuredB.Class = Class;
                                InsuredB.Classfctlid = Classfctlid;
                                InsuredB.fsclass = Gen.GenSubClass;
                                InsuredB.buttoncontroladd();
                                panel4.Controls.Add(InsuredB);
                            }
                            InsuredB.Caculate();
                        }
                    }

                }
                if (tabControl1.SelectedIndex == 3)
                {
                    counter = Policylist.CurrentCell.RowIndex;
                    if (Policylist.Rows[counter].Cells["fctlid"].Value != null)
                    {
                        if (Policylist.Rows[counter].Cells["fctlid"].Value.ToString().Length != 0)
                        {
                            fctlid = Policylist.Rows[counter].Cells["fctlid"].Value.ToString();
                            if (Addflag == false && updateflag == false)
                            {
                                tabPage4reload(fctlid);
                            }
                        }
                    }
                }
                if (tabControl1.SelectedIndex == 4)
                {
                    counter = Policylist.CurrentCell.RowIndex;
                    if (Policylist.Rows[counter].Cells["fctlid"].Value != null)
                    {
                        if (Policylist.Rows[counter].Cells["fctlid"].Value.ToString().Length != 0)
                        {
                            fctlid = Policylist.Rows[counter].Cells["fctlid"].Value.ToString();
                            if (Addflag == false && updateflag == false)
                            {
                                tabPage5reload(fctlid);
                            }
                        }
                    }
                }

                if (tabControl1.SelectedIndex == 5)
                {
                    counter = Policylist.CurrentCell.RowIndex;
                    if (Policylist.Rows[counter].Cells["fctlid"].Value != null)
                    {
                        if (Policylist.Rows[counter].Cells["fctlid"].Value.ToString().Length != 0)
                        {
                            fctlid = Policylist.Rows[counter].Cells["fctlid"].Value.ToString();
                        }
                    }
                    if (Policylist.Rows[counter].Cells["fctlid"].Value != null)
                    {
                        if (Policylist.Rows[counter].Cells["fctlid"].Value.ToString().Length != 0)
                        {
                            fctlid = Policylist.Rows[counter].Cells["fctlid"].Value.ToString();
                        }
                    }
                    if (Addflag == false && updateflag == false)
                    {
                        tabPage6reload(fctlid);
                        Install.InstallTotComm = Gen.GenTotComm;
                        Install.InstallTotDisc = Gen.GenTotDisc;
                        Install.InstallGP = Gen.GenGP;
                        Install.InstallNP = Gen.GenNP;
                        Install.InstallComm = Gen.GenComm;
                        Install.InstallDisc = Gen.GenDisc;
                        Install.InstallEffrDate = Gen.GenEffrDate;
                        Install.InstallEftoDate = Gen.GenEftoDate;
                        Install.InstallIsDate = Gen.GenIsDate;
                        Gen.installTotal = Install.installTotal;
                    }
                    else
                    {
                        if (Class == "CAR" || Class == "CSB")
                        {
                            Insured.Caculate();
                            Install.InstallTotComm = Insured.InsureTotComm;
                            Install.InstallTotDisc = Insured.InsureTotDisc;
                            Install.InstallGP = Insured.InsureGP;
                            Install.InstallNP = Insured.InsureNP;
                        }
                        if (Class == "MYP" || Class == "PAR")
                        {
                            Insured2.Caculate();
                            Install.InstallTotComm = Insured2.InsureTotComm;
                            Install.InstallTotDisc = Insured2.InsureTotDisc;
                            Install.InstallGP = Insured2.InsureGP;
                            Install.InstallNP = Insured2.InsureNP;
                        }
                        if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII")
                        {
                            InsuredC.Caculate();
                            Install.InstallTotComm = InsuredC.InsureTotComm;
                            Install.InstallTotDisc = InsuredC.InsureTotDisc;
                            Install.InstallGP = InsuredC.InsureGP;
                            Install.InstallNP = InsuredC.InsureNP;
                        }
                        if (Class == "CPM")
                        {
                            InsuredD.Caculate();
                            Install.InstallTotComm = InsuredD.InsureTotComm;
                            Install.InstallTotDisc = InsuredD.InsureTotDisc;
                            Install.InstallGP = InsuredD.InsureGP;
                            Install.InstallNP = InsuredD.InsureNP;
                        }
                        if (Class == "PMP" || Class == "CMP")
                        {
                            InsuredE.Caculate();
                            Install.InstallTotComm = InsuredE.InsureTotComm;
                            Install.InstallTotDisc = InsuredE.InsureTotDisc;
                            Install.InstallGP = InsuredE.InsureGP;
                            Install.InstallNP = InsuredE.InsureNP;
                        }
                        if (Class == "EEC")
                        {
                            InsuredB.Caculate();
                            Install.InstallTotComm = InsuredB.InsureTotComm;
                            Install.InstallTotDisc = InsuredB.InsureTotDisc;
                            Install.InstallGP = InsuredB.InsureGP;
                            Install.InstallNP = InsuredB.InsureNP;
                        }
                        Install.InstallComm = Gen.GenComm;
                        Install.InstallDisc = Gen.GenDisc;
                        Install.InstallEffrDate = Gen.GenEffrDate;
                        Install.InstallEftoDate = Gen.GenEftoDate;
                        Install.InstallIsDate = Gen.GenIsDate;
                        Gen.installTotal = Install.installTotal;
                    }
                }
            }
            else {
                if (Addflag == true || updateflag == true) {
                    if (tabControl1.SelectedIndex == 1)
                    {
                        if (Class == "CAR" || Class == "CSB")
                        {
                            Insured.Caculate();
                            Gen.GenOTPL = Insured.InsureOTPL;
                            Gen.GenTPL = Insured.InsureTPL;
                            Gen.GenTotComm = Insured.InsureTotComm;
                            Gen.GenTotDisc = Insured.InsureTotDisc;
                            Gen.GenOSum = Insured.InsureOSum;
                            Gen.GenSum = Insured.InsureSum;
                            Gen.GenOGP = Insured.InsureOGP;
                            Gen.GenGP = Insured.InsureGP;
                            Gen.GenNP = Insured.InsureNP;
                            Gen.installTotal = Install.installTotal;
                        }
                        if (Class == "MYP" || Class == "PAR")
                        {
                            Insured2.Caculate();
                            Gen.GenTPL = Insured2.InsureTPL;
                            Gen.GenTotComm = Insured2.InsureTotComm;
                            Gen.GenTotDisc = Insured2.InsureTotDisc;
                            Gen.GenOSum = Insured2.InsureOSum;
                            Gen.GenSum = Insured2.InsureSum;
                            Gen.GenOGP = Insured2.InsureOGP;
                            Gen.GenGP = Insured2.InsureGP;
                            Gen.GenNP = Insured2.InsureNP;
                            Gen.installTotal = Install.installTotal;
                        }
                        if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII" || Class == "GLF")
                        {
                            InsuredC.Caculate();
                            Gen.GenTotComm = InsuredC.InsureTotComm;
                            Gen.GenTotDisc = InsuredC.InsureTotDisc;
                            Gen.installTotal = Install.installTotal;
                            if (Class == "CGL")
                            {
                                Gen.GenOTPL = InsuredC.InsureOSum;
                                Gen.GenTPL = InsuredC.InsureSum;
                            }
                            else
                            {
                                Gen.GenOSum = InsuredC.InsureOSum;
                                Gen.GenSum = InsuredC.InsureSum;
                            }
                            Gen.GenOGP = InsuredC.InsureOGP;
                            Gen.GenGP = InsuredC.InsureGP;
                            Gen.GenNP = InsuredC.InsureNP;
                        }
                        if (Class == "CPM")
                        {
                            InsuredD.Caculate();
                            Gen.GenTotComm = InsuredD.InsureTotComm;
                            Gen.GenTotDisc = InsuredD.InsureTotDisc;
                            Gen.GenOSum = InsuredD.InsureOSum;
                            Gen.GenSum = InsuredD.InsureSum;
                            Gen.GenOGP = InsuredD.InsureOGP;
                            Gen.GenGP = InsuredD.InsureGP;
                            Gen.GenNP = InsuredD.InsureNP;
                            Gen.installTotal = Install.installTotal;
                        }
                        if (Class == "PMP" || Class == "CMP")
                        {
                            InsuredE.Caculate();
                            Gen.GenTotComm = InsuredE.InsureTotComm;
                            Gen.GenTotDisc = InsuredE.InsureTotDisc;
                            Gen.GenSum = InsuredE.InsureSum;
                            Gen.GenGP = InsuredE.InsureGP;
                            Gen.GenNP = InsuredE.InsureNP;
                            Gen.installTotal = Install.installTotal;
                        }
                        if (Class == "EEC")
                        {
                            InsuredB.Caculate();
                            Gen.GenTotComm = InsuredB.InsureTotComm;
                            Gen.GenTotDisc = InsuredB.InsureTotDisc;
                            Gen.GenOSum = InsuredB.InsureOSum;
                            Gen.GenSum = InsuredB.InsureSum;
                            Gen.GenOGP = InsuredB.InsureOGP;
                            Gen.GenGP = InsuredB.InsureGP;
                            Gen.GenNP = InsuredB.InsureNP;
                            InsuredB.fsclass = Gen.GenSubClass;
                            Gen.installTotal = Install.installTotal;
                        }
                    }
                    if (tabControl1.SelectedIndex == 2)
                    {
                        if (Class == "CAR" || Class == "CSB")
                        {
                            Insured.InsureComm = Gen.GenComm;
                            Insured.InsureDisc = Gen.GenDisc;
                            Insured.InsureShare1 = Gen.GenShare1;
                            Insured.InsureShare2 = Gen.GenShare2;
                            Insured.Caculate();
                        }
                        if (Class == "MYP" || Class == "PAR")
                        {
                            Insured2.InsureComm = Gen.GenComm;
                            Insured2.InsureDisc = Gen.GenDisc;
                            Insured2.InsureShare = Gen.GenShare1;
                            Insured2.Caculate();
                        }
                        if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII" || Class == "GLF")
                        {
                            InsuredC.InsureComm = Gen.GenComm;
                            InsuredC.InsureDisc = Gen.GenDisc;
                            if (Class == "CGL") { InsuredC.InsureShare = Gen.GenShare2; }
                            else { InsuredC.InsureShare = Gen.GenShare1; }
                            InsuredC.Caculate();
                        }
                        if (Class == "CPM")
                        {
                            InsuredD.InsureComm = Gen.GenComm;
                            InsuredD.InsureDisc = Gen.GenDisc;
                            InsuredD.InsureShare = Gen.GenShare1;
                            InsuredD.Caculate();
                        }
                        if (Class == "PMP" || Class == "CMP")
                        {
                            InsuredE.InsureComm = Gen.GenComm;
                            InsuredE.InsureDisc = Gen.GenDisc;
                            InsuredE.Caculate();
                        }
                        if (Class == "EEC")
                        {
                            InsuredB.InsureComm = Gen.GenComm;
                            InsuredB.InsureDisc = Gen.GenDisc;
                            InsuredB.InsureShare = Gen.GenShare1;
                            if (Gen.GenSubClass == "CONTRACTOR")
                            {
                                InsuredB.InsureSum2 = Gen.GenOSum;
                            }
                            if (InsuredB.fsclass != Gen.GenSubClass)
                            {
                                panel4.Controls.Clear();
                                InsuredB.fctlid = fctlid;
                                InsuredB.Class = Class;
                                InsuredB.Classfctlid = Classfctlid;
                                InsuredB.fsclass = Gen.GenSubClass;
                                InsuredB.buttoncontroladd();
                                panel4.Controls.Add(InsuredB);
                            }
                            InsuredB.Caculate();
                        }
                    }
                    if (tabControl1.SelectedIndex == 5)
                    {
                        if (Class == "CAR" || Class == "CSB")
                        {
                            Insured.Caculate();
                            Install.InstallTotComm = Insured.InsureTotComm;
                            Install.InstallTotDisc = Insured.InsureTotDisc;
                            Install.InstallGP = Insured.InsureGP;
                            Install.InstallNP = Insured.InsureNP;
                        }
                        if (Class == "MYP" || Class == "PAR")
                        {
                            Insured2.Caculate();
                            Install.InstallTotComm = Insured2.InsureTotComm;
                            Install.InstallTotDisc = Insured2.InsureTotDisc;
                            Install.InstallGP = Insured2.InsureGP;
                            Install.InstallNP = Insured2.InsureNP;
                        }
                        if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII")
                        {
                            InsuredC.Caculate();
                            Install.InstallTotComm = InsuredC.InsureTotComm;
                            Install.InstallTotDisc = InsuredC.InsureTotDisc;
                            Install.InstallGP = InsuredC.InsureGP;
                            Install.InstallNP = InsuredC.InsureNP;
                        }
                        if (Class == "CPM")
                        {
                            InsuredD.Caculate();
                            Install.InstallTotComm = InsuredD.InsureTotComm;
                            Install.InstallTotDisc = InsuredD.InsureTotDisc;
                            Install.InstallGP = InsuredD.InsureGP;
                            Install.InstallNP = InsuredD.InsureNP;
                        }
                        if (Class == "PMP" || Class == "CMP")
                        {
                            InsuredE.Caculate();
                            Install.InstallTotComm = InsuredE.InsureTotComm;
                            Install.InstallTotDisc = InsuredE.InsureTotDisc;
                            Install.InstallGP = InsuredE.InsureGP;
                            Install.InstallNP = InsuredE.InsureNP;
                        }
                        if (Class == "EEC")
                        {
                            InsuredB.Caculate();
                            Install.InstallTotComm = InsuredB.InsureTotComm;
                            Install.InstallTotDisc = InsuredB.InsureTotDisc;
                            Install.InstallGP = InsuredB.InsureGP;
                            Install.InstallNP = InsuredB.InsureNP;
                        }
                        Install.InstallComm = Gen.GenComm;
                        Install.InstallDisc = Gen.GenDisc;
                        Install.InstallEffrDate = Gen.GenEffrDate;
                        Install.InstallEftoDate = Gen.GenEftoDate;
                        Install.InstallIsDate = Gen.GenIsDate;
                        Gen.installTotal = Install.installTotal;
                    }
                }
            }
        }

        void tabpage1reload()
        {
            panel10.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttonload();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonRenewClicked += new EventHandler(renewattch_Click);
            panel10.Controls.Add(temp_ctrlbutton);

            tabPage2reload(fctlid);
            tabPage3reload(fctlid,com,disc);
            tabPage4reload(fctlid);
            tabPage5reload(fctlid);
            tabPage6reload(fctlid);
        }

        void tabPage2reload(string fctlid)
        {
            panel22.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttonload();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonRenewClicked += new EventHandler(renewattch_Click);
            panel22.Controls.Add(temp_ctrlbutton);

            panel1.Controls.Clear();
            Gen.fctlid = fctlid;
            Gen.Class = Class;
            Gen.Classfctlid = Classfctlid;
            Gen.fbus = "R";
            Gen.ftype = "P";
            Gen.user = user;
            Gen.buttoncontrolback();
            panel1.Controls.Add(Gen);
        }

        void tabPage3reload(string fctlid,string com, string disc) {
            panel24.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttonload();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonRenewClicked += new EventHandler(renewattch_Click);
            panel24.Controls.Add(temp_ctrlbutton);

            panel4.Controls.Clear();
            if (Class == "CAR" || Class == "CSB")
            {
                Insured.fctlid = fctlid;
                Insured.InsureComm = com;
                Insured.InsureDisc = disc;
                Insured.InsureShare1 = Gen.GenShare1;
                Insured.InsureShare2 = Gen.GenShare2;
                Insured.Class = Class;
                Insured.Classfctlid = Classfctlid;
                Insured.fbus = "R";
                Insured.ftype = "P";
                Insured.buttoncontrolback();
                panel4.Controls.Add(Insured);
            }
            if (Class == "MYP" || Class == "PAR")
            {
                Insured2.fctlid = fctlid;
                Insured2.InsureComm = com;
                Insured2.InsureDisc = disc;
                Insured2.Class = Class;
                Insured2.Classfctlid = Classfctlid;
                Insured2.InsureShare = Gen.GenShare1;
                Insured2.fbus = "R";
                Insured2.ftype = "P";
                Insured2.buttoncontrolback();
                panel4.Controls.Add(Insured2);
            }
            if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII" || Class == "GLF")
            {
                InsuredC.fctlid = fctlid;
                InsuredC.InsureComm = com;
                InsuredC.InsureDisc = disc;
                InsuredC.Class = Class;
                InsuredC.Classfctlid = Classfctlid;
                if (Class == "CGL") { InsuredC.InsureShare = Gen.GenShare2; }
                else { InsuredC.InsureShare = Gen.GenShare1; }
                InsuredC.fbus = "R";
                InsuredC.ftype = "P";
                InsuredC.buttoncontrolback();
                panel4.Controls.Add(InsuredC);
            }
            if (Class == "CPM")
            {
                InsuredD.fctlid = fctlid;
                InsuredD.InsureComm = com;
                InsuredD.InsureDisc = disc;
                InsuredD.Class = Class;
                InsuredD.Classfctlid = Classfctlid;
                InsuredD.InsureShare = Gen.GenShare1;
                InsuredD.fbus = "R";
                InsuredD.ftype = "P";
                InsuredD.buttoncontrolback();
                panel4.Controls.Add(InsuredD);
            }
            if (Class == "PMP" || Class == "CMP")
            {
                InsuredE.fctlid = fctlid;
                InsuredE.InsureComm = com;
                InsuredE.InsureDisc = disc;
                InsuredE.Class = Class;
                InsuredE.Classfctlid = Classfctlid;
                InsuredE.fbus = "R";
                InsuredE.ftype = "P";
                InsuredE.buttoncontrolback();
                panel4.Controls.Add(InsuredE); 
            }
              if (Class == "EEC")
              {
                  InsuredB.fctlid = fctlid;
                  InsuredB.InsureComm = com;
                  InsuredB.InsureDisc = disc;
                  InsuredB.Class = Class;
                  InsuredB.Classfctlid = Classfctlid;
                  InsuredB.InsureShare = Gen.GenShare1;
                  InsuredB.fsclass = Gen.GenSubClass;
                  InsuredB.fbus = "R";
                  InsuredB.ftype = "P";
                  InsuredB.buttoncontrolback();
                  panel4.Controls.Add(InsuredB);
              }
        }

        void tabPage4reload(string fctlid)
        {
            panel13.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttonload();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonRenewClicked += new EventHandler(renewattch_Click);
            panel13.Controls.Add(temp_ctrlbutton);

            panel11.Controls.Clear();
            Excess.fctlid = fctlid;
            if (Class == "CAR" || Class == "PMP" || Class == "CMP")
            {
                Excess.panel12.Visible = true;
            }
            Excess.Class = Class;
            Excess.Classfctlid = Classfctlid;
            Excess.fbus = "R";
            Excess.ftype = "P";
            Excess.buttoncontrolback();
            panel11.Controls.Add(Excess);
        }

        void tabPage5reload(string fctlid)
        {
            panel26.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttonload();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonRenewClicked += new EventHandler(renewattch_Click);
            panel26.Controls.Add(temp_ctrlbutton);

            panel12.Controls.Clear();
            Attach.fctlid = fctlid;
            Attach.Class = Class;
            Attach.Classfctlid = Classfctlid;
            Attach.fbus = "R";
            Attach.ftype = "P";
            Attach.buttoncontrolback();
            panel12.Controls.Add(Attach);
        }

        void tabPage6reload(string fctlid)
        {
            panel27.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttonload();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonRenewClicked += new EventHandler(renewattch_Click);
            panel27.Controls.Add(temp_ctrlbutton);

            panel2.Controls.Clear();
            Install.fctlid = fctlid;
            Install.Class = Class;
            Install.Classfctlid = Classfctlid;
            Install.fbus = "R";
            Install.ftype = "P";
            Install.InstallTotComm = Gen.GenTotComm;
            Install.InstallTotDisc = Gen.GenTotDisc;
            Install.InstallGP = Gen.GenGP;
            Install.InstallNP = Gen.GenNP;
            Install.InstallComm = Gen.GenComm;
            Install.InstallDisc = Gen.GenDisc;
            Install.InstallEffrDate = Gen.GenEffrDate;
            Install.InstallEftoDate = Gen.GenEftoDate;
            Install.InstallIsDate = Gen.GenIsDate;
            Gen.installTotal = Install.installTotal;
            Install.buttoncontrolback();
            panel2.Controls.Add(Install);
            
        }

        private void Policylist_SelectionChanged(object sender, EventArgs e) {
            Policylistrow = Policylist.CurrentCell.RowIndex;
            if (Policylist.Rows[Policylistrow].Cells["Policy#"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["Policy#"].Value.ToString().Length != 0)
                { 
                    policyNo = Policylist.Rows[Policylistrow].Cells["Policy#"].Value.ToString(); 
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["fctlid"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["fctlid"].Value.ToString().Length != 0)
                {
                    fctlid = Policylist.Rows[Policylistrow].Cells["fctlid"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["Status"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["Status"].Value.ToString().Length != 0)
                {
                    fconfirm = Policylist.Rows[Policylistrow].Cells["Status"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["fcom"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["fcom"].Value.ToString().Length != 0)
                {
                    com = Policylist.Rows[Policylistrow].Cells["fcom"].Value.ToString();
                }
            }
            if (Policylist.Rows[Policylistrow].Cells["ftd"].Value != null)
            {
                if (Policylist.Rows[Policylistrow].Cells["ftd"].Value.ToString().Length != 0)
                {
                    disc = Policylist.Rows[Policylistrow].Cells["ftd"].Value.ToString();
                }
            }
            tabpage1reload();
        }

        void exit() { this.Close();}
        void print() { }
        void confirm() { }

        void del()
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the Policy All information?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string deldatestr =
                    "delete from polh where fctlid='" + fctlid + "'";
                string deldatestr1 = "";
                if (Class == "CAR" || Class == "CSB" || Class == "MYP" || Class == "PAR")
                {
                    deldatestr1 = "delete from oinsint where fctlid_p='" + fctlid + "'";
                }
                if (Class == "EEC")
                {
                    deldatestr1 = "delete from oinsint_b where fctlid_p='" + fctlid + "'";
                }
                if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII" || Class == "GLF")
                {
                    deldatestr1 = "delete from oinsint_c where fctlid_p='" + fctlid + "'";
                }
                if (Class == "CPM")
                {
                    deldatestr1 = "delete from oinsint_d where fctlid_p='" + fctlid + "'";
                }
                if (Class == "PMP" || Class == "CMP")
                {
                    deldatestr1 = "delete from oinsint_e where fctlid_p='" + fctlid + "'";
                }
                string deldatestr2 =
                    "delete from oinsex where fctlid_p='" + fctlid + "'";
                string deldatestr3 =
                    "delete from oinsatt where fctlid_p='" + fctlid + "'";
                string deldatestr4 =
                     "delete from oinvdet where fctlid_1='" + fctlid + "'";
                string deldatestr5 = "", deldatestr6 = "";
                if (Class == "CGL" || Class == "FGP" || Class == "GLF" || Class == "MYP" || Class == "PAR")
                {
                    deldatestr5 = "delete from oinsloc where fctlid_p ='" + fctlid + "'";
                }
                if (Class == "FGP")
                {
                    deldatestr6 = "delete from oinsper where fctlid_p ='" + fctlid + "'";
                }
                string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();

                    SqlCommand command = connection.CreateCommand();
                    SqlTransaction transaction;

                    // Start a local transaction.
                    transaction = connection.BeginTransaction("SampleTransaction");

                    // Must assign both transaction object and connection 
                    // to Command object for a pending local transaction
                    command.Connection = connection;
                    command.Transaction = transaction;

                    try
                    {
                        command.CommandText = deldatestr;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr1;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr2;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr3;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr4;
                        command.ExecuteNonQuery();
                        if (deldatestr5 != "")
                        {
                            command.CommandText = deldatestr5;
                            command.ExecuteNonQuery();
                        }
                        if (deldatestr6 != "")
                        {
                            command.CommandText = deldatestr6;
                            command.ExecuteNonQuery();
                        }
                        transaction.Commit();
                        MessageBox.Show("Have Been Deleted", "Warning",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        FillData("");
                        tabControl1.SelectedTab = tabPage1;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("Haven't Been Deleted", "Warning",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);

                        Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                        Console.WriteLine("  Message: {0}", ex.Message);

                        // Attempt to roll back the transaction. 
                        try
                        {
                            transaction.Rollback();
                        }
                        catch (Exception ex2)
                        {
                            // This catch block will handle any errors that may have occurred 
                            // on the server that would cause the rollback to fail, such as 
                            // a closed connection.
                            Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                            Console.WriteLine("  Message: {0}", ex2.Message);
                        }
                    }
                }
            }
        }

       public void buttoncontrolback()
        {
            Addflag = false;
            updateflag = false;
        }

       public void buttoncontroladd()
        {
            Addflag = true;
            tabControl1.SelectedTab = tabPage2;
            tabpage2add();
            tabpage3add();
            tabpage4add();
            tabpage5add();
            tabpage6add();
        }

       public void buttoncontrolupdate()
       {
           updateflag = true;
           tabControl1.SelectedTab = tabPage2;
           tabpage2update();
           tabpage3update("update");
           tabpage4update("update");
           tabpage5update("update");
           tabpage6update("update");
       }

       public void buttoncontrolrenew()
       {
           Addflag = true;
           tabControl1.SelectedTab = tabPage2;
           tabpage2renew();
           tabpage3update("renew");
           tabpage4update("renew");
           tabpage5update("renew");
           tabpage6update("renew");
       }

        void tabpage2add()
       {
           panel22.Controls.Clear();
           DirectButton temp_ctrlbutton = new DirectButton();
           temp_ctrlbutton.fconfirm = fconfirm;
           temp_ctrlbutton.buttoncontroladd();
           temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
           temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
           temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
           temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
           temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
           temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
           temp_ctrlbutton.UserControlButtonRenewClicked += new EventHandler(renewattch_Click);
           panel22.Controls.Add(temp_ctrlbutton);

           panel1.Controls.Clear();
           Gen.fctlid = fctlid;
           Gen.Class = Class;
           Gen.Classfctlid = Classfctlid;
           Gen.fbus = "R";
           Gen.ftype = "P";
           Gen.buttoncontroladd();
           panel1.Controls.Add(Gen);
        }

        void tabpage3add()
        {
            panel24.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontroladd ();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonRenewClicked += new EventHandler(renewattch_Click);
            panel24.Controls.Add(temp_ctrlbutton);

            panel4.Controls.Clear();
            if (Class == "CAR" || Class == "CSB")
            {
                Insured.fctlid = fctlid;
                Insured.Class = Class;
                Insured.Classfctlid = Classfctlid;
                Insured.fbus = "R";
                Insured.ftype = "P";
                Insured.buttoncontroladd();
                panel4.Controls.Add(Insured);
            }
            if (Class == "MYP" || Class == "PAR")
            {
                Insured2.fctlid = fctlid;
                Insured2.Class = Class;
                Insured2.Classfctlid = Classfctlid;
                Insured2.fbus = "R";
                Insured2.ftype = "P";
                Insured2.buttoncontroladd();
                panel4.Controls.Add(Insured2);
            }
            if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII" || Class == "GLF")
            {
                InsuredC.fctlid = fctlid;
                InsuredC.Class = Class;
                InsuredC.Classfctlid = Classfctlid;
                InsuredC.fbus = "R";
                InsuredC.ftype = "P";
                InsuredC.buttoncontroladd();
                panel4.Controls.Add(InsuredC);
            }
            if (Class == "CPM")
            {
                InsuredD.fctlid = fctlid;
                InsuredD.Class = Class;
                InsuredD.Classfctlid = Classfctlid;
                InsuredD.fbus = "R";
                InsuredD.ftype = "P";
                InsuredD.buttoncontroladd();
                panel4.Controls.Add(InsuredD);
            }
            if (Class == "PMP" || Class == "CMP")
            {
                InsuredE.fctlid = fctlid;
                InsuredE.Class = Class;
                InsuredE.Classfctlid = Classfctlid;
                InsuredE.fbus = "R";
                InsuredE.ftype = "P";
                InsuredE.buttoncontroladd();
                panel4.Controls.Add(InsuredE);
            }
              if (Class == "EEC")
              {
                  InsuredB.fctlid = fctlid;
                  InsuredB.Class = Class;
                  InsuredB.Classfctlid = Classfctlid;
                  InsuredB.fsclass = Gen.GenSubClass;
                  InsuredB.fbus = "R";
                  InsuredB.ftype = "P";
                  InsuredB.buttoncontroladd();
                  panel4.Controls.Add(InsuredB);
              }
        }

        void tabpage4add() {
            panel13.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontroladd ();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonRenewClicked += new EventHandler(renewattch_Click);
            panel13.Controls.Add(temp_ctrlbutton);

            panel11.Controls.Clear();
            Excess.fctlid = fctlid;
            if (Class == "CAR" || Class == "PMP" || Class == "CMP")
            {
                Excess.panel12.Visible = true;
            }
            Excess.Class = Class;
            Excess.Classfctlid = Classfctlid;
            Excess.fbus = "R";
            Excess.ftype = "P";
            Excess.buttoncontroladd();
            panel11.Controls.Add(Excess);
        }

        void tabpage5add() {
            panel26.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontroladd();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonRenewClicked += new EventHandler(renewattch_Click);
            panel26.Controls.Add(temp_ctrlbutton);

            panel12.Controls.Clear();
            Attach.fctlid = fctlid;
            Attach.Class = Class;
            Attach.Classfctlid = Classfctlid;
            Attach.fbus = "R";
            Attach.ftype = "P";
            Attach.buttoncontroladd();
            panel12.Controls.Add(Attach);
        }

        void tabpage6add()
        {
            panel27.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontroladd ();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonRenewClicked += new EventHandler(renewattch_Click);
            panel27.Controls.Add(temp_ctrlbutton);

            panel2.Controls.Clear();
            Install.fctlid = fctlid;
            Install.Class = Class;
            Install.Classfctlid = Classfctlid;
            Install.fbus = "R";
            Install.ftype = "P";
            Gen.installTotal = Install.installTotal;
            Install.buttoncontroladd();
            panel2.Controls.Add(Install);
        }

        void tabpage2update()
        {
            panel22.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontrolupdate();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonRenewClicked += new EventHandler(renewattch_Click);
            panel22.Controls.Add(temp_ctrlbutton);

            panel1.Controls.Clear();
            Gen.fctlid = fctlid;
            Gen.Class = Class;
            Gen.Classfctlid = Classfctlid;
            Gen.fbus = "R";
            Gen.ftype = "P";
            Gen.buttoncontrolupdate();
            panel1.Controls.Add(Gen);
        }

        void tabpage3update(string flag)
        {
            panel24.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontrolupdate ();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonRenewClicked += new EventHandler(renewattch_Click);
            panel24.Controls.Add(temp_ctrlbutton);

            panel4.Controls.Clear();
            if (Class == "CAR" || Class == "CSB")
            {
                Insured.fctlid = fctlid;
                Insured.Class = Class;
                Insured.Classfctlid = Classfctlid;
                Insured.fbus = "R";
                Insured.ftype = "P";
                Insured.buttoncontrolupdate(flag);
                panel4.Controls.Add(Insured);
            }
            if (Class == "MYP" || Class == "PAR")
            {
                Insured2.fctlid = fctlid;
                Insured2.Class = Class;
                Insured2.Classfctlid = Classfctlid;
                Insured2.fbus = "R";
                Insured2.ftype = "P";
                Insured2.buttoncontrolupdate(flag);
                panel4.Controls.Add(Insured2);
            }
            if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII" || Class == "GLF")
            {
                InsuredC.fctlid = fctlid;
                InsuredC.Class = Class;
                InsuredC.Classfctlid = Classfctlid;
                InsuredC.fbus = "R";
                InsuredC.ftype = "P";
                InsuredC.buttoncontrolupdate(flag);
                panel4.Controls.Add(InsuredC);
            }
            if (Class == "CPM")
            {
                InsuredD.fctlid = fctlid;
                InsuredD.Class = Class;
                InsuredD.Classfctlid = Classfctlid;
                InsuredD.fbus = "R";
                InsuredD.ftype = "P";
                InsuredD.buttoncontrolupdate(flag);
                panel4.Controls.Add(InsuredD);
            }
            if (Class == "PMP" || Class == "CMP")
            {
                InsuredE.fctlid = fctlid;
                InsuredE.Class = Class;
                InsuredE.Classfctlid = Classfctlid;
                InsuredE.fbus = "R";
                InsuredE.ftype = "P";
                InsuredE.buttoncontrolupdate(flag);
                panel4.Controls.Add(InsuredE);
            }
            if (Class == "EEC")
            {
                InsuredB.fctlid = fctlid;
                InsuredB.Class = Class;
                InsuredB.Classfctlid = Classfctlid;
                InsuredB.fsclass = Gen.GenSubClass;
                InsuredB.fbus = "R";
                InsuredB.ftype = "P";
                InsuredB.buttoncontrolupdate(flag);
                panel4.Controls.Add(InsuredB);
            }
        }

        void tabpage4update(string flag)
        {
            panel13.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontrolupdate();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonRenewClicked += new EventHandler(renewattch_Click);
            panel13.Controls.Add(temp_ctrlbutton);

            panel11.Controls.Clear();
            Excess.fctlid = fctlid;
            if (Class == "CAR" || Class == "PMP" || Class == "CMP")
            {
                Excess.panel12.Visible = true;
            }
            Excess.Class = Class;
            Excess.Classfctlid = Classfctlid;
            Excess.fbus = "R";
            Excess.ftype = "P";
            Excess.buttoncontrolupdate(flag);
            panel11.Controls.Add(Excess);
        }

        void tabpage5update(string flag)
        {
            panel26.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontrolupdate ();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonRenewClicked += new EventHandler(renewattch_Click);
            panel26.Controls.Add(temp_ctrlbutton);

            panel12.Controls.Clear();
            Attach.fctlid = fctlid;
            Attach.Class = Class;
            Attach.Classfctlid = Classfctlid;
            Attach.fbus = "R";
            Attach.ftype = "P";
            Attach.buttoncontrolupdate(flag);
            panel12.Controls.Add(Attach);
        }

        void tabpage6update(string flag)
        {
            panel27.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontrolupdate();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonRenewClicked += new EventHandler(renewattch_Click);
            panel27.Controls.Add(temp_ctrlbutton);

            panel2.Controls.Clear();
            Install.fctlid = fctlid;
            Install.Class = Class;
            Install.Classfctlid = Classfctlid;
            Gen.installTotal = Install.installTotal;
            Install.fbus = "R";
            Install.ftype = "P";
            Install.buttoncontrolupdate(flag);
            panel2.Controls.Add(Install);
           
        }

        void tabpage2renew()
        {
            panel22.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = fconfirm;
            temp_ctrlbutton.buttoncontrolupdate();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonRenewClicked += new EventHandler(renewattch_Click);
            panel22.Controls.Add(temp_ctrlbutton);

            panel1.Controls.Clear();
            Gen.fctlid = fctlid;
            Gen.Class = Class;
            Gen.Classfctlid = Classfctlid;
            Install.fbus = "R";
            Install.ftype = "P";
            Gen.buttoncontrolrenew();
            panel1.Controls.Add(Gen);
        }

        private void renewattch_Click(object sender, EventArgs e)
        {
            buttoncontrolrenew();
        }


        private void modifyattch_Click(object sender, EventArgs e)
        {
            buttoncontrolupdate();
        }

        private void addattch_Click(object sender, EventArgs e)
        {
            buttoncontroladd();
        }

        private void cancelattch_Click(object sender, EventArgs e)
        {
                buttoncontrolback();
                tabpage1reload();
        }

        private void exitattch_Click(object sender, EventArgs e)
        {
            exit();
        }

        private void delattch_Click(object sender, EventArgs e)
        {
            del();
        }

        private void saveattch_Click(object sender, EventArgs e)
        {
            string result = save();

            if (result == "OK")
            {
                MessageBox.Show("Have Been Inserted!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("Haven't Been Inserted!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
   
        private void tabPage6_MouseClick(object sender, MouseEventArgs e)
        {
            this.tabPage6.Focus();
        }

        private void tabPage6_MouseWheel(object sender, MouseEventArgs e)
        {
            tabPage6.VerticalScroll.Value += 10;
            tabPage6.Refresh();
            tabPage6.Invalidate();
            tabPage6.Update();
        }

        void tabpage1Saveload(string newfctlid, string newcom, string newdisc)
        {
            panel10.Controls.Clear();
            DirectButton temp_ctrlbutton = new DirectButton();
            temp_ctrlbutton.fconfirm = "Pending";
            temp_ctrlbutton.buttonload();
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(addattch_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancelattch_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delattch_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exitattch_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modifyattch_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(saveattch_Click);
            temp_ctrlbutton.UserControlButtonRenewClicked += new EventHandler(renewattch_Click);
            panel10.Controls.Add(temp_ctrlbutton);

            tabPage2reload(newfctlid);
            tabPage3reload(newfctlid, newcom, newdisc);
            tabPage4reload(newfctlid);
            tabPage5reload(newfctlid);
            tabPage6reload(newfctlid);
        }

        public string save()
        {
            string result = "";
            tabControl1.SelectedTab = tabPage2;
            string sql1 = Gen.tabpage2save();
            string newfctlid = Gen.newfctlid;
            string newcom = Gen.newcom;
            string newdisc = Gen.newdisc;
            string Subclass = Gen.GenSubClass;
            string[] sql2;
            if (Class == "CAR" || Class == "CSB")
            {
                sql2 = Insured.tabpage3save(newfctlid, Subclass);
            }
            else if (Class == "MYP" || Class == "PAR")
            {
                sql2 = Insured2.tabpage3save(newfctlid, Subclass);
            }
            else if (Class == "EEC")
            {
                sql2 = InsuredB.tabpage3save(newfctlid, Subclass);
            }
            else if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII")
            {
                sql2 = InsuredC.tabpage3save(newfctlid, Subclass);
            }
            else if (Class == "CPM")
            {
                sql2 = InsuredD.tabpage3save(newfctlid, Subclass);
            }
            else if (Class == "PMP" || Class == "CMP")
            {
                sql2 = InsuredE.tabpage3save(newfctlid, Subclass);
            }
            else { sql2 = null; }

            string[] sql3 = Excess.tabpage4save(newfctlid, Subclass);
            string[] sql4 = Attach.tabpage5save(newfctlid, Subclass);
            string[] sql5 = Install.tabpage6save(newfctlid);

            string updatepolicy = "";
            string[] updateinsure = new string[4];
            string[] updateexcess = new string[2];
            string[] updateattch = new string[2];
            string[] updateinstall = new string[2];

            if (sql1 != "")
            {
                updatepolicy = "update altmdata.dbo.xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='POLID'";
            }

            if (sql2 != null && sql2.Length > 0)
            {
                if (Class == "PAR" || Class == "MYP")
                {
                    updateinsure[1] = "update altmdata.dbo.xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql2[0] + "')), 10) where fidtype ='OINSLOCID'";
                    updateinsure[2] = "update altmdata.dbo.xsysparm set fnxtid='" + sql2[sql2.Length - 1] + "' where fidtype ='OINSINTID'";
                }
                if (Class == "EEC")
                {
                    updateinsure[1] = "update altmdata.dbo.xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql2[0] + "')), 10) where fidtype ='OINSINTIDB'";
                }
                if (Class == "CPM")
                {
                    updateinsure[1] = "update altmdata.dbo.xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql2[0] + "')), 10) where fidtype ='OINSINTIDD'";
                }
                if (Class == "PMP" || Class == "CMP")
                {
                    updateinsure[1] = "update altmdata.dbo.xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql2[0] + "')), 10) where fidtype ='OINSINTIDE'";
                }
                if (Class == "CAR" || Class == "CSB")
                {
                    updateinsure[1] = "update altmdata.dbo.xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql2[0] + "')), 10) where fidtype ='OINSINTID'";
                }
                if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII")
                {
                    if (Class == "CGL" || Class == "FGP")
                    {
                        updateinsure[1] = "update altmdata.dbo.xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql2[0] + "')), 10) where fidtype ='OINSLOCID'";
                        if (Class == "FGP" && sql2.Length > 2)
                        {
                            updateinsure[2] = "update altmdata.dbo.xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql2[2] + "')), 10) where fidtype ='OINSPERID'";
                        }
                    }
                    updateinsure[3] = "update altmdata.dbo.xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='OINSINTIDC'";
                }
            }
            if (sql3 != null && sql3.Length > 0)
            {
                updateexcess[1] = "update altmdata.dbo.xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql3[0] + "')), 10) where fidtype ='OINSEXID'";
            }
            if (sql4 != null && sql4.Length > 0)
            {
                updateattch[1] = "update altmdata.dbo.xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql4[0] + "')), 10) where fidtype ='OINSATTID'";
            }
            if (sql5 != null && sql5.Length > 0)
            {
                updateinstall[1] = "update altmdata.dbo.xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+'" + sql5[0] + "')), 10) where fidtype ='OINVDET'";
            }

            if (sql5.Length > 0)
            {
                if (sql5[0] == "NO")
                {
                    MessageBox.Show("Installment total dose not match with Invoice total!", "Warning",
                               MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
                    result = ExecuteSqlTransaction(connectionString, sql1, sql2, sql3, sql4, sql5, updatepolicy, updateinsure, updateexcess, updateattch, updateinstall);
                    if (result == "OK")
                    {
                        buttoncontrolback();
                        tabpage1Saveload(newfctlid, newcom, newdisc);
                        FillData("");
                    }
                    else
                    {
                        MessageBox.Show(result, "Warning",
                           MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            return result;
        }

        private string ExecuteSqlTransaction(string connectionString, string sql1, string[] sql2, string[] sql3, string[] sql4, string[] sql5, string updatepolicy, string[] updateinsure, string[] updateexcess, string[] updateattch, string[] updateinstall)
        {
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                SqlCommand command = connection.CreateCommand();
                SqlTransaction transaction;

                // Start a local transaction.
                transaction = connection.BeginTransaction("SampleTransaction");

                // Must assign both transaction object and connection 
                // to Command object for a pending local transaction
                command.Connection = connection;
                command.Transaction = transaction;

                try
                {
                    command.CommandText = sql1;
                    command.ExecuteNonQuery();
                    command.CommandText = updatepolicy;
                    command.ExecuteNonQuery();
                    if (sql2 != null)
                    {
                        for (int i = 1; i < sql2.Length; i++)
                        {
                            if (sql2[i] != "" && sql2[i] != null && sql2[i].Length != 10)
                            {
                                command.CommandText = sql2[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (updateinsure != null)
                    {
                        for (int i = 1; i < updateinsure.Length; i++)
                        {
                            if (updateinsure[i] != "" && updateinsure[i] != null)
                            {
                                command.CommandText = updateinsure[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (sql3 != null)
                    {
                        for (int i = 1; i < sql3.Length; i++)
                        {
                            if (sql3[i] != "" && sql3[i] != null)
                            {
                                command.CommandText = sql3[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (updateexcess != null)
                    {
                        for (int i = 1; i < updateexcess.Length; i++)
                        {
                            if (updateexcess[i] != "" && updateexcess[i] != null)
                            {
                                command.CommandText = updateexcess[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (sql4 != null)
                    {
                        for (int i = 1; i < sql4.Length; i++)
                        {
                            if (sql4[i] != "" && sql4[i] != null)
                            {
                                command.CommandText = sql4[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (updateattch != null)
                    {
                        for (int i = 1; i < updateattch.Length; i++)
                        {
                            if (updateattch[i] != "" && updateattch[i] != null)
                            {
                                command.CommandText = updateattch[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (sql5 != null)
                    {
                        for (int i = 1; i < sql5.Length; i++)
                        {
                            if (sql5[i] != "" && sql5[i] != null)
                            {
                                command.CommandText = sql5[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (updateinstall != null)
                    {
                        for (int i = 1; i < updateinstall.Length; i++)
                        {
                            if (updateinstall[i] != "" && updateinstall[i] != null)
                            {
                                command.CommandText = updateinstall[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    // Attempt to commit the transaction.
                    transaction.Commit();
                    return "OK";
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                    Console.WriteLine("  Message: {0}", ex.Message);

                    // Attempt to roll back the transaction. 
                    try
                    {
                        transaction.Rollback();
                        return "RollBack";
                    }
                    catch (Exception ex2)
                    {
                        // This catch block will handle any errors that may have occurred 
                        // on the server that would cause the rollback to fail, such as 
                        // a closed connection.
                        Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                        Console.WriteLine("  Message: {0}", ex2.Message);
                        return ex2.Message;
                    }
                }
            }
        }

    }
}
