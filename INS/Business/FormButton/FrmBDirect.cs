using INS.Business;
using INS.Claim;
using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business
{
    public partial class FrmBDirect : Form
    {
        DBConnect operate = new DBConnect();
        public FrmBDirect()
        {
            InitializeComponent();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            progressBar1.Visible = true;
            progressBar1.Maximum = 50;//设置最大长度值 
            progressBar1.Value = 0;//设置当前值 
            progressBar1.Step = 10;//设置没次增长多少 
            for (int i = 0; i < 5; i++)//循环 
            {
                System.Threading.Thread.Sleep(100);//暂停1秒 
                progressBar1.Value += progressBar1.Step;
            }
            Button btn = (Button)sender;
            string name = btn.Name;
            SetValue(name);
            DirectPolicy temp_form = new DirectPolicy();
            temp_form.Fbus = "D";
            temp_form.Text = "Direct Policy - "+InsEnvironment.Minsc.GetFdesc()+"";
            temp_form.FillData("");
            temp_form.ShowDialog();
            progressBar1.Visible = false;
        }

        void SetValue(string fid)
        {
            String sql = "select * from minsc where fid='" + fid + "'";
            DataTable dt = operate.GetTable(sql);
            if (dt.Rows.Count > 0)
            {
                InsEnvironment.Minsc.SetFctlid(dt.Rows[0]["fctlid"].ToString().Trim());
                InsEnvironment.Minsc.SetFid(dt.Rows[0]["fid"].ToString().Trim());
                InsEnvironment.Minsc.SetFacclass(dt.Rows[0]["facclass"].ToString().Trim());
                InsEnvironment.Minsc.SetFdesc(dt.Rows[0]["fdesc"].ToString().Trim());
                InsEnvironment.Minsc.SetFcdesc(dt.Rows[0]["fcdesc"].ToString().Trim());
            }

        }

        private void covernote_Click(object sender, EventArgs e)
        {
            progressBar1.Visible = true;
            progressBar1.Maximum = 50;//设置最大长度值 
            progressBar1.Value = 0;//设置当前值 
            progressBar1.Step = 10;//设置没次增长多少 
            for (int i = 0; i < 5; i++)//循环 
            {
                System.Threading.Thread.Sleep(100);//暂停1秒 
                progressBar1.Value += progressBar1.Step;
            }
            frmCoverNote temp_form = new frmCoverNote();
            temp_form.FillData("","");
            temp_form.ShowDialog();
            progressBar1.Visible = false;
        }
    }
}
