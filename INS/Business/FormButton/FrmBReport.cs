using INS.Business.Report;
using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business
{
    public partial class FrmBReport : Form
    {
        public FrmBReport()
        {
            InitializeComponent();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            string name = btn.Name;
            PremiumI temp_form = new PremiumI("prmreg",btn.Text);
            temp_form.ShowDialog();
        }

        private void button8_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            string name = btn.Name;
            PremiumI temp_form = new PremiumI("daqcost", btn.Text);
            temp_form.ShowDialog();
        }

        private void button3_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            string name = btn.Name;
            PremiumI temp_form = new PremiumI("eecanly", btn.Text);
            temp_form.ShowDialog();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            string name = btn.Name;
            PremiumI temp_form = new PremiumI("prmdrcr", btn.Text);
            temp_form.ShowDialog();
        }

        private void button5_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            string name = btn.Name;
            PremiumI temp_form = new PremiumI("prmuniv", btn.Text);
            temp_form.ShowDialog();
        }

        private void button7_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            string name = btn.Name;
            PremiumI temp_form = new PremiumI("prmgrnd", btn.Text);
            temp_form.ShowDialog();
        }

        private void button15_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            string name = btn.Name;
            PremiumI temp_form = new PremiumI("prmunern", btn.Text);
            temp_form.ShowDialog();
        }

        private void button9_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            string name = btn.Name;
            PremiumI temp_form = new PremiumI("prmexpl", btn.Text);
            temp_form.ShowDialog();
        }

        private void button6_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            string name = btn.Name;
            PremiumI temp_form = new PremiumI("polprmsm", btn.Text);
            temp_form.ShowDialog();
        }

        private void button4_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            string name = btn.Name;
            PremiumI temp_form = new PremiumI("prmrel", btn.Text);
            temp_form.ShowDialog();
        }

        private void button14_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            string name = btn.Name;
            PremiumI temp_form = new PremiumI("effpmp", btn.Text);
            temp_form.ShowDialog();
        }

        private void button13_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            string name = btn.Name;
            PremiumI temp_form = new PremiumI("prmstat", btn.Text);
            temp_form.ShowDialog();
        }

        private void button10_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            string name = btn.Name;
            PremiumI temp_form = new PremiumI("prmfacout", btn.Text);
            temp_form.ShowDialog();
        }

        private void button11_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            string name = btn.Name;
            PremiumI temp_form = new PremiumI("prmtty", btn.Text);
            temp_form.ShowDialog();
        }

        private void button12_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            string name = btn.Name;
            PremiumI temp_form = new PremiumI("prmbord", btn.Text);
            temp_form.ShowDialog();
        }

        private void button16_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            string name = btn.Name;
            PremiumI temp_form = new PremiumI("prmanly", btn.Text);
            temp_form.ShowDialog();
        }

        private void button17_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            string name = btn.Name;
            PremiumI temp_form = new PremiumI("polprm", btn.Text);
            temp_form.ShowDialog();
        }

        private void button18_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            string name = btn.Name;
            PremiumI temp_form = new PremiumI("plvyia", btn.Text);
            temp_form.ShowDialog();
        }

        private void Upload_Click(object sender, EventArgs e)
        {
            string sql = "select * from [live_ilodata].[dbo].[reportrecord] where ID in (SELECT max(ID) FROM[live_ilodata].[dbo].[reportrecord] where flowid ='' and status ='1' group by reportname)  and reportdate > GETDATE()";
            DataTable dt = DBHelper.GetDataSet(sql);
            if (dt.Rows.Count > 0)
            {
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    ToA8.ToA8process("Business", "20230630", dt.Rows[i]["filename"].ToString(), dt.Rows[i]["ID"].ToString());
                }
            }

        }
    }
}
