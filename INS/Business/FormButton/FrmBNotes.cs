using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business
{
    public partial class FrmBNotes : Form
    {
        public FrmBNotes()
        {
            InitializeComponent();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            DrCrNote tempform = new DrCrNote(this);
            tempform.fbus = "D";
            tempform.requery = true;
            tempform.FillData("", "","D");
            tempform.ShowDialog();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            DrCrNote tempform = new DrCrNote(this);
            tempform.fbus = "K";
            tempform.requery = true;
            tempform.FillData("", "", "K");
            tempform.ShowDialog();
        }

        private void button3_Click(object sender, EventArgs e)
        {
            DrCrNoteFacIn tempform = new DrCrNoteFacIn(this);
            tempform.fbus = "R";
            tempform.requery = true;
            tempform.FillData("", "", "R");
            tempform.ShowDialog();
        }

        private void button4_Click(object sender, EventArgs e)
        {
            DrCrNoteFacOut tempform = new DrCrNoteFacOut(this);
            tempform.requery = true;
            tempform.fbus = "D";
            tempform.FillData("", "", "FacOut");
            tempform.ShowDialog();
        }
    }
}
