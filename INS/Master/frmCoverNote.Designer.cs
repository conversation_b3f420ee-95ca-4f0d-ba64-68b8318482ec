namespace INS
{
    partial class frmCoverNote
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.Detail = new System.Windows.Forms.TabPage();
            this.renew_d = new System.Windows.Forms.Button();
            this.progressBar1 = new System.Windows.Forms.ProgressBar();
            this.label27 = new System.Windows.Forms.Label();
            this.client = new System.Windows.Forms.Button();
            this.fclnt = new System.Windows.Forms.TextBox();
            this.label26 = new System.Windows.Forms.Label();
            this.Days = new Shorty.Windows.Forms.NumericTextBox();
            this.DaysEng = new System.Windows.Forms.TextBox();
            this.label25 = new System.Windows.Forms.Label();
            this.fconfirm = new System.Windows.Forms.ComboBox();
            this.lfconfirm = new System.Windows.Forms.Label();
            this.Special = new System.Windows.Forms.TextBox();
            this.label24 = new System.Windows.Forms.Label();
            this.com_d = new System.Windows.Forms.Button();
            this.cancel_d = new System.Windows.Forms.Button();
            this.push_d = new System.Windows.Forms.Button();
            this.add_d = new System.Windows.Forms.Button();
            this.upd_d = new System.Windows.Forms.Button();
            this.save_d = new System.Windows.Forms.Button();
            this.del_d = new System.Windows.Forms.Button();
            this.print_d = new System.Windows.Forms.Button();
            this.exit_d = new System.Windows.Forms.Button();
            this.label15 = new System.Windows.Forms.Label();
            this.UnamedExcess = new System.Windows.Forms.TextBox();
            this.label19 = new System.Windows.Forms.Label();
            this.LossExcess = new System.Windows.Forms.TextBox();
            this.label20 = new System.Windows.Forms.Label();
            this.InexExcess = new System.Windows.Forms.TextBox();
            this.label21 = new System.Windows.Forms.Label();
            this.ThirdExcess = new System.Windows.Forms.TextBox();
            this.label22 = new System.Windows.Forms.Label();
            this.YoungExcess = new System.Windows.Forms.TextBox();
            this.label23 = new System.Windows.Forms.Label();
            this.GeneralExcess = new System.Windows.Forms.TextBox();
            this.label13 = new System.Windows.Forms.Label();
            this.Year = new System.Windows.Forms.TextBox();
            this.label14 = new System.Windows.Forms.Label();
            this.Seating = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.Body = new System.Windows.Forms.TextBox();
            this.label12 = new System.Windows.Forms.Label();
            this.Weight = new System.Windows.Forms.TextBox();
            this.label18 = new System.Windows.Forms.Label();
            this.Model = new System.Windows.Forms.TextBox();
            this.label17 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.Capacity = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.Cover = new System.Windows.Forms.TextBox();
            this.label9 = new System.Windows.Forms.Label();
            this.RegMark = new System.Windows.Forms.TextBox();
            this.label8 = new System.Windows.Forms.Label();
            this.BeginDate = new System.Windows.Forms.MaskedTextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.EngineNo = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.Address = new System.Windows.Forms.TextBox();
            this.IssueName = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.CoverNo = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.IssueDate = new System.Windows.Forms.MaskedTextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.Value = new Shorty.Windows.Forms.NumericTextBox();
            this.Classes = new System.Windows.Forms.TabPage();
            this.renew_l = new System.Windows.Forms.Button();
            this.com_l = new System.Windows.Forms.Button();
            this.cancel_l = new System.Windows.Forms.Button();
            this.push_l = new System.Windows.Forms.Button();
            this.add_l = new System.Windows.Forms.Button();
            this.upd_l = new System.Windows.Forms.Button();
            this.save_l = new System.Windows.Forms.Button();
            this.del_l = new System.Windows.Forms.Button();
            this.print_l = new System.Windows.Forms.Button();
            this.rowlabel = new System.Windows.Forms.Label();
            this.exit_l = new System.Windows.Forms.Button();
            this.query = new System.Windows.Forms.Button();
            this.KeyWord = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.By = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.dataGridView1 = new System.Windows.Forms.DataGridView();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.Detail.SuspendLayout();
            this.Classes.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
            this.tabControl1.SuspendLayout();
            this.SuspendLayout();
            // 
            // Detail
            // 
            this.Detail.Controls.Add(this.renew_d);
            this.Detail.Controls.Add(this.progressBar1);
            this.Detail.Controls.Add(this.label27);
            this.Detail.Controls.Add(this.client);
            this.Detail.Controls.Add(this.fclnt);
            this.Detail.Controls.Add(this.label26);
            this.Detail.Controls.Add(this.Days);
            this.Detail.Controls.Add(this.DaysEng);
            this.Detail.Controls.Add(this.label25);
            this.Detail.Controls.Add(this.fconfirm);
            this.Detail.Controls.Add(this.lfconfirm);
            this.Detail.Controls.Add(this.Special);
            this.Detail.Controls.Add(this.label24);
            this.Detail.Controls.Add(this.com_d);
            this.Detail.Controls.Add(this.cancel_d);
            this.Detail.Controls.Add(this.push_d);
            this.Detail.Controls.Add(this.add_d);
            this.Detail.Controls.Add(this.upd_d);
            this.Detail.Controls.Add(this.save_d);
            this.Detail.Controls.Add(this.del_d);
            this.Detail.Controls.Add(this.print_d);
            this.Detail.Controls.Add(this.exit_d);
            this.Detail.Controls.Add(this.label15);
            this.Detail.Controls.Add(this.UnamedExcess);
            this.Detail.Controls.Add(this.label19);
            this.Detail.Controls.Add(this.LossExcess);
            this.Detail.Controls.Add(this.label20);
            this.Detail.Controls.Add(this.InexExcess);
            this.Detail.Controls.Add(this.label21);
            this.Detail.Controls.Add(this.ThirdExcess);
            this.Detail.Controls.Add(this.label22);
            this.Detail.Controls.Add(this.YoungExcess);
            this.Detail.Controls.Add(this.label23);
            this.Detail.Controls.Add(this.GeneralExcess);
            this.Detail.Controls.Add(this.label13);
            this.Detail.Controls.Add(this.Year);
            this.Detail.Controls.Add(this.label14);
            this.Detail.Controls.Add(this.Seating);
            this.Detail.Controls.Add(this.label11);
            this.Detail.Controls.Add(this.Body);
            this.Detail.Controls.Add(this.label12);
            this.Detail.Controls.Add(this.Weight);
            this.Detail.Controls.Add(this.label18);
            this.Detail.Controls.Add(this.Model);
            this.Detail.Controls.Add(this.label17);
            this.Detail.Controls.Add(this.label16);
            this.Detail.Controls.Add(this.Capacity);
            this.Detail.Controls.Add(this.label10);
            this.Detail.Controls.Add(this.Cover);
            this.Detail.Controls.Add(this.label9);
            this.Detail.Controls.Add(this.RegMark);
            this.Detail.Controls.Add(this.label8);
            this.Detail.Controls.Add(this.BeginDate);
            this.Detail.Controls.Add(this.label7);
            this.Detail.Controls.Add(this.EngineNo);
            this.Detail.Controls.Add(this.label6);
            this.Detail.Controls.Add(this.Address);
            this.Detail.Controls.Add(this.IssueName);
            this.Detail.Controls.Add(this.label5);
            this.Detail.Controls.Add(this.CoverNo);
            this.Detail.Controls.Add(this.label4);
            this.Detail.Controls.Add(this.IssueDate);
            this.Detail.Controls.Add(this.label3);
            this.Detail.Controls.Add(this.Value);
            this.Detail.Location = new System.Drawing.Point(4, 22);
            this.Detail.Name = "Detail";
            this.Detail.Padding = new System.Windows.Forms.Padding(3);
            this.Detail.Size = new System.Drawing.Size(1064, 689);
            this.Detail.TabIndex = 1;
            this.Detail.Text = "Detail";
            this.Detail.UseVisualStyleBackColor = true;
            // 
            // renew_d
            // 
            this.renew_d.Location = new System.Drawing.Point(434, 623);
            this.renew_d.Name = "renew_d";
            this.renew_d.Size = new System.Drawing.Size(75, 21);
            this.renew_d.TabIndex = 1012;
            this.renew_d.TabStop = false;
            this.renew_d.Text = "Copy";
            this.renew_d.UseVisualStyleBackColor = true;
            this.renew_d.Click += new System.EventHandler(this.renew_d_Click);
            // 
            // progressBar1
            // 
            this.progressBar1.Location = new System.Drawing.Point(330, 580);
            this.progressBar1.Name = "progressBar1";
            this.progressBar1.Size = new System.Drawing.Size(448, 23);
            this.progressBar1.TabIndex = 1011;
            this.progressBar1.Visible = false;
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(120, 81);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(33, 12);
            this.label27.TabIndex = 1010;
            this.label27.Text = "Client";
            // 
            // client
            // 
            this.client.CausesValidation = false;
            this.client.Location = new System.Drawing.Point(313, 77);
            this.client.Name = "client";
            this.client.Size = new System.Drawing.Size(23, 21);
            this.client.TabIndex = 1007;
            this.client.TabStop = false;
            this.client.Text = "...";
            this.client.UseVisualStyleBackColor = true;
            this.client.Click += new System.EventHandler(this.client_Click);
            // 
            // fclnt
            // 
            this.fclnt.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.fclnt.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
            this.fclnt.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.fclnt.Location = new System.Drawing.Point(207, 76);
            this.fclnt.MaxLength = 10;
            this.fclnt.Name = "fclnt";
            this.fclnt.ReadOnly = true;
            this.fclnt.Size = new System.Drawing.Size(100, 22);
            this.fclnt.TabIndex = 1008;
            this.fclnt.Validated += new System.EventHandler(this.fclnt_Validated);
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(582, 180);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(66, 12);
            this.label26.TabIndex = 1006;
            this.label26.Text = "Days English";
            // 
            // Days
            // 
            this.Days.AllowNegative = true;
            this.Days.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.Days.Location = new System.Drawing.Point(457, 177);
            this.Days.MaxLength = 10;
            this.Days.Name = "Days";
            this.Days.NumericPrecision = 17;
            this.Days.NumericScaleOnFocus = 0;
            this.Days.NumericScaleOnLostFocus = 0;
            this.Days.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.Days.ReadOnly = true;
            this.Days.Size = new System.Drawing.Size(80, 22);
            this.Days.TabIndex = 1005;
            this.Days.Text = "0";
            this.Days.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.Days.ZeroIsValid = true;
            // 
            // DaysEng
            // 
            this.DaysEng.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.DaysEng.Location = new System.Drawing.Point(674, 177);
            this.DaysEng.MaxLength = 50;
            this.DaysEng.Name = "DaysEng";
            this.DaysEng.ReadOnly = true;
            this.DaysEng.Size = new System.Drawing.Size(184, 22);
            this.DaysEng.TabIndex = 1004;
            this.DaysEng.Text = "thirtieth (30)";
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(411, 180);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(28, 12);
            this.label25.TabIndex = 1003;
            this.label25.Text = "Days";
            // 
            // fconfirm
            // 
            this.fconfirm.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fconfirm.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.fconfirm.Enabled = false;
            this.fconfirm.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.fconfirm.FormattingEnabled = true;
            this.fconfirm.Items.AddRange(new object[] {
            "Pending",
            "Confirmed",
            "Cancelled",
            "Pushed",
            "Signed"});
            this.fconfirm.Location = new System.Drawing.Point(167, 575);
            this.fconfirm.Name = "fconfirm";
            this.fconfirm.Size = new System.Drawing.Size(101, 24);
            this.fconfirm.TabIndex = 1002;
            this.fconfirm.TabStop = false;
            // 
            // lfconfirm
            // 
            this.lfconfirm.AutoSize = true;
            this.lfconfirm.Location = new System.Drawing.Point(117, 580);
            this.lfconfirm.Name = "lfconfirm";
            this.lfconfirm.Size = new System.Drawing.Size(32, 12);
            this.lfconfirm.TabIndex = 1001;
            this.lfconfirm.Text = "Status";
            // 
            // Special
            // 
            this.Special.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.Special.Location = new System.Drawing.Point(369, 422);
            this.Special.MaxLength = 120;
            this.Special.Name = "Special";
            this.Special.ReadOnly = true;
            this.Special.Size = new System.Drawing.Size(476, 22);
            this.Special.TabIndex = 90;
            this.Special.Text = "*of adjusted loss whichever is the greater";
            // 
            // label24
            // 
            this.label24.AutoEllipsis = true;
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(117, 426);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(242, 12);
            this.label24.TabIndex = 89;
            this.label24.Text = "SPECIAL CONDITIONAS AND RESTRICTIONS";
            // 
            // com_d
            // 
            this.com_d.Location = new System.Drawing.Point(501, 623);
            this.com_d.Name = "com_d";
            this.com_d.Size = new System.Drawing.Size(75, 21);
            this.com_d.TabIndex = 88;
            this.com_d.TabStop = false;
            this.com_d.Text = "Confirm";
            this.com_d.UseVisualStyleBackColor = true;
            this.com_d.Visible = false;
            this.com_d.Click += new System.EventHandler(this.com_d_Click);
            // 
            // cancel_d
            // 
            this.cancel_d.CausesValidation = false;
            this.cancel_d.Location = new System.Drawing.Point(663, 623);
            this.cancel_d.Name = "cancel_d";
            this.cancel_d.Size = new System.Drawing.Size(75, 21);
            this.cancel_d.TabIndex = 87;
            this.cancel_d.TabStop = false;
            this.cancel_d.Text = "Cancel";
            this.cancel_d.UseVisualStyleBackColor = true;
            this.cancel_d.Visible = false;
            this.cancel_d.Click += new System.EventHandler(this.cancel_d_Click);
            // 
            // push_d
            // 
            this.push_d.Location = new System.Drawing.Point(362, 623);
            this.push_d.Name = "push_d";
            this.push_d.Size = new System.Drawing.Size(75, 21);
            this.push_d.TabIndex = 86;
            this.push_d.TabStop = false;
            this.push_d.Text = "PushToA8";
            this.push_d.UseVisualStyleBackColor = true;
            this.push_d.Visible = false;
            this.push_d.Click += new System.EventHandler(this.push_d_Click);
            // 
            // add_d
            // 
            this.add_d.Location = new System.Drawing.Point(87, 623);
            this.add_d.Name = "add_d";
            this.add_d.Size = new System.Drawing.Size(75, 21);
            this.add_d.TabIndex = 81;
            this.add_d.TabStop = false;
            this.add_d.Text = "Add";
            this.add_d.UseVisualStyleBackColor = true;
            this.add_d.Click += new System.EventHandler(this.add_d_Click);
            // 
            // upd_d
            // 
            this.upd_d.Location = new System.Drawing.Point(182, 623);
            this.upd_d.Name = "upd_d";
            this.upd_d.Size = new System.Drawing.Size(75, 21);
            this.upd_d.TabIndex = 82;
            this.upd_d.TabStop = false;
            this.upd_d.Text = "Modify";
            this.upd_d.UseVisualStyleBackColor = true;
            this.upd_d.Visible = false;
            this.upd_d.Click += new System.EventHandler(this.upd_d_Click);
            // 
            // save_d
            // 
            this.save_d.CausesValidation = false;
            this.save_d.Location = new System.Drawing.Point(582, 623);
            this.save_d.Name = "save_d";
            this.save_d.Size = new System.Drawing.Size(75, 21);
            this.save_d.TabIndex = 85;
            this.save_d.TabStop = false;
            this.save_d.Text = "Save";
            this.save_d.UseVisualStyleBackColor = true;
            this.save_d.Visible = false;
            this.save_d.Click += new System.EventHandler(this.save_d_Click);
            // 
            // del_d
            // 
            this.del_d.Location = new System.Drawing.Point(273, 623);
            this.del_d.Name = "del_d";
            this.del_d.Size = new System.Drawing.Size(75, 21);
            this.del_d.TabIndex = 83;
            this.del_d.TabStop = false;
            this.del_d.Text = "Delete";
            this.del_d.UseVisualStyleBackColor = true;
            this.del_d.Visible = false;
            this.del_d.Click += new System.EventHandler(this.del_d_Click);
            // 
            // print_d
            // 
            this.print_d.Location = new System.Drawing.Point(745, 623);
            this.print_d.Name = "print_d";
            this.print_d.Size = new System.Drawing.Size(75, 21);
            this.print_d.TabIndex = 84;
            this.print_d.TabStop = false;
            this.print_d.Text = "Print";
            this.print_d.UseVisualStyleBackColor = true;
            this.print_d.Click += new System.EventHandler(this.print_d_Click);
            // 
            // exit_d
            // 
            this.exit_d.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.exit_d.Location = new System.Drawing.Point(826, 623);
            this.exit_d.Name = "exit_d";
            this.exit_d.Size = new System.Drawing.Size(75, 21);
            this.exit_d.TabIndex = 80;
            this.exit_d.Text = "Exit";
            this.exit_d.UseVisualStyleBackColor = true;
            this.exit_d.Click += new System.EventHandler(this.exit_d_Click);
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(537, 542);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(117, 12);
            this.label15.TabIndex = 79;
            this.label15.Text = "Unnamed Driver Excess";
            // 
            // UnamedExcess
            // 
            this.UnamedExcess.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.UnamedExcess.Location = new System.Drawing.Point(683, 539);
            this.UnamedExcess.MaxLength = 120;
            this.UnamedExcess.Name = "UnamedExcess";
            this.UnamedExcess.ReadOnly = true;
            this.UnamedExcess.Size = new System.Drawing.Size(184, 22);
            this.UnamedExcess.TabIndex = 78;
            // 
            // label19
            // 
            this.label19.AutoEllipsis = true;
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(116, 545);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(88, 12);
            this.label19.TabIndex = 77;
            this.label19.Text = "Theft Loss Excess";
            // 
            // LossExcess
            // 
            this.LossExcess.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.LossExcess.Location = new System.Drawing.Point(304, 542);
            this.LossExcess.MaxLength = 120;
            this.LossExcess.Name = "LossExcess";
            this.LossExcess.ReadOnly = true;
            this.LossExcess.Size = new System.Drawing.Size(205, 22);
            this.LossExcess.TabIndex = 76;
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(537, 503);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(138, 12);
            this.label20.TabIndex = 75;
            this.label20.Text = "Inexperienced Driver Excess";
            // 
            // InexExcess
            // 
            this.InexExcess.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.InexExcess.Location = new System.Drawing.Point(683, 500);
            this.InexExcess.MaxLength = 120;
            this.InexExcess.Name = "InexExcess";
            this.InexExcess.ReadOnly = true;
            this.InexExcess.Size = new System.Drawing.Size(184, 22);
            this.InexExcess.TabIndex = 74;
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(116, 506);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(176, 12);
            this.label21.TabIndex = 73;
            this.label21.Text = "Third Party Property Damage Excess";
            // 
            // ThirdExcess
            // 
            this.ThirdExcess.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.ThirdExcess.Location = new System.Drawing.Point(304, 503);
            this.ThirdExcess.MaxLength = 120;
            this.ThirdExcess.Name = "ThirdExcess";
            this.ThirdExcess.ReadOnly = true;
            this.ThirdExcess.Size = new System.Drawing.Size(205, 22);
            this.ThirdExcess.TabIndex = 72;
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(537, 461);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(104, 12);
            this.label22.TabIndex = 71;
            this.label22.Text = "Young Driver Excess";
            // 
            // YoungExcess
            // 
            this.YoungExcess.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.YoungExcess.Location = new System.Drawing.Point(683, 458);
            this.YoungExcess.MaxLength = 120;
            this.YoungExcess.Name = "YoungExcess";
            this.YoungExcess.ReadOnly = true;
            this.YoungExcess.Size = new System.Drawing.Size(184, 22);
            this.YoungExcess.TabIndex = 70;
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(116, 464);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(175, 12);
            this.label23.TabIndex = 69;
            this.label23.Text = "General Excess/Own Damage Excess";
            // 
            // GeneralExcess
            // 
            this.GeneralExcess.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.GeneralExcess.Location = new System.Drawing.Point(304, 461);
            this.GeneralExcess.MaxLength = 120;
            this.GeneralExcess.Name = "GeneralExcess";
            this.GeneralExcess.ReadOnly = true;
            this.GeneralExcess.Size = new System.Drawing.Size(205, 22);
            this.GeneralExcess.TabIndex = 68;
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(538, 385);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(102, 12);
            this.label13.TabIndex = 67;
            this.label13.Text = "Year of Manufacture";
            // 
            // Year
            // 
            this.Year.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.Year.Location = new System.Drawing.Point(674, 382);
            this.Year.MaxLength = 120;
            this.Year.Name = "Year";
            this.Year.ReadOnly = true;
            this.Year.Size = new System.Drawing.Size(184, 22);
            this.Year.TabIndex = 66;
            // 
            // label14
            // 
            this.label14.AutoEllipsis = true;
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(117, 388);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(172, 12);
            this.label14.TabIndex = 65;
            this.label14.Text = "Seating Capacity(Excluding Driver)";
            // 
            // Seating
            // 
            this.Seating.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.Seating.Location = new System.Drawing.Point(305, 385);
            this.Seating.MaxLength = 120;
            this.Seating.Name = "Seating";
            this.Seating.ReadOnly = true;
            this.Seating.Size = new System.Drawing.Size(205, 22);
            this.Seating.TabIndex = 64;
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(538, 346);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(71, 12);
            this.label11.TabIndex = 63;
            this.label11.Text = "Type of Body";
            // 
            // Body
            // 
            this.Body.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.Body.Location = new System.Drawing.Point(674, 343);
            this.Body.MaxLength = 120;
            this.Body.Name = "Body";
            this.Body.ReadOnly = true;
            this.Body.Size = new System.Drawing.Size(184, 22);
            this.Body.TabIndex = 62;
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(117, 349);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(106, 12);
            this.label12.TabIndex = 61;
            this.label12.Text = "Gross Vehicle Weight";
            // 
            // Weight
            // 
            this.Weight.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.Weight.Location = new System.Drawing.Point(305, 346);
            this.Weight.MaxLength = 120;
            this.Weight.Name = "Weight";
            this.Weight.ReadOnly = true;
            this.Weight.Size = new System.Drawing.Size(205, 22);
            this.Weight.TabIndex = 60;
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(538, 304);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(64, 12);
            this.label18.TabIndex = 59;
            this.label18.Text = "Make/Model";
            // 
            // Model
            // 
            this.Model.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.Model.Location = new System.Drawing.Point(674, 301);
            this.Model.MaxLength = 120;
            this.Model.Name = "Model";
            this.Model.ReadOnly = true;
            this.Model.Size = new System.Drawing.Size(184, 22);
            this.Model.TabIndex = 58;
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(117, 307);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(90, 12);
            this.label17.TabIndex = 57;
            this.label17.Text = "Cylinder Capacity";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(120, 143);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(42, 12);
            this.label16.TabIndex = 56;
            this.label16.Text = "Address";
            // 
            // Capacity
            // 
            this.Capacity.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.Capacity.Location = new System.Drawing.Point(305, 304);
            this.Capacity.MaxLength = 120;
            this.Capacity.Name = "Capacity";
            this.Capacity.ReadOnly = true;
            this.Capacity.Size = new System.Drawing.Size(205, 22);
            this.Capacity.TabIndex = 43;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(538, 263);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(124, 12);
            this.label10.TabIndex = 41;
            this.label10.Text = "Insured\'s Estimated Value";
            // 
            // Cover
            // 
            this.Cover.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.Cover.Location = new System.Drawing.Point(674, 221);
            this.Cover.MaxLength = 50;
            this.Cover.Name = "Cover";
            this.Cover.ReadOnly = true;
            this.Cover.Size = new System.Drawing.Size(184, 22);
            this.Cover.TabIndex = 40;
            this.Cover.Text = "Comprehensive";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(538, 228);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(130, 12);
            this.label9.TabIndex = 39;
            this.label9.Text = "Operative Insurance Cover";
            // 
            // RegMark
            // 
            this.RegMark.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.RegMark.Location = new System.Drawing.Point(305, 221);
            this.RegMark.Name = "RegMark";
            this.RegMark.ReadOnly = true;
            this.RegMark.Size = new System.Drawing.Size(205, 22);
            this.RegMark.TabIndex = 38;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(117, 224);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(89, 12);
            this.label8.TabIndex = 37;
            this.label8.Text = "Registration Mark";
            // 
            // BeginDate
            // 
            this.BeginDate.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.BeginDate.Location = new System.Drawing.Point(207, 177);
            this.BeginDate.Mask = "0000.00.00";
            this.BeginDate.Name = "BeginDate";
            this.BeginDate.ReadOnly = true;
            this.BeginDate.Size = new System.Drawing.Size(149, 22);
            this.BeginDate.TabIndex = 36;
            this.BeginDate.Leave += new System.EventHandler(this.BeginDate_Leave);
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(120, 180);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(57, 12);
            this.label7.TabIndex = 35;
            this.label7.Text = "Begin Date";
            // 
            // EngineNo
            // 
            this.EngineNo.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.EngineNo.Location = new System.Drawing.Point(306, 260);
            this.EngineNo.Name = "EngineNo";
            this.EngineNo.ReadOnly = true;
            this.EngineNo.Size = new System.Drawing.Size(204, 22);
            this.EngineNo.TabIndex = 34;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(117, 263);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(116, 12);
            this.label6.TabIndex = 33;
            this.label6.Text = "Engine/Chassis Number";
            // 
            // Address
            // 
            this.Address.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.Address.Location = new System.Drawing.Point(207, 140);
            this.Address.Name = "Address";
            this.Address.ReadOnly = true;
            this.Address.Size = new System.Drawing.Size(441, 22);
            this.Address.TabIndex = 32;
            // 
            // IssueName
            // 
            this.IssueName.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.IssueName.Location = new System.Drawing.Point(207, 106);
            this.IssueName.Name = "IssueName";
            this.IssueName.ReadOnly = true;
            this.IssueName.Size = new System.Drawing.Size(441, 22);
            this.IssueName.TabIndex = 31;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(120, 109);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(71, 12);
            this.label5.TabIndex = 29;
            this.label5.Text = "Name of Issue";
            // 
            // CoverNo
            // 
            this.CoverNo.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.CoverNo.Location = new System.Drawing.Point(505, 41);
            this.CoverNo.Name = "CoverNo";
            this.CoverNo.ReadOnly = true;
            this.CoverNo.Size = new System.Drawing.Size(100, 22);
            this.CoverNo.TabIndex = 28;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(411, 44);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(79, 12);
            this.label4.TabIndex = 27;
            this.label4.Text = "Cover Note No.";
            // 
            // IssueDate
            // 
            this.IssueDate.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.IssueDate.Location = new System.Drawing.Point(207, 41);
            this.IssueDate.Mask = "0000.00.00";
            this.IssueDate.Name = "IssueDate";
            this.IssueDate.ReadOnly = true;
            this.IssueDate.Size = new System.Drawing.Size(100, 22);
            this.IssueDate.TabIndex = 26;
            this.IssueDate.Leave += new System.EventHandler(this.IssueDate_Leave);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(120, 44);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 25;
            this.label3.Text = "Date of Issue";
            // 
            // Value
            // 
            this.Value.AllowNegative = true;
            this.Value.BackColor = System.Drawing.SystemColors.InactiveCaption;
            this.Value.Location = new System.Drawing.Point(674, 260);
            this.Value.MaxLength = 20;
            this.Value.Name = "Value";
            this.Value.NumericPrecision = 17;
            this.Value.NumericScaleOnFocus = 2;
            this.Value.NumericScaleOnLostFocus = 2;
            this.Value.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.Value.ReadOnly = true;
            this.Value.Size = new System.Drawing.Size(184, 22);
            this.Value.TabIndex = 42;
            this.Value.Text = "0";
            this.Value.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.Value.ZeroIsValid = true;
            // 
            // Classes
            // 
            this.Classes.Controls.Add(this.renew_l);
            this.Classes.Controls.Add(this.com_l);
            this.Classes.Controls.Add(this.cancel_l);
            this.Classes.Controls.Add(this.push_l);
            this.Classes.Controls.Add(this.add_l);
            this.Classes.Controls.Add(this.upd_l);
            this.Classes.Controls.Add(this.save_l);
            this.Classes.Controls.Add(this.del_l);
            this.Classes.Controls.Add(this.print_l);
            this.Classes.Controls.Add(this.rowlabel);
            this.Classes.Controls.Add(this.exit_l);
            this.Classes.Controls.Add(this.query);
            this.Classes.Controls.Add(this.KeyWord);
            this.Classes.Controls.Add(this.label2);
            this.Classes.Controls.Add(this.By);
            this.Classes.Controls.Add(this.label1);
            this.Classes.Controls.Add(this.dataGridView1);
            this.Classes.Location = new System.Drawing.Point(4, 22);
            this.Classes.Name = "Classes";
            this.Classes.Padding = new System.Windows.Forms.Padding(3);
            this.Classes.Size = new System.Drawing.Size(1064, 689);
            this.Classes.TabIndex = 0;
            this.Classes.Text = "List";
            this.Classes.UseVisualStyleBackColor = true;
            // 
            // renew_l
            // 
            this.renew_l.Location = new System.Drawing.Point(472, 647);
            this.renew_l.Name = "renew_l";
            this.renew_l.Size = new System.Drawing.Size(75, 21);
            this.renew_l.TabIndex = 90;
            this.renew_l.TabStop = false;
            this.renew_l.Text = "Copy";
            this.renew_l.UseVisualStyleBackColor = true;
            this.renew_l.Visible = false;
            this.renew_l.Click += new System.EventHandler(this.renew_Click);
            // 
            // com_l
            // 
            this.com_l.Location = new System.Drawing.Point(558, 647);
            this.com_l.Name = "com_l";
            this.com_l.Size = new System.Drawing.Size(75, 21);
            this.com_l.TabIndex = 89;
            this.com_l.TabStop = false;
            this.com_l.Text = "Confirm";
            this.com_l.UseVisualStyleBackColor = true;
            this.com_l.Visible = false;
            this.com_l.Click += new System.EventHandler(this.com_l_Click);
            // 
            // cancel_l
            // 
            this.cancel_l.CausesValidation = false;
            this.cancel_l.Location = new System.Drawing.Point(720, 647);
            this.cancel_l.Name = "cancel_l";
            this.cancel_l.Size = new System.Drawing.Size(75, 21);
            this.cancel_l.TabIndex = 22;
            this.cancel_l.TabStop = false;
            this.cancel_l.Text = "Cancel";
            this.cancel_l.UseVisualStyleBackColor = true;
            this.cancel_l.Visible = false;
            this.cancel_l.Click += new System.EventHandler(this.cancel_l_Click);
            // 
            // push_l
            // 
            this.push_l.Location = new System.Drawing.Point(389, 647);
            this.push_l.Name = "push_l";
            this.push_l.Size = new System.Drawing.Size(75, 21);
            this.push_l.TabIndex = 21;
            this.push_l.TabStop = false;
            this.push_l.Text = "PushToA8";
            this.push_l.UseVisualStyleBackColor = true;
            this.push_l.Visible = false;
            this.push_l.Click += new System.EventHandler(this.push_l_Click);
            // 
            // add_l
            // 
            this.add_l.Location = new System.Drawing.Point(114, 647);
            this.add_l.Name = "add_l";
            this.add_l.Size = new System.Drawing.Size(75, 21);
            this.add_l.TabIndex = 16;
            this.add_l.TabStop = false;
            this.add_l.Text = "Add";
            this.add_l.UseVisualStyleBackColor = true;
            this.add_l.Click += new System.EventHandler(this.add_l_Click);
            // 
            // upd_l
            // 
            this.upd_l.Location = new System.Drawing.Point(209, 647);
            this.upd_l.Name = "upd_l";
            this.upd_l.Size = new System.Drawing.Size(75, 21);
            this.upd_l.TabIndex = 17;
            this.upd_l.TabStop = false;
            this.upd_l.Text = "Modify";
            this.upd_l.UseVisualStyleBackColor = true;
            this.upd_l.Visible = false;
            this.upd_l.Click += new System.EventHandler(this.upd_l_Click);
            // 
            // save_l
            // 
            this.save_l.CausesValidation = false;
            this.save_l.Location = new System.Drawing.Point(639, 647);
            this.save_l.Name = "save_l";
            this.save_l.Size = new System.Drawing.Size(75, 21);
            this.save_l.TabIndex = 20;
            this.save_l.TabStop = false;
            this.save_l.Text = "Save";
            this.save_l.UseVisualStyleBackColor = true;
            this.save_l.Visible = false;
            this.save_l.Click += new System.EventHandler(this.save_l_Click);
            // 
            // del_l
            // 
            this.del_l.Location = new System.Drawing.Point(300, 647);
            this.del_l.Name = "del_l";
            this.del_l.Size = new System.Drawing.Size(75, 21);
            this.del_l.TabIndex = 18;
            this.del_l.TabStop = false;
            this.del_l.Text = "Delete";
            this.del_l.UseVisualStyleBackColor = true;
            this.del_l.Visible = false;
            this.del_l.Click += new System.EventHandler(this.del_l_Click);
            // 
            // print_l
            // 
            this.print_l.Location = new System.Drawing.Point(802, 647);
            this.print_l.Name = "print_l";
            this.print_l.Size = new System.Drawing.Size(75, 21);
            this.print_l.TabIndex = 19;
            this.print_l.TabStop = false;
            this.print_l.Text = "Print";
            this.print_l.UseVisualStyleBackColor = true;
            this.print_l.Click += new System.EventHandler(this.print_l_Click);
            // 
            // rowlabel
            // 
            this.rowlabel.AutoSize = true;
            this.rowlabel.Location = new System.Drawing.Point(1005, 276);
            this.rowlabel.Name = "rowlabel";
            this.rowlabel.Size = new System.Drawing.Size(45, 12);
            this.rowlabel.TabIndex = 10;
            this.rowlabel.Text = "rowlabel";
            this.rowlabel.Visible = false;
            // 
            // exit_l
            // 
            this.exit_l.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.exit_l.Location = new System.Drawing.Point(883, 647);
            this.exit_l.Name = "exit_l";
            this.exit_l.Size = new System.Drawing.Size(75, 21);
            this.exit_l.TabIndex = 6;
            this.exit_l.Text = "Exit";
            this.exit_l.UseVisualStyleBackColor = true;
            this.exit_l.Click += new System.EventHandler(this.exit_l_Click);
            // 
            // query
            // 
            this.query.Location = new System.Drawing.Point(694, 45);
            this.query.Name = "query";
            this.query.Size = new System.Drawing.Size(75, 21);
            this.query.TabIndex = 5;
            this.query.Text = "ReQuery";
            this.query.UseVisualStyleBackColor = true;
            this.query.Click += new System.EventHandler(this.query_Click);
            // 
            // KeyWord
            // 
            this.KeyWord.Location = new System.Drawing.Point(427, 44);
            this.KeyWord.Name = "KeyWord";
            this.KeyWord.Size = new System.Drawing.Size(231, 22);
            this.KeyWord.TabIndex = 4;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(397, 47);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(24, 12);
            this.label2.TabIndex = 3;
            this.label2.Text = "Key";
            // 
            // By
            // 
            this.By.FormattingEnabled = true;
            this.By.Location = new System.Drawing.Point(248, 44);
            this.By.Name = "By";
            this.By.Size = new System.Drawing.Size(121, 20);
            this.By.TabIndex = 2;
            this.By.SelectedIndexChanged += new System.EventHandler(this.comboBox1_SelectedIndexChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(194, 47);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(49, 12);
            this.label1.TabIndex = 1;
            this.label1.Text = "Order By";
            // 
            // dataGridView1
            // 
            this.dataGridView1.AllowUserToAddRows = false;
            this.dataGridView1.AllowUserToDeleteRows = false;
            this.dataGridView1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridView1.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.DisplayedCells;
            this.dataGridView1.BackgroundColor = System.Drawing.SystemColors.ButtonFace;
            this.dataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView1.Location = new System.Drawing.Point(38, 98);
            this.dataGridView1.Name = "dataGridView1";
            this.dataGridView1.ReadOnly = true;
            this.dataGridView1.Size = new System.Drawing.Size(961, 492);
            this.dataGridView1.TabIndex = 0;
            this.dataGridView1.CurrentCellChanged += new System.EventHandler(this.dataGridView1_CurrentCellChanged);
            // 
            // tabControl1
            // 
            this.tabControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tabControl1.Appearance = System.Windows.Forms.TabAppearance.FlatButtons;
            this.tabControl1.Controls.Add(this.Classes);
            this.tabControl1.Controls.Add(this.Detail);
            this.tabControl1.ItemSize = new System.Drawing.Size(48, 18);
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(1072, 715);
            this.tabControl1.TabIndex = 0;
            // 
            // frmCoverNote
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.AutoSize = true;
            this.ClientSize = new System.Drawing.Size(1073, 715);
            this.Controls.Add(this.tabControl1);
            this.Name = "frmCoverNote";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "FrmCoverNote";
            this.Detail.ResumeLayout(false);
            this.Detail.PerformLayout();
            this.Classes.ResumeLayout(false);
            this.Classes.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
            this.tabControl1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabPage Detail;
        private System.Windows.Forms.TabPage Classes;
        private System.Windows.Forms.Button query;
        private System.Windows.Forms.TextBox KeyWord;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox By;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.DataGridView dataGridView1;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.Button exit_l;
        private System.Windows.Forms.TextBox Capacity;
        private Shorty.Windows.Forms.NumericTextBox Value;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.TextBox Cover;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.TextBox RegMark;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.MaskedTextBox BeginDate;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.TextBox EngineNo;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.TextBox Address;
        private System.Windows.Forms.TextBox IssueName;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox CoverNo;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.MaskedTextBox IssueDate;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label rowlabel;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.TextBox Model;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.TextBox Year;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.TextBox Seating;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.TextBox Body;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.TextBox Weight;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.TextBox UnamedExcess;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.TextBox LossExcess;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.TextBox InexExcess;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.TextBox ThirdExcess;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.TextBox YoungExcess;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.TextBox GeneralExcess;
        private System.Windows.Forms.Button push_l;
        private System.Windows.Forms.Button add_l;
        private System.Windows.Forms.Button upd_l;
        private System.Windows.Forms.Button save_l;
        private System.Windows.Forms.Button del_l;
        private System.Windows.Forms.Button print_l;
        private System.Windows.Forms.Button cancel_l;
        private System.Windows.Forms.Button cancel_d;
        private System.Windows.Forms.Button push_d;
        private System.Windows.Forms.Button add_d;
        private System.Windows.Forms.Button upd_d;
        private System.Windows.Forms.Button save_d;
        private System.Windows.Forms.Button del_d;
        private System.Windows.Forms.Button print_d;
        private System.Windows.Forms.Button exit_d;
        private System.Windows.Forms.Button com_d;
        private System.Windows.Forms.Button com_l;
        private System.Windows.Forms.TextBox Special;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.ComboBox fconfirm;
        private System.Windows.Forms.Label lfconfirm;
        private System.Windows.Forms.TextBox DaysEng;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.Label label26;
        private Shorty.Windows.Forms.NumericTextBox Days;
        private System.Windows.Forms.Button client;
        private System.Windows.Forms.TextBox fclnt;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.ProgressBar progressBar1;
        private System.Windows.Forms.Button renew_l;
        private System.Windows.Forms.Button renew_d;
    }
}