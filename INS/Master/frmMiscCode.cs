using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using System.Collections;

namespace INS
{
    public partial class frmMiscCode : Form
    {
        public frmMiscCode()
        {
            InitializeComponent();
            InitCombobox();
            FillData("", "");
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            foreach (Control control in panel1.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
        }
        void control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
            {
                SendKeys.Send("{TAB}");
            }
        }
        DES des = new DES();
        DBConnect operate = new DBConnect();
        ExportDBF DBF = new ExportDBF();
        XLS txt = new XLS();
        public string fctlid_a = "", oldrow = "0";
        public String user = InsEnvironment.LoginUser.GetUserCode();
        public int row = 0, counter;
        public Boolean flag = false;
        public Boolean flag0 = false;
        public Boolean ADD = false;
        private DataTable dataSource = new DataTable();
        private bool datagridviewSelectionChanged = true;
        private bool datagridview2SelectionChanged = true;
        void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            try
            {
                if (order != "" && query != "")
                {
                    if (order == "Code#")
                    {
                        a = new SqlDataAdapter("SELECT fid as Code#,fdesc as Desc#,fctlid FROM mparam where fid like '%" + query.ToUpper() + "%'", c);
                    }
                    else
                    {
                        a = new SqlDataAdapter("SELECT fid as Code#,fdesc as Desc#,fctlid FROM mparam where fdesc like '%" + query.ToUpper() + "%'", c);
                    }
                }
                else if (order != "" && query == "")
                {
                    a = new SqlDataAdapter("SELECT fid as Code#,fdesc as Desc#,fctlid FROM mparam order by " + order, c);
                }
                else
                {
                    a = new SqlDataAdapter("select fid as Code#,fdesc as Desc#,fctlid from mparam", c);
                }
                DataTable t = new DataTable();
                a.Fill(t);
                dataGridView1.DataSource = t;
                this.dataGridView1.Columns[2].Visible = false;
            }
            catch { }
            c.Close();
        }


        void reloadgridview2(string fid, string lab)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select fctlid_1, fctldel, fctlid ,fid as SubCode#,fdesc as Desc#,fmapping from msparam where fctlid_1 = '" + fctlid_a + "' ORDER BY fid", c))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                dataGridView2.DataSource = t2;
                if (t2.Rows.Count == 0)
                {
                    textBox4.Text = "";
                    textBox5.Text = "";
                    textBox7.Text = "";
                }

                if (lab == "insert" || lab == "update")
                {
                    if (fid != "") //add update
                    {
                        for (int i = 0; i < t2.Rows.Count; i++)
                        {
                            if (t2.Rows[i]["SubCode#"].ToString().Trim() == fid.Trim())
                            {
                                dataGridView2.CurrentCell = dataGridView2.Rows[i].Cells["SubCode#"];
                                break;
                            }
                        }
                    }
                }
                if (lab == "del")
                {
                    if (dataGridView2.CurrentCell != null)
                    {
                        if (int.Parse(fid) == 0)
                        {
                            dataGridView2.CurrentCell = dataGridView2.Rows[int.Parse(fid)].Cells["SubCode#"];
                        }
                        else { dataGridView2.CurrentCell = dataGridView2.Rows[int.Parse(fid) - 1].Cells["SubCode#"]; }
                    }
                    else
                    {
                        textBox4.Text = "";
                        textBox5.Text = "";
                        textBox7.Text = "";

                    }
                }
                if (lab == "cancel")
                {
                    if (dataGridView2.CurrentCell != null)
                    {
                        dataGridView2.CurrentCell = dataGridView2.Rows[int.Parse(fid)].Cells["SubCode#"];
                        string str = "SELECT fid as Code#,fdesc as Desc#,fctlid FROM mparam where fctlid ='" + fctlid_a + "'";
                        DataTable dt = new DataTable();
                        dt = operate.GetTable(str);
                        if (dt.Rows.Count > 0)
                        {
                            textBox2.Text = dt.Rows[0]["Code#"].ToString();
                            textBox3.Text = dt.Rows[0]["Desc#"].ToString();
                        }
                    }
                }
            }
            this.dataGridView2.Columns[0].Visible = false;
            this.dataGridView2.Columns[1].Visible = false;
            this.dataGridView2.Columns[2].Visible = false;
            datagridview2SelectionChanged = true;
            flag = false;
            c.Close();

        }

        void ButtonControl()
        {
            ADD = false;
            textBox3.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox3.ReadOnly = true;
            textBox2.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox2.ReadOnly = true;
            textBox4.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox4.ReadOnly = true;
            textBox5.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox5.ReadOnly = true;
            textBox7.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox7.ReadOnly = true;
            button7.Visible = false;
            button8.Visible = false;
            button4.Visible = true;
            button5.Visible = false;
            button3.Visible = false;
            button2.Visible = false;
            dadd.Visible = true;
            dupdate.Visible = true;
            ddel.Visible = true;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting2);
            dataGridView2.Enabled = true;
        }

        private void dataGridView1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            if (!this.datagridviewSelectionChanged) return;
            try
            {
                if (flag == false && dataGridView1.CurrentCell != null)
                {
                    counter = dataGridView1.CurrentCell.RowIndex;
                }
                if (dataGridView1.Rows[counter].Cells["Code#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Code#"].Value.ToString().Length != 0)
                    {
                        textBox2.Text = dataGridView1.Rows[counter].Cells["Code#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        textBox3.Text = dataGridView1.Rows[counter].Cells["Desc#"].Value.ToString().Trim();
                    }
                }
                fctlid_a = dataGridView1.Rows[counter].Cells["fctlid"].Value.ToString();
                reloadgridview2("", "");
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void dataGridView2_SelectionChanged(object sender, EventArgs e)
        {
            if (!this.datagridview2SelectionChanged) return;
            try
            {
                if (flag == false && dataGridView2.CurrentCell != null)
                {
                    row = dataGridView2.CurrentCell.RowIndex;
                }

                if (dataGridView2.Rows[row].Cells["SubCode#"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["SubCode#"].Value.ToString().Length != 0)
                    {
                        textBox4.Text = dataGridView2.Rows[row].Cells["SubCode#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView2.Rows[row].Cells["Desc#"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        textBox5.Text = dataGridView2.Rows[row].Cells["Desc#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView2.Rows[row].Cells["fmapping"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["fmapping"].Value.ToString().Length != 0)
                    {
                        textBox7.Text = dataGridView2.Rows[row].Cells["fmapping"].Value.ToString().Trim();
                    }
                }

                if (dataGridView2.Rows[row].Cells["fctlid"].Value != null)
                {
                    fctlidlabel.Text = dataGridView2.Rows[row].Cells["fctlid"].Value.ToString();
                }
                rowlabel.Text = row.ToString();
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void button4_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void button5_Click(object sender, EventArgs e)
        {
            if (dataGridView2.RowCount != 0)
            {
                try
                {
                    dataSource = dataGridView2.DataSource as DataTable;
                    dataSource.Rows.RemoveAt(dataGridView2.CurrentCell.RowIndex);
                    dataGridView2.DataSource = dataSource;
                    dataGridView2.CurrentCell = dataGridView2.Rows[dataSource.Rows.Count - 1].Cells["Name"];
                }
                catch { }
            }
            if (dataGridView2.RowCount == 0)
            {
                textBox4.Text = "";
                textBox4.BackColor = System.Drawing.SystemColors.InactiveCaption;
                textBox4.ReadOnly = true;
                textBox5.Text = "";
                textBox5.BackColor = System.Drawing.SystemColors.InactiveCaption;
                textBox5.ReadOnly = true;
                textBox7.Text = "";
                textBox7.BackColor = System.Drawing.SystemColors.InactiveCaption;
                textBox7.ReadOnly = true;
                button5.Visible = false;
            }
        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            e.Cancel = true;
        }

        private void tabControl1_Selecting2(object sender, TabControlCancelEventArgs e)
        {
            e.Cancel = false;
        }

        private void button7_Click(object sender, EventArgs e)
        {
            ButtonControl();
            reloadgridview2(oldrow, "cancel");
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (textBox2.Text != "" && textBox3.Text != "")
            {
                ADD = true;
                oldrow = rowlabel.Text;
                DataTable listdataSource = dataGridView2.DataSource as DataTable;
                DataRow newCustomersRow = listdataSource.NewRow();
                listdataSource.Rows.InsertAt(newCustomersRow, listdataSource.Rows.Count);
                dataGridView2.DataSource = listdataSource;
                dataGridView2.CurrentCell = dataGridView2.Rows[listdataSource.Rows.Count - 1].Cells["SubCode#"];
                textBox4.BackColor = System.Drawing.SystemColors.Window;
                textBox4.ReadOnly = false;
                textBox4.Text = "";
                textBox5.BackColor = System.Drawing.SystemColors.Window;
                textBox5.ReadOnly = false;
                textBox5.Text = "";
                textBox7.BackColor = System.Drawing.SystemColors.Window;
                textBox7.ReadOnly = false;
                textBox7.Text = "";
                button8.Visible = true;
            }
            else
            {
                MessageBox.Show("Empty value is not allowed!", "Warning",
           MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void button8_Click(object sender, EventArgs e)
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really Change the items?", "Inserted Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                String code = textBox2.Text;
                String desc = textBox3.Text;
                String subcode = textBox4.Text;
                String sdesc = textBox5.Text;
                String mapping = textBox7.Text;
                if (ADD == true)
                {
                    string str = "select * from xsysparm where fidtype ='MISCID'";
                    DataTable dt = new DataTable();
                    dt = operate.GetTable(str);
                    fctlid_a = dt.Rows[0]["fnxtid"].ToString();
                }

                if (code != "" && desc != "")
                {
                    if (ADD == true)
                    {
                        string adddatestr1 = "insert into mparam(fctlid,fid,fdesc,fctldel,finpuser,finpdate,fupduser,fupddate) values" +
                            "('" + fctlid_a + "','" + code.Trim().Replace("'", "''") + "','" + desc.Trim().Replace("'", "''") + "','','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "')";
                        operate.OperateData(adddatestr1);

                        string updatestr1 = "update xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='MISCID'";
                        operate.OperateData(updatestr1);
                    }
                    else
                    {
                        string adddatestr1 = "update mparam set fid ='" + code.Trim().Replace("'", "''") + "',fdesc='" + desc.Trim().Replace("'", "''") + "', fupduser='" + user + "', fupddate= '" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid='" + fctlid_a + "'";
                        operate.OperateData(adddatestr1);
                    }

                    if (saveSubCode() == "yes")
                    {
                        MessageBox.Show("Have Been Done!!", "Warning",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        reloadgridview2(code, "insert");
                        ButtonControl();
                    }
                }
                else
                {
                    MessageBox.Show("Some field is empty!", "Warning",
 MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        public Boolean checknew(string strSql)
        {
            string str =
                    "select count(*) from mparam where fid ='" + strSql.Trim() + "'";
            int i = operate.SelectData(str);
            if (i > 0)
            { return true; }
            else { return false; }
        }

        private void textBox4_TextChanged(object sender, EventArgs e)
        {
            if (dataGridView2.CurrentCell != null)
            {
                dataGridView2.Rows[dataGridView2.CurrentCell.RowIndex].Cells["SubCode#"].Value = textBox4.Text;
                string result2 = CheckDuplicate(textBox4.Text);
                if (result2 != "")
                {
                    MessageBox.Show(result2);
                    textBox4.Focus();
                    textBox4.SelectAll();
                }
            }
        }

        public string CheckDuplicate(string fid)
        {
            if (fid != "")
            {
                DataTable listdataSource = dataGridView2.DataSource as DataTable;
                if (listdataSource.Rows.Count > 0)
                {
                    DataTable objectTable = listdataSource.DefaultView.ToTable();
                    DataRow[] rows = objectTable.Select("[SubCode#]='" + fid.Trim() + "'");
                    if (rows.Length > 1)
                    {
                        return "Duplicate Value";
                    }
                    else { return ""; }
                }
                else { return ""; }
            }
            else { return ""; }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", textBox1.Text.ToString().Trim());
        }

        public enum Mode
        {
            Code = 1,
            Desc = 2
        }

        private void InitCombobox()
        {
            Array arr = System.Enum.GetValues(typeof(Mode));    // 获取枚举的所有值
            DataTable dt = new DataTable();
            dt.Columns.Add("String", Type.GetType("System.String"));
            dt.Columns.Add("Value", typeof(int));
            foreach (var a in arr)
            {
                string strText = EnumTextByDescription.GetEnumDesc((Mode)a);
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = (int)a;

                dt.Rows.Add(aRow);
            }

            comboBox1.DataSource = dt;
            comboBox1.DisplayMember = "String";
            comboBox1.ValueMember = "Value";
        }


        private void button9_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void ladd_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            textBox2.BackColor = System.Drawing.SystemColors.Window;
            textBox2.ReadOnly = false;
            textBox2.Text = "";
            textBox3.BackColor = System.Drawing.SystemColors.Window;
            textBox3.ReadOnly = false;
            textBox3.Text = "";
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select fctlid_1, fctldel, fctlid ,fid as SubCode#,fdesc as Desc#,fmapping from msparam where fctlid_1 = '" + fctlid_a + "' ORDER BY fid", c))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                t2.Clear();
                dataGridView2.DataSource = t2;
            }
            c.Close();
            textBox4.Text = "";
            textBox5.Text = "";
            textBox7.Text = "";
            button7.Visible = true;
            button8.Visible = true;
            button4.Visible = false;
            button5.Visible = false;
            button3.Visible = false;
            button2.Visible = true;
            dadd.Visible = false;
            dupdate.Visible = false;
            ddel.Visible = false;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);
            dataGridView2.Enabled = false;
        }

        private void lupdate_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            oldrow = rowlabel.Text;
            textBox2.BackColor = System.Drawing.SystemColors.Window;
            textBox2.ReadOnly = false;
            textBox3.BackColor = System.Drawing.SystemColors.Window;
            textBox3.ReadOnly = false;
            textBox4.BackColor = System.Drawing.SystemColors.Window;
            textBox4.ReadOnly = false;
            textBox5.BackColor = System.Drawing.SystemColors.Window;
            textBox5.ReadOnly = false;
            textBox7.BackColor = System.Drawing.SystemColors.Window;
            textBox7.ReadOnly = false;
            button7.Visible = true;
            button4.Visible = false;
            button5.Visible = true;
            button3.Visible = false;
            button2.Visible = true;
            button8.Visible = true;
            dadd.Visible = false;
            dupdate.Visible = false;
            ddel.Visible = false;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);
            dataGridView2.Enabled = false;
        }

        private void ldel_Click(object sender, EventArgs e)
        {
            del();
        }

        void del()
        {

            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the ALL items?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string fctlid = fctlidlabel.Text;
                string code = textBox4.Text;
                string deldatestr =
                    "delete from msparam where fctlid='" + fctlid + "'";
                operate.OperateData(deldatestr);
                string deldatestr1 =
                    "delete from mparam where fctlid='" + fctlid_a + "'";
                operate.OperateData(deldatestr1);
                MessageBox.Show("Have Been Deleted", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                FillData("", "");
                tabControl1.SelectedTab = Classes;
            }
        }

        private void dadd_Click(object sender, EventArgs e)
        {
            textBox2.BackColor = System.Drawing.SystemColors.Window;
            textBox2.ReadOnly = false;
            textBox2.Text = "";
            textBox3.BackColor = System.Drawing.SystemColors.Window;
            textBox3.ReadOnly = false;
            textBox3.Text = "";
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select fctlid_1, fctldel, fctlid ,fid as SubCode#,fdesc as Desc#,fmapping from msparam where fctlid_1 = '" + fctlid_a + "' ORDER BY fid", c))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                t2.Clear();
                dataGridView2.DataSource = t2;
            }
            c.Close();
            textBox4.Text = "";
            textBox5.Text = "";
            textBox7.Text = "";
            button7.Visible = true;
            button8.Visible = true;
            button4.Visible = false;
            button5.Visible = false;
            button3.Visible = false;
            button2.Visible = true;
            dadd.Visible = false;
            dupdate.Visible = false;
            ddel.Visible = false;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);
            dataGridView2.Enabled = false;
        }

        private void dupdate_Click(object sender, EventArgs e)
        {
            oldrow = rowlabel.Text;
            textBox2.BackColor = System.Drawing.SystemColors.Window;
            textBox2.ReadOnly = false;
            textBox3.BackColor = System.Drawing.SystemColors.Window;
            textBox3.ReadOnly = false;
            textBox4.BackColor = System.Drawing.SystemColors.Window;
            textBox4.ReadOnly = false;
            textBox5.BackColor = System.Drawing.SystemColors.Window;
            textBox5.ReadOnly = false;
            textBox7.BackColor = System.Drawing.SystemColors.Window;
            textBox7.ReadOnly = false;
            button8.Visible = true;
            button7.Visible = true;
            button4.Visible = false;
            button5.Visible = true;
            button3.Visible = false;
            button2.Visible = true;
            dadd.Visible = false;
            dupdate.Visible = false;
            ddel.Visible = false;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);
            dataGridView2.Enabled = false;
        }

        private void ddel_Click(object sender, EventArgs e)
        {
            del();
        }

        private void textBox5_TextChanged(object sender, EventArgs e)
        {
            if (dataGridView2.CurrentCell != null)
            {
                dataGridView2.Rows[dataGridView2.CurrentCell.RowIndex].Cells["Desc#"].Value = textBox5.Text;
            }
        }

        private void textBox7_TextChanged(object sender, EventArgs e)
        {
            if (dataGridView2.CurrentCell != null)
            {
                dataGridView2.Rows[dataGridView2.CurrentCell.RowIndex].Cells["fmapping"].Value = textBox7.Text;
            }
        }

        public string saveSubCode()
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            string attsql = "select fctlid_1, fctldel, fctlid ,fid as SubCode#,fdesc as Desc#,fmapping from msparam where fctlid_1 = '" + fctlid_a + "' ORDER BY fid";
            DataTable dt0 = operate.GetTable(attsql);
            DataTable dt = dataGridView2.DataSource as DataTable;
            if (textBox4.Text != "")
            {
            if (dt != null)
            {
                string[] updatesql = CompareDt(dt0, dt, "fctlid");
                if (updatesql != null)
                {
                    for (int i = 0; i < updatesql.Length; i++)
                    {
                        if (updatesql[i] != "" && updatesql[i] != null)
                        {
                            operate.OperateData(updatesql[i]);
                        }
                    }
                }
                
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        if (!checknewsub(dt.Rows[i]["fctlid"].ToString().Trim()))
                        {
                            string str = "select * from xsysparm where fidtype ='SMISCID'";
                            DataTable dt1 = new DataTable();
                            dt1 = operate.GetTable(str);
                            string fctlid = dt1.Rows[0]["fnxtid"].ToString();
                            string adddatestr =
                                "insert into msparam(fctlid_1,fctlid,fid,fdesc,fmapping,fctldel) values('" + fctlid_a + "','" + fctlid + "','" + dt.Rows[i]["SubCode#"].ToString().Trim().Replace("'", "''") + "','" + dt.Rows[i]["Desc#"].ToString().Trim().Replace("'", "''") + "','" + dt.Rows[i]["fmapping"].ToString().Trim().Replace("'", "''") + "','')";
                            operate.OperateData(adddatestr);

                            string updatestr = "update xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='SMISCID'";
                            operate.OperateData(updatestr);
                        }
                        else
                        {
                            string updatedatestro =
                                   "update msparam set fid ='" + dt.Rows[i]["SubCode#"].ToString().Trim().Replace("'", "''") + "',fdesc ='" + dt.Rows[i]["Desc#"].ToString().Trim().Replace("'", "''") + "',fmapping ='" + dt.Rows[i]["fmapping"].ToString().Trim().Replace("'", "''") + "' where fctlid='" + dt.Rows[i]["fctlid"].ToString().Trim() + "'";
                            operate.OperateData(updatedatestro);
                        }
                    }
                }
                return "yes";
            }
            else
            {
                MessageBox.Show("Some field is empty!", "Warning",
MessageBoxButtons.OK, MessageBoxIcon.Information);
                return "no";
            }
        }

        public string[] CompareDt(DataTable dt1, DataTable dt2, string keyField)
        {
            //为三个表拷贝表结构
            DataTable dtRetDel = dt1.Clone();
            DataTable dtRetAdd = dt1.Clone();
            DataTable dtRetDif = dt1.Clone();

            ArrayList updsql = new ArrayList();
            int colCount = dt1.Columns.Count;

            DataView dv1 = dt1.DefaultView;
            DataView dv2 = dt2.DefaultView;

            //先以第一个表为参照，看第二个表是修改了还是删除了
            foreach (DataRowView dr1 in dv1)
            {
                dv2.RowFilter = keyField + " = '" + dr1[keyField].ToString() + "'";
                if (dv2.Count > 0)
                {
                    if (!CompareUpdate(dr1, dv2[0]))//比较是否有不同的
                    {
                        dtRetDif.Rows.Add(dv2[0].Row.ItemArray);//修改后
                        continue;
                    }
                }
                else
                {
                    //已经被删除的
                    dtRetDel.Rows.Add(dr1.Row.ItemArray);
                }
            }

            //以第一个表为参照，看记录是否是新增的
            dv2.RowFilter = "";//清空条件
            foreach (DataRowView dr2 in dv2)
            {
                dv1.RowFilter = keyField + " = '" + dr2[keyField].ToString() + "'";
                if (dv1.Count == 0)
                {
                    //新增的
                    dtRetAdd.Rows.Add(dr2.Row.ItemArray);
                }
            }

            if (dtRetDel != null && dtRetDel.Rows.Count > 0)
            {
                for (int i = 0; i < dtRetDel.Rows.Count; i++)
                {
                    string del = "delete from msparam where fctlid='" + dtRetDel.Rows[i]["fctlid"].ToString().Trim() + "'";
                    updsql.Add(del);
                }
            }

            return (string[])updsql.ToArray(typeof(string));
        }

        //比较是否有不同的
        private static bool CompareUpdate(DataRowView dr1, DataRowView dr2)
        {
            //行里只要有一项不一样，整个行就不一样,无需比较其它
            object val1;
            object val2;
            for (int i = 1; i < dr1.Row.ItemArray.Length; i++)
            {
                val1 = dr1[i];
                val2 = dr2[i];
                if (!val1.Equals(val2))
                {
                    return false;
                }
            }
            return true;
        }

        public Boolean checknewsub(string strSql)
        {
            string str =
                    "select count(*) from msparam where fctlid ='" + strSql.Trim() + "' and fctlid_1 ='" + fctlid_a.Trim() + "'";
            int i = operate.SelectData(str);
            if (i > 0)
            { return true; }
            else { return false; }
        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", "");
        }

        private void tabControl1_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (tabControl1.SelectedIndex == 0) { FillData("", ""); }
        }

        private void textBox2_Validated(object sender, EventArgs e)
        {
            if (!CheckDuplicate2(textBox2.Text))
            {
                MessageBox.Show("Duplicate Value!", "Warning",
                         MessageBoxButtons.OK, MessageBoxIcon.Information);
                textBox2.Focus();
                dataGridView1.CurrentCell = dataGridView1.Rows[dataGridView1.CurrentCell.RowIndex].Cells["Code#"];
            }
        }

        public Boolean CheckDuplicate2(string fid)
        {
            DataTable dt = dataGridView1.DataSource as DataTable;
            if (fid == "")
                return false;

            if (dt.Rows.Count > 0)
            {
                DataTable objectTable = dt.DefaultView.ToTable();
                DataRow[] rows = objectTable.Select("[Code#]='" + fid.Trim() + "'");
                if (rows.Length > 1)
                {
                    return false;
                }
            }

            return true;
        }

    }
}

