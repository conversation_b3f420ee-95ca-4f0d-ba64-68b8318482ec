using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;

namespace INS
{
    public partial class frmSubContractor : Form
    {
        public frmSubContractor()
        {
            InitializeComponent();
            foreach (Control control in panel1.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
                if (control is TextBox)
                    control.Enter += new EventHandler(textbox_Enter);                //添加事件
            }
            InitCombobox();
            FillData("","");
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
            {
                SendKeys.Send("{TAB}");
            }
        }

        void textbox_Enter(object sender, EventArgs e)
        {
            Cur_TextBox = (TextBox)sender;
        }

        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string user= InsEnvironment.LoginUser .GetUserCode(), oldrow="";
        private String access_Right = InsEnvironment.LoginUser.GetUserRight07(), input_User;

        private DataGridViewRow current_GridRow = null;

        public int row = 0;
        //public Boolean flag = false;
        public Boolean ADD = false;
        private Boolean Edit_Tran = false;
        private Boolean Skip_Validate = false;
        private TextBox Cur_TextBox = null;
        
        //private bool datagridview2SelectionChanged = true;
        void FillData(string order,string query)
        {
            SqlConnection c = DBConnect.mstdbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            if (order != "" && query != "")
            {
                a = new SqlDataAdapter("SELECT fid as ID, fdesc as Desc#, fdesc_c as Chinese, fctlid,finpuser FROM msite where ftype ='E' and fid like '%" + query.ToUpper() + "%' order by fid", c);
            }
            else {
                a = new SqlDataAdapter("SELECT fid as ID, fdesc as Desc#, fdesc_c as Chinese, fctlid,finpuser FROM msite where ftype ='E' order by fid", c);
               
            }
                DataTable t = new DataTable();
                a.Fill(t);
                dataGridView1.DataSource = t;
                this.dataGridView1.Columns[3].Visible = false;
                this.dataGridView1.Columns[4].Visible = false;
                c.Close();
        }

        private void ComboBox1_SelectedIndexChanged(object sender,System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(),"");
        }

        void reloadgridview2(string fid,string lab)
        {
            SqlConnection c = DBConnect.mstdbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("SELECT fid as ID, fdesc as Desc#, fdesc_c as Chinese, fctlid,finpuser FROM msite where ftype ='E' order by fid", c))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                dataGridView1.DataSource = t2;
                if (t2.Rows.Count == 0)
                {
                    textBox2.Text = "";
                    textBox3.Text = "";
                    textBox4.Text = "";
                }
                if (lab == "insert" || lab == "update")
                {
                    if (fid != "") //add update
                    {
                        for (int i = 0; i < t2.Rows.Count; i++)
                        {
                            if (t2.Rows[i]["ID"].ToString().Trim() == fid.Trim())
                            {
                                dataGridView1.CurrentCell = dataGridView1.Rows[i].Cells["ID"];
                                break;
                            }
                        }
                    }
                }
                if (lab == "del")
                {
                    if (dataGridView1.CurrentCell != null)
                    {
                        if (int.Parse(fid) == 0)
                        {
                            dataGridView1.CurrentCell = dataGridView1.Rows[int.Parse(fid)].Cells["ID"];
                        }
                        else { dataGridView1.CurrentCell = dataGridView1.Rows[int.Parse(fid) - 1].Cells["ID"]; }
                    }
                    else
                    {
                        textBox2.Text = "";
                        textBox3.Text = "";
                        textBox4.Text = "";
                    }
                }
                if (lab == "cancel")
                {
                    if (dataGridView1.CurrentCell != null && int.Parse(fid) != -1)
                    {
                        dataGridView1.CurrentCell = dataGridView1.Rows[int.Parse(fid)].Cells["ID"];
                    }
                }

            }
            this.dataGridView1.Columns[3].Visible = false;
            this.dataGridView1.Columns[4].Visible = false;
            //datagridview2SelectionChanged = true;
            //flag = false;
            c.Close();
        }

        void ButtonControl()
        {
            ADD = false;
            Edit_Tran = false;
            Skip_Validate = false;
            Cur_TextBox = null;
            textBox4.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox4.ReadOnly = true;
            textBox3.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox3.ReadOnly = true;
            textBox2.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox2.ReadOnly = true;

            textBox1.Enabled = true;
            comboBox1.Enabled = true;
            Query.Enabled = true;

            button6.Visible = false;
            button7.Visible = false;
            button8.Visible = false;
            button5.Visible = true;

            get_Access_Right();

            dataGridView1.Enabled = true;
        }

        private void get_Access_Right()
        {
            Boolean ll_add, ll_modify, ll_delete;

            ll_add = access_Right.Substring(2, 1) == "1";
            ll_modify = input_User == null ? false : input_User == user && access_Right.Substring(3, 1) == "1"
                ? true : input_User != user && access_Right.Substring(4, 1) == "1";
            ll_delete = input_User == null ? false : input_User == user && access_Right.Substring(7, 1) == "1"
                ? true : input_User != user && access_Right.Substring(8, 1) == "1";

            ll_modify = ll_modify && row != -1;
            ll_delete = ll_delete && row != -1;

            button4.Visible = ll_delete;    // Delete
            button3.Visible = ll_modify;    // Modify
            button2.Visible = ll_add;    // Add
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {

            if (current_GridRow == dataGridView1.CurrentRow) return;
            try
            {
                current_GridRow = dataGridView1.CurrentRow;
                row = current_GridRow == null ? -1 : dataGridView1.CurrentCell.RowIndex;

                textBox2.Text = row == -1 || dataGridView1.Rows[row].Cells["ID"].Value == null ? "" :
                    dataGridView1.Rows[row].Cells["ID"].Value.ToString().Trim();

                textBox3.Text = row == -1 || dataGridView1.Rows[row].Cells["Desc#"].Value == null ? "" :
                    dataGridView1.Rows[row].Cells["Desc#"].Value.ToString().Trim();

                textBox4.Text = row == -1 || dataGridView1.Rows[row].Cells["Chinese"].Value == null ? "" :
                    dataGridView1.Rows[row].Cells["Chinese"].Value.ToString().Trim();

                label6.Text = row == -1 || dataGridView1.Rows[row].Cells["fctlid"].Value == null ? "" :
                    dataGridView1.Rows[row].Cells["fctlid"].Value.ToString().Trim();
                input_User = row == -1 || dataGridView1.Rows[row].Cells["finpuser"].Value == null ? "" :
                    dataGridView1.Rows[row].Cells["finpuser"].Value.ToString().Trim();
                rowlabel.Text = row.ToString();

                ButtonControl();
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void button5_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void button4_Click(object sender, EventArgs e)
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Delete Current Record?", "", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string fid = textBox2.Text;
                string fctlid = label6.Text;
                string deldatestr = "delete from msite where fctlid='" + fctlid + "'";
                operate.OperateData(deldatestr,DBConnect.mstdbConn);
                //MessageBox.Show("Have Been Deleted", "Warning",
                //    MessageBoxButtons.OK, MessageBoxIcon.Information);
                reloadgridview2(rowlabel.Text, "del");
            }

        }


        private void button3_Click(object sender, EventArgs e)
        {
            oldrow = rowlabel.Text;
            textBox4.BackColor = System.Drawing.SystemColors.Window;
            textBox4.ReadOnly = false;
            textBox3.BackColor = System.Drawing.SystemColors.Window;
            textBox3.ReadOnly = false;
            textBox2.BackColor = System.Drawing.SystemColors.Window;
            textBox2.ReadOnly = false;

            textBox1.Enabled = false;
            comboBox1.Enabled = false;
            Query.Enabled = false;

            button6.Visible = true;
            button7.Visible = true;
            button4.Visible = false;
            button5.Visible = false;
            button3.Visible = false;
            button2.Visible = false;
            dataGridView1.Enabled = false;
            textBox2.Focus();
            Edit_Tran = true;
        }

        private void button6_Click(object sender, EventArgs e)
        {
            Skip_Validate = true;
            
            if (!Validate_Tran())
            {
                Cur_TextBox.Focus();
                Cur_TextBox.SelectAll();

                return;
            }
            
            DialogResult myResult = DialogResult.OK;
            //myResult = MessageBox.Show("Are you really Update the item?", "Updated Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                String fid = textBox2.Text;
                String desc = textBox3.Text;
                String desc_c = textBox4.Text;
                string fctlid = label6.Text;
                string updatedatestr =
                    "update msite set fid='" + fid.Trim().Replace("'", "''") + "',fdesc=N'" + desc.Trim().Replace("'", "''") + "',fdesc_c =N'" + desc_c.Trim().Replace("'", "''") + "',fid2='',fupduser='" + user + "',fupddate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid='" + fctlid + "'";
                operate.OperateData(updatedatestr,DBConnect.mstdbConn);
                //MessageBox.Show("Have Been Updated", "Warning",
                //    MessageBoxButtons.OK, MessageBoxIcon.Information);
                reloadgridview2(fid,"update");
                //ButtonControl();
            }
        }

        private void button7_Click(object sender, EventArgs e)
        {
            Skip_Validate = true;
            DialogResult myResult;
            myResult = MessageBox.Show("Cancel Data Entry?", "", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                //ButtonControl();
                reloadgridview2(oldrow, "cancel");

                return;
            }

            //if (Cur_TextBox != null)
            //    Cur_TextBox.Focus();
        }

        private void button2_Click(object sender, EventArgs e)
        {
           
            oldrow = rowlabel.Text;
            DataTable listdataSource = dataGridView1.DataSource as DataTable;
            DataRow newCustomersRow = listdataSource.NewRow();
            listdataSource.Rows.InsertAt(newCustomersRow, listdataSource.Rows.Count);
            dataGridView1.DataSource = listdataSource;
            dataGridView1.CurrentCell = dataGridView1.Rows[listdataSource.Rows.Count - 1].Cells["ID"];
            textBox4.BackColor = System.Drawing.SystemColors.Window;
            textBox4.ReadOnly = false;
            textBox4.Text = "";
            textBox3.BackColor = System.Drawing.SystemColors.Window;
            textBox3.ReadOnly = false;
            textBox3.Text = "";
            textBox2.BackColor = System.Drawing.SystemColors.Window;
            textBox2.ReadOnly = false;
            textBox2.Text = "";

            textBox1.Enabled = false;
            comboBox1.Enabled = false;
            Query.Enabled = false;

            button7.Visible = true;
            button8.Visible = true;
            button4.Visible = false;
            button5.Visible = false;
            button3.Visible = false;
            button2.Visible = false;
            dataGridView1.Enabled = false;
            textBox2.Focus();
            Edit_Tran = true;
            ADD = true;
        }

        private void button8_Click(object sender, EventArgs e)
        {
            Skip_Validate = true;
            
            if (!Validate_Tran())
            {
                Cur_TextBox.Focus();
                Cur_TextBox.SelectAll();

                return;
            }
            
            DialogResult myResult = DialogResult.OK;
            //myResult = MessageBox.Show("Are you really Insert the item?", "Inserted Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                String fid = textBox2.Text;
                String desc = textBox3.Text;
                String desc_c = textBox4.Text;
                string fctlid = "";
                string str = "select * from xsysparm where fidtype ='MSITE'";
                DataTable dt = new DataTable();
                dt = operate.GetTable(str,DBConnect.mstdbConn);
                fctlid = dt.Rows[0]["fnxtid"].ToString();
                label6.Text = fctlid;

                string adddatestr =
                        "insert into msite(ftype,fctlid,fid,fid2,fdesc,fdesc_c,fctldel,finpuser,finpdate,fupduser,fupddate) " +
                        "values('E','" + fctlid + "','" + fid.Trim().Replace("'", "''") + "','','" + desc.Trim().Replace("'", "''") + "',N'" + desc_c.Trim().Replace("'", "''") + "','','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "')";
                operate.OperateData(adddatestr,DBConnect.mstdbConn);

                string updatestr = "update xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='MSITE'";
                operate.OperateData(updatestr,DBConnect.mstdbConn);
                //MessageBox.Show("Have Been Inserted!", "Warning",
                //    MessageBoxButtons.OK, MessageBoxIcon.Information);
                reloadgridview2(fid,"insert");
                //ButtonControl();
            }
        }

        private void Query_Click(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", textBox1.Text.ToString().Trim());
        }

        public enum Mode
        {
            ID = 1
        }

        private void InitCombobox()
        {
            Array arr = System.Enum.GetValues(typeof(Mode));    // 获取枚举的所有值
            DataTable dt = new DataTable();
            dt.Columns.Add("String", Type.GetType("System.String"));
            dt.Columns.Add("Value", typeof(int));
            foreach (var a in arr)
            {
                string strText = EnumTextByDescription.GetEnumDesc((Mode)a);
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = (int)a;

                dt.Rows.Add(aRow);
            }

            comboBox1.DataSource = dt;
            comboBox1.DisplayMember = "String";
            comboBox1.ValueMember = "Value";
        }

        private void textBox3_Validated(object sender, EventArgs e)
        {
            if (dataGridView1.CurrentCell != null)
            {
                dataGridView1["Desc#", dataGridView1.CurrentCell.RowIndex].Value = textBox3.Text;
            }

            if (!Edit_Tran || Skip_Validate)
            {
                Skip_Validate = false;

                return;
            }

            //Cur_TextBox = (TextBox)sender;
            //Boolean ll_ok = CheckEmptyString(Cur_TextBox);

            //if (!ll_ok)
            //{
            //    Cur_TextBox.Focus();
            //    Cur_TextBox.SelectAll();
            //}
        }

        private void textBox4_Validated(object sender, EventArgs e)
        {
            if (dataGridView1.CurrentCell != null)
            {
                dataGridView1["Chinese", dataGridView1.CurrentCell.RowIndex].Value = textBox4.Text;
            }

            if (!Edit_Tran || Skip_Validate)
            {
                Skip_Validate = false;

                return;
            }
        }

        private void textBox2_Validated(object sender, EventArgs e)
        {
            if (dataGridView1.CurrentCell != null)
            {
                dataGridView1["ID", dataGridView1.CurrentCell.RowIndex].Value = textBox2.Text;
            }

            if (!Edit_Tran || Skip_Validate)
            {
                Skip_Validate = false;

                return;
            }

            Cur_TextBox = (TextBox)sender;
            Boolean ll_ok = CheckEmptyString(Cur_TextBox);

            if (ll_ok)
                ll_ok = CheckDuplicate(Cur_TextBox);

            if (!ll_ok)
            {
                Cur_TextBox.Focus();
                Cur_TextBox.SelectAll();
            }
        }

        public Boolean CheckEmptyString(TextBox text_obj)
        {
            if (text_obj.Text.Trim() == "")
            {
                MessageBox.Show("Invalid Value");
                return false;
            }

            return true;
        }

        public Boolean CheckDuplicate(Control text_obj)
        {
            String cmd, sqlString, fid = text_obj.Text.Trim();

            cmd = "select ftype,fctlid,fid from msite where ftype = '{0}' and fid = '{1}'";
            sqlString = string.Format(cmd, "E", fid);

            SqlDataAdapter da = new SqlDataAdapter(sqlString, DBConnect.mstdbConn);
            DataTable dt = new DataTable();

            da.Fill(dt);
            da.Dispose();

            Boolean ll_ok;

            if (ADD)
                ll_ok = dt.DefaultView.Count == 0;
            else
            {
                ll_ok = dt.DefaultView.Count <= 1;

                if (ll_ok && dt.DefaultView.Count == 1)
                {
                    string fctlid = label6.Text.Trim();
                    ll_ok = dt.DefaultView[0]["fctlid"].ToString().Trim() == fctlid;
                }
            }

            dt.Dispose();

            if (dataGridView1.CurrentCell != null)
            {
                dataGridView1["ID", dataGridView1.CurrentCell.RowIndex].Value = text_obj.Text;
            }

            if (!ll_ok)
                MessageBox.Show("Invalid Value");

            return ll_ok;
        }

        private Boolean Validate_Tran()
        {
            Boolean ll_ok;

            Cur_TextBox = textBox2;
            ll_ok = CheckEmptyString(Cur_TextBox);

            if (!ll_ok)
                return ll_ok;

            ll_ok = CheckDuplicate(Cur_TextBox);

            if (!ll_ok)
                return ll_ok;

            //Cur_TextBox = textBox3;
            //ll_ok = CheckEmptyString(Cur_TextBox);

            //if (!ll_ok)
            //    return ll_ok;

            return ll_ok;
        }
    }
}
