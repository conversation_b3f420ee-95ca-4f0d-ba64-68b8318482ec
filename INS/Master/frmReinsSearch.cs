using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;

namespace INS
{
    public partial class frmReinsSearch : Form
    {
        public frmReinsSearch()
        {
            InitializeComponent();
            FillData("", "");
        }

        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "";
        void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            if (order == "" && query == "")
            {
                a = new SqlDataAdapter("select fid as Reinsurer#,fdesc as Desc# from mprdr where ftype='r' order by fid", c);
            }
            else if (order != "")
            {
                a = new SqlDataAdapter("select fid as Reinsurer#,fdesc as Desc# from mprdr where ftype='r' order by '" + order + "'", c);
            }
            else if (query != "")
            {
                a = new SqlDataAdapter("select fid as Reinsurer#,fdesc as Desc# from mprdr where ftype='r' AND fdesc = '" + query + "'", c);
            }
            else
            {
                a = new SqlDataAdapter("select fid as Reinsurer#,fdesc as Desc# from mprdr where ftype='r' AND fdesc = '" + query + "' order by fid", c);
            }
            DataTable t = new DataTable();
            a.Fill(t);
            dataGridView1.DataSource = t;
            c.Close();
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                row = dataGridView1.CurrentCell.RowIndex;
                if (dataGridView1.Rows[row].Cells["Reinsurer#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Reinsurer#"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["Reinsurer#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["Desc#"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            if (flag == "frmSurplus")
            {
                frmSurplus ownerFrm = (frmSurplus)this.Owner;
                ownerFrm.CloseFlag = false;
                ownerFrm.StrValuer = textBox1.Text.ToString();
                ownerFrm.StrValuern = label3.Text.ToString();
                this.Close();
            }
            if (flag == "frmXOL")
            {
                frmXOL2 ownerFrm = (frmXOL2)this.Owner;
                ownerFrm.CloseFlag = false;
                ownerFrm.StrValuer = textBox1.Text.ToString();
                ownerFrm.StrValuern = label3.Text.ToString();
                this.Close();
            }
            if (flag == "frmFac")
            {
                frmFac ownerFrm = (frmFac)this.Owner;
                ownerFrm.CloseFlag = false;
                ownerFrm.StrValuer = textBox1.Text.ToString();
                ownerFrm.StrValuern = label3.Text.ToString();
                this.Close();
            }
        }


        private void button1_Click(object sender, EventArgs e)
        {
            if (flag == "frmSurplus")
            {
                frmSurplus ownerFrm = (frmSurplus)this.Owner;
                ownerFrm.CloseFlag = true;
                this.Close();
            }
            if (flag == "frmXOL")
            {
                frmXOL2 ownerFrm = (frmXOL2)this.Owner;
                ownerFrm.CloseFlag = true;
                this.Close();
            }
            if (flag == "frmFac")
            {
                frmFac ownerFrm = (frmFac)this.Owner;
                ownerFrm.CloseFlag = true;
                this.Close();
            }
            this.Close();
        }


    }
}
