using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;

namespace INS
{
    public partial class frmDept : Form
    {
        public frmDept()
        {
            InitializeComponent();
            InitCombobox();
            FillData("", "");
            foreach (Control control in panel1.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
        }
        void control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
            {
                SendKeys.Send("{TAB}");
            }
        }
        DES des = new DES();
        DBConnect operate = new DBConnect();
        public String user, oldrow="";
        public int row;
        public Boolean flag = false;
        public Boolean ADD = false;
        private bool datagridview2SelectionChanged = true;
        public string Dbo = InsEnvironment.DataBase.GetDbo();
        void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            if (order != "" && query != "")
            {
                a = new SqlDataAdapter("select fid as Dept#, fname as Desc#, falias as Alias, fmap as Map#, fnewmap as NewMap#, fctlid from " + Dbo + "ndept where fid like '%" + query.ToUpper() + "%'", c);
            }
            else
            {
                a = new SqlDataAdapter("select fid as Dept#, fname as Desc#, falias as Alias, fmap as Map#, fnewmap as NewMap#, fctlid from " + Dbo + "ndept order by fid", c);
            }
            DataTable t = new DataTable();
            a.Fill(t);
            dataGridView1.DataSource = t;
            this.dataGridView1.Columns[5].Visible = false;
            c.Close();
        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        void reloadgridview2(string fid, string lab)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select fid as Dept#, fname as Desc#, falias as Alias, fmap as Map#, fnewmap as NewMap#, fctlid from ndept order by fid", c))
            {
                 DataTable t2 = new DataTable();
                a.Fill(t2);
                dataGridView1.DataSource = t2;
                if (t2.Rows.Count == 0)
                {
                    textBox2.Text = "";
                    textBox3.Text = "";
                    textBox4.Text = "";
                    textBox5.Text = "";
                }
                if (lab == "insert" || lab == "update")
                {
                    if (fid != "") //add update
                    {
                        for (int i = 0; i < t2.Rows.Count; i++)
                        {
                            if (t2.Rows[i]["Dept#"].ToString().Trim() == fid.Trim())
                            {
                                dataGridView1.CurrentCell = dataGridView1.Rows[i].Cells["Dept#"];
                                break;
                            }
                        }
                    }
                }
                if (lab == "del")
                {
                    if (dataGridView1.CurrentCell != null)
                    {
                        if (int.Parse(fid) == 0)
                        {
                            dataGridView1.CurrentCell = dataGridView1.Rows[int.Parse(fid)].Cells["Dept#"];
                        }
                        else { dataGridView1.CurrentCell = dataGridView1.Rows[int.Parse(fid) - 1].Cells["Dept#"]; }
                    }
                    else
                    {
                        textBox2.Text = "";
                        textBox3.Text = "";
                        textBox4.Text = "";
                        textBox5.Text = "";
                    }
                }
                if (lab == "cancel")
                {
                    if (dataGridView1.CurrentCell != null)
                    {
                        dataGridView1.CurrentCell = dataGridView1.Rows[int.Parse(fid)].Cells["Dept#"];
                    }
                }
            }
            this.dataGridView1.Columns[5].Visible = false;
            datagridview2SelectionChanged = true;
            flag = false;
            c.Close();
        }

        void ButtonControl()
        {
            ADD = false;
            textBox4.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox4.ReadOnly = true;
            textBox3.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox3.ReadOnly = true;
            textBox2.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox2.ReadOnly = true;
            textBox5.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox5.ReadOnly = true;
            textBox6.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox6.ReadOnly = true;
            button6.Visible = false;
            button7.Visible = false;
            button8.Visible = false;
            button4.Visible = true;
            button5.Visible = true;
            button3.Visible = true;
            button2.Visible = true;
            dataGridView1.Enabled = true;
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            if (!this.datagridview2SelectionChanged) return;
            try
            {
                if (flag == false && dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                if (dataGridView1.Rows[row].Cells["NewMap#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["NewMap#"].Value.ToString().Length != 0)
                    {
                        textBox6.Text = dataGridView1.Rows[row].Cells["NewMap#"].Value.ToString().Trim();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Map#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Map#"].Value.ToString().Length != 0)
                    {
                        textBox5.Text = dataGridView1.Rows[row].Cells["Map#"].Value.ToString().Trim();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Alias"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Alias"].Value.ToString().Length != 0)
                    {
                        textBox4.Text = dataGridView1.Rows[row].Cells["Alias"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[row].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        textBox3.Text = dataGridView1.Rows[row].Cells["Desc#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[row].Cells["Dept#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Dept#"].Value.ToString().Length != 0)
                    {
                        textBox2.Text = dataGridView1.Rows[row].Cells["Dept#"].Value.ToString().Trim();
                    }
                }
                label6.Text = dataGridView1.Rows[row].Cells["fctlid"].Value.ToString();
                rowlabel.Text = row.ToString();
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void button5_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void button4_Click(object sender, EventArgs e)
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the item?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string fid = textBox2.Text;
                string fctlid = label6.Text;
                string index = rowlabel.Text;
                string deldatestr =
                    "delete from ndept where fctlid='" + fctlid + "'";
                operate.OperateData(deldatestr);
                MessageBox.Show("Have Been Deleted", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                reloadgridview2(rowlabel.Text, "del");
            }
        }


        private void button3_Click(object sender, EventArgs e)
        {
            oldrow = rowlabel.Text;
            textBox4.BackColor = System.Drawing.SystemColors.Window;
            textBox4.ReadOnly = false;
            textBox3.BackColor = System.Drawing.SystemColors.Window;
            textBox3.ReadOnly = false;
            textBox2.BackColor = System.Drawing.SystemColors.Window;
            textBox2.ReadOnly = false;
            textBox5.BackColor = System.Drawing.SystemColors.Window;
            textBox5.ReadOnly = false;
            textBox6.BackColor = System.Drawing.SystemColors.Window;
            textBox6.ReadOnly = false;
            button6.Visible = true;
            button7.Visible = true;
            button4.Visible = false;
            button5.Visible = false;
            button3.Visible = false;
            button2.Visible = false;
            dataGridView1.Enabled = false;
        }

        private void button6_Click(object sender, EventArgs e)
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really Update the item?", "Updated Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                String fid = textBox2.Text.Trim();
                String desc = textBox3.Text.Trim();
                String alias = textBox4.Text.Trim();
                String map = textBox5.Text.Trim();
                String newmap = textBox6.Text.Trim();
                string fctlid = label6.Text;
                if (fid != "" && desc != "" && alias != "" && map != "" && newmap != "")
                {
                string updatedatestr =
                    "update ndept set fid='" + fid + "',fname='" + desc.Trim().Replace("'", "''") + "',falias ='" + alias.Trim().Replace("'", "''") + "',fmap ='" + map.Trim().Replace("'", "''") + "',fnewmap ='" + newmap.Trim().Replace("'", "''") + "',fctldel='',fupduser='" + user + "',fupddate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid='" + fctlid + "'";
                operate.OperateData(updatedatestr);
                MessageBox.Show("Have Been Updated", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                reloadgridview2(fid, "update");
                ButtonControl();
                }
                 else
                 {
                     MessageBox.Show("Some field is empty!", "Warning",
                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                 }
            }

        }

        private void button7_Click(object sender, EventArgs e)
        {
            ButtonControl();
            reloadgridview2(oldrow, "cancel");

        }

        private void button2_Click(object sender, EventArgs e)
        {
            ADD = true; oldrow = rowlabel.Text;
            DataTable listdataSource = dataGridView1.DataSource as DataTable;
            DataRow newCustomersRow = listdataSource.NewRow();
            listdataSource.Rows.InsertAt(newCustomersRow, listdataSource.Rows.Count);
            dataGridView1.DataSource = listdataSource;
            dataGridView1.CurrentCell = dataGridView1.Rows[listdataSource.Rows.Count - 1].Cells["Dept#"];
            textBox4.BackColor = System.Drawing.SystemColors.Window;
            textBox4.ReadOnly = false;
            textBox4.Text = "";
            textBox3.BackColor = System.Drawing.SystemColors.Window;
            textBox3.ReadOnly = false;
            textBox3.Text = "";
            textBox2.BackColor = System.Drawing.SystemColors.Window;
            textBox2.ReadOnly = false;
            textBox2.Text = "";
            textBox5.BackColor = System.Drawing.SystemColors.Window;
            textBox5.ReadOnly = false;
            textBox5.Text = "";
            textBox6.BackColor = System.Drawing.SystemColors.Window;
            textBox6.ReadOnly = false;
            textBox6.Text = "";
            button7.Visible = true;
            button8.Visible = true;
            button4.Visible = false;
            button5.Visible = false;
            button3.Visible = false;
            button2.Visible = false;
            dataGridView1.Enabled = false;
        }

        private void button8_Click(object sender, EventArgs e)
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really Insert the item?", "Inserted Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                String fid = textBox2.Text;
                String desc = textBox3.Text;
                String alias = textBox4.Text;
                String map = textBox5.Text;
                String newmap = textBox6.Text;
                string fctlid = "";
                string str = "select * from xsysparm where fidtype ='NDEPT'";
                DataTable dt = new DataTable();
                dt = operate.GetTable(str);
                fctlid = dt.Rows[0]["fnxtid"].ToString();
                label6.Text = fctlid;
                if (fid != "" && desc != "" && alias != "" && map != "" && newmap != "")
                {
                    string adddatestr =
                            "insert into ndept(fctlid,fid,fname,falias,fmap,fnewmap,fctldel,finpuser,finpdate,fupduser,fupddate) " +
                            "values('" + fctlid + "','" + fid + "','" + desc.Trim().Replace("'", "''") + "','" + alias.Trim().Replace("'", "''") + "','" + map.Trim().Replace("'", "''") + "','" + newmap.Trim().Replace("'", "''") + "','','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "')";
                    operate.OperateData(adddatestr);

                    string updatestr = "update xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='NDEPT'";
                    operate.OperateData(updatestr);
                    MessageBox.Show("Have Been Inserted!", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    reloadgridview2(fid, "insert");
                    ButtonControl();
                }
                else
                {
                    MessageBox.Show("Some field is empty!", "Warning",
                     MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }


      
        public string CheckDuplicate(string fid)
        {
            if (fid != "")
            {
                DataTable listdataSource = dataGridView1.DataSource as DataTable;
                if (listdataSource.Rows.Count > 0)
                {
                    DataTable objectTable = listdataSource.DefaultView.ToTable();
                    DataRow[] rows = objectTable.Select("[Dept#]='" + fid.Trim() + "'");
                    if (rows.Length > 1)
                    {
                        return "Duplicate Value";
                    }
                    else { return ""; }
                }
                else { return ""; }
            }
            else { return ""; }
        }

        private void Query_Click(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", textBox1.Text.ToString().Trim());
        }

        public enum Mode
        {
            Dept = 1
        }

        private void InitCombobox()
        {
            Array arr = System.Enum.GetValues(typeof(Mode));    // 获取枚举的所有值
            DataTable dt = new DataTable();
            dt.Columns.Add("String", Type.GetType("System.String"));
            dt.Columns.Add("Value", typeof(int));
            foreach (var a in arr)
            {
                string strText = EnumTextByDescription.GetEnumDesc((Mode)a);
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = (int)a;

                dt.Rows.Add(aRow);
            }

            comboBox1.DataSource = dt;
            comboBox1.DisplayMember = "String";
            comboBox1.ValueMember = "Value";
        }

        private void textBox2_Validated(object sender, EventArgs e)
        {
            if (dataGridView1.CurrentCell != null)
            {
                dataGridView1["Dept#", dataGridView1.CurrentCell.RowIndex].Value = textBox2.Text;
                string result2 = CheckDuplicate(textBox2.Text);
                if (result2 != "")
                {
                    MessageBox.Show(result2);
                    textBox2.Focus();
                    textBox2.SelectAll();
                }
            }
        }

        private void textBox3_Validated(object sender, EventArgs e)
        {
            if (dataGridView1.CurrentCell != null)
            {
                dataGridView1["Desc#", dataGridView1.CurrentCell.RowIndex].Value = textBox3.Text;
            }
        }

        private void textBox4_Validated(object sender, EventArgs e)
        {
            if (dataGridView1.CurrentCell != null)
            {
                dataGridView1["Alias", dataGridView1.CurrentCell.RowIndex].Value = textBox4.Text;
            }
        }

        private void textBox5_Validated(object sender, EventArgs e)
        {
            if (dataGridView1.CurrentCell != null)
            {
                dataGridView1["Map#", dataGridView1.CurrentCell.RowIndex].Value = textBox5.Text;
            }
        }

        private void textBox6_Validated(object sender, EventArgs e)
        {
            if (dataGridView1.CurrentCell != null)
            {
                dataGridView1["NewMap#", dataGridView1.CurrentCell.RowIndex].Value = textBox6.Text;
            }
        }

       

    }
}
