<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="mprdr" targetNamespace="http://tempuri.org/mprdr.xsd" xmlns:mstns="http://tempuri.org/mprdr.xsd" xmlns="http://tempuri.org/mprdr.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="1" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="Settings" AppSettingsPropertyName="ConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="ConnectionString (Settings)" PropertyReference="ApplicationSettings.INS.Properties.Settings.GlobalReference.Default.ConnectionString" Provider="System.Data.Odbc" />
          <Connection AppSettingsObjectName="Settings" AppSettingsPropertyName="ConnectionString_alm" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="ConnectionString_alm (Settings)" PropertyReference="ApplicationSettings.INS.Properties.Settings.GlobalReference.Default.ConnectionString_alm" Provider="System.Data.Odbc" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="muserTableAdapter" GeneratorDataComponentClassName="muserTableAdapter" Name="muser" UserDataComponentName="muserTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="ConnectionString_alm (Settings)" DbObjectName="msdata.dbo.muser" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [muser] WHERE (([fctlid] = ?) AND ([fcode] = ?) AND ([fname] = ?) AND ([fcname] = ?) AND ([fpassword] = ?) AND ([fpasshis] = ?) AND ([fapcode] = ?) AND ([fapparm] = ?) AND ([fappas] = ?) AND ([fright01] = ?) AND ([fright02] = ?) AND ([fright03] = ?) AND ([fright3a] = ?) AND ([fright3b] = ?) AND ([fright3c] = ?) AND ([fright04] = ?) AND ([fright4a] = ?) AND ([fright4b] = ?) AND ([fright4c] = ?) AND ([fright05] = ?) AND ([fright06] = ?) AND ([fright07] = ?) AND ([fright08] = ?) AND ([fright09] = ?) AND ([bright03] = ?) AND ([bright04] = ?) AND ([bright05] = ?) AND ([bright06] = ?) AND ([bright07] = ?) AND ([bright08] = ?) AND ([fenable] = ?) AND ((? = 1 AND [fvdate] IS NULL) OR ([fvdate] = ?)) AND ([ffailure] = ?) AND ((? = 1 AND [xsuper] IS NULL) OR ([xsuper] = ?)) AND ([finpuser] = ?) AND ((? = 1 AND [finpdate] IS NULL) OR ([finpdate] = ?)) AND ([fupduser] = ?) AND ((? = 1 AND [fupddate] IS NULL) OR ([fupddate] = ?)) AND ([fctldel] = ?))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fctlid" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fctlid" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fcode" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fcode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fname" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fname" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fcname" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fcname" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fpassword" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fpassword" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fpasshis" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fpasshis" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fapcode" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fapcode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fapparm" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fapparm" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fappas" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fappas" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright01" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright01" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright02" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright02" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright03" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright03" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright3a" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright3a" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright3b" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright3b" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright3c" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright3c" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright04" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright04" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright4a" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright4a" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright4b" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright4b" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright4c" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright4c" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright05" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright05" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright06" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright06" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright07" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright07" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright08" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright08" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright09" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright09" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_bright03" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright03" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_bright04" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright04" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_bright05" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright05" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_bright06" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright06" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_bright07" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright07" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_bright08" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright08" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fenable" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fenable" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_fvdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="fvdate" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="Original_fvdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="fvdate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="Original_ffailure" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ffailure" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_xsuper" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="xsuper" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_xsuper" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="xsuper" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_finpuser" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="finpuser" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_finpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="finpdate" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="Original_finpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="finpdate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fupduser" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fupduser" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_fupddate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="fupddate" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="Original_fupddate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="fupddate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fctldel" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fctldel" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [muser] ([fctlid], [fcode], [fname], [fcname], [fpassword], [fpasshis], [fapcode], [fapparm], [fappas], [fright01], [fright02], [fright03], [fright3a], [fright3b], [fright3c], [fright04], [fright4a], [fright4b], [fright4c], [fright05], [fright06], [fright07], [fright08], [fright09], [bright03], [bright04], [bright05], [bright06], [bright07], [bright08], [fenable], [fvdate], [ffailure], [xsuper], [finpuser], [finpdate], [fupduser], [fupddate], [fctldel]) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fctlid" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fctlid" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fcode" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fcode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fname" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fname" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fcname" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fcname" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fpassword" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fpassword" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fpasshis" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fpasshis" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fapcode" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fapcode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fapparm" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fapparm" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fappas" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fappas" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright01" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright01" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright02" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright02" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright03" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright03" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright3a" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright3a" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright3b" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright3b" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright3c" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright3c" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright04" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright04" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright4a" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright4a" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright4b" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright4b" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright4c" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright4c" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright05" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright05" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright06" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright06" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright07" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright07" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright08" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright08" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright09" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright09" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="bright03" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright03" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="bright04" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright04" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="bright05" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright05" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="bright06" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright06" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="bright07" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright07" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="bright08" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright08" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fenable" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fenable" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="fvdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="fvdate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="ffailure" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ffailure" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="xsuper" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="xsuper" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="finpuser" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="finpuser" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="finpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="finpdate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fupduser" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fupduser" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="fupddate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="fupddate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fctldel" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fctldel" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>select * from muser</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [muser] SET [fctlid] = ?, [fcode] = ?, [fname] = ?, [fcname] = ?, [fpassword] = ?, [fpasshis] = ?, [fapcode] = ?, [fapparm] = ?, [fappas] = ?, [fright01] = ?, [fright02] = ?, [fright03] = ?, [fright3a] = ?, [fright3b] = ?, [fright3c] = ?, [fright04] = ?, [fright4a] = ?, [fright4b] = ?, [fright4c] = ?, [fright05] = ?, [fright06] = ?, [fright07] = ?, [fright08] = ?, [fright09] = ?, [bright03] = ?, [bright04] = ?, [bright05] = ?, [bright06] = ?, [bright07] = ?, [bright08] = ?, [fenable] = ?, [fvdate] = ?, [ffailure] = ?, [xsuper] = ?, [finpuser] = ?, [finpdate] = ?, [fupduser] = ?, [fupddate] = ?, [fctldel] = ? WHERE (([fctlid] = ?) AND ([fcode] = ?) AND ([fname] = ?) AND ([fcname] = ?) AND ([fpassword] = ?) AND ([fpasshis] = ?) AND ([fapcode] = ?) AND ([fapparm] = ?) AND ([fappas] = ?) AND ([fright01] = ?) AND ([fright02] = ?) AND ([fright03] = ?) AND ([fright3a] = ?) AND ([fright3b] = ?) AND ([fright3c] = ?) AND ([fright04] = ?) AND ([fright4a] = ?) AND ([fright4b] = ?) AND ([fright4c] = ?) AND ([fright05] = ?) AND ([fright06] = ?) AND ([fright07] = ?) AND ([fright08] = ?) AND ([fright09] = ?) AND ([bright03] = ?) AND ([bright04] = ?) AND ([bright05] = ?) AND ([bright06] = ?) AND ([bright07] = ?) AND ([bright08] = ?) AND ([fenable] = ?) AND ((? = 1 AND [fvdate] IS NULL) OR ([fvdate] = ?)) AND ([ffailure] = ?) AND ((? = 1 AND [xsuper] IS NULL) OR ([xsuper] = ?)) AND ([finpuser] = ?) AND ((? = 1 AND [finpdate] IS NULL) OR ([finpdate] = ?)) AND ([fupduser] = ?) AND ((? = 1 AND [fupddate] IS NULL) OR ([fupddate] = ?)) AND ([fctldel] = ?))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fctlid" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fctlid" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fcode" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fcode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fname" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fname" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fcname" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fcname" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fpassword" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fpassword" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fpasshis" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fpasshis" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fapcode" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fapcode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fapparm" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fapparm" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fappas" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fappas" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright01" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright01" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright02" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright02" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright03" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright03" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright3a" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright3a" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright3b" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright3b" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright3c" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright3c" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright04" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright04" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright4a" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright4a" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright4b" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright4b" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright4c" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright4c" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright05" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright05" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright06" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright06" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright07" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright07" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright08" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright08" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fright09" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright09" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="bright03" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright03" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="bright04" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright04" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="bright05" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright05" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="bright06" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright06" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="bright07" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright07" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="bright08" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright08" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fenable" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fenable" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="fvdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="fvdate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="ffailure" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ffailure" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="xsuper" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="xsuper" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="finpuser" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="finpuser" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="finpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="finpdate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fupduser" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fupduser" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="fupddate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="fupddate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="fctldel" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fctldel" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fctlid" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fctlid" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fcode" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fcode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fname" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fname" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fcname" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fcname" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fpassword" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fpassword" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fpasshis" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fpasshis" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fapcode" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fapcode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fapparm" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fapparm" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fappas" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fappas" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright01" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright01" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright02" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright02" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright03" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright03" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright3a" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright3a" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright3b" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright3b" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright3c" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright3c" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright04" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright04" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright4a" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright4a" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright4b" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright4b" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright4c" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright4c" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright05" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright05" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright06" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright06" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright07" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright07" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright08" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright08" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fright09" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fright09" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_bright03" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright03" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_bright04" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright04" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_bright05" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright05" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_bright06" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright06" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_bright07" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright07" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_bright08" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="bright08" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fenable" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fenable" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_fvdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="fvdate" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="Original_fvdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="fvdate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="Original_ffailure" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ffailure" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_xsuper" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="xsuper" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_xsuper" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="xsuper" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_finpuser" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="finpuser" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_finpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="finpdate" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="Original_finpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="finpdate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fupduser" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fupduser" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="IsNull_fupddate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="fupddate" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="Original_fupddate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="fupddate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiStringFixedLength" Direction="Input" ParameterName="Original_fctldel" Precision="0" ProviderType="Char" Scale="0" Size="0" SourceColumn="fctldel" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="fctlid" DataSetColumn="fctlid" />
              <Mapping SourceColumn="fcode" DataSetColumn="fcode" />
              <Mapping SourceColumn="fname" DataSetColumn="fname" />
              <Mapping SourceColumn="fcname" DataSetColumn="fcname" />
              <Mapping SourceColumn="fpassword" DataSetColumn="fpassword" />
              <Mapping SourceColumn="fpasshis" DataSetColumn="fpasshis" />
              <Mapping SourceColumn="fapcode" DataSetColumn="fapcode" />
              <Mapping SourceColumn="fapparm" DataSetColumn="fapparm" />
              <Mapping SourceColumn="fappas" DataSetColumn="fappas" />
              <Mapping SourceColumn="fright01" DataSetColumn="fright01" />
              <Mapping SourceColumn="fright02" DataSetColumn="fright02" />
              <Mapping SourceColumn="fright03" DataSetColumn="fright03" />
              <Mapping SourceColumn="fright3a" DataSetColumn="fright3a" />
              <Mapping SourceColumn="fright3b" DataSetColumn="fright3b" />
              <Mapping SourceColumn="fright3c" DataSetColumn="fright3c" />
              <Mapping SourceColumn="fright04" DataSetColumn="fright04" />
              <Mapping SourceColumn="fright4a" DataSetColumn="fright4a" />
              <Mapping SourceColumn="fright4b" DataSetColumn="fright4b" />
              <Mapping SourceColumn="fright4c" DataSetColumn="fright4c" />
              <Mapping SourceColumn="fright05" DataSetColumn="fright05" />
              <Mapping SourceColumn="fright06" DataSetColumn="fright06" />
              <Mapping SourceColumn="fright07" DataSetColumn="fright07" />
              <Mapping SourceColumn="fright08" DataSetColumn="fright08" />
              <Mapping SourceColumn="fright09" DataSetColumn="fright09" />
              <Mapping SourceColumn="bright03" DataSetColumn="bright03" />
              <Mapping SourceColumn="bright04" DataSetColumn="bright04" />
              <Mapping SourceColumn="bright05" DataSetColumn="bright05" />
              <Mapping SourceColumn="bright06" DataSetColumn="bright06" />
              <Mapping SourceColumn="bright07" DataSetColumn="bright07" />
              <Mapping SourceColumn="bright08" DataSetColumn="bright08" />
              <Mapping SourceColumn="fenable" DataSetColumn="fenable" />
              <Mapping SourceColumn="fvdate" DataSetColumn="fvdate" />
              <Mapping SourceColumn="ffailure" DataSetColumn="ffailure" />
              <Mapping SourceColumn="xsuper" DataSetColumn="xsuper" />
              <Mapping SourceColumn="finpuser" DataSetColumn="finpuser" />
              <Mapping SourceColumn="finpdate" DataSetColumn="finpdate" />
              <Mapping SourceColumn="fupduser" DataSetColumn="fupduser" />
              <Mapping SourceColumn="fupddate" DataSetColumn="fupddate" />
              <Mapping SourceColumn="fctldel" DataSetColumn="fctldel" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="mprdr" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="mprdr" msprop:Generator_UserDSName="mprdr">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="rprdr" msprop:Generator_TableClassName="rprdrDataTable" msprop:Generator_TableVarName="tablerprdr" msprop:Generator_RowChangedName="rprdrRowChanged" msprop:Generator_TablePropName="rprdr" msprop:Generator_RowDeletingName="rprdrRowDeleting" msprop:Generator_RowChangingName="rprdrRowChanging" msprop:Generator_RowEvHandlerName="rprdrRowChangeEventHandler" msprop:Generator_RowDeletedName="rprdrRowDeleted" msprop:Generator_RowClassName="rprdrRow" msprop:Generator_UserTableName="rprdr" msprop:Generator_RowEvArgName="rprdrRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ftype" msprop:Generator_ColumnVarNameInTable="columnftype" msprop:Generator_ColumnPropNameInRow="ftype" msprop:Generator_ColumnPropNameInTable="ftypeColumn" msprop:Generator_UserColumnName="ftype">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fctlid" msprop:Generator_ColumnVarNameInTable="columnfctlid" msprop:Generator_ColumnPropNameInRow="fctlid" msprop:Generator_ColumnPropNameInTable="fctlidColumn" msprop:Generator_UserColumnName="fctlid">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fid" msprop:Generator_ColumnVarNameInTable="columnfid" msprop:Generator_ColumnPropNameInRow="fid" msprop:Generator_ColumnPropNameInTable="fidColumn" msprop:Generator_UserColumnName="fid">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fmap" msprop:Generator_ColumnVarNameInTable="columnfmap" msprop:Generator_ColumnPropNameInRow="fmap" msprop:Generator_ColumnPropNameInTable="fmapColumn" msprop:Generator_UserColumnName="fmap">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fdescidx" msprop:Generator_ColumnVarNameInTable="columnfdescidx" msprop:Generator_ColumnPropNameInRow="fdescidx" msprop:Generator_ColumnPropNameInTable="fdescidxColumn" msprop:Generator_UserColumnName="fdescidx">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="60" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fdesc" msprop:Generator_ColumnVarNameInTable="columnfdesc" msprop:Generator_ColumnPropNameInRow="fdesc" msprop:Generator_ColumnPropNameInTable="fdescColumn" msprop:Generator_UserColumnName="fdesc">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="254" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fcname" msprop:Generator_ColumnVarNameInTable="columnfcname" msprop:Generator_ColumnPropNameInRow="fcname" msprop:Generator_ColumnPropNameInTable="fcnameColumn" msprop:Generator_UserColumnName="fcname">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="60" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="falias" msprop:Generator_ColumnVarNameInTable="columnfalias" msprop:Generator_ColumnPropNameInRow="falias" msprop:Generator_ColumnPropNameInTable="faliasColumn" msprop:Generator_UserColumnName="falias" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="60" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fadd1" msprop:Generator_ColumnVarNameInTable="columnfadd1" msprop:Generator_ColumnPropNameInRow="fadd1" msprop:Generator_ColumnPropNameInTable="fadd1Column" msprop:Generator_UserColumnName="fadd1">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="40" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fadd2" msprop:Generator_ColumnVarNameInTable="columnfadd2" msprop:Generator_ColumnPropNameInRow="fadd2" msprop:Generator_ColumnPropNameInTable="fadd2Column" msprop:Generator_UserColumnName="fadd2">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="40" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fadd3" msprop:Generator_ColumnVarNameInTable="columnfadd3" msprop:Generator_ColumnPropNameInRow="fadd3" msprop:Generator_ColumnPropNameInTable="fadd3Column" msprop:Generator_UserColumnName="fadd3">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="40" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fadd4" msprop:Generator_ColumnVarNameInTable="columnfadd4" msprop:Generator_ColumnPropNameInRow="fadd4" msprop:Generator_ColumnPropNameInTable="fadd4Column" msprop:Generator_UserColumnName="fadd4">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="40" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fpostcode" msprop:Generator_ColumnVarNameInTable="columnfpostcode" msprop:Generator_ColumnPropNameInRow="fpostcode" msprop:Generator_ColumnPropNameInTable="fpostcodeColumn" msprop:Generator_UserColumnName="fpostcode">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fcountry" msprop:Generator_ColumnVarNameInTable="columnfcountry" msprop:Generator_ColumnPropNameInRow="fcountry" msprop:Generator_ColumnPropNameInTable="fcountryColumn" msprop:Generator_UserColumnName="fcountry">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fregion" msprop:Generator_ColumnVarNameInTable="columnfregion" msprop:Generator_ColumnPropNameInRow="fregion" msprop:Generator_ColumnPropNameInTable="fregionColumn" msprop:Generator_UserColumnName="fregion">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ftel" msprop:Generator_ColumnVarNameInTable="columnftel" msprop:Generator_ColumnPropNameInRow="ftel" msprop:Generator_ColumnPropNameInTable="ftelColumn" msprop:Generator_UserColumnName="ftel">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ffax" msprop:Generator_ColumnVarNameInTable="columnffax" msprop:Generator_ColumnPropNameInRow="ffax" msprop:Generator_ColumnPropNameInTable="ffaxColumn" msprop:Generator_UserColumnName="ffax">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="femail" msprop:Generator_ColumnVarNameInTable="columnfemail" msprop:Generator_ColumnPropNameInRow="femail" msprop:Generator_ColumnPropNameInTable="femailColumn" msprop:Generator_UserColumnName="femail">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="frep" msprop:Generator_ColumnVarNameInTable="columnfrep" msprop:Generator_ColumnPropNameInRow="frep" msprop:Generator_ColumnPropNameInTable="frepColumn" msprop:Generator_UserColumnName="frep">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="finter" msprop:Generator_ColumnVarNameInTable="columnfinter" msprop:Generator_ColumnPropNameInRow="finter" msprop:Generator_ColumnPropNameInTable="finterColumn" msprop:Generator_UserColumnName="finter">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fsubside" msprop:Generator_ColumnVarNameInTable="columnfsubside" msprop:Generator_ColumnPropNameInRow="fsubside" msprop:Generator_ColumnPropNameInTable="fsubsideColumn" msprop:Generator_UserColumnName="fsubside">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fstatus" msprop:Generator_ColumnVarNameInTable="columnfstatus" msprop:Generator_ColumnPropNameInRow="fstatus" msprop:Generator_ColumnPropNameInTable="fstatusColumn" msprop:Generator_UserColumnName="fstatus" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fctldel" msprop:Generator_ColumnVarNameInTable="columnfctldel" msprop:Generator_ColumnPropNameInRow="fctldel" msprop:Generator_ColumnPropNameInTable="fctldelColumn" msprop:Generator_UserColumnName="fctldel">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="finpuser" msprop:Generator_ColumnVarNameInTable="columnfinpuser" msprop:Generator_ColumnPropNameInRow="finpuser" msprop:Generator_ColumnPropNameInTable="finpuserColumn" msprop:Generator_UserColumnName="finpuser">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="finpdate" msprop:Generator_ColumnVarNameInTable="columnfinpdate" msprop:Generator_ColumnPropNameInRow="finpdate" msprop:Generator_ColumnPropNameInTable="finpdateColumn" msprop:Generator_UserColumnName="finpdate" type="xs:dateTime" />
              <xs:element name="fupduser" msprop:Generator_ColumnVarNameInTable="columnfupduser" msprop:Generator_ColumnPropNameInRow="fupduser" msprop:Generator_ColumnPropNameInTable="fupduserColumn" msprop:Generator_UserColumnName="fupduser">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fupddate" msprop:Generator_ColumnVarNameInTable="columnfupddate" msprop:Generator_ColumnPropNameInRow="fupddate" msprop:Generator_ColumnPropNameInTable="fupddateColumn" msprop:Generator_UserColumnName="fupddate" type="xs:dateTime" />
              <xs:element name="id" msprop:Generator_ColumnVarNameInTable="columnid" msprop:Generator_ColumnPropNameInRow="id" msprop:Generator_ColumnPropNameInTable="idColumn" msprop:Generator_UserColumnName="id" type="xs:int" />
              <xs:element name="pinter" msprop:Generator_ColumnVarNameInTable="columnpinter" msprop:Generator_ColumnPropNameInRow="pinter" msprop:Generator_ColumnPropNameInTable="pinterColumn" msprop:Generator_UserColumnName="pinter" type="xs:string" minOccurs="0" />
              <xs:element name="psubside" msprop:Generator_ColumnVarNameInTable="columnpsubside" msprop:Generator_ColumnPropNameInRow="psubside" msprop:Generator_ColumnPropNameInTable="psubsideColumn" msprop:Generator_UserColumnName="psubside" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="rcontact" msprop:Generator_TableClassName="rcontactDataTable" msprop:Generator_TableVarName="tablercontact" msprop:Generator_RowChangedName="rcontactRowChanged" msprop:Generator_TablePropName="rcontact" msprop:Generator_RowDeletingName="rcontactRowDeleting" msprop:Generator_RowChangingName="rcontactRowChanging" msprop:Generator_RowEvHandlerName="rcontactRowChangeEventHandler" msprop:Generator_RowDeletedName="rcontactRowDeleted" msprop:Generator_RowClassName="rcontactRow" msprop:Generator_UserTableName="rcontact" msprop:Generator_RowEvArgName="rcontactRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="fctlid_1" msprop:Generator_ColumnVarNameInTable="columnfctlid_1" msprop:Generator_ColumnPropNameInRow="fctlid_1" msprop:Generator_ColumnPropNameInTable="fctlid_1Column" msprop:Generator_UserColumnName="fctlid_1">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fctlid" msprop:Generator_ColumnVarNameInTable="columnfctlid" msprop:Generator_ColumnPropNameInRow="fctlid" msprop:Generator_ColumnPropNameInTable="fctlidColumn" msprop:Generator_UserColumnName="fctlid">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fname" msprop:Generator_ColumnVarNameInTable="columnfname" msprop:Generator_ColumnPropNameInRow="fname" msprop:Generator_ColumnPropNameInTable="fnameColumn" msprop:Generator_UserColumnName="fname">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fpost" msprop:Generator_ColumnVarNameInTable="columnfpost" msprop:Generator_ColumnPropNameInRow="fpost" msprop:Generator_ColumnPropNameInTable="fpostColumn" msprop:Generator_UserColumnName="fpost">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ftel" msprop:Generator_ColumnVarNameInTable="columnftel" msprop:Generator_ColumnPropNameInRow="ftel" msprop:Generator_ColumnPropNameInTable="ftelColumn" msprop:Generator_UserColumnName="ftel">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ffax" msprop:Generator_ColumnVarNameInTable="columnffax" msprop:Generator_ColumnPropNameInRow="ffax" msprop:Generator_ColumnPropNameInTable="ffaxColumn" msprop:Generator_UserColumnName="ffax">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="femail" msprop:Generator_ColumnVarNameInTable="columnfemail" msprop:Generator_ColumnPropNameInRow="femail" msprop:Generator_ColumnPropNameInTable="femailColumn" msprop:Generator_UserColumnName="femail">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fctldel" msprop:Generator_ColumnVarNameInTable="columnfctldel" msprop:Generator_ColumnPropNameInRow="fctldel" msprop:Generator_ColumnPropNameInTable="fctldelColumn" msprop:Generator_UserColumnName="fctldel">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="rdept" msprop:Generator_TableClassName="rdeptDataTable" msprop:Generator_TableVarName="tablerdept" msprop:Generator_TablePropName="rdept" msprop:Generator_RowDeletingName="rdeptRowDeleting" msprop:Generator_RowChangingName="rdeptRowChanging" msprop:Generator_RowEvHandlerName="rdeptRowChangeEventHandler" msprop:Generator_RowDeletedName="rdeptRowDeleted" msprop:Generator_UserTableName="rdept" msprop:Generator_RowChangedName="rdeptRowChanged" msprop:Generator_RowEvArgName="rdeptRowChangeEvent" msprop:Generator_RowClassName="rdeptRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ftype" msprop:Generator_ColumnVarNameInTable="columnftype" msprop:Generator_ColumnPropNameInRow="ftype" msprop:Generator_ColumnPropNameInTable="ftypeColumn" msprop:Generator_UserColumnName="ftype">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fctlid_1" msprop:Generator_ColumnVarNameInTable="columnfctlid_1" msprop:Generator_ColumnPropNameInRow="fctlid_1" msprop:Generator_ColumnPropNameInTable="fctlid_1Column" msprop:Generator_UserColumnName="fctlid_1">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fctlid" msprop:Generator_ColumnVarNameInTable="columnfctlid" msprop:Generator_ColumnPropNameInRow="fctlid" msprop:Generator_ColumnPropNameInTable="fctlidColumn" msprop:Generator_UserColumnName="fctlid">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fid_1" msprop:Generator_ColumnVarNameInTable="columnfid_1" msprop:Generator_ColumnPropNameInRow="fid_1" msprop:Generator_ColumnPropNameInTable="fid_1Column" msprop:Generator_UserColumnName="fid_1">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fid" msprop:Generator_ColumnVarNameInTable="columnfid" msprop:Generator_ColumnPropNameInRow="fid" msprop:Generator_ColumnPropNameInTable="fidColumn" msprop:Generator_UserColumnName="fid">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="3" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fname" msprop:Generator_ColumnVarNameInTable="columnfname" msprop:Generator_ColumnPropNameInRow="fname" msprop:Generator_ColumnPropNameInTable="fnameColumn" msprop:Generator_UserColumnName="fname">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="falias" msprop:Generator_ColumnVarNameInTable="columnfalias" msprop:Generator_ColumnPropNameInRow="falias" msprop:Generator_ColumnPropNameInTable="faliasColumn" msprop:Generator_UserColumnName="falias" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fmap" msprop:Generator_ColumnVarNameInTable="columnfmap" msprop:Generator_ColumnPropNameInRow="fmap" msprop:Generator_ColumnPropNameInTable="fmapColumn" msprop:Generator_UserColumnName="fmap" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="3" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fnewmap" msprop:Generator_ColumnVarNameInTable="columnfnewmap" msprop:Generator_ColumnPropNameInRow="fnewmap" msprop:Generator_ColumnPropNameInTable="fnewmapColumn" msprop:Generator_UserColumnName="fnewmap" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="3" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="muser" msprop:Generator_TableClassName="muserDataTable" msprop:Generator_TableVarName="tablemuser" msprop:Generator_TablePropName="muser" msprop:Generator_RowDeletingName="muserRowDeleting" msprop:Generator_RowChangingName="muserRowChanging" msprop:Generator_RowEvHandlerName="muserRowChangeEventHandler" msprop:Generator_RowDeletedName="muserRowDeleted" msprop:Generator_UserTableName="muser" msprop:Generator_RowChangedName="muserRowChanged" msprop:Generator_RowEvArgName="muserRowChangeEvent" msprop:Generator_RowClassName="muserRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="fctlid" msprop:Generator_ColumnVarNameInTable="columnfctlid" msprop:Generator_ColumnPropNameInRow="fctlid" msprop:Generator_ColumnPropNameInTable="fctlidColumn" msprop:Generator_UserColumnName="fctlid">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fcode" msprop:Generator_ColumnVarNameInTable="columnfcode" msprop:Generator_ColumnPropNameInRow="fcode" msprop:Generator_ColumnPropNameInTable="fcodeColumn" msprop:Generator_UserColumnName="fcode">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fname" msprop:Generator_ColumnVarNameInTable="columnfname" msprop:Generator_ColumnPropNameInRow="fname" msprop:Generator_ColumnPropNameInTable="fnameColumn" msprop:Generator_UserColumnName="fname">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fcname" msprop:Generator_ColumnVarNameInTable="columnfcname" msprop:Generator_ColumnPropNameInRow="fcname" msprop:Generator_ColumnPropNameInTable="fcnameColumn" msprop:Generator_UserColumnName="fcname">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="12" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fpassword" msprop:Generator_ColumnVarNameInTable="columnfpassword" msprop:Generator_ColumnPropNameInRow="fpassword" msprop:Generator_ColumnPropNameInTable="fpasswordColumn" msprop:Generator_UserColumnName="fpassword">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fpasshis" msprop:Generator_ColumnVarNameInTable="columnfpasshis" msprop:Generator_ColumnPropNameInRow="fpasshis" msprop:Generator_ColumnPropNameInTable="fpasshisColumn" msprop:Generator_UserColumnName="fpasshis">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="120" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fapcode" msprop:Generator_ColumnVarNameInTable="columnfapcode" msprop:Generator_ColumnPropNameInRow="fapcode" msprop:Generator_ColumnPropNameInTable="fapcodeColumn" msprop:Generator_UserColumnName="fapcode">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fapparm" msprop:Generator_ColumnVarNameInTable="columnfapparm" msprop:Generator_ColumnPropNameInRow="fapparm" msprop:Generator_ColumnPropNameInTable="fapparmColumn" msprop:Generator_UserColumnName="fapparm">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fappas" msprop:Generator_ColumnVarNameInTable="columnfappas" msprop:Generator_ColumnPropNameInRow="fappas" msprop:Generator_ColumnPropNameInTable="fappasColumn" msprop:Generator_UserColumnName="fappas">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fright01" msprop:Generator_ColumnVarNameInTable="columnfright01" msprop:Generator_ColumnPropNameInRow="fright01" msprop:Generator_ColumnPropNameInTable="fright01Column" msprop:Generator_UserColumnName="fright01">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fright02" msprop:Generator_ColumnVarNameInTable="columnfright02" msprop:Generator_ColumnPropNameInRow="fright02" msprop:Generator_ColumnPropNameInTable="fright02Column" msprop:Generator_UserColumnName="fright02">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fright03" msprop:Generator_ColumnVarNameInTable="columnfright03" msprop:Generator_ColumnPropNameInRow="fright03" msprop:Generator_ColumnPropNameInTable="fright03Column" msprop:Generator_UserColumnName="fright03">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fright3a" msprop:Generator_ColumnVarNameInTable="columnfright3a" msprop:Generator_ColumnPropNameInRow="fright3a" msprop:Generator_ColumnPropNameInTable="fright3aColumn" msprop:Generator_UserColumnName="fright3a">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fright3b" msprop:Generator_ColumnVarNameInTable="columnfright3b" msprop:Generator_ColumnPropNameInRow="fright3b" msprop:Generator_ColumnPropNameInTable="fright3bColumn" msprop:Generator_UserColumnName="fright3b">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fright3c" msprop:Generator_ColumnVarNameInTable="columnfright3c" msprop:Generator_ColumnPropNameInRow="fright3c" msprop:Generator_ColumnPropNameInTable="fright3cColumn" msprop:Generator_UserColumnName="fright3c">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fright04" msprop:Generator_ColumnVarNameInTable="columnfright04" msprop:Generator_ColumnPropNameInRow="fright04" msprop:Generator_ColumnPropNameInTable="fright04Column" msprop:Generator_UserColumnName="fright04">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fright4a" msprop:Generator_ColumnVarNameInTable="columnfright4a" msprop:Generator_ColumnPropNameInRow="fright4a" msprop:Generator_ColumnPropNameInTable="fright4aColumn" msprop:Generator_UserColumnName="fright4a">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fright4b" msprop:Generator_ColumnVarNameInTable="columnfright4b" msprop:Generator_ColumnPropNameInRow="fright4b" msprop:Generator_ColumnPropNameInTable="fright4bColumn" msprop:Generator_UserColumnName="fright4b">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fright4c" msprop:Generator_ColumnVarNameInTable="columnfright4c" msprop:Generator_ColumnPropNameInRow="fright4c" msprop:Generator_ColumnPropNameInTable="fright4cColumn" msprop:Generator_UserColumnName="fright4c">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fright05" msprop:Generator_ColumnVarNameInTable="columnfright05" msprop:Generator_ColumnPropNameInRow="fright05" msprop:Generator_ColumnPropNameInTable="fright05Column" msprop:Generator_UserColumnName="fright05">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fright06" msprop:Generator_ColumnVarNameInTable="columnfright06" msprop:Generator_ColumnPropNameInRow="fright06" msprop:Generator_ColumnPropNameInTable="fright06Column" msprop:Generator_UserColumnName="fright06">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fright07" msprop:Generator_ColumnVarNameInTable="columnfright07" msprop:Generator_ColumnPropNameInRow="fright07" msprop:Generator_ColumnPropNameInTable="fright07Column" msprop:Generator_UserColumnName="fright07">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fright08" msprop:Generator_ColumnVarNameInTable="columnfright08" msprop:Generator_ColumnPropNameInRow="fright08" msprop:Generator_ColumnPropNameInTable="fright08Column" msprop:Generator_UserColumnName="fright08">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fright09" msprop:Generator_ColumnVarNameInTable="columnfright09" msprop:Generator_ColumnPropNameInRow="fright09" msprop:Generator_ColumnPropNameInTable="fright09Column" msprop:Generator_UserColumnName="fright09">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="bright03" msprop:Generator_ColumnVarNameInTable="columnbright03" msprop:Generator_ColumnPropNameInRow="bright03" msprop:Generator_ColumnPropNameInTable="bright03Column" msprop:Generator_UserColumnName="bright03">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="bright04" msprop:Generator_ColumnVarNameInTable="columnbright04" msprop:Generator_ColumnPropNameInRow="bright04" msprop:Generator_ColumnPropNameInTable="bright04Column" msprop:Generator_UserColumnName="bright04">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="bright05" msprop:Generator_ColumnVarNameInTable="columnbright05" msprop:Generator_ColumnPropNameInRow="bright05" msprop:Generator_ColumnPropNameInTable="bright05Column" msprop:Generator_UserColumnName="bright05">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="bright06" msprop:Generator_ColumnVarNameInTable="columnbright06" msprop:Generator_ColumnPropNameInRow="bright06" msprop:Generator_ColumnPropNameInTable="bright06Column" msprop:Generator_UserColumnName="bright06">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="bright07" msprop:Generator_ColumnVarNameInTable="columnbright07" msprop:Generator_ColumnPropNameInRow="bright07" msprop:Generator_ColumnPropNameInTable="bright07Column" msprop:Generator_UserColumnName="bright07">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="bright08" msprop:Generator_ColumnVarNameInTable="columnbright08" msprop:Generator_ColumnPropNameInRow="bright08" msprop:Generator_ColumnPropNameInTable="bright08Column" msprop:Generator_UserColumnName="bright08">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fenable" msprop:Generator_ColumnVarNameInTable="columnfenable" msprop:Generator_ColumnPropNameInRow="fenable" msprop:Generator_ColumnPropNameInTable="fenableColumn" msprop:Generator_UserColumnName="fenable">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fvdate" msprop:Generator_ColumnVarNameInTable="columnfvdate" msprop:Generator_ColumnPropNameInRow="fvdate" msprop:Generator_ColumnPropNameInTable="fvdateColumn" msprop:Generator_UserColumnName="fvdate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="ffailure" msprop:Generator_ColumnVarNameInTable="columnffailure" msprop:Generator_ColumnPropNameInRow="ffailure" msprop:Generator_ColumnPropNameInTable="ffailureColumn" msprop:Generator_UserColumnName="ffailure" type="xs:short" />
              <xs:element name="xsuper" msprop:Generator_ColumnVarNameInTable="columnxsuper" msprop:Generator_ColumnPropNameInRow="xsuper" msprop:Generator_ColumnPropNameInTable="xsuperColumn" msprop:Generator_UserColumnName="xsuper" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="finpuser" msprop:Generator_ColumnVarNameInTable="columnfinpuser" msprop:Generator_ColumnPropNameInRow="finpuser" msprop:Generator_ColumnPropNameInTable="finpuserColumn" msprop:Generator_UserColumnName="finpuser">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="finpdate" msprop:Generator_ColumnVarNameInTable="columnfinpdate" msprop:Generator_ColumnPropNameInRow="finpdate" msprop:Generator_ColumnPropNameInTable="finpdateColumn" msprop:Generator_UserColumnName="finpdate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="fupduser" msprop:Generator_ColumnVarNameInTable="columnfupduser" msprop:Generator_ColumnPropNameInRow="fupduser" msprop:Generator_ColumnPropNameInTable="fupduserColumn" msprop:Generator_UserColumnName="fupduser">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fupddate" msprop:Generator_ColumnVarNameInTable="columnfupddate" msprop:Generator_ColumnPropNameInRow="fupddate" msprop:Generator_ColumnPropNameInTable="fupddateColumn" msprop:Generator_UserColumnName="fupddate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="fctldel" msprop:Generator_ColumnVarNameInTable="columnfctldel" msprop:Generator_ColumnPropNameInRow="fctldel" msprop:Generator_ColumnPropNameInTable="fctldelColumn" msprop:Generator_UserColumnName="fctldel">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:muser" />
      <xs:field xpath="mstns:fctlid" />
    </xs:unique>
  </xs:element>
</xs:schema>