<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="mprdr" targetNamespace="http://tempuri.org/mprdr.xsd" xmlns:mstns="http://tempuri.org/mprdr.xsd" xmlns="http://tempuri.org/mprdr.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="1" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="Settings" AppSettingsPropertyName="ConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="ConnectionString (Settings)" PropertyReference="ApplicationSettings.INS.Properties.Settings.GlobalReference.Default.ConnectionString" Provider="System.Data.Odbc" />
          <Connection AppSettingsObjectName="Settings" AppSettingsPropertyName="ConnectionString_alm" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="ConnectionString_alm (Settings)" PropertyReference="ApplicationSettings.INS.Properties.Settings.GlobalReference.Default.ConnectionString_alm" Provider="System.Data.Odbc" />
        </Connections>
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="mprdr" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="mprdr" msprop:Generator_UserDSName="mprdr">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="rprdr" msprop:Generator_TableClassName="rprdrDataTable" msprop:Generator_TableVarName="tablerprdr" msprop:Generator_RowChangedName="rprdrRowChanged" msprop:Generator_TablePropName="rprdr" msprop:Generator_RowDeletingName="rprdrRowDeleting" msprop:Generator_RowChangingName="rprdrRowChanging" msprop:Generator_RowEvHandlerName="rprdrRowChangeEventHandler" msprop:Generator_RowDeletedName="rprdrRowDeleted" msprop:Generator_RowClassName="rprdrRow" msprop:Generator_UserTableName="rprdr" msprop:Generator_RowEvArgName="rprdrRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ftype" msprop:Generator_ColumnVarNameInTable="columnftype" msprop:Generator_ColumnPropNameInRow="ftype" msprop:Generator_ColumnPropNameInTable="ftypeColumn" msprop:Generator_UserColumnName="ftype">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fctlid" msprop:Generator_ColumnVarNameInTable="columnfctlid" msprop:Generator_ColumnPropNameInRow="fctlid" msprop:Generator_ColumnPropNameInTable="fctlidColumn" msprop:Generator_UserColumnName="fctlid">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fid" msprop:Generator_ColumnVarNameInTable="columnfid" msprop:Generator_ColumnPropNameInRow="fid" msprop:Generator_ColumnPropNameInTable="fidColumn" msprop:Generator_UserColumnName="fid">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fmap" msprop:Generator_ColumnVarNameInTable="columnfmap" msprop:Generator_ColumnPropNameInRow="fmap" msprop:Generator_ColumnPropNameInTable="fmapColumn" msprop:Generator_UserColumnName="fmap">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fdescidx" msprop:Generator_ColumnVarNameInTable="columnfdescidx" msprop:Generator_ColumnPropNameInRow="fdescidx" msprop:Generator_ColumnPropNameInTable="fdescidxColumn" msprop:Generator_UserColumnName="fdescidx">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="60" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fdesc" msprop:Generator_ColumnVarNameInTable="columnfdesc" msprop:Generator_ColumnPropNameInRow="fdesc" msprop:Generator_ColumnPropNameInTable="fdescColumn" msprop:Generator_UserColumnName="fdesc">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="254" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fcname" msprop:Generator_ColumnVarNameInTable="columnfcname" msprop:Generator_ColumnPropNameInRow="fcname" msprop:Generator_ColumnPropNameInTable="fcnameColumn" msprop:Generator_UserColumnName="fcname">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="60" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="falias" msprop:Generator_ColumnVarNameInTable="columnfalias" msprop:Generator_ColumnPropNameInRow="falias" msprop:Generator_ColumnPropNameInTable="faliasColumn" msprop:Generator_UserColumnName="falias" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="60" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fadd1" msprop:Generator_ColumnVarNameInTable="columnfadd1" msprop:Generator_ColumnPropNameInRow="fadd1" msprop:Generator_ColumnPropNameInTable="fadd1Column" msprop:Generator_UserColumnName="fadd1">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="40" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fadd2" msprop:Generator_ColumnVarNameInTable="columnfadd2" msprop:Generator_ColumnPropNameInRow="fadd2" msprop:Generator_ColumnPropNameInTable="fadd2Column" msprop:Generator_UserColumnName="fadd2">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="40" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fadd3" msprop:Generator_ColumnVarNameInTable="columnfadd3" msprop:Generator_ColumnPropNameInRow="fadd3" msprop:Generator_ColumnPropNameInTable="fadd3Column" msprop:Generator_UserColumnName="fadd3">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="40" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fadd4" msprop:Generator_ColumnVarNameInTable="columnfadd4" msprop:Generator_ColumnPropNameInRow="fadd4" msprop:Generator_ColumnPropNameInTable="fadd4Column" msprop:Generator_UserColumnName="fadd4">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="40" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fpostcode" msprop:Generator_ColumnVarNameInTable="columnfpostcode" msprop:Generator_ColumnPropNameInRow="fpostcode" msprop:Generator_ColumnPropNameInTable="fpostcodeColumn" msprop:Generator_UserColumnName="fpostcode">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fcountry" msprop:Generator_ColumnVarNameInTable="columnfcountry" msprop:Generator_ColumnPropNameInRow="fcountry" msprop:Generator_ColumnPropNameInTable="fcountryColumn" msprop:Generator_UserColumnName="fcountry">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fregion" msprop:Generator_ColumnVarNameInTable="columnfregion" msprop:Generator_ColumnPropNameInRow="fregion" msprop:Generator_ColumnPropNameInTable="fregionColumn" msprop:Generator_UserColumnName="fregion">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ftel" msprop:Generator_ColumnVarNameInTable="columnftel" msprop:Generator_ColumnPropNameInRow="ftel" msprop:Generator_ColumnPropNameInTable="ftelColumn" msprop:Generator_UserColumnName="ftel">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ffax" msprop:Generator_ColumnVarNameInTable="columnffax" msprop:Generator_ColumnPropNameInRow="ffax" msprop:Generator_ColumnPropNameInTable="ffaxColumn" msprop:Generator_UserColumnName="ffax">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="femail" msprop:Generator_ColumnVarNameInTable="columnfemail" msprop:Generator_ColumnPropNameInRow="femail" msprop:Generator_ColumnPropNameInTable="femailColumn" msprop:Generator_UserColumnName="femail">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="frep" msprop:Generator_ColumnVarNameInTable="columnfrep" msprop:Generator_ColumnPropNameInRow="frep" msprop:Generator_ColumnPropNameInTable="frepColumn" msprop:Generator_UserColumnName="frep">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="finter" msprop:Generator_ColumnVarNameInTable="columnfinter" msprop:Generator_ColumnPropNameInRow="finter" msprop:Generator_ColumnPropNameInTable="finterColumn" msprop:Generator_UserColumnName="finter">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fsubside" msprop:Generator_ColumnVarNameInTable="columnfsubside" msprop:Generator_ColumnPropNameInRow="fsubside" msprop:Generator_ColumnPropNameInTable="fsubsideColumn" msprop:Generator_UserColumnName="fsubside">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fstatus" msprop:Generator_ColumnVarNameInTable="columnfstatus" msprop:Generator_ColumnPropNameInRow="fstatus" msprop:Generator_ColumnPropNameInTable="fstatusColumn" msprop:Generator_UserColumnName="fstatus" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fctldel" msprop:Generator_ColumnVarNameInTable="columnfctldel" msprop:Generator_ColumnPropNameInRow="fctldel" msprop:Generator_ColumnPropNameInTable="fctldelColumn" msprop:Generator_UserColumnName="fctldel">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="finpuser" msprop:Generator_ColumnVarNameInTable="columnfinpuser" msprop:Generator_ColumnPropNameInRow="finpuser" msprop:Generator_ColumnPropNameInTable="finpuserColumn" msprop:Generator_UserColumnName="finpuser">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="finpdate" msprop:Generator_ColumnVarNameInTable="columnfinpdate" msprop:Generator_ColumnPropNameInRow="finpdate" msprop:Generator_ColumnPropNameInTable="finpdateColumn" msprop:Generator_UserColumnName="finpdate" type="xs:dateTime" />
              <xs:element name="fupduser" msprop:Generator_ColumnVarNameInTable="columnfupduser" msprop:Generator_ColumnPropNameInRow="fupduser" msprop:Generator_ColumnPropNameInTable="fupduserColumn" msprop:Generator_UserColumnName="fupduser">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fupddate" msprop:Generator_ColumnVarNameInTable="columnfupddate" msprop:Generator_ColumnPropNameInRow="fupddate" msprop:Generator_ColumnPropNameInTable="fupddateColumn" msprop:Generator_UserColumnName="fupddate" type="xs:dateTime" />
              <xs:element name="id" msprop:Generator_ColumnVarNameInTable="columnid" msprop:Generator_ColumnPropNameInRow="id" msprop:Generator_ColumnPropNameInTable="idColumn" msprop:Generator_UserColumnName="id" type="xs:int" />
              <xs:element name="pinter" msprop:Generator_ColumnVarNameInTable="columnpinter" msprop:Generator_ColumnPropNameInRow="pinter" msprop:Generator_ColumnPropNameInTable="pinterColumn" msprop:Generator_UserColumnName="pinter" type="xs:string" minOccurs="0" />
              <xs:element name="psubside" msprop:Generator_ColumnVarNameInTable="columnpsubside" msprop:Generator_ColumnPropNameInRow="psubside" msprop:Generator_ColumnPropNameInTable="psubsideColumn" msprop:Generator_UserColumnName="psubside" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="rcontact" msprop:Generator_TableClassName="rcontactDataTable" msprop:Generator_TableVarName="tablercontact" msprop:Generator_RowChangedName="rcontactRowChanged" msprop:Generator_TablePropName="rcontact" msprop:Generator_RowDeletingName="rcontactRowDeleting" msprop:Generator_RowChangingName="rcontactRowChanging" msprop:Generator_RowEvHandlerName="rcontactRowChangeEventHandler" msprop:Generator_RowDeletedName="rcontactRowDeleted" msprop:Generator_RowClassName="rcontactRow" msprop:Generator_UserTableName="rcontact" msprop:Generator_RowEvArgName="rcontactRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="fctlid_1" msprop:Generator_ColumnVarNameInTable="columnfctlid_1" msprop:Generator_ColumnPropNameInRow="fctlid_1" msprop:Generator_ColumnPropNameInTable="fctlid_1Column" msprop:Generator_UserColumnName="fctlid_1">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fctlid" msprop:Generator_ColumnVarNameInTable="columnfctlid" msprop:Generator_ColumnPropNameInRow="fctlid" msprop:Generator_ColumnPropNameInTable="fctlidColumn" msprop:Generator_UserColumnName="fctlid">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fname" msprop:Generator_ColumnVarNameInTable="columnfname" msprop:Generator_ColumnPropNameInRow="fname" msprop:Generator_ColumnPropNameInTable="fnameColumn" msprop:Generator_UserColumnName="fname">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fpost" msprop:Generator_ColumnVarNameInTable="columnfpost" msprop:Generator_ColumnPropNameInRow="fpost" msprop:Generator_ColumnPropNameInTable="fpostColumn" msprop:Generator_UserColumnName="fpost">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ftel" msprop:Generator_ColumnVarNameInTable="columnftel" msprop:Generator_ColumnPropNameInRow="ftel" msprop:Generator_ColumnPropNameInTable="ftelColumn" msprop:Generator_UserColumnName="ftel">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ffax" msprop:Generator_ColumnVarNameInTable="columnffax" msprop:Generator_ColumnPropNameInRow="ffax" msprop:Generator_ColumnPropNameInTable="ffaxColumn" msprop:Generator_UserColumnName="ffax">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="femail" msprop:Generator_ColumnVarNameInTable="columnfemail" msprop:Generator_ColumnPropNameInRow="femail" msprop:Generator_ColumnPropNameInTable="femailColumn" msprop:Generator_UserColumnName="femail">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fctldel" msprop:Generator_ColumnVarNameInTable="columnfctldel" msprop:Generator_ColumnPropNameInRow="fctldel" msprop:Generator_ColumnPropNameInTable="fctldelColumn" msprop:Generator_UserColumnName="fctldel">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="rdept" msprop:Generator_TableClassName="rdeptDataTable" msprop:Generator_TableVarName="tablerdept" msprop:Generator_TablePropName="rdept" msprop:Generator_RowDeletingName="rdeptRowDeleting" msprop:Generator_RowChangingName="rdeptRowChanging" msprop:Generator_RowEvHandlerName="rdeptRowChangeEventHandler" msprop:Generator_RowDeletedName="rdeptRowDeleted" msprop:Generator_UserTableName="rdept" msprop:Generator_RowChangedName="rdeptRowChanged" msprop:Generator_RowEvArgName="rdeptRowChangeEvent" msprop:Generator_RowClassName="rdeptRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ftype" msprop:Generator_ColumnVarNameInTable="columnftype" msprop:Generator_ColumnPropNameInRow="ftype" msprop:Generator_ColumnPropNameInTable="ftypeColumn" msprop:Generator_UserColumnName="ftype">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fctlid_1" msprop:Generator_ColumnVarNameInTable="columnfctlid_1" msprop:Generator_ColumnPropNameInRow="fctlid_1" msprop:Generator_ColumnPropNameInTable="fctlid_1Column" msprop:Generator_UserColumnName="fctlid_1">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fctlid" msprop:Generator_ColumnVarNameInTable="columnfctlid" msprop:Generator_ColumnPropNameInRow="fctlid" msprop:Generator_ColumnPropNameInTable="fctlidColumn" msprop:Generator_UserColumnName="fctlid">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fid_1" msprop:Generator_ColumnVarNameInTable="columnfid_1" msprop:Generator_ColumnPropNameInRow="fid_1" msprop:Generator_ColumnPropNameInTable="fid_1Column" msprop:Generator_UserColumnName="fid_1">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fid" msprop:Generator_ColumnVarNameInTable="columnfid" msprop:Generator_ColumnPropNameInRow="fid" msprop:Generator_ColumnPropNameInTable="fidColumn" msprop:Generator_UserColumnName="fid">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="3" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fname" msprop:Generator_ColumnVarNameInTable="columnfname" msprop:Generator_ColumnPropNameInRow="fname" msprop:Generator_ColumnPropNameInTable="fnameColumn" msprop:Generator_UserColumnName="fname">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="falias" msprop:Generator_ColumnVarNameInTable="columnfalias" msprop:Generator_ColumnPropNameInRow="falias" msprop:Generator_ColumnPropNameInTable="faliasColumn" msprop:Generator_UserColumnName="falias" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fmap" msprop:Generator_ColumnVarNameInTable="columnfmap" msprop:Generator_ColumnPropNameInRow="fmap" msprop:Generator_ColumnPropNameInTable="fmapColumn" msprop:Generator_UserColumnName="fmap" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="3" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="fnewmap" msprop:Generator_ColumnVarNameInTable="columnfnewmap" msprop:Generator_ColumnPropNameInRow="fnewmap" msprop:Generator_ColumnPropNameInTable="fnewmapColumn" msprop:Generator_UserColumnName="fnewmap" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="3" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>