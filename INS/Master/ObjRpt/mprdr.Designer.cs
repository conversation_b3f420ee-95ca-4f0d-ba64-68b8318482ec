//------------------------------------------------------------------------------
// <auto-generated>
//     這段程式碼是由工具產生的。
//     執行階段版本:4.0.30319.34209
//
//     對這個檔案所做的變更可能會造成錯誤的行為，而且如果重新產生程式碼，
//     變更將會遺失。
// </auto-generated>
//------------------------------------------------------------------------------

#pragma warning disable 1591

namespace INS.Master.ObjRpt {
    
    
    /// <summary>
    ///Represents a strongly typed in-memory cache of data.
    ///</summary>
    [global::System.Serializable()]
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedDataSetSchema")]
    [global::System.Xml.Serialization.XmlRootAttribute("mprdr")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.DataSet")]
    public partial class mprdr : global::System.Data.DataSet {
        
        private rprdrDataTable tablerprdr;
        
        private rcontactDataTable tablercontact;
        
        private rdeptDataTable tablerdept;
        
        private global::System.Data.SchemaSerializationMode _schemaSerializationMode = global::System.Data.SchemaSerializationMode.IncludeSchema;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        public mprdr() {
            this.BeginInit();
            this.InitClass();
            global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
            base.Tables.CollectionChanged += schemaChangedHandler;
            base.Relations.CollectionChanged += schemaChangedHandler;
            this.EndInit();
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        protected mprdr(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                base(info, context, false) {
            if ((this.IsBinarySerialized(info, context) == true)) {
                this.InitVars(false);
                global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler1 = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
                this.Tables.CollectionChanged += schemaChangedHandler1;
                this.Relations.CollectionChanged += schemaChangedHandler1;
                return;
            }
            string strSchema = ((string)(info.GetValue("XmlSchema", typeof(string))));
            if ((this.DetermineSchemaSerializationMode(info, context) == global::System.Data.SchemaSerializationMode.IncludeSchema)) {
                global::System.Data.DataSet ds = new global::System.Data.DataSet();
                ds.ReadXmlSchema(new global::System.Xml.XmlTextReader(new global::System.IO.StringReader(strSchema)));
                if ((ds.Tables["rprdr"] != null)) {
                    base.Tables.Add(new rprdrDataTable(ds.Tables["rprdr"]));
                }
                if ((ds.Tables["rcontact"] != null)) {
                    base.Tables.Add(new rcontactDataTable(ds.Tables["rcontact"]));
                }
                if ((ds.Tables["rdept"] != null)) {
                    base.Tables.Add(new rdeptDataTable(ds.Tables["rdept"]));
                }
                this.DataSetName = ds.DataSetName;
                this.Prefix = ds.Prefix;
                this.Namespace = ds.Namespace;
                this.Locale = ds.Locale;
                this.CaseSensitive = ds.CaseSensitive;
                this.EnforceConstraints = ds.EnforceConstraints;
                this.Merge(ds, false, global::System.Data.MissingSchemaAction.Add);
                this.InitVars();
            }
            else {
                this.ReadXmlSchema(new global::System.Xml.XmlTextReader(new global::System.IO.StringReader(strSchema)));
            }
            this.GetSerializationData(info, context);
            global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
            base.Tables.CollectionChanged += schemaChangedHandler;
            this.Relations.CollectionChanged += schemaChangedHandler;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public rprdrDataTable rprdr {
            get {
                return this.tablerprdr;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public rcontactDataTable rcontact {
            get {
                return this.tablercontact;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public rdeptDataTable rdept {
            get {
                return this.tablerdept;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        [global::System.ComponentModel.BrowsableAttribute(true)]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Visible)]
        public override global::System.Data.SchemaSerializationMode SchemaSerializationMode {
            get {
                return this._schemaSerializationMode;
            }
            set {
                this._schemaSerializationMode = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public new global::System.Data.DataTableCollection Tables {
            get {
                return base.Tables;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public new global::System.Data.DataRelationCollection Relations {
            get {
                return base.Relations;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        protected override void InitializeDerivedDataSet() {
            this.BeginInit();
            this.InitClass();
            this.EndInit();
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        public override global::System.Data.DataSet Clone() {
            mprdr cln = ((mprdr)(base.Clone()));
            cln.InitVars();
            cln.SchemaSerializationMode = this.SchemaSerializationMode;
            return cln;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        protected override bool ShouldSerializeTables() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        protected override bool ShouldSerializeRelations() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        protected override void ReadXmlSerializable(global::System.Xml.XmlReader reader) {
            if ((this.DetermineSchemaSerializationMode(reader) == global::System.Data.SchemaSerializationMode.IncludeSchema)) {
                this.Reset();
                global::System.Data.DataSet ds = new global::System.Data.DataSet();
                ds.ReadXml(reader);
                if ((ds.Tables["rprdr"] != null)) {
                    base.Tables.Add(new rprdrDataTable(ds.Tables["rprdr"]));
                }
                if ((ds.Tables["rcontact"] != null)) {
                    base.Tables.Add(new rcontactDataTable(ds.Tables["rcontact"]));
                }
                if ((ds.Tables["rdept"] != null)) {
                    base.Tables.Add(new rdeptDataTable(ds.Tables["rdept"]));
                }
                this.DataSetName = ds.DataSetName;
                this.Prefix = ds.Prefix;
                this.Namespace = ds.Namespace;
                this.Locale = ds.Locale;
                this.CaseSensitive = ds.CaseSensitive;
                this.EnforceConstraints = ds.EnforceConstraints;
                this.Merge(ds, false, global::System.Data.MissingSchemaAction.Add);
                this.InitVars();
            }
            else {
                this.ReadXml(reader);
                this.InitVars();
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        protected override global::System.Xml.Schema.XmlSchema GetSchemaSerializable() {
            global::System.IO.MemoryStream stream = new global::System.IO.MemoryStream();
            this.WriteXmlSchema(new global::System.Xml.XmlTextWriter(stream, null));
            stream.Position = 0;
            return global::System.Xml.Schema.XmlSchema.Read(new global::System.Xml.XmlTextReader(stream), null);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        internal void InitVars() {
            this.InitVars(true);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        internal void InitVars(bool initTable) {
            this.tablerprdr = ((rprdrDataTable)(base.Tables["rprdr"]));
            if ((initTable == true)) {
                if ((this.tablerprdr != null)) {
                    this.tablerprdr.InitVars();
                }
            }
            this.tablercontact = ((rcontactDataTable)(base.Tables["rcontact"]));
            if ((initTable == true)) {
                if ((this.tablercontact != null)) {
                    this.tablercontact.InitVars();
                }
            }
            this.tablerdept = ((rdeptDataTable)(base.Tables["rdept"]));
            if ((initTable == true)) {
                if ((this.tablerdept != null)) {
                    this.tablerdept.InitVars();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        private void InitClass() {
            this.DataSetName = "mprdr";
            this.Prefix = "";
            this.Namespace = "http://tempuri.org/mprdr.xsd";
            this.EnforceConstraints = true;
            this.SchemaSerializationMode = global::System.Data.SchemaSerializationMode.IncludeSchema;
            this.tablerprdr = new rprdrDataTable();
            base.Tables.Add(this.tablerprdr);
            this.tablercontact = new rcontactDataTable();
            base.Tables.Add(this.tablercontact);
            this.tablerdept = new rdeptDataTable();
            base.Tables.Add(this.tablerdept);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        private bool ShouldSerializerprdr() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        private bool ShouldSerializercontact() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        private bool ShouldSerializerdept() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        private void SchemaChanged(object sender, global::System.ComponentModel.CollectionChangeEventArgs e) {
            if ((e.Action == global::System.ComponentModel.CollectionChangeAction.Remove)) {
                this.InitVars();
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedDataSetSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
            mprdr ds = new mprdr();
            global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
            global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
            global::System.Xml.Schema.XmlSchemaAny any = new global::System.Xml.Schema.XmlSchemaAny();
            any.Namespace = ds.Namespace;
            sequence.Items.Add(any);
            type.Particle = sequence;
            global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
            if (xs.Contains(dsSchema.TargetNamespace)) {
                global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                try {
                    global::System.Xml.Schema.XmlSchema schema = null;
                    dsSchema.Write(s1);
                    for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                        schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                        s2.SetLength(0);
                        schema.Write(s2);
                        if ((s1.Length == s2.Length)) {
                            s1.Position = 0;
                            s2.Position = 0;
                            for (; ((s1.Position != s1.Length) 
                                        && (s1.ReadByte() == s2.ReadByte())); ) {
                                ;
                            }
                            if ((s1.Position == s1.Length)) {
                                return type;
                            }
                        }
                    }
                }
                finally {
                    if ((s1 != null)) {
                        s1.Close();
                    }
                    if ((s2 != null)) {
                        s2.Close();
                    }
                }
            }
            xs.Add(dsSchema);
            return type;
        }
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        public delegate void rprdrRowChangeEventHandler(object sender, rprdrRowChangeEvent e);
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        public delegate void rcontactRowChangeEventHandler(object sender, rcontactRowChangeEvent e);
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        public delegate void rdeptRowChangeEventHandler(object sender, rdeptRowChangeEvent e);
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class rprdrDataTable : global::System.Data.TypedTableBase<rprdrRow> {
            
            private global::System.Data.DataColumn columnftype;
            
            private global::System.Data.DataColumn columnfctlid;
            
            private global::System.Data.DataColumn columnfid;
            
            private global::System.Data.DataColumn columnfmap;
            
            private global::System.Data.DataColumn columnfdescidx;
            
            private global::System.Data.DataColumn columnfdesc;
            
            private global::System.Data.DataColumn columnfcname;
            
            private global::System.Data.DataColumn columnfalias;
            
            private global::System.Data.DataColumn columnfadd1;
            
            private global::System.Data.DataColumn columnfadd2;
            
            private global::System.Data.DataColumn columnfadd3;
            
            private global::System.Data.DataColumn columnfadd4;
            
            private global::System.Data.DataColumn columnfpostcode;
            
            private global::System.Data.DataColumn columnfcountry;
            
            private global::System.Data.DataColumn columnfregion;
            
            private global::System.Data.DataColumn columnftel;
            
            private global::System.Data.DataColumn columnffax;
            
            private global::System.Data.DataColumn columnfemail;
            
            private global::System.Data.DataColumn columnfrep;
            
            private global::System.Data.DataColumn columnfinter;
            
            private global::System.Data.DataColumn columnfsubside;
            
            private global::System.Data.DataColumn columnfstatus;
            
            private global::System.Data.DataColumn columnfctldel;
            
            private global::System.Data.DataColumn columnfinpuser;
            
            private global::System.Data.DataColumn columnfinpdate;
            
            private global::System.Data.DataColumn columnfupduser;
            
            private global::System.Data.DataColumn columnfupddate;
            
            private global::System.Data.DataColumn columnid;
            
            private global::System.Data.DataColumn columnpinter;
            
            private global::System.Data.DataColumn columnpsubside;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public rprdrDataTable() {
                this.TableName = "rprdr";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            internal rprdrDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected rprdrDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn ftypeColumn {
                get {
                    return this.columnftype;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fctlidColumn {
                get {
                    return this.columnfctlid;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fidColumn {
                get {
                    return this.columnfid;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fmapColumn {
                get {
                    return this.columnfmap;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fdescidxColumn {
                get {
                    return this.columnfdescidx;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fdescColumn {
                get {
                    return this.columnfdesc;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fcnameColumn {
                get {
                    return this.columnfcname;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn faliasColumn {
                get {
                    return this.columnfalias;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fadd1Column {
                get {
                    return this.columnfadd1;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fadd2Column {
                get {
                    return this.columnfadd2;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fadd3Column {
                get {
                    return this.columnfadd3;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fadd4Column {
                get {
                    return this.columnfadd4;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fpostcodeColumn {
                get {
                    return this.columnfpostcode;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fcountryColumn {
                get {
                    return this.columnfcountry;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fregionColumn {
                get {
                    return this.columnfregion;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn ftelColumn {
                get {
                    return this.columnftel;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn ffaxColumn {
                get {
                    return this.columnffax;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn femailColumn {
                get {
                    return this.columnfemail;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn frepColumn {
                get {
                    return this.columnfrep;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn finterColumn {
                get {
                    return this.columnfinter;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fsubsideColumn {
                get {
                    return this.columnfsubside;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fstatusColumn {
                get {
                    return this.columnfstatus;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fctldelColumn {
                get {
                    return this.columnfctldel;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn finpuserColumn {
                get {
                    return this.columnfinpuser;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn finpdateColumn {
                get {
                    return this.columnfinpdate;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fupduserColumn {
                get {
                    return this.columnfupduser;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fupddateColumn {
                get {
                    return this.columnfupddate;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn idColumn {
                get {
                    return this.columnid;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn pinterColumn {
                get {
                    return this.columnpinter;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn psubsideColumn {
                get {
                    return this.columnpsubside;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public rprdrRow this[int index] {
                get {
                    return ((rprdrRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public event rprdrRowChangeEventHandler rprdrRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public event rprdrRowChangeEventHandler rprdrRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public event rprdrRowChangeEventHandler rprdrRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public event rprdrRowChangeEventHandler rprdrRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void AddrprdrRow(rprdrRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public rprdrRow AddrprdrRow(
                        string ftype, 
                        string fctlid, 
                        string fid, 
                        string fmap, 
                        string fdescidx, 
                        string fdesc, 
                        string fcname, 
                        string falias, 
                        string fadd1, 
                        string fadd2, 
                        string fadd3, 
                        string fadd4, 
                        string fpostcode, 
                        string fcountry, 
                        string fregion, 
                        string ftel, 
                        string ffax, 
                        string femail, 
                        string frep, 
                        string finter, 
                        string fsubside, 
                        string fstatus, 
                        string fctldel, 
                        string finpuser, 
                        System.DateTime finpdate, 
                        string fupduser, 
                        System.DateTime fupddate, 
                        int id, 
                        string pinter, 
                        string psubside) {
                rprdrRow rowrprdrRow = ((rprdrRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        ftype,
                        fctlid,
                        fid,
                        fmap,
                        fdescidx,
                        fdesc,
                        fcname,
                        falias,
                        fadd1,
                        fadd2,
                        fadd3,
                        fadd4,
                        fpostcode,
                        fcountry,
                        fregion,
                        ftel,
                        ffax,
                        femail,
                        frep,
                        finter,
                        fsubside,
                        fstatus,
                        fctldel,
                        finpuser,
                        finpdate,
                        fupduser,
                        fupddate,
                        id,
                        pinter,
                        psubside};
                rowrprdrRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowrprdrRow);
                return rowrprdrRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public override global::System.Data.DataTable Clone() {
                rprdrDataTable cln = ((rprdrDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new rprdrDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            internal void InitVars() {
                this.columnftype = base.Columns["ftype"];
                this.columnfctlid = base.Columns["fctlid"];
                this.columnfid = base.Columns["fid"];
                this.columnfmap = base.Columns["fmap"];
                this.columnfdescidx = base.Columns["fdescidx"];
                this.columnfdesc = base.Columns["fdesc"];
                this.columnfcname = base.Columns["fcname"];
                this.columnfalias = base.Columns["falias"];
                this.columnfadd1 = base.Columns["fadd1"];
                this.columnfadd2 = base.Columns["fadd2"];
                this.columnfadd3 = base.Columns["fadd3"];
                this.columnfadd4 = base.Columns["fadd4"];
                this.columnfpostcode = base.Columns["fpostcode"];
                this.columnfcountry = base.Columns["fcountry"];
                this.columnfregion = base.Columns["fregion"];
                this.columnftel = base.Columns["ftel"];
                this.columnffax = base.Columns["ffax"];
                this.columnfemail = base.Columns["femail"];
                this.columnfrep = base.Columns["frep"];
                this.columnfinter = base.Columns["finter"];
                this.columnfsubside = base.Columns["fsubside"];
                this.columnfstatus = base.Columns["fstatus"];
                this.columnfctldel = base.Columns["fctldel"];
                this.columnfinpuser = base.Columns["finpuser"];
                this.columnfinpdate = base.Columns["finpdate"];
                this.columnfupduser = base.Columns["fupduser"];
                this.columnfupddate = base.Columns["fupddate"];
                this.columnid = base.Columns["id"];
                this.columnpinter = base.Columns["pinter"];
                this.columnpsubside = base.Columns["psubside"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            private void InitClass() {
                this.columnftype = new global::System.Data.DataColumn("ftype", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnftype);
                this.columnfctlid = new global::System.Data.DataColumn("fctlid", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfctlid);
                this.columnfid = new global::System.Data.DataColumn("fid", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfid);
                this.columnfmap = new global::System.Data.DataColumn("fmap", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfmap);
                this.columnfdescidx = new global::System.Data.DataColumn("fdescidx", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfdescidx);
                this.columnfdesc = new global::System.Data.DataColumn("fdesc", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfdesc);
                this.columnfcname = new global::System.Data.DataColumn("fcname", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfcname);
                this.columnfalias = new global::System.Data.DataColumn("falias", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfalias);
                this.columnfadd1 = new global::System.Data.DataColumn("fadd1", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfadd1);
                this.columnfadd2 = new global::System.Data.DataColumn("fadd2", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfadd2);
                this.columnfadd3 = new global::System.Data.DataColumn("fadd3", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfadd3);
                this.columnfadd4 = new global::System.Data.DataColumn("fadd4", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfadd4);
                this.columnfpostcode = new global::System.Data.DataColumn("fpostcode", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfpostcode);
                this.columnfcountry = new global::System.Data.DataColumn("fcountry", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfcountry);
                this.columnfregion = new global::System.Data.DataColumn("fregion", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfregion);
                this.columnftel = new global::System.Data.DataColumn("ftel", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnftel);
                this.columnffax = new global::System.Data.DataColumn("ffax", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnffax);
                this.columnfemail = new global::System.Data.DataColumn("femail", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfemail);
                this.columnfrep = new global::System.Data.DataColumn("frep", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfrep);
                this.columnfinter = new global::System.Data.DataColumn("finter", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfinter);
                this.columnfsubside = new global::System.Data.DataColumn("fsubside", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfsubside);
                this.columnfstatus = new global::System.Data.DataColumn("fstatus", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfstatus);
                this.columnfctldel = new global::System.Data.DataColumn("fctldel", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfctldel);
                this.columnfinpuser = new global::System.Data.DataColumn("finpuser", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfinpuser);
                this.columnfinpdate = new global::System.Data.DataColumn("finpdate", typeof(global::System.DateTime), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfinpdate);
                this.columnfupduser = new global::System.Data.DataColumn("fupduser", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfupduser);
                this.columnfupddate = new global::System.Data.DataColumn("fupddate", typeof(global::System.DateTime), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfupddate);
                this.columnid = new global::System.Data.DataColumn("id", typeof(int), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnid);
                this.columnpinter = new global::System.Data.DataColumn("pinter", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnpinter);
                this.columnpsubside = new global::System.Data.DataColumn("psubside", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnpsubside);
                this.columnftype.AllowDBNull = false;
                this.columnftype.MaxLength = 1;
                this.columnfctlid.AllowDBNull = false;
                this.columnfctlid.MaxLength = 10;
                this.columnfid.AllowDBNull = false;
                this.columnfid.MaxLength = 10;
                this.columnfmap.AllowDBNull = false;
                this.columnfmap.MaxLength = 10;
                this.columnfdescidx.AllowDBNull = false;
                this.columnfdescidx.MaxLength = 60;
                this.columnfdesc.AllowDBNull = false;
                this.columnfdesc.MaxLength = 254;
                this.columnfcname.AllowDBNull = false;
                this.columnfcname.MaxLength = 60;
                this.columnfalias.MaxLength = 60;
                this.columnfadd1.AllowDBNull = false;
                this.columnfadd1.MaxLength = 40;
                this.columnfadd2.AllowDBNull = false;
                this.columnfadd2.MaxLength = 40;
                this.columnfadd3.AllowDBNull = false;
                this.columnfadd3.MaxLength = 40;
                this.columnfadd4.AllowDBNull = false;
                this.columnfadd4.MaxLength = 40;
                this.columnfpostcode.AllowDBNull = false;
                this.columnfpostcode.MaxLength = 10;
                this.columnfcountry.AllowDBNull = false;
                this.columnfcountry.MaxLength = 20;
                this.columnfregion.AllowDBNull = false;
                this.columnfregion.MaxLength = 20;
                this.columnftel.AllowDBNull = false;
                this.columnftel.MaxLength = 15;
                this.columnffax.AllowDBNull = false;
                this.columnffax.MaxLength = 15;
                this.columnfemail.AllowDBNull = false;
                this.columnfemail.MaxLength = 20;
                this.columnfrep.AllowDBNull = false;
                this.columnfrep.MaxLength = 20;
                this.columnfinter.AllowDBNull = false;
                this.columnfinter.MaxLength = 1;
                this.columnfsubside.AllowDBNull = false;
                this.columnfsubside.MaxLength = 1;
                this.columnfstatus.MaxLength = 1;
                this.columnfctldel.AllowDBNull = false;
                this.columnfctldel.MaxLength = 10;
                this.columnfinpuser.AllowDBNull = false;
                this.columnfinpuser.MaxLength = 10;
                this.columnfinpdate.AllowDBNull = false;
                this.columnfupduser.AllowDBNull = false;
                this.columnfupduser.MaxLength = 10;
                this.columnfupddate.AllowDBNull = false;
                this.columnid.AllowDBNull = false;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public rprdrRow NewrprdrRow() {
                return ((rprdrRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new rprdrRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override global::System.Type GetRowType() {
                return typeof(rprdrRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.rprdrRowChanged != null)) {
                    this.rprdrRowChanged(this, new rprdrRowChangeEvent(((rprdrRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.rprdrRowChanging != null)) {
                    this.rprdrRowChanging(this, new rprdrRowChangeEvent(((rprdrRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.rprdrRowDeleted != null)) {
                    this.rprdrRowDeleted(this, new rprdrRowChangeEvent(((rprdrRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.rprdrRowDeleting != null)) {
                    this.rprdrRowDeleting(this, new rprdrRowChangeEvent(((rprdrRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void RemoverprdrRow(rprdrRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                mprdr ds = new mprdr();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "rprdrDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class rcontactDataTable : global::System.Data.TypedTableBase<rcontactRow> {
            
            private global::System.Data.DataColumn columnfctlid_1;
            
            private global::System.Data.DataColumn columnfctlid;
            
            private global::System.Data.DataColumn columnfname;
            
            private global::System.Data.DataColumn columnfpost;
            
            private global::System.Data.DataColumn columnftel;
            
            private global::System.Data.DataColumn columnffax;
            
            private global::System.Data.DataColumn columnfemail;
            
            private global::System.Data.DataColumn columnfctldel;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public rcontactDataTable() {
                this.TableName = "rcontact";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            internal rcontactDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected rcontactDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fctlid_1Column {
                get {
                    return this.columnfctlid_1;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fctlidColumn {
                get {
                    return this.columnfctlid;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fnameColumn {
                get {
                    return this.columnfname;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fpostColumn {
                get {
                    return this.columnfpost;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn ftelColumn {
                get {
                    return this.columnftel;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn ffaxColumn {
                get {
                    return this.columnffax;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn femailColumn {
                get {
                    return this.columnfemail;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fctldelColumn {
                get {
                    return this.columnfctldel;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public rcontactRow this[int index] {
                get {
                    return ((rcontactRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public event rcontactRowChangeEventHandler rcontactRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public event rcontactRowChangeEventHandler rcontactRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public event rcontactRowChangeEventHandler rcontactRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public event rcontactRowChangeEventHandler rcontactRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void AddrcontactRow(rcontactRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public rcontactRow AddrcontactRow(string fctlid_1, string fctlid, string fname, string fpost, string ftel, string ffax, string femail, string fctldel) {
                rcontactRow rowrcontactRow = ((rcontactRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        fctlid_1,
                        fctlid,
                        fname,
                        fpost,
                        ftel,
                        ffax,
                        femail,
                        fctldel};
                rowrcontactRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowrcontactRow);
                return rowrcontactRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public override global::System.Data.DataTable Clone() {
                rcontactDataTable cln = ((rcontactDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new rcontactDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            internal void InitVars() {
                this.columnfctlid_1 = base.Columns["fctlid_1"];
                this.columnfctlid = base.Columns["fctlid"];
                this.columnfname = base.Columns["fname"];
                this.columnfpost = base.Columns["fpost"];
                this.columnftel = base.Columns["ftel"];
                this.columnffax = base.Columns["ffax"];
                this.columnfemail = base.Columns["femail"];
                this.columnfctldel = base.Columns["fctldel"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            private void InitClass() {
                this.columnfctlid_1 = new global::System.Data.DataColumn("fctlid_1", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfctlid_1);
                this.columnfctlid = new global::System.Data.DataColumn("fctlid", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfctlid);
                this.columnfname = new global::System.Data.DataColumn("fname", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfname);
                this.columnfpost = new global::System.Data.DataColumn("fpost", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfpost);
                this.columnftel = new global::System.Data.DataColumn("ftel", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnftel);
                this.columnffax = new global::System.Data.DataColumn("ffax", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnffax);
                this.columnfemail = new global::System.Data.DataColumn("femail", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfemail);
                this.columnfctldel = new global::System.Data.DataColumn("fctldel", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfctldel);
                this.columnfctlid_1.AllowDBNull = false;
                this.columnfctlid_1.MaxLength = 10;
                this.columnfctlid.AllowDBNull = false;
                this.columnfctlid.MaxLength = 10;
                this.columnfname.AllowDBNull = false;
                this.columnfname.MaxLength = 30;
                this.columnfpost.AllowDBNull = false;
                this.columnfpost.MaxLength = 20;
                this.columnftel.AllowDBNull = false;
                this.columnftel.MaxLength = 15;
                this.columnffax.AllowDBNull = false;
                this.columnffax.MaxLength = 15;
                this.columnfemail.AllowDBNull = false;
                this.columnfemail.MaxLength = 30;
                this.columnfctldel.AllowDBNull = false;
                this.columnfctldel.MaxLength = 10;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public rcontactRow NewrcontactRow() {
                return ((rcontactRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new rcontactRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override global::System.Type GetRowType() {
                return typeof(rcontactRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.rcontactRowChanged != null)) {
                    this.rcontactRowChanged(this, new rcontactRowChangeEvent(((rcontactRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.rcontactRowChanging != null)) {
                    this.rcontactRowChanging(this, new rcontactRowChangeEvent(((rcontactRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.rcontactRowDeleted != null)) {
                    this.rcontactRowDeleted(this, new rcontactRowChangeEvent(((rcontactRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.rcontactRowDeleting != null)) {
                    this.rcontactRowDeleting(this, new rcontactRowChangeEvent(((rcontactRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void RemovercontactRow(rcontactRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                mprdr ds = new mprdr();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "rcontactDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class rdeptDataTable : global::System.Data.TypedTableBase<rdeptRow> {
            
            private global::System.Data.DataColumn columnftype;
            
            private global::System.Data.DataColumn columnfctlid_1;
            
            private global::System.Data.DataColumn columnfctlid;
            
            private global::System.Data.DataColumn columnfid_1;
            
            private global::System.Data.DataColumn columnfid;
            
            private global::System.Data.DataColumn columnfname;
            
            private global::System.Data.DataColumn columnfalias;
            
            private global::System.Data.DataColumn columnfmap;
            
            private global::System.Data.DataColumn columnfnewmap;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public rdeptDataTable() {
                this.TableName = "rdept";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            internal rdeptDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected rdeptDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn ftypeColumn {
                get {
                    return this.columnftype;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fctlid_1Column {
                get {
                    return this.columnfctlid_1;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fctlidColumn {
                get {
                    return this.columnfctlid;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fid_1Column {
                get {
                    return this.columnfid_1;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fidColumn {
                get {
                    return this.columnfid;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fnameColumn {
                get {
                    return this.columnfname;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn faliasColumn {
                get {
                    return this.columnfalias;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fmapColumn {
                get {
                    return this.columnfmap;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn fnewmapColumn {
                get {
                    return this.columnfnewmap;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public rdeptRow this[int index] {
                get {
                    return ((rdeptRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public event rdeptRowChangeEventHandler rdeptRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public event rdeptRowChangeEventHandler rdeptRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public event rdeptRowChangeEventHandler rdeptRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public event rdeptRowChangeEventHandler rdeptRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void AddrdeptRow(rdeptRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public rdeptRow AddrdeptRow(string ftype, string fctlid_1, string fctlid, string fid_1, string fid, string fname, string falias, string fmap, string fnewmap) {
                rdeptRow rowrdeptRow = ((rdeptRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        ftype,
                        fctlid_1,
                        fctlid,
                        fid_1,
                        fid,
                        fname,
                        falias,
                        fmap,
                        fnewmap};
                rowrdeptRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowrdeptRow);
                return rowrdeptRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public override global::System.Data.DataTable Clone() {
                rdeptDataTable cln = ((rdeptDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new rdeptDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            internal void InitVars() {
                this.columnftype = base.Columns["ftype"];
                this.columnfctlid_1 = base.Columns["fctlid_1"];
                this.columnfctlid = base.Columns["fctlid"];
                this.columnfid_1 = base.Columns["fid_1"];
                this.columnfid = base.Columns["fid"];
                this.columnfname = base.Columns["fname"];
                this.columnfalias = base.Columns["falias"];
                this.columnfmap = base.Columns["fmap"];
                this.columnfnewmap = base.Columns["fnewmap"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            private void InitClass() {
                this.columnftype = new global::System.Data.DataColumn("ftype", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnftype);
                this.columnfctlid_1 = new global::System.Data.DataColumn("fctlid_1", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfctlid_1);
                this.columnfctlid = new global::System.Data.DataColumn("fctlid", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfctlid);
                this.columnfid_1 = new global::System.Data.DataColumn("fid_1", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfid_1);
                this.columnfid = new global::System.Data.DataColumn("fid", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfid);
                this.columnfname = new global::System.Data.DataColumn("fname", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfname);
                this.columnfalias = new global::System.Data.DataColumn("falias", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfalias);
                this.columnfmap = new global::System.Data.DataColumn("fmap", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfmap);
                this.columnfnewmap = new global::System.Data.DataColumn("fnewmap", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnfnewmap);
                this.columnftype.AllowDBNull = false;
                this.columnftype.MaxLength = 1;
                this.columnfctlid_1.AllowDBNull = false;
                this.columnfctlid_1.MaxLength = 10;
                this.columnfctlid.AllowDBNull = false;
                this.columnfctlid.MaxLength = 10;
                this.columnfid_1.AllowDBNull = false;
                this.columnfid_1.MaxLength = 10;
                this.columnfid.AllowDBNull = false;
                this.columnfid.MaxLength = 3;
                this.columnfname.AllowDBNull = false;
                this.columnfname.MaxLength = 30;
                this.columnfalias.MaxLength = 30;
                this.columnfmap.MaxLength = 3;
                this.columnfnewmap.MaxLength = 3;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public rdeptRow NewrdeptRow() {
                return ((rdeptRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new rdeptRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override global::System.Type GetRowType() {
                return typeof(rdeptRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.rdeptRowChanged != null)) {
                    this.rdeptRowChanged(this, new rdeptRowChangeEvent(((rdeptRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.rdeptRowChanging != null)) {
                    this.rdeptRowChanging(this, new rdeptRowChangeEvent(((rdeptRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.rdeptRowDeleted != null)) {
                    this.rdeptRowDeleted(this, new rdeptRowChangeEvent(((rdeptRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.rdeptRowDeleting != null)) {
                    this.rdeptRowDeleting(this, new rdeptRowChangeEvent(((rdeptRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void RemoverdeptRow(rdeptRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                mprdr ds = new mprdr();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "rdeptDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class rprdrRow : global::System.Data.DataRow {
            
            private rprdrDataTable tablerprdr;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            internal rprdrRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tablerprdr = ((rprdrDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string ftype {
                get {
                    return ((string)(this[this.tablerprdr.ftypeColumn]));
                }
                set {
                    this[this.tablerprdr.ftypeColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fctlid {
                get {
                    return ((string)(this[this.tablerprdr.fctlidColumn]));
                }
                set {
                    this[this.tablerprdr.fctlidColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fid {
                get {
                    return ((string)(this[this.tablerprdr.fidColumn]));
                }
                set {
                    this[this.tablerprdr.fidColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fmap {
                get {
                    return ((string)(this[this.tablerprdr.fmapColumn]));
                }
                set {
                    this[this.tablerprdr.fmapColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fdescidx {
                get {
                    return ((string)(this[this.tablerprdr.fdescidxColumn]));
                }
                set {
                    this[this.tablerprdr.fdescidxColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fdesc {
                get {
                    return ((string)(this[this.tablerprdr.fdescColumn]));
                }
                set {
                    this[this.tablerprdr.fdescColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fcname {
                get {
                    return ((string)(this[this.tablerprdr.fcnameColumn]));
                }
                set {
                    this[this.tablerprdr.fcnameColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string falias {
                get {
                    try {
                        return ((string)(this[this.tablerprdr.faliasColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("資料表 \'rprdr\' 中資料行 \'falias\' 的值是 DBNull。", e);
                    }
                }
                set {
                    this[this.tablerprdr.faliasColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fadd1 {
                get {
                    return ((string)(this[this.tablerprdr.fadd1Column]));
                }
                set {
                    this[this.tablerprdr.fadd1Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fadd2 {
                get {
                    return ((string)(this[this.tablerprdr.fadd2Column]));
                }
                set {
                    this[this.tablerprdr.fadd2Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fadd3 {
                get {
                    return ((string)(this[this.tablerprdr.fadd3Column]));
                }
                set {
                    this[this.tablerprdr.fadd3Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fadd4 {
                get {
                    return ((string)(this[this.tablerprdr.fadd4Column]));
                }
                set {
                    this[this.tablerprdr.fadd4Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fpostcode {
                get {
                    return ((string)(this[this.tablerprdr.fpostcodeColumn]));
                }
                set {
                    this[this.tablerprdr.fpostcodeColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fcountry {
                get {
                    return ((string)(this[this.tablerprdr.fcountryColumn]));
                }
                set {
                    this[this.tablerprdr.fcountryColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fregion {
                get {
                    return ((string)(this[this.tablerprdr.fregionColumn]));
                }
                set {
                    this[this.tablerprdr.fregionColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string ftel {
                get {
                    return ((string)(this[this.tablerprdr.ftelColumn]));
                }
                set {
                    this[this.tablerprdr.ftelColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string ffax {
                get {
                    return ((string)(this[this.tablerprdr.ffaxColumn]));
                }
                set {
                    this[this.tablerprdr.ffaxColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string femail {
                get {
                    return ((string)(this[this.tablerprdr.femailColumn]));
                }
                set {
                    this[this.tablerprdr.femailColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string frep {
                get {
                    return ((string)(this[this.tablerprdr.frepColumn]));
                }
                set {
                    this[this.tablerprdr.frepColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string finter {
                get {
                    return ((string)(this[this.tablerprdr.finterColumn]));
                }
                set {
                    this[this.tablerprdr.finterColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fsubside {
                get {
                    return ((string)(this[this.tablerprdr.fsubsideColumn]));
                }
                set {
                    this[this.tablerprdr.fsubsideColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fstatus {
                get {
                    try {
                        return ((string)(this[this.tablerprdr.fstatusColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("資料表 \'rprdr\' 中資料行 \'fstatus\' 的值是 DBNull。", e);
                    }
                }
                set {
                    this[this.tablerprdr.fstatusColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fctldel {
                get {
                    return ((string)(this[this.tablerprdr.fctldelColumn]));
                }
                set {
                    this[this.tablerprdr.fctldelColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string finpuser {
                get {
                    return ((string)(this[this.tablerprdr.finpuserColumn]));
                }
                set {
                    this[this.tablerprdr.finpuserColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public System.DateTime finpdate {
                get {
                    return ((global::System.DateTime)(this[this.tablerprdr.finpdateColumn]));
                }
                set {
                    this[this.tablerprdr.finpdateColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fupduser {
                get {
                    return ((string)(this[this.tablerprdr.fupduserColumn]));
                }
                set {
                    this[this.tablerprdr.fupduserColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public System.DateTime fupddate {
                get {
                    return ((global::System.DateTime)(this[this.tablerprdr.fupddateColumn]));
                }
                set {
                    this[this.tablerprdr.fupddateColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public int id {
                get {
                    return ((int)(this[this.tablerprdr.idColumn]));
                }
                set {
                    this[this.tablerprdr.idColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string pinter {
                get {
                    try {
                        return ((string)(this[this.tablerprdr.pinterColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("資料表 \'rprdr\' 中資料行 \'pinter\' 的值是 DBNull。", e);
                    }
                }
                set {
                    this[this.tablerprdr.pinterColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string psubside {
                get {
                    try {
                        return ((string)(this[this.tablerprdr.psubsideColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("資料表 \'rprdr\' 中資料行 \'psubside\' 的值是 DBNull。", e);
                    }
                }
                set {
                    this[this.tablerprdr.psubsideColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsfaliasNull() {
                return this.IsNull(this.tablerprdr.faliasColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetfaliasNull() {
                this[this.tablerprdr.faliasColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsfstatusNull() {
                return this.IsNull(this.tablerprdr.fstatusColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetfstatusNull() {
                this[this.tablerprdr.fstatusColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IspinterNull() {
                return this.IsNull(this.tablerprdr.pinterColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetpinterNull() {
                this[this.tablerprdr.pinterColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IspsubsideNull() {
                return this.IsNull(this.tablerprdr.psubsideColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetpsubsideNull() {
                this[this.tablerprdr.psubsideColumn] = global::System.Convert.DBNull;
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class rcontactRow : global::System.Data.DataRow {
            
            private rcontactDataTable tablercontact;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            internal rcontactRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tablercontact = ((rcontactDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fctlid_1 {
                get {
                    return ((string)(this[this.tablercontact.fctlid_1Column]));
                }
                set {
                    this[this.tablercontact.fctlid_1Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fctlid {
                get {
                    return ((string)(this[this.tablercontact.fctlidColumn]));
                }
                set {
                    this[this.tablercontact.fctlidColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fname {
                get {
                    return ((string)(this[this.tablercontact.fnameColumn]));
                }
                set {
                    this[this.tablercontact.fnameColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fpost {
                get {
                    return ((string)(this[this.tablercontact.fpostColumn]));
                }
                set {
                    this[this.tablercontact.fpostColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string ftel {
                get {
                    return ((string)(this[this.tablercontact.ftelColumn]));
                }
                set {
                    this[this.tablercontact.ftelColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string ffax {
                get {
                    return ((string)(this[this.tablercontact.ffaxColumn]));
                }
                set {
                    this[this.tablercontact.ffaxColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string femail {
                get {
                    return ((string)(this[this.tablercontact.femailColumn]));
                }
                set {
                    this[this.tablercontact.femailColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fctldel {
                get {
                    return ((string)(this[this.tablercontact.fctldelColumn]));
                }
                set {
                    this[this.tablercontact.fctldelColumn] = value;
                }
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class rdeptRow : global::System.Data.DataRow {
            
            private rdeptDataTable tablerdept;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            internal rdeptRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tablerdept = ((rdeptDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string ftype {
                get {
                    return ((string)(this[this.tablerdept.ftypeColumn]));
                }
                set {
                    this[this.tablerdept.ftypeColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fctlid_1 {
                get {
                    return ((string)(this[this.tablerdept.fctlid_1Column]));
                }
                set {
                    this[this.tablerdept.fctlid_1Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fctlid {
                get {
                    return ((string)(this[this.tablerdept.fctlidColumn]));
                }
                set {
                    this[this.tablerdept.fctlidColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fid_1 {
                get {
                    return ((string)(this[this.tablerdept.fid_1Column]));
                }
                set {
                    this[this.tablerdept.fid_1Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fid {
                get {
                    return ((string)(this[this.tablerdept.fidColumn]));
                }
                set {
                    this[this.tablerdept.fidColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fname {
                get {
                    return ((string)(this[this.tablerdept.fnameColumn]));
                }
                set {
                    this[this.tablerdept.fnameColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string falias {
                get {
                    try {
                        return ((string)(this[this.tablerdept.faliasColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("資料表 \'rdept\' 中資料行 \'falias\' 的值是 DBNull。", e);
                    }
                }
                set {
                    this[this.tablerdept.faliasColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fmap {
                get {
                    try {
                        return ((string)(this[this.tablerdept.fmapColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("資料表 \'rdept\' 中資料行 \'fmap\' 的值是 DBNull。", e);
                    }
                }
                set {
                    this[this.tablerdept.fmapColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string fnewmap {
                get {
                    try {
                        return ((string)(this[this.tablerdept.fnewmapColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("資料表 \'rdept\' 中資料行 \'fnewmap\' 的值是 DBNull。", e);
                    }
                }
                set {
                    this[this.tablerdept.fnewmapColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsfaliasNull() {
                return this.IsNull(this.tablerdept.faliasColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetfaliasNull() {
                this[this.tablerdept.faliasColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsfmapNull() {
                return this.IsNull(this.tablerdept.fmapColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetfmapNull() {
                this[this.tablerdept.fmapColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsfnewmapNull() {
                return this.IsNull(this.tablerdept.fnewmapColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetfnewmapNull() {
                this[this.tablerdept.fnewmapColumn] = global::System.Convert.DBNull;
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        public class rprdrRowChangeEvent : global::System.EventArgs {
            
            private rprdrRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public rprdrRowChangeEvent(rprdrRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public rprdrRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        public class rcontactRowChangeEvent : global::System.EventArgs {
            
            private rcontactRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public rcontactRowChangeEvent(rcontactRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public rcontactRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        public class rdeptRowChangeEvent : global::System.EventArgs {
            
            private rdeptRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public rdeptRowChangeEvent(rdeptRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public rdeptRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
    }
}

#pragma warning restore 1591