using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;

namespace INS
{
    public partial class frmBrkSearch : Form
    {
        public frmBrkSearch()
        {
            InitializeComponent();
            InitCombobox();
            FillData("", "");
        }

        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "";

        void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            if (order == "" && query == "")
            {
                a = new SqlDataAdapter("select fid as Broker#,fdesc as Desc# from mprdr where ftype='B' order by fid", c);
            }
            else if (order != "")
            {
                a = new SqlDataAdapter("select fid as Broker#,fdesc as Desc# from mprdr where ftype='B' order by '" + order +"'", c);
            }
            else if (query != "")
            {
                a = new SqlDataAdapter("select fid as Broker#,fdesc as Desc# from mprdr where ftype='B' AND fdesc = '" + query + "'", c);
            }
            else
            {
                a = new SqlDataAdapter("select fid as Broker#,fdesc as Desc# from mprdr where ftype='B' AND fdesc = '" + query + "' order by fid", c);
            }
            DataTable t = new DataTable();
            a.Fill(t);
            dataGridView1.DataSource = t;
            c.Close();
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                row = dataGridView1.CurrentCell.RowIndex;
                if (dataGridView1.Rows[row].Cells["Broker#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Broker#"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["Broker#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["Desc#"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        public enum Mode
        {
            Broker = 1,
            Desc = 2
        }

        private void InitCombobox()
        {
            Array arr = System.Enum.GetValues(typeof(Mode));    // 获取枚举的所有值
            DataTable dt = new DataTable();
            dt.Columns.Add("String", Type.GetType("System.String"));
            dt.Columns.Add("Value", typeof(int));
            foreach (var a in arr)
            {
                string strText = EnumTextByDescription.GetEnumDesc((Mode)a);
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = (int)a;

                dt.Rows.Add(aRow);
            }

            comboBox1.DataSource = dt;
            comboBox1.DisplayMember = "String";
            comboBox1.ValueMember = "Value";
        }

        private void Query_Click(object sender, EventArgs e)
        {
            if (flag == "frmSurplus") { 
            frmSurplus ownerFrm = (frmSurplus)this.Owner;
            ownerFrm.CloseFlag = false;
            ownerFrm.StrValue = textBox1.Text.ToString ();
            ownerFrm.StrValuebn  = label3.Text.ToString();
            this.Close();
            }
            if (flag == "frmXOL")
            {
                frmXOL2 ownerFrm = (frmXOL2)this.Owner;
                ownerFrm.CloseFlag = false;
                ownerFrm.StrValue = textBox1.Text.ToString();
                ownerFrm.StrValuebn = label3.Text.ToString();
                this.Close();
            }
            if (flag == "frmFac")
            {
                frmFac ownerFrm = (frmFac)this.Owner;
                ownerFrm.CloseFlag = false;
                ownerFrm.StrValue = textBox1.Text.ToString();
                ownerFrm.StrValuebn = label3.Text.ToString();
                this.Close();
            }
        }


        private void button1_Click(object sender, EventArgs e)
        {
            if (flag == "frmSurplus")
            {
                frmSurplus ownerFrm = (frmSurplus)this.Owner;
                ownerFrm.CloseFlag = true;
                this.Close();
            }
            if (flag == "frmXOL")
            {
                frmXOL2 ownerFrm = (frmXOL2)this.Owner;
                ownerFrm.CloseFlag = true;
                this.Close();
            }
            if (flag == "frmFac")
            {
                frmFac ownerFrm = (frmFac)this.Owner;
                ownerFrm.CloseFlag = true;
                this.Close();
            }
            this.Close();
        }


    }
}
