using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;

namespace INS
{
    public partial class frmProject : Form
    {
        public frmProject()
        {
            InitializeComponent();
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            InitCombobox();
            FillData("", "");
        }
        void control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
            {
                SendKeys.Send("{TAB}");
            }
        }
        DES des = new DES();
        DBConnect operate = new DBConnect();
        ExportDBF DBF = new ExportDBF();
        XLS txt = new XLS();
        public string fctlid_a = "";
        public String user = InsEnvironment.LoginUser .GetUserCode(), oldrow = "";
        public string Dbo = InsEnvironment.DataBase.GetDbo();
        void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            try
            {
                if (order != "" && query != "")
                {
                    if (order == "Policy#")
                    {
                        a = new SqlDataAdapter("select fctlid, fpolno as Policy# ,fclnt,fsclass ,finsd as Insured, " +
    "fdept, fcontrt as Contract#, fprjcode as Project#,fprj,fprj_alt as [ ], " +
    "case when fmonitor ='1' then 'Yes' else 'No' end as monitor, " +
    "case when fnew ='1' then 'Yes' else 'No' end as New, " +
    "case when finter ='1' then 'Yes' else 'No' end as internal, " +
    "b.fdesc as fclntd, isnull(c.fname,'N/A') as fsclnt_a,finpuser, finpdate,fupduser,fupddate  from " + Dbo + "nprjdet a " +
    "left join (select fid,fdesc from mprdr where ftype ='c') b on a.fclnt = b.fid " +
    "left join (select fid,fname from " + Dbo + "ndept) c on c.fid = a.fsclnt " +
    "where  fpolno like '%" + query.ToUpper() + "%' order by " + order, c);
                    }
                    else
                    {

                        a = new SqlDataAdapter("select fctlid, fpolno as Policy# ,fclnt,fsclass ,finsd as Insured, " +
       "fdept, fcontrt as Contract#, fprjcode as Project#,fprj,fprj_alt as [ ], " +
       "case when fmonitor ='1' then 'Yes' else 'No' end as monitor, " +
       "case when fnew ='1' then 'Yes' else 'No' end as New, " +
       "case when finter ='1' then 'Yes' else 'No' end as internal, " +
       "b.fdesc as fclntd, isnull(c.fname,'N/A') as fsclnt_a,finpuser, finpdate,fupduser,fupddate  from " + Dbo + "nprjdet a " +
       "left join (select fid,fdesc from mprdr where ftype ='c') b on a.fclnt = b.fid " +
       "left join (select fid,fname from " + Dbo + "ndept) c on c.fid = a.fsclnt " +
       "where  fprjcode like '%" + query.ToUpper() + "%' order by " + order, c);
                    }
                }
                else if (order != "" && query == "")
                {
                    a = new SqlDataAdapter("select fctlid, fpolno as Policy# ,fclnt,fsclass ,finsd as Insured, " +
    "fdept, fcontrt as Contract#, fprjcode as Project#,fprj,fprj_alt as [ ], " +
    "case when fmonitor ='1' then 'Yes' else 'No' end as monitor, " +
    "case when fnew ='1' then 'Yes' else 'No' end as New, " +
    "case when finter ='1' then 'Yes' else 'No' end as internal, " +
    "b.fdesc as fclntd, isnull(c.fname,'N/A') as fsclnt_a,finpuser, finpdate,fupduser,fupddate  from " + Dbo + "nprjdet a " +
    "left join (select fid,fdesc from mprdr where ftype ='c') b on a.fclnt = b.fid " +
    "left join (select fid,fname from " + Dbo + "ndept) c on c.fid = a.fsclnt " +
    "order by " + order, c);
                }

                else
                {
                    a = new SqlDataAdapter("select fctlid, fpolno as Policy# ,fclnt,fsclass ,finsd as Insured, " +
    "fdept, fcontrt as Contract#, fprjcode as Project#,fprj,fprj_alt as [ ], " +
    "case when fmonitor ='1' then 'Yes' else 'No' end as monitor, " +
    "case when fnew ='1' then 'Yes' else 'No' end as New, " +
    "case when finter ='1' then 'Yes' else 'No' end as internal, " +
    "b.fdesc as fclntd, isnull(c.fname,'N/A') as fsclnt_a,finpuser, finpdate,fupduser,fupddate  from " + Dbo + "nprjdet a " +
    "left join (select fid,fdesc from mprdr where ftype ='c') b on a.fclnt = b.fid " +
    "left join (select fid,fname from " + Dbo + "ndept) c on c.fid = a.fsclnt " +
    "order by fpolno ", c);

                }
                DataTable t = new DataTable();
                a.Fill(t);
                dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
                dataGridView1.DataSource = t;
                this.dataGridView1.Columns[0].Visible = false;
                this.dataGridView1.Columns[2].Visible = false;
                this.dataGridView1.Columns[3].Visible = false;
                this.dataGridView1.Columns[5].Visible = false;
                this.dataGridView1.Columns[8].Visible = false;
                this.dataGridView1.Columns[10].Visible = false;
                this.dataGridView1.Columns[12].Visible = false;
                this.dataGridView1.Columns[13].Visible = false;
                this.dataGridView1.Columns[14].Visible = false;
                this.dataGridView1.Columns[15].Visible = false;
                this.dataGridView1.Columns[16].Visible = false;
                this.dataGridView1.Columns[17].Visible = false;
                this.dataGridView1.Columns[18].Visible = false;
            }
            catch { }

            c.Close();
        }

        private void dataGridView1_CurrentCellChanged(object sender, EventArgs e)
        {
            if (dataGridView1.CurrentCell != null)
            {
                int counter;
                counter = dataGridView1.CurrentCell.RowIndex;
                if (dataGridView1.Rows[counter].Cells["Policy#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Policy#"].Value.ToString().Length != 0)
                    {
                        policy.Text = dataGridView1.Rows[counter].Cells["Policy#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fsclass"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fsclass"].Value.ToString().Length != 0)
                    {
                        subclass.Text = dataGridView1.Rows[counter].Cells["fsclass"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fclnt"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fclnt"].Value.ToString().Length != 0)
                    {
                        ClientNo.Text = dataGridView1.Rows[counter].Cells["fclnt"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fclntd"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fclntd"].Value.ToString().Length != 0)
                    {
                        ClientName.Text = dataGridView1.Rows[counter].Cells["fclntd"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fsclnt_a"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fsclnt_a"].Value.ToString().Length != 0)
                    {
                        ClientShort.Text = dataGridView1.Rows[counter].Cells["fsclnt_a"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["internal"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["internal"].Value.ToString().Length != 0)
                    {
                        inter.Text = dataGridView1.Rows[counter].Cells["internal"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fdept"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fdept"].Value.ToString().Length != 0)
                    {
                        Dept.Text = dataGridView1.Rows[counter].Cells["fdept"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Insured"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Insured"].Value.ToString().Length != 0)
                    {
                        Insured.Text = dataGridView1.Rows[counter].Cells["Insured"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Contract#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Contract#"].Value.ToString().Length != 0)
                    {
                        contract.Text = dataGridView1.Rows[counter].Cells["Contract#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Project#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Project#"].Value.ToString().Length != 0)
                    {
                        fprjcode.Text = dataGridView1.Rows[counter].Cells["Project#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fprj"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fprj"].Value.ToString().Length != 0)
                    {
                        fprj.Text = dataGridView1.Rows[counter].Cells["fprj"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells[" "].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells[" "].Value.ToString().Length != 0)
                    {
                        fprj_alt.Text = dataGridView1.Rows[counter].Cells[" "].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["finpuser"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["finpuser"].Value.ToString().Length != 0)
                    {
                        finpuser.Text = dataGridView1.Rows[counter].Cells["finpuser"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["finpdate"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["finpdate"].Value.ToString().Length != 0)
                    {
                        finpdate.Text = dataGridView1.Rows[counter].Cells["finpdate"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fupduser"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fupduser"].Value.ToString().Length != 0)
                    {
                        fupduser.Text = dataGridView1.Rows[counter].Cells["fupduser"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fupddate"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fupddate"].Value.ToString().Length != 0)
                    {
                        fupddate.Text = dataGridView1.Rows[counter].Cells["fupddate"].Value.ToString().Trim();
                    }
                }
                if (dataGridView1.Rows[counter].Cells["monitor"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["monitor"].Value.ToString().Length != 0)
                    {
                        comboBox2.SelectedItem = dataGridView1.Rows[counter].Cells["monitor"].Value.ToString().Trim();

                    }
                }
                if (dataGridView1.Rows[counter].Cells["New"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["New"].Value.ToString().Length != 0)
                    {
                        comboBox3.SelectedItem = dataGridView1.Rows[counter].Cells["New"].Value.ToString().Trim();

                    }
                }
                fctlid_a = dataGridView1.Rows[counter].Cells["fctlid"].Value.ToString();
                rowlabel.Text = counter.ToString();
            }

        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            e.Cancel = true;
        }

        private void tabControl1_Selecting2(object sender, TabControlCancelEventArgs e)
        {
            e.Cancel = false;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", textBox1.Text.ToString().Trim());
        }

        public enum Mode
        {
            Policy = 1,
            Project = 2
        }

        private void InitCombobox()
        {
            Array arr = System.Enum.GetValues(typeof(Mode));    // 获取枚举的所有值
            DataTable dt = new DataTable();
            dt.Columns.Add("String", Type.GetType("System.String"));
            dt.Columns.Add("Value", typeof(int));
            foreach (var a in arr)
            {
                string strText = EnumTextByDescription.GetEnumDesc((Mode)a);
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = (int)a;

                dt.Rows.Add(aRow);
            }

            comboBox1.DataSource = dt;
            comboBox1.DisplayMember = "String";
            comboBox1.ValueMember = "Value";
        }

        void ButtonControl()
        {
            Dept.BackColor = System.Drawing.SystemColors.InactiveCaption;
            Dept.ReadOnly = true;
            contract.BackColor = System.Drawing.SystemColors.InactiveCaption;
            contract.ReadOnly = true;
            fprjcode.BackColor = System.Drawing.SystemColors.InactiveCaption;
            fprjcode.ReadOnly = true;
            fprj.BackColor = System.Drawing.SystemColors.InactiveCaption;
            fprj.ReadOnly = true;
            fprj_alt.BackColor = System.Drawing.SystemColors.InactiveCaption;
            fprj_alt.ReadOnly = true;
            comboBox2.BackColor = System.Drawing.SystemColors.InactiveCaption;
            comboBox2.Enabled = false;
            comboBox3.BackColor = System.Drawing.SystemColors.InactiveCaption;
            comboBox3.Enabled = false;
            dcancel.Visible = false;
            dsave.Visible = false;
            dexit.Visible = true;
            dupdate.Visible = true;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting2);
        }

        private void button9_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void dexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void lupdate_Click(object sender, EventArgs e)
        {
            oldrow = rowlabel.Text;
            tabControl1.SelectedTab = Clauses;
            Dept.BackColor = System.Drawing.SystemColors.Window;
            Dept.ReadOnly = false;
            contract.BackColor = System.Drawing.SystemColors.Window;
            contract.ReadOnly = false;
            fprjcode.BackColor = System.Drawing.SystemColors.Window;
            fprjcode.ReadOnly = false;
            fprj.BackColor = System.Drawing.SystemColors.Window;
            fprj.ReadOnly = false;
            fprj_alt.BackColor = System.Drawing.SystemColors.Window;
            fprj_alt.ReadOnly = false;
            comboBox2.BackColor = System.Drawing.SystemColors.Window;
            comboBox2.Enabled = true;
            comboBox3.BackColor = System.Drawing.SystemColors.Window;
            comboBox3.Enabled = true;
            dcancel.Visible = true;
            dsave.Visible = true;
            dexit.Visible = false;
            dupdate.Visible = false;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);
        }

        private void dupdate_Click(object sender, EventArgs e)
        {
            oldrow = rowlabel.Text;
            Dept.BackColor = System.Drawing.SystemColors.Window;
            Dept.ReadOnly = false;
            contract.BackColor = System.Drawing.SystemColors.Window;
            contract.ReadOnly = false;
            fprjcode.BackColor = System.Drawing.SystemColors.Window;
            fprjcode.ReadOnly = false;
            fprj.BackColor = System.Drawing.SystemColors.Window;
            fprj.ReadOnly = false;
            fprj_alt.BackColor = System.Drawing.SystemColors.Window;
            fprj_alt.ReadOnly = false;
            comboBox2.BackColor = System.Drawing.SystemColors.Window;
            comboBox2.Enabled = true;
            comboBox3.BackColor = System.Drawing.SystemColors.Window;
            comboBox3.Enabled = true;
            dcancel.Visible = true;
            dsave.Visible = true;
            dexit.Visible = false;
            dupdate.Visible = false;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);
        }

        private void dsave_Click(object sender, EventArgs e)
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really Update the item?", "Updated Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                String fctlid = fctlid_a;
                String dept = Dept.Text;
                String Contract = contract.Text;
                String prjcode = fprjcode.Text;
                String prj = fprj.Text;
                String prj_alt = fprj_alt.Text;
                int fmonitor;
                if (comboBox2.SelectedItem.ToString() == "Yes")
                { fmonitor = 1; }
                else { fmonitor = 2; }
                int fnew;
                if (comboBox2.SelectedItem.ToString() == "Yes")
                { fnew = 1; }
                else { fnew = 2; }
                if (prjcode != "")
                {
                string updatedatestr =
                    "update " + Dbo + "nprjdet set fdept ='" + dept.Trim().Replace("'", "''") + "', fcontrt ='" + Contract.Trim().Replace("'", "''") + "', fprjcode='" + prjcode.Trim().Replace("'", "''") + "',fprj ='" + prj.Trim().Replace("'", "''") + "',fprj_alt ='" + prj_alt.Trim().Replace("'", "''") + "', " +
                    "fmonitor ='" + fmonitor + "', fnew ='" + fnew + "', fupduser ='" + user + "', fupddate ='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid='" + fctlid + "'";
                operate.OperateData(updatedatestr);
                MessageBox.Show("Have Been Updated", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                ButtonControl();
                FillData("", "");
                reload(prjcode, "update");
                }
                else
                {
                    MessageBox.Show("Some field is empty!", "Warning",
 MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private void dcancel_Click(object sender, EventArgs e)
        {
            ButtonControl();
            reload(oldrow, "cancel");
        }

        void reload(string fid, string lab)
        {
            string str = "select fctlid, fpolno as Policy# ,fclnt,fsclass ,finsd as Insured, " +
    "fdept, fcontrt as Contract#, fprjcode as Project#,fprj,fprj_alt as [ ], " +
    "case when fmonitor ='1' then 'Yes' else 'No' end as monitor, " +
    "case when fnew ='1' then 'Yes' else 'No' end as New, " +
    "case when finter ='1' then 'Yes' else 'No' end as internal, " +
    "b.fdesc as fclntd, isnull(c.fname,'N/A') as fsclnt_a,finpuser, finpdate,fupduser,fupddate  from " + Dbo + "nprjdet a " +
    "left join (select fid,fdesc from mprdr where ftype ='c') b on a.fclnt = b.fid " +
    "left join (select fid,fname from " + Dbo + "ndept) c on c.fid = a.fsclnt " +
    "order by fpolno ";
            DataTable dt = new DataTable();
            dt = operate.GetTable(str);
            if (dt.Rows.Count > 0)
            {
                dataGridView1.DataSource = dt;
                this.dataGridView1.Columns[5].Visible = false;
                this.dataGridView1.Columns[6].Visible = false;
                if (lab == "insert" || lab == "update")
                {
                    if (fid != "") //add update
                    {
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            if (dt.Rows[i]["Policy#"].ToString().Trim() == fid.Trim())
                            {
                                dataGridView1.CurrentCell = dataGridView1.Rows[i].Cells["Policy#"];
                                break;
                            }
                        }
                    }
                }
                if (lab == "del")
                {
                    if (dataGridView1.CurrentCell != null)
                    {
                        if (int.Parse(fid) == 0)
                        {
                            dataGridView1.CurrentCell = dataGridView1.Rows[int.Parse(fid)].Cells["Policy#"];
                        }
                        else { dataGridView1.CurrentCell = dataGridView1.Rows[int.Parse(fid) - 1].Cells["Policy#"]; }
                    }
                    else
                    {
                        foreach (Control ctrl in Clauses.Controls)
                        {
                            if (ctrl is TextBox)
                            {
                                ((TextBox)ctrl).Text = "";
                            }
                        }
                    }
                }
                if (lab == "cancel")
                {
                    if (dataGridView1.CurrentCell != null)
                    {
                        dataGridView1.CurrentCell = dataGridView1.Rows[int.Parse(fid)].Cells["Policy#"];
                    }
                }
            }
        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", "");
        }



    }
}

