using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using System.Configuration;
using CrystalDecisions.CrystalReports.Engine;
using INS.Business.objRpt;
using CrystalDecisions.Shared;

namespace INS
{
    public partial class frmCoverNote : Form
    {
        private String Cur_StrBFedit = "";
        private TextBox Cur_TextBox = null;
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public String user = InsEnvironment.LoginUser.GetUserCode(), oldrow = "", fctlid = "", flag = "", confirm = "";
        public string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
        public DateTime Time = DateTime.Now;
        private string rpDirectory = "M:\\Software II\\New INS Runtime\\Setup\\objRpt";
        private MaskedTextBox Cur_MaskedTextBox = null;
        public frmCoverNote()
        {
            InitializeComponent();
            foreach (Control control in Detail.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);
                if (control is TextBox)
                    control.Enter += new EventHandler(textBox_Enter);   //添加事件
            }
            InitCombobox();
            FillData("", "");
        }
        public String CliValue
        {
            set
            { fclnt.Text = value; }
        }
        public String CliAddr
        {
            set
            { Address.Text = value; }
        }
        public String CliDesc
        {
            set
            {
                IssueName.Text = value;
            }
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
            {
                SendKeys.Send("{TAB}");
            }
        }
        private void textBox_Enter(object sender, EventArgs e)
        {
            Cur_TextBox = (TextBox)sender;
            Cur_StrBFedit = Cur_TextBox.Text;
        }

        public enum Mode
        {
            CoverNote = 1,
            IssueDate = 2
        }

        private void InitCombobox()
        {
            Array arr = System.Enum.GetValues(typeof(Mode));    // 获取枚举的所有值
            DataTable dt = new DataTable();
            dt.Columns.Add("String", Type.GetType("System.String"));
            dt.Columns.Add("Value", typeof(int));
            foreach (var a in arr)
            {
                string strText = EnumTextByDescription.GetEnumDesc((Mode)a);
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = (int)a;
                dt.Rows.Add(aRow);
            }
            By.DataSource = dt;
            By.DisplayMember = "String";
            By.ValueMember = "Value";
        }


        public void FillData(string order, string query)
        {
            try
            {
                String sql = "";
                if (order != "" && query != "")
                {
                    sql = "select *,case when fconfirm = '1' then 'Pending' else " +
                        "case when fconfirm='2' then 'Cancelled' else " +
                        "case when fconfirm='3' then 'Confirmed' else " +
                        "case when fconfirm='4' then 'Pushed' else 'Signed' end end end end as Status from cover_note where CoverNo like '%" + query.ToUpper() + "%' order by " + order;
                }
                else if (order != "" && query == "")
                {
                    sql = "select *,case when fconfirm = '1' then 'Pending' else " +
                        "case when fconfirm='2' then 'Cancelled' else " +
                        "case when fconfirm='3' then 'Confirmed' else " +
                        "case when fconfirm='4' then 'Pushed' else 'Signed' end end end end as Status from  cover_note order by " + order;
                }
                else {
                    sql = "select *,case when fconfirm = '1' then 'Pending' else " +
                        "case when fconfirm='2' then 'Cancelled' else " +
                        "case when fconfirm='3' then 'Confirmed' else " +
                        "case when fconfirm='4' then 'Pushed' else 'Signed' end end end end as Status from cover_note";
                }
                DataTable dt = DBHelper.GetDataSet(sql);
                dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
                dataGridView1.DataSource = dt;
                foreach (DataGridViewColumn Column in dataGridView1.Columns)
                {
                    Column.Visible = false;
                }
                this.dataGridView1.Columns[0].Visible = false;
                this.dataGridView1.Columns[1].Visible = true;
                this.dataGridView1.Columns[2].Visible = true;
                this.dataGridView1.Columns[3].Visible = true;
                this.dataGridView1.Columns[30].Visible = true;
            }
            catch { }
        }

        private void dataGridView1_CurrentCellChanged(object sender, EventArgs e)
        {
            if (dataGridView1.CurrentCell != null)
            {
                int counter = dataGridView1.CurrentCell.RowIndex;
                rowlabel.Text = counter.ToString();
                if (dataGridView1.Rows[counter].Cells["IssueDate"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["IssueDate"].Value.ToString().Length != 0)
                    {
                        DateTime dt1 = Convert.ToDateTime(dataGridView1.Rows[counter].Cells["IssueDate"].Value.ToString().Trim());
                        IssueDate.Text = dt1.ToString("yyyy.MM.dd");
                    }
                }

                if (dataGridView1.Rows[counter].Cells["CoverNo"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["CoverNo"].Value.ToString().Length != 0)
                    {
                        CoverNo.Text = dataGridView1.Rows[counter].Cells["CoverNo"].Value.ToString().Trim();
                    }
                }


                if (dataGridView1.Rows[counter].Cells["IssueName"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["IssueName"].Value.ToString().Length != 0)
                    {
                        IssueName.Text = dataGridView1.Rows[counter].Cells["IssueName"].Value.ToString().Trim();
                    }
                    else { IssueName.Text = ""; }

                }

                if (dataGridView1.Rows[counter].Cells["Address"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Address"].Value.ToString().Length != 0)
                    {
                        Address.Text = dataGridView1.Rows[counter].Cells["Address"].Value.ToString().Trim();
                    }
                } 

                if (dataGridView1.Rows[counter].Cells["BeginDate"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["BeginDate"].Value.ToString().Length != 0)
                    {
                        DateTime dt1 = Convert.ToDateTime(dataGridView1.Rows[counter].Cells["BeginDate"].Value.ToString().Trim());
                        BeginDate.Text = dt1.ToString("yyyy.MM.dd");
                    }
                }

                if (dataGridView1.Rows[counter].Cells["RegMark"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["RegMark"].Value.ToString().Length != 0)
                    {
                        RegMark.Text = dataGridView1.Rows[counter].Cells["RegMark"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["EngineNo"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["EngineNo"].Value.ToString().Length != 0)
                    {
                        EngineNo.Text = dataGridView1.Rows[counter].Cells["EngineNo"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Cover"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Cover"].Value.ToString().Length != 0)
                    {
                        Cover.Text = dataGridView1.Rows[counter].Cells["Cover"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Value"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Value"].Value.ToString().Length != 0)
                    {
                        Value.Text = dataGridView1.Rows[counter].Cells["Value"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Capacity"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Capacity"].Value.ToString().Length != 0)
                    {
                        Capacity.Text = dataGridView1.Rows[counter].Cells["Capacity"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Model"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Model"].Value.ToString().Length != 0)
                    {
                        Model.Text = dataGridView1.Rows[counter].Cells["Model"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Weight"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Weight"].Value.ToString().Length != 0)
                    {
                        Weight.Text = dataGridView1.Rows[counter].Cells["Weight"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Body"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Body"].Value.ToString().Length != 0)
                    {
                        Body.Text = dataGridView1.Rows[counter].Cells["Body"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Seating"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Seating"].Value.ToString().Length != 0)
                    {
                        Seating.Text = dataGridView1.Rows[counter].Cells["Seating"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Year"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Year"].Value.ToString().Length != 0)
                    {
                        Year.Text = dataGridView1.Rows[counter].Cells["Year"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["GeneralExcess"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["GeneralExcess"].Value.ToString().Length != 0)
                    {
                        GeneralExcess.Text = dataGridView1.Rows[counter].Cells["GeneralExcess"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["YoungExcess"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["YoungExcess"].Value.ToString().Length != 0)
                    {
                        YoungExcess.Text = dataGridView1.Rows[counter].Cells["YoungExcess"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["ThirdExcess"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["ThirdExcess"].Value.ToString().Length != 0)
                    {
                        ThirdExcess.Text = dataGridView1.Rows[counter].Cells["ThirdExcess"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["InexExcess"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["InexExcess"].Value.ToString().Length != 0)
                    {
                        InexExcess.Text = dataGridView1.Rows[counter].Cells["InexExcess"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["LossExcess"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["LossExcess"].Value.ToString().Length != 0)
                    {
                        LossExcess.Text = dataGridView1.Rows[counter].Cells["LossExcess"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["UnamedExcess"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["UnamedExcess"].Value.ToString().Length != 0)
                    {
                        UnamedExcess.Text = dataGridView1.Rows[counter].Cells["UnamedExcess"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Special"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Special"].Value.ToString().Length != 0)
                    {
                        Special.Text = dataGridView1.Rows[counter].Cells["Special"].Value.ToString().Trim();
                    }
                }
                if (dataGridView1.Rows[counter].Cells["Days"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Days"].Value.ToString().Length != 0)
                    {
                        Days.Text = dataGridView1.Rows[counter].Cells["Days"].Value.ToString().Trim();
                    }
                }
                if (dataGridView1.Rows[counter].Cells["DaysEng"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["DaysEng"].Value.ToString().Length != 0)
                    {
                        DaysEng.Text = dataGridView1.Rows[counter].Cells["DaysEng"].Value.ToString().Trim();
                    }
                }

                fctlid = dataGridView1.Rows[counter].Cells["Fctlid"].Value.ToString();

                if (dataGridView1.Rows[counter].Cells["fconfirm"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fconfirm"].Value.ToString().Length != 0)
                    {
                        confirm = dataGridView1.Rows[counter].Cells["fconfirm"].Value.ToString();
                        if (confirm == "1"){ fconfirm.SelectedItem = "Pending"; }
                        else if (confirm == "2"){ fconfirm.SelectedItem = "Cancelled"; }
                        else if (confirm == "3") { fconfirm.SelectedItem = "Confirmed"; }
                        else if (confirm == "4") { fconfirm.SelectedItem = "Pushed"; }
                        else { fconfirm.SelectedItem = "Signed"; }

                        if (confirm == "1")
                        {
                            add_d.Visible = true;
                            add_l.Visible = true;
                            renew_d.Visible = true;
                            renew_l.Visible = true;
                            upd_d.Visible = true;
                            upd_l.Visible = true;
                            del_d.Visible = true;
                            del_l.Visible = true;
                            com_d.Visible = true;
                            com_l.Visible = true;
                            print_d.Visible = true;
                            print_l.Visible = true;
                            exit_d.Visible = true;
                            exit_l.Visible = true;


                            push_d.Visible = false;
                            push_l.Visible = false;
                            save_d.Visible = false;
                            save_l.Visible = false;
                            cancel_d.Visible = false;
                            cancel_l.Visible = false;
                        }
                        if (confirm == "3")
                        {
                            add_d.Visible = true;
                            add_l.Visible = true;
                            renew_d.Visible = true;
                            renew_l.Visible = true;
                            push_d.Visible = true;
                            push_l.Visible = true;
                            print_d.Visible = true;
                            print_l.Visible = true;
                            exit_d.Visible = true;
                            exit_l.Visible = true;

                            upd_d.Visible = false;
                            upd_l.Visible = false;
                            del_d.Visible = false;
                            del_l.Visible = false;
                            com_d.Visible = false;
                            com_l.Visible = false;
                            save_d.Visible = false;
                            save_l.Visible = false;
                            cancel_d.Visible = false;
                            cancel_l.Visible = false;
                        }

                        if (confirm == "4"|| confirm == "5")
                        {
                            add_d.Visible = true;
                            add_l.Visible = true;
                            renew_d.Visible = true;
                            renew_l.Visible = true;
                            print_d.Visible = true;
                            print_l.Visible = true;
                            exit_d.Visible = true;
                            exit_l.Visible = true;

                            push_d.Visible = false;
                            push_l.Visible = false;
                            upd_d.Visible = false;
                            upd_l.Visible = false;
                            del_d.Visible = false;
                            del_l.Visible = false;
                            com_d.Visible = false;
                            com_l.Visible = false;
                            save_d.Visible = false;
                            save_l.Visible = false;
                            cancel_d.Visible = false;
                            cancel_l.Visible = false;
                        }
                    }
                }
                
                
            }

        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            e.Cancel = true;
        }

        private void tabControl1_Selecting2(object sender, TabControlCancelEventArgs e)
        {
            e.Cancel = false;
        }

        void ModifyButtonControl()
        {
            save_d.Visible = true;
            save_l.Visible = true;
            cancel_d.Visible = true;
            cancel_l.Visible = true;
            add_d.Visible = false;
            add_l.Visible = false;
            upd_d.Visible = false;
            upd_l.Visible = false;
            del_d.Visible = false;
            del_l.Visible = false;
            push_d.Visible = false;
            push_l.Visible = false;
            com_d.Visible = false;
            com_l.Visible = false;
            print_d.Visible = false;
            print_l.Visible = false;
            exit_d.Visible = false;
            exit_l.Visible = false;

            tabControl1.SelectedTab = Detail;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);
            IssueDate.BackColor = System.Drawing.SystemColors.Window;
            IssueDate.ReadOnly = false;
            CoverNo.BackColor = System.Drawing.SystemColors.Window;
            CoverNo.ReadOnly = false;
            IssueName.BackColor = System.Drawing.SystemColors.Window;
            IssueName.ReadOnly = false;
            Address.BackColor = System.Drawing.SystemColors.Window;
            Address.ReadOnly = false;
            BeginDate.BackColor = System.Drawing.SystemColors.Window;
            BeginDate.ReadOnly = false;
            RegMark.BackColor = System.Drawing.SystemColors.Window;
            RegMark.ReadOnly = false;
            Cover.BackColor = System.Drawing.SystemColors.Window;
            Cover.ReadOnly = false;
            EngineNo.BackColor = System.Drawing.SystemColors.Window;
            EngineNo.ReadOnly = false;
            Value.BackColor = System.Drawing.SystemColors.Window;
            Value.ReadOnly = false;
            Capacity.BackColor = System.Drawing.SystemColors.Window;
            Capacity.ReadOnly = false;
            Model.BackColor = System.Drawing.SystemColors.Window;
            Model.ReadOnly = false;
            Weight.BackColor = System.Drawing.SystemColors.Window;
            Weight.ReadOnly = false;
            Body.BackColor = System.Drawing.SystemColors.Window;
            Body.ReadOnly = false;
            Seating.BackColor = System.Drawing.SystemColors.Window;
            Seating.ReadOnly = false;
            Year.BackColor = System.Drawing.SystemColors.Window;
            Year.ReadOnly = false;
            Days.BackColor = System.Drawing.SystemColors.Window;
            Days.ReadOnly = false;
            DaysEng.BackColor = System.Drawing.SystemColors.Window;
            DaysEng.ReadOnly = false;
            GeneralExcess.BackColor = System.Drawing.SystemColors.Window;
            GeneralExcess.ReadOnly = false;
            YoungExcess.BackColor = System.Drawing.SystemColors.Window;
            YoungExcess.ReadOnly = false;
            ThirdExcess.BackColor = System.Drawing.SystemColors.Window;
            ThirdExcess.ReadOnly = false;
            InexExcess.BackColor = System.Drawing.SystemColors.Window;
            InexExcess.ReadOnly = false;
            LossExcess.BackColor = System.Drawing.SystemColors.Window;
            LossExcess.ReadOnly = false;
            UnamedExcess.BackColor = System.Drawing.SystemColors.Window;
            UnamedExcess.ReadOnly = false;
            Special.BackColor = System.Drawing.SystemColors.Window;
            Special.ReadOnly = false;
            fclnt.BackColor = System.Drawing.SystemColors.Window;
            fclnt.ReadOnly = false;
        }

        void BackButtonControl() {
            flag = "";
            if (confirm == "1")
            {
                upd_d.Visible = true;
                upd_l.Visible = true;
                del_d.Visible = true;
                del_l.Visible = true;
                com_d.Visible = true;
                com_l.Visible = true;
                print_d.Visible = true;
                print_l.Visible = true;
                exit_d.Visible = true;
                exit_l.Visible = true;
            }
            if (confirm == "3")
            {
                add_d.Visible = true;
                add_l.Visible = true;
                push_d.Visible = true;
                push_l.Visible = true;
                print_d.Visible = true;
                print_l.Visible = true;
                exit_d.Visible = true;
                exit_l.Visible = true;
            }
            IssueDate.BackColor = System.Drawing.SystemColors.InactiveCaption;
            IssueDate.ReadOnly = true;
            CoverNo.BackColor = System.Drawing.SystemColors.InactiveCaption;
            CoverNo.ReadOnly = true;
            IssueName.BackColor = System.Drawing.SystemColors.InactiveCaption;
            IssueName.ReadOnly = true;
            Address.BackColor = System.Drawing.SystemColors.InactiveCaption;
            Address.ReadOnly = true;
            BeginDate.BackColor = System.Drawing.SystemColors.InactiveCaption;
            BeginDate.ReadOnly = true;
            RegMark.BackColor = System.Drawing.SystemColors.InactiveCaption;
            RegMark.ReadOnly = true;
            Cover.BackColor = System.Drawing.SystemColors.InactiveCaption;
            Cover.ReadOnly = true;
            EngineNo.BackColor = System.Drawing.SystemColors.InactiveCaption;
            EngineNo.ReadOnly = true;
            Value.BackColor = System.Drawing.SystemColors.InactiveCaption;
            Value.ReadOnly = true;
            Capacity.BackColor = System.Drawing.SystemColors.InactiveCaption;
            Capacity.ReadOnly = true;
            Model.BackColor = System.Drawing.SystemColors.InactiveCaption;
            Model.ReadOnly = true;
            Weight.BackColor = System.Drawing.SystemColors.InactiveCaption;
            Weight.ReadOnly = true;
            Body.BackColor = System.Drawing.SystemColors.InactiveCaption;
            Body.ReadOnly = true;
            Seating.BackColor = System.Drawing.SystemColors.InactiveCaption;
            Seating.ReadOnly = true;
            Year.BackColor = System.Drawing.SystemColors.InactiveCaption;
            Year.ReadOnly = true;
            Days.BackColor = System.Drawing.SystemColors.InactiveCaption;
            Days.ReadOnly = true;
            DaysEng.BackColor = System.Drawing.SystemColors.InactiveCaption;
            DaysEng.ReadOnly = true;
            GeneralExcess.BackColor = System.Drawing.SystemColors.InactiveCaption;
            GeneralExcess.ReadOnly = true;
            YoungExcess.BackColor = System.Drawing.SystemColors.InactiveCaption;
            YoungExcess.ReadOnly = true;
            ThirdExcess.BackColor = System.Drawing.SystemColors.InactiveCaption;
            ThirdExcess.ReadOnly = true;
            InexExcess.BackColor = System.Drawing.SystemColors.InactiveCaption;
            InexExcess.ReadOnly = true;
            LossExcess.BackColor = System.Drawing.SystemColors.InactiveCaption;
            LossExcess.ReadOnly = true;
            UnamedExcess.BackColor = System.Drawing.SystemColors.InactiveCaption;
            UnamedExcess.ReadOnly = true;
            Special.BackColor = System.Drawing.SystemColors.InactiveCaption;
            Special.ReadOnly = true;
            fclnt.BackColor = System.Drawing.SystemColors.InactiveCaption;
            fclnt.ReadOnly = true;
            client.Enabled = false;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting2);
        }

        void reload(string row, string label)
        {
            string str = "select *,case when fconfirm = '1' then 'Pending' else " +
                        "case when fconfirm='2' then 'Cancelled' else " +
                        "case when fconfirm='3' then 'Confirmed' else " +
                        "case when fconfirm='4' then 'Pushed' else 'Signed' end end end end as Status from  cover_note order by CoverNo ";
            DataTable dt = DBHelper.GetDataSet(str);
            if (dt.Rows.Count > 0)
            {
                dataGridView1.DataSource = dt;
                foreach (DataGridViewColumn Column in dataGridView1.Columns)
                {
                    Column.Visible = false;
                }
                this.dataGridView1.Columns[0].Visible = false;
                this.dataGridView1.Columns[1].Visible = true;
                this.dataGridView1.Columns[2].Visible = true;
                this.dataGridView1.Columns[3].Visible = true;
                this.dataGridView1.Columns[30].Visible = true;
                if (label == "Add" || label == "Update")
                {
                    if (row != "") //add update
                    {
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            if (dt.Rows[i]["CoverNo"].ToString().Trim() == row.Trim())
                            {
                                dataGridView1.CurrentCell = dataGridView1.Rows[i].Cells["CoverNo"];
                                break;
                            }
                        }
                    }
                }
                if (label == "Delete")
                {
                    if (dataGridView1.CurrentCell != null)
                    {
                        if (int.Parse(row) == 0)
                        {
                            dataGridView1.CurrentCell = dataGridView1.Rows[int.Parse(row)].Cells["CoverNo"];
                        }
                        else { dataGridView1.CurrentCell = dataGridView1.Rows[int.Parse(row) - 1].Cells["CoverNo"]; }
                    }
                    else
                    {
                        foreach (Control ctrl in Detail.Controls)
                        {
                            if (ctrl is TextBox)
                            {
                                ((TextBox)ctrl).Text = "";
                            }
                        }
                    }
                }
                if (label == "Cancel" || label == "Confirm")
                {
                    if (row != "") //add update
                    {
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            if (dt.Rows[i]["CoverNo"].ToString().Trim() == row.Trim())
                            {
                                dataGridView1.CurrentCell = dataGridView1.Rows[i].Cells["CoverNo"];
                                break;
                            }
                        }
                    }
                }
            }
        }

        private void query_Click(object sender, EventArgs e)
        {
            FillData("",KeyWord.Text.ToString().Trim());
        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            FillData(By.Text.ToString() + "#", "");
        }

        private void add_l_Click(object sender, EventArgs e)
        {
            Add();
        }

        private void add_d_Click(object sender, EventArgs e)
        {
            Add();
        }

        void Add() 
        {
            flag = "Add";
            oldrow = rowlabel.Text;
            fconfirm.SelectedItem = "Pending";
            ModifyButtonControl();
            save_d.Visible = true;
            cancel_d.Visible = true;
            add_d.Visible = false;
            print_d.Visible = false;
            foreach (Control ctrl in Detail.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                }
            }
            CoverNo.Text = Fct.NewCoverNo(DateTime.Now.ToString("yyyy.MM.dd").Substring(0,4));

            IssueDate.Text = DateTime.Now.ToString("yyyy.MM.dd");
            client.Enabled = true;
            DaysEng.Text = "thirtieth (30)";
            Cover.Text = "Comprehensive";
            Special.Text = "*of adjusted loss whichever is the greater";
            BeginDate.Text = "";
        }

        private void upd_l_Click(object sender, EventArgs e)
        {
            Update();
        }

        private void upd_d_Click(object sender, EventArgs e)
        {
            Update();
        }

        void Update()
        {
            flag = "Update";
            fconfirm.SelectedItem = "Pending";
            oldrow = rowlabel.Text;
            ModifyButtonControl();
            IssueDate.Text = DateTime.Now.ToString("yyyy.MM.dd");
            client.Enabled = true;
            save_d.Visible = true;
            cancel_d.Visible = true;
            add_d.Visible = false;
            print_d.Visible = false;
        }

        private void del_l_Click(object sender, EventArgs e)
        {
            Delete();
        }

        private void del_d_Click(object sender, EventArgs e)
        {
            Delete();
        }

        void Delete()
        {
            DialogResult myResult = MessageBox.Show("Are you really Delete the item?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string deldatestr = "delete from cover_note where Fctlid = '" + fctlid + "'";
                string result = ExecuteSqlTransaction(connectionString, deldatestr);
                if (result == "OK")
                {
                    DBConnect operate = new DBConnect();
                    string Sql2 = "update xsysparm set fnxtid=RIGHT('0000'+ LTRIM(STR(CAST(fnxtid as int)-1)), 4) where fidtype='CoverNote' AND fuy ='" + DateTime.Now.ToString("yyyy.MM.dd").Substring(0, 4) + "'";
                    operate.OperateData(Sql2);
                    MessageBox.Show("Have Been Deleted", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    reload(rowlabel.Text, "Delete");
                }
                else
                {
                    MessageBox.Show("Have Not Been Deleted", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                
            }
        }

        private void com_l_Click(object sender, EventArgs e)
        {
            Confirm();
        }

        private void com_d_Click(object sender, EventArgs e)
        {
            Confirm();
        }

        void Confirm()
        {
            DialogResult myResult = MessageBox.Show("Are you really Confirm the item?", "Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string comstr = "update cover_note set fconfirm = 3, [fcnfdate] = '" + Time.ToString("yyyy-MM-dd HH:mm:ss") + "'," +
                    "[fcnfuser] = '" + InsEnvironment.LoginUser.GetUserCode() + "' where fctlid = '" + fctlid + "' and fconfirm = 1";
                string result = ExecuteSqlTransaction(connectionString, comstr);
                if (result == "OK")
                {
                    MessageBox.Show("Have Been Confirmed", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    reload(CoverNo.Text, "Confirm");
                }
                else
                {
                    MessageBox.Show("Have Not Been Confirmed", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

            }
        }

        private void save_l_Click(object sender, EventArgs e)
        {
            Save();
        }

        private void save_d_Click(object sender, EventArgs e)
        {
            Save();
        }

        public Boolean CheckEmptyString(TextBox text_obj)
        {
            if (text_obj.Text.Trim() == "")
            {
                MessageBox.Show("Invalid Value");
                return false;
            }

            return true;
        }
        private Boolean Validate_Tran()
        {
            Boolean ll_ok;
            Cur_TextBox = CoverNo;
            ll_ok = CheckEmptyString(Cur_TextBox);
            if (!ll_ok)
                return ll_ok;

            DateTime dateTime;
            Cur_MaskedTextBox = IssueDate;
            ll_ok = DateTime.TryParseExact(Cur_MaskedTextBox.Text, "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out dateTime);
            if (!ll_ok)
                return ll_ok;

            Cur_MaskedTextBox = BeginDate;
            ll_ok = DateTime.TryParseExact(Cur_MaskedTextBox.Text, "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out dateTime);
            if (!ll_ok)
                return ll_ok;

            return ll_ok;
        }
        void Save()
        {

            if (!Validate_Tran())
            {
                Cur_TextBox.Focus();
                Cur_TextBox.SelectAll();

                return;
            }

            String CoverNoStr = CoverNo.Text;
            DialogResult myResult = MessageBox.Show("Are you really Save the item?", "Saved Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                if (flag == "Add") {
                    string addstr = "INSERT INTO[dbo].[cover_note]([IssueDate],[CoverNo],[IssueName],[Address],[BeginDate],[RegMark],[Cover],[EngineNo],[Value],[Capacity],[Model],[Weight],[Body] "+
                    ",[Seating],[Year],[Days],[DaysEng],[Special],[GeneralExcess],[YoungExcess],[ThirdExcess],[InexExcess],[LossExcess],[UnamedExcess],[finpuser],[finpdate],[fupduser],[fupddate] " +
                    ",[fcnfuser],[fcnfdate],[fconfirm],[flowid]) VALUES (convert(datetime,'" + IssueDate.Text.Trim() + "',102), '" + CoverNo.Text + "', '" + Fct.stFat(IssueName.Text) + "','" + Fct.stFat(Address.Text) + "', convert(datetime,'" + BeginDate.Text.Trim() + "',102), '" + RegMark.Text + "','" + Cover.Text + "', '" + EngineNo.Text + "', '" + Fct.sdFat(Value.Text) + "', '" + Capacity.Text + "', "+
                    "'" + Model.Text + "', '" + Weight.Text + "', '" + Body.Text + "', '" + Seating.Text + "', '" + Year.Text + "','" + Days.Text + "','" + DaysEng.Text + "', '" + Special.Text + "', '" + GeneralExcess.Text + "','" + YoungExcess.Text + "', '" + ThirdExcess.Text + "', " +
                    "'" + InexExcess.Text + "', '" + LossExcess.Text + "', '" + UnamedExcess.Text + "', '" + InsEnvironment.LoginUser.GetUserCode() + "', '" + Time.ToString("yyyy-MM-dd HH:mm:ss") + "', " +
                    "'" + InsEnvironment.LoginUser.GetUserCode() + "', '" + Time.ToString("yyyy-MM-dd HH:mm:ss") + "', '" + InsEnvironment.LoginUser.GetUserCode() + "', '" + Time.ToString("yyyy-MM-dd HH:mm:ss") + "', 1,'')";
                    
                    string result = ExecuteSqlTransaction(connectionString, addstr);
                    if (result == "OK")
                    {
                        MessageBox.Show("Have Been Inserted!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        DBConnect operate = new DBConnect();
                        string Sql2 = "update xsysparm set fnxtid=RIGHT('0000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 4) where fidtype='CoverNote' AND fuy ='" + DateTime.Now.ToString("yyyy.MM.dd").Substring(0, 4) + "'";
                        operate.OperateData(Sql2);

                        BackButtonControl();
                        reload(CoverNoStr, "Add");
                    }
                    else
                    {
                        MessageBox.Show("Have Not Been Inserted", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                if (flag == "Update")
                {
                    string updstr = "UPDATE [dbo].[cover_note] SET [IssueDate] =convert(datetime,'" + IssueDate.Text.Trim() + "',102),[CoverNo] ='" + CoverNo.Text + "',[IssueName] = '" + Fct.stFat(IssueName.Text) + "' ,[Address] = '" + Fct.stFat(Address.Text) + "' " +
                      ",[BeginDate] =convert(datetime,'" + BeginDate.Text.Trim() + "',102),[RegMark] = '" + RegMark.Text + "',[Cover] = '" +  Cover.Text + "' ,[EngineNo] = '" + EngineNo.Text + "' ,[Value] = '" + Fct.sdFat(Value.Text) + "',[Capacity] = '" +  Capacity.Text + "' " +
                      ",[Model] = '" + Model.Text + "',[Weight] = '" + Weight.Text + "' ,[Body] = '" + Body.Text + "',[Seating] = '" + Seating.Text + "',[Year] = '" + Year.Text + "',[Days] = '" + Days.Text + "',[DaysEng] = '" + DaysEng.Text + "',[Special] = '" + Special.Text + "' " +
                      ",[GeneralExcess] = '" + GeneralExcess.Text + "',[YoungExcess] = '" + YoungExcess.Text + "',[ThirdExcess] = '" + ThirdExcess.Text + "' " +
                      ",[InexExcess] ='" + InexExcess.Text + "',[LossExcess] = '" + LossExcess.Text + "',[UnamedExcess] = '" + UnamedExcess.Text + "',[fupduser] = '" + InsEnvironment.LoginUser.GetUserCode() + "' " +
                      ",[fupddate] = '" + Time.ToString("yyyy-MM-dd HH:mm:ss") + "' WHERE Fctlid ='" + fctlid+"' ";

                    string result = ExecuteSqlTransaction(connectionString, updstr);
                    if (result == "OK")
                    {
                        MessageBox.Show("Have Been Updated!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        BackButtonControl();
                        reload(CoverNoStr, "Update");
                    }
                    else
                    {
                        MessageBox.Show("Have Not Been Updated", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
        }

        private void cancel_l_Click(object sender, EventArgs e)
        {
            Cancel();
        }

        private void cancel_d_Click(object sender, EventArgs e)
        {
            Cancel();
        }

        void Cancel() 
        {
            BackButtonControl();
            reload(oldrow, "Cancel");
        }

        private void print_l_Click(object sender, EventArgs e)
        {
            Print();
        }

        private void print_d_Click(object sender, EventArgs e)
        {
            Print();
        }

        void Print() 
        {
            string sql = "select * from cover_note where Fctlid ='"+fctlid+"'";
            DataTable dt = DBHelper.GetDataSet(sql);
            ReportDocument cryRpt = new ReportDocument();
            cryRpt.Load(rpDirectory + "\\covernote.rpt");
            cryRpt.SetDataSource(dt);
            cryDocViewer temp_form = new cryDocViewer(cryRpt);
            temp_form.ShowDialog();
            if (cryRpt != null)
            {
                cryRpt.Close();
                cryRpt.Dispose();
            }
        }

        private void exit_l_Click(object sender, EventArgs e)
        {
            Exit();
        }

        private void exit_d_Click(object sender, EventArgs e)
        {
            Exit();
        }

        void Exit() 
        {
            this.Close();
        }

        private void IssueDate_Leave(object sender, EventArgs e)
        {
            DateTime dateTime;
            IssueDate.Text = DateTime.Now.ToString("yyyy.MM.dd");
            if (!DateTime.TryParseExact(IssueDate.Text, "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out dateTime))
            {
                MessageBox.Show("Invalid Value");
                IssueDate.Focus();
                IssueDate.SelectAll();
                return;
            }
        }

        private void BeginDate_Leave(object sender, EventArgs e)
        {
            DateTime dateTime;
            if (!DateTime.TryParseExact(BeginDate.Text, "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out dateTime))
            {
                MessageBox.Show("Invalid Value");
                BeginDate.Focus();
                BeginDate.SelectAll();
                return;
            }
        }

        private void fclnt_Validated(object sender, EventArgs e)
        {

            if (Cur_TextBox.ReadOnly)
                return;

            Boolean ll_ok = CheckValidCode(Cur_TextBox, Cur_StrBFedit, false);

            if (!ll_ok)
            {
                MessageBox.Show("Invalid Value");
                Cur_TextBox.Focus();
            }

            return;
        }

        private Boolean CheckValidCode(TextBox text_obj, String oldText, Boolean skipReplace)
        {
            String fid = text_obj.Text.Trim(), faddress = "";
            oldText = oldText.Trim();

            String sqlstr = "select * from " + Dbm + "{0} where {1} and fid = '{2}'";
            String table = "", ftype = "";

            TextBox d_TextBox1 = null, d_TextBox2 = null;


            if (text_obj == fclnt) { table = "mprdr"; ftype = "ftype = 'C'"; d_TextBox1 = IssueName; d_TextBox2 = Address; }


            sqlstr = string.Format(sqlstr, table, ftype, fid);

            string fdesc = "", fadd1 = "", fadd2 = "", fadd3 = "", fadd4 = "";
            Boolean ll_ok = false;

            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["odata"].ConnectionString);
            SqlCommand cmd = new SqlCommand(sqlstr, con);
            con.Open();
            using (SqlDataReader sdr = cmd.ExecuteReader())
            {
                if (sdr.HasRows && sdr.Read())
                {
                    fdesc = sdr["fdesc"].ToString().Trim();
                    ll_ok = true;

                    if (text_obj == fclnt)
                    {
                        fadd1 = sdr["fadd1"].ToString().Trim();
                        fadd2 = sdr["fadd2"].ToString().Trim();
                        fadd3 = sdr["fadd3"].ToString().Trim();
                        fadd4 = sdr["fadd4"].ToString().Trim();

                        faddress = fadd1.Trim();
                        faddress = (faddress + (fadd2.Trim() == "" ? "" : " ") + fadd2.Trim()).Trim();
                        faddress = (faddress + (fadd3.Trim() == "" ? "" : " ") + fadd3.Trim()).Trim();
                        faddress = (faddress + (fadd4.Trim() == "" ? "" : " ") + fadd4.Trim()).Trim();
                    }

                    sdr.Close();
                    sdr.Dispose();
                }
            }

            con.Close();
            con.Dispose();

            if (ll_ok && d_TextBox1 != null)
                d_TextBox1.Text = fdesc;

            if (!ll_ok && "fsiteid|ftrade|fprof".Contains(text_obj.Name) && fid == "")
                ll_ok = true;

            if (!ll_ok && d_TextBox1 != null)
                d_TextBox1.Text = fdesc;

            if (!ll_ok)
                return false;

            if (skipReplace)
                return true;

            if (fid != oldText && d_TextBox2 != null)
                d_TextBox2.Text = fdesc;

            if (fid != oldText && text_obj == fclnt)
            {
                Address.Text = faddress;
            }

            return true;
        }
        private void client_Click(object sender, EventArgs e)
        {
            frmClientSearch tempform = new frmClientSearch(this);
            tempform.flag = "CoverNote";
            tempform.ftype = "C";
            tempform.FillData("", "");
            tempform.ShowDialog();
            fclnt.Focus();
        }

        private void renew_Click(object sender, EventArgs e)
        {
            flag = "Add";
            oldrow = rowlabel.Text;
            fconfirm.SelectedItem = "Pending";
            ModifyButtonControl();
            save_d.Visible = true;
            cancel_d.Visible = true;
            add_d.Visible = false;
            renew_d.Visible = false;
            print_d.Visible = false;
            client.Enabled = true;
            IssueDate.Text = DateTime.Now.ToString("yyyy.MM.dd");
            CoverNo.Text = Fct.NewCoverNo(DateTime.Now.ToString("yyyy.MM.dd").Substring(0, 4));

            DaysEng.Text = "thirtieth (30)";
            Cover.Text = "Comprehensive";
            Special.Text = "*of adjusted loss whichever is the greater";
            ModifyButtonControl();
        }

        private void renew_d_Click(object sender, EventArgs e)
        {
            flag = "Add";
            oldrow = rowlabel.Text;
            fconfirm.SelectedItem = "Pending";
            ModifyButtonControl();
            save_d.Visible = true;
            cancel_d.Visible = true;
            add_d.Visible = false;
            renew_d.Visible = false;
            print_d.Visible = false;
            client.Enabled = true;
            CoverNo.Text = Fct.NewCoverNo(DateTime.Now.ToString("yyyy.MM.dd").Substring(0, 4));

            IssueDate.Text = DateTime.Now.ToString("yyyy.MM.dd");
            DaysEng.Text = "thirtieth (30)";
            Cover.Text = "Comprehensive";
            Special.Text = "*of adjusted loss whichever is the greater";
            ModifyButtonControl();
        }

        private void push_l_Click(object sender, EventArgs e)
        {
            Push();
        }

        private void push_d_Click(object sender, EventArgs e)
        {
            Push();
        }

        void Push() {
            progressBar1.Visible = true;
            progressBar1.Maximum = 50;//设置最大长度值 
            progressBar1.Value = 0;//设置当前值 
            progressBar1.Step = 10;//设置没次增长多少 
            for (int i = 0; i < 5; i++)//循环 
            {
                System.Threading.Thread.Sleep(100);//暂停1秒 
                progressBar1.Value += progressBar1.Step;
            }
            string sql = "select * from cover_note where Fctlid ='" + fctlid + "'";
            DataTable dt = DBHelper.GetDataSet(sql);
            ReportDocument cryRpt = new ReportDocument();
            cryRpt.Load(rpDirectory + "\\covernote.rpt");
            cryRpt.SetDataSource(dt);
            cryDocViewer temp_form = new cryDocViewer(cryRpt);

            try
            {
                ExportOptions CrExportOptions;
                DiskFileDestinationOptions CrDiskFileDestinationOptions = new DiskFileDestinationOptions();
                PdfRtfWordFormatOptions CrFormatTypeOptions = new PdfRtfWordFormatOptions();

                CrDiskFileDestinationOptions.DiskFileName = "M:\\PushToA8\\" + CoverNo.Text.Trim() + ".doc";

                CrExportOptions = cryRpt.ExportOptions;
                {
                    CrExportOptions.ExportDestinationType = ExportDestinationType.DiskFile;
                    CrExportOptions.ExportFormatType = ExportFormatType.WordForWindows;
                    CrExportOptions.DestinationOptions = CrDiskFileDestinationOptions;
                    CrExportOptions.FormatOptions = CrFormatTypeOptions;
                }
                cryRpt.Export();
                Word2PDF WP = new Word2PDF();
                WP.word2PDF("M:\\PushToA8\\" + CoverNo.Text.Trim() + ".doc", "M:\\PushToA8\\" + CoverNo.Text.Trim() + ".pdf");

                try
                {
                    string str = ToA8.ToA8process("CoverNote", confirm, CoverNo.Text.Trim(), fctlid);
                    if (str == "已经上传过") {
                        reload(CoverNo.Text, "Confirm");
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.ToString(), "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
            progressBar1.Visible = false;
        }

        private string ExecuteSqlTransaction(string connectionString, string sql)
        {
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                SqlCommand command = connection.CreateCommand();
                SqlTransaction transaction;
                transaction = connection.BeginTransaction("SampleTransaction");

                command.Connection = connection;
                command.Transaction = transaction;

                try
                {
                    if (sql != null)
                    {
                        command.CommandText = sql;
                        command.ExecuteNonQuery();
                    }
                    transaction.Commit();
                    return "OK";
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                    Console.WriteLine("  Message: {0}", ex.Message);

                    try
                    {
                        transaction.Rollback();
                        return "RollBack";
                    }
                    catch (Exception ex2)
                    {
                        Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                        Console.WriteLine("  Message: {0}", ex2.Message);
                        return ex2.Message;
                    }
                }
            }
        }


    }
}

