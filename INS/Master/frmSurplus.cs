using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using System.Globalization;
using System.Windows.Documents;
using System.Text.RegularExpressions;
using System.Configuration;
using System.Collections;

namespace INS
{
    public partial class frmSurplus : Form
    {
        List<string> list = new List<string>();
        List<string> list1 = new List<string>();
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public frmSurplus()
        {
            InitializeComponent();
            InitCombobox();
            FillData("", "");
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            foreach (Control control in contact.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            foreach (Control control in panel2.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
        }
        void control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
            {
                SendKeys.Send("{TAB}");
            }
        }
        private DataTable dataSource = new DataTable();
        private DataTable dataSource1 = new DataTable();
        public int i = 0, counter;
        DES des = new DES();
        DBConnect operate = new DBConnect();
        ExportDBF DBF = new ExportDBF();
        XLS txt = new XLS();
        public string fctlid_a = "";
        public String user = InsEnvironment.LoginUser.GetUserCode();
        public int row = 0;
        public int row1 = 0;
        public Boolean flag = false;
        public Boolean ADD = false;
        public Boolean Update = false;
        public Boolean flag1 = false;
        private bool datagridviewSelectionChanged = true;
        private bool datagridview2SelectionChanged = true;
        private bool datagridview3SelectionChanged = true;
        public string db = InsEnvironment.DataBase.GetDbm();

        void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            try
            {
                if (order != "" && query != "")
                {
                    if (order == "TTY#")
                    {
                        a = new SqlDataAdapter("select fctlid,fid as TTY#,fdesc as Desc#,fefffr as [Eff From] ,feffto as [Eff To],fcur , " +
        "case when factive='1' then 'Yes' else 'No' end as factive from mty " +
        "where  fid like '%" + query.ToUpper() + "%' order by " + order, c);
                    }
                    else
                    {
                        a = new SqlDataAdapter("select fctlid,fid as TTY#,fdesc as Desc#,fefffr as [Eff From] ,feffto as [Eff To],fcur , " +
                            "case when factive='1' then 'Yes' else 'No' end as factive from mty " +
                            "where  fdesc like '%" + query.ToUpper() + "%' order by " + order, c);
                    }
                }
                else if (order != "" && query == "")
                {
                    a = new SqlDataAdapter("select fctlid,fid as TTY#,fdesc as Desc#,fefffr as [Eff From] ,feffto as [Eff To],fcur , " +
    "case when factive='1' then 'Yes' else 'No' end as factive from mty order by " + order, c);
                }
                else
                {
                    a = new SqlDataAdapter("select fctlid,fid as TTY#,fdesc as Desc#,fefffr as [Eff From] ,feffto as [Eff To],fcur , " +
    "case when factive='1' then 'Yes' else 'No' end as factive from mty order by fid ", c);

                }
                DataTable t = new DataTable();
                a.Fill(t);
                dataGridView1.DataSource = t;
                this.dataGridView1.Columns[0].Visible = false;
                this.dataGridView1.Columns[4].Visible = false;
                this.dataGridView1.Columns[5].Visible = false;
            }
            catch { }

            c.Close();
        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            if (ADD == true || Update == true)
            {
                if (e.TabPage == Classes)
                    e.Cancel = true;
            }
        }

        private void dataGridView1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            if (!this.datagridviewSelectionChanged) return;
            try
            {
                if (flag == false && dataGridView1.CurrentCell != null)
                {
                    counter = dataGridView1.CurrentCell.RowIndex;
                }

                if (dataGridView1.Rows[counter].Cells["TTY#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["TTY#"].Value.ToString().Length != 0)
                    {
                        textBox8.Text = dataGridView1.Rows[counter].Cells["TTY#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        textBox9.Text = dataGridView1.Rows[counter].Cells["Desc#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Eff From"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Eff From"].Value.ToString().Length != 0)
                    {
                        if (dataGridView1.Rows[counter].Cells["Eff From"].Value.ToString() != "")
                        {
                            try
                            {
                                string a = dataGridView1.Rows[counter].Cells["Eff From"].Value.ToString();
                                DateTime b = Convert.ToDateTime(a);
                                textBox10.Text = b.ToString("yyyy.MM.dd");
                            }
                            catch { }
                        }
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Eff To"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Eff To"].Value.ToString().Length != 0)
                    {
                        if (dataGridView1.Rows[counter].Cells["Eff To"].Value.ToString() != "")
                        {
                            try
                            {
                                string a = dataGridView1.Rows[counter].Cells["Eff To"].Value.ToString();
                                DateTime b = Convert.ToDateTime(a);
                                textBox11.Text = b.ToString("yyyy.MM.dd");
                            }
                            catch { }
                        }
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fcur"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fcur"].Value.ToString().Length != 0)
                    {
                        comboBox3.SelectedItem = dataGridView1.Rows[counter].Cells["fcur"].Value.ToString();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["factive"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["factive"].Value.ToString().Length != 0)
                    {
                        comboBox4.SelectedItem = dataGridView1.Rows[counter].Cells["factive"].Value.ToString();
                    }
                }
                if (ADD == false)
                {
                    fctlid_a = dataGridView1.Rows[counter].Cells["fctlid"].Value.ToString();
                    reloadgridview2("");
                    reloadgridview3("");
                }
            }
            catch { }
        }

        void reloadgridview1(string fid, string lab)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select fctlid,fid as TTY#,fdesc as Desc#,fefffr as [Eff From] ,feffto as [Eff To],fcur , " +
                    "case when factive='1' then 'Yes' else 'No' end as factive from mty order by fid ", c))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                if (lab == "insert" || lab == "update")
                {
                    dataGridView1.DataSource = t2;
                    if (fid != "")
                    {
                        for (int i = 0; i < t2.Rows.Count; i++)
                        {
                            if (t2.Rows[i]["TTY#"].ToString().Trim() == fid.Trim())
                            {
                                counter = i;
                                dataGridView1.CurrentCell = dataGridView1.Rows[i].Cells["TTY#"];
                                break;
                            }
                        }
                    }
                }
                if (lab == "del")
                {
                    datagridviewSelectionChanged = false;
                    dataGridView1.DataSource = t2;
                    datagridviewSelectionChanged = true;
                    if (counter == 0)
                    {
                        dataGridView1.CurrentCell = dataGridView1.Rows[counter].Cells["Desc#"];
                    }
                    else
                    {
                        dataGridView1.CurrentCell = dataGridView1.Rows[counter - 1].Cells["Desc#"];
                    }
                }
                if (lab == "cancel")
                {
                    datagridviewSelectionChanged = false;
                    dataGridView1.DataSource = t2;
                    datagridviewSelectionChanged = true;
                    if (counter >= t2.Rows.Count)
                    {
                        dataGridView1.CurrentCell = dataGridView1.Rows[counter - 1].Cells["Desc#"];
                    }
                    else { dataGridView1.CurrentCell = dataGridView1.Rows[counter].Cells["Desc#"]; }
                }
                if (lab == "")
                {
                    if (t2.Rows.Count != 0)
                    {
                        dataGridView1.DataSource = t2;
                        dataGridView1.CurrentCell = dataGridView1.Rows[counter].Cells["TTY#"];
                    }
                }
            }
            dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            datagridviewSelectionChanged = true;
            c.Close();
        }

        void reloadgridview2(string fid)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select a.fctlid, a.flogseq as Seq#, freinsr as Reinsurer,fshare as [Share (%)], fbrkr1, " +
                                "b.fdesc as Bdesc,c.fdesc as Rdesc, a.fctlid_1 from mtyinsr a " +
                                "left join (select fid,fdesc  from mprdr where ftype='B') b on a.fbrkr1=b.fid " +
                                "left join (select fid,fdesc from mprdr where ftype='R') c on a.freinsr=c.fid " +
                                "where fctlid_1 = '" + fctlid_a + "' ORDER BY flogseq", c))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                dataGridView2.DataSource = null;
                dataGridView2.DataSource = t2;
                if (t2.Rows.Count == 0)
                {
                    textBox12.Text = "";
                    textBox13.Text = "";
                    textBox14.Text = "";
                    textBox15.Text = "";
                    textBox16.Text = "";
                    textBox17.Text = "";
                }
                else
                {
                    dataGridView2.CurrentCell = dataGridView2.Rows[row].Cells["Seq#"];
                }
                this.dataGridView2.Columns[0].Visible = false;
                this.dataGridView2.Columns[4].Visible = false;
                this.dataGridView2.Columns[5].Visible = false;
                this.dataGridView2.Columns[6].Visible = false;
                this.dataGridView2.Columns[7].Visible = false;
                int total = 0;
                for (int i = 0; i < t2.Rows.Count; i++)
                {
                    total = total + int.Parse(t2.Rows[i]["Share (%)"].ToString().Trim().Replace(".0000", ""));
                }
                textBox18.Text = total.ToString();
                textBox18.Text = string.Format("{0:N4}", Convert.ToDecimal(textBox18.Text));
            }
            datagridview2SelectionChanged = true;
            flag = false;
            c.Close();

        }

        void reloadgridview3(string fid)
        {
            if (Update == true)
            {
                dataGridView3.DataSource = null;

                SqlParameter[] param = new SqlParameter[] {
                    new SqlParameter("@fctlid_a", fctlid_a)};

                dataGridView3.DataSource = DBConnect.GetDataSetProd("SurplusList", Dbm, param);
                for (int i = 0; i < dataGridView3.Rows.Count; i++)
                {
                    dataGridView3["Treaty Limit", i].Value = Convert.ToDecimal(dataGridView3["Treaty Limit", i].Value);
                }
                if (dataGridView3.Rows.Count == 0)
                {
                    textBox2.Text = "";
                    textBox3.Text = "";
                    textBox4.Text = "";
                    textBox5.Text = "";
                    textBox6.Text = "";
                    textBox7.Text = "";
                }
                else
                {
                    dataGridView3.CurrentCell = dataGridView3.Rows[row1].Cells["Sec#"];
                }
                this.dataGridView3.Columns[0].Visible = false;
                this.dataGridView3.Columns[3].Visible = false;
                this.dataGridView3.Columns[4].Visible = false;
                this.dataGridView3.Columns[5].Visible = false;
                this.dataGridView3.Columns[6].Visible = false;
                this.dataGridView3.Columns[7].Visible = false;
                this.dataGridView3.Columns[8].Visible = false;
                this.dataGridView3.Columns[9].Visible = false;
                this.dataGridView3.Columns[10].Visible = false;
            }
            else
            {
                dataGridView3.DataSource = null;
                SqlParameter[] param = new SqlParameter[] {
                    new SqlParameter("@fctlid_a", fctlid_a)};
                dataGridView3.DataSource = DBConnect.GetDataSetProd("SurplusList", Dbm, param);
                for (int i = 0; i < dataGridView3.Rows.Count; i++)
                {
                    dataGridView3["Treaty Limit", i].Value = Convert.ToDecimal(dataGridView3["Treaty Limit", i].Value);
                }
                if (dataGridView3.Rows.Count == 0)
                {
                    textBox2.Text = "";
                    textBox3.Text = "";
                    textBox4.Text = "";
                    textBox5.Text = "";
                    textBox6.Text = "";
                    textBox7.Text = "";
                }
                else
                {
                    dataGridView3.CurrentCell = dataGridView3.Rows[row1].Cells["Sec#"];
                }
                this.dataGridView3.Columns[0].Visible = false;
                this.dataGridView3.Columns[3].Visible = false;
                this.dataGridView3.Columns[4].Visible = false;
                this.dataGridView3.Columns[5].Visible = false;
                this.dataGridView3.Columns[6].Visible = false;
                this.dataGridView3.Columns[7].Visible = false;
                this.dataGridView3.Columns[8].Visible = false;
                this.dataGridView3.Columns[9].Visible = false;
                this.dataGridView3.Columns[10].Visible = false;
            }

            datagridview3SelectionChanged = true;
            flag1 = false;
        }

        private void dataGridView2_SelectionChanged(object sender, EventArgs e)
        {
            if (!this.datagridview2SelectionChanged) return;
            try
            {
                if (flag == false && dataGridView2.CurrentCell != null)
                {
                    row = dataGridView2.CurrentCell.RowIndex;
                }

                if (dataGridView2.Rows[row].Cells["Seq#"].Value != null)
                {
                    textBox12.Text = dataGridView2.Rows[row].Cells["Seq#"].Value.ToString().Trim();
                }
                if (dataGridView2.Rows[row].Cells["fbrkr1"].Value != null)
                {
                    textBox13.Text = dataGridView2.Rows[row].Cells["fbrkr1"].Value.ToString().Trim();
                }
                if (dataGridView2.Rows[row].Cells["Bdesc"].Value != null)
                {
                    textBox14.Text = dataGridView2.Rows[row].Cells["Bdesc"].Value.ToString().Trim();
                }
                if (dataGridView2.Rows[row].Cells["Reinsurer"].Value != null)
                {
                    textBox15.Text = dataGridView2.Rows[row].Cells["Reinsurer"].Value.ToString().Trim();
                }
                if (dataGridView2.Rows[row].Cells["Rdesc"].Value != null)
                {
                    textBox16.Text = dataGridView2.Rows[row].Cells["Rdesc"].Value.ToString().Trim();
                }
                if (dataGridView2.Rows[row].Cells["Share (%)"].Value != null)
                {
                    textBox17.Text = dataGridView2.Rows[row].Cells["Share (%)"].Value.ToString().Trim();
                }
                if (dataGridView2.Rows[row].Cells["fctlid"].Value != null)
                {
                    fctlidlabelinfo.Text = dataGridView2.Rows[row].Cells["fctlid"].Value.ToString();
                }
                rowlabelinfo.Text = row.ToString();
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void dataGridView3_SelectionChanged(object sender, EventArgs e)
        {
            if (!this.datagridview3SelectionChanged) return;
            try
            {
                if (flag1 == false && dataGridView3.CurrentCell != null)
                {
                    row1 = dataGridView3.CurrentCell.RowIndex;
                }

                if (dataGridView3.Rows[row1].Cells["Sec#"].Value != null)
                {
                    textBox2.Text = dataGridView3.Rows[row1].Cells["Sec#"].Value.ToString().Trim();
                }
                if (dataGridView3.Rows[row1].Cells["Treaty Limit"].Value != null)
                {
                    textBox3.Text = dataGridView3.Rows[row1].Cells["Treaty Limit"].Value.ToString().Trim();
                }
                if (dataGridView3.Rows[row1].Cells["ftimes"].Value != null)
                {
                    textBox7.Text = dataGridView3.Rows[row1].Cells["ftimes"].Value.ToString().Trim();
                }
                if (dataGridView3.Rows[row1].Cells["fscale1"].Value != null)
                {
                    textBox4.Text = dataGridView3.Rows[row1].Cells["fscale1"].Value.ToString().Trim();
                }

                if (dataGridView3.Rows[row1].Cells["fscale2"].Value != null)
                {
                    textBox5.Text = dataGridView3.Rows[row1].Cells["fscale2"].Value.ToString().Trim();
                }

                if (dataGridView3.Rows[row1].Cells["fcom"].Value != null)
                {
                    textBox6.Text = dataGridView3.Rows[row1].Cells["fcom"].Value.ToString().Trim();
                }

                if (dataGridView3.Rows[row1].Cells["fgrnet"].Value != null)
                {
                    comboBox2.Text = dataGridView3.Rows[row1].Cells["fgrnet"].Value.ToString().Trim();
                }

                if (dataGridView3.Rows[row1].Cells["fctlid"].Value != null)
                {
                    fctlidlabelcontact.Text = dataGridView3.Rows[row1].Cells["fctlid"].Value.ToString();
                }
                if (dataGridView3.Rows[row1].Cells["list"].Value != null)
                {

                    string[] parts = dataGridView3.Rows[row1].Cells["list"].Value.ToString().Split(',');
                    List<string> listl1 = new List<string>(parts);
                    list.Clear();
                    foreach (string item in listl1)
                    {
                        list.Add(item);
                    }
                    listBox1.DataSource = null;
                    listBox1.DataSource = list;
                }
                if (dataGridView3.Rows[row1].Cells["list1"].Value != null)
                {

                    string[] parts = dataGridView3.Rows[row1].Cells["list1"].Value.ToString().Split(',');
                    List<string> listl2 = new List<string>(parts);
                    list1.Clear();
                    foreach (string item in listl2)
                    {
                        list1.Add(item);
                    }
                    listBox2.DataSource = null;
                    listBox2.DataSource = list1;
                }
                rowlabelcontact.Text = row1.ToString();
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", "");
        }

        private void button1_Click(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", textBox1.Text.ToString().Trim());
        }

        public enum Mode
        {
            TTY = 1,
            Desc = 2
        }

        private void InitCombobox()
        {
            Array arr = System.Enum.GetValues(typeof(Mode));    // 获取枚举的所有值
            DataTable dt = new DataTable();
            dt.Columns.Add("String", Type.GetType("System.String"));
            dt.Columns.Add("Value", typeof(int));
            foreach (var a in arr)
            {
                string strText = EnumTextByDescription.GetEnumDesc((Mode)a);
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = (int)a;

                dt.Rows.Add(aRow);
            }

            comboBox1.DataSource = dt;
            comboBox1.DisplayMember = "String";
            comboBox1.ValueMember = "Value";
        }


        void ButtonControlback()
        {
            ADD = false;
            Update = false;
            textBox8.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox8.ReadOnly = true;
            textBox9.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox9.ReadOnly = true;
            textBox10.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox10.ReadOnly = true;
            textBox11.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox11.ReadOnly = true;
            comboBox3.BackColor = System.Drawing.SystemColors.InactiveCaption;
            comboBox3.Enabled = false;
            comboBox4.BackColor = System.Drawing.SystemColors.InactiveCaption;
            comboBox4.Enabled = false;
            comboBox2.BackColor = System.Drawing.SystemColors.InactiveCaption;
            comboBox2.Enabled = false;

            textBox12.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox12.ReadOnly = true;
            textBox13.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox13.ReadOnly = true;
            textBox15.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox15.ReadOnly = true;
            textBox17.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox17.ReadOnly = true;
            textBox2.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox2.ReadOnly = true;
            textBox3.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox3.ReadOnly = true;
            textBox4.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox4.ReadOnly = true;
            textBox7.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox7.ReadOnly = true;
            textBox5.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox5.ReadOnly = true;
            textBox6.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox6.ReadOnly = true;

            dcancel.Visible = false;
            dsave.Visible = false;
            dexit.Visible = true;
            dadd.Visible = true;
            dupdate.Visible = true;
            ddel.Visible = true;
            csave.Visible = false;
            cCancel.Visible = false;
            cadd.Visible = true;
            cdel.Visible = true;
            cupdate.Visible = true;
            cexit.Visible = true;
            add1.Visible = false;
            add2.Visible = false;
            del1.Visible = false;
            del2.Visible = false;
            button4.Enabled = false;
            button5.Enabled = false;
            button2.Enabled = false;
            button3.Enabled = false;
            button6.Enabled = false;
            button7.Enabled = false;
        }

        void ButtonControl()
        {
            textBox8.BackColor = System.Drawing.SystemColors.Window;
            textBox8.ReadOnly = false;
            textBox9.BackColor = System.Drawing.SystemColors.Window;
            textBox9.ReadOnly = false;
            textBox10.BackColor = System.Drawing.SystemColors.Window;
            textBox10.ReadOnly = false;
            textBox11.BackColor = System.Drawing.SystemColors.Window;
            textBox11.ReadOnly = false;
            comboBox3.BackColor = System.Drawing.SystemColors.Window;
            comboBox3.Enabled = true;
            comboBox4.BackColor = System.Drawing.SystemColors.Window;
            comboBox4.Enabled = true;
            dcancel.Visible = true;
            dsave.Visible = true;
            dexit.Visible = false;
            dadd.Visible = false;
            dupdate.Visible = false;
            ddel.Visible = false;
            csave.Visible = true;
            cCancel.Visible = true;
            cadd.Visible = false;
            cdel.Visible = false;
            cupdate.Visible = false;
            cexit.Visible = false;
            add1.Visible = true;
            add2.Visible = true;
            del1.Visible = true;
            del2.Visible = true;
            button4.Enabled = true;
            button5.Enabled = true;
            button2.Enabled = true;
            button3.Enabled = true;
            button6.Enabled = true;
            button7.Enabled = true;
        }

        void ButtonControladd()
        {
            ADD = true;
            DataTable listdataSource = dataGridView1.DataSource as DataTable;
            DataRow newCustomersRow = listdataSource.NewRow();
            listdataSource.Rows.InsertAt(newCustomersRow, counter);
            dataGridView1.DataSource = listdataSource;
            dataGridView1.CurrentCell = dataGridView1.Rows[counter - 1].Cells["TTY#"];

            textBox8.Text = "";
            textBox9.Text = "";
            textBox10.Text = "";
            textBox11.Text = "";
            textBox12.Text = "";
            textBox13.Text = "";
            textBox14.Text = "";
            textBox15.Text = "";
            textBox16.Text = "";
            textBox17.Text = "";
            textBox2.Text = "";
            textBox3.Text = "";
            textBox7.Text = "";
            textBox4.Text = "";
            textBox5.Text = "";
            textBox6.Text = "";

            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);
        }

        void ButtonCountrolupdate()
        {
            Update = true;
            if (dataGridView2.RowCount > 0)
            {
                textBox12.BackColor = System.Drawing.SystemColors.Window;
                textBox12.ReadOnly = false;
                textBox13.BackColor = System.Drawing.SystemColors.Window;
                textBox13.ReadOnly = false;
                textBox15.BackColor = System.Drawing.SystemColors.Window;
                textBox15.ReadOnly = false;
                textBox17.BackColor = System.Drawing.SystemColors.Window;
                textBox17.ReadOnly = false;
            }
            reloadgridview3("");
            if (dataGridView3.RowCount > 0)
            {
                textBox2.BackColor = System.Drawing.SystemColors.Window;
                textBox2.ReadOnly = false;
                textBox3.BackColor = System.Drawing.SystemColors.Window;
                textBox3.ReadOnly = false;
                textBox7.BackColor = System.Drawing.SystemColors.Window;
                textBox7.ReadOnly = false;
                textBox4.BackColor = System.Drawing.SystemColors.Window;
                textBox4.ReadOnly = false;
                textBox5.BackColor = System.Drawing.SystemColors.Window;
                textBox5.ReadOnly = false;
                textBox6.BackColor = System.Drawing.SystemColors.Window;
                textBox6.ReadOnly = false;
                comboBox2.BackColor = System.Drawing.SystemColors.Window;
                comboBox2.Enabled = true;
            }
        }

        private void button9_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void dexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void cexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void dcancel_Click(object sender, EventArgs e)
        {
            ButtonControlback();
            reload();
            reloadgridview1("", "cancel");
        }

        private void cCancel_Click(object sender, EventArgs e)
        {
            ButtonControlback();
            reload();
            reloadgridview1("", "cancel");
        }

        private void lupdate_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonCountrolupdate();
        }

        private void dupdate_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonCountrolupdate();
        }

        private void cupdate_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonCountrolupdate();
        }

        private void ladd_Click(object sender, EventArgs e)
        {
            add();
        }

        private void dadd_Click(object sender, EventArgs e)
        {
            add();
        }

        private void cadd_Click(object sender, EventArgs e)
        {
            add();
        }

        void add()
        {
            tabControl1.SelectedTab = Clauses;
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select a.fctlid, a.flogseq as Seq#, freinsr as Reinsurer,'' as [Share (%)], fbrkr1, " +
                                "b.fdesc as Bdesc,c.fdesc as Rdesc, a.fctlid_1 from mtyinsr a " +
                                "left join (select fid,fdesc  from mprdr where ftype='B') b on a.fbrkr1=b.fid " +
                                "left join (select fid,fdesc from mprdr where ftype='R') c on a.freinsr=c.fid " +
                                "where fctlid_1 = '1' ORDER BY flogseq", c))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                dataGridView2.DataSource = t2;
                this.dataGridView2.Columns[0].Visible = false;
                this.dataGridView2.Columns[4].Visible = false;
                this.dataGridView2.Columns[5].Visible = false;
                this.dataGridView2.Columns[6].Visible = false;
                this.dataGridView2.Columns[7].Visible = false;
            }
            c.Close();
            SqlConnection c1 = DBConnect.dbConn;
            if (c1.State == ConnectionState.Closed)
            { c1.Open(); }

            using (SqlDataAdapter a1 = new SqlDataAdapter("select fctlid, '' as Sec#, '' as [Treaty Limit], ftimes, '' AS fscale1 ,'' as fscale2 ,'' as fcom ,case when fgrnet='1' then 'Gross' else 'Net' end as fgrnet, fctlid_1,'' as list, '' as list1 " +
                "from mtysec where fctlid_1 = '1' ORDER BY fid", c1))
            {
                DataTable t3 = new DataTable();
                a1.Fill(t3);
                dataGridView3.DataSource = t3;
                this.dataGridView3.Columns[0].Visible = false;
                this.dataGridView3.Columns[3].Visible = false;
                this.dataGridView3.Columns[4].Visible = false;
                this.dataGridView3.Columns[5].Visible = false;
                this.dataGridView3.Columns[6].Visible = false;
                this.dataGridView3.Columns[7].Visible = false;
                this.dataGridView3.Columns[8].Visible = false;
                this.dataGridView3.Columns[9].Visible = false;
                this.dataGridView3.Columns[10].Visible = false;
            }
            c1.Close();
            list.Clear();
            list1.Clear();
            listBox1.DataSource = null;
            listBox2.DataSource = null;
            ButtonControl();
            ButtonControladd();
        }

        private void ldel_Click(object sender, EventArgs e)
        {
            del();
        }

        private void ddel_Click(object sender, EventArgs e)
        {
            del();
        }

        private void cdel_Click(object sender, EventArgs e)
        {
            del();
        }

        void del()
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the ALL items?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string deldatestr1 =
                    "delete from mty where fctlid='" + fctlid_a + "'";
                operate.OperateData(deldatestr1);
                string deldatestr =
                    "delete from mtyinsr where fctlid_1='" + fctlid_a + "'";
                operate.OperateData(deldatestr);
                string deldatestr2 =
                    "delete from mtysec where fctlid_1='" + fctlid_a + "'";
                operate.OperateData(deldatestr2);
                MessageBox.Show("Have Been Deleted", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                FillData("", "");
                tabControl1.SelectedTab = Classes;
            }

        }

        private void textBox8_TextChanged(object sender, EventArgs e)
        {
            dataGridView1.Rows[counter].Cells["TTY#"].Value = textBox8.Text;
            string result2 = CheckDuplicate(textBox8.Text);
            if (result2 != "")
            {
                MessageBox.Show(result2);
                textBox8.Focus();
                textBox8.SelectAll();
            }
        }

        public string CheckDuplicate(string fid)
        {
            if (fid != "")
            {
                DataTable listdataSource = dataGridView1.DataSource as DataTable;
                if (listdataSource.Rows.Count > 0)
                {
                    DataTable objectTable = listdataSource.DefaultView.ToTable();
                    DataRow[] rows = objectTable.Select("[TTY#]='" + fid.Trim() + "'");
                    if (rows.Length > 1)
                    {
                        return "Duplicate Value";
                    }
                    else { return ""; }
                }
                else { return ""; }
            }
            else { return ""; }
        }

        private void textBox17_TextChanged(object sender, EventArgs e)
        {
            if (dataGridView2.CurrentCell != null && (ADD == true || Update == true))
            {
                dataGridView2["Share (%)", dataGridView2.CurrentCell.RowIndex].Value = textBox17.Text;
                total();
            }
        }

        void total()
        {
            Decimal sum = 0;
            for (int i = 0; i < dataGridView2.Rows.Count; i++)
            {
                if (dataGridView2["Share (%)", i].Value.ToString() != "")
                {
                    sum = sum + Convert.ToDecimal(dataGridView2["Share (%)", i].Value);
                }
            }
            textBox18.Text = string.Format("{0:N0}", Convert.ToDecimal(sum.ToString().Replace(",", "")));
        }

        private void textBox3_TextChanged(object sender, EventArgs e)
        {
            if (dataGridView3.CurrentCell != null && (ADD == true || Update == true))
            {
                dataGridView3["Treaty Limit", dataGridView3.CurrentCell.RowIndex].Value = textBox3.Text;
            }
        }
        private void textBox4_TextChanged(object sender, EventArgs e)
        {
            if (dataGridView3.CurrentCell != null && (ADD == true || Update == true))
            {
                dataGridView3["fscale1", dataGridView3.CurrentCell.RowIndex].Value = textBox4.Text;
            }
        }
        private void textBox5_TextChanged(object sender, EventArgs e)
        {
            if (dataGridView3.CurrentCell != null && (ADD == true || Update == true))
            {
                dataGridView3["fscale2", dataGridView3.CurrentCell.RowIndex].Value = textBox5.Text;
            }
        }

        private void textBox6_TextChanged(object sender, EventArgs e)
        {
            if (dataGridView3.CurrentCell != null && (ADD == true || Update == true))
            {
                dataGridView3["fcom", dataGridView3.CurrentCell.RowIndex].Value = textBox6.Text;
            }
        }

        private void comboBox2_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (dataGridView3.CurrentCell != null && (ADD == true || Update == true))
            {
                dataGridView3["fgrnet", dataGridView3.CurrentCell.RowIndex].Value = comboBox2.Text;
            }
        }

        private void textBox12_TextChanged(object sender, EventArgs e)
        {
            if (textBox12.Text != "")
            {
                Regex r = new Regex("^[0-9]{1,}$");
                if (!r.IsMatch(textBox12.Text))
                {
                    MessageBox.Show("Please input number!");
                    textBox12.Text.Remove(textBox12.Text.Length - 1);
                }
                dataGridView2["Seq#", row].Value = int.Parse(textBox12.Text.ToString());
            }
        }

        private void textBox13_TextChanged(object sender, EventArgs e)
        {
            if (textBox13.Text != "")
            {
                dataGridView2["fbrkr1", row].Value = textBox13.Text;
            }
        }

        private void textBox15_TextChanged(object sender, EventArgs e)
        {
            if (textBox15.Text != "")
            {
                dataGridView2["Reinsurer", row].Value = textBox15.Text;
            }
        }

        private void textBox2_TextChanged(object sender, EventArgs e)
        {
            if (textBox2.Text != "")
            {
                Regex r = new Regex("^[0-9]{1,}$");
                if (!r.IsMatch(textBox2.Text))
                {
                    MessageBox.Show("Please input number!");
                }
                dataGridView3["Sec#", row1].Value = int.Parse(textBox2.Text.ToString());
            }
        }

        private void textBox7_TextChanged(object sender, EventArgs e)
        {
            if (textBox7.Text != "")
            {
                dataGridView3["ftimes", row1].Value = textBox7.Text.ToString().Replace(",", "");
            }
        }

        private void textBox13_Validated(object sender, EventArgs e)
        {
            string desc = CheckValue(textBox13.Text.ToString(), "Broker");
            if (desc == null)
            {
                textBox13.Text = "";
                MessageBox.Show("Invalid Value");
            }
            else
            {
                textBox14.Text = desc;
            }
        }

        private void textBox15_Validated(object sender, EventArgs e)
        {
            string desc = CheckValue(textBox15.Text.ToString(), "Reinsurer");
            if (desc == null)
            {
                textBox15.Text = "";
                MessageBox.Show("Invalid Value");
            }
            else
            {
                textBox16.Text = desc;
            }
        }

        public string CheckValue(string fid, string flag)
        {
            string str = "";

            if (flag == "Broker")
            {
                str = "select fid,fdesc from " + db + "mprdr where ftype='B' and fid='" + fid + "'";
            }
            if (flag == "Reinsurer")
            {
                str = "select fid,fdesc from " + db + "mprdr where ftype='R' and fid='" + fid + "'";
            }
            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["odata"].ConnectionString);
            SqlCommand cmd = new SqlCommand(str, con);
            con.Open();
            using (SqlDataReader sdr = cmd.ExecuteReader())
            {
                if (sdr.HasRows && sdr.Read())
                {
                    string fdesc = sdr["fdesc"].ToString().Trim();
                    sdr.Close();
                    return fdesc;
                }
                else { return null; }
            }
        }

        private void del1_Click(object sender, EventArgs e)
        {
            if (dataGridView2.RowCount != 0)
            {
                try
                {
                    DataTable dataGridView2Source = dataGridView2.DataSource as DataTable;
                    dataGridView2Source.Rows.RemoveAt(dataGridView2.CurrentCell.RowIndex);
                    dataGridView2.DataSource = dataGridView2Source;
                    dataGridView2.CurrentCell = dataGridView2.Rows[dataGridView2.Rows.Count - 1].Cells["Seq#"];
                }
                catch { }
            }
            if (dataGridView2.RowCount == 0)
            {
                foreach (Control ctrl in panel2.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.InactiveCaption;
                    }
                    if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                    if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
                }
            }
            total();
        }

        private void del2_Click(object sender, EventArgs e)
        {
            if (dataGridView3.RowCount != 0)
            {
                try
                {
                    DataTable dataGridView3Source = dataGridView3.DataSource as DataTable;
                    dataGridView3Source.Rows.RemoveAt(dataGridView3.CurrentCell.RowIndex);
                    dataGridView3.DataSource = dataGridView3Source;
                    dataGridView3.CurrentCell = dataGridView3.Rows[dataGridView2.Rows.Count - 1].Cells["Sec#"];
                }
                catch { }
            }
            if (dataGridView3.RowCount == 0)
            {
                foreach (Control ctrl in panel2.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.InactiveCaption;
                    }
                    if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                    if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
                }
            }
        }

        private void add1_Click(object sender, EventArgs e)
        {
            dataSource = dataGridView2.DataSource as DataTable;
            DataRow newCustomersRow = dataSource.NewRow();
            dataSource.Rows.InsertAt(newCustomersRow, dataSource.Rows.Count);
            dataGridView2.DataSource = dataSource;
            dataGridView2.CurrentCell = dataGridView2.Rows[dataSource.Rows.Count - 1].Cells["Seq#"];

            textBox12.BackColor = System.Drawing.SystemColors.Window;
            textBox12.ReadOnly = false;
            textBox12.Text = "";
            textBox13.BackColor = System.Drawing.SystemColors.Window;
            textBox13.ReadOnly = false;
            textBox13.Text = "";
            textBox14.Text = "";
            textBox16.Text = "";
            textBox15.BackColor = System.Drawing.SystemColors.Window;
            textBox15.ReadOnly = false;
            textBox15.Text = "";
            textBox17.BackColor = System.Drawing.SystemColors.Window;
            textBox17.ReadOnly = false;
            textBox17.Text = "0.0";
        }

        private void add2_Click(object sender, EventArgs e)
        {
            dataSource1 = dataGridView3.DataSource as DataTable;
            DataRow newCustomersRow = dataSource1.NewRow();
            dataSource1.Rows.InsertAt(newCustomersRow, dataSource1.Rows.Count);
            dataGridView3.DataSource = dataSource1;
            dataGridView3.CurrentCell = dataGridView3.Rows[dataSource1.Rows.Count - 1].Cells["Sec#"];
            dataGridView3["list", dataSource1.Rows.Count - 1].Value = "CAR (SEC 1),CPM,FGP,MYP,PAR";
            string[] parts = dataGridView3.Rows[dataSource1.Rows.Count - 1].Cells["list"].Value.ToString().Split(',');
            List<string> listl1 = new List<string>(parts);
            list.Clear();
            list1.Clear();
            foreach (string item in listl1)
            {
                list.Add(item);
            }
            listBox1.DataSource = null;
            listBox1.DataSource = list;
            textBox2.BackColor = System.Drawing.SystemColors.Window;
            textBox2.ReadOnly = false;
            textBox2.Text = "";
            textBox3.BackColor = System.Drawing.SystemColors.Window;
            textBox3.ReadOnly = false;
            textBox3.Text = "";
            textBox7.BackColor = System.Drawing.SystemColors.Window;
            textBox7.ReadOnly = false;
            textBox7.Text = "";
            textBox4.BackColor = System.Drawing.SystemColors.Window;
            textBox4.ReadOnly = false;
            textBox4.Text = "";
            textBox5.BackColor = System.Drawing.SystemColors.Window;
            textBox5.ReadOnly = false;
            textBox5.Text = "";
            textBox6.BackColor = System.Drawing.SystemColors.Window;
            textBox6.ReadOnly = false;
            textBox6.Text = "";
            comboBox2.BackColor = System.Drawing.SystemColors.Window;
            comboBox2.Enabled = true;

        }

        private void dsave_Click(object sender, EventArgs e)
        {
            saveadd();
        }

        private void csave_Click(object sender, EventArgs e)
        {
            saveadd();
        }

        void saveadd()
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really Insert the item?", "Inserted Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                String fctlid_contact = fctlidlabelcontact.Text;
                String fctlid_info = fctlidlabelinfo.Text;
                String active = "";

                if (comboBox4.SelectedItem.ToString() == "Yes")
                { active = "1"; }
                else { active = "2"; }

                if (ADD == true)
                {
                    string str = "select * from xsysparm where fidtype ='MTY'";
                    DataTable dt = new DataTable();
                    dt = operate.GetTable(str);
                    fctlid_a = dt.Rows[0]["fnxtid"].ToString();
                }

                if (textBox8.Text.Trim() != "")
                {
                    if (ADD == true)
                    {
                        string adddatestr = "insert into mty(fctlid,fid,fdesc,fbrkr,fefffr,feffto,fcur,factive,ftshare,fctldel,finpuser,finpdate,fupduser,fupddate) values" +
                            "('" + fctlid_a + "','" + textBox8.Text.Trim().Replace("'", "''") + "','" + textBox9.Text.Trim().Replace("'", "''") + "','','" + textBox10.Text.Trim().Replace("'", "''") + "','" + textBox11.Text.Trim().Replace("'", "''") + "','" + comboBox3.SelectedItem.ToString().Trim().Replace("'", "''") + "','" + active + "','100.0000','','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "')";
                        operate.OperateData(adddatestr);

                        string updatestr = "update xsysparm set fnxtid=RIGHT('00000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='MTY'";
                        operate.OperateData(updatestr);
                    }
                    else
                    {
                        string updatedatestr =
                            "update mty set fid ='" + textBox8.Text.Trim() + "',fdesc ='" + textBox9.Text.Trim() + "',fefffr ='" + textBox10.Text.Trim() + "' ,feffto ='" + textBox11.Text.Trim() + "' ,fcur ='" + comboBox3.SelectedItem.ToString().Trim() + "',factive ='" + active + "', fupduser ='" + user + "',fupddate ='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid='" + fctlid_a + "'";
                        operate.OperateData(updatedatestr);
                    }

                    saveinfo();
                    saveContact();

                    MessageBox.Show("Have Been Inserted!", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    ButtonControlback();
                    reloadgridview1(textBox8.Text.Trim(), "insert");
                }
                else
                {
                    MessageBox.Show("Some field is empty!", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        void saveinfo()
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            DataTable dt = dataGridView2.DataSource as DataTable;
            if (dt != null)
            {
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    if (!checknewinfo(dt.Rows[i]["fctlid"].ToString().Trim()))
                    {
                        string str = "select * from xsysparm where fidtype ='mtyinsr'";
                        DataTable dt1 = new DataTable();
                        dt1 = operate.GetTable(str);
                        string fctlid_info = dt1.Rows[0]["fnxtid"].ToString();
                        string adddatestrc =
                                "insert into mtyinsr(fctlid_1,fctlid,flogseq,freinsr,fbrkr1,fbrkge1,fbrkr2,fbrkge2,fbrkr3,fbrkge3,fshare) " +
                        "values('" + fctlid_a + "','" + fctlid_info + "','" + dt.Rows[i]["Seq#"].ToString() + "','" + dt.Rows[i]["Reinsurer"].ToString().Trim().Replace("'", "''") + "','" + dt.Rows[i]["fbrkr1"].ToString().Trim().Replace("'", "''") + "','0.0000','','0.0000','','0.0000','" + dt.Rows[i]["Share (%)"].ToString() + "')";
                        operate.OperateData(adddatestrc);

                        string updatestrc = "update xsysparm set fnxtid=RIGHT('000000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='mtyinsr'";
                        operate.OperateData(updatestrc);
                    }
                    else
                    {
                        string updatedatestro =
                               "update mtyinsr set flogseq ='" + dt.Rows[i]["Seq#"].ToString() + "',freinsr ='" + dt.Rows[i]["Reinsurer"].ToString().Trim().Replace("'", "''") + "' ,fbrkr1 ='" + dt.Rows[i]["fbrkr1"].ToString().Trim().Replace("'", "''") + "',fshare ='" + dt.Rows[i]["Share (%)"].ToString() + "' where fctlid='" + dt.Rows[i]["fctlid"].ToString() + "'";
                        operate.OperateData(updatedatestro);

                        string sql = "select a.fctlid, a.flogseq as Seq#, freinsr as Reinsurer,fshare as [Share (%)], fbrkr1, " +
                                "b.fdesc as Bdesc,c.fdesc as Rdesc, a.fctlid_1 from mtyinsr a " +
                                "left join (select fid,fdesc  from mprdr where ftype='B') b on a.fbrkr1=b.fid " +
                                "left join (select fid,fdesc from mprdr where ftype='R') c on a.freinsr=c.fid " +
                                "where fctlid_1 = '" + fctlid_a + "' ORDER BY flogseq";
                        DataTable dt0 = operate.GetTable(sql);
                        if (dt != null)
                        {
                            string[] delsql = CompareDt(dt0, dt, "fctlid", "mtyinsr");
                            for (int j = 0; j <= delsql.Length - 1; j++)
                            {
                                operate.OperateData(delsql[j]);
                            }
                        }
                    }
                }
            }

        }

        void saveContact()
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            String gmet = "";

            DataTable dt = dataGridView3.DataSource as DataTable;
            if (dt != null)
            {
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    if (dt.Rows[i]["fgrnet"].ToString() == "Gross") { gmet = "1"; }
                    else { gmet = "2"; }
                    if (!checknewsec(dt.Rows[i]["fctlid"].ToString().Trim()))
                    {
                        string str = "select * from xsysparm where fidtype ='mtysec'";
                        DataTable dt1 = new DataTable();
                        dt1 = operate.GetTable(str);
                        string fctlid_contact = dt1.Rows[0]["fnxtid"].ToString();

                        string adddatestrc =
                                "insert into mtysec(fctlid_1,fctlid,fid,ftylmt,ftimes,fscale1,fscale2,fcom,fgrnet,fbrkge,fgrnet2) " +
                        "values('" + fctlid_a + "','" + fctlid_contact + "','" + Fct.sdFat(dt.Rows[i]["Sec#"]) + "','" + Fct.sdFat(dt.Rows[i]["Treaty Limit"]) + "','" + Fct.sdFat(dt.Rows[i]["ftimes"]) + "','" + Fct.sdFat(dt.Rows[i]["fscale1"]) + "','" + Fct.sdFat(dt.Rows[i]["fscale2"]) + "','" + Fct.sdFat(dt.Rows[i]["fcom"]) + "','" + Fct.sdFat(gmet) + "','0.0000','1')";
                        operate.OperateData(adddatestrc);

                        string updatestro = "update xsysparm set fnxtid=RIGHT('00000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='mtysec'";
                        operate.OperateData(updatestro);


                        List<string> results1 = dt.Rows[i]["list"].ToString().Split(',').ToList();
                        for (int j = 0; j < results1.Count; j++)
                        {
                            string str2 = "select * from xsysparm where fidtype ='MTYCLASS'";
                            DataTable dt2 = new DataTable();
                            dt2 = operate.GetTable(str2);
                            string fctlid_mtyclass = dt2.Rows[0]["fnxtid"].ToString();

                            string addmtyclass = "insert into MTYCLASS (fctlid_1,fctlid_2,fctlid,ftype,fclass,fsec,fdesc)" +
                                "values('" + fctlid_a + "','" + fctlid_contact + "','" + fctlid_mtyclass + "','1','" + Regex.Replace(results1[j].ToString(), @"/\(([^\(]*)\)/", "").Replace(" (SEC 1)", "").Replace(" (SEC 2)", "") + "','','" + results1[j].ToString() + "')";
                            operate.OperateData(addmtyclass);

                            string updatestrc = "update xsysparm set fnxtid=RIGHT('00000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='MTYCLASS'";
                            operate.OperateData(updatestrc);
                        }
                        List<string> results2 = dt.Rows[i]["list1"].ToString().Split(',').ToList();
                        for (int j = 0; j < results2.Count; j++)
                        {
                            string str2 = "select * from xsysparm where fidtype ='MTYCLASS'";
                            DataTable dt2 = new DataTable();
                            dt2 = operate.GetTable(str2);
                            string fctlid_mtyclass = dt2.Rows[0]["fnxtid"].ToString();

                            string addmtyclass1 = "insert into MTYCLASS (fctlid_1,fctlid_2,fctlid,ftype,fclass,fsec,fdesc)" +
                                "values('" + fctlid_a + "','" + fctlid_contact + "','" + fctlid_mtyclass + "','2','" + Regex.Replace(results2[j].ToString(), @"/\(([^\(]*)\)/", "").Replace(" (SEC 1)", "").Replace(" (SEC 2)", "") + "','','" + results2[j].ToString() + "')";
                            operate.OperateData(addmtyclass1);

                            string updatestrc1 = "update xsysparm set fnxtid=RIGHT('00000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='MTYCLASS'";
                            operate.OperateData(updatestrc1);
                        }
                    }
                    else
                    {
                        string updatedatestro =
                               "update mtysec set fid ='" + dt.Rows[i]["Sec#"].ToString().Trim() + "',ftylmt ='" + dt.Rows[i]["Treaty Limit"].ToString().Trim().Replace(",", "").Replace(".0000", "") + "',fscale1 ='" + dt.Rows[i]["fscale1"].ToString().Trim() + "',fscale2 ='" + dt.Rows[i]["fscale2"].ToString().Trim() + "' where fctlid='" + dt.Rows[i]["fctlid"].ToString().Trim() + "'";
                        operate.OperateData(updatedatestro);
                        SqlParameter[] param = new SqlParameter[] {
                                               new SqlParameter("@fctlid_a", fctlid_a)};

                        DataTable dt0 = DBConnect.GetDataSetProd("SurplusList", Dbm, param);
                        if (dt != null)
                        {
                            string[] delsql = CompareDt(dt0, dt, "fctlid", "mtysec");
                            for (int k = 0; k <= delsql.Length - 1; k++)
                            {
                                operate.OperateData(delsql[k]);
                            }
                        }
                        List<string> result1 = dt.Rows[i]["list"].ToString().Split(',').ToList();
                        for (int j = 0; j < result1.Count; j++)
                        {
                            string updatemtyclass = "update MTYCLASS set ftype ='1' where fctlid_1='" + fctlid_a + "' and fctlid_2='" + dt.Rows[i]["fctlid"].ToString().Trim() + "' and fdesc ='" + result1[j].ToString() + "'";
                            operate.OperateData(updatemtyclass);
                        }
                        List<string> result2 = dt.Rows[i]["list1"].ToString().Split(',').ToList();
                        for (int j = 0; j < result2.Count; j++)
                        {
                            string updatemtyclass1 = "update MTYCLASS set ftype ='2' where fctlid_1='" + fctlid_a + "' and fctlid_2='" + dt.Rows[i]["fctlid"].ToString().Trim() + "' and fdesc ='" + result2[j].ToString() + "'";
                            operate.OperateData(updatemtyclass1);
                        }
                    }
                }
            }

        }

        public string[] CompareDt(DataTable dt1, DataTable dt2, string keyField, string flag)
        {
            //为三个表拷贝表结构
            DataTable dtRetDel = dt1.Clone();
            DataTable dtRetAdd = dt1.Clone();
            DataTable dtRetDif = dt1.Clone();

            ArrayList updsql = new ArrayList();
            int colCount = dt1.Columns.Count;

            DataView dv1 = dt1.DefaultView;
            DataView dv2 = dt2.DefaultView;

            //先以第一个表为参照，看第二个表是修改了还是删除了
            foreach (DataRowView dr1 in dv1)
            {
                dv2.RowFilter = keyField + " = '" + dr1[keyField].ToString() + "'";
                if (dv2.Count > 0)
                {
                    if (!CompareUpdate(dr1, dv2[0]))//比较是否有不同的
                    {
                        dtRetDif.Rows.Add(dv2[0].Row.ItemArray);//修改后
                        continue;
                    }
                }
                else
                {
                    //已经被删除的
                    dtRetDel.Rows.Add(dr1.Row.ItemArray);
                }
            }

            //以第一个表为参照，看记录是否是新增的
            dv2.RowFilter = "";//清空条件
            foreach (DataRowView dr2 in dv2)
            {
                dv1.RowFilter = keyField + " = '" + dr2[keyField].ToString() + "'";
                if (dv1.Count == 0)
                {
                    //新增的
                    dtRetAdd.Rows.Add(dr2.Row.ItemArray);
                }
            }

            if (dtRetDel != null && dtRetDel.Rows.Count > 0)
            {
                for (int i = 0; i < dtRetDel.Rows.Count; i++)
                {
                    string del;
                    if (flag == "mtysec") { del = "delete from mtysec where fctlid='" + dtRetDel.Rows[i]["fctlid"].ToString().Trim() + "'"; }
                    else { del = "delete from mtyinsr where fctlid='" + dtRetDel.Rows[i]["fctlid"].ToString().Trim() + "'"; }
                    updsql.Add(del);
                }
            }

            return (string[])updsql.ToArray(typeof(string));
        }

        //比较是否有不同的
        private static bool CompareUpdate(DataRowView dr1, DataRowView dr2)
        {
            //行里只要有一项不一样，整个行就不一样,无需比较其它
            object val1;
            object val2;
            for (int i = 1; i < dr1.Row.ItemArray.Length; i++)
            {
                val1 = dr1[i];
                val2 = dr2[i];
                if (!val1.Equals(val2))
                {
                    return false;
                }
            }
            return true;
        }

        public Boolean checknew(string strSql)
        {
            string str =
                    "select count(*) from mty where fid ='" + strSql.Trim() + "'";
            int i = operate.SelectData(str);
            if (i > 0)
            { return true; }
            else { return false; }
        }

        public Boolean checknewinfo(string strSql)
        {
            string str =
                    "select count(*) from mtyinsr where fctlid ='" + strSql.Trim() + "' and fctlid_1 ='" + fctlid_a.Trim() + "'";
            int i = operate.SelectData(str);
            if (i > 0)
            { return true; }
            else { return false; }
        }

        public Boolean checknewsec(string strSql)
        {
            string str =
                    "select count(*) from mtysec where fctlid ='" + strSql.Trim() + "' and fctlid_1 ='" + fctlid_a.Trim() + "'";
            int i = operate.SelectData(str);
            if (i > 0)
            { return true; }
            else { return false; }
        }

        void reload()
        {
            string str = "select * from mty where fctlid ='" + fctlid_a + "'";
            DataTable dt = new DataTable();
            dt = operate.GetTable(str);
            textBox8.Text = dt.Rows[0]["fid"].ToString();
            textBox9.Text = dt.Rows[0]["fdesc"].ToString();
            if (dt.Rows[0]["fefffr"].ToString() != "")
            {
                try
                {
                    string a = dt.Rows[0]["fefffr"].ToString();
                    DateTime b = Convert.ToDateTime(a);
                    textBox10.Text = b.ToString("yyyy.MM.dd");
                }
                catch { }
            }
            if (dt.Rows[0]["feffto"].ToString() != "")
            {
                try
                {
                    string a = dt.Rows[0]["feffto"].ToString();
                    DateTime b = Convert.ToDateTime(a);
                    textBox11.Text = b.ToString("yyyy.MM.dd");
                }
                catch { }
            }
            comboBox3.SelectedItem = dt.Rows[0]["fcur"].ToString();
            String active = "";
            if (dt.Rows[0]["factive"].ToString() == "1") { active = "Yes"; } else { active = "No"; }
            comboBox4.SelectedItem = active;
        }


        private String _strValue;

        public String StrValue
        {
            set
            {
                _strValue = value;
            }
        }

        private String _strValuebn;

        public String StrValuebn
        {
            set
            {
                _strValuebn = value;
            }
        }
        private String _strValuer;

        public String StrValuer
        {
            set
            {
                _strValuer = value;
            }
        }

        private String _strValuern;

        public String StrValuern
        {
            set
            {
                _strValuern = value;
            }
        }

        public Boolean CloseFlag = false;

        private void button4_Click(object sender, EventArgs e)
        {
            frmBrkSearch temp_form = new frmBrkSearch();
            temp_form.Owner = this;
            temp_form.flag = "frmSurplus";
            temp_form.ShowDialog();
            if (CloseFlag == false)
            {
                textBox13.Text = _strValue;
                textBox14.Text = _strValuebn;
            }
        }

        private void button5_Click(object sender, EventArgs e)
        {
            frmReinsSearch temp_form = new frmReinsSearch();
            temp_form.Owner = this;
            temp_form.flag = "frmSurplus";
            temp_form.ShowDialog();
            if (CloseFlag == false)
            {
                textBox15.Text = _strValuer;
                textBox16.Text = _strValuern;
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (listBox1.SelectedIndex != -1)
            {
                int total = listBox1.SelectedItems.Count;
                for (int x = 0; x < total; x++)
                {
                    list.Remove(listBox1.SelectedItem.ToString());
                    list1.Add(listBox1.SelectedItem.ToString());
                }
            }
            listBox1.DataSource = null;
            listBox2.DataSource = null;
            listBox1.DataSource = list;
            listBox2.DataSource = list1;
            dataGridView3["list", row1].Value = string.Join(",", list.ToArray());
            dataGridView3["list1", row1].Value = string.Join(",", list1.ToArray());
        }

        private void button3_Click(object sender, EventArgs e)
        {
            listBox2.DataSource = null;
            list1.AddRange(list);
            listBox2.DataSource = list1;
            listBox1.DataSource = null;
            list.Clear();
            dataGridView3["list", row1].Value = string.Join(",", list.ToArray());
            dataGridView3["list1", row1].Value = string.Join(",", list1.ToArray());
        }

        private void button6_Click(object sender, EventArgs e)
        {
            if (listBox2.SelectedIndex != -1)
            {
                int total = listBox2.SelectedItems.Count;
                for (int x = 0; x < total; x++)
                {
                    list1.Remove(listBox2.SelectedItem.ToString());
                    list.Add(listBox2.SelectedItem.ToString());
                }
            }
            listBox1.DataSource = null;
            listBox2.DataSource = null;
            listBox1.DataSource = list;
            listBox2.DataSource = list1;
            dataGridView3["list", row1].Value = string.Join(",", list.ToArray());
            dataGridView3["list1", row1].Value = string.Join(",", list1.ToArray());
        }

        private void button7_Click(object sender, EventArgs e)
        {
            list.AddRange(list1);
            listBox1.DataSource = list;
            listBox2.DataSource = null;
            list1.Clear();
            dataGridView3["list", row1].Value = string.Join(",", list.ToArray());
            dataGridView3["list1", row1].Value = string.Join(",", list1.ToArray());
        }

        private void button8_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonCountrolupdate();
            textBox8.Text = "";
        }





    }
}

