using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using System.Globalization;
using System.Windows.Documents;
using System.Text.RegularExpressions;
using System.Configuration;
using System.Collections;

namespace INS
{
    public partial class frmLevy : Form
    {
        List<string> list = new List<string>();
        List<string> list1 = new List<string>();
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public frmLevy()
        {
            InitializeComponent();
            InitCombobox();
            FillData("", "");
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            foreach (Control control in panel2.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
        }
        void control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
            {
                SendKeys.Send("{TAB}");
            }
        }
        private DataTable dataSource = new DataTable();
        private DataTable dataSource1 = new DataTable();
        public int i = 0, counter;
        DES des = new DES();
        DBConnect operate = new DBConnect();
        ExportDBF DBF = new ExportDBF();
        XLS txt = new XLS();
        public string fctlid_a = "";
        public String user = InsEnvironment.LoginUser.GetUserCode();
        public int row = 0;
        public int row1 = 0;
        public Boolean flag = false;
        public Boolean ADD = false;
        public Boolean Update = false;
        public Boolean flag1 = false;
        private bool datagridviewSelectionChanged = true;
        private bool datagridview3SelectionChanged = true;
        public string db = InsEnvironment.DataBase.GetDbm();

        public void FillData(string order, string query)
        {
            try
            {
                DataTable dt = DBConnect.GetDataSetProd("ElvyUpd", Dbm, null);
                dataGridView1.DataSource = dt;

                this.dataGridView1.Columns[0].Visible = false;
                this.dataGridView1.Columns[4].Visible = false;
                this.dataGridView1.Columns[5].Visible = false;
                ButtonControlback();
            }
            catch { }
        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            if (ADD == true || Update == true)
            {
                if (e.TabPage == Classes)
                    e.Cancel = true;
            }
        }

        void dataGridView1chg()
        {
            if (!this.datagridviewSelectionChanged) return;
            try
            {
                if (flag == false && dataGridView1.CurrentCell != null)
                {
                    counter = dataGridView1.CurrentCell.RowIndex;
                }

                if (dataGridView1.Rows[counter].Cells["Levy#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Levy#"].Value.ToString().Length != 0)
                    {
                        fid.Text = dataGridView1.Rows[counter].Cells["Levy#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        fdesc.Text = dataGridView1.Rows[counter].Cells["Desc#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Eff From"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Eff From"].Value.ToString().Length != 0)
                    {
                        if (dataGridView1.Rows[counter].Cells["Eff From"].Value.ToString() != "")
                        {
                            try
                            {
                                string a = dataGridView1.Rows[counter].Cells["Eff From"].Value.ToString();
                                DateTime b = Convert.ToDateTime(a);
                                feffr.Text = b.ToString("yyyy.MM.dd");
                            }
                            catch { }
                        }
                    }
                }
                if (dataGridView1.Rows[counter].Cells["list"].Value != null)
                {
                    string s = dataGridView1.Rows[counter].Cells["list"].Value.ToString().Trim();
                    if (s.Length != 0) { s = s.Substring(0, s.Length - 1); }
                    string[] parts = s.Split(',');
                    List<string> listl1 = new List<string>(parts);
                    list.Clear();
                    foreach (string item in listl1)
                    {
                        list.Add(item);
                    }
                    listBox1.DataSource = null;
                    listBox1.DataSource = list;
                }
                if (dataGridView1.Rows[counter].Cells["list1"].Value != null)
                {
                    string s = dataGridView1.Rows[counter].Cells["list1"].Value.ToString().Trim();
                    if (s.Length != 0) { s = s.Substring(0, s.Length - 1); }
                    string[] parts = s.Split(',');
                    List<string> listl2 = new List<string>(parts);
                    list1.Clear();
                    foreach (string item in listl2)
                    {
                        list1.Add(item);
                    }
                    listBox2.DataSource = null;
                    listBox2.DataSource = list1;
                }
                if (dataGridView1.Rows[counter].Cells["Eff To"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Eff To"].Value.ToString().Length != 0)
                    {
                        if (dataGridView1.Rows[counter].Cells["Eff To"].Value.ToString() != "")
                        {
                            try
                            {
                                string a = dataGridView1.Rows[counter].Cells["Eff To"].Value.ToString();
                                DateTime b = Convert.ToDateTime(a);
                                feffto.Text = b.ToString("yyyy.MM.dd");
                            }
                            catch { }
                        }
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fupperlmt"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fupperlmt"].Value.ToString().Length != 0)
                    {
                        if (dataGridView1.Rows[counter].Cells["fupperlmt"].Value.ToString() != "")
                        {
                            fupperlimt.Text = dataGridView1.Rows[counter].Cells["fupperlmt"].Value.ToString().Trim();
                        }
                    }
                }
                if (dataGridView1.Rows[counter].Cells["frate"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["frate"].Value.ToString().Length != 0)
                    {
                        if (dataGridView1.Rows[counter].Cells["frate"].Value.ToString() != "")
                        {
                            frate.Text = dataGridView1.Rows[counter].Cells["frate"].Value.ToString().Trim();
                        }
                    }
                }

                if (dataGridView1.Rows[counter].Cells["factive"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["factive"].Value.ToString().Length != 0)
                    {
                        factive.SelectedItem = dataGridView1.Rows[counter].Cells["factive"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[counter].Cells["fapplymax"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fapplymax"].Value.ToString().Length != 0)
                    {
                        fapplymax.SelectedItem = dataGridView1.Rows[counter].Cells["fapplymax"].Value.ToString();
                    }
                }
                if (ADD == false)
                {
                    fctlid_a = dataGridView1.Rows[counter].Cells["fctlid"].Value.ToString();
                }
            }
            catch { }
        }

        private void dataGridView1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            dataGridView1chg();
        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", "");
        }

        private void button1_Click(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", textBox1.Text.ToString().Trim());
        }

        public enum Mode
        {
            TTY = 1,
            Desc = 2
        }

        private void InitCombobox()
        {
            Array arr = System.Enum.GetValues(typeof(Mode));    // 获取枚举的所有值
            DataTable dt = new DataTable();
            dt.Columns.Add("String", Type.GetType("System.String"));
            dt.Columns.Add("Value", typeof(int));
            foreach (var a in arr)
            {
                string strText = EnumTextByDescription.GetEnumDesc((Mode)a);
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = (int)a;

                dt.Rows.Add(aRow);
            }

            comboBox1.DataSource = dt;
            comboBox1.DisplayMember = "String";
            comboBox1.ValueMember = "Value";
        }


        void ButtonControlback()
        {
            ADD = false;
            Update = false;
            fid.BackColor = System.Drawing.SystemColors.InactiveCaption;
            fid.ReadOnly = true;
            fdesc.BackColor = System.Drawing.SystemColors.InactiveCaption;
            fdesc.ReadOnly = true;
            feffr.BackColor = System.Drawing.SystemColors.InactiveCaption;
            feffr.ReadOnly = true;
            feffto.BackColor = System.Drawing.SystemColors.InactiveCaption;
            feffto.ReadOnly = true;
            factive.BackColor = System.Drawing.SystemColors.InactiveCaption;
            factive.Enabled = false;
            fupperlimt.BackColor = System.Drawing.SystemColors.InactiveCaption;
            fupperlimt.ReadOnly = true;
            frate.BackColor = System.Drawing.SystemColors.InactiveCaption;
            frate.ReadOnly = true;

            dcancel.Visible = false;
            dsave.Visible = false;
            dexit.Visible = true;
            dadd.Visible = true;
            dupdate.Visible = true;
            ddel.Visible = true;
            button4.Enabled = false;
            button5.Enabled = false;
            button10.Enabled = false;
            button9.Enabled = false;
        }

        void ButtonControl()
        {
            fid.BackColor = System.Drawing.SystemColors.Window;
            fid.ReadOnly = false;
            fdesc.BackColor = System.Drawing.SystemColors.Window;
            fdesc.ReadOnly = false;
            feffr.BackColor = System.Drawing.SystemColors.Window;
            feffr.ReadOnly = false;
            feffto.BackColor = System.Drawing.SystemColors.Window;
            feffto.ReadOnly = false;
            factive.BackColor = System.Drawing.SystemColors.Window;
            factive.Enabled = true;
            fapplymax.BackColor = System.Drawing.SystemColors.Window;
            fapplymax.Enabled = true;
            frate.BackColor = System.Drawing.SystemColors.Window;
            frate.ReadOnly = false;
            fupperlimt.BackColor = System.Drawing.SystemColors.Window;
            fupperlimt.ReadOnly = false;

            dcancel.Visible = true;
            dsave.Visible = true;
            dexit.Visible = false;
            dadd.Visible = false;
            dupdate.Visible = false;
            ddel.Visible = false;
            button4.Enabled = true;
            button5.Enabled = true;
            button10.Enabled = true;
            button9.Enabled = true;
        }

        void ButtonControladd()
        {
            ADD = true;
            DataTable listdataSource = dataGridView1.DataSource as DataTable;
            DataRow newCustomersRow = listdataSource.NewRow();
            listdataSource.Rows.InsertAt(newCustomersRow, counter);
            dataGridView1.DataSource = listdataSource;
            dataGridView1.CurrentCell = dataGridView1.Rows[counter - 1].Cells["Levy#"];

            fid.Text = "";
            fdesc.Text = "";
            feffr.Text = "";
            feffto.Text = "";
            fupperlimt.Text = "";
            frate.Text = "";
            string[] parts = "EEC,CLL,CAR,CGL,CPM,MYP,PAR,FGP,PMP,CMP,CSB,PII,GLF".ToString().Split(',');
            List<string> listl1 = new List<string>(parts);
            list.Clear();
            list1.Clear();
            foreach (string item in listl1)
            {
                list.Add(item);
            }
            listBox1.DataSource = null;
            listBox1.DataSource = list;

            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);
        }

        private void button9_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void dexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void cexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        void cancel()
        {
            string oldfctlid = fctlid_a;
            ButtonControlback();
            FillData("", "");
            DataTable dt3 = dataGridView1.DataSource as DataTable;
            foreach (DataRow row in dt3.Rows)
            {
                int SelectedIndex = dt3.Rows.IndexOf(row);
                if (oldfctlid == row["fctlid"].ToString().Trim())
                {
                    dataGridView1.CurrentCell = dataGridView1.Rows[SelectedIndex].Cells["Levy#"];
                    dataGridView1chg();
                    break;
                }
            }
        }

        private void dcancel_Click(object sender, EventArgs e)
        {
            cancel();
        }

        private void cCancel_Click(object sender, EventArgs e)
        {
            cancel();
        }

        private void lupdate_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            Update = true;
            ButtonControl();
        }

        private void dupdate_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            Update = true;
            ButtonControl();
        }

        private void cupdate_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            Update = true;
            ButtonControl();
        }

        private void ladd_Click(object sender, EventArgs e)
        {
            add();
        }

        private void dadd_Click(object sender, EventArgs e)
        {
            add();
        }

        private void cadd_Click(object sender, EventArgs e)
        {
            add();
        }

        void add()
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonControladd();
        }

        private void ldel_Click(object sender, EventArgs e)
        {
            del();
        }

        private void ddel_Click(object sender, EventArgs e)
        {
            del();
        }

        private void cdel_Click(object sender, EventArgs e)
        {
            del();
        }

        void del()
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the ALL items?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string deldatestr1 =
                    "delete from mlvyhd where fctlid='" + fctlid_a + "'";
                operate.OperateData(deldatestr1);
                string deldatestr =
                    "delete from mlvyclass where fctlid_hd='" + fctlid_a + "'";
                operate.OperateData(deldatestr);
                MessageBox.Show("Have Been Deleted", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                FillData("", "");
                tabControl1.SelectedTab = Classes;
            }

        }

        private void textBox8_TextChanged(object sender, EventArgs e)
        {
            dataGridView1.Rows[counter].Cells["Levy#"].Value = fid.Text;
            string result2 = CheckDuplicate(fid.Text);
            if (result2 != "")
            {
                MessageBox.Show(result2);
                fid.Focus();
                fid.SelectAll();
            }
        }

        public string CheckDuplicate(string fid)
        {
            if (fid != "")
            {
                DataTable listdataSource = dataGridView1.DataSource as DataTable;
                if (listdataSource.Rows.Count > 0)
                {
                    DataTable objectTable = listdataSource.DefaultView.ToTable();
                    DataRow[] rows = objectTable.Select("[Levy#]='" + fid.Trim() + "'");
                    if (rows.Length > 1)
                    {
                        return "Duplicate Value";
                    }
                    else { return ""; }
                }
                else { return ""; }
            }
            else { return ""; }
        }

        private void dsave_Click(object sender, EventArgs e)
        {
            saveadd();
        }

        private void csave_Click(object sender, EventArgs e)
        {
            saveadd();
        }

        void saveadd()
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really Insert the item?", "Inserted Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                ArrayList addsql = new ArrayList();
                String active = "", applymax = "";
                if (factive.SelectedItem.ToString() == "Yes")
                { active = "1"; }
                else { active = "2"; }
                if (fapplymax.SelectedItem.ToString() == "Yes")
                { applymax = "1"; }
                else { applymax = "2"; }
                string fctlid_hd = fctlid_a;

                if (ADD == true)
                {
                    fctlid_hd = Fct.NewId("MLVYHD");
                    string insertsql = "insert into mlvyhd(fctlid,flvytype,fid,fdesc,fefffr,feffto,factive,frate,fapplymax,fupperlmt,finpuser,finpdate,fupduser,fupddate) values" +
                            "('" + fctlid_hd + "','IAL','" + Fct.stFat(fid.Text) + "','" + Fct.stFat(fdesc.Text) + "','" + feffr.Text.Trim().Replace("'", "''") + "','" + feffto.Text.Trim().Replace("'", "''") + "','" + active + "','" + Fct.sdFat(frate.Text) + "','" + applymax + "','" + Fct.sdFat(fupperlimt.Text) + "', '" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "')";
                    addsql.Add(insertsql);

                    string updsql = "update xsysparm set fnxtid=RIGHT('00000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='MLVYHD'";
                    addsql.Add(updsql);

                    string fctlidcls = Fct.NewId("MLVYCLASS");
                    List<string> results1 = list;
                    for (int n = 0; n < results1.Count; n++)
                    {
                        fctlidcls = (int.Parse(fctlidcls) + 1).ToString().PadLeft(10, '0');
                        string Addmtyclass = "insert into mlvyclass (fctlid,fctlid_hd,fclass,fclsseq,fexclude)" +
                        "values('" + fctlidcls + "','" + fctlid_hd + "','" + Regex.Replace(results1[n].ToString(), @"/\(([^\(]*)\)/", "").Replace(" (SEC 1)", "").Replace(" (SEC 2)", "").Replace(" (TPBI)", "") + "','','2')";
                        addsql.Add(Addmtyclass);
                    }
                    List<string> results2 = list1;
                    for (int n = 0; n < results2.Count; n++)
                    {
                        fctlidcls = (int.Parse(fctlidcls) + 1).ToString().PadLeft(10, '0');
                        string Addmtyclass = "insert into mlvyclass (fctlid,fctlid_hd,fclass,fclsseq,fexclude)" +
                        "values('" + fctlidcls + "','" + fctlid_hd + "','" + Regex.Replace(results1[n].ToString(), @"/\(([^\(]*)\)/", "").Replace(" (SEC 1)", "").Replace(" (SEC 2)", "").Replace(" (TPBI)", "") + "','','1')";
                        addsql.Add(Addmtyclass);
                    }

                    string updclass = "update xsysparm set fnxtid='" + (int.Parse(fctlidcls) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='MLVYCLASS'";
                    addsql.Add(updclass);
                }

                if (Update == true)
                {
                    fctlid_hd = fctlid_a;
                    string updsql = "UPDATE [dbo].[mlvyhd] "+
                                  "SET [fid] = '" + Fct.stFat(fid.Text) + "' ,[fdesc] = '" + Fct.stFat(fdesc.Text) + "',[fefffr] ='" + feffr.Text.Trim().Replace("'", "''") + "' " +
                                  ",[feffto] ='" + feffto.Text.Trim().Replace("'", "''") + "',[factive] = '" + active + "',[frate] = '" + Fct.sdFat(frate.Text) + "' " +
                                  ",[fapplymax] = '" + applymax + "' ,[fupperlmt] = '" + Fct.sdFat(fupperlimt.Text) + "' " +
                                  ",[fupduser] = '" + user + "',[fupddate] = '" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' " +
                                  "WHERE fctlid ='"+fctlid_hd+"'";
                    addsql.Add(updsql);

                    string delsql = "delete from mlvyclass where fctlid_hd='" + fctlid_hd + "'";
                    addsql.Add(delsql);

                    string fctlidcls = Fct.NewId("MLVYCLASS");
                    List<string> results1 = list;
                    for (int n = 0; n < results1.Count; n++)
                    {
                        fctlidcls = (int.Parse(fctlidcls) + 1).ToString().PadLeft(10, '0');
                        string Addmtyclass = "insert into mlvyclass (fctlid,fctlid_hd,fclass,fclsseq,fexclude)" +
                        "values('" + fctlidcls + "','" + fctlid_hd + "','" + Regex.Replace(results1[n].ToString(), @"/\(([^\(]*)\)/", "").Replace(" (SEC 1)", "").Replace(" (SEC 2)", "").Replace(" (TPBI)", "") + "','','2')";
                        addsql.Add(Addmtyclass);
                    }
                    List<string> results2 = list1;
                    for (int n = 0; n < results2.Count; n++)
                    {
                        fctlidcls = (int.Parse(fctlidcls) + 1).ToString().PadLeft(10, '0');
                        string Addmtyclass = "insert into mlvyclass (fctlid,fctlid_hd,fclass,fclsseq,fexclude)" +
                        "values('" + fctlidcls + "','" + fctlid_hd + "','" + Regex.Replace(results1[n].ToString(), @"/\(([^\(]*)\)/", "").Replace(" (SEC 1)", "").Replace(" (SEC 2)", "").Replace(" (TPBI)", "") + "','','1')";
                        addsql.Add(Addmtyclass);
                    }

                    string updclass = "update xsysparm set fnxtid='" + (int.Parse(fctlidcls) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='MLVYCLASS'";
                    addsql.Add(updclass);
                }

                string connectionString = ConfigurationManager.ConnectionStrings["mdata"].ConnectionString;
                string result = ExecuteSqlTransaction(connectionString, (string[])addsql.ToArray(typeof(string)));
                if (result == "OK")
                {
                    MessageBox.Show("Have Been Inserted!", "Warning",
                             MessageBoxButtons.OK, MessageBoxIcon.Information);
                    ButtonControlback();
                    FillData("", "");
                    DataTable dt3 = dataGridView1.DataSource as DataTable;
                    foreach (DataRow row in dt3.Rows)
                    {
                        int SelectedIndex = dt3.Rows.IndexOf(row);
                        if (fctlid_hd == row["fctlid"].ToString().Trim())
                        {
                            dataGridView1.CurrentCell = dataGridView1.Rows[SelectedIndex].Cells["Levy#"];
                            dataGridView1chg();
                            break;
                        }
                    }
                }
                else {
                    MessageBox.Show("Haven't Been Inserted!", "Warning",
                               MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private string ExecuteSqlTransaction(string connectionString, string[] sql1)
        {
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                SqlCommand command = connection.CreateCommand();
                SqlTransaction transaction;
                transaction = connection.BeginTransaction("SampleTransaction");

                command.Connection = connection;
                command.Transaction = transaction;

                try
                {
                    if (sql1 != null)
                    {
                        for (int i = 0; i < sql1.Length; i++)
                        {
                            if (sql1[i] != "" && sql1[i] != null)
                            {
                                command.CommandText = sql1[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }

                    transaction.Commit();
                    return "OK";
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                    Console.WriteLine("  Message: {0}", ex.Message);

                    try
                    {
                        transaction.Rollback();
                        return "RollBack";
                    }
                    catch (Exception ex2)
                    {
                        Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                        Console.WriteLine("  Message: {0}", ex2.Message);
                        return ex2.Message;
                    }
                }
            }
        }
        private String _strValue;

        public String StrValue
        {
            set
            {
                _strValue = value;
            }
        }

        private String _strValuebn;

        public String StrValuebn
        {
            set
            {
                _strValuebn = value;
            }
        }
        private String _strValuer;

        public String StrValuer
        {
            set
            {
                _strValuer = value;
            }
        }

        private String _strValuern;

        public String StrValuern
        {
            set
            {
                _strValuern = value;
            }
        }

        public Boolean CloseFlag = false;

        private void button2_Click(object sender, EventArgs e)
        {
            if (listBox1.SelectedIndex != -1)
            {
                int total = listBox1.SelectedItems.Count;
                for (int x = 0; x < total; x++)
                {
                    list.Remove(listBox1.SelectedItem.ToString());
                    list1.Add(listBox1.SelectedItem.ToString());
                }
            }
            listBox1.DataSource = null;
            listBox2.DataSource = null;
            listBox1.DataSource = list;
            listBox2.DataSource = list1;

        }

        private void button3_Click(object sender, EventArgs e)
        {
            listBox2.DataSource = null;
            list1.AddRange(list);
            listBox2.DataSource = list1;
            listBox1.DataSource = null;
            list.Clear();

        }

        private void button6_Click(object sender, EventArgs e)
        {
            if (listBox2.SelectedIndex != -1)
            {
                int total = listBox2.SelectedItems.Count;
                for (int x = 0; x < total; x++)
                {
                    list1.Remove(listBox2.SelectedItem.ToString());
                    list.Add(listBox2.SelectedItem.ToString());
                }
            }
            listBox1.DataSource = null;
            listBox2.DataSource = null;
            listBox1.DataSource = list;
            listBox2.DataSource = list1;
        }

        private void button7_Click(object sender, EventArgs e)
        {
            list.AddRange(list1);
            listBox1.DataSource = list;
            listBox2.DataSource = null;
            list1.Clear();

        }

        private void button8_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            fid.Text = "";
        }

        private void button10_Click(object sender, EventArgs e)
        {
            if (listBox1.SelectedIndex != -1)
            {
                int total = listBox1.SelectedItems.Count;
                for (int x = 0; x < total; x++)
                {
                    list.Remove(listBox1.SelectedItem.ToString());
                    list1.Add(listBox1.SelectedItem.ToString());
                }
            }
            listBox1.DataSource = null;
            listBox2.DataSource = null;
            listBox1.DataSource = list;
            listBox2.DataSource = list1;
        }

        private void button9_Click_1(object sender, EventArgs e)
        {
            listBox2.DataSource = null;
            list1.AddRange(list);
            listBox2.DataSource = list1;
            listBox1.DataSource = null;
            list.Clear();
        }

        private void button5_Click(object sender, EventArgs e)
        {
            if (listBox2.SelectedIndex != -1)
            {
                int total = listBox2.SelectedItems.Count;
                for (int x = 0; x < total; x++)
                {
                    list1.Remove(listBox2.SelectedItem.ToString());
                    list.Add(listBox2.SelectedItem.ToString());
                }
            }
            listBox1.DataSource = null;
            listBox2.DataSource = null;
            listBox1.DataSource = list;
            listBox2.DataSource = list1;
        }

        private void button4_Click(object sender, EventArgs e)
        {
            listBox1.DataSource = null;
            list.AddRange(list1);
            listBox1.DataSource = list;
            listBox2.DataSource = null;
            list1.Clear();
        }





    }
}

