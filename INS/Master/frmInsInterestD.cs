using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;

namespace INS
{
    public partial class frmInsInterestD : Form
    {
        DES des = new DES();
        DBConnect operate = new DBConnect();
        ExportDBF DBF = new ExportDBF();
        XLS txt = new XLS();
        public String user = InsEnvironment.LoginUser.GetUserCode();
        public String fctlid_a = "", oldrow = "0";
        public String fctlid_1 = "";
        public String row = "";
        public Boolean ADD = false;

        public frmInsInterestD(String fctlid_a, String fctlid_1)
        {
            InitializeComponent();
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            this.fctlid_a = fctlid_a;
            this.fctlid_1 = fctlid_1;
            FillData("","");
        }
        void control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
            {
                SendKeys.Send("{TAB}");
            }
        }
        void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
                a = new SqlDataAdapter("select a.fid as Detail#, fsid as Item#, flogseq as Seq#, a.fdesc as Desc#, "+
                                        "case when fdefault ='1' then 'Yes' else 'No' end as Default#, "+
                                        "fcontent,a.fctlid from misec a "+
                                        "left join minsc b on a.fctlid_a = b.fctlid "+
                                        "where ftype =2 and fdoctype =1 and fctlid_a ='" + fctlid_a + "' " +
                                        "and fctlid_1 = '" + fctlid_1+ "'", c);
                DataTable t = new DataTable();
                a.Fill(t);
                dataGridView1.DataSource = t;
                this.dataGridView1.Columns[5].Visible = false;
                this.dataGridView1.Columns[6].Visible = false;
                c.Close();
                loadmain();
        }

        void loadmain() {
            string str = "select b.fid as cfid,b.fdesc as cdesc,a.fid as afid,a.fdesc as adesc from misec a "+
                            "left join minsc b on a.fctlid_a = b.fctlid "+
                            "where ftype =1 and fdoctype =1 and fctlid_a ='" + fctlid_a + "' "+
                            "and a.fctlid = '" + fctlid_1 + "' ";
            DataTable dt = new DataTable();
            dt = operate.GetTable(str);
            textBox1.Text = dt.Rows[0]["cfid"].ToString();
            textBox6.Text = dt.Rows[0]["cdesc"].ToString();
            textBox7.Text = dt.Rows[0]["afid"].ToString();
            textBox8.Text = dt.Rows[0]["adesc"].ToString();
            textBox12.Text = dt.Rows[0]["cfid"].ToString();
            textBox11.Text = dt.Rows[0]["cdesc"].ToString();
            textBox10.Text = dt.Rows[0]["afid"].ToString();
            textBox9.Text = dt.Rows[0]["adesc"].ToString();
        }
        private void dataGridView1_CurrentCellChanged(object sender, EventArgs e)
        {
            if (dataGridView1.CurrentCell != null)
            {
                int counter;
                counter = dataGridView1.CurrentCell.RowIndex;
                if (dataGridView1.Rows[counter].Cells["Detail#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Detail#"].Value.ToString().Length != 0)
                    {
                        textBox2.Text = dataGridView1.Rows[counter].Cells["Detail#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        textBox3.Text = dataGridView1.Rows[counter].Cells["Desc#"].Value.ToString().Trim();
                    }
                }
                if (dataGridView1.Rows[counter].Cells["Item#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Item#"].Value.ToString().Length != 0)
                    {
                        textBox4.Text = dataGridView1.Rows[counter].Cells["Item#"].Value.ToString().Trim();
                    }
                }
                if (dataGridView1.Rows[counter].Cells["Seq#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Seq#"].Value.ToString().Length != 0)
                    {
                        textBox5.Text = dataGridView1.Rows[counter].Cells["Seq#"].Value.ToString().Trim();
                    }
                }
                if (dataGridView1.Rows[counter].Cells["Default#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Default#"].Value.ToString().Length != 0)
                    {
                        comboBox1.SelectedItem = dataGridView1.Rows[counter].Cells["Default#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[counter].Cells["fcontent"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fcontent"].Value.ToString().Length != 0)
                    {
                        richTextBox1.Text = dataGridView1.Rows[counter].Cells["fcontent"].Value.ToString().Trim();
                    }
                }
                row = dataGridView1.Rows[counter].Cells["fctlid"].Value.ToString();
                rowlabel.Text = counter.ToString();
            }
        }

        private void tabControl1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            
          
        }

        private void dexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void gexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            e.Cancel = true;
        }

        private void tabControl1_Selecting2(object sender, TabControlCancelEventArgs e)
        {
            e.Cancel = false;
        }

        private void ddel_Click(object sender, EventArgs e)
        {

            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the item?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string code = textBox4.Text;
                string deldatestr =
                    "delete from misec where ftype =2 and fdoctype =1 and fctlid='" + row + "'";
                operate.OperateData(deldatestr);
                MessageBox.Show("Have Been Deleted", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                Reload(rowlabel.Text, "del");
            }

        }

        private void saving_Click(object sender, EventArgs e)
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really Update the item?", "Updated Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                String code = textBox2.Text;
                String desc = textBox3.Text;
                String item = textBox4.Text;
                String seq = textBox5.Text;
                String fdefault="";
                if (comboBox1.SelectedItem.ToString () == "Yes") { fdefault = "1"; }
                else { fdefault = "2"; }
                String content = richTextBox1.Text;
                  if (code != "" && desc != "")
                {
                string updatedatestr =
                    "update misec set fid ='" + code.Trim().Replace("'", "''") + "', fdesc ='" + desc.Trim().Replace("'", "''") + "',fsid ='" + item.Trim().Replace("'", "''") + "',flogseq ='" + seq + "',fdefault ='" + fdefault + "',fcontent ='" + content.Trim().Replace("'", "''") + "', fupduser ='" + user + "', fupddate ='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid='" + row + "'";
                operate.OperateData(updatedatestr);
                MessageBox.Show("Have Been Updated", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                ButtonControl(); Reload(code, "insert");
                }
                  else
                  {
                      MessageBox.Show("Some field is empty!", "Warning",
   MessageBoxButtons.OK, MessageBoxIcon.Information);
                  }
            }

        }

        private void save_Click(object sender, EventArgs e)
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really Insert the item?", "Inserted Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                String code = textBox2.Text;
                String desc = textBox3.Text;
                String item = textBox4.Text;
                String seq = textBox5.Text;
                String fdefault = "";
                if (comboBox1.SelectedItem.ToString () == "Yes") { fdefault = "1"; }
                else { fdefault = "2"; }
                String content = richTextBox1.Text;
                string str = "select * from xsysparm where fidtype ='SECID'";
                DataTable dt = new DataTable();
                string fctlid = "";
                dt = operate.GetTable(str);
                fctlid = dt.Rows[0]["fnxtid"].ToString();
                if (code != "" && desc != "")
                {
                    string adddatestr =
                            "insert into misec(ftype,fctlid_a,fdoctype,fctlid_1,fctlid,fid,fsid,flogseq," +
                    "fdesc,fcontent,fsubno,fdefault,fctldel,finpuser,finpdate,fupduser,fupddate) " +
                    "values('2','" + fctlid_a + "','1','" + fctlid_1 + "','" + fctlid + "','" + code.Trim().Replace("'", "''") + "','" + item.Trim().Replace("'", "''") + "','" + seq + "'," +
                    "'" + desc.Trim().Replace("'", "''") + "','" + content.Trim().Replace("'", "''") + "','','" + fdefault + "','','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "')";
                    operate.OperateData(adddatestr);

                    string updatestr = "update xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='SECID'";
                    operate.OperateData(updatestr);

                    MessageBox.Show("Have Been Inserted!", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    ButtonControl();
                    Reload(code, "insert");
                }
                else {
                    MessageBox.Show("Some field is empty!", "Warning",
 MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private void textBox2_TextChanged(object sender, EventArgs e)
        {
            if (dataGridView1.CurrentCell != null)
            {
                dataGridView1["Detail#", dataGridView1.CurrentCell.RowIndex].Value = textBox2.Text;
                string result2 = CheckDuplicate(textBox2.Text);
                if (result2 != "")
                {
                    MessageBox.Show(result2);
                    textBox2.Focus();
                    textBox2.SelectAll();
                }
            }
        }

        public string CheckDuplicate(string fid)
        {
            if (fid != "")
            {
                DataTable listdataSource = dataGridView1.DataSource as DataTable;
                if (listdataSource.Rows.Count > 0)
                {
                    DataTable objectTable = listdataSource.DefaultView.ToTable();
                    DataRow[] rows = objectTable.Select("[Detail#]='" + fid.Trim() + "'");
                    if (rows.Length > 1)
                    {
                        return "Duplicate Value";
                    }
                    else { return ""; }
                }
                else { return ""; }
            }
            else { return ""; }
        }
        private void dadd_Click(object sender, EventArgs e)
        {
            ADD = true; oldrow = rowlabel.Text;
            textBox2.BackColor = System.Drawing.SystemColors.Window;
            textBox2.ReadOnly = false;
            textBox2.Text = "";
            textBox3.BackColor = System.Drawing.SystemColors.Window;
            textBox3.ReadOnly = false;
            textBox3.Text = "";
            textBox4.BackColor = System.Drawing.SystemColors.Window;
            textBox4.ReadOnly = false;
            textBox4.Text = "";
            textBox5.BackColor = System.Drawing.SystemColors.Window;
            textBox5.ReadOnly = false;
            textBox5.Text = "";
            richTextBox1.BackColor = System.Drawing.SystemColors.Window;
            richTextBox1.ReadOnly = false;
            richTextBox1.Text = "";
            comboBox1.BackColor = System.Drawing.SystemColors.Window;
            comboBox1.Enabled = true;
            cancel.Visible = true;
            save.Visible = true;
            dexit.Visible = false;
            ddel.Visible = false;
            dupdate.Visible = false;
            dadd.Visible = false;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);
        }

        private void dupdate_Click(object sender, EventArgs e)
        {
            oldrow = rowlabel.Text;
            textBox2.BackColor = System.Drawing.SystemColors.Window;
            textBox2.ReadOnly = false;
            textBox3.BackColor = System.Drawing.SystemColors.Window;
            textBox3.ReadOnly = false;
            textBox4.BackColor = System.Drawing.SystemColors.Window;
            textBox4.ReadOnly = false;
            textBox5.BackColor = System.Drawing.SystemColors.Window;
            textBox5.ReadOnly = false;
            richTextBox1.BackColor = System.Drawing.SystemColors.Window;
            richTextBox1.ReadOnly = false;
            comboBox1.BackColor = System.Drawing.SystemColors.Window;
            comboBox1.Enabled = true;
            saving.Visible = true;
            cancel.Visible = true;
            dexit.Visible = false;
            ddel.Visible = false;
            dupdate.Visible = false;
            dadd.Visible = false;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);
        }

        private void cancel_Click(object sender, EventArgs e)
        {
            ButtonControl();
            Reload(oldrow, "cancel");
        }

        void ButtonControl()
        {
            ADD = false;
            textBox2.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox2.ReadOnly = true;
            textBox3.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox3.ReadOnly = true;
            textBox4.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox4.ReadOnly = true;
            textBox6.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox6.ReadOnly = true;
            richTextBox1.BackColor = System.Drawing.SystemColors.InactiveCaption;
            richTextBox1.ReadOnly = true;
            comboBox1.BackColor = System.Drawing.SystemColors.InactiveCaption;
            comboBox1.Enabled = false;
            saving.Visible = false;
            cancel.Visible = false;
            save.Visible = false;
            dexit.Visible = true;
            ddel.Visible = true;
            dupdate.Visible = true;
            dadd.Visible = true;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting2);
        }

        void Reload(string fid, string lab)
        {
            string str = "select a.fid as Detail#, fsid as Item#, flogseq as Seq#, a.fdesc as Desc#, " +
                                        "case when fdefault ='1' then 'Yes' else 'No' end as Default#, " +
                                        "fcontent,a.fctlid from misec a " +
                                        "left join minsc b on a.fctlid_a = b.fctlid " +
                                        "where ftype =2 and fdoctype =2 and fctlid_a ='" + fctlid_a + "' " +
                                        "and fctlid_1 = '" + fctlid_1 + "'";
            DataTable dt = new DataTable();
            dt = operate.GetTable(str);
            if (dt.Rows.Count > 0)
            {
                dataGridView1.DataSource = dt;
                this.dataGridView1.Columns[5].Visible = false;
                this.dataGridView1.Columns[6].Visible = false;
                if (lab == "insert" || lab == "update")
                {
                    if (fid != "") //add update
                    {
                        for (int i = 0; i < dt.Rows.Count; i++)
                        {
                            if (dt.Rows[i]["Detail#"].ToString().Trim() == fid.Trim())
                            {
                                dataGridView1.CurrentCell = dataGridView1.Rows[i].Cells["Detail#"];
                                break;
                            }
                        }
                    }
                }
                if (lab == "del")
                {
                    if (dataGridView1.CurrentCell != null)
                    {
                        if (int.Parse(fid) == 0)
                        {
                            dataGridView1.CurrentCell = dataGridView1.Rows[int.Parse(fid)].Cells["Detail#"];
                        }
                        else { dataGridView1.CurrentCell = dataGridView1.Rows[int.Parse(fid) - 1].Cells["Detail#"]; }
                    }
                    else
                    {
                        textBox4.Text = "";
                        textBox5.Text = "";
                        textBox6.Text = "";
                        textBox7.Text = "";
                        textBox8.Text = "";
                        dupdate.Visible = false;
                        ddel.Visible = false;
                    }
                }
                if (lab == "cancel")
                {
                    if (dataGridView1.CurrentCell != null)
                    {
                        dataGridView1.CurrentCell = dataGridView1.Rows[int.Parse(fid)].Cells["Detail#"];
                    }
                }
            }
        }


    }
}

