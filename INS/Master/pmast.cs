using CrystalDecisions.CrystalReports.Engine;
using INS.Business.objRpt;
using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business.Report
{
    public partial class pmast : Form
    {
        //private string rpDirectory = Application.StartupPath.Replace("\\bin\\Debug", "\\objRpt");
        private string rpDirectory = "M:\\Software II\\New INS Runtime\\Setup\\objRpt";
        public string fctlid = "", p_rpttype = "", lc_title = "", lc_idlbl = "";
        public int ln_flogseq = 0; public DateTime ld_faccdate;
        private frmLossAdjuster frmLossAdjuster;
        public string Dbm = InsEnvironment.DataBase.GetDbm();

        public pmast()
        {
            InitializeComponent();
        }

        public pmast(frmLossAdjuster frmLossAdjuster)
        {
            // TODO: Complete member initialization
            this.frmLossAdjuster = frmLossAdjuster;
            InitializeComponent();
        }

        public void conctrl()
        {
            if (p_rpttype == "L") { 
                lc_title = "Record List - Loss Adjuster";
		        lc_idlbl = "Adjuster";
                this.Text = "Record List - Loss Adjuster"; 
            }
            else if (p_rpttype == "S")
            {
                lc_title = "Record List - Solicitor";
                lc_idlbl = "Solicitor";
                this.Text = "Record List - Solicitor";
            }
            else if (p_rpttype == "H")
            {
                lc_title = "Record List - Health Care";
                lc_idlbl = "Centre";
                this.Text = "Record List - Health Care";
            }
            else if (p_rpttype == "P")
            {
                lc_title = "Record List - Producer";
                lc_idlbl = "Producer";
                this.Text = "Record List - Producer";
            }
            else if (p_rpttype == "C")
            {
                lc_title = "Record List - Client";
                lc_idlbl = "Client";
                this.Text = "Record List - Client";
            }
            else if (p_rpttype == "B")
            {
                lc_title = "Record List - R/I Broker";
                lc_idlbl = "R/I Broker";
                this.Text = "Record List - R/I Broker";
            }
            else if (p_rpttype == "R")
            {
                lc_title = "Record List - Reinsurer";
                lc_idlbl = "Reinsurer";
                this.Text = "Record List - Reinsurer";
            }
        }

        private void PS_Click(object sender, EventArgs e)
        {
            progressBar1.Visible = true;
            progressBar1.Maximum = 50;//设置最大长度值 
            progressBar1.Value = 0;//设置当前值 
            progressBar1.Step = 5;//设置没次增长多少 
            for (int i = 0; i < 10; i++)//循环 
            {
                System.Threading.Thread.Sleep(1000);//暂停1秒 
                progressBar1.Value += progressBar1.Step;
            }
            string sql = "select *,case when finter='1' then 'Y' else 'N' end as pinter, " +
                    "case when fsubside='1' then 'COLI' else case when fsubside='2' then 'COXX' else '------' end end as psubside " +
                    "from " + Dbm + "mprdr where fctlid ='" + fctlid + "'";
            DataTable dt = DBHelper.GetDataSet(sql);

            string sql1 = "select * from " + Dbm + "mcontact where fctlid_1 ='" + fctlid + "'";
            DataTable dtc = DBHelper.GetDataSet(sql1);

            string sql2 = "select * from " + Dbm + "mdept where fctlid_1 ='" + fctlid + "'";
            DataTable dtd = DBHelper.GetDataSet(sql2);

            ReportDocument cryRpt = new ReportDocument();
            cryRpt.Load(rpDirectory + "\\mprdr.rpt");
            cryRpt.SetDataSource(dt);
            cryRpt.Subreports["cont"].SetDataSource(dtc);
            cryRpt.Subreports["dept"].SetDataSource(dtd);
            cryDocViewer temp_form = new cryDocViewer(cryRpt);
            temp_form.ShowDialog();
            this.Close();
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
