using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;

namespace INS
{
    public partial class frmAtt : Form
    {
        public frmAtt()
        {
            InitializeComponent();
            InitCombobox();
            FillData("", "");
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
        }
        void control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
            {
                SendKeys.Send("{TAB}");
            }
        }
        DES des = new DES();
        DBConnect operate = new DBConnect();
        ExportDBF DBF = new ExportDBF();
        XLS txt = new XLS();
        public string fctlid_a = "", oldrow="";
        public String user = InsEnvironment.LoginUser.GetUserCode();
        public int row = 0;
        public Boolean flag = false;
        public Boolean ADD = false;
        private bool datagridview2SelectionChanged = true;
        private DataTable dataSource = new DataTable();

        void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            try
            {
                if (order != "" && query != "")
                {
                    if (order == "Class#")
                    {
                        a = new SqlDataAdapter("SELECT fid as Class#,fdesc as Desc#,fctlid FROM minsc where fid like '%" + query.ToUpper() + "%'", c);
                    }
                    else
                    {
                        a = new SqlDataAdapter("SELECT fid as Class#,fdesc as Desc#,fctlid FROM minsc where fdesc like '%" + query.ToUpper() + "%'", c);
                    }
                }
                else if (order != "" && query == "")
                {
                    a = new SqlDataAdapter("SELECT fid as Class#,fdesc as Desc#,fctlid FROM minsc order by " + order, c);
                }
                else
                {
                    a = new SqlDataAdapter("SELECT fid as Class#,fdesc as Desc#,fctlid FROM minsc", c);
                }
                DataTable t = new DataTable();
                a.Fill(t);
                dataGridView1.DataSource = t;
                this.dataGridView1.Columns[2].Visible = false;
            }
            catch { }

            //DBF.CreateDBF("D:\\text1.dbf",t).ToString();
            //dataGridView1.DataSource = txt.ReadExcelToTable("D:\\book1.xls");
            c.Close();
        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", "");
        }

        void reloadgridview2(string fid, string lab)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("SELECT fctlid, fid as Code#,flogseq as Seq#,fabbr as Abbr,fshort as ShortName,fdesc as Desc#,fdefault FROM mclause where fdoctype=2 and fctlid_a = '" + fctlid_a + "' ORDER BY Seq#", c))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                dataGridView2.DataSource = t2;
                this.dataGridView2.Columns[0].Visible = false;
                this.dataGridView2.Columns[6].Visible = false;
                if (t2.Rows.Count == 0)
                {
                    textBox4.Text = "";
                    textBox5.Text = "";
                    textBox6.Text = "";
                    textBox7.Text = "";
                    textBox8.Text = "";
                    comboBox2.Enabled = false;
                    bupdate.Visible = false;
                    bdel.Visible = false;
                }
                else
                {
                    bupdate.Visible = true;
                    bdel.Visible = true;
                }

                if (lab == "insert" || lab == "update")
                {
                    if (fid != "") //add update
                    {
                        for (int i = 0; i < t2.Rows.Count; i++)
                        {
                            if (t2.Rows[i]["Code#"].ToString().Trim() == fid.Trim())
                            {
                                dataGridView2.CurrentCell = dataGridView2.Rows[i].Cells["Code#"];
                                break;
                            }
                        }
                    }
                }
                if (lab == "del")
                {
                    if (dataGridView2.CurrentCell != null)
                    {
                        if (int.Parse(fid) == 0) { 
                            dataGridView2.CurrentCell = dataGridView2.Rows[int.Parse(fid)].Cells["Seq#"]; } 
                        else { dataGridView2.CurrentCell = dataGridView2.Rows[int.Parse(fid) - 1].Cells["Seq#"]; }
                    }
                    else
                    {
                        textBox4.Text = "";
                        textBox5.Text = "";
                        textBox6.Text = "";
                        textBox7.Text = "";
                        textBox8.Text = "";
                        comboBox2.Enabled = false;
                        bupdate.Visible = false;
                        bdel.Visible = false;
                    }
                }
                if (lab == "cancel")
                {
                    if (dataGridView2.CurrentCell != null)
                    {
                        dataGridView2.CurrentCell = dataGridView2.Rows[int.Parse(fid)].Cells["Seq#"];
                    }
                }

            }
            datagridview2SelectionChanged = true;
            flag = false;
            c.Close();

        }


        void ButtonControl()
        {
            ADD = false;
            textBox4.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox4.ReadOnly = true;
            textBox5.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox5.ReadOnly = true;
            textBox6.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox6.ReadOnly = true;
            textBox7.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox7.ReadOnly = true;
            textBox8.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox8.ReadOnly = true;
            bsaving.Visible = false;
            bcancel.Visible = false;
            bsave.Visible = false;
            bexit.Visible = true;
            bdel.Visible = true;
            bupdate.Visible = true;
            badd.Visible = true;
            comboBox2.BackColor = System.Drawing.SystemColors.InactiveCaption;
            comboBox2.Enabled = false;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting2);
            dataGridView2.Enabled = true;
        }

        private void tabControl1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            int counter;
            if (tabControl1.SelectedIndex == 1)
            {
                counter = dataGridView1.CurrentCell.RowIndex;
                if (dataGridView1.Rows[counter].Cells["Class#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Class#"].Value.ToString().Length != 0)
                    {
                        textBox2.Text = dataGridView1.Rows[counter].Cells["Class#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        textBox3.Text = dataGridView1.Rows[counter].Cells["Desc#"].Value.ToString().Trim();
                    }
                }
                fctlid_a = dataGridView1.Rows[counter].Cells["fctlid"].Value.ToString();
                reloadgridview2("", "");
            }
            if (tabControl1.SelectedIndex == 0) { row = 0; rowlabel.Text = "0"; textBox4.Text = ""; textBox5.Text = ""; textBox6.Text = ""; textBox7.Text = ""; textBox8.Text = ""; }
        }


        private void dataGridView2_SelectionChanged(object sender, EventArgs e)
        {
            if (!this.datagridview2SelectionChanged) return;
            try
            {
                if (flag == false && dataGridView2.CurrentCell != null)
                {
                    row = dataGridView2.CurrentCell.RowIndex;
                }

                if (dataGridView2.Rows[row].Cells["Code#"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["Code#"].Value.ToString().Length != 0)
                    {
                        textBox4.Text = dataGridView2.Rows[row].Cells["Code#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView2.Rows[row].Cells["Seq#"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["Seq#"].Value.ToString().Length != 0)
                    {
                        textBox5.Text = dataGridView2.Rows[row].Cells["Seq#"].Value.ToString().Trim();
                    }
                }
                if (dataGridView2.Rows[row].Cells["ShortName"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["ShortName"].Value.ToString().Length != 0)
                    {
                        textBox8.Text = dataGridView2.Rows[row].Cells["ShortName"].Value.ToString().Trim();
                    }
                }
                if (dataGridView2.Rows[row].Cells["Desc#"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        textBox6.Text = dataGridView2.Rows[row].Cells["Desc#"].Value.ToString().Trim();
                    }
                }
                if (dataGridView2.Rows[row].Cells["Abbr"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["Abbr"].Value.ToString().Length != 0)
                    {
                        textBox7.Text = dataGridView2.Rows[row].Cells["Abbr"].Value.ToString().Trim();
                    }
                }
                if (dataGridView2.Rows[row].Cells["fdefault"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["fdefault"].Value.ToString().Length != 0)
                    {
                        if (dataGridView2.Rows[row].Cells["fdefault"].Value.ToString() == "2")
                        { comboBox2.SelectedItem = "No"; }
                        else { comboBox2.SelectedItem = "Yes"; }

                    }
                }
                if (dataGridView2.Rows[row].Cells["fctlid"].Value != null)
                {
                    fctlidlabel.Text = dataGridView2.Rows[row].Cells["fctlid"].Value.ToString();
                }
                rowlabel.Text = row.ToString();
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void button4_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void button5_Click(object sender, EventArgs e)
        {

            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the item?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string fctlid = fctlidlabel.Text;
                string code = textBox4.Text;
                string deldatestr =
                    "delete from mclause where fctlid='" + fctlid + "'";
                operate.OperateData(deldatestr);
                MessageBox.Show("Have Been Deleted", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                reloadgridview2(rowlabel.Text, "del");
            }

        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            e.Cancel = true;
        }

        private void tabControl1_Selecting2(object sender, TabControlCancelEventArgs e)
        {
            e.Cancel = false;
        }

        private void button3_Click(object sender, EventArgs e)
        {
            oldrow = rowlabel.Text;
            dataSource = dataGridView2.DataSource as DataTable;
            textBox4.BackColor = System.Drawing.SystemColors.Window;
            textBox4.ReadOnly = false;
            textBox5.BackColor = System.Drawing.SystemColors.Window;
            textBox5.ReadOnly = false;
            textBox6.BackColor = System.Drawing.SystemColors.Window;
            textBox6.ReadOnly = false;
            textBox7.BackColor = System.Drawing.SystemColors.Window;
            textBox7.ReadOnly = false;
            textBox8.BackColor = System.Drawing.SystemColors.Window;
            textBox8.ReadOnly = false;
            bsaving.Visible = true;
            bcancel.Visible = true;
            bexit.Visible = false;
            bdel.Visible = false;
            bupdate.Visible = false;
            badd.Visible = false;
            comboBox2.BackColor = System.Drawing.SystemColors.Window;
            comboBox2.Enabled = true;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);
            dataGridView2.Enabled = false;
        }

        private void button6_Click(object sender, EventArgs e)
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really Update the item?", "Updated Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                String fctlid = fctlidlabel.Text;
                String code = textBox4.Text;
                String flogseq = textBox5.Text;
                String abbr = textBox7.Text;
                String desc = textBox6.Text;
                String shortname = textBox8.Text;
                int fdefault;
                if (comboBox2.SelectedItem.ToString() == "Yes")
                { fdefault = 1; }
                else { fdefault = 2; }
                if (code != "" && flogseq != "" && fdefault != 0)
                {
                string updatedatestr =
                    "update mclause set fid ='" + code.Trim().Replace("'", "''") + "', flogseq='" + flogseq + "',fabbr ='" + abbr.Trim().Replace("'", "''") + "', fshort ='" + shortname.Trim().Replace("'", "''") + "', " +
                    "fdesc ='" + desc.Trim().Replace("'", "''") + "', fdefault ='" + fdefault + "', fupduser ='" + user + "', fupddate ='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid='" + fctlid + "'";
                operate.OperateData(updatedatestr);
                MessageBox.Show("Have Been Updated", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                ButtonControl();
                reloadgridview2(code, "update");
                }
                else
                {
                    MessageBox.Show("Some field is empty!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            

        }

        private void button7_Click(object sender, EventArgs e)
        {
            ButtonControl();
            reloadgridview2(oldrow, "cancel");
        }

        private void button2_Click(object sender, EventArgs e)
        {
            ADD = true;
            oldrow = rowlabel.Text;
            DataTable listdataSource = dataGridView2.DataSource as DataTable;
            DataRow newCustomersRow = listdataSource.NewRow();
            listdataSource.Rows.InsertAt(newCustomersRow, listdataSource.Rows.Count);
            dataGridView2.DataSource = listdataSource;
            dataGridView2.CurrentCell = dataGridView2.Rows[listdataSource.Rows.Count - 1].Cells["Code#"];
            textBox4.BackColor = System.Drawing.SystemColors.Window;
            textBox4.ReadOnly = false;
            textBox4.Text = "";
            textBox5.BackColor = System.Drawing.SystemColors.Window;
            textBox5.ReadOnly = false;
            textBox5.Text = "";
            textBox6.BackColor = System.Drawing.SystemColors.Window;
            textBox6.ReadOnly = false;
            textBox6.Text = "";
            textBox7.BackColor = System.Drawing.SystemColors.Window;
            textBox7.ReadOnly = false;
            textBox7.Text = "";
            textBox8.BackColor = System.Drawing.SystemColors.Window;
            textBox8.ReadOnly = false;
            textBox8.Text = "";
            comboBox2.BackColor = System.Drawing.SystemColors.Window;
            comboBox2.Enabled = true;
            bcancel.Visible = true;
            bsave.Visible = true;
            bexit.Visible = false;
            bdel.Visible = false;
            bupdate.Visible = false;
            badd.Visible = false;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);
            dataGridView2.Enabled = false;
        }

        private void button8_Click(object sender, EventArgs e)
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really Insert the item?", "Inserted Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                String fctlid = "";
                String code = textBox4.Text;
                String flogseq = textBox5.Text;
                String abbr = textBox7.Text;
                String desc = textBox6.Text;
                String shortname = textBox8.Text;
                string str = "select * from xsysparm where fidtype ='CLSID'";
                DataTable dt = new DataTable();
                dt = operate.GetTable(str);
                fctlid = dt.Rows[0]["fnxtid"].ToString();
                fctlidlabel.Text = fctlid;
                int fdefault = 0;
                if (comboBox2.SelectedItem.ToString() == "Yes")
                { fdefault = 1; }
                else { fdefault = 2; }


                if (code != "" && flogseq != "" && fdefault != 0)
                {
                    string adddatestr =
                            "insert into mclause(ftype,fctlid_a,fctlid_1,fctlid,fid,fdoctype,fsid,flogseq,fabbr," +
                    "fshort,fdesc,fcontent,findent,ftoprnsid,ftoprnhd,ftoprncont,fsubno,fdefault,fctldel,finpuser,finpdate,fupduser,fupddate) " +
                    "values('1','" + fctlid_a + "','','" + fctlid + "','" + code.Trim().Replace("'", "''") + "','2','','" + flogseq + "','" + abbr.Trim().Replace("'", "''") + "','" + shortname.Trim().Replace("'", "''") + "'," +
                    "'" + desc.Trim().Replace("'", "''") + "','','','','','','','" + fdefault + "','','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "')";
                    operate.OperateData(adddatestr);

                    string updatestr = "update xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='CLSID'";
                    operate.OperateData(updatestr);

                    MessageBox.Show("Have Been Inserted!", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    ButtonControl();
                    reloadgridview2(code, "insert");

                }
                else
                {
                    MessageBox.Show("Some field is empty!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

       

        public string CheckDuplicate(string fid)
        {
            if (fid != "")
            {
                DataTable listdataSource = dataGridView2.DataSource as DataTable;
                if (listdataSource.Rows.Count > 0)
                {
                    DataTable objectTable = listdataSource.DefaultView.ToTable();
                    DataRow[] rows = objectTable.Select("[Code#]='" + fid.Trim() + "'");
                    if (rows.Length > 1)
                    {
                        return "Duplicate Value";
                    }
                    else { return ""; }
                }
                else { return ""; }
            }
            else { return ""; }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", textBox1.Text.ToString().Trim());
        }

        private void button9_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        public enum Mode
        {
            Class = 1,
            Desc = 2
        }

        private void InitCombobox()
        {
            Array arr = System.Enum.GetValues(typeof(Mode));    // 获取枚举的所有值
            DataTable dt = new DataTable();
            dt.Columns.Add("String", Type.GetType("System.String"));
            dt.Columns.Add("Value", typeof(int));
            foreach (var a in arr)
            {
                string strText = EnumTextByDescription.GetEnumDesc((Mode)a);
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = (int)a;

                dt.Rows.Add(aRow);
            }

            comboBox1.DataSource = dt;
            comboBox1.DisplayMember = "String";
            comboBox1.ValueMember = "Value";
        }

        private void textBox4_TextChanged(object sender, EventArgs e)
        {
            if (dataGridView2.CurrentCell != null)
            {
                dataGridView2["Code#", dataGridView2.CurrentCell.RowIndex].Value = textBox4.Text;
                string result2 = CheckDuplicate(textBox4.Text);
                if (result2 != "")
                {
                    MessageBox.Show(result2);
                    textBox4.Focus();
                    textBox4.SelectAll();
                }
            }
        }

        private void textBox5_TextChanged(object sender, EventArgs e)
        {
            if (dataGridView2.CurrentCell != null)
            {
                if (textBox5.Text != "")
                {
                    dataGridView2["Seq#", dataGridView2.CurrentCell.RowIndex].Value = textBox5.Text;
                }
            }
        }

        private void textBox7_TextChanged(object sender, EventArgs e)
        {
            if (dataGridView2.CurrentCell != null)
            {
                dataGridView2["Abbr", dataGridView2.CurrentCell.RowIndex].Value = textBox7.Text;
            }
        }

        private void textBox8_TextChanged(object sender, EventArgs e)
        {
            if (dataGridView2.CurrentCell != null)
            {
                dataGridView2["ShortName", dataGridView2.CurrentCell.RowIndex].Value = textBox8.Text;
            }
        }

        private void textBox6_TextChanged(object sender, EventArgs e)
        {
            if (dataGridView2.CurrentCell != null)
            {
                dataGridView2["Desc#", dataGridView2.CurrentCell.RowIndex].Value = textBox6.Text;
            }
        }





    }
}

