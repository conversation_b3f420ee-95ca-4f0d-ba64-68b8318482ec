using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using System.Collections;
using System.Configuration;
using INS.Business.Report;
using INS.Business;
using INS.Forms;
using System.Globalization;
using INS.Business.objRpt;
using CrystalDecisions.CrystalReports.Engine;

namespace INS
{
    public partial class tcvrlet : Form
    {
        BindData[] u_dtypearr = new BindData[]  {
                                 new BindData("M","Misc."),
                                 new BindData("C","Client"),
                                 new BindData("P","Producer"),
                                 new BindData("B","R/I Broker"),
                                 new BindData("R","Reinsurer")};
        BindData[] u_statidarr = new BindData[]  {
                                   new BindData("D0100","Premium (Dir)"),
                                   new BindData("D0104","Premium (Out)"),
                                   new BindData("D0200","Claims (Dir)"),
                                   new BindData("D0204","Claims (Out)"),
                                   new BindData("R0100","Premium (In)"),
                                   new BindData("R0200","Claims (In)"),
                                   new BindData("G0000","Miscellaneous")};

        private string rpDirectory = "M:\\Software II\\New INS Runtime\\Setup\\objRpt";
        DBConnect operate = new DBConnect();
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public string fctlid_1 = "", fbus = "";
        public String oldrow = "", fctlid = "";
        public Boolean requery = false;
        public Boolean Addflag = false, Updateflag = false;
        public Boolean Skip_Validate = false;
        private TextBox Cur_TextBox = null;

        public tcvrlet()
        {
            InitializeComponent();
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            FillData("", "", "");
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
            {
                SendKeys.Send("{TAB}");
            }
        }

        public void FillData(string order, string query, string list)
        {
            try
            {
                string sql = "select frefno as [Ref No.], CONVERT(varchar(10),fdocdate,102) as [Doc Date], faddrsee as [Addressee], " +
                    "fchkno,faddr,fattn,ftotamt,finpuser,finpdate,fupduser,fupddate,fctlid " +
                    "from OCVRLET ";

                dataGridView1.DataSource = DBHelper.GetDataSet(sql);
                for (int i = 3; i < dataGridView1.ColumnCount; i++)
                {
                    this.dataGridView1.Columns[i].Visible = false;
                }
                dataGridView1.CellFormatting +=
                new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.dataGridView1_CellFormatting);
                btnctrl("back");
            }
            catch { }
        }

        private void dataGridView1_CellFormatting(object sender,System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {

            // Set the background to red for negative values in the Balance column.
            if (dataGridView1.Columns[e.ColumnIndex].Name.Equals("Ref No."))
            {
                dataGridView1.Columns[e.ColumnIndex].Width = 85;
            }
            if (dataGridView1.Columns[e.ColumnIndex].Name.Equals("Doc Date"))
            {
                dataGridView1.Columns[e.ColumnIndex].Width = 62;
            }     
        }

        private void dataGridView1_CurrentCellChanged(object sender, EventArgs e)
        {
            chgdview();
        }

        void chgdview() {
            if (dataGridView1.CurrentCell != null)
            {
                int counter;
                counter = dataGridView1.CurrentCell.RowIndex;
                if (dataGridView1.Rows[counter].Cells["Ref No."].Value != null)
                {
                    frefno.Text = dataGridView1.Rows[counter].Cells["Ref No."].Value.ToString().Trim();
                }

                if (dataGridView1.Rows[counter].Cells["Doc Date"].Value != null)
                {
                    try
                    {
                        string a = dataGridView1.Rows[counter].Cells["Doc Date"].Value.ToString().Trim();
                        DateTime b = Convert.ToDateTime(a);
                        fdocdate.Text = b.ToString("yyyy.MM.dd");
                    }
                    catch { }
                }

                if (dataGridView1.Rows[counter].Cells["fchkno"].Value != null)
                {
                    fchkno.Text = dataGridView1.Rows[counter].Cells["fchkno"].Value.ToString().Trim();
                }

                if (dataGridView1.Rows[counter].Cells["Addressee"].Value != null)
                {
                    faddrsee.Text = dataGridView1.Rows[counter].Cells["Addressee"].Value.ToString().Trim();
                }

                if (dataGridView1.Rows[counter].Cells["faddr"].Value != null)
                {
                    faddr_t.Text = dataGridView1.Rows[counter].Cells["faddr"].Value.ToString().Trim();
                }

                if (dataGridView1.Rows[counter].Cells["fattn"].Value != null)
                {
                    fattn.Text = dataGridView1.Rows[counter].Cells["fattn"].Value.ToString().Trim();
                }

                if (dataGridView1.Rows[counter].Cells["ftotamt"].Value != null)
                {
                    ftotamt.Text = dataGridView1.Rows[counter].Cells["ftotamt"].Value.ToString().Trim();
                }

                if (dataGridView1.Rows[counter].Cells["finpuser"].Value != null)
                {
                    finpuser.Text = dataGridView1.Rows[counter].Cells["finpuser"].Value.ToString().Trim();
                }

                if (dataGridView1.Rows[counter].Cells["finpdate"].Value != null)
                {
                    finpdate.Text = dataGridView1.Rows[counter].Cells["finpdate"].Value.ToString().Trim();
                }

                if (dataGridView1.Rows[counter].Cells["fupduser"].Value != null)
                {
                    fupduser.Text = dataGridView1.Rows[counter].Cells["fupduser"].Value.ToString().Trim();
                }

                if (dataGridView1.Rows[counter].Cells["fupddate"].Value != null)
                {
                    fupddate.Text = dataGridView1.Rows[counter].Cells["fupddate"].Value.ToString().Trim();
                }
                rowlabel.Text = counter.ToString();
                fctlid = dataGridView1.Rows[counter].Cells["fctlid"].Value.ToString().Trim();
                Reloadgridview2(fctlid);
            }
        }

        void Reloadgridview2(string fctlid_i)
        {
            string sql = "SELECT flogseq,finvno,fyrref,forref,fremark,famount1,famount2,fdesc1,fdesc2,fdbtrtype,fdbtr,fpayee,fmodule,fctlid from ocvrdet where fctlid_lt='" + fctlid_i + "' ";
            tcvrdet.DataSource = DBHelper.GetDataSet(sql);

            for (int i = 7; i < tcvrdet.ColumnCount; i++)
            {
                this.tcvrdet.Columns[i].Visible = false;
            }
            tcvrdet.CellFormatting +=
            new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.tcvrdet_CellFormatting);

           
        }

        private void tcvrdet_CellFormatting(object sender,
System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            tcvrdet.Columns["flogseq"].HeaderText = "Seq#";
            tcvrdet.Columns["finvno"].HeaderText = "Dr/Cr No.";
            tcvrdet.Columns["fyrref"].HeaderText = "Your Ref#";
            tcvrdet.Columns["forref"].HeaderText = "Our Ref#";
            tcvrdet.Columns["fremark"].HeaderText = "Remark";
            tcvrdet.Columns["famount1"].HeaderText = "Amount(1)";
            tcvrdet.Columns["famount2"].HeaderText = "Amount(2)";
            tcvrdet.Columns["famount1"].DefaultCellStyle.Format = "N2";
            tcvrdet.Columns["famount2"].DefaultCellStyle.Format = "N2";
            tcvrdet.Columns["flogseq"].DefaultCellStyle.Format = "N0";
        }

        void chg() {
            if (tcvrdet.CurrentCell != null)
            {
                int counter;
                counter = tcvrdet.CurrentCell.RowIndex;
                if (tcvrdet.Rows[counter].Cells["flogseq"].Value != null)
                {
                    flogseq.Text = tcvrdet.Rows[counter].Cells["flogseq"].Value.ToString().Trim();
                }
                if (tcvrdet.Rows[counter].Cells["finvno"].Value != null)
                {
                    finvno.Text = tcvrdet.Rows[counter].Cells["finvno"].Value.ToString().Trim();
                }
                if (tcvrdet.Rows[counter].Cells["fyrref"].Value != null)
                {
                    fyrref.Text = tcvrdet.Rows[counter].Cells["fyrref"].Value.ToString().Trim();
                }
                if (tcvrdet.Rows[counter].Cells["forref"].Value != null)
                {
                    forref.Text = tcvrdet.Rows[counter].Cells["forref"].Value.ToString().Trim();
                }
                if (tcvrdet.Rows[counter].Cells["fremark"].Value != null)
                {
                    fremark.Text = tcvrdet.Rows[counter].Cells["fremark"].Value.ToString().Trim();
                }
                if (tcvrdet.Rows[counter].Cells["famount1"].Value != null)
                {
                    famount1.Text = tcvrdet.Rows[counter].Cells["famount1"].Value.ToString().Trim();
                }
                if (tcvrdet.Rows[counter].Cells["famount2"].Value != null)
                {
                    famount2.Text = tcvrdet.Rows[counter].Cells["famount2"].Value.ToString().Trim();
                }
                if (tcvrdet.Rows[counter].Cells["fdesc1"].Value != null)
                {
                    fdesc1.Text = tcvrdet.Rows[counter].Cells["fdesc1"].Value.ToString().Trim();
                }
                if (tcvrdet.Rows[counter].Cells["fdesc2"].Value != null)
                {
                    fdesc2.Text = tcvrdet.Rows[counter].Cells["fdesc2"].Value.ToString().Trim();
                }
            }
        }

        private void tcvrdet_CurrentCellChanged(object sender, EventArgs e)
        {
            chg();
        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            e.Cancel = true;
        }

        private void tabControl1_Selecting2(object sender, TabControlCancelEventArgs e)
        {
            e.Cancel = false;
        }

        private void add2_Click(object sender, EventArgs e)
        {
            decimal Row = 0;
            if (tcvrdet.Rows.Count != 0)
            {
                Row = Fct.sdFat(tcvrdet.Rows[tcvrdet.Rows.Count - 1].Cells["flogseq"].Value);
                del2.Visible = true;
            }
            else { Row = 0; del2.Visible = false; }

            DataTable DTtcvrdet = tcvrdet.DataSource as DataTable;
            DataRow newCustomersRow = DTtcvrdet.NewRow();
            newCustomersRow["flogseq"] = Row + 1;
            DTtcvrdet.Rows.InsertAt(newCustomersRow, tcvrdet.Rows.Count);
            tcvrdet.DataSource = DTtcvrdet;
            tcvrdet.CurrentCell = tcvrdet.Rows[tcvrdet.Rows.Count - 1].Cells["flogseq"];

            foreach (Control ctrl in panel15.Controls)
            {
                if (ctrl is TextBox)
                {
                    if (ctrl.Name != "flogseq") { ((TextBox)ctrl).Text = ""; }
                    ((TextBox)ctrl).ReadOnly = false;
                    ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                }
            }
            foreach (Control ctrl in panel2.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                    ((TextBox)ctrl).ReadOnly = false;
                    ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                }
            }
            finvno.ReadOnly = true;
            finvno.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            XBTN.Enabled = true;
            if (tcvrdet.Rows.Count != 0) { del2.Visible = true; }
        }

        private void del2_Click(object sender, EventArgs e)
        {
            if (tcvrdet.RowCount != 0)
            {
                try
                {
                    DataTable DTtcvrdet = tcvrdet.DataSource as DataTable;
                    DTtcvrdet.Rows.RemoveAt(tcvrdet.CurrentCell.RowIndex);
                    tcvrdet.DataSource = DTtcvrdet;
                    tcvrdet.CurrentCell = tcvrdet.Rows[tcvrdet.Rows.Count - 1].Cells["flogseq"];
                }
                catch { }
            }
            if (tcvrdet.RowCount == 0)
            {
                foreach (Control ctrl in panel15.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                }
                foreach (Control ctrl in panel2.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                }
                del2.Visible = false;
                XBTN.Enabled = false;
            }
        }

        void btnctrl(string flag)
        {
            if (flag == "add" || flag == "update")
            {
                ladd.Visible = false;
                dadd.Visible = false;
                ldel.Visible = false;
                ddel.Visible = false;
                lupdate.Visible = false;
                dupdate.Visible = false;
                lexit.Visible = false;
                dexit.Visible = false;
                lprint.Visible = false;
                dprint.Visible = false;
                lsave.Visible = true;
                dsave.Visible = true;
                lcancel.Visible = true;
                dcancel.Visible = true;
            }
            if (flag == "back")
            {
                Addflag = false;
                Updateflag = false;
                foreach (Control ctrl in Clauses.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                    if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
                    if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                }
                foreach (Control ctrl in panel15.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                    if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
                }
                foreach (Control ctrl in panel2.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                }
                foreach (Control ctrl in panel4.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                }
                add2.Visible = false;
                del2.Visible = false;
                ladd.Visible = true;
                dadd.Visible = true;
                lupdate.Visible = true;
                dupdate.Visible = true;
                ldel.Visible = true;
                ddel.Visible = true;
                lexit.Visible = true;
                dexit.Visible = true;
                lprint.Visible = true;
                dprint.Visible = true;
                lsave.Visible = false;
                dsave.Visible = false;
                lcancel.Visible = false;
                dcancel.Visible = false;
                tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting2);
            }
        }

        void add() 
        {
            tabControl1.SelectedTab = Clauses;
            oldrow = rowlabel.Text;
            btnctrl("add");
            Addflag = true;
            add2.Visible = true;
            frefno.ReadOnly = false;
            frefno.BackColor = System.Drawing.SystemColors.Window;
            fdocdate.ReadOnly = false;
            fdocdate.BackColor = System.Drawing.SystemColors.Window;
            fchkno.ReadOnly = false;
            fchkno.BackColor = System.Drawing.SystemColors.Window;
            foreach (Control ctrl in Clauses.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                }
            }
            foreach (Control ctrl in panel4.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                    ((TextBox)ctrl).ReadOnly = false;
                    ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                }
            }
            XBTN.Enabled = true;
            btnaddr.Enabled = true;
            DataTable dt = tcvrdet.DataSource as DataTable;
            dt.Clear(); tcvrdet.DataSource = dt;
            foreach (Control ctrl in panel15.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                }
            }
            foreach (Control ctrl in panel2.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                }
            }
            fdocdate.Text = DateTime.Now.ToString("yyyy.MM.dd");
            frefno.Focus();
        }

        void del()
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the information?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string deldatestr = "delete from ocvrlet where fctlid='" + fctlid + "'";
                string deldatestr1 = "delete from ocvrdet where fctlid_lt='" + fctlid + "'";
                string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();

                    SqlCommand command = connection.CreateCommand();
                    SqlTransaction transaction;

                    // Start a local transaction.
                    transaction = connection.BeginTransaction("SampleTransaction");

                    // Must assign both transaction object and connection 
                    // to Command object for a pending local transaction
                    command.Connection = connection;
                    command.Transaction = transaction;

                    try
                    {
                        command.CommandText = deldatestr;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr1;
                        command.ExecuteNonQuery();
                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("Haven't Been Deleted", "Warning",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                FillData("", "", "");
                tabControl1.SelectedTab = Classes;
            }
        }

        void modify()
        {
            tabControl1.SelectedTab = Clauses;
            oldrow = rowlabel.Text;
            btnctrl("update");
            Updateflag = true;
            add2.Visible = true;
            del2.Visible = true;
            frefno.ReadOnly = false;
            frefno.BackColor = System.Drawing.SystemColors.Window;
            fdocdate.ReadOnly = false;
            fdocdate.BackColor = System.Drawing.SystemColors.Window;
            fchkno.ReadOnly = false;
            fchkno.BackColor = System.Drawing.SystemColors.Window;
            foreach (Control ctrl in panel4.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = false;
                    ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                }
            }
            XBTN.Enabled = true;
            btnaddr.Enabled = true;
            foreach (Control ctrl in panel15.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = false;
                    ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                }
            }
            foreach (Control ctrl in panel2.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = false;
                    ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                }
            }
            finvno.ReadOnly = true;
            finvno.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
        }

        void reload(string fid, string lab)
        {
            string str = "select frefno as [Ref No.], CONVERT(varchar(10),fdocdate,102) as [Doc Date], faddrsee as [Addressee], " +
                    "fchkno,faddr,fattn,ftotamt,finpuser,finpdate,fupduser,fupddate,fctlid " +
                    "from OCVRLET ";
            DataTable dt = DBHelper.GetDataSet(str);
            if (dt.Rows.Count > 0)
            {
                dataGridView1.DataSource = dt;
                for (int i = 3; i < dataGridView1.ColumnCount; i++)
                {
                    this.dataGridView1.Columns[i].Visible = false;
                }
                dataGridView1.CellFormatting +=
                new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.dataGridView1_CellFormatting);

                if ((lab == "insert" || lab == "update") && fid != "")
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        if (dt.Rows[i]["Ref No."].ToString().Trim() == fid.Trim())
                        {
                            dataGridView1.CurrentCell = dataGridView1.Rows[i].Cells["Ref No."];
                            break;
                        }
                    }
                }

                //if (lab == "cancel" && dataGridView1.CurrentCell != null)
                //{
                //    dataGridView1.CurrentCell = dataGridView1.Rows[int.Parse(fid)].Cells["Ref No."];
                //}
            }
        }

        void cancel()
        {
            string oldrow = frefno.Text;
            btnctrl("back");
            reload(oldrow, "cancel");
            DataTable dt3 = dataGridView1.DataSource as DataTable;
            foreach (DataRow row in dt3.Rows)
            {
                int SelectedIndex = dt3.Rows.IndexOf(row);
                if (oldrow == row["Ref No."].ToString().Trim())
                {
                    dataGridView1.CurrentCell = dataGridView1.Rows[SelectedIndex].Cells["Ref No."];
                    break;
                }
            }
        }

        private void ladd_Click(object sender, EventArgs e)
        {
            add();
        }

        private void lupdate_Click(object sender, EventArgs e)
        {
            modify();
        }

        private void ldel_Click(object sender, EventArgs e)
        {
            del();
        }

        private void lprint_Click(object sender, EventArgs e)
        {
            pcvrlet();
        }

        private void lcancel_Click(object sender, EventArgs e)
        {
            cancel();
        }

        private void lsave_Click(object sender, EventArgs e)
        {
            string oldrow = frefno.Text;
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really Save this item?", "Updated Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                string ctrlName ="";
                if (ctrlName != "")
                {
                    //setControlFocus(ctrlName);
                    return;
                }
                else
                {
                    string[] sql1 = save();
                    string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        connection.Open();

                        SqlCommand command = connection.CreateCommand();
                        SqlTransaction transaction;

                        // Start a local transaction.
                        transaction = connection.BeginTransaction("SampleTransaction");

                        // Must assign both transaction object and connection 
                        // to Command object for a pending local transaction
                        command.Connection = connection;
                        command.Transaction = transaction;

                        try
                        {
                            if (sql1 != null)
                            {
                                for (int i = 0; i < sql1.Length; i++)
                                {
                                    if (sql1[i] != "" && sql1[i] != null)
                                    {
                                        command.CommandText = sql1[i];
                                        command.ExecuteNonQuery();
                                    }
                                }
                            }
                            transaction.Commit();
                            btnctrl("back");
                            FillData("", "", "");
                            DataTable dt3 = dataGridView1.DataSource as DataTable;
                            foreach (DataRow row in dt3.Rows)
                            {
                                int SelectedIndex = dt3.Rows.IndexOf(row);
                                if (oldrow == row["Ref No."].ToString().Trim())
                                {
                                    dataGridView1.CurrentCell = dataGridView1.Rows[SelectedIndex].Cells["Ref No."];
                                    chg();
                                    break;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show("Haven't Been inserted!", "Warning",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                }
            }
        }

        private void lexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void dadd_Click(object sender, EventArgs e)
        {
            add();
        }

        private void dupdate_Click(object sender, EventArgs e)
        {
            modify();
        }

        private void ddel_Click(object sender, EventArgs e)
        {
            del();
        }

        private void dsave_Click(object sender, EventArgs e)
        {
            string oldrow = frefno.Text;
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really Save this item?", "Updated Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                string ctrlName = "";
                if (ctrlName != "")
                {
                    //setControlFocus(ctrlName);
                    return;
                }
                else
                {
                    string[] sql1 = save();
                    string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        connection.Open();

                        SqlCommand command = connection.CreateCommand();
                        SqlTransaction transaction;

                        // Start a local transaction.
                        transaction = connection.BeginTransaction("SampleTransaction");

                        // Must assign both transaction object and connection 
                        // to Command object for a pending local transaction
                        command.Connection = connection;
                        command.Transaction = transaction;

                        try
                        {
                            if (sql1 != null)
                            {
                                for (int i = 0; i < sql1.Length; i++)
                                {
                                    if (sql1[i] != "" && sql1[i] != null)
                                    {
                                        command.CommandText = sql1[i];
                                        command.ExecuteNonQuery();
                                    }
                                }
                            }
                            transaction.Commit();
                            btnctrl("back");
                            FillData("", "", "");
                            DataTable dt3 = dataGridView1.DataSource as DataTable;
                            foreach (DataRow row in dt3.Rows)
                            {
                                int SelectedIndex = dt3.Rows.IndexOf(row);
                                if (oldrow == row["Ref No."].ToString().Trim())
                                {
                                    dataGridView1.CurrentCell = dataGridView1.Rows[SelectedIndex].Cells["Ref No."];
                                    chgdview();
                                    break;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show("Haven't Been inserted!", "Warning",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                   
                }
            }
        }

        private void dprint_Click(object sender, EventArgs e)
        {
            pcvrlet();
        }

        private void dcancel_Click(object sender, EventArgs e)
        {
            cancel();
        }

        private void dexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void XBTN_Click(object sender, EventArgs e)
        {
            reqcinv tempform = new reqcinv(this);
            tempform.GenPayee = faddrsee.Text;
            tempform.query();
            tempform.ShowDialog();
        }

        private void btnaddr_Click(object sender, EventArgs e)
        {
            string fmodule = tcvrdet.Rows[tcvrdet.CurrentCell.RowIndex].Cells["fmodule"].Value.ToString();
            if (fmodule == "D0200")
            {
                faddrsee.Text = tcvrdet.Rows[tcvrdet.CurrentCell.RowIndex].Cells["fpayee"].Value.ToString();
                string vftype, vfid, lc_faddr="";
                vftype = tcvrdet.Rows[tcvrdet.CurrentCell.RowIndex].Cells["fdbtrtype"].Value.ToString();
                vfid = tcvrdet.Rows[tcvrdet.CurrentCell.RowIndex].Cells["fdbtr"].Value.ToString();

                string sql = "select * from " + Dbm + " mprdr where fdesc ='" + Fct.stFat(faddrsee.Text) + "'";
                DataTable dt = DBHelper.GetDataSet(sql);
                if (dt.Rows.Count > 0)
                {
                    lc_faddr = dt.Rows[0]["fadd1"].ToString().Trim().Replace("\r\n", "") + dt.Rows[0]["fadd2"].ToString().Trim().Replace("\r\n", "") + dt.Rows[0]["fadd3"].ToString().Trim().Replace("\r\n", "") + dt.Rows[0]["fadd4"].ToString().Trim().Replace("\r\n", "");
                }
                faddr_t.Text = lc_faddr;
            }
            else { 
                string vftype, vfid, lc_faddrsee, lc_faddr="";
	            vftype = tcvrdet.Rows[tcvrdet.CurrentCell.RowIndex].Cells["fdbtrtype"].Value.ToString();
	            vfid   = tcvrdet.Rows[tcvrdet.CurrentCell.RowIndex].Cells["fdbtr"].Value.ToString();

                string sql ="select * from "+Dbm +" mprdr where fid ='"+ vfid +"'";
                DataTable dt = DBHelper .GetDataSet(sql);
	            lc_faddrsee = dt.Rows[0]["fdesc"].ToString().Trim();
	            lc_faddrsee = lc_faddrsee.Replace("\r\n","");
                if (dt.Rows.Count > 0)
                {
                    lc_faddr = dt.Rows[0]["fadd1"].ToString().Trim().Replace("\r\n", "") + dt.Rows[0]["fadd2"].ToString().Trim().Replace("\r\n", "") + dt.Rows[0]["fadd3"].ToString().Trim().Replace("\r\n", "") + dt.Rows[0]["fadd4"].ToString().Trim().Replace("\r\n", "");
                }
	            faddrsee.Text = lc_faddrsee;
	            faddr_t.Text  = lc_faddr;
            }
        }

        private void flogseq_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tcvrdet.CurrentCell != null)
                {
                    tcvrdet["flogseq", tcvrdet.CurrentCell.RowIndex].Value = flogseq.Text.ToString();
                }
            }
        }

        private void fyrref_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tcvrdet.CurrentCell != null)
                {
                    tcvrdet["fyrref", tcvrdet.CurrentCell.RowIndex].Value = fyrref.Text.ToString();
                }
            }
        }

        private void fremark_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tcvrdet.CurrentCell != null)
                {
                    tcvrdet["fremark", tcvrdet.CurrentCell.RowIndex].Value = fremark.Text.ToString();
                }
            }
        }

        private void forref_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tcvrdet.CurrentCell != null)
                {
                    tcvrdet["forref", tcvrdet.CurrentCell.RowIndex].Value = forref.Text.ToString();
                }
            }
        }

        private void fdesc1_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tcvrdet.CurrentCell != null)
                {
                    tcvrdet["fdesc1", tcvrdet.CurrentCell.RowIndex].Value = fdesc1.Text.ToString();
                }
            }
        }

        private void fdesc2_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tcvrdet.CurrentCell != null)
                {
                    tcvrdet["fdesc2", tcvrdet.CurrentCell.RowIndex].Value = fdesc2.Text.ToString();
                }
            }
        }

        private void famount1_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tcvrdet.CurrentCell != null)
                {
                    tcvrdet["famount1", tcvrdet.CurrentCell.RowIndex].Value = Fct.sdFat(famount1.Text.ToString());
                }
                totalcontrol();
            }
        }

        private void famount2_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tcvrdet.CurrentCell != null)
                {
                    tcvrdet["famount2", tcvrdet.CurrentCell.RowIndex].Value = Fct.sdFat(famount2.Text.ToString());
                }
                totalcontrol();
            }
        }

        void totalcontrol()
        {
            Decimal famounts = 0;
            for (int i = 0; i < tcvrdet.Rows.Count; i++)
            {
                if (tcvrdet["famount1", i].Value.ToString() != "")
                {
                    famounts = famounts + Convert.ToDecimal(tcvrdet["famount1", i].Value);
                }
                if (tcvrdet["famount2", i].Value.ToString() != "")
                {
                    famounts = famounts + Convert.ToDecimal(tcvrdet["famount2", i].Value);
                }
            }
            ftotamt.Text = famounts.ToString("n2");
        }

        public string[] save()
        {
            DateTime time = DateTime.Now;
            if (Addflag == true)
            {
                ArrayList addsql = new ArrayList();
                string user = InsEnvironment.LoginUser.GetUserCode();
                string fcinpname = InsEnvironment.LoginUser.GetChineseName();
                string date = time.ToString("yyyy-MM-dd");
                string fctlid_ocvrlet = Fct.NewId("ocvrlet");
                string fctlid_ocvrdet = Fct.NewId("ocvrdet");
                string ocvrlet_sql = "INSERT INTO [dbo].[ocvrlet]([fctlid],[frefno],[fdocdate],[fchkno],[faddrsee],[faddr],[fattn],[ftotamt],[finpuser],[finpdate],[fupduser],[fupddate]) VALUES( "+
                                     "'" + fctlid_ocvrlet + "','" + Fct.stFat(frefno.Text) + "','" + date + "','" + Fct.stFat(fchkno.Text) + "','" + Fct.stFat(faddrsee.Text) + "','" + Fct.stFat(faddr_t.Text) + "','" + Fct.stFat(fattn.Text) + "','" + Fct.sdFat(ftotamt.Text) + "','" + user + "','" + date + "','" + user + "','" + date + "')";
                addsql.Add(ocvrlet_sql);

                string sqlocvrlet = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_ocvrlet) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='ocvrlet'";
                addsql.Add(sqlocvrlet);

                DataTable dt = tcvrdet.DataSource as DataTable;
                if (dt != null && dt.Rows.Count > 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        if (i != 0) { fctlid_ocvrdet = (int.Parse(fctlid_ocvrdet) + 1).ToString().PadLeft(10, '0'); }
                        string insertsql = "INSERT INTO [dbo].[ocvrdet]([fctlid_lt],[fctlid],[flogseq],[fmodule],[finvno],[fyrref],[forref],[fremark],[fdesc1],[fdesc2],[famount1],[famount2],[fdbtrtype],[fdbtr],[fpayee])  VALUES( "+
                                            "'" + fctlid_ocvrlet + "','" + fctlid_ocvrdet + "'," + Fct.snFat(dt.Rows[i]["flogseq"]) + ",'" + Fct.stFat(dt.Rows[i]["fmodule"]) + "','" + Fct.stFat(dt.Rows[i]["finvno"]) + "','" + Fct.stFat(dt.Rows[i]["fyrref"]) + "','" + Fct.stFat(dt.Rows[i]["forref"]) + "','" + Fct.stFat(dt.Rows[i]["fremark"]) + "','" + Fct.stFat(dt.Rows[i]["fdesc1"]) + "','" + Fct.stFat(dt.Rows[i]["fdesc2"]) + "','" + Fct.sdFat(dt.Rows[i]["famount1"]) + "','" + Fct.sdFat(dt.Rows[i]["famount2"]) + "','" + Fct.stFat(dt.Rows[i]["fdbtrtype"]) + "','" + Fct.stFat(dt.Rows[i]["fdbtr"]) + "','" + Fct.stFat(dt.Rows[i]["fpayee"]) + "')";
                        addsql.Add(insertsql);
                    }
                    string sqlfctlid = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_ocvrdet) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='ocvrdet'";
                    addsql.Add(sqlfctlid);
                }
                return (string[])addsql.ToArray(typeof(string));
            }
            else if (Updateflag == true)
            {
                ArrayList updatesql = new ArrayList();
                string user = InsEnvironment.LoginUser.GetUserCode();
                string date = time.ToString("yyyy-MM-dd");
                string ocvrlet_sql = "UPDATE [dbo].[ocvrlet] SET [frefno] = '" + Fct.stFat(frefno.Text) + "',[fdocdate] = convert(datetime,'" + Fct.dateFat(fdocdate.Text.Trim()) + "',120),[fchkno] = '" + Fct.stFat(fchkno.Text) + "',[faddrsee] = '" + Fct.stFat(faddrsee.Text) + "',[faddr] = '" + Fct.stFat(faddr_t.Text) + "',[fattn] = '" + Fct.stFat(fattn.Text) + "',[ftotamt] = '" + Fct.sdFat(ftotamt.Text) + "',[fupduser] = '" + user + "',[fupddate] = '" + date + "' WHERE fctlid ='"+ fctlid +"'";
                updatesql.Add(ocvrlet_sql);
                string sql = "SELECT flogseq,finvno,fyrref,forref,fremark,famount1,famount2,fdesc1,fdesc2,fdbtrtype,fdbtr,fpayee,fmodule,fctlid from ocvrdet where fctlid_lt='" + fctlid + "' ";
                DataTable dtOrg = DBHelper.GetDataSet(sql);
                DataTable dt = tcvrdet.DataSource as DataTable;
                if (dt != null)
                {
                    updatesql.AddRange(CompareDt(dtOrg, dt, "fctlid"));
                }
                return (string[])updatesql.ToArray(typeof(string));
            }
            else { return null; }
        }

        public string[] CompareDt(DataTable dt1, DataTable dt2, string keyField)
        {
            //为三个表拷贝表结构
            DataTable dtRetDel = dt1.Clone();
            DataTable dtRetAdd = dt1.Clone();
            DataTable dtRetDif = dt1.Clone();

            ArrayList updsql = new ArrayList();
            int colCount = dt1.Columns.Count;

            DataView dv1 = dt1.DefaultView;
            DataView dv2 = dt2.DefaultView;

            //先以第一个表为参照，看第二个表是修改了还是删除了
            foreach (DataRowView dr1 in dv1)
            {
                dv2.RowFilter = keyField + " = '" + dr1[keyField].ToString() + "'";
                if (dv2.Count > 0)
                {
                    if (!CompareUpdate(dr1, dv2[0]))//比较是否有不同的
                    {
                        dtRetDif.Rows.Add(dv2[0].Row.ItemArray);//修改后
                        continue;
                    }
                }
                else
                {
                    //已经被删除的
                    dtRetDel.Rows.Add(dr1.Row.ItemArray);
                }
            }

            //以第一个表为参照，看记录是否是新增的
            dv2.RowFilter = "";//清空条件
            foreach (DataRowView dr2 in dv2)
            {
                dv1.RowFilter = keyField + " = '" + dr2[keyField].ToString() + "'";
                if (dv1.Count == 0)
                {
                    //新增的
                    dtRetAdd.Rows.Add(dr2.Row.ItemArray);
                }
            }

            if (dtRetAdd != null && dtRetAdd.Rows.Count > 0)
            {
                string fctlid_ocvrdet = Fct.NewId("ocvrdet");
                for (int i = 0; i < dtRetAdd.Rows.Count; i++)
                {
                    if (i != 0) { fctlid_ocvrdet = (int.Parse(fctlid_ocvrdet) + 1).ToString().PadLeft(10, '0'); }
                    //decimal famounts = Fct.sdFat(dtRetAdd.Rows[i]["famount"]);
                    string insertsql = "INSERT INTO [dbo].[ocvrdet]([fctlid_lt],[fctlid],[flogseq],[fmodule],[finvno],[fyrref],[forref],[fremark],[fdesc1],[fdesc2],[famount1],[famount2],[fdbtrtype],[fdbtr],[fpayee])  VALUES( "+
                    "'" + fctlid + "','" + fctlid_ocvrdet + "'," + Fct.snFat(dtRetAdd.Rows[i]["flogseq"]) + ",'" + Fct.stFat(dtRetAdd.Rows[i]["fmodule"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["finvno"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fyrref"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["forref"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fremark"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fdesc1"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fdesc2"]) + "','" + Fct.sdFat(dtRetAdd.Rows[i]["famount1"]) + "','" + Fct.sdFat(dtRetAdd.Rows[i]["famount2"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fdbtrtype"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fdbtr"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fpayee"]) + "')";
                    updsql.Add(insertsql);
                }
                string sqlfctlid = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_ocvrdet) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='ocvrdet'";
                updsql.Add(sqlfctlid);
            }
            if (dtRetDif != null && dtRetDif.Rows.Count > 0)
            {
                for (int i = 0; i < dtRetDif.Rows.Count; i++)
                {
                    string update = "UPDATE [dbo].[ocvrdet] SET [flogseq] = '" + dtRetDif.Rows[i]["flogseq"].ToString().Trim() + "',[fmodule] = '" + dtRetDif.Rows[i]["fmodule"].ToString().Trim() + "',[finvno] = '" + Fct.stFat(dtRetDif.Rows[i]["finvno"])+ "',[fyrref] = '" + Fct.stFat(dtRetDif.Rows[i]["fyrref"]) + "',[forref] = '" + Fct.stFat(dtRetDif.Rows[i]["forref"]) + "',[fremark] = '" + Fct.stFat(dtRetDif.Rows[i]["fremark"]) + "',[fdesc1] = '" + Fct.stFat(dtRetDif.Rows[i]["fdesc1"]) + "',[fdesc2] = '" + Fct.stFat(dtRetDif.Rows[i]["fdesc2"]) + "',[famount1] = '" + dtRetDif.Rows[i]["famount1"].ToString().Trim() + "',[famount2] = '" + dtRetDif.Rows[i]["famount2"].ToString().Trim() + "',[fdbtrtype] = '" + dtRetDif.Rows[i]["fdbtrtype"].ToString().Trim() + "',[fdbtr] = '" + dtRetDif.Rows[i]["fdbtr"].ToString().Trim() + "',[fpayee] = '" + Fct.stFat(dtRetDif.Rows[i]["fpayee"]) + "' where fctlid='" + dtRetDif.Rows[i]["fctlid"].ToString().Trim() + "'";
                    updsql.Add(update);
                }
            }

            if (dtRetDel != null && dtRetDel.Rows.Count > 0)
            {
                for (int i = 0; i < dtRetDel.Rows.Count; i++)
                {
                    string del = "delete from ocvrdet where fctlid='" + dtRetDel.Rows[i]["fctlid"].ToString().Trim() + "'";
                    updsql.Add(del);
                }
            }

            return (string[])updsql.ToArray(typeof(string));
        }

        //比较是否有不同的
        private static bool CompareUpdate(DataRowView dr1, DataRowView dr2)
        {
            //行里只要有一项不一样，整个行就不一样,无需比较其它
            object val1;
            object val2;
            for (int i = 0; i < dr1.Row.ItemArray.Length; i++)
            {
                val1 = dr1[i];
                val2 = dr2[i];
                if (!val1.Equals(val2))
                {
                    return false;
                }
            }
            return true;
        }

        public void tinvlist(string fctlid) {
            string sql = "select * from opyinvh where fctlid ='"+ fctlid +"'";
            DataTable dt = DBHelper.GetDataSet(sql);
            if (dt.Rows.Count > 0)
            {
                if (tcvrdet["finvno", tcvrdet.CurrentCell.RowIndex].Value.ToString() != dt.Rows[0]["finvno"].ToString())
                {
                    string lc_result, lc_fpayee;

                    lc_fpayee = dt.Rows[0]["fpayee"].ToString().Trim().Replace("/r/n", "");

                    string ld_flosdate = dt.Rows[0]["flosdate"].ToString().Trim();
                    DateTime b = Convert.ToDateTime(dt.Rows[0]["flosdate"].ToString().Trim());
                    ld_flosdate = b.ToString("dd MMM yyyy");

                    string lc_flosdate = ld_flosdate.Substring(8, 2) + ' ' + ld_flosdate.Substring(5, 2) + ' ' + ld_flosdate.Substring(0, 4);

                    string tcpay = "select * from ocpay where fctlid ='" + dt.Rows[0]["fctlid_py"].ToString().Trim() + "'";
                    DataTable ocpay = DBHelper.GetDataSet(tcpay);
                    tcvrdet["fdesc1", tcvrdet.CurrentCell.RowIndex].Value = ocpay.Rows[0]["fnatdesc"].ToString().Trim();

                    tcvrdet["fmodule", tcvrdet.CurrentCell.RowIndex].Value = dt.Rows[0]["fmodule"].ToString().Trim();
                    tcvrdet["finvno", tcvrdet.CurrentCell.RowIndex].Value = dt.Rows[0]["finvno"].ToString().Trim();
                    if (dt.Rows[0]["fmodule"].ToString().Trim() == "D0200")
                    {
                        tcvrdet["fyrref", tcvrdet.CurrentCell.RowIndex].Value = ocpay.Rows[0]["fpayref"].ToString().Trim();
                    }
                    else { tcvrdet["fyrref", tcvrdet.CurrentCell.RowIndex].Value = dt.Rows[0]["fcedeclm"].ToString().Trim(); }
                    tcvrdet["forref", tcvrdet.CurrentCell.RowIndex].Value = dt.Rows[0]["fclmno"].ToString().Trim();
                    tcvrdet["famount1", tcvrdet.CurrentCell.RowIndex].Value = -Fct.sdFat(dt.Rows[0]["famount"].ToString().Trim());
                    tcvrdet["famount2", tcvrdet.CurrentCell.RowIndex].Value = 0;
                    tcvrdet["fdbtrtype", tcvrdet.CurrentCell.RowIndex].Value = dt.Rows[0]["fdbtrtype"].ToString().Trim();
                    tcvrdet["fdbtr", tcvrdet.CurrentCell.RowIndex].Value = dt.Rows[0]["fdbtr"].ToString().Trim();
                    tcvrdet["fpayee", tcvrdet.CurrentCell.RowIndex].Value = dt.Rows[0]["fpayee"].ToString();

                    faddrsee.Text = dt.Rows[0]["fpayee"].ToString();
                    string tceci = "select * from oceci where fctlid_1 ='" + dt.Rows[0]["fctlid_c"].ToString().Trim() + "'";
                    DataTable oceci = DBHelper.GetDataSet(tceci);
                    string title = ""; string fempyee = "";
                    if (oceci.Rows.Count > 0)
                    {
                        fempyee = oceci.Rows[0]["fempyee"].ToString().Trim(); 
                        if (oceci.Rows[0]["fsex"].ToString().Trim() == "1") { title = "Mr. "; }
                        else { title = "Ms. "; }
                    }
                    
                    tcvrdet["fremark", tcvrdet.CurrentCell.RowIndex].Value = Fct.stFat("Accident to " + title + fempyee + " on " + ld_flosdate);
                    chg();
                    totalcontrol();
                }
            }
        }

        private void frefno_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (frefno.Text.ToString().Trim() == "")
                {
                    MessageBox.Show("Empty value is not allowed!");
                    frefno.Focus();
                }
            }
        }

        void pcvrlet()
        {
            DataSet ds = new DataSet();
            string sql = "select fctlid_lt,flogseq,finvno,fyrref,case when fyrref ='' then 'N' else 'Y' end as pyrref,forref, " +
                        "case when forref ='' then 'N' else 'Y' end as porref,fremark,fdesc1,famount1, " +
                        "case when famount1 =0 then 'N' else 'Y' end as pfamt1,fdesc2,famount2, " +
                        "case when famount2 =0 then 'N' else 'Y' end as pfamt2 from ocvrdet where fctlid_lt ='" + fctlid + "'";
            DataTable dtocvrdet = DBHelper.GetDataSet(sql);
            ds.Tables.Add(dtocvrdet);

            string sql1 = "select * from ocvrlet where fctlid ='" + fctlid + "'";
            DataTable dtocvrlet = DBHelper.GetDataSet(sql1);
            ds.Tables.Add(dtocvrlet);

            ReportDocument cryRpt = new ReportDocument();
            cryRpt.Load(rpDirectory + "\\pcvrlet.rpt");
            cryRpt.SetDataSource(dtocvrlet);
            cryRpt.Subreports["cvrdet"].SetDataSource(dtocvrdet);
            cryDocViewer temp_form = new cryDocViewer(cryRpt);
            temp_form.ShowDialog();
        }



    }
}

