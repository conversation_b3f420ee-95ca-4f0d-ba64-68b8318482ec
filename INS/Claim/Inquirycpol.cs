using INS.Business.Inquiry;
using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business
{
    public partial class Inquirycpol : Form
    {
        public string fclass = "",flag = "" , fbus = "", fctlid_p;
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        private Inquirylist inquirylist;
        private Claim.tClaim tClaim;
        private Claim.rClaim rClaim;
        private Claim.tclmeci tclmeci;
        private Claim.rclmeci rclmeci;
        private Ctrl.Claim.riREG riREG;
        private Ctrl.Claim.REG rEG;
        public Inquirycpol()
        {
            InitializeComponent();
            InitializeSubclassName();
        }

        public Inquirycpol(Inquirylist inquirylist)
        {
            // TODO: Complete member initialization
            this.inquirylist = inquirylist;
            InitializeComponent();
            InitializeSubclassName();
        }

        public Inquirycpol(Claim.tClaim tClaim)
        {
            // TODO: Complete member initialization
            this.tClaim = tClaim;
            InitializeComponent();
            InitializeSubclassName();
        }

        public Inquirycpol(Claim.rClaim rClaim)
        {
            // TODO: Complete member initialization
            this.rClaim = rClaim;
            InitializeComponent();
            InitializeSubclassName();
        }

        public Inquirycpol(Claim.tclmeci tclmeci)
        {
            // TODO: Complete member initialization
            this.tclmeci = tclmeci;
            InitializeComponent();
            InitializeSubclassName();
        }

        public Inquirycpol(Claim.rclmeci rclmeci)
        {
            // TODO: Complete member initialization
            this.rclmeci = rclmeci;
            InitializeComponent();
            InitializeSubclassName();
        }

        public Inquirycpol(Ctrl.Claim.riREG riREG)
        {
            // TODO: Complete member initialization
            this.riREG = riREG;
            InitializeComponent();
            InitializeSubclassName();
        }

        public Inquirycpol(Ctrl.Claim.REG rEG)
        {
            // TODO: Complete member initialization
            this.rEG = rEG;
            InitializeComponent();
            InitializeSubclassName();
        }

        public void InitializeSubclassName()
        {
            DataTable dt = new DataTable();
            string sql = "SELECT 'ALL' as fid union select RTRIM(a.fid) as fid from " + Dbm + "msinsc a join " + Dbm + "minsc b on a.fctlid_1=b.fctlid and b.fid ='" + fclass + "'";
            dt = DBHelper.GetDataSet(sql);
            this.subclass.DisplayMember = "fid";
            this.subclass.ValueMember = "fid";
            this.subclass.DataSource = dt;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            string sqladd = "";
            foreach (Control ctrl in panel1.Controls)
            {
                if (ctrl is TextBox)
                {
                    if (((TextBox)ctrl).Text==""){
                        sqladd = "";
                    }
                }
                if (ctrl is MaskedTextBox)
                {
                    if (((MaskedTextBox)ctrl).Text == "")
                    {
                        sqladd = "";
                    }
                }
            }
            sqladd = "And fbus='" + fbus + "'";
            if (fpolno.Text != "") {
                sqladd = "And fpolno = '" + fpolno.Text.Trim() + "' ";
            }
            if (fuwyr.Text != "") {
                sqladd = sqladd + "And fuwyr = '" + fuwyr.Text.Trim() + "' ";
            }
            if (subclass.SelectedValue.ToString().Trim() != "ALL")
            {
                sqladd = sqladd + "And fsclass = '" + subclass.SelectedValue + "' ";
            }
            if (fprdr.Text != "")
            {
                sqladd = sqladd + "And fprdr = '" + fprdr.Text.Trim() + "' ";
            }
            if (fclnt.Text != "")
            {
                sqladd = sqladd + "And fclnt = '" + fclnt.Text.Trim() + "' ";
            }
            if (finsd.Text != "")
            {
                sqladd = sqladd + "And finsd = '" + finsd.Text.Trim() + "' ";
            }
            if (fempyr.Text != "")
            {
                sqladd = sqladd + "And fempyr = '" + fempyr.Text.Trim() + "' ";
            }
            if (fcontrt.Text != "")
            {
                sqladd = sqladd + "And fcontrt = '" + fcontrt.Text.Trim() + "' ";
            }
            if (fsite.Text != "")
            {
                sqladd = sqladd + "And fsite = '" + fsite.Text.Trim() + "' ";
            }
            if (flag == "tclaim")
            {
                Inquirylist tempform = new Inquirylist(this);
                tempform.flag = "tclaim";
                tempform.fclass = fclass;
                tempform.FillData("", "", sqladd);
                tempform.ShowDialog();
                tClaim.fctlid_p = fctlid_p;
                
            }
            if (flag == "rclaim")
            {
                Inquirylist tempform = new Inquirylist(this);
                tempform.flag = "rclaim";
                tempform.fclass = fclass;
                tempform.FillData("", "", sqladd);
                tempform.ShowDialog();
                rClaim.fctlid_p = fctlid_p;
            }
            if (flag == "tclmeci")
            {
                Inquirylist tempform = new Inquirylist(this);
                tempform.flag = "tclmeci";
                tempform.fclass = fclass;
                tempform.FillData("", "", sqladd);
                tempform.ShowDialog();
                tclmeci.fctlid_p = fctlid_p;
            }
            if (flag == "rclmeci")
            {
                Inquirylist tempform = new Inquirylist(this);
                tempform.flag = "rclmeci";
                tempform.fclass = fclass;
                tempform.FillData("", "", sqladd);
                tempform.ShowDialog();
                rclmeci.fctlid_p = fctlid_p;
            }
            this.Close();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (flag == "tclaim")
            { tClaim.fctlid_p = ""; }
            if (flag == "rclaim")
            { rClaim.fctlid_p = ""; }
            if (flag == "tclmeci")
            { tclmeci.fctlid_p = ""; }
            this.Close();
        }

        private void Inquirycpol_Resize(object sender, EventArgs e)
        {
            if (WindowState == FormWindowState.Maximized)
            {
                //最大化时所需的操作 
                this.ClientSize = new System.Drawing.Size(755, 348);
                this.WindowState = FormWindowState.Normal;
            }
            else if (WindowState == FormWindowState.Minimized)
            {
                //最小化时所需的操作

                this.ClientSize = new System.Drawing.Size(50, 50);
                this.WindowState = FormWindowState.Normal;
            } 
        }


    }
}
