using INS.Business;
using INS.Business.Report;
using INS.Business.SearchForm;
using INS.Ctrl;
using INS.Ctrl.Claim;
using INS.Ctrl.EecClaim;
using INS.INSClass;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Claim
{
    public partial class rclmeci : Form
    {
        public string ConfirmRlt = "", fclass = "", fbus = "", fctlid = "", fstatus = "", fctlid_p = "", fmode = "";
        public Boolean Addflag = false;
        public Boolean Updateflag = false;
        riEciREG PG_REG = new riEciREG();
        REINSR PG_REINSR = new REINSR();
        riEciAMD PG_AMD = new riEciAMD();
        ADJ PG_ADJ = new ADJ();
        PAY PG_PAY = new PAY();
        SUM PG_SUM = new SUM();
        EciClmdet PG_Clmdet = new EciClmdet();
        private Inquirylist inquirylist;
        private Business.Inquiry.DirectClaimInquiry directClaimInquiry;

        public rclmeci()
        {
            InitializeComponent();
        }

        public rclmeci(Inquirylist inquirylist)
        {
            // TODO: Complete member initialization
            this.inquirylist = inquirylist;
            InitializeComponent();
        }

        public rclmeci(Business.Inquiry.DirectClaimInquiry directClaimInquiry)
        {
            // TODO: Complete member initialization
            this.directClaimInquiry = directClaimInquiry;
            InitializeComponent();
        }

        public void FillData(string order)
        {
            String Sql = "select fctlid,fclmno,faccdate,frepdate,flosdate,fpolno,finsd, " +
                        "case when fstatus='1' then 'Pending' else  " +
                        "case when fstatus='2' then 'Hold' else " +
                        "case when fstatus='3' then 'Confirmed' else 'Cancelled' end end end as fstatus," +
                        "case when fmode='1' then 'Open' else  " +
                        "case when fmode='2' then 'Recovery' else 'Closed' end end as fmode from oclaim " +
                        "where fbus='" + fbus + "' AND fclass ='" + fclass + "' ";
            if (order == "order by System.Data.DataRowView#") { Sql = Sql + "order by case when fstatus=1 then '1' when fstatus='2' then '2' when fstatus='4' then '4' else '3' END,fclmno desc,fpolno desc"; }
            else
            {
                if (order.Contains("order"))
                {
                    Sql = Sql + order;
                }
                else
                {
                    Sql = Sql + order;
                    Sql = Sql + "order by case when fstatus=1 then '1' when fstatus='2' then '2' when fstatus='4' then '4' else '3' END,fclmno desc,fpolno desc ";
                }
            }
            Claimlist.DataSource = DBHelper.GetDataSet(Sql);
            Claimlist.Columns[0].Visible = false;
            Claimlist.CellFormatting +=
            new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.Claimlist_CellFormatting);
            if (Claimlist.Rows.Count == 0)
            {
                list_btn.Controls.Clear();
                list_btn.Controls.Add(buttonload("Load", "Page1"));
            }
        }

        private void Claimlist_CellFormatting(object sender, System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            Claimlist.Columns["fclmno"].HeaderText = "Claim No.";
            Claimlist.Columns["faccdate"].HeaderText = "A/C Date";
            Claimlist.Columns["frepdate"].HeaderText = "Report Date";
            Claimlist.Columns["flosdate"].HeaderText = "Loss Date";
            Claimlist.Columns["fpolno"].HeaderText = "Policy No.";
            Claimlist.Columns["finsd"].HeaderText = "Insured";
            Claimlist.Columns["fstatus"].HeaderText = "Status";
            Claimlist.Columns["fmode"].HeaderText = "Mode";
            if (Claimlist.Columns[e.ColumnIndex].Name.Equals("finsd"))
            {
                Claimlist.Columns[e.ColumnIndex].Width = 180;
            }
            if (Claimlist.Columns[e.ColumnIndex].Name.Equals("faccdate") || Claimlist.Columns[e.ColumnIndex].Name.Equals("frepdate") || Claimlist.Columns[e.ColumnIndex].Name.Equals("flosdate"))
            {
                Claimlist.Columns[e.ColumnIndex].Width = 80;
            }
        }

        void claimlistchg() {
            try
            {
                int Claimlistrow = 0;
                if (Claimlist.CurrentCell != null)
                {
                    Claimlistrow = Claimlist.CurrentCell.RowIndex;
                }
                else { return; }
                if (Claimlist.Rows[Claimlistrow].Cells["fctlid"].Value != null)
                {
                    if (Claimlist.Rows[Claimlistrow].Cells["fctlid"].Value.ToString().Length != 0)
                    {
                        fctlid = Claimlist.Rows[Claimlistrow].Cells["fctlid"].Value.ToString();
                    }
                }
                if (Claimlist.Rows[Claimlistrow].Cells["fstatus"].Value != null)
                {
                    if (Claimlist.Rows[Claimlistrow].Cells["fstatus"].Value.ToString().Length != 0)
                    {
                        fstatus = Claimlist.Rows[Claimlistrow].Cells["fstatus"].Value.ToString();
                    }
                }
                if (Claimlist.Rows[Claimlistrow].Cells["fmode"].Value != null)
                {
                    if (Claimlist.Rows[Claimlistrow].Cells["fmode"].Value.ToString().Length != 0)
                    {
                        fmode = Claimlist.Rows[Claimlistrow].Cells["fmode"].Value.ToString();
                    }
                }
                tabpageReload();
            }
            catch { }
        }

        private void Claimlist_CurrentCellChanged(object sender, EventArgs e)
        {
            claimlistchg();
        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            if (Addflag == true)
            {
                if (e.TabPage == tabPage1 || e.TabPage == tabPage4 || e.TabPage == tabPage5 || e.TabPage == tabPage6 || e.TabPage == tabPage7)
                    e.Cancel = true;
            }
            if (Updateflag == true) {
                e.Cancel = true;
            }   
        }

        private void tabControl1_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (Addflag == true || Updateflag == true)
            {
                if (tabControl1.SelectedIndex == 1)
                { }
                if (tabControl1.SelectedIndex == 2)
                { }
                if (tabControl1.SelectedIndex == 3)
                { }
                if (tabControl1.SelectedIndex == 4)
                { }
                if (tabControl1.SelectedIndex == 5)
                { }
                if (tabControl1.SelectedIndex == 6)
                { }
            }
            else {
                if (tabControl1.SelectedIndex == 1)
                { PG_REG.tpReload(fctlid); }
                if (tabControl1.SelectedIndex == 5)
                {
                    adj_btn.Controls.Clear();
                    adj_btn.Controls.Add(buttonload("Load", "tabPage5"));
                }
                if (tabControl1.SelectedIndex == 6)
                {
                    pay_btn.Controls.Clear();
                    pay_btn.Controls.Add(buttonload("Load", "tabPage6"));
                }
            }
        }

        public Control buttonload(string flag, string page)
        {
            ClaimButton temp_ctrlbutton = new ClaimButton();
            temp_ctrlbutton.fconfirm = fstatus;
            temp_ctrlbutton.fmode = fmode;
            temp_ctrlbutton.fctlid = fctlid;
            if (flag == "Load") { temp_ctrlbutton.buttonload("", page); }
            if (flag == "Add") { temp_ctrlbutton.buttoncontroladd(page); }
            if (flag == "Back") { temp_ctrlbutton.buttoncontrolback(page); }
            if (flag == "Mod") { temp_ctrlbutton.buttoncontrolupdate(page); }
            if (flag == "Save") { temp_ctrlbutton.buttoncontrolsaveback(page); }
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(add_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancel_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(del_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exit_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modify_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(save_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(conf_Click);
            temp_ctrlbutton.UserControlButtonPrintClicked += new EventHandler(printattch_Click);
            
            return temp_ctrlbutton;
        }

        void tabpageReload()
        {
            Addflag = false;
            Updateflag = false;
            list_btn.Controls.Clear();
            list_btn.Controls.Add(buttonload("Load", "tabPage1"));
            reg_btn.Controls.Clear();
            reg_btn.Controls.Add(buttonload("Load", "tabPage2"));
            reg_ct.Controls.Clear();
            PG_REG.fclass = fclass;
            PG_REG.fbus = fbus;
            PG_REG.tpReload(fctlid);
            reg_ct.Controls.Add(PG_REG);

            clmdet_btn.Controls.Clear();
            clmdet_btn.Controls.Add(buttonload("Load", "tabPage8"));
            clmdet_ct.Controls.Clear();
            PG_Clmdet.fclass = fclass;
            PG_Clmdet.fbus = fbus;
            PG_Clmdet.tpReload(fctlid);
            clmdet_ct.Controls.Add(PG_Clmdet);

            reinsr_btn.Controls.Clear();
            reinsr_btn.Controls.Add(buttonload("Load", "tabPage3"));
            reinsr_ct.Controls.Clear();
            PG_REINSR.fclass = fclass;
            PG_REINSR.fbus = fbus;
            PG_REINSR.tpReload(fctlid);
            reinsr_ct.Controls.Add(PG_REINSR);

            amd_btn.Controls.Clear();
            amd_btn.Controls.Add(buttonload("Load", "tabPage4"));
            amd_ct.Controls.Clear();
            PG_AMD.fclass = fclass;
            PG_AMD.fbus = fbus;
            PG_AMD.tpReload(fctlid);
            amd_ct.Controls.Add(PG_AMD);

            adj_btn.Controls.Clear();
            adj_btn.Controls.Add(buttonload("Load", "tabPage5"));
            adj_ct.Controls.Clear();
            PG_ADJ.fclass = fclass;
            PG_ADJ.fbus = fbus;
            PG_ADJ.tpReload(fctlid);
            adj_ct.Controls.Add(PG_ADJ);

            pay_btn.Controls.Clear();
            pay_btn.Controls.Add(buttonload("Load", "tabPage6"));
            pay_ct.Controls.Clear();
            PG_PAY.fclass = fclass;
            PG_PAY.fbus = fbus;
            PG_PAY.tpReload(fctlid);
            pay_ct.Controls.Add(PG_PAY);

            sum_btn.Controls.Clear();
            sum_btn.Controls.Add(buttonload("Load", "tabPage7"));
            sum_ct.Controls.Clear();
            PG_SUM.fclass = fclass;
            PG_SUM.fbus = fbus;
            PG_SUM.tpReload(fctlid);
            sum_ct.Controls.Add(PG_SUM);

        }

        public void tabpageAdd()
        {
            Addflag = true;
            tabControl1.SelectedTab = tabPage2;
            reg_btn.Controls.Clear();
            reg_btn.Controls.Add(buttonload("Add", "tabPage2"));
            reg_ct.Controls.Clear();
            PG_REG.fclass = fclass;
            PG_REG.fbus = fbus;
            PG_REG.tpAdd(fctlid_p);
            reg_ct.Controls.Add(PG_REG);

            reinsr_btn.Controls.Clear();
            reinsr_btn.Controls.Add(buttonload("Add", "tabPage3"));
            reinsr_ct.Controls.Clear();
            PG_REINSR.fclass = fclass;
            PG_REINSR.fbus = fbus;
            PG_REINSR.tpAdd(fctlid_p);
            reinsr_ct.Controls.Add(PG_REINSR);

            PG_REG.setControlFocus("flosdate");
        }

        private void add_Click(object sender, EventArgs e)
        {
            Inquirycpol tempform = new Inquirycpol(this);
            tempform.flag = "rclmeci";
            tempform.fbus = fbus;
            tempform.fclass = fclass;
            tempform.InitializeSubclassName();
            tempform.ShowDialog();
            if (fctlid_p != "") { tabpageAdd(); }
        }

        public void tabpageUpdate()
        {
            Updateflag = true;
            string page = tabControl1.SelectedTab.Name;
            if (page == "tabPage2" || (page == "tabPage3" && fstatus != "Confirmed"))
            {
                tabControl1.SelectedTab = tabPage2;
                reg_btn.Controls.Clear();
                reg_btn.Controls.Add(buttonload("Mod", page));
                reg_ct.Controls.Clear();
                PG_REG.fclass = fclass;
                PG_REG.fbus = fbus;
                PG_REG.tpUpdate(fctlid);
                reg_ct.Controls.Add(PG_REG);

                reinsr_btn.Controls.Clear();
                reinsr_btn.Controls.Add(buttonload("Mod", "tabPage3"));
                reinsr_ct.Controls.Clear();
                PG_REINSR.fclass = fclass;
                PG_REINSR.fbus = fbus;
                PG_REINSR.tpUpdate(fctlid);
                reinsr_ct.Controls.Add(PG_REINSR);

                PG_REG.setControlFocus("flosdate");
            }
            if (page == "tabPage8")
            {
                tabControl1.SelectedTab = tabPage8;
                clmdet_btn.Controls.Clear();
                clmdet_btn.Controls.Add(buttonload("Mod", "tabPage8"));
                clmdet_ct.Controls.Clear();
                PG_Clmdet.fclass = fclass;
                PG_Clmdet.fbus = fbus;
                PG_Clmdet.tpUpdate(fctlid);
                clmdet_ct.Controls.Add(PG_Clmdet);
            }
            if (page == "tabPage3")
            {
                tabControl1.SelectedTab = tabPage3;
                reinsr_btn.Controls.Clear();
                reinsr_btn.Controls.Add(buttonload("Mod", page));
                reinsr_ct.Controls.Clear();
                PG_REINSR.fclass = fclass;
                PG_REINSR.fbus = fbus;
                PG_REINSR.tpUpdate(fctlid);
                reinsr_ct.Controls.Add(PG_REINSR);
            }
            if (page == "tabPage4")
            {
                tabControl1.SelectedTab = tabPage4;
                amd_btn.Controls.Clear();
                amd_btn.Controls.Add(buttonload("Mod", page));
                amd_ct.Controls.Clear();
                PG_AMD.fclass = fclass;
                PG_AMD.fbus = fbus;
                PG_AMD.tpUpdate(fctlid);
                amd_ct.Controls.Add(PG_AMD);
            }
            if (page == "tabPage5")
            {
                tabControl1.SelectedTab = tabPage5;
                adj_btn.Controls.Clear();
                adj_btn.Controls.Add(buttonload("Mod", page));
                adj_ct.Controls.Clear();
                PG_ADJ.fclass = fclass;
                PG_ADJ.fbus = fbus;
                PG_ADJ.tpUpdate(fctlid);
                adj_ct.Controls.Add(PG_ADJ);
            }
            if (page == "tabPage6")
            {
                tabControl1.SelectedTab = tabPage6;
                pay_btn.Controls.Clear();
                pay_btn.Controls.Add(buttonload("Mod", page));
                pay_ct.Controls.Clear();
                PG_PAY.fclass = fclass;
                PG_PAY.fbus = fbus;
                PG_PAY.tpUpdate(fctlid);
                pay_ct.Controls.Add(PG_PAY);
            }
        }

        private void modify_Click(object sender, EventArgs e)
        {
            tabpageUpdate();
        }

        public Boolean Validation()
        {
            string page = tabControl1.SelectedTab.Name;
            string ctrlName = "";
            ctrlName = PG_REG.u_validation();
            if (ctrlName != "")
            {
                tabControl1.SelectedIndex = 1;
                PG_REG.setControlFocus(ctrlName);
                return false;
            }
            ctrlName = PG_REINSR.u_valreinsr();
            if (ctrlName != "")
            {
                tabControl1.SelectedIndex = 2;
                PG_REINSR.setControlFocus(ctrlName);
                return false;
            }
            if (page == "tabPage5")
            {
                ctrlName = PG_ADJ.u_valadj();
                if (ctrlName != "")
                {
                    tabControl1.SelectedIndex = 4;
                    PG_ADJ.setControlFocus(ctrlName);
                    return false;
                }
            }
            if (page == "tabPage6")
            {
                ctrlName = PG_PAY.u_valpay("");
                if (ctrlName != "")
                {
                    tabControl1.SelectedIndex = 5;
                    PG_PAY.setControlFocus(ctrlName);
                    return false;
                }
            }
            return true;
        }

        public string tabpageSave()
        {
            string result = ""; Boolean chk3 = true, chk4 = true, chk1 = true, chk2 = true;
            string page = tabControl1.SelectedTab.Name;
            //Boolean chk1 = PG_REG.u_validation();
            //Boolean chk2 = PG_REINSR.u_valreinsr();
            //if (page == "tabPage5")
            //{
            //    chk3 = PG_ADJ.u_valadj();
            //}
            //if (page == "tabPage6")
            //{
            //    chk4 = PG_PAY.u_valpay("");
            //}
            if (chk1 && chk2 && chk3 && chk4)
            {
                string[] sql2 = null, sql3 = null, sql4 = null, sql5 = null, sql6 = null, sql8 = null;
                if (Addflag == true) {
                    if (page == "tabPage2" || page == "tabPage3") { sql2 = PG_REG.tabpage2save(fctlid_p); }
                    string newfctlid = PG_REG.newfctlid;
                    if (page == "tabPage2" || page == "tabPage3") { sql3 = PG_REINSR.tabpage3save(newfctlid); }
                }
                if (Updateflag == true) {                
                    if (page == "tabPage2" || page == "tabPage3") { sql2 = PG_REG.tabpage2save(fctlid); }
                    if (page == "tabPage2" || page == "tabPage3") { sql3 = PG_REINSR.tabpage3save(fctlid); }
                    if (page == "tabPage4") { sql4 = PG_AMD.tabpage4save(fctlid); }
                    if (page == "tabPage5") { sql5 = PG_ADJ.tabpage5save(fctlid); }
                    if (page == "tabPage6") { sql6 = PG_PAY.tabpage6save(fctlid); }
                    if (page == "tabPage8") { sql8 = PG_Clmdet.tabpage8save(fctlid); } 
                }
                string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
                result = ExecuteSqlTransaction(connectionString, sql2, sql3, sql4, sql5, sql6, sql8);
                if (result == "OK")
                {
                    if (Addflag == true && sql2 == null) { }
                    else if (Addflag == true)
                    {
                        //int row =0;
                        //if (Claimlist.CurrentCell != null) 
                        //{ row = Claimlist.CurrentCell.RowIndex; }
                        FillData("");
                        //Claimlist.CurrentCell = Claimlist.Rows[row].Cells["fclmno"];
                    }
                    tabpageReload();
                }
                else
                {
                    MessageBox.Show(result, "Warning",
                       MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            } 
            return result;
        }

        private void save_Click(object sender, EventArgs e)
        {
            string result = tabpageSave();

            if (Addflag == true)
            {
                if (result == "OK")
                {
                    MessageBox.Show("Have Been Inserted!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("Haven't Been Inserted!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            if (Updateflag == true)
            {
                if (result == "OK")
                {
                    MessageBox.Show("Have Been Updated!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("Haven't Been Updated!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private void conf_Click(object sender, EventArgs e)
        {
            confclm tempform = new confclm(this);
            tempform.fctlid = fctlid;
            tempform.page = tabControl1.SelectedTab.Name;
            tempform.flag = "rclmeci";
            tempform.Control();
            tempform.ShowDialog();
            if (ConfirmRlt != "No")
            {
                FillData("");
                tabControl1.SelectedTab = tabPage2;
                DataTable dt3 = Claimlist.DataSource as DataTable;
                foreach (DataRow row in dt3.Rows)
                {
                    int SelectedIndex = dt3.Rows.IndexOf(row);
                    if (ConfirmRlt == row["fctlid"].ToString().Trim())
                    {
                        Claimlist.CurrentCell = Claimlist.Rows[SelectedIndex].Cells["Policy#"];
                        claimlistchg();
                        break;
                    }
                }
            }
        }

        void delRecord() {
            ArrayList delarr = new ArrayList(); bool isSuccuss = false;
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the Policy All information?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                delarr.Add("delete from oclaim where fctlid='" + fctlid + "'");
                delarr.Add("delete from ocfac where fctlid_1='" + fctlid + "'");
                delarr.Add("delete from oclaim_a where fctlid_1='" + fctlid + "'");
                delarr.Add("delete from oclaim_s where fctlid_1='" + fctlid + "'");
                delarr.Add("delete from ocabk_oe where fctlid_c='" + fctlid + "'");
                delarr.Add("delete from ocabk_oc where fctlid_c='" + fctlid + "'");
                delarr.Add("delete from ocabk_e where fctlid_c='" + fctlid + "'");
                delarr.Add("delete from ocabk_c where fctlid_c='" + fctlid + "'");
                delarr.Add("delete from ocadj where fctlid_1='" + fctlid + "'");
                delarr.Add("delete from ocadjfac where fctlid_1='" + fctlid + "'");
                delarr.Add("delete from ocpay where fctlid_1='" + fctlid + "'");
                delarr.Add("delete from ocpayfac where fctlid_1='" + fctlid + "'");
                delarr.Add("delete from ocpbk_e where fctlid_c='" + fctlid + "'");
                delarr.Add("delete from ocpbk_c where fctlid_c='" + fctlid + "'");
                isSuccuss = DBHelper.ExecuteSqlTransaction((string[])delarr.ToArray(typeof(string)));
                if (isSuccuss == true)
                {
                    MessageBox.Show("Have Been Deleted", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("Haven't Been Deleted", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }  
        }

        private void del_Click(object sender, EventArgs e)
        {
            delRecord();
            FillData("");
            tabpageReload();
        }

        private void cancel_Click(object sender, EventArgs e)
        {
            Addflag = false;
            Updateflag = false;
            tabpageReload();
        }

        private void exit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void printattch_Click(object sender, EventArgs e)
        {
            pclaim tempform = new pclaim(this);
            tempform.fclass = fclass;
            tempform.fctlid_1 = fctlid;
            if (tabControl1.SelectedIndex == 1) { tempform.p_rpttype = "clmcvr"; }
            if (tabControl1.SelectedIndex == 3)
            {
                tempform.p_rpttype = "clmamd";
                tempform.fctlid = PG_AMD.Selectfctlid;
            }
            if (tabControl1.SelectedIndex == 4)
            {
                tempform.p_rpttype = "clmadj";
                tempform.fctlid = PG_ADJ.Selectfctlid;
            }
            if (tabControl1.SelectedIndex == 5)
            {
                tempform.p_rpttype = "clmpay";
                tempform.fctlid = PG_PAY.Selectfctlid;
                tempform.ln_flogseq = PG_PAY.Selectflogseq;
                tempform.ld_faccdate = PG_PAY.Selectfaccdate;
            }
            tempform.fbus = fbus;
            tempform.conctrl();
            tempform.ShowDialog();
        }

        private string ExecuteSqlTransaction(string connectionString, string[] sql2, string[] sql3, string[] sql4, string[] sql5, string[] sql6, string[] sql8)
        {
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                SqlCommand command = connection.CreateCommand();
                SqlTransaction transaction;
                transaction = connection.BeginTransaction("SampleTransaction");
                command.Connection = connection;
                command.Transaction = transaction;

                try
                {
                    if (sql2 != null)
                    {
                        for (int i = 0; i < sql2.Length; i++)
                        {
                            if (sql2[i] != "" && sql2[i] != null && sql2[i].Length != 10 && sql2[i].Length > 1)
                            {
                                command.CommandText = sql2[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (sql3 != null)
                    {
                        for (int i = 0; i < sql3.Length; i++)
                        {
                            if (sql3[i] != "" && sql3[i] != null)
                            {
                                command.CommandText = sql3[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (sql4 != null)
                    {
                        for (int i = 0; i < sql4.Length; i++)
                        {
                            if (sql4[i] != "" && sql4[i] != null)
                            {
                                command.CommandText = sql4[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (sql5 != null)
                    {
                        for (int i = 0; i < sql5.Length; i++)
                        {
                            if (sql5[i] != "" && sql5[i] != null)
                            {
                                command.CommandText = sql5[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (sql6 != null)
                    {
                        for (int i = 0; i < sql6.Length; i++)
                        {
                            if (sql6[i] != "" && sql6[i] != null)
                            {
                                command.CommandText = sql6[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    if (sql8 != null)
                    {
                        for (int i = 0; i < sql8.Length; i++)
                        {
                            if (sql8[i] != "" && sql8[i] != null)
                            {
                                command.CommandText = sql8[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    transaction.Commit();
                    return "OK";
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                    Console.WriteLine("  Message: {0}", ex.Message);

                    try
                    {
                        transaction.Rollback();
                        return "RollBack";
                    }
                    catch (Exception ex2)
                    {
                        Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                        Console.WriteLine("  Message: {0}", ex2.Message);
                        return ex2.Message;
                    }
                }
            }
        }

        private void ReQuery_Click(object sender, EventArgs e)
        {
            inqclm tempform = new inqclm(this);
            tempform.Class = fclass;
            tempform.Text = "Inquiry - Employees' Compensation";
            tempform.flag = "rclmeci";
            tempform.InitializeSubclassName();
            tempform.ShowDialog();
        }

        private void rclmeci_Resize(object sender, EventArgs e)
        {
            if (WindowState == FormWindowState.Maximized)
            {
                //最大化时所需的操作 
                this.ClientSize = new System.Drawing.Size(920, 714);
                this.WindowState = FormWindowState.Normal;
            }
            else if (WindowState == FormWindowState.Minimized)
            {
                //最小化时所需的操作

                this.ClientSize = new System.Drawing.Size(50, 50);
                this.WindowState = FormWindowState.Normal;
            } 
        }




    }
}
