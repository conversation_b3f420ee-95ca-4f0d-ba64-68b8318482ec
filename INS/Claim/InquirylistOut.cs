using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;
using INS.Claim;
using System.Collections;

namespace INS
{
    public partial class InquirylistOut : Form
    {
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public InquirylistOut()
        {
            InitializeComponent();
            FillData("", "","");
        }

        public InquirylistOut(tclmeci tclmeci)
        {
            // TODO: Complete member initialization
            this.tclmeci = tclmeci;
            InitializeComponent();
        }

        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "", fclass="";
        private tclmeci tclmeci;

        public void FillData(string order, string query, string sqladd)
        {
            dataGridView1.Focus();
            string Sql = "select b.fpolno as Policy#, b.fendtno as [Endt No.],b.finsd as [Insured],b.fvseq as [V#],b.fvarno as [VO],b.fctlid, a.fctlid as ID, a.flosdate "+
                         "from ECO.dbo.oclaim a left join polh b on a.fpolno = b.fpolno and a.fendtno = b.fendtno where b.fclass ='EEC' and fconfirm ='3' and (a.finput <> 1 or a.finput is null) ORDER BY fctlid";
            dataGridView1.DataSource = DBHelper.GetDataSet(Sql);
            this.dataGridView1.Columns["fctlid"].Visible = false;
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null) { row = dataGridView1.CurrentCell.RowIndex; }
                else { return; }
                if (dataGridView1.Rows[row].Cells["Policy#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Policy#"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["Policy#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["fctlid"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["fctlid"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["fctlid"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["ID"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["ID"].Value.ToString().Length != 0)
                    {
                        label4.Text = dataGridView1.Rows[row].Cells["ID"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), textBox1.Text, "");
        }

        private void Query_Click(object sender, EventArgs e)
        {  
            ArrayList addsql = new ArrayList();
            DateTime time = DateTime.Now;
            bool isSuccuss = false;

            string fctlid_clm = Fct.NewId("OCLAIM");
            string fctlid_oceci = Fct.NewId("oceci");

            string updatesql = "INSERT INTO [dbo].[oclaim] "+
                                "select '" + fctlid_clm + "' as fctlid, a.fctlid_p as fctlid_p,a.fctlid as fctlid_e,rtrim(a.fbus) as fbus,'1' as fcoins, " +
                                "rtrim(a.fclass) as fclass,'' as fclmno,GETDATE() as faccdate,  "+
                                "GETDATE() as frepdate,h.flosdate as flosdate, "+
                                "case when '.  .' ='.  .' then null else convert(datetime,'.  .',102) end as ffrm2date, "+
                                "rtrim(a.fpolno) as fpolno,a.fvseq,a.fvarno,rtrim(a.fendtno) as fendtno,rtrim(a.fsrcref) as fsrcref, "+
                                "'' as fcedepol,rtrim(a.fcedeendt) as fcedeendt,'' as fsrcclm,'' as fcedeclm, rtrim(a.fstype) as fstype, "+
                                "rtrim(a.fuwyr) as fuwyr,rtrim(a.fsclass) as fsclass,'' as fsec,'' as fcover,'' as frno,rtrim(a.ftrade) as ftrade, "+
                                "rtrim(a.ftrdesc) as ftrdesc,rtrim(a.fprof) as fprof,rtrim(a.fprfdesc) as fprfdesc,a.fprdr as fprdr, "+
                                "a.fclnt as fclnt,'' as fsclnt,'' as fleader,'' as fsiteid, a.fsite as fsite, "+
                                "a.finsd as finsd,a.finsd_t1 as finsd_t1, "+
                                "rtrim(a.finsd_t2) as finsd_t2,'0' as fitem,h.flosloc as flosloc, "+
                                "'' as fpartr_t1,'' as fpartr_t2,'' as fnatlos,'' as fadjr1, '' as fadjr2,'' as fsolr1,'' as fsolr2,  "+
                                "case when '.  .'='.  .' then null else convert(datetime,'.  .',102) end as frcrdate, "+
                                "case when '.  .' ='.  .' then null else convert(datetime,'.  .',102) end as frcrto, '' as fhlhc, "+
                                "a.fincfr as fincfr,a.fincto as fincto,a.fmainten as fmainten,a.fmntfr as fmntfr,a.fmntto as fmntto,  "+
                                "a.fsicur,a.frate_b,a.fsish,a.fliabsh, '0' as fshare,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0 "+
                                ",0,0,0,0,0,0,0,0,0,0, 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1 as fstatus,1 as fmode,2 as fdestory, "+
                                "null as fdesdate,'100.0000',0,0,0,'100.0000','' as fttycode, '' as fttysec,'' as ffacbcode,'' as ffacbsec,'' as fxolcode, "+
                                "'' as fxollayr,2 as fxol,2 as ffacnp,0 as ffacnplyr,2 as fdstat, '' as fdstdesc,2 as fcontri,'1' as fcontype, "+
                                "0,0,2 as fconsetl,'Immediate Advice' as fplaword,'Debit Note and supporting document' as fpayword,'Credit Note and supporting document' as frcyword,2 as ftobemod, 2 as fstage,'', "+
                                "'" + InsEnvironment.LoginUser.GetUserCode() + "' as finpuser,'" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' as finpdate,'" + InsEnvironment.LoginUser.GetUserCode() + "' as fupduser, "+
                                "'" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' as fupddate,'" + InsEnvironment.LoginUser.GetUserCode() + "' as fcnfuser,'" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' as fcnfdate from polh a  "+
                                "left join (select fid,fdesc from " + Dbm + "mprdr where ftype='P') b on a.fprdr=b.fid  " +
                                "left join (select fid,fdesc from " + Dbm + "mprdr where ftype='C') c on a.fclnt=c.fid  " +
                                "left join (select fid,fdesc from " + Dbm + "mprdr where ftype='R') f on a.fleader=f.fid  " +
                                "left join (select fid,fdesc from " + Dbm + "msparam where fctlid_1='0000000001') d on a.ftrade=d.fid  " +
                                "left join (select fid,fdesc from " + Dbm + "msparam where fctlid_1='0000000002') e on a.fprof=e.fid " +
                                "left join ECO.dbo.oclaim h on a.fpolno = h.fpolno and a.fendtno = h.fendtno "+
                                "where a.fctlid='" + label3.Text + "' and h.fctlid = '" + label4.Text + "'";
            addsql.Add(updatesql);

            string oclaimsqlupd = "update " + Dbm + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='OCLAIM'";
            addsql.Add(oclaimsqlupd);

            string ocecisql = "INSERT INTO [dbo].[oceci] "+
                                "select b.fctlid_p as fctlid_p,b.fctlid as fctlid_e,'" + fctlid_clm + "' as fctlid_1,'" + fctlid_oceci + "' as fctlid,'1' as fno, b.fclass,b.fsclass,'' as fclmno, " +
                                "b.fpolno,b.fendtno,a.fscontr,a.fimempyr,a.fempyee,a.fempyee_c,a.fid,a.fdob,a.fage,case when a.fsex ='m' then '1' else '2' end as fsex, "+
                                "fjobn,a.foccup,a.foccup_c, "+
                                "a.fclmamt,a.fsalary,a.fdays,a.fpdsh,a.fl01,a.fl02,a.fl03,a.fl04,a.fl05,a.fl06,a.fl07,a.fl08,a.fl09,a.fl10,a.fl11,a.fl12, "+
                                "a.fl13,a.fl14,a.fl15,a.fl16,a.fl17,a.fl18,a.fl19,a.fl20,a.fdesc20,a.fl21,a.fl22,a.fl23,a.fl24,a.fl25,a.fl26,a.fl31,a.fl32, "+
                                "a.fl33,a.fl34,a.fl35,a.fl36,a.fl41,a.fl42,a.fl43,a.fl44,a.fl45,a.fl46,a.fl51,a.fl52,a.fl53,a.fl54,a.fl55,a.fl56,a.fl61,a.fdesc61, "+
                                "a.facctype,a.fheight,a.faccdesc "+
                                "from ECO.dbo.[oceci] a "+
                                "left join polh b on a.fpolno = b.fpolno and a.fendtno = b.fendtno  " +
                                "where b.fclass ='EEC' and fconfirm ='3' and a.fctlid ='" + label4.Text + "'";
            addsql.Add(ocecisql);

            string ocecisqlupd = "update " + Dbm + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='oceci'";
            addsql.Add(ocecisqlupd);

            string oceciupd = "update ECO.dbo.oclaim set finput =1 where fctlid ='" + label4.Text + "'";
            addsql.Add(oceciupd);

            isSuccuss = DBHelper.ExecuteSqlTransaction((string[])addsql.ToArray(typeof(string)));
            if (isSuccuss == true)
            {
                MessageBox.Show("Have Been Inputed", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("Haven't Been Inputed", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
           tclmeci.FillData("");
           this.Close();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            tclmeci.fctlid_p = "";
            this.Close();
        }

        private void Inquirylist_Resize(object sender, EventArgs e)
        {
            if (WindowState == FormWindowState.Maximized)
            {
                //最大化时所需的操作 
                this.ClientSize = new System.Drawing.Size(747, 265);
                this.WindowState = FormWindowState.Normal;
            }
            else if (WindowState == FormWindowState.Minimized)
            {
                //最小化时所需的操作

                this.ClientSize = new System.Drawing.Size(50, 50);
                this.WindowState = FormWindowState.Normal;
            } 
        }

        

    }
}
