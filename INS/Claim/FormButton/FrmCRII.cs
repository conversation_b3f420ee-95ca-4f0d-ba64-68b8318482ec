using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Claim
{
    public partial class FrmCRII : Form
    {
        DBConnect operate = new DBConnect();
        public FrmCRII()
        {
            InitializeComponent();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            string name = btn.Name;
            SetValue(name);
            rClaim temp_form = new rClaim();
            temp_form.fbus = "R";
            temp_form.fclass = name;
            temp_form.Text = "Claims Registration - " + InsEnvironment.Minsc.GetFdesc() + "";
            temp_form.FillData("");
            temp_form.ShowDialog();
        }

        void SetValue(string fid)
        {
            String sql = "select * from minsc where fid='" + fid + "'";
            DataTable dt = operate.GetTable(sql);
            if (dt.Rows.Count > 0)
            {
                InsEnvironment.Minsc.SetFctlid(dt.Rows[0]["fctlid"].ToString().Trim());
                InsEnvironment.Minsc.SetFid(dt.Rows[0]["fid"].ToString().Trim());
                InsEnvironment.Minsc.SetFacclass(dt.Rows[0]["facclass"].ToString().Trim());
                InsEnvironment.Minsc.SetFdesc(dt.Rows[0]["fdesc"].ToString().Trim());
                InsEnvironment.Minsc.SetFcdesc(dt.Rows[0]["fcdesc"].ToString().Trim());
            }
        }

        private void EEC_Click(object sender, EventArgs e)
        {
            Button btn = (Button)sender;
            string name = btn.Name;
            SetValue(name);
            rclmeci temp_form = new rclmeci();
            temp_form.fbus = "R";
            temp_form.fclass = name;
            temp_form.Text = "Claims Registration - " + InsEnvironment.Minsc.GetFdesc() + "";
            temp_form.FillData("");
            temp_form.ShowDialog();
        }
    }
}
