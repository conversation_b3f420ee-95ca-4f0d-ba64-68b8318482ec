using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;
using INS.Claim;

namespace INS
{
    public partial class Inquirylist : Form
    {
        public Inquirylist()
        {
            InitializeComponent();
            FillData("", "","");
        }

        public Inquirylist(Inquirycpol inquirycpol)
        {
            // TODO: Complete member initialization
            this.inquirycpol = inquirycpol;
            InitializeComponent();
        }

        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "", fclass="";
        private Inquirycpol inquirycpol;

        public void FillData(string order, string query, string sqladd)
        {
            inquirycpol.Close();
            dataGridView1.Focus();
            string Sql = "";
            if (flag == "tclaim" || flag == "tclmeci")
            {
                if (order == "" && query == "")
                {
                    Sql = "select fpolno as Policy#, fendtno as [Endt No.],finsd as [Insured],fvseq as [V#],fvarno as [VO],fctlid from polh where fclass ='" + fclass + "' and fbus <> 'R' and fconfirm ='3' " + sqladd + " ORDER BY fctlid";
                }
                else if (order != "")
                {
                    Sql = "select fpolno as Policy#, fendtno as [Endt No.],finsd as [Insured],fvseq as [V#],fvarno as [VO],fctlid from polh where fclass ='" + fclass + "' and fbus <> 'R' and fconfirm ='3' and fpolno = '" + query.Trim() + "' " + sqladd + " ORDER BY '" + order + "'";
                }
                else if (query != "")
                {
                    Sql = "select fpolno as Policy#, fendtno as [Endt No.],finsd as [Insured],fvseq as [V#],fvarno as [VO],fctlid from polh where fclass ='" + fclass + "' and fbus <> 'R' and fconfirm ='3' and fdesc = '" + query.Trim() + "'  " + sqladd + "";
                }
                else
                {
                    Sql = "select fpolno as Policy#, fendtno as [Endt No.],finsd as [Insured],fvseq as [V#],fvarno as [VO],fctlid from polh where fclass ='" + fclass + "' and fbus <> 'R' and fconfirm ='3' and fdesc = '" + query.Trim() + "'  " + sqladd + " order by fctlid";
                }
            }
            else {
                if (order == "" && query == "")
                {
                    Sql = "select fpolno as Policy#, fendtno as [Endt No.],finsd as [Insured],fvseq as [V#],fvarno as [VO],fctlid from polh where fclass ='" + fclass + "' and fconfirm ='3' " + sqladd + " ORDER BY fctlid";
                }
                else if (order != "")
                {
                    Sql = "select fpolno as Policy#, fendtno as [Endt No.],finsd as [Insured],fvseq as [V#],fvarno as [VO],fctlid from polh where fclass ='" + fclass + "' and fconfirm ='3' and fpolno = '" + query.Trim() + "' " + sqladd + " ORDER BY '" + order + "'";
                }
                else if (query != "")
                {
                    Sql = "select fpolno as Policy#, fendtno as [Endt No.],finsd as [Insured],fvseq as [V#],fvarno as [VO],fctlid from polh where fclass ='" + fclass + "' and fconfirm ='3' and fdesc = '" + query.Trim() + "'  " + sqladd + "";
                }
                else
                {
                    Sql = "select fpolno as Policy#, fendtno as [Endt No.],finsd as [Insured],fvseq as [V#],fvarno as [VO],fctlid from polh where fclass ='" + fclass + "' and fconfirm ='3' and fdesc = '" + query.Trim() + "'  " + sqladd + " order by fctlid";
                }
            }
           
            dataGridView1.DataSource = DBHelper.GetDataSet(Sql);
            this.dataGridView1.Columns["fctlid"].Visible = false;
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null) { row = dataGridView1.CurrentCell.RowIndex; }
                else { return; }
                if (dataGridView1.Rows[row].Cells["Policy#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Policy#"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["Policy#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["fctlid"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["fctlid"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["fctlid"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), textBox1.Text, "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            inquirycpol.fctlid_p = label3.Text.ToString();
            this.Close();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            inquirycpol.fctlid_p = "";
            this.Close();
        }

        private void Inquirylist_Resize(object sender, EventArgs e)
        {
            if (WindowState == FormWindowState.Maximized)
            {
                //最大化时所需的操作 
                this.ClientSize = new System.Drawing.Size(747, 265);
                this.WindowState = FormWindowState.Normal;
            }
            else if (WindowState == FormWindowState.Minimized)
            {
                //最小化时所需的操作

                this.ClientSize = new System.Drawing.Size(50, 50);
                this.WindowState = FormWindowState.Normal;
            } 
        }

        

    }
}
