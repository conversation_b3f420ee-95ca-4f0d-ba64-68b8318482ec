<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{CD3FA6B9-3CCB-4198-948F-30C4F5520363}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>INS</RootNamespace>
    <AssemblyName>INS</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <TargetFrameworkProfile />
    <PublishUrl>M:\Software II\New INS Runtime\Setup\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <AutorunEnabled>true</AutorunEnabled>
    <ApplicationRevision>456</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <CreateDesktopShortcut>true</CreateDesktopShortcut>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>082A9C0E8D1A908BC430E0A1B4637824A2C1A4F4</ManifestCertificateThumbprint>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>INS_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>true</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>false</SignManifests>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject>INS.Program</StartupObject>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>Properties\app.manifest</ApplicationManifest>
  </PropertyGroup>
  <PropertyGroup>
    <TargetZone>LocalIntranet</TargetZone>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>ooopic_0.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AxShockwaveFlashObjects, Version=1.0.0.0, Culture=neutral, PublicKeyToken=692fbea5521e1304" />
    <Reference Include="BouncyCastle.Crypto, Version=1.8.9.0, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>..\packages\Portable.BouncyCastle.1.8.9\lib\net40\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="Common.Logging, Version=3.4.1.0, Culture=neutral, PublicKeyToken=af08829b84f0328e, processorArchitecture=MSIL">
      <HintPath>..\packages\Common.Logging.3.4.1\lib\net40\Common.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Common.Logging.Core, Version=3.4.1.0, Culture=neutral, PublicKeyToken=af08829b84f0328e, processorArchitecture=MSIL">
      <HintPath>..\packages\Common.Logging.Core.3.4.1\lib\net40\Common.Logging.Core.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.CrystalReports.Engine, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\CrystalDecisions.CrystalReports.Engine.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.ReportAppServer.DataSetConversion, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\CrystalDecisions.ReportAppServer.DataSetConversion.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.ReportSource, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\CrystalDecisions.ReportSource.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.Shared, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\CrystalDecisions.Shared.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.Windows.Forms, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\CrystalDecisions.Windows.Forms.dll</HintPath>
    </Reference>
    <Reference Include="EnvDTE, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="FlashControlV71, Version=1.0.3187.32366, Culture=neutral, PublicKeyToken=692fbea5521e1304" />
    <Reference Include="itext.barcodes, Version=7.1.15.0, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.7.1.15\lib\net45\itext.barcodes.dll</HintPath>
    </Reference>
    <Reference Include="itext.forms, Version=7.1.15.0, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.7.1.15\lib\net45\itext.forms.dll</HintPath>
    </Reference>
    <Reference Include="itext.io, Version=7.1.15.0, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.7.1.15\lib\net45\itext.io.dll</HintPath>
    </Reference>
    <Reference Include="itext.kernel, Version=7.1.15.0, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.7.1.15\lib\net45\itext.kernel.dll</HintPath>
    </Reference>
    <Reference Include="itext.layout, Version=7.1.15.0, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.7.1.15\lib\net45\itext.layout.dll</HintPath>
    </Reference>
    <Reference Include="itext.pdfa, Version=7.1.15.0, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.7.1.15\lib\net45\itext.pdfa.dll</HintPath>
    </Reference>
    <Reference Include="itext.sign, Version=7.1.15.0, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.7.1.15\lib\net45\itext.sign.dll</HintPath>
    </Reference>
    <Reference Include="itext.styledxmlparser, Version=7.1.15.0, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.7.1.15\lib\net45\itext.styledxmlparser.dll</HintPath>
    </Reference>
    <Reference Include="itext.svg, Version=7.1.15.0, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.7.1.15\lib\net45\itext.svg.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp, Version=5.5.13.2, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\iTextSharp.5.5.13.2\lib\itextsharp.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Excel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Word, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Office.Interop.Word.15.0.4797.1003\lib\net20\Microsoft.Office.Interop.Word.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Microsoft.VisualBasic.PowerPacks.Vs, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="Newtonsoft.Json, Version=9.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.9.0.1\lib\net40\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="RestSharp, Version=106.6.10.0, Culture=neutral, PublicKeyToken=598062e77f915f75, processorArchitecture=MSIL">
      <HintPath>..\packages\RestSharp.106.6.10\lib\net452\RestSharp.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="StylesSheetManager, Version=1.0.5291.17688, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\StylesSheetManager.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <HintPath>C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.6.1\System.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.7\lib\net45\System.Net.Http.Formatting.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.7\lib\net45\System.Web.Http.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.7\lib\net45\System.Web.Http.WebHost.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Account\acds.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>acds.xsd</DependentUpon>
    </Compile>
    <Compile Include="Account\acvoucher.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>acvoucher.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Account\ctrl\acadj.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Account\ctrl\acadj.Designer.cs">
      <DependentUpon>acadj.cs</DependentUpon>
    </Compile>
    <Compile Include="Account\ctrl\acchk.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Account\ctrl\acchk.Designer.cs">
      <DependentUpon>acchk.cs</DependentUpon>
    </Compile>
    <Compile Include="Account\ctrl\acset.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Account\ctrl\acset.Designer.cs">
      <DependentUpon>acset.cs</DependentUpon>
    </Compile>
    <Compile Include="Account\ctrl\actogl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Account\ctrl\actogl.Designer.cs">
      <DependentUpon>actogl.cs</DependentUpon>
    </Compile>
    <Compile Include="Account\ctrl\btn.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Account\ctrl\btn.Designer.cs">
      <DependentUpon>btn.cs</DependentUpon>
    </Compile>
    <Compile Include="Account\drcrmsc.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>drcrmsc.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Account\FrmAReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Account\FrmAReport.Designer.cs">
      <DependentUpon>FrmAReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Account\pmscinv.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Account\pmscinv.Designer.cs">
      <DependentUpon>pmscinv.cs</DependentUpon>
    </Compile>
    <Compile Include="Account\ppyset.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Account\ppyset.Designer.cs">
      <DependentUpon>ppyset.cs</DependentUpon>
    </Compile>
    <Compile Include="Account\reqginv.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Account\reqginv.Designer.cs">
      <DependentUpon>reqginv.cs</DependentUpon>
    </Compile>
    <Compile Include="Account\reqset.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Account\reqset.Designer.cs">
      <DependentUpon>reqset.cs</DependentUpon>
    </Compile>
    <Compile Include="Account\tpyset.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Account\tpyset.Designer.cs">
      <DependentUpon>tpyset.cs</DependentUpon>
    </Compile>
    <Compile Include="Account\tginvh.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Account\tginvh.Designer.cs">
      <DependentUpon>tginvh.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\Inquiry\DrCrNoteFacIn.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\Inquiry\DrCrNoteFacIn.Designer.cs">
      <DependentUpon>DrCrNoteFacIn.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\Inquiry\reqinv.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\Inquiry\reqinv.Designer.cs">
      <DependentUpon>reqinv.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\Inquiry\undauto.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\Inquiry\undauto.Designer.cs">
      <DependentUpon>undauto.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\objRpt\cryDocViewer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\objRpt\cryDocViewer.Designer.cs">
      <DependentUpon>cryDocViewer.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\objRpt\cryRptViewer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\objRpt\cryRptViewer.Designer.cs">
      <DependentUpon>cryRptViewer.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\pexpnote.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\pexpnote.designer.cs">
      <DependentUpon>pexpnote.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\prih.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\prih.Designer.cs">
      <DependentUpon>prih.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\InquirylistOut.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\InquirylistOut.Designer.cs">
      <DependentUpon>InquirylistOut.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\Inquirylist.Designer.cs">
      <DependentUpon>Inquirylist.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\Inquiry\reqpyinv.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\Inquiry\reqpyinv.Designer.cs">
      <DependentUpon>reqpyinv.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\objRpt\DsClaim.cs">
      <DependentUpon>DsClaim.xsd</DependentUpon>
    </Compile>
    <Compile Include="Claim\objRpt\RptDataClm.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>RptDataClm.xsd</DependentUpon>
    </Compile>
    <Compile Include="Claim\Report\ClaimI.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\Report\ClaimI.designer.cs">
      <DependentUpon>ClaimI.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\tcvrlet.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\tcvrlet.Designer.cs">
      <DependentUpon>tcvrlet.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Claim\Log.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Claim\Log.Designer.cs">
      <DependentUpon>Log.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\EecClaim\EciLog.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\EecClaim\EciLog.Designer.cs">
      <DependentUpon>EciLog.cs</DependentUpon>
    </Compile>
    <Compile Include="edituser.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="edituser.Designer.cs">
      <DependentUpon>edituser.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\bdwn_cl2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\bdwn_cl2.Designer.cs">
      <DependentUpon>bdwn_cl2.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Browse.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Browse.Designer.cs">
      <DependentUpon>Browse.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\renewopt.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\renewopt.Designer.cs">
      <DependentUpon>renewopt.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\expnote.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\expnote.designer.cs">
      <DependentUpon>expnote.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmset.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmset.Designer.cs">
      <DependentUpon>frmset.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmCodeSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmCodeSearch.Designer.cs">
      <DependentUpon>frmCodeSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmGlacSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmGlacSearch.Designer.cs">
      <DependentUpon>frmGlacSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmfinvno.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmfinvno.Designer.cs">
      <DependentUpon>frmfinvno.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmPayee.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmPayee.Designer.cs">
      <DependentUpon>frmPayee.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\reqcinv.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\reqcinv.Designer.cs">
      <DependentUpon>reqcinv.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\selstat.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\selstat.Designer.cs">
      <DependentUpon>selstat.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\tcpyappr.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\tcpyappr.Designer.cs">
      <DependentUpon>tcpyappr.cs</DependentUpon>
    </Compile>
    <Compile Include="INSClass\AutoSize.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="INSClass\CharPosition.cs" />
    <Compile Include="INSClass\ClaimDataFunc.cs" />
    <Compile Include="INSClass\ClaimInExcel.cs" />
    <Compile Include="INSClass\ClaimInExcel_2.cs" />
    <Compile Include="INSClass\ClaimInExcel_3.cs" />
    <Compile Include="INSClass\CopyFile.cs" />
    <Compile Include="INSClass\FileToByte.cs" />
    <Compile Include="INSClass\GenPolicy.cs" />
    <Compile Include="INSClass\InterMedData_2.cs" />
    <Compile Include="INSClass\JsonOutput.cs" />
    <Compile Include="INSClass\MyProcess.cs" />
    <Compile Include="INSClass\PdfKeywordFinder.cs" />
    <Compile Include="INSClass\PdfRenderListener.cs" />
    <Compile Include="INSClass\pushA8.cs" />
    <Compile Include="INSClass\ReadfileToByte.cs" />
    <Compile Include="INSClass\RestClient.cs" />
    <Compile Include="INSClass\RptInExcel.cs" />
    <Compile Include="INSClass\RptInExcel_2.cs" />
    <Compile Include="INSClass\RptInExcel_3.cs" />
    <Compile Include="INSClass\SendEmail.cs" />
    <Compile Include="INSClass\StringHelper.cs" />
    <Compile Include="INSClass\ToA8.cs" />
    <Compile Include="INSClass\Validation.cs" />
    <Compile Include="INSClass\Word2PDF.cs" />
    <Compile Include="Master\frmLevy.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmLevy.Designer.cs">
      <DependentUpon>frmLevy.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmCoverNote.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmCoverNote.Designer.cs">
      <DependentUpon>frmCoverNote.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\mprdr.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>mprdr.xsd</DependentUpon>
    </Compile>
    <Compile Include="Master\pmast.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\pmast.Designer.cs">
      <DependentUpon>pmast.cs</DependentUpon>
    </Compile>
    <Compile Include="objRpt\cpmsche.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>cpmsche.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Business\objRpt\DocDataSet.cs">
      <DependentUpon>DocDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Business\objRpt\DocDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DocDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="objRpt\drac.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>drac.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\drnote.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>drnote.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\drnote2.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>drnote2.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\drnote3.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>drnote3.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\drnote4.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>drnote4.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\eecni.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>eecni.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\eecsche.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>eecsche.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\expgenl.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>expgenl.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\expnote.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>expnote.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\fgpsche.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>fgpsche.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\mprdr.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>mprdr.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\mypsche.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>mypsche.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\parsche.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>parsche.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\pendt.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>pendt.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\pendtcar.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>pendtcar.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\piische.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>piische.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\pmpci.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>pmpci.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\pmpsche.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>pmpsche.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\reecsche.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>reecsche.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\riapp.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>riapp.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\ribrkdn.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ribrkdn.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\ribrkdn2.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ribrkdn2.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\rparsche.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rparsche.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Business\pendt.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\pendt.Designer.cs">
      <DependentUpon>pendt.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\pfacinv.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\pfacinv.Designer.cs">
      <DependentUpon>pfacinv.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\pinv.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\pinv.Designer.cs">
      <DependentUpon>pinv.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\psche.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\psche.Designer.cs">
      <DependentUpon>psche.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\RIEndt.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\RIEndt.Designer.cs">
      <DependentUpon>RIEndt.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\RIWard.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\RIWard.Designer.cs">
      <DependentUpon>RIWard.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\EndtReinsurance.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\EndtReinsurance.Designer.cs">
      <DependentUpon>EndtReinsurance.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\EndtReinsurancePar.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\EndtReinsurancePar.Designer.cs">
      <DependentUpon>EndtReinsurancePar.cs</DependentUpon>
    </Compile>
    <Compile Include="objRpt\daqcost.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>daqcost.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\eecanly.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>eecanly.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\effpmp.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>effpmp.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\polprmsm.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>polprmsm.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\prmanly.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>prmanly.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\prmbord02.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>prmbord02.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\prmbord05.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>prmbord05.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\prmdrcr.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>prmdrcr.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\prmexpl.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>prmexpl.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\prmfac.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>prmfac.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\prmgrnd.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>prmgrnd.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\prmreg.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>prmreg.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\prmrel.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>prmrel.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\prmstat.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>prmstat.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\prmtty.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>prmtty.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\prmunern.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>prmunern.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\prmuniv.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>prmuniv.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Business\objRpt\RptDataSet.cs">
      <DependentUpon>RptDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Business\objRpt\RptDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>RptDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Business\ReinsurancePar.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\ReinsurancePar.Designer.cs">
      <DependentUpon>ReinsurancePar.cs</DependentUpon>
    </Compile>
    <Compile Include="objRpt\clmadj.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>clmadj.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\clma_eci.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>clma_eci.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\clmcvr.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>clmcvr.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\clmpadv.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>clmpadv.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\clmpay.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>clmpay.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\clmpla.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>clmpla.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\clmp_eci.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>clmp_eci.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Claim\objRpt\DsClaim.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DsClaim.xsd</DependentUpon>
    </Compile>
    <Compile Include="objRpt\pclmappr.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>pclmappr.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\pclmlist.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>pclmlist.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\pclmpayl.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>pclmpayl.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="objRpt\pcvrlet.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>pcvrlet.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Claim\pclaim.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\pclaim.Designer.cs">
      <DependentUpon>pclaim.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\rclmeci.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\rclmeci.Designer.cs">
      <DependentUpon>rclmeci.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\EecClaim\riEciAMD.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\EecClaim\riEciAMD.Designer.cs">
      <DependentUpon>riEciAMD.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\EecClaim\riEciREG.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\EecClaim\riEciREG.Designer.cs">
      <DependentUpon>riEciREG.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\FacIn\EndorRIInsured_PAR.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\FacIn\EndorRIInsured_PAR.Designer.cs">
      <DependentUpon>EndorRIInsured_PAR.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\FacIn\EndorRIInsured_CGL.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\FacIn\EndorRIInsured_CGL.Designer.cs">
      <DependentUpon>EndorRIInsured_CGL.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\FacIn\EndorRIInsured_EEC.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\FacIn\EndorRIInsured_EEC.Designer.cs">
      <DependentUpon>EndorRIInsured_EEC.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\FacIn\EndorRIInsured_CAR.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\FacIn\EndorRIInsured_CAR.Designer.cs">
      <DependentUpon>EndorRIInsured_CAR.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\FacIn\EndorRIGen.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\FacIn\EndorRIGen.Designer.cs">
      <DependentUpon>EndorRIGen.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\FacIn\RIInstall.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\FacIn\RIInstall.Designer.cs">
      <DependentUpon>RIInstall.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\FacIn\RIInsured_PAR.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\FacIn\RIInsured_PAR.Designer.cs">
      <DependentUpon>RIInsured_PAR.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\FacIn\RIInsured_EEC.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\FacIn\RIInsured_EEC.Designer.cs">
      <DependentUpon>RIInsured_EEC.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\FacIn\RIInsured_CPM.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\FacIn\RIInsured_CPM.Designer.cs">
      <DependentUpon>RIInsured_CPM.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\FacIn\RIInsured_CGL.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\FacIn\RIInsured_CGL.Designer.cs">
      <DependentUpon>RIInsured_CGL.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\FacIn\RIInsured_CAR.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\FacIn\RIInsured_CAR.Designer.cs">
      <DependentUpon>RIInsured_CAR.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\RIApp.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\RIApp.Designer.cs">
      <DependentUpon>RIApp.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\Inquiry\DrCrNoteFacOut.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\Inquiry\DrCrNoteFacOut.Designer.cs">
      <DependentUpon>DrCrNoteFacOut.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\Inquiry\DrCrNote.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\Inquiry\DrCrNote.Designer.cs">
      <DependentUpon>DrCrNote.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\Inquiry\COInquiry.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\Inquiry\COInquiry.Designer.cs">
      <DependentUpon>COInquiry.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\Inquiry\RIInquiry.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\Inquiry\RIInquiry.Designer.cs">
      <DependentUpon>RIInquiry.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\Inquiry\DirectInquiry.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\Inquiry\DirectInquiry.Designer.cs">
      <DependentUpon>DirectInquiry.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\Reinsurance.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\Reinsurance.Designer.cs">
      <DependentUpon>Reinsurance.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\Report\PremiumI.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\Report\PremiumI.Designer.cs">
      <DependentUpon>PremiumI.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\Inquiry\inqpol.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\Inquiry\inqpol.Designer.cs">
      <DependentUpon>inqpol.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\Inquiry\inqclm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\Inquiry\inqclm.Designer.cs">
      <DependentUpon>inqclm.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\Inquiry\DirectClaimInquiry.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\Inquiry\DirectClaimInquiry.Designer.cs">
      <DependentUpon>DirectClaimInquiry.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\Inquiry\RIClaimInquiry.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\Inquiry\RIClaimInquiry.Designer.cs">
      <DependentUpon>RIClaimInquiry.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\Inquiry\tpyrinvh.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\Inquiry\tpyrinvh.Designer.cs">
      <DependentUpon>tpyrinvh.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\Inquiry\tpyinvh.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\Inquiry\tpyinvh.Designer.cs">
      <DependentUpon>tpyinvh.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\Inquirylist.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\Inquirycpol.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\Inquirycpol.Designer.cs">
      <DependentUpon>Inquirycpol.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\rClaim.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\rClaim.Designer.cs">
      <DependentUpon>rClaim.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\tclmeci.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\tclmeci.Designer.cs">
      <DependentUpon>tclmeci.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Claim\riAMD.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Claim\riAMD.Designer.cs">
      <DependentUpon>riAMD.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Claim\ClaimButton.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Claim\ClaimButton.Designer.cs">
      <DependentUpon>ClaimButton.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Claim\riREG.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Claim\riREG.Designer.cs">
      <DependentUpon>riREG.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\EecClaim\EciADJ.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\EecClaim\EciADJ.Designer.cs">
      <DependentUpon>EciADJ.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\EecClaim\EciAMD.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\EecClaim\EciAMD.Designer.cs">
      <DependentUpon>EciAMD.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\EecClaim\EciClmdet.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\EecClaim\EciClmdet.Designer.cs">
      <DependentUpon>EciClmdet.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\EecClaim\EciPAY.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\EecClaim\EciPAY.Designer.cs">
      <DependentUpon>EciPAY.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\EecClaim\EciREG.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\EecClaim\EciREG.Designer.cs">
      <DependentUpon>EciREG.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\EecClaim\EciREINSR.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\EecClaim\EciREINSR.Designer.cs">
      <DependentUpon>EciREINSR.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\EecClaim\EciSUM.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\EecClaim\EciSUM.Designer.cs">
      <DependentUpon>EciSUM.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\inqtrn.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\inqtrn.Designer.cs">
      <DependentUpon>inqtrn.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\inqpay.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\inqpay.Designer.cs">
      <DependentUpon>inqpay.cs</DependentUpon>
    </Compile>
    <Compile Include="INSClass\ExportExcel.cs" />
    <Compile Include="INSClass\genadj.cs" />
    <Compile Include="Forms\bdwn_ec2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\bdwn_ec2.Designer.cs">
      <DependentUpon>bdwn_ec2.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\bdwn_sue.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\bdwn_sue.Designer.cs">
      <DependentUpon>bdwn_sue.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\bdwn_suc.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\bdwn_suc.Designer.cs">
      <DependentUpon>bdwn_suc.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\bdwn_pyc.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\bdwn_pyc.Designer.cs">
      <DependentUpon>bdwn_pyc.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\bdwn_cl.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\bdwn_cl.Designer.cs">
      <DependentUpon>bdwn_cl.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\bdwn_pye.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\bdwn_pye.Designer.cs">
      <DependentUpon>bdwn_pye.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\bdwn_ec.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\bdwn_ec.Designer.cs">
      <DependentUpon>bdwn_ec.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\confclm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\confclm.Designer.cs">
      <DependentUpon>confclm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\confpol.cs" />
    <Compile Include="Forms\confpol.Designer.cs">
      <DependentUpon>confpol.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\addendt.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\addendt.Designer.cs">
      <DependentUpon>addendt.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\EndtDirectInsured.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\EndtDirectInsured.Designer.cs">
      <DependentUpon>EndtDirectInsured.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmFabSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmFabSearch.Designer.cs">
      <DependentUpon>frmFabSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmMchSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmMchSearch.Designer.cs">
      <DependentUpon>frmMchSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmNatlosSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmNatlosSearch.Designer.cs">
      <DependentUpon>frmNatlosSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmLocSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmLocSearch.Designer.cs">
      <DependentUpon>frmLocSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmClmdet.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmClmdet.Designer.cs">
      <DependentUpon>frmClmdet.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmTtySearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmTtySearch.Designer.cs">
      <DependentUpon>frmTtySearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmXolSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmXolSearch.Designer.cs">
      <DependentUpon>frmXolSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmCedantSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmCedantSearch.Designer.cs">
      <DependentUpon>frmCedantSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\EndtClauseSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\EndtClauseSearch.Designer.cs">
      <DependentUpon>EndtClauseSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\EndtExcessSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\EndtExcessSearch.Designer.cs">
      <DependentUpon>EndtExcessSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmBrokerSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmBrokerSearch.Designer.cs">
      <DependentUpon>frmBrokerSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmSourceSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmSourceSearch.Designer.cs">
      <DependentUpon>frmSourceSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmReinsurerSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmReinsurerSearch.Designer.cs">
      <DependentUpon>frmReinsurerSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\Endorsement.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\Endorsement.Designer.cs">
      <DependentUpon>Endorsement.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\DirectPolicy.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\DirectPolicy.Designer.cs">
      <DependentUpon>DirectPolicy.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmOccSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmOccSearch.Designer.cs">
      <DependentUpon>frmOccSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmProfessionSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmProfessionSearch.Designer.cs">
      <DependentUpon>frmProfessionSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmInsuredSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmInsuredSearch.Designer.cs">
      <DependentUpon>frmInsuredSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmExcessSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmExcessSearch.Designer.cs">
      <DependentUpon>frmExcessSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmClauseSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmClauseSearch.Designer.cs">
      <DependentUpon>frmClauseSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmBusniessSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmBusniessSearch.Designer.cs">
      <DependentUpon>frmBusniessSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmSiteSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmSiteSearch.Designer.cs">
      <DependentUpon>frmSiteSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmClientSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmClientSearch.Designer.cs">
      <DependentUpon>frmClientSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Others\DContractor.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Others\DContractor.Designer.cs">
      <DependentUpon>DContractor.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\FormButton\FrmBNotes.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\FormButton\FrmBNotes.Designer.cs">
      <DependentUpon>FrmBNotes.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\FormButton\FrmBReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\FormButton\FrmBReport.Designer.cs">
      <DependentUpon>FrmBReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\FormButton\FrmBCOInsur.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\FormButton\FrmBCOInsur.Designer.cs">
      <DependentUpon>FrmBCOInsur.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\FormButton\FrmBRII.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\FormButton\FrmBRII.Designer.cs">
      <DependentUpon>FrmBRII.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmProducerSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmProducerSearch.Designer.cs">
      <DependentUpon>frmProducerSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\geninstall.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\geninstall.Designer.cs">
      <DependentUpon>geninstall.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\selvo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\selvo.Designer.cs">
      <DependentUpon>selvo.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\FormButton\FrmCCOInsur.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\FormButton\FrmCCOInsur.Designer.cs">
      <DependentUpon>FrmCCOInsur.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\FormButton\FrmCContractor.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\FormButton\FrmCContractor.Designer.cs">
      <DependentUpon>FrmCContractor.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\FormButton\FrmCDirect.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\FormButton\FrmCDirect.Designer.cs">
      <DependentUpon>FrmCDirect.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\FormButton\FrmCNotes.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\FormButton\FrmCNotes.Designer.cs">
      <DependentUpon>FrmCNotes.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\FormButton\FrmCReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\FormButton\FrmCReport.Designer.cs">
      <DependentUpon>FrmCReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\FormButton\FrmCRII.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\FormButton\FrmCRII.Designer.cs">
      <DependentUpon>FrmCRII.cs</DependentUpon>
    </Compile>
    <Compile Include="Claim\tClaim.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Claim\tClaim.Designer.cs">
      <DependentUpon>tClaim.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Claim\ADJ.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Claim\ADJ.Designer.cs">
      <DependentUpon>ADJ.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Claim\AMD.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Claim\AMD.Designer.cs">
      <DependentUpon>AMD.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Claim\PAY.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Claim\PAY.Designer.cs">
      <DependentUpon>PAY.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Claim\REINSR.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Claim\REINSR.Designer.cs">
      <DependentUpon>REINSR.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Claim\REG.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Claim\REG.Designer.cs">
      <DependentUpon>REG.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Claim\SUM.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Claim\SUM.Designer.cs">
      <DependentUpon>SUM.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\ParReins\FacInstallPar.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\ParReins\FacInstallPar.Designer.cs">
      <DependentUpon>FacInstallPar.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\ParReins\EndtFacPropPar.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\ParReins\EndtFacPropPar.Designer.cs">
      <DependentUpon>EndtFacPropPar.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\ParReins\FacPropPar.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\ParReins\FacPropPar.Designer.cs">
      <DependentUpon>FacPropPar.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\ParReins\EndtGenInfoPar.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\ParReins\EndtGenInfoPar.Designer.cs">
      <DependentUpon>EndtGenInfoPar.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\ParReins\GenInfoPar.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\ParReins\GenInfoPar.Designer.cs">
      <DependentUpon>GenInfoPar.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\ParReins\TeatyInstallPar.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\ParReins\TeatyInstallPar.Designer.cs">
      <DependentUpon>TeatyInstallPar.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectAttach.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectAttach.Designer.cs">
      <DependentUpon>DirectAttach.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectButton.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectButton.Designer.cs">
      <DependentUpon>DirectButton.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectExcess.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectExcess.Designer.cs">
      <DependentUpon>DirectExcess.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Reins\EndtGenInfo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Reins\EndtGenInfo.Designer.cs">
      <DependentUpon>EndtGenInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Reins\EndtFacProp.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Reins\EndtFacProp.Designer.cs">
      <DependentUpon>EndtFacProp.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Reins\NonProp.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Reins\NonProp.Designer.cs">
      <DependentUpon>NonProp.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Reins\ReinsButton.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Reins\ReinsButton.Designer.cs">
      <DependentUpon>ReinsButton.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Reins\FacProp.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Reins\FacProp.Designer.cs">
      <DependentUpon>FacProp.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Reins\GenInfo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Reins\GenInfo.Designer.cs">
      <DependentUpon>GenInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Reins\FacInstall.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Reins\FacInstall.Designer.cs">
      <DependentUpon>FacInstall.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Reins\TeatyInstall.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Reins\TeatyInstall.Designer.cs">
      <DependentUpon>TeatyInstall.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorInstall.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorInstall.Designer.cs">
      <DependentUpon>EndorInstall.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorDirectInsured_CAR.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorDirectInsured_CAR.Designer.cs">
      <DependentUpon>EndorDirectInsured_CAR.cs</DependentUpon>
    </Compile>
    <Compile Include="INSClass\InterMedData.cs" />
    <Compile Include="INSClass\NNumericTextBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="INSClass\NNumericTextBox.Designer.cs">
      <DependentUpon>NNumericTextBox.cs</DependentUpon>
    </Compile>
    <Compile Include="INSClass\NumericINT.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="INSClass\NumericTexttox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="INSClass\RptDataFunc.cs" />
    <Compile Include="INSClass\SizeAuto.cs" />
    <Compile Include="INSClass\UtilityFunc.cs" />
    <Compile Include="InsEnvironment.cs" />
    <Compile Include="NumericTextBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Others\frmFac.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Others\frmFac.Designer.cs">
      <DependentUpon>frmFac.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmXOL2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmXOL2.Designer.cs">
      <DependentUpon>frmXOL2.cs</DependentUpon>
    </Compile>
    <Compile Include="Others\frmXOL1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Others\frmXOL1.Designer.cs">
      <DependentUpon>frmXOL1.cs</DependentUpon>
    </Compile>
    <Compile Include="Others\COGen1.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Others\COGen1.Designer.cs">
      <DependentUpon>COGen1.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorDirectInsured_CGL.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorDirectInsured_CGL.Designer.cs">
      <DependentUpon>EndorDirectInsured_CGL.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorDirectInsured_PAR.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorDirectInsured_PAR.Designer.cs">
      <DependentUpon>EndorDirectInsured_PAR.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorDirectInsured_EEC.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorDirectInsured_EEC.Designer.cs">
      <DependentUpon>EndorDirectInsured_EEC.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorDirectInsured_CPM.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorDirectInsured_CPM.Designer.cs">
      <DependentUpon>EndorDirectInsured_CPM.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorDirectInsured_PMP.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorDirectInsured_PMP.Designer.cs">
      <DependentUpon>EndorDirectInsured_PMP.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorAttach.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorAttach.Designer.cs">
      <DependentUpon>EndorAttach.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorExcess.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorExcess.Designer.cs">
      <DependentUpon>EndorExcess.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorDirectGen.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorDirectGen.Designer.cs">
      <DependentUpon>EndorDirectGen.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorPrint.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Endor\EndorPrint.Designer.cs">
      <DependentUpon>EndorPrint.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\FacIn\RIGen.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\FacIn\RIGen.Designer.cs">
      <DependentUpon>RIGen.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectGen.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectGen.Designer.cs">
      <DependentUpon>DirectGen.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectInstall.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectInstall.Designer.cs">
      <DependentUpon>DirectInstall.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectInsured_PAR.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectInsured_PAR.Designer.cs">
      <DependentUpon>DirectInsured_PAR.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectInsured_EEC.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectInsured_EEC.Designer.cs">
      <DependentUpon>DirectInsured_EEC.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectInsured_PMP.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectInsured_PMP.Designer.cs">
      <DependentUpon>DirectInsured_PMP.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectInsured_CPM.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectInsured_CPM.Designer.cs">
      <DependentUpon>DirectInsured_CPM.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectInsured_CGL.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectInsured_CGL.Designer.cs">
      <DependentUpon>DirectInsured_CGL.cs</DependentUpon>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectInsured_CAR.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Ctrl\Policy\DirectInsured_CAR.Designer.cs">
      <DependentUpon>DirectInsured_CAR.cs</DependentUpon>
    </Compile>
    <Compile Include="INSClass\EnumHelper.cs" />
    <Compile Include="Master\frmChargeCode.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmChargeCode.Designer.cs">
      <DependentUpon>frmChargeCode.cs</DependentUpon>
    </Compile>
    <Compile Include="Others\frmXOL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Others\frmXOL.Designer.cs">
      <DependentUpon>frmXOL.cs</DependentUpon>
    </Compile>
    <Compile Include="Others\frmFac1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Others\frmFac1.Designer.cs">
      <DependentUpon>frmFac1.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmReinsSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmReinsSearch.Designer.cs">
      <DependentUpon>frmReinsSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmBrkSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmBrkSearch.Designer.cs">
      <DependentUpon>frmBrkSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmSurplus.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmSurplus.Designer.cs">
      <DependentUpon>frmSurplus.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmInsInterestD.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmInsInterestD.Designer.cs">
      <DependentUpon>frmInsInterestD.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmExcessClauseD.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmExcessClauseD.Designer.cs">
      <DependentUpon>frmExcessClauseD.cs</DependentUpon>
    </Compile>
    <Compile Include="Others\frmClient.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Others\frmClient.Designer.cs">
      <DependentUpon>frmClient.cs</DependentUpon>
    </Compile>
    <Compile Include="Others\frmReinsurer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Others\frmReinsurer.Designer.cs">
      <DependentUpon>frmReinsurer.cs</DependentUpon>
    </Compile>
    <Compile Include="Others\frmBroker.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Others\frmBroker.Designer.cs">
      <DependentUpon>frmBroker.cs</DependentUpon>
    </Compile>
    <Compile Include="Others\frmProducer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Others\frmProducer.Designer.cs">
      <DependentUpon>frmProducer.cs</DependentUpon>
    </Compile>
    <Compile Include="Others\frmHealth.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Others\frmHealth.Designer.cs">
      <DependentUpon>frmHealth.cs</DependentUpon>
    </Compile>
    <Compile Include="Others\frmSolicitor.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Others\frmSolicitor.Designer.cs">
      <DependentUpon>frmSolicitor.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmLossAdjuster.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmLossAdjuster.Designer.cs">
      <DependentUpon>frmLossAdjuster.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmMiscCode.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmMiscCode.Designer.cs">
      <DependentUpon>frmMiscCode.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmProject.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmProject.Designer.cs">
      <DependentUpon>frmProject.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmSelectPolicy.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmSelectPolicy.Designer.cs">
      <DependentUpon>frmSelectPolicy.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmNatureLoss.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmNatureLoss.Designer.cs">
      <DependentUpon>frmNatureLoss.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmInsClass.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmInsClass.Designer.cs">
      <DependentUpon>frmInsClass.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmInsInterest.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmInsInterest.Designer.cs">
      <DependentUpon>frmInsInterest.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmExcessClause.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmExcessClause.Designer.cs">
      <DependentUpon>frmExcessClause.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmGL.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmGL.Designer.cs">
      <DependentUpon>frmGL.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmDept.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmDept.Designer.cs">
      <DependentUpon>frmDept.cs</DependentUpon>
    </Compile>
    <Compile Include="frmAddUser1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmAddUser1.Designer.cs">
      <DependentUpon>frmAddUser1.cs</DependentUpon>
    </Compile>
    <Compile Include="Business\FormButton\FrmBDirect.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Business\FormButton\FrmBDirect.Designer.cs">
      <DependentUpon>FrmBDirect.cs</DependentUpon>
    </Compile>
    <Compile Include="FrmMain1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmMain1.Designer.cs">
      <DependentUpon>FrmMain1.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmJobNature.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmJobNature.Designer.cs">
      <DependentUpon>frmJobNature.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmAccident.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmAccident.Designer.cs">
      <DependentUpon>frmAccident.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmSubContractor.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmSubContractor.Designer.cs">
      <DependentUpon>frmSubContractor.cs</DependentUpon>
    </Compile>
    <Compile Include="Others\frmAddUser.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Others\frmAddUser.designer.cs">
      <DependentUpon>frmAddUser.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmAtt.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmAtt.Designer.cs">
      <DependentUpon>frmAtt.cs</DependentUpon>
    </Compile>
    <Compile Include="frmChangePwd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmChangePwd.Designer.cs">
      <DependentUpon>frmChangePwd.cs</DependentUpon>
    </Compile>
    <Compile Include="frmLogin.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmLogin.Designer.cs">
      <DependentUpon>frmLogin.cs</DependentUpon>
    </Compile>
    <Compile Include="Others\frmMain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Others\frmMain.Designer.cs">
      <DependentUpon>frmMain.cs</DependentUpon>
    </Compile>
    <Compile Include="Master\frmSite.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Master\frmSite.Designer.cs">
      <DependentUpon>frmSite.cs</DependentUpon>
    </Compile>
    <Compile Include="Others\Frm_Demo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Others\Frm_Demo.Designer.cs">
      <DependentUpon>Frm_Demo.cs</DependentUpon>
    </Compile>
    <Compile Include="INSClass\DBHelper.cs" />
    <Compile Include="INSClass\DES.cs" />
    <Compile Include="INSClass\DBConnect.cs" />
    <Compile Include="INSClass\ExportDBF.cs" />
    <Compile Include="INSClass\PswText.cs" />
    <Compile Include="INSClass\NumGetstring.cs" />
    <Compile Include="INSClass\SQLHandle.cs" />
    <Compile Include="INSClass\XLS.cs" />
    <Compile Include="Menu.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Menu.Designer.cs">
      <DependentUpon>Menu.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="Account\acvoucher.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>acvoucher.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Account\ctrl\acadj.resx">
      <DependentUpon>acadj.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Account\ctrl\acchk.resx">
      <DependentUpon>acchk.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Account\ctrl\acset.resx">
      <DependentUpon>acset.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Account\ctrl\actogl.resx">
      <DependentUpon>actogl.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Account\ctrl\btn.resx">
      <DependentUpon>btn.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Account\drcrmsc.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>drcrmsc.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Account\FrmAReport.resx">
      <DependentUpon>FrmAReport.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Account\pmscinv.resx">
      <DependentUpon>pmscinv.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Account\ppyset.resx">
      <DependentUpon>ppyset.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Account\reqginv.resx">
      <DependentUpon>reqginv.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Account\reqset.resx">
      <DependentUpon>reqset.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Account\tpyset.resx">
      <DependentUpon>tpyset.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Account\tginvh.resx">
      <DependentUpon>tginvh.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\Inquiry\DrCrNoteFacIn.resx">
      <DependentUpon>DrCrNoteFacIn.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\Inquiry\reqinv.resx">
      <DependentUpon>reqinv.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\Inquiry\undauto.resx">
      <DependentUpon>undauto.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\objRpt\cryDocViewer.resx">
      <DependentUpon>cryDocViewer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\objRpt\cryRptViewer.resx">
      <DependentUpon>cryRptViewer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\pexpnote.resx">
      <DependentUpon>pexpnote.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\prih.resx">
      <DependentUpon>prih.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\InquirylistOut.resx">
      <DependentUpon>InquirylistOut.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\Inquirylist.resx">
      <DependentUpon>Inquirylist.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\Inquiry\reqpyinv.resx">
      <DependentUpon>reqpyinv.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\Report\ClaimI.resx">
      <DependentUpon>ClaimI.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\tcvrlet.resx">
      <DependentUpon>tcvrlet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Claim\AMD.zh-TW.resx">
      <DependentUpon>AMD.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Claim\Log.resx">
      <DependentUpon>Log.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Claim\REG.zh-TW.resx">
      <DependentUpon>REG.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\EecClaim\EciAMD.zh-TW.resx">
      <DependentUpon>EciAMD.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\EecClaim\EciClmdet.zh-TW.resx">
      <DependentUpon>EciClmdet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\EecClaim\EciREG.zh-TW.resx">
      <DependentUpon>EciREG.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\EecClaim\EciLog.resx">
      <DependentUpon>EciLog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="edituser.resx">
      <DependentUpon>edituser.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\bdwn_cl2.resx">
      <DependentUpon>bdwn_cl2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Browse.resx">
      <DependentUpon>Browse.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\renewopt.resx">
      <DependentUpon>renewopt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\expnote.resx">
      <DependentUpon>expnote.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmset.resx">
      <DependentUpon>frmset.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmCodeSearch.resx">
      <DependentUpon>frmCodeSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmGlacSearch.resx">
      <DependentUpon>frmGlacSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmfinvno.resx">
      <DependentUpon>frmfinvno.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmPayee.resx">
      <DependentUpon>frmPayee.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\reqcinv.resx">
      <DependentUpon>reqcinv.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\selstat.resx">
      <DependentUpon>selstat.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\tcpyappr.resx">
      <DependentUpon>tcpyappr.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmLevy.resx">
      <DependentUpon>frmLevy.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmCoverNote.resx">
      <DependentUpon>frmCoverNote.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\pmast.resx">
      <DependentUpon>pmast.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\cpmsche.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>cpmsche.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\drac.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>drac.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\drnote.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>drnote.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\drnote2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>drnote2.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\drnote3.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>drnote3.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\drnote4.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>drnote4.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\eecni.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>eecni.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\eecsche.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>eecsche.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\expgenl.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>expgenl.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\expnote.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>expnote.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\fgpsche.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>fgpsche.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\mprdr.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>mprdr.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\mypsche.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>mypsche.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\parsche.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>parsche.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\pendt.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>pendt.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\pendtcar.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>pendtcar.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\piische.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>piische.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\pmpci.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>pmpci.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\pmpsche.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>pmpsche.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\reecsche.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>reecsche.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\riapp.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>riapp.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\ribrkdn.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>ribrkdn.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\ribrkdn2.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>ribrkdn2.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\rparsche.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rparsche.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\pendt.resx">
      <DependentUpon>pendt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\pfacinv.resx">
      <DependentUpon>pfacinv.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\pinv.resx">
      <DependentUpon>pinv.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\psche.resx">
      <DependentUpon>psche.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\RIEndt.resx">
      <DependentUpon>RIEndt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\RIWard.resx">
      <DependentUpon>RIWard.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\EndtReinsurance.resx">
      <DependentUpon>EndtReinsurance.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\EndtReinsurancePar.resx">
      <DependentUpon>EndtReinsurancePar.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\daqcost.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>daqcost.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\eecanly.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>eecanly.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\effpmp.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>effpmp.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\polprmsm.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>polprmsm.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\prmanly.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>prmanly.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\prmbord02.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>prmbord02.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\prmbord05.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>prmbord05.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\prmdrcr.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>prmdrcr.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\prmexpl.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>prmexpl.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\prmfac.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>prmfac.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\prmgrnd.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>prmgrnd.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\prmreg.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>prmreg.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\prmrel.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>prmrel.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\prmstat.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>prmstat.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\prmtty.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>prmtty.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\prmunern.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>prmunern.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\prmuniv.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>prmuniv.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\ReinsurancePar.resx">
      <DependentUpon>ReinsurancePar.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\clmadj.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>clmadj.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\clma_eci.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>clma_eci.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\clmcvr.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>clmcvr.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\clmpadv.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>clmpadv.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\clmpay.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>clmpay.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\clmpla.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>clmpla.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\clmp_eci.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>clmp_eci.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\pclmappr.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>pclmappr.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\pclmlist.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>pclmlist.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\pclmpayl.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>pclmpayl.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="objRpt\pcvrlet.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>pcvrlet.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\pclaim.resx">
      <DependentUpon>pclaim.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\rclmeci.resx">
      <DependentUpon>rclmeci.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\EecClaim\riEciAMD.resx">
      <DependentUpon>riEciAMD.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\EecClaim\riEciREG.resx">
      <DependentUpon>riEciREG.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\FacIn\EndorRIInsured_PAR.resx">
      <DependentUpon>EndorRIInsured_PAR.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\FacIn\EndorRIInsured_CGL.resx">
      <DependentUpon>EndorRIInsured_CGL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\FacIn\EndorRIInsured_EEC.resx">
      <DependentUpon>EndorRIInsured_EEC.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\FacIn\EndorRIInsured_CAR.resx">
      <DependentUpon>EndorRIInsured_CAR.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\FacIn\EndorRIGen.resx">
      <DependentUpon>EndorRIGen.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\FacIn\RIInstall.resx">
      <DependentUpon>RIInstall.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\FacIn\RIInsured_PAR.resx">
      <DependentUpon>RIInsured_PAR.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\FacIn\RIInsured_EEC.resx">
      <DependentUpon>RIInsured_EEC.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\FacIn\RIInsured_CPM.resx">
      <DependentUpon>RIInsured_CPM.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\FacIn\RIInsured_CGL.resx">
      <DependentUpon>RIInsured_CGL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\FacIn\RIInsured_CAR.resx">
      <DependentUpon>RIInsured_CAR.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\RIApp.resx">
      <DependentUpon>RIApp.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\Inquiry\DrCrNoteFacOut.resx">
      <DependentUpon>DrCrNoteFacOut.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\Inquiry\DrCrNote.resx">
      <DependentUpon>DrCrNote.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\Inquiry\COInquiry.resx">
      <DependentUpon>COInquiry.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\Inquiry\RIInquiry.resx">
      <DependentUpon>RIInquiry.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\Inquiry\DirectInquiry.resx">
      <DependentUpon>DirectInquiry.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\Reinsurance.resx">
      <DependentUpon>Reinsurance.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\Report\PremiumI.resx">
      <DependentUpon>PremiumI.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\Inquiry\inqpol.resx">
      <DependentUpon>inqpol.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\Inquiry\inqclm.resx">
      <DependentUpon>inqclm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\Inquiry\DirectClaimInquiry.resx">
      <DependentUpon>DirectClaimInquiry.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\Inquiry\RIClaimInquiry.resx">
      <DependentUpon>RIClaimInquiry.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\Inquiry\tpyrinvh.resx">
      <DependentUpon>tpyrinvh.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\Inquiry\tpyinvh.resx">
      <DependentUpon>tpyinvh.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\Inquirycpol.resx">
      <DependentUpon>Inquirycpol.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\rClaim.resx">
      <DependentUpon>rClaim.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\tclmeci.resx">
      <DependentUpon>tclmeci.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Claim\riAMD.resx">
      <DependentUpon>riAMD.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Claim\ClaimButton.resx">
      <DependentUpon>ClaimButton.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Claim\riREG.resx">
      <DependentUpon>riREG.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\EecClaim\EciADJ.resx">
      <DependentUpon>EciADJ.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\EecClaim\EciAMD.resx">
      <DependentUpon>EciAMD.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\EecClaim\EciClmdet.resx">
      <DependentUpon>EciClmdet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\EecClaim\EciPAY.resx">
      <DependentUpon>EciPAY.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\EecClaim\EciREG.resx">
      <DependentUpon>EciREG.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\EecClaim\EciREINSR.resx">
      <DependentUpon>EciREINSR.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\EecClaim\EciSUM.resx">
      <DependentUpon>EciSUM.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\bdwn_ec2.resx">
      <DependentUpon>bdwn_ec2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\bdwn_sue.resx">
      <DependentUpon>bdwn_sue.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\bdwn_suc.resx">
      <DependentUpon>bdwn_suc.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\bdwn_pyc.resx">
      <DependentUpon>bdwn_pyc.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\bdwn_cl.resx">
      <DependentUpon>bdwn_cl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\bdwn_pye.resx">
      <DependentUpon>bdwn_pye.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\bdwn_ec.resx">
      <DependentUpon>bdwn_ec.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\confclm.resx">
      <DependentUpon>confclm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\confpol.resx">
      <DependentUpon>confpol.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\addendt.resx">
      <DependentUpon>addendt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\EndtDirectInsured.resx">
      <DependentUpon>EndtDirectInsured.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmFabSearch.resx">
      <DependentUpon>frmFabSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmMchSearch.resx">
      <DependentUpon>frmMchSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmNatlosSearch.resx">
      <DependentUpon>frmNatlosSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmLocSearch.resx">
      <DependentUpon>frmLocSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmClmdet.resx">
      <DependentUpon>frmClmdet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmTtySearch.resx">
      <DependentUpon>frmTtySearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmXolSearch.resx">
      <DependentUpon>frmXolSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmCedantSearch.resx">
      <DependentUpon>frmCedantSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\EndtClauseSearch.resx">
      <DependentUpon>EndtClauseSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\EndtExcessSearch.resx">
      <DependentUpon>EndtExcessSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmBrokerSearch.resx">
      <DependentUpon>frmBrokerSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmSourceSearch.resx">
      <DependentUpon>frmSourceSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmReinsurerSearch.resx">
      <DependentUpon>frmReinsurerSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\Endorsement.resx">
      <DependentUpon>Endorsement.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\DirectPolicy.resx">
      <DependentUpon>DirectPolicy.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmOccSearch.resx">
      <DependentUpon>frmOccSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmProfessionSearch.resx">
      <DependentUpon>frmProfessionSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmInsuredSearch.resx">
      <DependentUpon>frmInsuredSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmExcessSearch.resx">
      <DependentUpon>frmExcessSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmClauseSearch.resx">
      <DependentUpon>frmClauseSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmBusniessSearch.resx">
      <DependentUpon>frmBusniessSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmSiteSearch.resx">
      <DependentUpon>frmSiteSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmClientSearch.resx">
      <DependentUpon>frmClientSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Others\DContractor.resx">
      <DependentUpon>DContractor.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\FormButton\FrmBNotes.resx">
      <DependentUpon>FrmBNotes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\FormButton\FrmBReport.resx">
      <DependentUpon>FrmBReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\FormButton\FrmBCOInsur.resx">
      <DependentUpon>FrmBCOInsur.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\FormButton\FrmBRII.resx">
      <DependentUpon>FrmBRII.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmProducerSearch.resx">
      <DependentUpon>frmProducerSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\geninstall.resx">
      <DependentUpon>geninstall.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\inqtrn.resx">
      <DependentUpon>inqtrn.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\inqpay.resx">
      <DependentUpon>inqpay.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\selvo.resx">
      <DependentUpon>selvo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\FormButton\FrmCCOInsur.resx">
      <DependentUpon>FrmCCOInsur.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\FormButton\FrmCContractor.resx">
      <DependentUpon>FrmCContractor.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\FormButton\FrmCDirect.resx">
      <DependentUpon>FrmCDirect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\FormButton\FrmCNotes.resx">
      <DependentUpon>FrmCNotes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\FormButton\FrmCReport.resx">
      <DependentUpon>FrmCReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\FormButton\FrmCRII.resx">
      <DependentUpon>FrmCRII.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Claim\tClaim.resx">
      <DependentUpon>tClaim.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Claim\ADJ.resx">
      <DependentUpon>ADJ.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Claim\AMD.resx">
      <DependentUpon>AMD.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Claim\PAY.resx">
      <DependentUpon>PAY.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Claim\REINSR.resx">
      <DependentUpon>REINSR.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Claim\REG.resx">
      <DependentUpon>REG.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Claim\SUM.resx">
      <DependentUpon>SUM.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\ParReins\FacInstallPar.resx">
      <DependentUpon>FacInstallPar.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\ParReins\EndtFacPropPar.resx">
      <DependentUpon>EndtFacPropPar.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\ParReins\FacPropPar.resx">
      <DependentUpon>FacPropPar.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\ParReins\EndtGenInfoPar.resx">
      <DependentUpon>EndtGenInfoPar.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\ParReins\GenInfoPar.resx">
      <DependentUpon>GenInfoPar.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\ParReins\TeatyInstallPar.resx">
      <DependentUpon>TeatyInstallPar.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Policy\DirectAttach.resx">
      <DependentUpon>DirectAttach.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Policy\DirectButton.resx">
      <DependentUpon>DirectButton.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Policy\DirectExcess.resx">
      <DependentUpon>DirectExcess.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Reins\EndtGenInfo.resx">
      <DependentUpon>EndtGenInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Reins\EndtFacProp.resx">
      <DependentUpon>EndtFacProp.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Reins\NonProp.resx">
      <DependentUpon>NonProp.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Reins\ReinsButton.resx">
      <DependentUpon>ReinsButton.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Reins\FacProp.resx">
      <DependentUpon>FacProp.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Reins\GenInfo.resx">
      <DependentUpon>GenInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Reins\FacInstall.resx">
      <DependentUpon>FacInstall.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Reins\TeatyInstall.resx">
      <DependentUpon>TeatyInstall.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Endor\EndorInstall.resx">
      <DependentUpon>EndorInstall.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Endor\EndorDirectInsured_CAR.resx">
      <DependentUpon>EndorDirectInsured_CAR.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="INSClass\NNumericTextBox.resx">
      <DependentUpon>NNumericTextBox.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Others\frmFac.resx">
      <DependentUpon>frmFac.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmXOL2.resx">
      <DependentUpon>frmXOL2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Others\frmXOL1.resx">
      <DependentUpon>frmXOL1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Others\COGen1.resx">
      <DependentUpon>COGen1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Endor\EndorDirectInsured_CGL.resx">
      <DependentUpon>EndorDirectInsured_CGL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Endor\EndorDirectInsured_PAR.resx">
      <DependentUpon>EndorDirectInsured_PAR.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Endor\EndorDirectInsured_EEC.resx">
      <DependentUpon>EndorDirectInsured_EEC.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Endor\EndorDirectInsured_CPM.resx">
      <DependentUpon>EndorDirectInsured_CPM.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Endor\EndorDirectInsured_PMP.resx">
      <DependentUpon>EndorDirectInsured_PMP.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Endor\EndorAttach.resx">
      <DependentUpon>EndorAttach.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Endor\EndorExcess.resx">
      <DependentUpon>EndorExcess.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Endor\EndorDirectGen.resx">
      <DependentUpon>EndorDirectGen.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Endor\EndorPrint.resx">
      <DependentUpon>EndorPrint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\FacIn\RIGen.resx">
      <DependentUpon>RIGen.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Policy\DirectGen.resx">
      <DependentUpon>DirectGen.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Policy\DirectInstall.resx">
      <DependentUpon>DirectInstall.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Policy\DirectInsured_PAR.resx">
      <DependentUpon>DirectInsured_PAR.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Policy\DirectInsured_EEC.resx">
      <DependentUpon>DirectInsured_EEC.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Policy\DirectInsured_PMP.resx">
      <DependentUpon>DirectInsured_PMP.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Policy\DirectInsured_CPM.resx">
      <DependentUpon>DirectInsured_CPM.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Policy\DirectInsured_CGL.resx">
      <DependentUpon>DirectInsured_CGL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Ctrl\Policy\DirectInsured_CAR.resx">
      <DependentUpon>DirectInsured_CAR.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmChargeCode.resx">
      <DependentUpon>frmChargeCode.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Others\frmXOL.resx">
      <DependentUpon>frmXOL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Others\frmFac1.resx">
      <DependentUpon>frmFac1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmReinsSearch.resx">
      <DependentUpon>frmReinsSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmBrkSearch.resx">
      <DependentUpon>frmBrkSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmSurplus.resx">
      <DependentUpon>frmSurplus.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmInsInterestD.resx">
      <DependentUpon>frmInsInterestD.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmExcessClauseD.resx">
      <DependentUpon>frmExcessClauseD.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Others\frmClient.resx">
      <DependentUpon>frmClient.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Others\frmReinsurer.resx">
      <DependentUpon>frmReinsurer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Others\frmBroker.resx">
      <DependentUpon>frmBroker.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Others\frmProducer.resx">
      <DependentUpon>frmProducer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Others\frmHealth.resx">
      <DependentUpon>frmHealth.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Others\frmSolicitor.resx">
      <DependentUpon>frmSolicitor.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmLossAdjuster.resx">
      <DependentUpon>frmLossAdjuster.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmMiscCode.resx">
      <DependentUpon>frmMiscCode.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmProject.resx">
      <DependentUpon>frmProject.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmSelectPolicy.resx">
      <DependentUpon>frmSelectPolicy.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmNatureLoss.resx">
      <DependentUpon>frmNatureLoss.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmInsClass.resx">
      <DependentUpon>frmInsClass.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmInsInterest.resx">
      <DependentUpon>frmInsInterest.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmExcessClause.resx">
      <DependentUpon>frmExcessClause.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmGL.resx">
      <DependentUpon>frmGL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmDept.resx">
      <DependentUpon>frmDept.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmAddUser1.resx">
      <DependentUpon>frmAddUser1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Business\FormButton\FrmBDirect.resx">
      <DependentUpon>FrmBDirect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FrmMain1.resx">
      <DependentUpon>FrmMain1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmJobNature.resx">
      <DependentUpon>frmJobNature.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmAccident.resx">
      <DependentUpon>frmAccident.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmSubContractor.resx">
      <DependentUpon>frmSubContractor.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Others\frmAddUser.resx">
      <DependentUpon>frmAddUser.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmAtt.resx">
      <DependentUpon>frmAtt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmChangePwd.resx">
      <DependentUpon>frmChangePwd.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmLogin.resx">
      <DependentUpon>frmLogin.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Others\frmMain.resx">
      <DependentUpon>frmMain.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Master\frmSite.resx">
      <DependentUpon>frmSite.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Others\Frm_Demo.resx">
      <DependentUpon>Frm_Demo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Menu.resx">
      <DependentUpon>Menu.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="Account\acds.xsc">
      <DependentUpon>acds.xsd</DependentUpon>
    </None>
    <None Include="Account\acds.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>acds.Designer.cs</LastGenOutput>
    </None>
    <None Include="Account\acds.xss">
      <DependentUpon>acds.xsd</DependentUpon>
    </None>
    <None Include="Business\objRpt\DocDataSet.xsc">
      <DependentUpon>DocDataSet.xsd</DependentUpon>
    </None>
    <None Include="Business\objRpt\DocDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DocDataSet.Designer.cs</LastGenOutput>
    </None>
    <None Include="Business\objRpt\DocDataSet.xss">
      <DependentUpon>DocDataSet.xsd</DependentUpon>
    </None>
    <None Include="Business\objRpt\RptDataSet.xsc">
      <DependentUpon>RptDataSet.xsd</DependentUpon>
    </None>
    <None Include="Business\objRpt\RptDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>RptDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <None Include="Business\objRpt\RptDataSet.xss">
      <DependentUpon>RptDataSet.xsd</DependentUpon>
    </None>
    <None Include="Claim\objRpt\DsClaim.xsc">
      <DependentUpon>DsClaim.xsd</DependentUpon>
    </None>
    <None Include="Claim\objRpt\DsClaim.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DsClaim.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <None Include="Claim\objRpt\DsClaim.xss">
      <DependentUpon>DsClaim.xsd</DependentUpon>
    </None>
    <None Include="Claim\objRpt\RptDataClm.xsc">
      <DependentUpon>RptDataClm.xsd</DependentUpon>
    </None>
    <None Include="Claim\objRpt\RptDataClm.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>RptDataClm.Designer.cs</LastGenOutput>
    </None>
    <None Include="Claim\objRpt\RptDataClm.xss">
      <DependentUpon>RptDataClm.xsd</DependentUpon>
    </None>
    <None Include="INS_TemporaryKey.pfx" />
    <None Include="Master\mprdr.xsc">
      <DependentUpon>mprdr.xsd</DependentUpon>
    </None>
    <None Include="Master\mprdr.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>mprdr.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <None Include="Master\mprdr.xss">
      <DependentUpon>mprdr.xsd</DependentUpon>
    </None>
    <None Include="objRpt\riapp2.rpt" />
    <None Include="packages.config" />
    <None Include="Properties\app.manifest" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Images\" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.0">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.4.5">
      <Visible>False</Visible>
      <ProductName>Windows Installer 4.5</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{C0C07587-41A7-46C8-8FBD-3F9C8EBE2DDC}" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <Content Include="ooopic_0.ico" />
    <Content Include="ooopic_1496368250.ico" />
  </ItemGroup>
  <ItemGroup>
    <FileAssociation Include=".Insurance System">
      <Visible>False</Visible>
      <Description>INS</Description>
      <Progid>1</Progid>
      <DefaultIcon>ooopic_1496368250.ico</DefaultIcon>
    </FileAssociation>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>