using iText.Kernel.Pdf.Canvas.Parser.Data;
using iTextSharp.text.pdf.parser;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ScanToSign.PDF
{
    public class PdfRenderListener : IRenderListener
    {
        private int pageNum;
        private float pageWidth;
        private float pageHeight;
        private StringBuilder contentBuilder = new StringBuilder();
        private IList<CharPosition> charPositions = new List<CharPosition>();


        public PdfRenderListener(int pageNum, float pageWidth, float pageHeight)
        {
            this.pageNum = pageNum;
            this.pageWidth = pageWidth;
            this.pageHeight = pageHeight;
        }


        public void beginTextBlock()
        {


        }


        public void RenderText(iText.Kernel.Pdf.Canvas.Parser.Data.TextRenderInfo renderInfo)
        {
            
        }


        public void endTextBlock()
        {
        }


        public void renderImage(iText.Kernel.Pdf.Canvas.Parser.Data.ImageRenderInfo renderInfo)
        {

        }

        public virtual string Content
        {
            get
            {
                return contentBuilder.ToString();
            }
        }


        public virtual IList<CharPosition> getcharPositions()
        {
            return charPositions;
        }

        public void BeginTextBlock()
        {
            //throw new NotImplementedException();
        }

        public void RenderText(iTextSharp.text.pdf.parser.TextRenderInfo renderInfo)
        {
            IList<iTextSharp.text.pdf.parser.TextRenderInfo> characterRenderInfos = renderInfo.GetCharacterRenderInfos();
            foreach (iTextSharp.text.pdf.parser.TextRenderInfo textRenderInfo in characterRenderInfos)
            {
                string word = textRenderInfo.GetText();
                if (word.Length > 1)
                {
                    word = StringHelper.SubstringSpecial(word, word.Length - 1, word.Length);
                }
                var rectangle = textRenderInfo.GetAscentLine().GetBoundingRectange();
                float x = (float)rectangle.X;
                float y = (float)rectangle.Y;
                float charWidth = (float)(rectangle.Width);
                //也可以返回坐标相对于pdf页面大小的百分比
                float xPercent = (int)Math.Round(x / pageWidth * 10000, MidpointRounding.AwayFromZero) / 10000f;
                float yPercent = (int)Math.Round((1 - y / pageHeight) * 10000, MidpointRounding.AwayFromZero) / 10000f;

                CharPosition charPosition = new CharPosition(pageNum, x, y, charWidth);
                charPositions.Add(charPosition);
                contentBuilder.Append(word);
            }
        }

        public void EndTextBlock()
        {
            //throw new NotImplementedException();
        }

        public void RenderImage(iTextSharp.text.pdf.parser.ImageRenderInfo renderInfo)
        {
            //throw new NotImplementedException();
        }
    }

}
