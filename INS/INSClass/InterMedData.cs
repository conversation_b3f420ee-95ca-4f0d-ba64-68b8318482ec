using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Text;

namespace INS.INSClass
{
    partial class InterMedData
    {
        public static DataSet IntEff_PMP(DateTime vasat, String vfbus, String wfbus, String vfstatus)
        {
            DataTable dtpolh = new DataTable();
            DataTable dtoinsint_e = new DataTable();

            String 
            cmd = "select A.fctlid_p,A.fctlid,A.fclass,A.fsclass,A.fpolno,A<PERSON>f<PERSON>,A.finsd,A.fprdr,A.fclnt,A.fissdate," +
                  "A.fincfr,A.fincto,A.flmt1,A.flmt2,A.fupdlmt1,A.fupdlmt2 from polh A, " +
                  "(select a.fctlid_p, max(a.fendtno) as fendtno from polh a," +
                  "(select distinct fctlid_p from polh where fissdate <= {1} and {1} between fincfr and fincto and " +
                  "fconfirm = '{0}') as b " +
                  "where a.fissdate <= {1} and a.fconfirm = '{0}' and a.fctlid_p=b.fctlid_p group by a.fctlid_p) as B " +
                  "where {1} between A.fincfr and A.fincto and A.fconfirm = '{0}' and A.fctlid_p = B.fctlid_p and " +
                  "A.fendtno = B.fendtno ";

            string sqlString = string.Format(cmd, vfstatus, csCommon.DbDateFormat(vasat));
            SqlDataAdapter da = new SqlDataAdapter(sqlString, DBConnect.opdbConn);

            da.Fill(dtpolh);
            da.Dispose();

            cmd = "select A3.fctlid_p,A3.fctlid_1,A3.fpolno,A3.fendtno,A3.fkind,A3.fitem,A3.fmode,A3.frno,A3.fupdsi," +
                  "'' as fclass,'' as fsclass,'' as fclsseq,'' as fclsname,'' as finsd,'' as fprdr,'' as fclnt," +
                  "cast(null as datetime) as fissdate,cast(null as datetime) as fincfr,cast(null as datetime) as fincto," +
                  "'' as fcover,cast(0 as decimal) as ftpbi,cast(0 as decimal) as ftppd " +
                  "from oinsint_e A3," +
                  "(select a2.fctlid_p,a2.fitem,max(a2.fendtno) as fendtno from oinsint_e a2," +
                  "(select A1.fctlid_p,A1.fctlid,A1.fclass,A1.fsclass,A1.fpolno,A1.fendtno,A1.finsd,A1.fprdr,A1.fclnt," +
                  "A1.fissdate,A1.fincfr,A1.fincto,A1.flmt1,A1.flmt2,A1.fupdlmt1,A1.fupdlmt2 from polh A1," +
                  "(select a0.fctlid_p, max(a0.fendtno) as fendtno from polh a0," +
                  "(select distinct fctlid_p from polh where fissdate <= {1} and {1} between fincfr and fincto and " +
                  "fconfirm = '{0}') as b0 " +
                  "where a0.fissdate <= {1} and a0.fconfirm = '{0}' and a0.fctlid_p=b0.fctlid_p group by a0.fctlid_p ) as B1 " +
                  "where {1} between A1.fincfr and A1.fincto and A1.fconfirm = '{0}' and A1.fctlid_p = B1.fctlid_p and " +
                  "A1.fendtno = B1.fendtno) as b2 " +
                  "where a2.fctlid_p = b2.fctlid_p and a2.fendtno <= b2.fendtno group by a2.fctlid_p,a2.fitem) as B3 " +
                  "where A3.fctlid_p = B3.fctlid_p and A3.fendtno = B3.fendtno and A3.fitem = B3.fitem and A3.fmode <> '3'";

            sqlString = string.Format(cmd, vfstatus, csCommon.DbDateFormat(vasat));

            SqlDataAdapter da2 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            da2.Fill(dtoinsint_e);
            da2.Dispose();

            DataTable dttmpslt = PremDataFunc.GenEff_PMP(dtpolh, dtoinsint_e);
            dttmpslt.DefaultView.Sort = "fclsseq,fkind";

            DataTable dtresult = dttmpslt.DefaultView.ToTable(false, "fclass", "fsclass", "fclsseq", "fclsname", "fpolno", "fendtno",
                     "finsd", "fprdr", "fclnt", "fissdate", "fincfr", "fincto", "fkind", "fcover", "fitem", "fmode", "frno", "fupdsi",
                     "ftpbi", "ftppd"
                    );

            if (dtresult.Rows.Count == 0)
            {
                //DataRow dr = dtresult.NewRow();
                //dr["Fclass"] = "???";
                //dtresult.Rows.Add(dr);
            }

            DataSet ds = new DataSet();
            ds.Tables.Add(dtresult);

            return ds;
        }
       
        public static DataSet IntPolPrmSm(DateTime vfbk_fr, DateTime vfbk_to, String vfbus, String wfbus, String vfstatus)
        {
            DataTable dtmprdr = new DataTable();
            DataTable dtoinvh = new DataTable();
            DataTable dtpolh = new DataTable();
            DataTable dtmin = new DataTable();
            DataTable dtminsc = new DataTable();

            String cmd = "select * from mprdr";
            SqlDataAdapter da = new SqlDataAdapter(cmd, DBConnect.mstdbConn);

            da.Fill(dtmprdr);
            da.Dispose();

            cmd = "select fctlid_1,fctlid,finstall,fbkdate as fbilldate,fgpm,ftdamt,fcomamt,fnpm,fbus " +
                  "from oinvh where fbkdate between {0} and {1} and (fbus like '{2}' or fbus like '{3}')";

            string sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                                vfbus, wfbus);

            SqlDataAdapter da2 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            da2.Fill(dtoinvh);
            da2.Dispose();

            cmd = "select distinct b.fctlid,b.fpolno,b.fendtno,b.fclass,b.fsclass,b.ftrade,b.ftrdesc,b.fextend," +
                  "b.fissdate,b.fincfr,b.fincto,b.fefffr,b.feffto,b.finsd,b.finsd_t1,b.finsd_t2,b.fcontrt," +
                  "b.fcontrt_t1,b.fsiteid,b.fsite,b.fsite_t1,b.fprdr,b.fclnt,b.fsum,b.fupdsum,b.fsi,b.fupdsi," +
                  "b.fliab,b.fupdliab,b.fgpm,b.ftdamt,b.fcomamt,b.fnpm,b.flgpm,b.fltdamt,b.flcomamt,b.flnpm,b.fbus " +
                  "from polh b,oinvh a " +
                  "where a.fbkdate between {0} and {1} and (a.fbus like '{2}' or a.fbus like '{3}') and a.fctlid_1 = b.fctlid";

            cmd = "select distinct b.fctlid,b.fpolno,b.fendtno,b.fclass,b.fsclass,b.finsd,cast(0 as decimal) as finstall,"+
                  "cast(null as datetime) as fissdate,"+
                  "'' as fclsseq, '' as fclsname,b.fprdr,'' as fpdesc,b.fclnt,'' as finter,'' as fcdesc,b.fsum,b.fupdsum,"+
                  "b.fsi,b.fupdsi,b.fliab,b.fupdliab,b.fgpm as pgpm,b.ftdamt as ptdamt,b.fcomamt as pcomamt,b.fnpm as pnpm,"+
                  "b.flgpm as plgpm,b.fltdamt as pltdamt,b.flcomamt as plcomamt,b.flnpm as plnpm,b.fbus," +
                  "cast(0 as smallint) as fpcnt, cast(0 as smallint) as fecnt "+
                  "from polh b,oinvh a " +
                  "where a.fbkdate between {0} and {1} and (a.fbus like '{2}' or a.fbus like '{3}') and a.fctlid_1 = b.fctlid";

            sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                                vfbus, wfbus);

            SqlDataAdapter da3 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            da3.Fill(dtpolh);
            da3.Dispose();

            cmd = "select fctlid_1,min(finstall) as finstall,min(fbkdate) as fbilldate "+
                  "from oinvh where fposted = '1' and fctlid_1 in "+
                  "(select distinct fctlid_1 from oinvh "+
                  "where fbkdate between {0} and {1} and (fbus like '{2}' or fbus like '{3}')) "+
                  "group by fctlid_1";

            sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                    vfbus, wfbus);

            SqlDataAdapter da4 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            da4.Fill(dtmin);
            da4.Dispose();

            cmd = "select fid as fclass,'' as fsclass,'' as fclsseq,'' as fclsname from minsc";
            da = new SqlDataAdapter(cmd, DBConnect.mstdbConn);
            da.Fill(dtminsc);
            da.Dispose();

            UtilityFunc.ExtendClassTable(dtminsc, false, false);
            
            DataTable dttmpslt = PremDataFunc.GenPolPremDet(dtpolh, dtmin, dtmprdr, vfbk_fr, vfbk_to);
            DataTable dtsum = PremDataFunc.GenPolPremSm(dttmpslt, dtminsc, vfbk_to);

            DataSet ds = new DataSet();
            ds.Tables.Add(dtsum);

            return ds;
        }
        
        public static DataSet IntPrmExpl(DateTime vfbk_fr, DateTime vfbk_to, String vfbus, String wfbus, String vfstatus)
        {
            DataTable dtmprdr = new DataTable();
            DataTable dtpolh = new DataTable();
            DataTable dtoinvh = new DataTable();
            DataTable dtmax = new DataTable();

            String cmd = "select * from mprdr";
            SqlDataAdapter da = new SqlDataAdapter(cmd, DBConnect.mstdbConn);

            da.Fill(dtmprdr);
            da.Dispose();

            cmd = "select a.fctlid_p,a.fctlid,a.fctlid_v,a.fbus,a.fpolno,a.fvseq,a.fvarno,a.fendtno,a.fendttype,a.fextend," +
                  "a.fsrcref,a.fcedepol,a.fcedeendt,'' as fnewendt,'' as fnextend,a.fuwyr,a.fclass,'' as fclsseq," +
                  "'' as fclsname,a.fsclass,a.fincfr,a.fincto,a.fefffr,a.feffto,a.fmntfr,a.fmntto,'' as prmnt,a.fprdr," +
                  "'' as fpdesc,a.fclnt,'' as fcdesc,a.fcontrt_t1,a.fsclnt,a.fcur,a.fsi,a.fliab,a.fpmcur,a.fgpm,a.ftdamt," +
                  "a.fcomamt,a.fnpm,a.flvyamt1,a.flvyamt2,a.flvyamt3,'' as fremark " +
                  "from polh a, " +
                  "(select distinct fctlid_p, fctlid_v from polh where ((feffto between {1} and {2}) or " +
                  "(fincto between {1} and {2}) or (fmntto between {1} and {2})) and " +
                  "fconfirm = '{0}' and (fbus like '{3}' or fbus like '{4}')) as b " +
                  "where a.fconfirm = '{0}' and a.fctlid_p = b.fctlid_p and a.fctlid_v = b.fctlid_v";

            string sqlString = string.Format(cmd, vfstatus, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                                vfbus, wfbus);

            SqlDataAdapter da2 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            da2.Fill(dtpolh);
            da2.Dispose();

            cmd = "select a.fctlid_1,a.fctlid,a.fremark " +
                  "from oinvh a,polh b " +
                  "where a.fctlid_1 = b.fctlid and b.fctlid_p in " +
                  "(select distinct fctlid_p from polh where ((feffto between {1} and {2}) or " +
                  "(fincto between {1} and {2}) or (fmntto between {1} and {2})) and " +
                  "fconfirm = '{0}' and (fbus like '{3}' or fbus like '{4}')) " +
                  "and a.fctlid in " +
                  "(select max(fctlid) as fctlid from oinvh group by fctlid_1) order by a.fctlid_1";

            sqlString = string.Format(cmd, vfstatus, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                                vfbus, wfbus);

            SqlDataAdapter da3 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            da3.Fill(dtoinvh);
            da3.Dispose();

            cmd = "select fctlid_p,max(fctlid) as fctlid,fctlid_v " +
                  "from polh where fconfirm = '{0}' and ((fincto between {1} and {2}) or (fmntto between {1} and {2})) and " +
                  "(fbus like '{3}' or fbus like '{4}') group by fctlid_p,fctlid_v";

            sqlString = string.Format(cmd, vfstatus, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                                vfbus, wfbus);

            SqlDataAdapter da4 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            da4.Fill(dtmax);
            da4.Dispose();

            DataTable dttmpslt = PremDataFunc.GenPrmExpl(dtoinvh, dtmprdr, dtpolh, dtmax);

            DataSet ds = new DataSet();
            ds.Tables.Add(dttmpslt);

            return ds;
        }

        public static DataSet IntPrmExpn(DateTime vfbk_fr, DateTime vfbk_to, String vfbus, String wfbus, 
            String vfstatus, String vfpolno)
        {
            DataTable dtmprdr = new DataTable();
            DataTable dtpolh = new DataTable();

            String cmd = "select * from mprdr";
            SqlDataAdapter da = new SqlDataAdapter(cmd, DBConnect.mstdbConn);
            da.Fill(dtmprdr);

            cmd = "select fctlid_p,fctlid,fbus,fpolno,fendtno,fvseq,fvarno,'' as fautono,fsrcref,fcedepol,fcedeendt," +
                "fuwyr,finsd,finsd_t1,faddr_t1,fsite_t1,fcontrt,fcontrt_t1,fclass,'' as fclsseq,'' as fclsname," +
                "fsclass,fincfr,fincto,fefffr,feffto,fmntfr,fmntto,cast(null as datetime) as fexpire,'' as prmnt,fprdr,'' as fpdesc," +
                "'' as fpadd1,'' as fpadd2,'' as fpadd3, '' as fpadd4, fclnt,'' as fcdesc,'' as fcadd1,'' as fcadd2," +
                "'' as fcadd3,'' as fcadd4,fsclnt,fpmcur,fsi,fliab,fgpm,ftdamt,fcomamt,fnpm,flvyamt1,flvyamt2,flvyamt3," +
                "'' as fposted " +
                "from polh where fctlid_p in   " +
                "(select fctlid_p from polh  " +
                "where ((feffto between {0} and {1}) or (fincto between {0} and {1}) or (fmntto between {0} and {1})) " +
                "and fconfirm = '{4}' and (fbus like '{2}' or fbus like '{3}') and fpolno like '{5}' group by fctlid_p) "+
                "and ((feffto between {0} and {1}) or (fincto between {0} and {1}) or (fmntto between {0} and {1}))  " +
                " order by fpolno, fendtno ";

            string sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                                vfbus, wfbus, vfstatus, vfpolno);

            SqlDataAdapter da2 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            da2.Fill(dtpolh);
            da2.Dispose();

            DataTable dttmpslt = PremDataFunc.GenPrmExpn(dtpolh, dtmprdr, vfbk_fr, vfbk_to, "E");

            DataSet ds = new DataSet();
            ds.Tables.Add(dttmpslt);

            return ds;
        }
        
        public static DataSet IntPrmFacOut(DateTime vfbk_fr, DateTime vfbk_to, String vfbus, String wfbus, String vfintadj)
        {
            DataTable dtmprdr = new DataTable();
            DataTable dtoriinvh = new DataTable();

            String cmd = "select * from mprdr";
            SqlDataAdapter da = new SqlDataAdapter(cmd, DBConnect.mstdbConn);

            da.Fill(dtmprdr);
            da.Dispose();

            cmd = "select a.fctlid_i,a.fpolno,a.fendtno,a.fclass,a.fsclass,a.finstall,a.ftotinstal," +
                  "a.finvdate,a.fbilldate,a.fbkdate,a.finvno,a.fdbtrtype,a.fdbtr,'' as fdbdesc,a.fclnt,a.fsclnt," +
                  "a.fpmcur,-a.fgpm as fgpm,-a.fcomamt as fcomamt,-a.fmiscamt1 as fmiscamt1,"+
                  "-a.fmiscamt2 as fmiscamt2,-a.fmiscamt3 as fmiscamt3,-a.fmiscamt as fmiscamt,"+
                  "-a.fpayable as fpayable,a.fdoctype,'' as fclsseq,''as fclsname " +
                  "from oriinvh a,oinvh c " +
                  "where a.fbilldate between {0} and {1} and " +
                  "a.fctlid_i = c.fctlid and (a.fbus like '{2}' or a.fbus like '{3}') and a.fintadj = '{4}'";

            string sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                                vfbus, wfbus, vfintadj);

            SqlDataAdapter da2 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            da2.Fill(dtoriinvh);
            da2.Dispose();

            DataTable dttmpslt = PremDataFunc.GenPrmFacOut(dtoriinvh,dtmprdr);

            DataSet ds = new DataSet();
            ds.Tables.Add(dttmpslt);

            return ds;
        }

        public static DataSet IntPrmTty(DateTime vfbk_fr, DateTime vfbk_to, String vfbus, String wfbus,
                                        String vttytype, String vfintadj)
        {
            DataTable dtmprdr = new DataTable();
            DataTable dtmtty = new DataTable();
            DataTable dtoriinvd = new DataTable();

            String cmd = "select * from mprdr";
            SqlDataAdapter da = new SqlDataAdapter(cmd, DBConnect.mstdbConn);

            da.Fill(dtmprdr);
            da.Dispose();

            String lc_ttymstr = vttytype.Equals("02") ? "mty" : vttytype.Equals("03") ? "mxl" : "mfb";

            cmd = string.Format("select * from {0}",lc_ttymstr);
            SqlDataAdapter da2 = new SqlDataAdapter(cmd, DBConnect.mstdbConn);

            da2.Fill(dtmtty);
            da2.Dispose();

            cmd = "select b.fritype,b.fttycode,b.fdbtrtype,b.fdbtr,b.fpolno,b.fendtno,b.fsec,b.fclass,b.fsclass,"+
                  "b.finstall,b.fkind,b.fgrnet," +
                  "sum(-b.fgpm) as fgpm,sum(-b.fnpm) as fnpm,sum(-b.fcomamt) as fcomamt,sum(-b.fpayable) as fpayable," +
                  "cast(0 as decimal) as fttypm,'' as fdbdesc,'' as fttydesc,"+
                  "'' as acc_class,'' as acc_desc,'' as pcode,'' as fclsseq,'' as fclsname,'' as sec_desc " +
                  "from oinvh a, oriinvd b where ((a.fbkdate between {0} and {1} and b.fbkdate is null) or " +
                  "(b.fbkdate between {0} and {1})) and (a.fbus like '{2}' or a.fbus like '{3}') and " +
                  "a.fctlid = b.fctlid_i and b.fritype = '{4}' and a.fintadj = '{5}' " +
                  "group by b.fritype,b.fttycode,b.fdbtrtype,b.fdbtr,b.fpolno,b.fendtno,b.fsec,b.fclass,b.fsclass,"+
                  "b.finstall,b.fkind,b.fgrnet";

            string sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                                vfbus, wfbus,vttytype, vfintadj);

            SqlDataAdapter da3 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            da3.Fill(dtoriinvd);
            da3.Dispose();

            DataTable dttmpslt = PremDataFunc.GenPrmTty(dtoriinvd, dtmprdr, dtmtty);

            DataSet ds = new DataSet();
            ds.Tables.Add(dttmpslt);

            return ds;
        }

        public static DataSet IntPrmBord(DateTime vfbk_fr, DateTime vfbk_to, String vfbus, String wfbus,
                                String vttytype, String vfintadj)
        {
            DataTable dtmprdr = new DataTable();
            DataTable dtmtty = new DataTable();
            DataTable dtmtyinsr = new DataTable(); 
            DataTable dtoriinstl = new DataTable();
            DataTable dtorid1 = new DataTable();
            DataTable dtpolh = new DataTable();
            DataTable dtoriinvd = new DataTable();
            DataTable dtoriinvd_s = new DataTable();

            String cmd = "select * from mprdr";
            SqlDataAdapter da = new SqlDataAdapter(cmd, DBConnect.mstdbConn);

            da.Fill(dtmprdr);
            da.Dispose();

            String lc_ttymstr = vttytype.Equals("02") ? "mty" : vttytype.Equals("03") ? "mxl" : "mfb";

            cmd = string.Format("select * from {0}", lc_ttymstr);
            SqlDataAdapter da2 = new SqlDataAdapter(cmd, DBConnect.mstdbConn);

            da2.Fill(dtmtty);
            da2.Dispose();

            cmd = "select a.fid,a.fdbtrtype,a.fdbtr,sum(a.fshare) as fshare from " +
                  "(select a.fid,case when b.fbrkr1 = 'PRD001' then 'R' else 'B' end as fdbtrtype," +
                  "case when b.fbrkr1 = 'PRD001' then b.freinsr else b.fbrkr1 end as fdbtr,b.freinsr,b.fbrkr1,b.fshare " +
                  "from {0} a, {1} b where a.fctlid = b.fctlid_1) as a " +
                  "group by a.fid,a.fdbtrtype,a.fdbtr";

            if (vttytype == "02")
                cmd = String.Format(cmd, "mty", "mtyinsr");
            else if (vttytype == "03")
                cmd = String.Format(cmd, "mxl", "mxlinsr");
            else
                cmd = String.Format(cmd, "mfb", "mfbinsr");

            SqlDataAdapter da3 = new SqlDataAdapter(cmd, DBConnect.mstdbConn);

            da3.Fill(dtmtyinsr);
            da3.Dispose();

            cmd = "select b.fctlid_1,b.fctlid_i,b.fritype,b.fttycode,b.fttysec,b.fkind,b.fgrnet," +
                  "sum(-b.fgpm) as fgpm,sum(-b.fnpm) as fnpm,sum(-b.fcomamt) as fcomamt,sum(-b.fbrkgeamt) as fbrkgeamt " +
                  "from oinvh a, oriinstl b " +
                  "where ((a.fbkdate between {0} and {1} and b.fbkdate is null) or " +
                  "(b.fbkdate between {0} and {1})) and (a.fbus like '{2}' or a.fbus like '{3}') and " +
                  "a.fintadj = '{4}' and a.fctlid = b.fctlid_i " +
                  "group by b.fctlid_1,b.fctlid_i,b.fritype,b.fttycode,b.fttysec,b.fkind,b.fgrnet";

            string sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                    vfbus, wfbus, vfintadj);

            SqlDataAdapter da4 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);

            da4.Fill(dtoriinstl);
            da4.Dispose();

            cmd = "select c.fctlid,c.fctlid_2,b.fctlid_i,c.fritype,c.fkind,c.fclass," +
                  "c.fttycode,c.fttysec,c.fsi,c.fsi2,c.fshare " +
                  "from oinvh a,oriinstl b,orid1 c " +
                  "where ((a.fbkdate between {0} and {1} and b.fbkdate is null) or " +
                  "(b.fbkdate between {0} and {1})) and (a.fbus like '{2}' or a.fbus like '{3}') and " +
                  "a.fintadj = '{4}' and a.fctlid = b.fctlid_i and c.fctlid_2=b.fctlid_1 and c.fritype = b.fritype";

            sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                        vfbus, wfbus, vfintadj);

            SqlDataAdapter da5 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);

            da5.Fill(dtorid1);
            da5.Dispose();

            cmd = "select distinct c.fctlid,c.fclass,c.fpolno,c.fendtno,c.finsd,c.finsd_t1,c.finsd_t2,c.fcontrt,"+
                  "c.fcontrt_t1,c.fsite,c.fefffr,c.feffto,c.fmntfr,c.fmntto,c.fsi,c.fliab "+
                  "from oinvh a,oriinstl b,polh c " +
                  "where ((a.fbkdate between {0} and {1} and b.fbkdate is null) or " +
                  "(b.fbkdate between {0} and {1})) and (a.fbus like '{2}' or a.fbus like '{3}') and " +
                  "a.fintadj = '{4}' and a.fctlid = b.fctlid_i and c.fctlid=a.fctlid_1";

            sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                        vfbus, wfbus, vfintadj);
            SqlDataAdapter da6 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);

            da6.Fill(dtpolh);
            da6.Dispose();

            cmd = "select c.fctlid_e,c.fctlid_1,c.fctlid_i,c.fkind,c.fritype,c.fttycode,c.fttysec,c.fgrnet,c.fdbtrtype,c.fdbtr," +
                  "sum(-c.fgpm) as fgpm_b,sum(-c.fnpm) as fnpm_b,"+
                  "sum(-c.fcomamt) as fttycom_b,sum(-c.fbrkgeamt) as fttybrk_b,"+
                  "'' as fpolno,'' as fendtno,'' as fdbdesc,cast(0 as decimal) as fsi,cast(0 as decimal) as ftl," +
                  "cast(0 as decimal) as fttysi,cast(0 as decimal) as fttytl,"+
                  "cast(0 as decimal) as fgpm,cast(0 as decimal) as fnpm," +
                  "cast(0 as decimal) as fttygm,cast(0 as decimal) as fttygm_b,cast(0 as decimal) as fttyprem,"+
                  "cast(0 as decimal) as fttyprem_b,cast(0 as decimal) as fttycom,cast(0 as decimal) as fttybrk,"+
                  "cast(0 as decimal) as fpaybrk,cast(0 as decimal) as fpaybrk_b,"+
                  "cast(0 as decimal) as fttynett,cast(0 as decimal) as fttynett_b,cast(0 as decimal) as fshare," +
                  "cast(null as datetime) as fefffr,cast(null as datetime) as feffto," +
                  "cast(null as datetime) as fmntfr,cast(null as datetime) as fmntto," +
                  "'' as finsd,'' as finsd_t1,'' as finsd_t2,'' as fcontrt,'' as fcontrt_t1," +
                  "'Y' as ftoprn,'Y' as fprntotal,'' as pcode "+
                  "from oinvh a, oriinstl b, oriinvd c " +
                  "where ((a.fbkdate between {0} and {1} and b.fbkdate is null) or " +
                  "(b.fbkdate between {0} and {1})) and (a.fbus like '{2}' or a.fbus like '{3}') and " +
                  "a.fintadj = '{4}' and a.fctlid = b.fctlid_i and a.fctlid=c.fctlid_i and "+
                  "c.fritype = b.fritype and c.fsec = b.fsec "+
                  "group by c.fctlid_e,c.fctlid_1,c.fctlid_i,c.fkind,c.fritype,c.fttycode,c.fttysec,c.fgrnet,c.fdbtrtype,c.fdbtr";
            
            sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                                vfbus, wfbus, vfintadj);

            SqlDataAdapter da7 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            da7.Fill(dtoriinvd);
            da7.Dispose();

            cmd = "select c.fctlid_e,c.fctlid_1,c.fctlid_i,c.fritype,c.fttycode,c.fttysec,c.fgrnet," +
                  "sum(c.fsi) as fttysi,sum(-c.fgpm) as fgpm,sum(-c.fnpm) as fnpm," +
                  "sum(-c.fcomamt) as fttycom,sum(-c.fbrkgeamt) as fttybrk " +
                  "from oinvh a, oriinstl b, oriinvd c " +
                  "where ((a.fbkdate between {0} and {1} and b.fbkdate is null) or " +
                  "(b.fbkdate between {0} and {1})) and (a.fbus like '{2}' or a.fbus like '{3}') and " +
                  "a.fintadj = '{4}' and a.fctlid = b.fctlid_i and a.fctlid=c.fctlid_i and " +
                  "c.fritype = b.fritype and c.fsec = b.fsec " +
                  "group by c.fctlid_e,c.fctlid_1,c.fctlid_i,c.fritype,c.fttycode,c.fttysec,c.fgrnet";

            sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                                vfbus, wfbus, vfintadj);

            SqlDataAdapter da8 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            da8.Fill(dtoriinvd_s);
            da8.Dispose();

            DataTable dttmpslt = PremDataFunc.GenPrmBord(dtoriinvd, dtoriinvd_s, dtpolh, dtorid1, dtmprdr,
                                    dtmtyinsr, vttytype);

            DataSet ds = new DataSet();
            ds.Tables.Add(dttmpslt);

            return ds;
        }

        public static DataSet IntPrmGrnd (DateTime vfbk_fr,DateTime vfbk_to,DateTime vlastasat,DateTime vthisasat,
                                          String vfbus,String wfbus,String vfintadj, String vfnew, dynamic progBar, dynamic waitlbl)
        {
            DataTable dtmprdr = new DataTable();
            DataTable dtnewbus = new DataTable();
            DataTable dtoinvh = new DataTable();
            DataTable dtpolh = new DataTable();
            DataTable dtoriinstl = new DataTable();
            DataTable dtoriinvf = new DataTable();
            DataTable dtoriinvlf = new DataTable();
            DataTable dtoinsint_e = new DataTable();

            String cmd = "select * from mprdr";
            SqlDataAdapter damprdr = new SqlDataAdapter(cmd, DBConnect.mstdbConn);

            damprdr.Fill(dtmprdr);
            damprdr.Dispose();

            if (progBar != null) { progBar.Value = 5; waitlbl.Text = progBar.Value.ToString().Trim()+"%"; progBar.Update(); waitlbl.Update(); }


            cmd = "select distinct fctlid_1,fctlid,fbkdate,fpolno,fendtno,'Y' as fnew " +
                  "from oinvh " +
                  "where fbkdate between {0} and {1} and finstall = 1 and " +
                  "(fbus like '{2}' or fbus like '{3}') and fintadj = '{4}'";

            string sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                                vfbus, wfbus, vfintadj);

            SqlDataAdapter danewbus = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            danewbus.Fill(dtnewbus);
            danewbus.Dispose();

            if (progBar != null) { progBar.Value = 10; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }

            cmd = "select * from {5} " +
                     "where {6} in " +
                     "(select distinct {7} from oinvh " +
                     "where ((fbkdate between {0} and {1}) or (feffto >= {0} and fefffr <= {1} and " +
                     "fbkdate <= {1}) or (fbkdate < {0} and fefffr >= {0})) and " +
                     "(fbus like '{2}' or fbus like '{3}') and fintadj = '{4}') union " +
                     "(select * from {5} " +
                     "where {6} in " +
                     "(select distinct b.{7} from oinvh b, oriinstl a " +
                     "where a.fbkdate between {0} and {1} and " +
                     "b.fctlid = a.fctlid_i and " +
                     "ltrim(rtrim(a.fritype)) <> '04' and ltrim(rtrim(a.fritype)) <> '06' and " +
                     "(a.fbus like '{2}' or a.fbus like '{3}') and b.fintadj = '{4}')) union " +
                     "(select * from {5} " +
                     "where {6} in " +
                     "(select distinct b.{7} from oinvh b, oriinvf a " +
                     "where a.fbkdate between {0} and {1} and b.fctlid = a.fctlid_i and " +
                     "ltrim(rtrim(a.fclass)) <> 'CPM' and ltrim(rtrim(a.fclass)) <> 'PAR' and " +
                     "(a.fbus like '{2}' or a.fbus like '{3}') and b.fintadj = '{4}')) union " +
                     "(select * from {5} " +
                     "where {6} in " +
                     "(select distinct b.{7} from oinvh b, oriinvlf a " +
                     "where a.fbkdate between {0} and {1} and b.fctlid = a.fctlid_i and " +
                     "(a.fbus like '{2}' or a.fbus like '{3}') and b.fintadj = '{4}'))";

            sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                    vfbus, wfbus, vfintadj, "oinvh", "fctlid", "fctlid");

            SqlDataAdapter daoinvh = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            daoinvh.SelectCommand.CommandTimeout = 90;

            daoinvh.Fill(dtoinvh);
            daoinvh.Dispose();

            if (progBar != null) { progBar.Value = 15; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }

            sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                        vfbus, wfbus, vfintadj, "polh", "fctlid", "fctlid_1");

            SqlDataAdapter dapolh = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            dapolh.SelectCommand.CommandTimeout = 90;

            dapolh.Fill(dtpolh);
            dapolh.Dispose();

            if (progBar != null) { progBar.Value = 20; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }
            if (progBar != null) { progBar.Value = 25; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }


            sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                vfbus, wfbus, vfintadj, "oriinstl", "fctlid_e", "fctlid_1");

            SqlDataAdapter daoriinstl = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            daoriinstl.SelectCommand.CommandTimeout = 90;

            daoriinstl.Fill(dtoriinstl);
            daoriinstl.Dispose();

            if (progBar != null) { progBar.Value = 30; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }
            if (progBar != null) { progBar.Value = 35; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }
            if (progBar != null) { progBar.Value = 40; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }

            sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                vfbus, wfbus, vfintadj, "oriinvf", "fctlid_e", "fctlid_1");

            SqlDataAdapter daoriinvf = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            daoriinvf.SelectCommand.CommandTimeout = 90;

            daoriinvf.Fill(dtoriinvf);
            daoriinvf.Dispose();

            if (progBar != null) { progBar.Value = 45; waitlbl.Text = progBar.Value.ToString(); progBar.Update(); waitlbl.Update(); }

            sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                    vfbus, wfbus, vfintadj, "oriinvlf", "fctlid_e", "fctlid_1");

            SqlDataAdapter daoriinvlf = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            daoriinvlf.SelectCommand.CommandTimeout = 90;

            daoriinvlf.Fill(dtoriinvlf);
            daoriinvlf.Dispose();

            if (progBar != null) { progBar.Value = 50; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }

            sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                    vfbus, wfbus, vfintadj, "oinsint_e", "fctlid_1", "fctlid_1");

            SqlDataAdapter daoinsint_e = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            daoinsint_e.SelectCommand.CommandTimeout = 90;

            daoinsint_e.Fill(dtoinsint_e);
            daoinsint_e.Dispose();

            if (progBar != null) { progBar.Value = 55; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }

            dtpolh.Columns.Add("fbkdate", typeof(DateTime));

            foreach (DataRow dr in dtpolh.Rows)
            {
                dtoinvh.DefaultView.RowFilter = String.Format("fctlid_1 = '{0}'", dr["fctlid"].ToString());
                for (int i = 0; i < dtoinvh.DefaultView.Count; i++)
                {
                    if (String.IsNullOrEmpty(dtoinvh.DefaultView[i]["fbkdate"].ToString()))
                        continue;

                    if (String.IsNullOrEmpty(dr["fbkdate"].ToString()))
                        dr["fbkdate"] = Convert.ToDateTime(dtoinvh.DefaultView[i]["fbkdate"]);

                    if (Convert.ToDateTime(dtoinvh.DefaultView[i]["fbkdate"]) < Convert.ToDateTime(dr["fbkdate"]))
                        dr["fbkdate"] = Convert.ToDateTime(dtoinvh.DefaultView[i]["fbkdate"]);
                }
            }

            if (progBar != null) { progBar.Value = 60; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }
            if (progBar != null) { progBar.Value = 65; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }

            dtoinvh.DefaultView.RowFilter = "";

            DataTable dtinvAZ = PremDataFunc.GenGrndInvh(dtoinvh, dtpolh, vthisasat);

            if (progBar != null) { progBar.Value = 70; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }
            if (progBar != null) { progBar.Value = 75; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }

            DataTable dtdir = PremDataFunc.GenGrndDirInfo(dtinvAZ, dtpolh, dtmprdr, vfbk_fr, vfbk_to, vlastasat);

            if (progBar != null) { progBar.Value = 80; waitlbl.Text = progBar.Value.ToString(); progBar.Update(); waitlbl.Update(); }

            DataTable dtriinvAZ = PremDataFunc.GenGrndRIInvh(dtinvAZ, dtoriinstl, dtoriinvf, dtoriinvlf, dtpolh,
                                                             vfbk_fr, vfbk_to, vlastasat);

            if (progBar != null) { progBar.Value = 85; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }

            PremDataFunc.MergeDirRIInfo(dtriinvAZ, dtdir, dtpolh, dtoinsint_e, dtnewbus, vfbk_fr, vfbk_to, vlastasat);
            if (progBar != null) { progBar.Value = 100; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }

            dtmprdr.Dispose();
            dtnewbus.Dispose();
            dtoinvh.Dispose();
            dtpolh.Dispose();
            dtoriinstl.Dispose();
            dtoriinvf.Dispose();
            dtoriinvlf.Dispose();
            dtoinsint_e.Dispose();
            dtinvAZ.Dispose();
            dtriinvAZ.Dispose();

            if (vfnew == "A")
                dtdir.DefaultView.RowFilter = "";
            else if (vfnew == "1")
                dtdir.DefaultView.RowFilter = String.Format("fnew = '{0}'", "Y");
            else
                dtdir.DefaultView.RowFilter = String.Format("fnew = '{0}'", "N");

            dtdir.DefaultView.Sort = "fclsseq,fpolno,fendtno";

            DataTable dtgrand = dtdir.DefaultView.ToTable(false, "fkind", "fbus","fclnt", "fsclnt", "fdbtr", "fclsseq", "fclass", "fsclass",
                                "ftrade", "ftrdseq", "fsec", "fpolno", "fendtno", "fcomtype", "fuwyr", "finstall", "ftotinstal", "fefffr",
                                "feffto", "flastasat", "fpolday_a", "funexp_a", "fthisasat", "fpolday_y", "funexp_y", "finsratio", "fgpm_p",
                                "fgpm_di", "fttypm_ri", "fxolpm_ri", "ffacpm_ri", "fricost_di", "fripm_ri", "fnetpm_i", "funernpm",
                                "gernpm", "rernpm", "fernpm", "ftdamt_di", "fcomamt_di", "fadjcom", "fttycom_ri", "ffaccom_ri", "fricom_ri",
                                "fadjricom", "fnetcom_i", "fdac", "fdricom", "fdacqcost", "fnetcompay", "funnpm_fy", "funnpm_fa", "finsd",
                                "fclsname", "acc_class", "acc_desc", "ftrdname", "pcode", "sec_desc", "fnew", "fcdesc"
                                );

            if (progBar != null) { progBar.Value = 70; waitlbl.Text = progBar.Value.ToString(); progBar.Update(); waitlbl.Update(); }

            if (dtgrand.Rows.Count == 0)
            {
                //DataRow dr = dtgrand.NewRow();
                //dr["Fclass"] = "???";
                //dtgrand.Rows.Add(dr);
            }

            DataSet ds = new DataSet();

            ds.Tables.Add(dtgrand);
            ds.Tables.Add(dtdir);

            return ds;
        }

        public static DataSet IntPrmAnly(DateTime vfbk_fr, DateTime vfbk_to, String vfbus, String wfbus, 
            String vrowfilter, String vford, String vfsumm)
        {
            DataTable dtmprdr = new DataTable();
            DataTable dtoinvh = new DataTable();
            DataTable dtoinvh_b = new DataTable();
            DataTable dtpolh = new DataTable();

            String cmd = "select * from mprdr";
            SqlDataAdapter da = new SqlDataAdapter(cmd, DBConnect.mstdbConn);

            da.Fill(dtmprdr);
            da.Dispose();

            dtmprdr.DefaultView.Sort = "fid,ftype";

            cmd = "select fctlid_1,min(finstall) as finstall,sum(fgpm) as mgpm,sum(ftdamt) as mtdamt,sum(fcomamt) as mcomamt," +
                "sum(fnpm) as mnpm,sum(flvyamt1) as mlvyamt1,sum(flvyamt2) as mlvyamt2,sum(flvyamt3) as mlvyamt3," +
                "'' as fnew,cast(0 as decimal) as agpm,cast(0 as decimal) as atdamt,cast(0 as decimal) as acomamt," +
                "cast(0 as decimal) as anpm,cast(0 as decimal) as alvyamt1,cast(0 as decimal) as alvyamt2," +
                "cast(0 as decimal) as alvyamt3,"+
                "'' as fpolno,'' as fendtno,'' as fclass, '' as fsclass,'' as fclsseq, '' as fclsname,"+
                "'' as ftrade,'' as ftrdesc,''as ftrdseq,'' as ftrdname,'' as fextend,cast(null as datetime) as fissdate,"+
                "cast(null as datetime) as fincfr,cast(null as datetime) as fincto,cast(null as datetime) as fefffr," +
                "cast(null as datetime) as feffto,'' as finsd,'' as finsd_t1,'' as finsd_t2,'' as fcontrt," +
                "'' as fcontrt_t1,'' as fsiteid,'' as fsite,'' as fsite_t1,'' as fsdesc,'' as fprdr,'' as fpdesc,"+
                "'' as fclnt,'' as finter,'' as fintdesc,'' as fpcode,'' as fsubside,'' as fsubsdesc,'' as fcdesc,"+
                "cast(0 as decimal) as fsum,cast(0 as decimal) as fupdsum,cast(0 as decimal) as fsi," +
                "cast(0 as decimal) as fupdsi,cast(0 as decimal) as fliab,cast(0 as decimal) as fupdliab," +
                "cast(0 as decimal) as pgpm,cast(0 as decimal) as ptdamt,cast(0 as decimal) as pcomamt," +
                "cast(0 as decimal) as plvyamt1,cast(0 as decimal) as plvyamt2,cast(0 as decimal) as plvyamt3," +
                "cast(0 as decimal) as pnpm,cast(0 as decimal) as plgpm,cast(0 as decimal) as pltdamt," +
                "cast(0 as decimal) as plcomamt,cast(0 as decimal) as plnpm,'' as fbus,cast(0 as decimal) as fpcnt," +
                "cast(0 as decimal) as fecnt,'' as stcode1,'' as sthd1,'' as stdesc1,'' as stft1,'' as stcode2,"+
                "'' as sthd2,'' as stdesc2,'' as stft2,'' as sumcode1,'' as sumdesc1,'' as sumcode1b,'' as sumdesc1b,"+
                "'' as sumcode2,'' as sumdesc2,'' as sumcode2b,'' as sumdesc2b,cast(0 as decimal) as msi," +
                "cast(0 as decimal) as mliab "+
                  "from oinvh where fbkdate between {0} and {1} and (fbus like '{2}' or fbus like '{3}') " +
                  "group by fctlid_1";

            string sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                                vfbus, wfbus);

            SqlDataAdapter da2 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            da2.Fill(dtoinvh);
            da2.Dispose();

            cmd = "select a.fctlid_1,sum(a.fgpm) as agpm,sum(a.ftdamt) as atdamt,sum(a.fcomamt) acomamt,sum(a.fnpm) as anpm," +
                "sum(a.flvyamt1) as alvyamt1,sum(a.flvyamt2) as alvyamt2,sum(a.flvyamt3) as alvyamt3 " +
                  "from oinvh a, oinvh b where a.fbkdate <= {1} and a.fctlid_1 = b.fctlid_1 and " +
                  "b.fbkdate between {0} and {1} and (b.fbus like '{2}' or b.fbus like '{3}') " +
                  "group by a.fctlid_1";

            sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                                vfbus, wfbus);

            SqlDataAdapter da3 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            da3.Fill(dtoinvh_b);
            da3.Dispose();

            cmd = "select b.fctlid,b.fpolno,b.fendtno,b.fclass,b.fsclass,b.ftrade,b.ftrdesc,b.fextend,b.fissdate," +
                "b.fincfr,b.fincto,b.fefffr,b.feffto,b.finsd,b.finsd_t1,b.finsd_t2,b.fcontrt,b.fcontrt_t1,b.fsiteid," +
                "b.fsite,b.fsite_t1,b.fprdr,b.fclnt,b.fsum,b.fupdsum,b.fsi,b.fupdsi,b.fliab,b.fupdliab,b.fgpm,"+
                "b.ftdamt+b.ftdadj as ftdamt,b.fcomamt+b.fcomadj as fcomamt,b.fnpm,b.flvyamt1+b.flvyadj1 as flvyamt1," +
                "b.flvyamt2,b.flvyamt3,b.flgpm,b.fltdamt,b.flcomamt,b.flnpm,b.fbus " +
                "from oinvh a, polh b " +
                "where a.fbkdate between {0} and {1} and (a.fbus like '{2}' or a.fbus like '{3}') "+
                "and a.fctlid_1 = b.fctlid";

            sqlString = string.Format(cmd, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                    vfbus, wfbus);

            SqlDataAdapter da4 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            da4.Fill(dtpolh);
            da4.Dispose();

            DataTable dttmpslt = PremDataFunc.GenPrmAnly(dtoinvh, dtoinvh_b, dtmprdr, dtpolh, vford, vfsumm);

            dttmpslt.DefaultView.RowFilter = vrowfilter;
            dttmpslt.DefaultView.Sort = "stcode1,stcode2,fpolno,fendtno";

            DataTable dtresult = dttmpslt.DefaultView.ToTable();

            if (dtresult.Rows.Count == 0)
            {
                //DataRow dr = dtresult.NewRow();
                //dr["Fclass"] = "???";
                //dtresult.Rows.Add(dr);
            }

            DataSet ds = new DataSet();
            ds.Tables.Add(dtresult);

            return ds;
        }

        public static DataSet IntPrmReg(DateTime vfbk_fr, DateTime vfbk_to, String vfbus, String wfbus, String vfintadj, String vperiodtype,
                                        String rpOptid, String vrowfilter)
        {
            DataTable dtmprdr = new DataTable();
            DataTable dtmlvydet = new DataTable();
            DataTable dtoinvh = new DataTable();
            DataTable dtpolh = new DataTable();

            String cmd = "select * from mprdr";
            SqlDataAdapter da = new SqlDataAdapter(cmd, DBConnect.mstdbConn);

            da.Fill(dtmprdr);
            da.Dispose();

            dtmprdr.DefaultView.Sort = "fid,ftype";
            
            String dateFieldName = vperiodtype == "1" ? "fbilldate" : "fbkdate";

            String baseDrCr = "polprm".Contains(rpOptid) ? "2" : "1";

            cmd = "select a.flvytype,a.fefffr,a.feffto,a.factive,a.frate,a.fapplymax,a.fupperlmt,b.fclass " +
                  "from mlvyhd a, mlvyclass b WHERE a.fctlid = b.fctlid_hd and a.factive = '1' ";

            SqlDataAdapter da2 = new SqlDataAdapter(cmd, DBConnect.mstdbConn);

            da2.Fill(dtmlvydet);
            da2.Dispose();

            dtmlvydet.DefaultView.Sort = "feffto desc";

            cmd = "select fctlid_1,fctlid,fbus,fpolno,fendtno,fsrcref,fcedepol,fcedeendt,fuwyr,fclass,'' as fclsseq," +
                  "'' as fclsname, fsclass,facclass,finstall,ftotinstal,fissdate,finvdate,fbilldate,fbkdate,fefffr," +
                  "feffto,fdoctype,fcomtype,finvno,fdbtrtype,fdbtr,fdbtrtype+fdbtr as fdcode,'' as fdesc,fprdr,fpdesc," +
                  "'' as finter,'' as fintdesc,'' as fpcode,''as fsubside,'' as fsubsdesc, fclnt, '' as fcdesc,fsclnt," +
                  "fpmcur,fgpm, case when fcomtype = '2' then 0 else ftdamt end as ftdamt," +
                  "fgpm-ftdamt as fpayable,fcomamt, case when fcomtype = '2' then ftdamt else 0 end as fricost," +
                  "fnpm,flvyamt1,flvyamt2,flvyamt3,flvyamta,famount,fremark," +
                  "'' as fextend,'' as ftype,'' as fautolvy,'' as fautoedate,cast(null as datetime) as fendtdate,"+  //
                  "cast(0 as decimal) as flvya,cast(null as datetime) as fincfr," +  //
                  "cast(null as datetime) as fanvdate,cast(0 as decimal) as fsysrate,"+  //
                  "cast(0 as decimal) as fsyslmt," +  //
                  "cast(0 as decimal) as fsysamta,cast(0 as decimal) as flvydiffa,'' as plvya " +  //
                  "from oinvh where {0} between {1} and " +  //
                  "{2} and (fbus like '{3}' or fbus like '{4}') and fintadj = '{5}'"; //

            string sqlString = string.Format(cmd, dateFieldName, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to),
                                vfbus, wfbus, vfintadj);

            SqlDataAdapter da3 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            da3.Fill(dtoinvh);
            da3.Dispose();

            cmd = "select a.fctlid_1,a.fctlid,b.finsd,b.fcomtype,b.fgpm,b.flgpm,"+
                         "case when b.fcomtype = '2' then 0 else b.ftdamt+b.ftdadj end as ftdamt,"+
                         "case when b.fcomtype = '2' then b.ftdamt+b.ftdadj else 0 end as fricost," +
                         "b.fltdamt,b.fgpm-b.ftdamt-b.ftdadj as fpayable,"+
                         "b.fcomamt+b.fcomadj as fcomamt,b.flcomamt," +
                         "b.fnpm,b.flnpm,"+
                         "b.flvyamt1+b.flvyadj1 as flvyamt1,b.flvyamt2,b.flvyamt3,b.flvyamta,"+
                         "b.fgpm-b.ftdamt-b.ftdadj-b.fcomamt-b.fcomadj+b.flvyamt1+b.flvyadj1+b.flvyamt2+b.flvyamt3+b.flvyamta as famount,"+
                         "b.fextend,b.ftype,b.fautolvy,b.fautoedate,b.fendtdate,b.flvya,c.fincfr " +
                         "from oinvh a, polh b, polh c " +
                         "where a.{0} between {1} and {2} and (a.fbus like '{3}' or a.fbus like '{4}') and " +
                         "a.fctlid_1 = b.fctlid and a.fintadj = '{5}' and b.fctlid_p = c.fctlid";

            sqlString = string.Format(cmd, dateFieldName, csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to), vfbus, wfbus, vfintadj);

            SqlDataAdapter da4 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            da4.Fill(dtpolh);
            da4.Dispose();

            DataTable dttmp = PremDataFunc.GenPrmReg(dtoinvh, dtmprdr, dtpolh, dtmlvydet,rpOptid, baseDrCr), dttmpslt;

            dttmp.DefaultView.RowFilter = "polprm".Contains(rpOptid) ? "finstall = 1" : "";
            dttmpslt = dttmp.DefaultView.ToTable();

            if ("prmreg|polprm".Contains(rpOptid))
                dttmpslt.DefaultView.Sort = "fclsseq,fpolno,fendtno";
            else if (rpOptid == "prmdrcr")
                dttmpslt.DefaultView.Sort = "fdbtrtype,fdbtr,finvno";
            else if (rpOptid == "prmrel")
                dttmpslt.DefaultView.Sort = "fpcode";

            dttmpslt.DefaultView.RowFilter = vrowfilter;

            DataTable dtresult = dttmpslt.DefaultView.ToTable();

            if (dtresult.Rows.Count == 0)
            {
                //DataRow dr = dtresult.NewRow();
                //dr["Fclass"] = "???";
                //dtresult.Rows.Add(dr);
            }
           
            DataSet ds = new DataSet();
            ds.Tables.Add(dtresult);

            return ds;
        }

        public static DataSet IntPrmStat(DataTable dtgrand, DateTime vfbk_to)
        {
            DataTable dtminsc = new DataTable();

            String cmd = "select fid as fclass,'' as fsclass,'' as fclsseq,'' as fclsname,"+
                         "cast(0 as decimal) as famt1,"+
                         "cast(0 as decimal) as famt2,cast(0 as decimal) as famt3,cast(0 as decimal) as famt4,"+
                         "cast(0 as decimal) as famt5,cast(0 as decimal) as famt6,cast(0 as decimal) as famt7,"+
                         "cast(0 as decimal) as famt8,cast(0 as decimal) as famt8b,cast(0 as decimal) as famt8c from minsc";

            SqlDataAdapter da = new SqlDataAdapter(cmd, DBConnect.mstdbConn);

            da.Fill(dtminsc);
            da.Dispose();

            DataTable dtgpm = PremDataFunc.GenPrmStat(dtminsc, dtgrand, vfbk_to, "1");
            DataTable dtnpm = PremDataFunc.GenPrmStat(dtminsc, dtgrand, vfbk_to, "2");

            DataSet ds = new DataSet();

            ds.Tables.Add(dtgpm);
            ds.Tables.Add(dtnpm);

            return ds;
        }

        public static DataSet IntPrmUnEarn(DataTable dtdir)
        {
            dtdir.DefaultView.RowFilter = "";

            DataTable dtunernwrk = dtdir.DefaultView.ToTable(false, "fctlid_p", "fctlid_e", "fctlid_v", "fbus","fclnt", "fsclnt", "fdbtr", "fkind",
                     "fclsseq", "fclass", "fsclass", "fsec", "finstall", "ftotinstal", "fpolno", "fendtno", "fuwyr", "finsd",
                     "fsite", "fefffr", "feffto", "flastasat", "fincfr_a", "fincto_a", "fpolday_a", "funexp_a", "fexpday_a",
                     "fthisasat", "fincfr_y", "fincto_y", "fpolday_y", "funexp_y", "fexpday_y", "finsratio", "fgpm_p", "ftdamt_p",
                     "fcomamt_p", "fgpm_ia", "ftdamt_ia", "fricost_ia", "fcomamt_ia", "fgpm_iy", "ftdamt_iy", "fricost_iy",
                     "fcomamt_iy", "frtnpm_ra", "fttypm_ra", "fxolpm_ra", "ffacpm_ra", "ffobpm_ra", "frtncom_ra", "fttycom_ra",
                     "ffaccom_ra", "ffobcom_ra", "frtnpm_ry", "fttypm_ry", "fxolpm_ry", "ffacpm_ry", "ffobpm_ry", "frtncom_ry",
                     "fttycom_ry", "ffaccom_ry", "ffobcom_ry", "fripm_ra", "fxripm_ra", "fricom_ra", "fripm_ry", "fxripm_ry",
                     "fricom_ry", "gernpm_y", "gernpm_a", "rernpm_y", "rernpm_a",
                     "fernpm_y", "fernpm_a", "fexpcom_y", "fexpcom_a", "fexpcom_ry", "fexpcom_ra",
                     "fnetpm_y", "fnetpm_a", "fnetcom_a", "fnetcom_y", "fclsname",
                     "acc_class", "acc_desc", "pcode", "sec_desc",
                     "fgpm_y", "fgpm_a", "fripm_y", "fripm_a", "fcomamt_y", "fcomamt_a", "fricom_y", "fricom_a",
                     "gernpm_fy", "gernpm_fa", "rernpm_fy", "rernpm_fa", "fernpm_fy", "fernpm_fa",
                     "fexpcom_fy", "fexpcom_fa", "ericom_fy", "ericom_fa", "fcdesc"
                    );

            PremDataFunc.RefineUnearnData(dtunernwrk);

            DataTable dtunern = dtunernwrk.DefaultView.ToTable(false, "fctlid_p", "fctlid_e", "fctlid_v", "fbus","fclnt", "fsclnt", "fdbtr",
                     "fkind", "fclsseq", "fclass", "fsclass", "fsec", "finstall", "ftotinstal", "fpolno", "fendtno", "fuwyr",
                     "fefffr", "feffto", "flastasat", "fincfr_a", "fincto_a", "fpolday_a", "funexp_a", "fexpday_a",
                     "fthisasat", "fincfr_y", "fincto_y", "fpolday_y", "funexp_y", "fexpday_y", "finsratio", "fgpm_p", "fernpm_y",
                     "fernpm_a", "fgpm_y", "fgpm_a", "fricost_y", "fricost_a", "fripm_y", "fripm_a", "fxolpm_y", "fxolpm_a", "fxripm_y",
                     "fxripm_a", "fcomamt_y", "fcomamt_a", "fricom_y", "fricom_a", "fnetpm_y", "fnetpm_a", "gernpm_fy", "gernpm_fa",
                     "rernpm_fy", "rernpm_fa", "xernpm_fy", "xernpm_fa", "rxernpm_fy", "rxernpm_fa", "fernpm_fy", "fernpm_fa",
                     "fnetcom_a", "fnetcom_y", "fexpcom_fy", "fexpcom_fa", "ericom_fy", "ericom_fa", "enetcom_fy", "enetcom_fa",
                     "finsd", "fclsname", "acc_class", "acc_desc", "pcode", "sec_desc", "fcdesc"
                    );

            DataTable dtunerndetail = PremDataFunc.GenUnErnDetail(dtunern);
            DataTable dtunernsummry = PremDataFunc.GenUnErnSummary(dtunerndetail);

            if (dtunerndetail.Rows.Count == 0)
            {
                //DataRow dr = dtunerndetail.NewRow();
                //dr["Fclass"] = "???";
                //dtunerndetail.Rows.Add(dr);
            }

            if (dtunernsummry.Rows.Count == 0)
            {
                //DataRow dr = dtunernsummry.NewRow();
                //dr["Fclass"] = "???";
                //dtunernsummry.Rows.Add(dr);
            }

            DataSet ds = new DataSet();

            ds.Tables.Add(dtunernsummry);
            ds.Tables.Add(dtunerndetail);

            return ds;
        }

        public static DataSet IntPrmUnIv(DateTime vfbk_fr, DateTime vfbk_to, DateTime vlastasat, DateTime vthisasat,
                                  String vfbus, String wfbus, String vfstatus)
        {
            DataTable dtoinvh = new DataTable();
            DataTable dtoinvh2 = new DataTable();
            DataTable dtoriinstl = new DataTable();
            DataTable dtoriinstl2 = new DataTable();
            DataTable dtpolh = new DataTable();
            DataTable dtorih = new DataTable();
            DataTable dtxol = new DataTable();

            String cmd;

            cmd = "select fctlid_1,fctlid,fpolno,fendtno,finvno,finvdate,fbkdate,finstall,ftotinstal," +
                    "fclass,fgpm,ftdamt,fcomamt,fnpm,flvyamt1,flvyamt2,flvyamt3,fbus " +
                    "from oinvh " +
                    "where fstatus >= '{0}' and fissdate <= {1} and famount <> 0.00 and " +
                    "(fbus like '{2}' or fbus like '{3}') and (fbkdate is null or fbkdate > {1}) " +
                    "and fctlid_1 not in " +
                    "(select distinct fctlid_1 from oinvh " +
                    "where fstatus >= '{0}' and fissdate <= {1} and famount <> 0.00 and finstall = 1 and " +
                    "(fbus like '{2}' or fbus like '{3}') and (fbkdate is null or fbkdate > {1})) ";

            string sqlString = string.Format(cmd, vfstatus, csCommon.DbDateFormat(vlastasat), vfbus, wfbus);

            SqlDataAdapter daoinvh = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            daoinvh.Fill(dtoinvh);
            daoinvh.Dispose();

            sqlString = string.Format(cmd, vfstatus, csCommon.DbDateFormat(vthisasat), vfbus, wfbus);

            SqlDataAdapter daoinvh2 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            daoinvh2.Fill(dtoinvh2);
            daoinvh2.Dispose();

            cmd = "select	fctlid_i,fctlid,fkind,fmodule,fbkdate,fgpm,fcomamt,fpayable " +
                    "from oriinstl " +
                    "where fctlid_i in " +
                    "(select fctlid from oinvh " +
                    "where fstatus >= '{0}' and fissdate <= {1} and famount <> 0.00 and " +
                    "(fbus like '{2}' or fbus like '{3}') and (fbkdate is null or fbkdate > {4}))";

            sqlString = string.Format(cmd, vfstatus, csCommon.DbDateFormat(vthisasat), vfbus, wfbus,
                                      csCommon.DbDateFormat(vlastasat));
            SqlDataAdapter daoriinstl = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            daoriinstl.Fill(dtoriinstl);

            cmd = "select	fctlid_e,fctlid_i,fctlid,fkind,fmodule,fbkdate,fgpm,fcomamt,fpayable " +
                    "from oriinstl " +
                    "where fctlid_e in " +
                    "(select distinct fctlid_1 from oinvh " +
                    "where fstatus >= '{0}' and fissdate <= {1} and famount <> 0.00 and " +
                    "(fbus like '{2}' or fbus like '{3}') and (fbkdate is null or fbkdate > {1})) and " +
                    "fctlid_i not in " +
                    "(select fctlid from oinvh " +
                    "where fstatus >= '{0}' and fissdate <= {1} and famount <> 0.00 and " +
                    "(fbus like '{2}' or fbus like '{3}') and (fbkdate is null or fbkdate > {1}))";

            sqlString = string.Format(cmd, vfstatus, csCommon.DbDateFormat(vthisasat), vfbus, wfbus);

            SqlDataAdapter daoriinstl2 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            daoriinstl2.Fill(dtoriinstl2);

            cmd = "select	fctlid,fctlid_p,fctlid_v,fbus,fclass,fsclass,fpolno,fendtno," +
                    "fsum,fuwyr,finsd,fsite,fincfr,fincto,fefffr,feffto,fextend,fgpm,flgpm," +
                    "ftdamt,fltdamt,fcomamt,flcomamt " +
                    "from polh " +
                    "where fctlid in " +
                    "(select distinct fctlid_1 from oinvh " +
                    "where fstatus >= '{0}' and fissdate <= {1} and famount <> 0.00 and " +
                    "(fbus like '{2}' or fbus like '{3}') and (fbkdate is null or fbkdate > {4}))";

            sqlString = string.Format(cmd, vfstatus, csCommon.DbDateFormat(vthisasat), vfbus, wfbus,
                                      csCommon.DbDateFormat(vlastasat));

            SqlDataAdapter daopolh = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            daopolh.Fill(dtpolh);

            cmd = "select fctlid,fctlid_1,fpolno,fendtno,fkind,fxol " +
                    "from orih " +
                    "where fctlid_1 in " +
                    "(select distinct fctlid_1 from oinvh " +
                    "where fstatus >= '{0}' and fissdate <= {1} and famount <> 0.00 and " +
                    "(fbus like '{2}' or fbus like '{3}') and (fbkdate is null or fbkdate > {4}))";

            sqlString = string.Format(cmd, vfstatus, csCommon.DbDateFormat(vthisasat), vfbus, wfbus,
                                      csCommon.DbDateFormat(vlastasat));

            SqlDataAdapter daorih = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
            daorih.Fill(dtorih);

            cmd = "select a.fid as fttycode,a.fefffr,a.feffto," +
                  "sum(round(b.fprate*b.fadjrate/100*(1-b.fdrate/100),8)) as fprate " +
                  "from mxl a, mxllayr b " +
                  "where b.fctlid_1 = a.fctlid " +
                  "group by a.fid, a.fefffr,a.feffto";

            SqlDataAdapter daxol = new SqlDataAdapter(cmd, DBConnect.mstdbConn);
            daxol.Fill(dtxol);

            DataTable dttmppolh = PremDataFunc.GenUnInvPolh(dtpolh, dtorih);
            DataTable dttmpbrkdn = PremDataFunc.GenUnInvBrkDn(dtoinvh, dttmppolh, dtoriinstl, dtxol, vlastasat,
                                                               vfbk_fr, vfbk_to);

            DataTable dttmpbrkdn2 = PremDataFunc.GenUnInvBrkDn(dtoinvh2, dttmppolh, dtoriinstl, dtxol, vthisasat,
                                                   vfbk_fr, vfbk_to);

            DataTable dtunidet = PremDataFunc.GenUnInvPrem(dttmpbrkdn, dttmpbrkdn2);

            dtunidet.DefaultView.Sort = "fclsseq,fpolno,fendtno,finstall";

            DataTable dtunisum = PremDataFunc.GenUnInvSum(dtunidet, vlastasat, vthisasat);

            DataView dvunisum = dtunisum.DefaultView;

            dvunisum.Sort = "fclsseq,frectype";
            dvunisum.RowFilter = "";

            DataTable dtresult = new DataTable();

            dtresult = dvunisum.ToTable();

            if (dtunidet.Rows.Count == 0)
            {
                //DataRow dr = dtunidet.NewRow();
                //dr["Fclass"] = "???";
                //dtunidet.Rows.Add(dr);
            }

            if (dtresult.Rows.Count == 0)
            {
                //DataRow dr = dtresult.NewRow();
                //dr["Fclass"] = "???";
                //dtresult.Rows.Add(dr);
            }

            DataSet ds = new DataSet();
            ds.Tables.Add(dtresult);
            ds.Tables.Add(dtunidet);

            return ds;
        }

        //public static DataSet IntPrmUnIv(DateTime vfbk_fr, DateTime vfbk_to, DateTime vlastasat, DateTime vthisasat,
        //                                  String vfbus, String wfbus, String vfstatus)
        //{
        //    DataTable dtoinvh = new DataTable();
        //    DataTable dtoinvh2 = new DataTable();
        //    DataTable dtoriinstl = new DataTable();
        //    DataTable dtoriinstl2 = new DataTable();
        //    DataTable dtpolh = new DataTable();
        //    DataTable dtorih = new DataTable();
        //    DataTable dtxol = new DataTable();

        //    String cmd;

        //    cmd = "select fctlid_1,fctlid,fpolno,fendtno,finvno,finvdate,fbkdate,finstall,ftotinstal," +
        //            "fclass,fgpm,ftdamt,fcomamt,fnpm,flvyamt1,flvyamt2,flvyamt3,fbus " +
        //            "from oinvh " +
        //            "where fstatus = '{0}' and fissdate <= {1} and famount <> 0.00 and " +
        //            "(fbus like '{2}' or fbus like '{3}') and (fbkdate is null or fbkdate > {1}) " +
        //            "and fctlid_1 not in " +
        //            "(select distinct fctlid_1 from oinvh " +
        //            "where fstatus = '{0}' and fissdate <= {1} and famount <> 0.00 and finstall = 1 and " +
        //            "(fbus like '{2}' or fbus like '{3}') and (fbkdate is null or fbkdate > {1})) ";

        //    string sqlString = string.Format(cmd, vfstatus, csCommon.DbDateFormat(vlastasat), vfbus, wfbus);

        //    SqlDataAdapter daoinvh = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
        //    daoinvh.Fill(dtoinvh);
        //    daoinvh.Dispose();

        //    sqlString = string.Format(cmd, vfstatus, csCommon.DbDateFormat(vthisasat), vfbus, wfbus);

        //    SqlDataAdapter daoinvh2 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
        //    daoinvh2.Fill(dtoinvh2);
        //    daoinvh2.Dispose();

        //    cmd = "select	fctlid_i,fctlid,fkind,fmodule,fbkdate,fgpm,fcomamt,fpayable " +
        //            "from oriinstl " +
        //            "where fctlid_i in " +
        //            "(select fctlid from oinvh " +
        //            "where fstatus = '{0}' and fissdate <= {1} and famount <> 0.00 and " +
        //            "(fbus like '{2}' or fbus like '{3}') and (fbkdate is null or fbkdate > {4}))";

        //    sqlString = string.Format(cmd, vfstatus, csCommon.DbDateFormat(vthisasat), vfbus, wfbus,
        //                              csCommon.DbDateFormat(vlastasat));
        //    SqlDataAdapter daoriinstl = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
        //    daoriinstl.Fill(dtoriinstl);

        //    cmd = "select	fctlid_e,fctlid_i,fctlid,fkind,fmodule,fbkdate,fgpm,fcomamt,fpayable " +
        //            "from oriinstl " +
        //            "where fctlid_e in " +
        //            "(select distinct fctlid_1 from oinvh " +
        //            "where fstatus = '{0}' and fissdate <= {1} and famount <> 0.00 and " +
        //            "(fbus like '{2}' or fbus like '{3}') and (fbkdate is null or fbkdate > {1})) and " +
        //            "fctlid_i not in " +
        //            "(select fctlid from oinvh " +
        //            "where fstatus = '{0}' and fissdate <= {1} and famount <> 0.00 and " +
        //            "(fbus like '{2}' or fbus like '{3}') and (fbkdate is null or fbkdate > {1}))";

        //    sqlString = string.Format(cmd, vfstatus, csCommon.DbDateFormat(vthisasat), vfbus, wfbus);

        //    SqlDataAdapter daoriinstl2 = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
        //    daoriinstl2.Fill(dtoriinstl2);

        //    cmd = "select	fctlid,fctlid_p,fctlid_v,fbus,fclass,fsclass,fpolno,fendtno," +
        //            "fsum,fuwyr,finsd,fsite,fincfr,fincto,fefffr,feffto,fextend,fgpm,flgpm," +
        //            "ftdamt,fltdamt,fcomamt,flcomamt " +
        //            "from polh " +
        //            "where fctlid in " +
        //            "(select distinct fctlid_1 from oinvh " +
        //            "where fstatus = '{0}' and fissdate <= {1} and famount <> 0.00 and " +
        //            "(fbus like '{2}' or fbus like '{3}') and (fbkdate is null or fbkdate > {4}))";

        //    sqlString = string.Format(cmd, vfstatus, csCommon.DbDateFormat(vthisasat), vfbus, wfbus,
        //                              csCommon.DbDateFormat(vlastasat));

        //    SqlDataAdapter daopolh = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
        //    daopolh.Fill(dtpolh);

        //    cmd = "select fctlid,fctlid_1,fpolno,fendtno,fkind,fxol " +
        //            "from orih " +
        //            "where fctlid_1 in " +
        //            "(select distinct fctlid_1 from oinvh " +
        //            "where fstatus = '{0}' and fissdate <= {1} and famount <> 0.00 and " +
        //            "(fbus like '{2}' or fbus like '{3}') and (fbkdate is null or fbkdate > {4}))";

        //    sqlString = string.Format(cmd, vfstatus, csCommon.DbDateFormat(vthisasat), vfbus, wfbus,
        //                              csCommon.DbDateFormat(vlastasat));

        //    SqlDataAdapter daorih = new SqlDataAdapter(sqlString, DBConnect.opdbConn);
        //    daorih.Fill(dtorih);

        //    cmd = "select a.fid as fttycode,a.fefffr,a.feffto," +
        //          "sum(round(b.fprate*b.fadjrate/100*(1-b.fdrate/100),8)) as fprate " +
        //          "from mxl a, mxllayr b " +
        //          "where b.fctlid_1 = a.fctlid " +
        //          "group by a.fid, a.fefffr,a.feffto";

        //    SqlDataAdapter daxol = new SqlDataAdapter(cmd, DBConnect.mstdbConn);
        //    daxol.Fill(dtxol);

        //    DataTable dttmppolh = PremDataFunc.GenUnInvPolh(dtpolh, dtorih);
        //    DataTable dttmpbrkdn = PremDataFunc.GenUnInvBrkDn(dtoinvh, dttmppolh, dtoriinstl, dtxol, vlastasat,
        //                                                       vfbk_fr, vfbk_to);

        //    DataTable dttmpbrkdn2 = PremDataFunc.GenUnInvBrkDn(dtoinvh2, dttmppolh, dtoriinstl, dtxol, vthisasat,
        //                                           vfbk_fr, vfbk_to);

        //    DataTable dtunidet = PremDataFunc.GenUnInvPrem(dttmpbrkdn, dttmpbrkdn2);

        //    dtunidet.DefaultView.Sort = "fclsseq,fpolno,fendtno,finstall";

        //    DataTable dtunisum = PremDataFunc.GenUnInvSum(dtunidet, vlastasat, vthisasat);

        //    DataView dvunisum = dtunisum.DefaultView;

        //    dvunisum.Sort = "fclsseq,frectype";
        //    dvunisum.RowFilter = "";

        //    DataTable dtresult = new DataTable();

        //    dtresult = dvunisum.ToTable();

        //    if (dtunidet.Rows.Count == 0)
        //    {
        //        //DataRow dr = dtunidet.NewRow();
        //        //dr["Fclass"] = "???";
        //        //dtunidet.Rows.Add(dr);
        //    }

        //    if (dtresult.Rows.Count == 0)
        //    {
        //        //DataRow dr = dtresult.NewRow();
        //        //dr["Fclass"] = "???";
        //        //dtresult.Rows.Add(dr);
        //    }

        //    DataSet ds = new DataSet();
        //    ds.Tables.Add(dtresult);
        //    ds.Tables.Add(dtunidet);

        //    return ds;
        //}
    }
}
