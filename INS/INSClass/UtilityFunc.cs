using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace INS.INSClass
{
    public class UtilityFunc
    {
        private static Int32[] G_EncodeArr = { 015, 052, 007, 057, 053, 002, 041, 073, 067, 040, 042, 085,
                                               025, 088, 043, 090, 027, 026, 092, 089, 014, 093, 008, 072,
                                               016, 058, 004, 056, 054, 001, 074, 028, 066, 084, 029, 086,
                                               024, 068, 044, 030, 045, 036, 069, 051, 013, 070, 009, 071,
                                               017, 059, 006, 055, 018, 003, 075, 019, 065, 081, 091, 048,
                                               023, 087, 083, 031, 046, 035, 062, 037, 012, 061, 010, 049,
                                               038, 060, 005, 078, 077, 039, 076, 020, 064, 082, 079, 021,
                                               022, 080, 063, 032, 047, 034, 033, 050, 011 };

        private static Int32[] G_DecodeArr = { 030, 006, 054, 027, 075, 051, 003, 023, 047, 071, 093, 069,
                                               045, 021, 001, 025, 049, 053, 056, 080, 084, 085, 061, 037,
                                               013, 018, 017, 032, 035, 040, 064, 088, 091, 090, 066, 042,
                                               068, 073, 078, 010, 007, 011, 015, 039, 041, 065, 089, 060,
                                               072, 092, 044, 002, 005, 029, 052, 028, 004, 026, 050, 074,
                                               070, 067, 087, 081, 057, 033, 009, 038, 043, 046, 048, 024,
                                               008, 031, 055, 079, 077, 076, 083, 086, 058, 082, 063, 034,
                                               012, 036, 062, 014, 020, 016, 059, 019, 022};

        private static String G_Para_String = "CHINA OVERSEAS";
        private static Int32 G_Para_Int = 3268 + 4;
        public static String[,] G_Subsiarr = { { "ALL", "A" }, { "COLI", "1" }, { "COXX", "2" }, { "Misc", "3" } };
        public const String G_Misclnt = "CLM001";

        public static bool HasChinese(String str)
        {
            return Regex.IsMatch(str, @"[\u4e00-\u9fa5]");
        }

        public static String EnCode_Str(String input_str, Boolean is_encode)
        {
            String Input_str_trim = input_str, Result_str = "";
            int Str_len = input_str.Length;    // m_len1
            int Para_string_len = G_Para_String.Length; // m_len2
            Int32 Offset = 0;

            char c;
            Int32 Ascii_no, Ascii_rslt;
            int Subscript;

            if (is_encode)
                for (int i = 0; i != Str_len; i++)
                {
                    Ascii_no = (int)Input_str_trim[i];
                    Subscript = (i + 1) % Para_string_len;

                    if (Subscript == 0)
                        Subscript = Para_string_len - 1;
                    else
                        Subscript--;

                    Subscript = Ascii_no + (int)G_Para_String[Subscript] + G_Para_Int + Offset * Offset;
                    Subscript = Subscript % 93;

                    if (Subscript == 0)
                        Subscript = 93 - 1;
                    else
                        Subscript--;

                    Ascii_rslt = G_EncodeArr[Subscript] + 31;

                    c = (char)(Ascii_rslt);
                    Result_str = Result_str + c.ToString();
                    Offset = Offset + Ascii_no;
                }
            else
                for (int i = 0; i != Str_len; i++)
                {
                    Ascii_no = (int)Input_str_trim[i] - 1;

                    Ascii_no = G_DecodeArr[Ascii_no - 31];
                    Subscript = (i + 1) % Para_string_len;

                    if (Subscript == 0)
                        Subscript = Para_string_len - 1;
                    else
                        Subscript--;

                    Ascii_no = Ascii_no - ((int)G_Para_String[Subscript] + G_Para_Int + Offset * Offset) % 93;

                    while (Ascii_no <= 31)
                    {
                        Ascii_no += 93;
                    }

                    c = (char)Ascii_no;
                    Result_str = Result_str + c.ToString();
                    Offset = Offset + Ascii_no;
                };

            return Result_str;
        }
        public static String ClsSeq(String class_str, String sclass_str)
        {
            String lc_result = "UN";

            if (class_str.Trim() == "CAR" && sclass_str.Trim() == "SEC1")
                lc_result = "01";
            else if (class_str.Trim() == "CAR" && sclass_str.Trim() == "SEC2")
                lc_result = "02";
            else if (class_str.Trim() == "CAR")
                lc_result = "03";
            else if (class_str.Trim() == "EEC" && sclass_str.Trim() == "CONTRACTOR")
                lc_result = "04";
            else if (class_str.Trim() == "EEC" && sclass_str.Trim() == "HOTEL")
                lc_result = "05";
            else if (class_str.Trim() == "EEC")
                lc_result = "06";
            else if (class_str.Trim() == "CGL")
                lc_result = "07";
            else if (class_str.Trim() == "CPM")
                lc_result = "08";
            else if (class_str.Trim() == "MYP")
                lc_result = "09";
            else if (class_str.Trim() == "PAR")
                lc_result = "10";
            else if (class_str.Trim() == "FGP")
                lc_result = "11";
            else if (class_str.Trim() == "PMP")
                lc_result = "12";
            else if (class_str.Trim() == "CMP")
                lc_result = "13";
            else if (class_str.Trim() == "CSB")
                lc_result = "14";
            else if (class_str.Trim() == "PII")
                lc_result = "15";
            else if (class_str.Trim() == "GLF")
                lc_result = "16";
            else if (class_str.Trim() == "CLL")
                lc_result = "17";

            return lc_result;
        }

        public static String ClsName(String class_str, String sclass_str)
        {
            String lc_result = "Undefined".PadRight(35, ' ');

            if (class_str.Trim() == "CAR" && sclass_str.Trim() == "SEC1")
                lc_result = "CAR - Section 1".PadRight(35, ' ');
            else if (class_str.Trim() == "CAR" && sclass_str.Trim() == "SEC2")
                lc_result = "CAR - Section 2".PadRight(35, ' ');
            else if (class_str.Trim() == "CAR")
                lc_result = "Contractors All Risks".PadRight(35, ' ');
            else if (class_str.Trim() == "EEC" && sclass_str.Trim() == "CONTRACTOR")
                lc_result = "EEC - Construction".PadRight(35, ' ');
            else if (class_str.Trim() == "EEC" && sclass_str.Trim() == "HOTEL")
                lc_result = "EEC - Hotel".PadRight(35, ' ');
            else if (class_str.Trim() == "EEC")
                lc_result = "EEC - Others".PadRight(35, ' ');
            else if (class_str.Trim() == "CLL")
                lc_result = "Contractor Liability".PadRight(35, ' ');
            else if (class_str.Trim() == "CGL")
                lc_result = "Public Liability".PadRight(35, ' ');
            else if (class_str.Trim() == "CPM")
                lc_result = "Contractor's Plant & Machinery".PadRight(35, ' ');
            else if (class_str.Trim() == "MYP")
                lc_result = "Money Insurance".PadRight(35, ' ');
            else if (class_str.Trim() == "PAR")
                lc_result = "Property All Risks".PadRight(35, ' ');
            else if (class_str.Trim() == "FGP")
                lc_result = "Fidelity Guarantee".PadRight(35, ' ');
            else if (class_str.Trim() == "PMP")
                lc_result = "Private Car".PadRight(35, ' ');
            else if (class_str.Trim() == "CMP")
                lc_result = "Commercial Vehicle".PadRight(35, ' ');
            else if (class_str.Trim() == "CSB")
                lc_result = "Bond Insurance".PadRight(35, ' ');
            else if (class_str.Trim() == "PII")
                lc_result = "Professional Indemnity".PadRight(35, ' ');
            else if (class_str.Trim() == "GLF")
                lc_result = "Golf Insurance".PadRight(35, ' ');

            return lc_result;
        }

        public static String ClsTrdSeq(String class_str, String sclass_str, String trdcode_str)
        {
            String lc_result = "UN", lc_trdcode;
            Int32 ln_len;

            lc_trdcode = trdcode_str.Trim();
            ln_len = lc_trdcode.Length;

            if (ln_len <= 2)
                lc_trdcode = "10";
            else
                lc_trdcode = "0" + lc_trdcode.Substring(0, 1);

            if (class_str.Trim() == "CAR" && sclass_str.Trim() == "SEC1")
                lc_result = "01";
            else if (class_str.Trim() == "CAR" && sclass_str.Trim() == "SEC2")
                lc_result = "02";
            else if (class_str.Trim() == "CAR")
                lc_result = "03";
            else if (class_str.Trim() == "EEC" && sclass_str.Trim() == "CONTRACTOR")
                lc_result = "04";
            else if (class_str.Trim() == "EEC" && sclass_str.Trim() == "HOTEL")
                lc_result = "05";
            else if (class_str.Trim() == "EEC")
                lc_result = "06" + lc_trdcode;
            else if (class_str.Trim() == "CGL")
                lc_result = "07";
            else if (class_str.Trim() == "CPM")
                lc_result = "08";
            else if (class_str.Trim() == "MYP")
                lc_result = "09";
            else if (class_str.Trim() == "PAR")
                lc_result = "10";
            else if (class_str.Trim() == "FGP")
                lc_result = "11";
            else if (class_str.Trim() == "PMP")
                lc_result = "12";
            else if (class_str.Trim() == "CMP")
                lc_result = "13";
            else if (class_str.Trim() == "CSB")
                lc_result = "14";
            else if (class_str.Trim() == "PII")
                lc_result = "15";
            else if (class_str.Trim() == "GLF")
                lc_result = "16";
            else if (class_str.Trim() == "CLL")
                lc_result = "17";

            lc_result = lc_result.PadRight(4);

            return lc_result;
        }

        public static String ClsTrdName(String class_str, String sclass_str, String trdcode_str)
        {
            String lc_result = "Undefined", lc_trdcode;
            Int32 ln_len;

            lc_trdcode = trdcode_str.Trim();
            ln_len = lc_trdcode.Length;

            if (ln_len <= 2)
                lc_trdcode = "10";
            else
                lc_trdcode = "0" + lc_trdcode.Substring(0, 1);

            if (class_str.Trim() == "CAR" && sclass_str.Trim() == "SEC1")
                lc_result = "CAR - Section 1";
            else if (class_str.Trim() == "CAR" && sclass_str.Trim() == "SEC2")
                lc_result = "CAR - Section 2";
            else if (class_str.Trim() == "CAR")
                lc_result = "Contractors' All Risks";
            else if (class_str.Trim() == "EEC" && sclass_str.Trim() == "CONTRACTOR")
                lc_result = "EEC - Construction";
            else if (class_str.Trim() == "EEC" && sclass_str.Trim() == "HOTEL")
                lc_result = "EEC - Hotel";
            else if (class_str.Trim() == "EEC")
                lc_result = "EEC - " + lc_trdcode;
            else if (class_str.Trim() == "CGL")
                lc_result = "Public Liability";
            else if (class_str.Trim() == "CPM")
                lc_result = "Contractor's Plant & Machinery";
            else if (class_str.Trim() == "MYP")
                lc_result = "Money Insurance";
            else if (class_str.Trim() == "PAR")
                lc_result = "Property All Risks";
            else if (class_str.Trim() == "FGP")
                lc_result = "Fidelity Guarantee";
            else if (class_str.Trim() == "PMP")
                lc_result = "Private Car";
            else if (class_str.Trim() == "CMP")
                lc_result = "Commercial Vehicle";
            else if (class_str.Trim() == "CSB")
                lc_result = "Bond Insurance";
            else if (class_str.Trim() == "PII")
                lc_result = "Professional Indemnity";
            else if (class_str.Trim() == "GLF")
                lc_result = "Golf Insurance";
            else if (class_str.Trim() == "CLL")
                lc_result = "Contractor Liability";

            lc_result = lc_result.PadRight(35);

            return lc_result;
        }

        public static String AcCode(String class_str, String sclass_str, String fkind_str)
        {
            String lc_result = "Z";

            if ("CMP|PMP".Contains(class_str))
                lc_result = "A";
            else if ("PAR|CPM".Contains(class_str) || (class_str == "CAR" && sclass_str == "SEC1"))
                lc_result = "B";
            else if (class_str == "EEC" && sclass_str == "CONTRACTOR")
                lc_result = "C";
            else if (class_str == "EEC" && sclass_str == "HOTEL")
                lc_result = "D";
            else if (class_str == "EEC" && sclass_str == "OTHERS")
                lc_result = "E";
            else if ("CLL|CGL|PII".Contains(class_str) || (class_str == "CAR" && sclass_str == "SEC2"))
                lc_result = "F";
            else if ("MYP|FGP|GLF|CSB".Contains(class_str))
                lc_result = "G";

            return lc_result;
        }

        public static String AccDesc(String class_str, String sclass_str, String fkind_str)
        {
            String lc_result = "Undefined";

            if ("CMP|PMP".Contains(class_str))
                lc_result = "Motor Vehicle";
            else if ("PAR|CPM".Contains(class_str) || (class_str == "CAR" && sclass_str == "SEC1"))
                lc_result = "Property Demage";
            else if (class_str == "EEC" && sclass_str == "CONTRACTOR")
                lc_result = "GL Statutary - EEC Construction";
            else if (class_str == "EEC" && sclass_str == "HOTEL")
                lc_result = "GL Statutary - EEC Hotel";
            else if (class_str == "EEC" && sclass_str == "OTHERS")
                lc_result = "GL Statutary - EEC Others";
            else if ("CLL|CGL|PII".Contains(class_str) || (class_str == "CAR" && sclass_str == "SEC2"))
                lc_result = "GL Non Statuory";
            else if ("MYP|FGP|GLF|CSB".Contains(class_str))
                lc_result = "Pecuniary Loss";

            return lc_result;
        }

        public static String AccDescB(String class_str, String sclass_str, String fkind_str)
        {
            String lc_result = "Undefined";

            if ("CMP|PMP".Contains(class_str))
                lc_result = "Motor Vehicle, Damage & Liability";
            else if ("PAR|CPM".Contains(class_str) || (class_str == "CAR" && sclass_str == "SEC1"))
                lc_result = "Property Demage";
            else if (class_str == "EEC" && sclass_str == "CONTRACTOR")
                lc_result = "GL Liability - EEC Construction";
            else if (class_str == "EEC" && sclass_str == "HOTEL")
                lc_result = "GL Liability - EEC Hotel";
            else if (class_str == "EEC" && sclass_str == "OTHERS")
                lc_result = "GL Liability - EEC Others";
            else if ("CLL|CGL|PII".Contains(class_str) || (class_str == "CAR" && sclass_str == "SEC2"))
                lc_result = "GL Liability - Non Statuory";
            else if ("MYP|FGP|GLF|CSB".Contains(class_str))
                lc_result = "Pecuniary Loss";

            return lc_result;
        }

        public static String SecName(String class_str, String acclass_str)
        {
            String lc_result = "Undefined";

            if (class_str.Trim() == "CAR" && acclass_str.Trim() == "B")
                lc_result = "CAR - Property Damage";
            else if (class_str.Trim() == "CAR" && acclass_str.Trim() == "F")
                lc_result = "CAR - General Liability (Non Statutory)";
            else if (class_str.Trim() == "EEC" && acclass_str.Trim() == "C")
                lc_result = "EEC - General Liability (EEC Construction)";
            else if (class_str.Trim() == "EEC" && acclass_str.Trim() == "D")
                lc_result = "EEC - General Liability (EEC Hotel)";
            else if (class_str.Trim() == "EEC" && acclass_str.Trim() == "E")
                lc_result = "EEC - General Liability (EEC Others)";
            else if (class_str.Trim() == "MYP")
                lc_result = "MYP - Pecuniary Loss";
            else if (class_str.Trim() == "FGP")
                lc_result = "FGP - Pecuniary Loss";
            else if (class_str.Trim() == "CSB")
                lc_result = "CSB - Pecuniary Loss";
            else if (class_str.Trim() == "GLF")
                lc_result = "GLF - Pecuniary Loss";
            else if (class_str.Trim() == "PMP")
                lc_result = "PMP - Motor Vehicle, Damage & Liability";
            else if (class_str.Trim() == "CMP")
                lc_result = "CMP - Motor Vehicle, Damage & Liability";
            else if (class_str.Trim() == "PAR")
                lc_result = "PAR - Property Damage";
            else if (class_str.Trim() == "CPM")
                lc_result = "CPM - Property Damage";
            else if (class_str.Trim() == "CGL")
                lc_result = "CGL - General Liability (Non Statutory)";
            else if (class_str.Trim() == "CLL")
                lc_result = "CLL - General Liability (Non Statutory)";
            else if (class_str.Trim() == "PII")
                lc_result = "PII - General Liability (Non Statutory)";

            return lc_result;
        }

        public static Decimal CompSecFig(String fclass, Int32 ftotinstal, Decimal prin, Decimal numerator, Decimal denom1, Decimal denom2)
        {
            Decimal ln_result;

            if (fclass.Trim() != "CAR")
                ln_result = prin;
            else if (denom2 == 0.00m)
                ln_result = prin;
            else if (denom1 == 0.00m)
                ln_result = 0.00m;
            else if (denom1 + denom2 == 0.00m)
                ln_result = Math.Round(numerator / (decimal)ftotinstal, 2, MidpointRounding.AwayFromZero);
            else
                ln_result = Math.Round(prin * numerator / (denom1 + denom2), 2, MidpointRounding.AwayFromZero);

            return ln_result;
        }

        public static Decimal CompEarnedFig(Decimal prinfig, Int32 fexpday, Int32 fpolday, Decimal fgpm_p, Decimal asatprm)
        {
            Decimal ln_result;
            //Double ln_ress;

            //UtilityFunc.CompEarnedFig(fgpm_iy, fexpday_y, fpolday_y, fgpm_p, fgpm_iy)

            if (prinfig == 0.00m)
                ln_result = 0.00m;
            else if (fgpm_p == 0.00m)
                ln_result = prinfig - Math.Round(prinfig * ((Decimal)1 - (Decimal)fexpday / (Decimal)fpolday), 2, MidpointRounding.AwayFromZero);
            else
                ln_result = prinfig - Math.Round(prinfig * ((Decimal)1 - (Decimal)fexpday / (Decimal)fpolday * fgpm_p / asatprm), 2,
                                               MidpointRounding.AwayFromZero);
            //{
            //    ln_ress = Math.Round((Double)prinfig, 2, MidpointRounding.AwayFromZero) - Math.Round((Double)prinfig * ((Double)1 - (Double)fexpday / (Double)fpolday * (Double)fgpm_p / (Double)asatprm), 2,
            //        MidpointRounding.AwayFromZero);
            //    ln_result = Math.Round((Decimal)ln_ress, 2, MidpointRounding.AwayFromZero);
            //}

            return ln_result;
        }

        public static String GetKindFrmClass(String fclass)
        {
            String lc_result = null;

            fclass = fclass.Trim();

            if (fclass == "CAR")
                lc_result = " ";
            else if (fclass == "EEC")
                lc_result = "2";
            else if (fclass == "CLL")
                lc_result = "2";
            else if (fclass == "CGL")
                lc_result = "2";
            else if (fclass == "CPM")
                lc_result = "1";
            else if (fclass == "MYP")
                lc_result = "1";
            else if (fclass == "PAR")
                lc_result = "1";
            else if (fclass == "FGP")
                lc_result = "1";
            else if (fclass == "GLF")
                lc_result = "1";
            else if (fclass == "PMP")
                lc_result = "2";
            else if (fclass == "CMP")
                lc_result = "2";
            else if (fclass == "CSB")
                lc_result = "1";
            else if (fclass == "PII")
                lc_result = "2";

            return lc_result;
        }

        public static void ExtendClassTable(DataTable dtminsc, Boolean splitCAR, Boolean addHotel)
        {
            DataView dv = dtminsc.DefaultView;
            DataRowView rowView;

            if (splitCAR)
            {
                dv.RowFilter = String.Format("trim(fclass) = '{0}'", "CAR");

                if (dv.Count != 0)
                    dv[0]["fsclass"] = "SEC1";


                rowView = dv.AddNew();

                rowView["fclass"] = "CAR";
                rowView["fsclass"] = "SEC2";
                rowView.EndEdit();
            }

            rowView = dv.AddNew();
            rowView["fclass"] = "EEC";
            rowView["fsclass"] = "CONTRACTOR";
            rowView.EndEdit();

            if (addHotel)
            {
                rowView = dv.AddNew();
                rowView["fclass"] = "EEC";
                rowView["fsclass"] = "HOTEL";
                rowView.EndEdit();
            }

            dv.RowFilter = "";

            for (Int32 i = 0; i < dv.Count; i++)
            {
                String fclass = dv[i]["fclass"].ToString().Trim(), fsclass = dv[i]["fsclass"].ToString().Trim();

                dv[i]["fclsseq"] = UtilityFunc.ClsSeq(fclass, fsclass);
                dv[i]["fclsname"] = UtilityFunc.ClsName(fclass, fsclass);
            }

            return;
        }

        public static String AccidentType(String class_str)
        {
            String lc_result = "UN";

            if (class_str.Trim() == "68")
                lc_result = "01";
            else if (class_str.Trim() == "69")
                lc_result = "02";
            else if (class_str.Trim() == "70")
                lc_result = "03";
            else if (class_str.Trim() == "71")
                lc_result = "04";
            else if (class_str.Trim() == "72")
                lc_result = "05";
            else if (class_str.Trim() == "73")
                lc_result = "06";
            else if (class_str.Trim() == "74")
                lc_result = "07";
            else if (class_str.Trim() == "75")
                lc_result = "08";
            else if (class_str.Trim() == "76")
                lc_result = "09";
            else if (class_str.Trim() == "77")
                lc_result = "10";
            else if (class_str.Trim() == "78")
                lc_result = "11";
            else if (class_str.Trim() == "79")
                lc_result = "12";
            else if (class_str.Trim() == "80")
                lc_result = "13";
            else if (class_str.Trim() == "81")
                lc_result = "14";
            else if (class_str.Trim() == "82")
                lc_result = "15";
            else if (class_str.Trim() == "83")
                lc_result = "16";
            else if (class_str.Trim() == "84")
                lc_result = "17";

            return lc_result;
        }

        public static String InjureAttr(String class_str)
        {
            String lc_result = "";
            List<string> list = new List<string>();
            list = class_str.Split(',').ToList();
            foreach (var l in list)
            {
                if (l.Trim() != "")
                {
                    if (l.Trim() == "19")
                        lc_result = "fl01 = 1";
                   // else
                    //    lc_result = "fl01 = 0";

                    if (l.Trim() == "20")
                        lc_result = lc_result + ",fl02 = 1";
                    //else
                      //  lc_result = lc_result + ",fl02 = 0";

                    if (l.Trim() == "21")
                        lc_result = lc_result + ",fl03 = 1";
                    //else
                      //  lc_result = lc_result + ",fl03 = 0";

                    if (l.Trim() == "22")
                        lc_result = lc_result + ",fl04 = 1";
                   // else
                       // lc_result = lc_result + ",fl04 = 0";

                    if (l.Trim() == "23")
                        lc_result = lc_result + ",fl05 = 1";
                   // else
                       // lc_result = lc_result + ",fl05 = 0";

                    if (l.Trim() == "24")
                        lc_result = lc_result + ",fl06 = 1";
                    //else
                        //lc_result = lc_result + ",fl06 = 0";

                    if (l.Trim() == "25")
                        lc_result = lc_result + ",fl07 = 1";
                   // else
                        //lc_result = lc_result + ",fl07 = 0";

                    if (l.Trim() == "27")
                        lc_result = lc_result + ",fl08 = 1";
                    //else
                       // lc_result = lc_result + ",fl08 = 0";

                    if (l.Trim() == "28")
                        lc_result = lc_result + ",fl09 = 1";
                    //else
                       // lc_result = lc_result + ",fl09 = 0";

                    if (l.Trim() == "29")
                        lc_result = lc_result + ",fl10 = 1";
                    //else
                        //lc_result = lc_result + ",fl10 = 0";

                    if (l.Trim() == "30")
                        lc_result = lc_result + ",fl11 = 1";
                    //else
                       // lc_result = lc_result + ",fl11 = 0";

                    if (l.Trim() == "31")
                        lc_result = lc_result + ",fl12 = 1";
                    //else
                       // lc_result = lc_result + ",fl12 = 0";

                    if (l.Trim() == "32")
                        lc_result = lc_result + ",fl13 = 1";
                    //else
                        //lc_result = lc_result + ",fl13 = 0";

                    if (l.Trim() == "33")
                        lc_result = lc_result + ",fl14 = 1";
                   // else
                        //lc_result = lc_result + ",fl14 = 0";

                    if (l.Trim() == "34")
                        lc_result = lc_result + ",fl15 = 1";
                    //else
                       // lc_result = lc_result + ",fl15 = 0";

                    if (l.Trim() == "35")
                        lc_result = lc_result + ",fl16 = 1";
                    //else
                        //lc_result = lc_result + ",fl16 = 0";

                    if (l.Trim() == "36")
                        lc_result = lc_result + ",fl17 = 1";
                    //else
                        //lc_result = lc_result + ",fl17 = 0";

                    if (l.Trim() == "37")
                        lc_result = lc_result + ",fl18 = 1";
                    //else
                       // lc_result = lc_result + ",fl18 = 0";

                    if (l.Trim() == "38")
                        lc_result = lc_result + ",fl19 = 1";
                   // else
                       // lc_result = lc_result + ",fl19 = 0";

                    if (l.Trim() == "41")
                        lc_result = lc_result + ",fl20 = 1";
                   // else
                      //  lc_result = lc_result + ",fl20 = 0";
                }
            }


            return lc_result;
        }

        public static String InjureBody(String class_str)
        {
            String lc_result = "";
            List<string> list = new List<string>();
            list = class_str.Split(',').ToList();
            foreach (var l in list)
            {
                if (l.Trim() != "")
                {
                    if (l.Trim() == "42")
                        lc_result = "fl21 = 1";
                   // else
                       // lc_result = "fl21 = 0";

                    if (l.Trim() == "43")
                        lc_result = lc_result + ",fl22 = 1";
                   // else
                     //   lc_result = lc_result + ",fl22 = 0";

                    if (l.Trim() == "44")
                        lc_result = lc_result + ",fl23 = 1";
                  //  else
                     //   lc_result = lc_result + ",fl23 = 0";

                    if (l.Trim() == "45")
                        lc_result = lc_result + ",fl24 = 1";
                    //else
                      //  lc_result = lc_result + ",fl24 = 0";

                    if (l.Trim() == "46")
                        lc_result = lc_result + ",fl25 = 1";
                    //else
                      //  lc_result = lc_result + ",fl25 = 0";

                    if (l.Trim() == "47")
                        lc_result = lc_result + ",fl26 = 1";
                   // else
                      //  lc_result = lc_result + ",fl26 = 0";

                    if (l.Trim() == "48")
                        lc_result = lc_result + ",fl31 = 1";
                   // else
                      //  lc_result = lc_result + ",fl31 = 0";

                    if (l.Trim() == "49")
                        lc_result = lc_result + ",fl32 = 1";
                  //  else
                      //  lc_result = lc_result + ",fl32 = 0";

                    if (l.Trim() == "50")
                        lc_result = lc_result + ",fl33 = 1";
                    //else
                        //lc_result = lc_result + ",fl33 = 0";

                    if (l.Trim() == "51")
                        lc_result = lc_result + ",fl34 = 1";
                   // else
                       // lc_result = lc_result + ",fl34 = 0";

                    if (l.Trim() == "52")
                        lc_result = lc_result + ",fl35 = 1";
                  //  else
                   //     lc_result = lc_result + ",fl35 = 0";

                    if (l.Trim() == "53")
                        lc_result = lc_result + ",fl36 = 1";
                    //else
                    //    lc_result = lc_result + ",fl36 = 0";

                    if (l.Trim() == "54")
                        lc_result = lc_result + ",fl41 = 1";
                    //else
                      //  lc_result = lc_result + ",fl41 = 0";

                    if (l.Trim() == "55")
                        lc_result = lc_result + ",fl42 = 1";
                  //  else
                     //   lc_result = lc_result + ",fl42 = 0";

                    if (l.Trim() == "56")
                        lc_result = lc_result + ",fl43 = 1";
                    //else
                     //   lc_result = lc_result + ",fl43 = 0";

                    if (l.Trim() == "57")
                        lc_result = lc_result + ",fl44 = 1";
                   // else
                       // lc_result = lc_result + ",fl44 = 0";

                    if (l.Trim() == "58")
                        lc_result = lc_result + ",fl45 = 1";
                    //else
                     //   lc_result = lc_result + ",fl45 = 0";

                    if (l.Trim() == "59")
                        lc_result = lc_result + ",fl46 = 1";
                    //else
                       // lc_result = lc_result + ",fl46 = 0";

                    if (l.Trim() == "60")
                        lc_result = lc_result + ",fl51 = 1";
                  //  else
                       // lc_result = lc_result + ",fl51 = 0";

                    if (l.Trim() == "61")
                        lc_result = lc_result + ",fl52 = 1";
                    //else
                       // lc_result = lc_result + ",fl52 = 0";

                    if (l.Trim() == "62")
                        lc_result = lc_result + ",fl53 = 1";
                   // else
                       // lc_result = lc_result + ",fl53 = 0";

                    if (l.Trim() == "63")
                        lc_result = lc_result + ",fl54 = 1";
                   // else
                       // lc_result = lc_result + ",fl54 = 0";

                    if (l.Trim() == "64")
                        lc_result = lc_result + ",fl55 = 1";
                  //  else
                       // lc_result = lc_result + ",fl55 = 0";

                    if (l.Trim() == "65")
                        lc_result = lc_result + ",fl56 = 1";
                   // else
                     //   lc_result = lc_result + ",fl56 = 0";
                    
                    if (l.Trim() == "67")
                        lc_result = lc_result + ",fl61 = 1";
                    //else
                       // lc_result = lc_result + ",fl61 = 0";

                }
            }


            return lc_result;
        }
    }

    public static class Fct
    {
        public static String NewId(string fidtype)
        {
            DBConnect operate = new DBConnect();
            string str = "";
            if (fidtype != "OINVHID")
            {
                str = "select * from xsysparm where fidtype ='" + fidtype + "'";
            }
            else {
                str = "select RIGHT('0000000'+ LTRIM(STR(CAST(max(fctlid) as int)+1)), 10) as fnxtid from live_ilodata.dbo.oinvh";
            }
            
            
            DataTable dt = new DataTable();
            dt = operate.GetTable(str);
            string fctlid = dt.Rows[0]["fnxtid"].ToString();
            return fctlid;
        }

        public static String NewCoverNo(string fuwyr)
        {
            DBConnect operate = new DBConnect();
            string Sql1 = "select rtrim(fprefix) +'-' + rtrim(fnxtid) as CoverNo from xsysparm where fidtype='CoverNote' AND fuy ='" + fuwyr + "'";
            DataTable dt = operate.GetTable(Sql1);
            string CoverNo = dt.Rows[0]["CoverNo"].ToString();

            return CoverNo;
        }

        public static string cal_LevyRate(string p_lvyhd, string p_lvyclass, string p_lvytype, string p_class, string p_date, Decimal p_frate)
        {
            decimal ln_result = 0;
            if (p_date == "    .  ." || p_date == "")
            {
                return ln_result.ToString();
            }
            DBConnect operate = new DBConnect();
            string str = "select a.fefffr,a.feffto,a.factive,a.frate,a.fapplymax,a.fupperlmt, "+
                        "b.fclass from " + p_lvyhd + " a, " + p_lvyclass + " b  " +
                        "where a.fctlid = b.fctlid_hd and a.factive = '1' and a.flvytype = '" + p_lvytype + "' and b.fclass= '" + p_class + "' and  " +
                        "a.fefffr <= '" + p_date + "' and a.feffto >= '" + p_date + "' order by a.feffto desc";
            DataTable dt = operate.GetTable(str);
            if (dt.Rows.Count > 0)
            {
                p_frate = Convert.ToDecimal(dt.Rows[0]["frate"].ToString());
            }
            return p_frate.ToString();
        }

        public static Decimal cal_levy(string p_lvyhd, string p_lvyclass, string p_lvytype, string p_class, string p_date, Decimal p_gpm, Decimal p_accgpm, Decimal p_acclevy, Decimal p_frate)
        {
            decimal ln_frate, ll_fapplymax, ln_fupperlmt, ln_result;
            ln_result = 0;
            p_frate = 0;
            if (p_date == "    .  ." || p_date =="")
            {
                return ln_result;
            }
            DBConnect operate = new DBConnect();
            string str = "select a.fefffr,a.feffto,a.factive,a.frate,a.fapplymax,a.fupperlmt, "+
                        "b.fclass from " + p_lvyhd + " a, " + p_lvyclass + " b  " +
                        "where a.fctlid = b.fctlid_hd and a.factive = '1' and a.flvytype = '" + p_lvytype + "' and b.fclass= '" + p_class + "' and  " +
                        "a.fefffr <= '" + p_date + "' and a.feffto >= '" + p_date + "' order by a.feffto desc";
            DataTable dt = operate.GetTable(str);
            if (dt.Rows.Count > 0)
            {
                ln_frate = Convert.ToDecimal(dt.Rows[0]["frate"].ToString());
                p_frate = Convert.ToDecimal(dt.Rows[0]["frate"].ToString());
                ln_fupperlmt = Convert.ToDecimal(dt.Rows[0]["fupperlmt"].ToString());
                ll_fapplymax = Convert.ToDecimal(dt.Rows[0]["fapplymax"].ToString());

                ln_result = Math.Round(((Decimal)p_gpm * (Decimal)ln_frate / 100), 2, MidpointRounding.AwayFromZero);

                if (ll_fapplymax != 1) { return ln_result; }


                ln_result = Math.Round((p_gpm + p_accgpm) * ln_frate / 100, 2, MidpointRounding.AwayFromZero);
                ln_result = Math.Min(Math.Round(ln_result - p_acclevy, 2, MidpointRounding.AwayFromZero), Math.Round(ln_fupperlmt - p_acclevy, 2, MidpointRounding.AwayFromZero));
            }
          
            return ln_result;
        }

        //  very difficult

        public static string get_incpdate(string p_table, string p_fctlid)
        {
            string ld_efffr="";

            string sql = "select * from " + p_table + " where fconfirm = '3' and fctlid = '" + p_fctlid + "' order by fctlid";
             DataTable dt = DBHelper.GetDataSet(sql);
             if (dt.Rows.Count > 0)
             {
                 ld_efffr = dt.Rows[0]["fefffr"].ToString();
             }
            return ld_efffr;   
        }

        public static string get_annivery(string p_incpdate, string p_effdate)
        {
            DateTime ld_annivery = Convert.ToDateTime(p_incpdate);
            do
            {
                if (Convert.ToDateTime(p_effdate) < ld_annivery.AddMonths(12))
                {break;}
                ld_annivery = ld_annivery.AddMonths(12);
            }
            while (Convert.ToDateTime(p_effdate) >= ld_annivery.AddMonths(12));
                
            return ld_annivery.ToString();   
        }

        public static decimal get_AmntPaid(string p_returnOpt, string p_levytype, string p_table, string p_fctlid)
        {
            decimal ln_fgpm = 0, ln_flvyamt1 = 0, ln_flvyamt2 = 0, ln_flvyamt3 = 0, ln_flvyamta = 0;

            string sql = "select * from " + p_table + " where fconfirm = '3' and fctlid = '" + p_fctlid + "' order by fctlid";
            DataTable dt = DBHelper.GetDataSet(sql);
            if (dt.Rows.Count > 0)
            {
                ln_fgpm = Fct.sdFat(dt.Rows[0]["fgpm"].ToString());
                ln_flvyamt1 = Fct.sdFat(dt.Rows[0]["flvyamt1"].ToString());
                ln_flvyamt2 = Fct.sdFat(dt.Rows[0]["flvyamt2"].ToString());
                ln_flvyamt3 = Fct.sdFat(dt.Rows[0]["flvyamt3"].ToString());
                ln_flvyamta = Fct.sdFat(dt.Rows[0]["flvyamta"].ToString());
            }
            if (p_returnOpt == "PREM") { return ln_fgpm; }
            if (p_returnOpt == "IAL") { return ln_flvyamta; }
            if (p_returnOpt == "IFS" || p_returnOpt == "ECILMB") { return ln_flvyamt1; }
            if (p_returnOpt == "FFS" || p_returnOpt == "GTFC") { return ln_flvyamt2; }
            if (p_returnOpt == "ECIIB") { return ln_flvyamt3; }

            return 0;
        }

        public static Decimal cal_levyXX(string ptab_lvyhd, string ptab_lvyclass, string ptab_polh, string p_levytype, string p_class, string p_effdateOpt, string p_returnOpt, string p_trantype, string p_fctlid_p, string p_feffdate, decimal p_gpm, string p_autolvy, decimal p_lvyrate, decimal p_lvyamt)
        {
            decimal ln_acclvy = 0,ln_accprm=0;
            string ld_incpdate, ld_effdate;

            if (p_autolvy == "No"){
                if (p_returnOpt == "RATE"){return p_lvyrate;}
                else {return p_lvyamt;}
            }

            if (p_feffdate == "    .  ." || p_feffdate == "")
            {
                return 0;
            }

            if (p_trantype == "P") { ln_accprm = 0; }
            else {
                ln_accprm = get_AmntPaid("PREM", p_levytype, ptab_polh, p_fctlid_p);
            }

            if (p_trantype == "P") { ln_acclvy = 0; }
            else
            {
                ln_acclvy = get_AmntPaid("LEVY", p_levytype, ptab_polh, p_fctlid_p);
            }

            if (p_trantype == "P") { ld_incpdate = p_feffdate; }
            else
            {
                ld_incpdate = get_incpdate(ptab_polh,p_fctlid_p);
            }

            if (p_effdateOpt == "1")  { ld_effdate = ld_incpdate; }
            else if (p_effdateOpt == "2")
            {
                ld_effdate = p_feffdate;
            }
            else {
                ld_effdate = get_annivery(ld_incpdate, p_feffdate);
            }

            if (ld_effdate != "")
            {
                try
                {
                    string a = ld_effdate;
                    DateTime b = Convert.ToDateTime(a);
                    ld_effdate = b.ToString("yyyy.MM.dd");
                }
                catch { }
            }

            decimal ln_frate=0, ll_fapplymax=0, ln_fupperlmt=0, ln_result=0;
            DBConnect operate = new DBConnect();
            string str = "select a.fefffr,a.feffto,a.factive,a.frate,a.fapplymax,a.fupperlmt, "+
                        "b.fclass from " + ptab_lvyhd + " a, " + ptab_lvyclass + " b  " +
                        "where a.fctlid = b.fctlid_hd and a.factive = '1' and a.flvytype = '" + p_levytype + "' and b.fclass= '" + p_class + "' and  " +
                        "a.fefffr <= convert(datetime,'" + ld_effdate + "',102) and a.feffto >= convert(datetime,'" + ld_effdate + "',102)  order by a.feffto desc";
            DataTable dt = operate.GetTable(str);
            if (dt.Rows.Count > 0)
            {
                ln_frate = Convert.ToDecimal(dt.Rows[0]["frate"].ToString());
                ln_fupperlmt = Convert.ToDecimal(dt.Rows[0]["fupperlmt"].ToString());
                ll_fapplymax = Convert.ToDecimal(dt.Rows[0]["fapplymax"].ToString());

                if (p_returnOpt == "RATE") { return ln_frate; }
                ln_result = Math.Round(((Decimal)p_gpm * (Decimal)ln_frate / 100), 2, MidpointRounding.AwayFromZero);
                if (ll_fapplymax != 1) { return ln_result; }
                if (p_effdateOpt == "1")
                {
                    ln_result = Math.Round((p_gpm + ln_accprm) * ln_frate / 100, 2, MidpointRounding.AwayFromZero);
                    ln_result = Math.Min(Math.Round(ln_result - ln_acclvy, 2, MidpointRounding.AwayFromZero), Math.Round(ln_fupperlmt - ln_acclvy, 2, MidpointRounding.AwayFromZero));
                    return ln_result;
                }
            }
            if (ln_result >= 0)
            {
                ln_result = Math.Min(ln_result, ln_fupperlmt);
            }
            ln_result = Math.Max(ln_result, -ln_fupperlmt);
            //ln_result = Math.Max(-ln_acclvy, ln_result);
            return ln_result;
        }

        public static string cal_LevyParam(Decimal p_flvyamt1, Decimal p_flvyamt2, Decimal p_flvyamt3, Decimal p_flvyamta, Decimal p_fgpm, string p_incpdate, string p_table, string p_fextend, string p_fctlid, string p_fvseq)
        {
            cal_LevyParam1(p_flvyamt1, p_flvyamt2, p_flvyamt3, p_flvyamta, p_fgpm, p_incpdate, p_table, p_fctlid);
           //cal_LevyParam2(@p_flvyamt1,@p_flvyamt2,@p_flvyamt3,@p_flvyamta,@p_fgpm,@p_incpdate,p_table,p_fextend,p_fctlid,p_fvseq)
           return "1";
        }

        public static string cal_LevyParam1(Decimal p_flvyamt1, Decimal p_flvyamt2, Decimal p_flvyamt3, Decimal p_flvyamta, Decimal p_fgpm, string p_incpdate, string p_table, string p_fctlid) 
        {
            return "1";
        }

        public static string cal_LevyParam2(Decimal p_flvyamt1, Decimal p_flvyamt2, Decimal p_flvyamt3, Decimal p_flvyamta, Decimal p_fgpm, string p_incpdate, string p_table, string p_fextend, string p_fctlid, string p_fvseq)
        {
            return "1";
        }
        //  very difficult

        public static Decimal LevyFee(Decimal fgpm, Decimal PrmPaid, string fissdate)
        {
            DBConnect operate = new DBConnect();
            string str = "select * from levy where '" + fissdate + "' between fefffr and feffto";
            DataTable dt = operate.GetTable(str);
            Decimal flimit = Convert.ToDecimal(dt.Rows[0]["flimit"].ToString());
            Decimal frate = Convert.ToDecimal(dt.Rows[0]["frate"].ToString());
            Decimal PrmLevyPaid = frate * PrmPaid / 100;

            if (PrmLevyPaid == flimit)
            {
                return 0;
            }
            else { return frate * fgpm / 100; }
        }

        public static Decimal LevyFeeRefund(Decimal Prmrefund, Decimal PrmPaid, string fissdate)
        {
            DBConnect operate = new DBConnect();
            string str = "select * from levy where '" + fissdate + "' between fefffr and feffto";
            DataTable dt = operate.GetTable(str);
            Decimal flimit = Convert.ToDecimal(dt.Rows[0]["flimit"].ToString());
            Decimal frate = Convert.ToDecimal(dt.Rows[0]["frate"].ToString());
            Decimal RefundLevyAmt = frate * Prmrefund / 100;
            Decimal PrmLevyPaid = frate * (PrmPaid - Prmrefund) / 100;

            if (PrmLevyPaid > flimit)
            {
                return 0;
            }
            else { return RefundLevyAmt; }
        }

        public static string stFat(object str) {
            if (str == null) { return ""; }
            else { return str.ToString().Replace("'", "''").Trim(); }
        }

        public static string dateFat(object str)
        {
            if (str.ToString().Trim() != "")
            {
                DateTime oDate = Convert.ToDateTime(str);
                return oDate.ToString("yyyy-MM-dd");
            }
            else {
                return "";
            }
        }

        public static Boolean sbFat(object str)
        {
            if (str.ToString().Trim() == "1") { return true; }
            else { return false; }
        }

        public static string sbtFat(object str)
        {
            if (str.Equals(true)) { return "1"; }
            else { return "0"; }
        }

        public static decimal sdFat(object str)
        {
            if (str.ToString().Trim()  == "") { return 0; } else { return Convert.ToDecimal(str.ToString().Replace(",", "").Trim()); }
        }

        public static int snFat(object str)
        {
            if (str.ToString().Trim() == "") { return 0; } else { return Convert.ToInt32(str.ToString().Replace(",", "").Trim()); }
        }

        public static decimal Refat(string str)
        {
            if (str == "") { return 0; } else { return Convert.ToDecimal(str.Replace(",", "").Trim()); }
        }

        public static decimal noofyears(DateTime p_from, DateTime p_to)
        {
            int ln_year; decimal ln_result;
            int ln_day, ln_noofdays;
            DateTime ln_date;


            if ((p_from == null) || (p_to == null) || (p_to < p_from))
            { return 0; }

            ln_year = p_to.AddDays(1).Year - p_from.Year;
            ln_date = p_from.AddMonths(ln_year * 12);

            if ((p_from.Day == 29) && (ln_date.Day == 28))
            { ln_day = (int) (p_to - ln_date).TotalDays; }
            else { ln_day = (int) (p_to - ln_date).TotalDays+1; }

            if (ln_date > p_to)
            { ln_noofdays = (int) (ln_date - ln_date.AddMonths(-12)).TotalDays; }
            else
            { ln_noofdays = (int) (ln_date.AddMonths(12) - ln_date).TotalDays; }

            ln_result = ln_year + Convert.ToDecimal(ln_day) / Convert.ToDecimal(ln_noofdays);
            return ln_result;
        }

        public static decimal calgpm(decimal p_fsi, decimal p_fprate, decimal p_frate_p, DateTime p_fefffr, DateTime p_feffto, bool p_annual)
        {
            decimal ln_result, rat;
            if (p_annual)
            {
                rat = noofyears(p_fefffr, p_feffto);
            }
            else
            {
                rat = 1;
            }
            ln_result = p_fsi * p_fprate / 100 * p_frate_p * rat;
            return ln_result;
        }

        public static decimal calprate(decimal p_fsi, decimal p_fgpm, decimal p_frate_p, DateTime p_fefffr, DateTime p_feffto, bool p_annual)
        {
            decimal ln_result=0, ln_years, ln_numerator, ln_denominator;
            if (p_annual)
            {
                ln_years = noofyears(p_fefffr, p_feffto);
            }
            else
            {
                ln_years = 1;
            }
            ln_numerator = p_fgpm;
            ln_denominator = p_fsi * ln_years;

            if (ln_years > 100)
            { ln_result = 0; }

            else if (p_fgpm >= p_fsi * ln_years)
            { ln_result = 0;}

            else if (ln_denominator == 0)
            { ln_result = 0; }
            else { ln_result = ln_numerator / ln_denominator * 100; }
            return ln_result;
        }
    }

    public partial class csCommon
    {
        /// <summary>
        /// Default for MSSql, MySql that accepts datetime as single-quoted string
        /// </summary>
        /// <param name="Date"></param>
        /// <returns></returns>
        public static string DbDateFormat(DateTime Date)
        {
            return DbDateFormat(Date, "");
        }

        /// <summary>
        /// MSSql, Oracle etc. Default blank=MSSql
        /// </summary>
        /// <param name="Date"></param>
        /// <param name="DBType"></param>
        /// <returns></returns>
        public static string DbDateFormat(DateTime Date, string DBType)
        {
            string cDateTime = "";
            switch (DBType)
            {
                case "Ora":
                    cDateTime = string.Format("ToDate({0:yyyy-MM-dd HH:mm:ss}, 'yyyy-MM-dd HH:mm:ss')", Date);
                    break;
                case "Access":     //Access
                    cDateTime = string.Format("#{0:yyyy-MM-dd HH:mm:ss}", Date);
                    break;
                default:
                    cDateTime = string.Format("'{0:yyyy-MM-dd HH:mm:ss}'", Date);
                    break;
            }
            return cDateTime;
        }
    }
}
