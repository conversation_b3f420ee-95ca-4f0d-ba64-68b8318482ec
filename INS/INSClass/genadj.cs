using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading;

namespace INS.INSClass
{
    public static class genadj
    {
        public static string Dbm = InsEnvironment.DataBase.GetDbm();
        public static string Dbo = InsEnvironment.DataBase.GetDbo();
        public static string[] u_genadj(string p_calling, string finvdate, string fclmno,string fctlid_p)
        {
            decimal ln_adj01_oe = 0, ln_adj02_oe = 0, ln_adj03_oe = 0, ln_adj04_oe = 0, ln_adj05_oe = 0,
                    ln_adj06_oe = 0, ln_adj07_oe = 0, ln_adj08_oe = 0, ln_adj09_oe = 0, ln_adj10_oe = 0,
                    ln_adj01_oc = 0, ln_adj02_oc = 0, ln_adj03_oc = 0, ln_adj04_oc = 0, ln_adj05_oc = 0,
                    ln_adj06_oc = 0, ln_adj07_oc = 0, ln_adj10_oc = 0, ln_adj08_oc = 0, ln_adj09_oc = 0,
                    ln_adj11_oc = 0, ln_adj12_oc = 0, ln_adj13_oc = 0, ln_adjA_oc = 0;
            decimal ln_famount = 0, ln_fecamt = 0, ln_fclawamt = 0, ln_flogseq = 0;
            decimal ln_fttysh = 0, ln_ffacsh = 0, ln_ffacbsh = 0, ln_fretn = 0, ln_ftty = 0, ln_ffac = 0, ln_fxol = 0, ln_ffacb = 0, ln_ffacnp = 0;
            decimal ls_fosxol = 0, ls_fostty = 0, ls_fosretn = 0, ls_fosfacnp = 0, ls_fosfacb = 0, ls_fosfac = 0, ls_fosamt = 0;
            decimal ln_fos01_oe = 0, ln_fos02_oe = 0, ln_fos03_oe = 0, ln_fos04_oe = 0, ln_fos05_oe = 0,
                    ln_fos06_oe = 0, ln_fos07_oe = 0, ln_fos08_oe = 0, ln_fos09_oe = 0, ln_fos10_oe = 0, ln_fosA_oe = 0, ln_fosB_oe = 0, ln_fosC_oe = 0, ln_fos_oe = 0,
                    ln_fos01_oc = 0, ln_fos02_oc = 0, ln_fos03_oc = 0, ln_fos04_oc = 0, ln_fos05_oc = 0,
                    ln_fos06_oc = 0, ln_fos07_oc = 0, ln_fos10_oc = 0, ln_fos08_oc = 0, ln_fos09_oc = 0,
                    ln_fos11_oc = 0, ln_fos12_oc = 0, ln_fos13_oc = 0, ln_fosA_oc = 0, ln_fosA2_oc = 0, ln_fosB_oc = 0, ln_fosC_oc = 0, ln_fos_oc = 0;
            string fnature = "";
            string ld_faccdate = "";
            string str = "select * from ocabk_oc where fctlid_c=(select fctlid from oclaim where fclmno ='" + fclmno + "')";
            DataTable DTocabk_oc = DBHelper.GetDataSet(str);
            string str1 = "select * from ocabk_oe where fctlid_c=(select fctlid from oclaim where fclmno ='" + fclmno + "')";
            DataTable DTocabk_oe = DBHelper.GetDataSet(str1);
            string user = "", date = "";
            date = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            user = InsEnvironment.LoginUser.GetUserCode();
            if (p_calling == "CLOSE")
            {
                foreach (DataRow dr in DTocabk_oe.Rows)
                {
                    ln_fos01_oe = Fct.sdFat(dr["fos01"]);
                    ln_fos02_oe = Fct.sdFat(dr["fos02"]);
                    ln_fos03_oe = Fct.sdFat(dr["fos03"]);
                    ln_fos04_oe = Fct.sdFat(dr["fos04"]);
                    ln_fos05_oe = Fct.sdFat(dr["fos05"]);
                    ln_fos06_oe = Fct.sdFat(dr["fos06"]);
                    ln_fos07_oe = Fct.sdFat(dr["fos07"]);
                    ln_fos08_oe = Fct.sdFat(dr["fos08"]);
                    ln_fos09_oe = Fct.sdFat(dr["fos09"]);
                    ln_fos10_oe = Fct.sdFat(dr["fos10"]);
                    ln_fosA_oe = Fct.sdFat(dr["fosA"]);
                    ln_fosB_oe = Fct.sdFat(dr["fosB"]);
                    ln_fosC_oe = Fct.sdFat(dr["fosC"]);
                    ln_fos_oe = Fct.sdFat(dr["fos"]);
                    if (Fct.sdFat(dr["fos01"]) > 0)
                    {
                        ln_adj01_oe = -Fct.sdFat(dr["fos01"]);
                    }
                    if (Fct.sdFat(dr["fos02"]) > 0)
                    {
                        ln_adj02_oe = -Fct.sdFat(dr["fos02"]);
                    }
                    if (Fct.sdFat(dr["fos03"]) > 0)
                    {
                        ln_adj03_oe = -Fct.sdFat(dr["fos03"]);
                    }
                    if (Fct.sdFat(dr["fos04"]) > 0)
                    {
                        ln_adj04_oe = -Fct.sdFat(dr["fos04"]);
                    }
                    if (Fct.sdFat(dr["fos05"]) > 0)
                    {
                        ln_adj05_oe = -Fct.sdFat(dr["fos05"]);
                    }
                    if (Fct.sdFat(dr["fos06"]) > 0)
                    {
                        ln_adj06_oe = -Fct.sdFat(dr["fos06"]);
                    }
                    if (Fct.sdFat(dr["fos07"]) > 0)
                    {
                        ln_adj07_oe = -Fct.sdFat(dr["fos07"]);
                    }
                    if (Fct.sdFat(dr["fos08"]) > 0)
                    {
                        ln_adj08_oe = -Fct.sdFat(dr["fos08"]);
                    }
                    if (Fct.sdFat(dr["fos09"]) > 0)
                    {
                        ln_adj09_oe = -Fct.sdFat(dr["fos09"]);
                    }
                    if (Fct.sdFat(dr["fos10"]) > 0)
                    {
                        ln_adj10_oe = -Fct.sdFat(dr["fos10"]);
                    }
                }
                foreach (DataRow dr in DTocabk_oc.Rows)
                {
                    ln_fos01_oc = Fct.sdFat(dr["fos01"]);
                    ln_fos02_oc = Fct.sdFat(dr["fos02"]);
                    ln_fos03_oc = Fct.sdFat(dr["fos03"]);
                    ln_fos04_oc = Fct.sdFat(dr["fos04"]);
                    ln_fos05_oc = Fct.sdFat(dr["fos05"]);
                    ln_fos06_oc = Fct.sdFat(dr["fos06"]);
                    ln_fos07_oc = Fct.sdFat(dr["fos07"]);
                    ln_fos08_oc = Fct.sdFat(dr["fos08"]);
                    ln_fos09_oc = Fct.sdFat(dr["fos09"]);
                    ln_fos10_oc = Fct.sdFat(dr["fos10"]);
                    ln_fos11_oc = Fct.sdFat(dr["fos11"]);
                    ln_fos12_oc = Fct.sdFat(dr["fos12"]);
                    ln_fos13_oc = Fct.sdFat(dr["fos13"]);
                    ln_fosA_oc = Fct.sdFat(dr["fosA"]);
                    ln_fosA2_oc = Fct.sdFat(dr["fosA2"]);
                    ln_fosB_oc = Fct.sdFat(dr["fosB"]);
                    ln_fosC_oc = Fct.sdFat(dr["fosC"]);
                    ln_fos_oc = Fct.sdFat(dr["fos"]);
                    if (Fct.stFat(dr["fzeropay"]) == "1")
                    {
                        if (Fct.sdFat(dr["fos01"]) > 0)
                        {
                            ln_adj01_oc = -Fct.sdFat(dr["fos01"]);
                        }
                        if (Fct.sdFat(dr["fos02"]) > 0)
                        {
                            ln_adj02_oc = -Fct.sdFat(dr["fos02"]);
                        }
                        if (Fct.sdFat(dr["fos03"]) > 0)
                        {
                            ln_adj03_oc = -Fct.sdFat(dr["fos03"]);
                        }
                        if (Fct.sdFat(dr["fos04"]) > 0)
                        {
                            ln_adj04_oc = -Fct.sdFat(dr["fos04"]);
                        }
                        if (Fct.sdFat(dr["fos05"]) > 0)
                        {
                            ln_adj05_oc = -Fct.sdFat(dr["fos05"]);
                        }
                        if (Fct.sdFat(dr["fos06"])< 0)
                        {
                            ln_adj06_oc = -Fct.sdFat(dr["fos06"]);
                        }
                        if (Fct.sdFat(dr["fos07"])< 0)
                        {
                            ln_adj07_oc = -Fct.sdFat(dr["fos07"]);
                        }
                        if (Fct.sdFat(dr["fos10"]) > 0)
                        {
                            ln_adj10_oc = -Fct.sdFat(dr["fos10"]);
                        }
                        ln_adjA_oc = ln_adj01_oc + ln_adj02_oc + ln_adj03_oc + ln_adj04_oc + ln_adj05_oc + ln_adj06_oc + ln_adj07_oc + ln_adj10_oc;
                    }
                    else
                    {
                        if (Fct.sdFat(dr["fosA"]) > 0)
                        {
                            ln_adjA_oc = -Fct.sdFat(dr["fosA"]);
                        }
                    }
                    if (Fct.sdFat(dr["fos08"]) > 0)
                    {
                        ln_adj08_oc = -Fct.sdFat(dr["fos08"]);
                    }
                    if (Fct.sdFat(dr["fos09"]) > 0)
                    {
                        ln_adj09_oc = -Fct.sdFat(dr["fos09"]);
                    }
                    if (Fct.sdFat(dr["fos11"]) > 0)
                    {
                        ln_adj11_oc = -Fct.sdFat(dr["fos11"]);
                    }
                    if (Fct.sdFat(dr["fos12"]) > 0)
                    {
                        ln_adj12_oc = -Fct.sdFat(dr["fos12"]);
                    }
                    if (Fct.sdFat(dr["fos13"]) > 0)
                    {
                        ln_adj13_oc = -Fct.sdFat(dr["fos13"]);
                    }
                }
            }
            if (p_calling == "PAY")
            {
                foreach (DataRow dr in DTocabk_oe.Rows)
                {
                    ln_fos01_oe = Fct.sdFat(dr["fos01"]);
                    ln_fos02_oe = Fct.sdFat(dr["fos02"]);
                    ln_fos03_oe = Fct.sdFat(dr["fos03"]);
                    ln_fos04_oe = Fct.sdFat(dr["fos04"]);
                    ln_fos05_oe = Fct.sdFat(dr["fos05"]);
                    ln_fos06_oe = Fct.sdFat(dr["fos06"]);
                    ln_fos07_oe = Fct.sdFat(dr["fos07"]);
                    ln_fos08_oe = Fct.sdFat(dr["fos08"]);
                    ln_fos09_oe = Fct.sdFat(dr["fos09"]);
                    ln_fos10_oe = Fct.sdFat(dr["fos10"]);
                    ln_fosA_oe = Fct.sdFat(dr["fosA"]);
                    ln_fosB_oe = Fct.sdFat(dr["fosB"]);
                    ln_fosC_oe = Fct.sdFat(dr["fosC"]);
                    ln_fos_oe = Fct.sdFat(dr["fos"]);
                    if (Fct.sdFat(dr["fos01"]) < 0)
                    {
                        ln_adj01_oe = -Fct.sdFat(dr["fos01"]);
                    }
                    if (Fct.sdFat(dr["fos02"]) < 0)
                    {
                        ln_adj02_oe = -Fct.sdFat(dr["fos02"]);
                    }
                    if (Fct.sdFat(dr["fos03"]) < 0)
                    {
                        ln_adj03_oe = -Fct.sdFat(dr["fos03"]);
                    }
                    if (Fct.sdFat(dr["fos04"]) < 0)
                    {
                        ln_adj04_oe = -Fct.sdFat(dr["fos04"]);
                    }
                    if (Fct.sdFat(dr["fos05"]) < 0)
                    {
                        ln_adj05_oe = -Fct.sdFat(dr["fos05"]);
                    }
                    if (Fct.sdFat(dr["fos06"]) < 0)
                    {
                        ln_adj06_oe = -Fct.sdFat(dr["fos06"]);
                    }
                    if (Fct.sdFat(dr["fos07"]) < 0)
                    {
                        ln_adj07_oe = -Fct.sdFat(dr["fos07"]);
                    }
                    if (Fct.sdFat(dr["fos08"]) < 0)
                    {
                        ln_adj08_oe = -Fct.sdFat(dr["fos08"]);
                    }
                    if (Fct.sdFat(dr["fos09"]) < 0)
                    {
                        ln_adj09_oe = -Fct.sdFat(dr["fos09"]);
                    }
                    if (Fct.sdFat(dr["fos10"]) < 0)
                    {
                        ln_adj10_oe = -Fct.sdFat(dr["fos10"]);
                    }
                }
                foreach (DataRow dr in DTocabk_oc.Rows)
                {
                    ln_fos01_oc = Fct.sdFat(dr["fos01"]);
                    ln_fos02_oc = Fct.sdFat(dr["fos02"]);
                    ln_fos03_oc = Fct.sdFat(dr["fos03"]);
                    ln_fos04_oc = Fct.sdFat(dr["fos04"]);
                    ln_fos05_oc = Fct.sdFat(dr["fos05"]);
                    ln_fos06_oc = Fct.sdFat(dr["fos06"]);
                    ln_fos07_oc = Fct.sdFat(dr["fos07"]);
                    ln_fos08_oc = Fct.sdFat(dr["fos08"]);
                    ln_fos09_oc = Fct.sdFat(dr["fos09"]);
                    ln_fos10_oc = Fct.sdFat(dr["fos10"]);
                    ln_fos11_oc = Fct.sdFat(dr["fos11"]);
                    ln_fos12_oc = Fct.sdFat(dr["fos12"]);
                    ln_fos13_oc = Fct.sdFat(dr["fos13"]);
                    ln_fosA_oc = Fct.sdFat(dr["fosA"]);
                    ln_fosA2_oc = Fct.sdFat(dr["fosA2"]);
                    ln_fosB_oc = Fct.sdFat(dr["fosB"]);
                    ln_fosC_oc = Fct.sdFat(dr["fosC"]);
                    ln_fos_oc = Fct.sdFat(dr["fos"]);
                    //if (Fct.stFat(dr["fzeropay"]) == "1")
                    //{
                    //    if (Fct.sdFat(dr["fos01"]) < 0)
                    //    {
                    //        ln_adj01_oc = -Fct.sdFat(dr["fos01"]);
                    //    }
                    //    if (Fct.sdFat(dr["fos02"]) < 0)
                    //    {
                    //        ln_adj02_oc = -Fct.sdFat(dr["fos02"]);
                    //    }
                    //    if (Fct.sdFat(dr["fos03"]) < 0)
                    //    {
                    //        ln_adj03_oc = -Fct.sdFat(dr["fos03"]);
                    //    }
                    //    if (Fct.sdFat(dr["fos04"]) < 0)
                    //    {
                    //        ln_adj04_oc = -Fct.sdFat(dr["fos04"]);
                    //    }
                    //    if (Fct.sdFat(dr["fos05"]) < 0)
                    //    {
                    //        ln_adj05_oc = -Fct.sdFat(dr["fos05"]);
                    //    }
                    //    if (Fct.sdFat(dr["fos06"]) < 0)
                    //    {
                    //        ln_adj06_oc = -Fct.sdFat(dr["fos06"]);
                    //    }
                    //    if (Fct.sdFat(dr["fos07"]) < 0)
                    //    {
                    //        ln_adj07_oc = -Fct.sdFat(dr["fos07"]);
                    //    }
                    //    if (Fct.sdFat(dr["fos10"]) < 0)
                    //    {
                    //        ln_adj10_oc = -Fct.sdFat(dr["fos10"]);
                    //    }
                    //    ln_adjA_oc = ln_adj01_oc + ln_adj02_oc + ln_adj03_oc + ln_adj04_oc + ln_adj05_oc + ln_adj06_oc + ln_adj07_oc + ln_adj10_oc;
                    //}
                    //else
                    //{
                    //    if (Fct.sdFat(dr["fosA"]) < 0)
                    //    {
                    //        ln_adjA_oc = -Fct.sdFat(dr["fosA"]);
                    //    }
                    //}

                    if (Fct.sdFat(dr["fos08"]) < 0)
                    {
                        ln_adj08_oc = -Fct.sdFat(dr["fos08"]);
                    }
                    if (Fct.sdFat(dr["fos09"]) < 0)
                    {
                        ln_adj09_oc = -Fct.sdFat(dr["fos09"]);
                    }
                    if (Fct.sdFat(dr["fos11"]) < 0)
                    {
                        ln_adj11_oc = -Fct.sdFat(dr["fos11"]);
                    }
                    if (Fct.sdFat(dr["fos12"]) < 0)
                    {
                        ln_adj12_oc = -Fct.sdFat(dr["fos12"]);
                    }
                    if (Fct.sdFat(dr["fos13"]) < 0)
                    {
                        ln_adj13_oc = -Fct.sdFat(dr["fos13"]);
                    }
                    if (Fct.sdFat(dr["fosA"]) < 0)
                    {
                        ln_adjA_oc = -Fct.sdFat(dr["fosA"]);
                    }
                }
            }
            if (p_calling == "INTADJ")
            {
                foreach (DataRow dr in DTocabk_oe.Rows)
                {
                    ln_fos01_oe = Fct.sdFat(dr["fos01"]);
                    ln_fos02_oe = Fct.sdFat(dr["fos02"]);
                    ln_fos03_oe = Fct.sdFat(dr["fos03"]);
                    ln_fos04_oe = Fct.sdFat(dr["fos04"]);
                    ln_fos05_oe = Fct.sdFat(dr["fos05"]);
                    ln_fos06_oe = Fct.sdFat(dr["fos06"]);
                    ln_fos07_oe = Fct.sdFat(dr["fos07"]);
                    ln_fos08_oe = Fct.sdFat(dr["fos08"]);
                    ln_fos09_oe = Fct.sdFat(dr["fos09"]);
                    ln_fos10_oe = Fct.sdFat(dr["fos10"]);
                    ln_fosA_oe = Fct.sdFat(dr["fosA"]);
                    ln_fosB_oe = Fct.sdFat(dr["fosB"]);
                    ln_fosC_oe = Fct.sdFat(dr["fosC"]);
                    ln_fos_oe = Fct.sdFat(dr["fos"]);
                    ln_adj01_oe = 0;
                    ln_adj02_oe = 0;
                    ln_adj03_oe = 0;
                    ln_adj04_oe = 0;
                    ln_adj05_oe = 0;
                    ln_adj06_oe = 0;
                    ln_adj07_oe = 0;
                    ln_adj08_oe = 0;
                    ln_adj09_oe = 0;
                    ln_adj10_oe = 0;
                }
                foreach (DataRow dr in DTocabk_oc.Rows)
                {
                    ln_fos01_oc = Fct.sdFat(dr["fos01"]);
                    ln_fos02_oc = Fct.sdFat(dr["fos02"]);
                    ln_fos03_oc = Fct.sdFat(dr["fos03"]);
                    ln_fos04_oc = Fct.sdFat(dr["fos04"]);
                    ln_fos05_oc = Fct.sdFat(dr["fos05"]);
                    ln_fos06_oc = Fct.sdFat(dr["fos06"]);
                    ln_fos07_oc = Fct.sdFat(dr["fos07"]);
                    ln_fos08_oc = Fct.sdFat(dr["fos08"]);
                    ln_fos09_oc = Fct.sdFat(dr["fos09"]);
                    ln_fos10_oc = Fct.sdFat(dr["fos10"]);
                    ln_fos11_oc = Fct.sdFat(dr["fos11"]);
                    ln_fos12_oc = Fct.sdFat(dr["fos12"]);
                    ln_fos13_oc = Fct.sdFat(dr["fos13"]);
                    ln_fosA_oc = Fct.sdFat(dr["fosA"]);
                    ln_fosA2_oc = Fct.sdFat(dr["fosA2"]);
                    ln_fosB_oc = Fct.sdFat(dr["fosB"]);
                    ln_fosC_oc = Fct.sdFat(dr["fosC"]);
                    ln_fos_oc = Fct.sdFat(dr["fos"]);
                    if (Fct.stFat(dr["fzeropay"]) == "1")
                    {
                        ln_adj01_oc = 0;
                        ln_adj02_oc = 0;
                        ln_adj03_oc = 0;
                        ln_adj04_oc = 0;
                        ln_adj05_oc = 0;
                        ln_adj06_oc = 0;
                        ln_adj07_oc = 0;
                        ln_adj10_oc = 0;
                    }
                    ln_adjA_oc = 0;
                    ln_adj08_oc = 0;
                    ln_adj09_oc = 0;
                    ln_adj11_oc = 0;
                    ln_adj12_oc = 0;
                    ln_adj13_oc = 0;
                }
            }
            String Sql = "select LEFT(CONVERT(VARCHAR,b.ld_faccdate, 120), 10) as ld_faccdate from  " +
                    "(select case when getdate() between fdatefr and fdateto then getdate() else fnxtdate end ld_faccdate  " +
                    "from " + Dbm + "xsysperiod where fidtype ='CLMMON') b";
            DataTable dt = DBHelper.GetDataSet(Sql);
            if (dt != null && dt.Rows.Count > 0)
            {
                if (p_calling == "CLOSE")
                {
                    ld_faccdate = dt.Rows[0]["ld_faccdate"].ToString().Trim();
                }
                else
                {
                    ld_faccdate = finvdate;
                }
            }
            ArrayList Addsql = new ArrayList();
            string fritype="",ln_fclass = "", ln_fsclass = "", ln_fclmno = "", ln_fpolno = "", ln_fendtno = "", ln_fctlid_p = "", ln_fctlid_e = "", ln_fctlid_1 = "";
            string fxol = "", ffacnp ="", ffacbsec = "", ffacnplyr = "", fxolcode = "", fxollayr = "", fttycode = "", fttysec = "", ffacbcode = "";
            string sqloclaim = "select a.*,b.fritype from oclaim a left join ocfac b on a.fctlid = b.fctlid_1 where fclmno ='" + fclmno + "'";
            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["odata"].ConnectionString);
            SqlCommand cmd = new SqlCommand(sqloclaim, con);
            con.Open();
            using (SqlDataReader sdr = cmd.ExecuteReader())
            {
                if (sdr.HasRows && sdr.Read())
                {
                    ln_fclass = Fct.stFat(sdr["fclass"].ToString().Trim());
                    ln_fsclass = Fct.stFat(sdr["fsclass"].ToString().Trim());
                    ln_fclmno = Fct.stFat(sdr["fclmno"].ToString().Trim());
                    ln_fpolno = Fct.stFat(sdr["fpolno"].ToString().Trim());
                    ln_fendtno = Fct.stFat(sdr["fendtno"].ToString().Trim());
                    ln_fctlid_p = Fct.stFat(sdr["fctlid_p"].ToString().Trim());
                    ln_fctlid_e = Fct.stFat(sdr["fctlid_e"].ToString().Trim());
                    ln_fctlid_1 = Fct.stFat(sdr["fctlid"].ToString().Trim());
                    ffacbsec = Fct.stFat(sdr["ffacbsec"].ToString().Trim());
                    ffacnplyr = Fct.stFat(sdr["ffacnplyr"].ToString().Trim());
                    fxolcode = Fct.stFat(sdr["fxolcode"].ToString().Trim());
                    fxollayr = Fct.stFat(sdr["fxollayr"].ToString().Trim());
                    fttycode = Fct.stFat(sdr["fttycode"].ToString().Trim());
                    fttysec = Fct.stFat(sdr["fttysec"].ToString().Trim());
                    ffacbcode = Fct.stFat(sdr["ffacbcode"].ToString().Trim());
                    fxol = Fct.stFat(sdr["fxol"].ToString().Trim());
                    ffacnp = Fct.stFat(sdr["ffacnp"].ToString().Trim());
                    ln_fttysh = Fct.sdFat(sdr["fttysh"].ToString().Trim());
                    ln_ffacsh = Fct.sdFat(sdr["ffacsh"].ToString().Trim());
                    ln_ffacbsh = Fct.sdFat(sdr["ffacbsh"].ToString().Trim());
                    fritype = Fct.stFat(sdr["fritype"].ToString().Trim());
                }
            }
            ln_famount = ln_adj01_oe + ln_adj02_oe + ln_adj03_oe +
                     ln_adj04_oe + ln_adj05_oe + ln_adj06_oe +
                     ln_adj07_oe + ln_adj08_oe + ln_adj09_oe + ln_adj10_oe +
                     ln_adjA_oc + ln_adj08_oc + ln_adj09_oc + ln_adj11_oc + ln_adj12_oc + ln_adj13_oc;
            ln_fecamt = ln_adj01_oe + ln_adj02_oe + ln_adj03_oe +
                    ln_adj04_oe + ln_adj05_oe + ln_adj06_oe +
                    ln_adj07_oe + ln_adj08_oe + ln_adj09_oe + ln_adj10_oe;
            ln_fclawamt = ln_adj08_oc + ln_adj09_oc + ln_adj11_oc + ln_adj12_oc + ln_adj13_oc + ln_adjA_oc;

            string str2 = "select * from oclaim_s where fctlid_1=(select fctlid from oclaim where fclmno ='" + fclmno + "')";
            DataTable DToclaim_s = DBHelper.GetDataSet(str2);
            if (DToclaim_s.Rows.Count > 0)
            {
                ls_fosamt = Fct.sdFat(DToclaim_s.Rows[0]["fosamt"]);
                ls_fosretn = Fct.sdFat(DToclaim_s.Rows[0]["fosretn"]);
                ls_fostty = Fct.sdFat(DToclaim_s.Rows[0]["fostty"]);
                ls_fosfac = Fct.sdFat(DToclaim_s.Rows[0]["fosfac"]);
                ls_fosfacb = Fct.sdFat(DToclaim_s.Rows[0]["fosfacb"]);
                ls_fosfacnp = Fct.sdFat(DToclaim_s.Rows[0]["fosfacnp"]);
                ls_fosxol = Fct.sdFat(DToclaim_s.Rows[0]["fosxol"]);
            }

            if (p_calling == "CLOSE")
            {
                ln_fretn = -ls_fosretn;
                ln_ftty = -ls_fostty;
                ln_ffac = -ls_fosfac;
                ln_ffacb = -ls_fosfacb;
                ln_ffacnp = -ls_fosfacnp;
                ln_fxol = -ls_fosxol;
            }

            if (p_calling == "PAY")
            {
                ln_ftty = Math.Round(ln_famount * ln_fttysh / 100, 2, MidpointRounding.AwayFromZero);
                ln_ffac = Math.Round(ln_famount * ln_ffacsh / 100, 2, MidpointRounding.AwayFromZero);
                ln_ffacb = Math.Round(ln_famount * ln_ffacbsh / 100, 2, MidpointRounding.AwayFromZero);
                ln_fretn = ln_famount - ln_ftty - ln_ffac - ln_ffacb;
                ln_fxol = 0;
                ln_ffacnp = 0;

                if (fxol == "1")
                {
                    ln_fxol = ln_fretn;
                    ln_fretn = 0;
                }
                else
                {
                    if (ffacnp == "1")
                    {
                        ln_ffacnp = ln_fretn;
                        ln_fretn = 0;
                    }
                }
            }

            if (p_calling == "INTADJ") {
                DataTable ocpay = DBHelper.GetDataSet("select * from ocpay where fctlid ='" + fctlid_p + "'");
                if (ocpay.Rows.Count > 0)
                {
                    ln_fretn = Fct.sdFat(ocpay.Rows[0]["fretn"]);
                    ln_ftty = Fct.sdFat(ocpay.Rows[0]["ftty"]);
                    ln_ffac = Fct.sdFat(ocpay.Rows[0]["ffac"]);
                    ln_ffacb = Fct.sdFat(ocpay.Rows[0]["ffacb"]);
                    ln_ffacnp = Fct.sdFat(ocpay.Rows[0]["ffacnp"]);
                    ln_fxol = Fct.sdFat(ocpay.Rows[0]["fxol"]);
                }
            }

            string ocadjsql = "select * from ocadj where fctlid = (select max(fctlid) from ocadj where fclmno = '" + fclmno + "')";
            DataTable ocadjdt = DBHelper.GetDataSet(ocadjsql);
            foreach (DataRow dr in ocadjdt.Rows)
            {
                ln_flogseq = Fct.sdFat(dr["flogseq"]) + 1;
                fnature = Fct.stFat(dr["fnature"]);
            }
            string insertsql = "INSERT INTO [dbo].[ocadj]([fclass],[fsclass],[fclmno],[fpolno],[fendtno],[fctlid_p],[fctlid_e],[fctlid_1],[fctlid],[ftrntype],[fnature],[flogseq], " +
                                      "[faccdate],[fecamt],[fclawamt],[famount],[fretn],[ftty],[ffac],[ffacb],[ffacnp],[fxol],[fttycode],[fttysec],[ffacbcode],[ffacbsec],[ffacnplyr], " +
                                      "[fxolcode],[fxollayr],[fstatus],[finpuser],[finpdate],[fupduser],[fupddate],[fcnfuser],[fcnfdate],[flyrid]) " +
                                       "VALUES ('" + ln_fclass + "','" + ln_fsclass + "','" + ln_fclmno + "','" + ln_fpolno + "','" + ln_fendtno + "', " +
                                       "'" + ln_fctlid_p + "','" + ln_fctlid_e + "','" + ln_fctlid_1 + "','" + Fct.NewId("ocadj") + "','3','1','" + ln_flogseq + "',convert(datetime,'" + Fct.dateFat(ld_faccdate) + "',120), " +
                                       "'" + ln_fecamt + "','" + ln_fclawamt + "','" + ln_famount + "','" + ln_fretn + "','" + ln_ftty + "','" + ln_ffac + "','" + ln_ffacb + "', " +
                                       "'" + ln_ffacnp + "','" + ln_fxol + "','" + fttycode + "','" + fttysec + "','" + ffacbcode + "', " +
                                       "'" + ffacbsec + "','" + ffacnplyr + "','" + fxolcode + "','" + fxollayr + "','3','" + user + "','" + date + "', " +
                                       "'" + user + "','" + date + "','" + user + "','" + date + "',null) ";
            Addsql.Add(insertsql);
            string sqlfctlid = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(Fct.NewId("ocadj")) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='ocadj'";
            Addsql.Add(sqlfctlid);

            decimal ln_totfac = 0; DataTable dtcadjfac;
            if (ln_famount == -ls_fosamt && p_calling != "INTADJ")
            {
                string lc_fctlid = Fct.NewId("ocadj");
                SqlParameter[] param = new SqlParameter[] {
                    new SqlParameter("@fctlid_1", ln_fctlid_1),
                    new SqlParameter("@fctlid_2", lc_fctlid),
                    new SqlParameter("@fnature", fnature),
                    new SqlParameter("@fritype","04")};
                dtcadjfac = DBHelper.GetDataSetProd("cadjfacGen", Dbo, param);
            }
            else
            {
                string lc_fctlid = Fct.NewId("ocadj");
                if (fritype == "04")
                {
                    string str3 = "select b.fclass, b.fsclass, b.fclmno, b.fpolno, b.fendtno,b.fctlid_p,b.fctlid_e,fctlid_1, " +
                                    "'" + lc_fctlid + "' as fctlid_2,'' as fctlid, '04' as fritype, '1' as fnature,flyrid, " +
                                    "flogseq,frinsrref,freinsr,fbrkr,round(a.fshare/100 * " + ln_famount + ",2) as famount,d.fdesc as fbrkrN, e.fdesc as freinsrN,a.fshare from ocfac a " +
                                    "left join oclaim b on a.fctlid_1= b.fctlid " +
                                    "left join (select fid,fdesc from " + Dbm + "mprdr where ftype='B') d on d.fid = a.fbrkr " +
                                    "left join (select fid,fdesc from " + Dbm + "mprdr where ftype='R') e on e.fid = a.freinsr " +
                                    "where fritype='04' and fclmno ='" + fclmno + "'";
                    dtcadjfac = DBHelper.GetDataSet(str3);
                }
                else
                {
                    string str4 = "select b.fclass, b.fsclass, b.fclmno, b.fpolno, b.fendtno,b.fctlid_p,b.fctlid_e,fctlid_1, " +
                                    "'" + lc_fctlid + "' as fctlid_2,'' as fctlid, '06' as fritype, '1' as fnature,flyrid, " +
                                    "flogseq,frinsrref,freinsr,fbrkr,round(a.fshare/100 * " + ln_ffacnp + ",2) as famount,d.fdesc as fbrkrN, e.fdesc as freinsrN,a.fshare from ocfac a " +
                                    "left join oclaim b on a.fctlid_1= b.fctlid " +
                                    "left join (select fid,fdesc from " + Dbm + "mprdr where ftype='B') d on d.fid = a.fbrkr " +
                                    "left join (select fid,fdesc from " + Dbm + "mprdr where ftype='R') e on e.fid = a.freinsr " +
                                    "where fritype='06'  and fclmno ='" + fclmno + "'";
                    dtcadjfac = DBHelper.GetDataSet(str4);
                }
                foreach (DataRow dr in dtcadjfac.Rows)
                {
                    ln_totfac = ln_totfac + Fct.sdFat(dr["famount"]);
                }
                if (dtcadjfac.Rows.Count > 0)
                {
                    dtcadjfac.Rows[0]["famount"] = Fct.sdFat(dtcadjfac.Rows[0]["famount"]) + ln_ffac - ln_totfac;
                }
            }

            string fctlid_ocadjfac = Fct.NewId("ocadjfac");
            if (dtcadjfac.Select("LEN(fctlid)<10").Length > 0)
            {
                for (int j = 0; j < dtcadjfac.Rows.Count; j++)
                {
                    if (j != 0) { fctlid_ocadjfac = (int.Parse(fctlid_ocadjfac) + 1).ToString().PadLeft(10, '0'); }
                    string sql = "INSERT INTO [dbo].[ocadjfac]([fclass],[fsclass],[fclmno],[fpolno],[fendtno],[fctlid_p],[fctlid_e],[fctlid_1],[fctlid_2],[fctlid],[fritype], " +
                                 "[fnature],[flogseq],[frinsrref],[freinsr],[fbrkr],[famount],[flyrid]) " +
                                 "VALUES('" + ln_fclass + "','" + ln_fsclass + "','" + ln_fclmno + "','" + ln_fpolno + "','" + ln_fendtno + "','" + ln_fctlid_p + "','" + ln_fctlid_e + "', " +
                                 "'" + ln_fctlid_1 + "','" + Fct.NewId("ocadj") + "','" + fctlid_ocadjfac + "','" + Fct.stFat(dtcadjfac.Rows[j]["fritype"]) + "','" + Fct.stFat(dtcadjfac.Rows[j]["fnature"]) + "','" + Fct.sdFat(dtcadjfac.Rows[j]["flogseq"]) + "', " +
                                 "'" + Fct.stFat(dtcadjfac.Rows[j]["frinsrref"]) + "','" + Fct.stFat(dtcadjfac.Rows[j]["freinsr"]) + "','" + Fct.stFat(dtcadjfac.Rows[j]["fbrkr"]) + "','" + Fct.sdFat(dtcadjfac.Rows[j]["famount"]) + "','" + Fct.sdFat(dtcadjfac.Rows[j]["flyrid"]) + "')";
                    Addsql.Add(sql);
                } 
                string sqlfctlid2 = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_ocadjfac) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='ocadjfac'";
                Addsql.Add(sqlfctlid2);
            }

            string ocabk_esql = "select * from ocabk_e where fctlid = (select max(fctlid) from ocabk_e where fctlid_c =(select fctlid from oclaim where fclmno= '" + fclmno + "'))";
            DataTable ocabk_edt = DBHelper.GetDataSet(ocabk_esql);
            foreach (DataRow dr in ocabk_edt.Rows)
            {
                ln_flogseq = Fct.sdFat(dr["flogseq"]) + 1;
                dr["fctlid"] = Fct.NewId("ocabk_e");
                dr["fctlid_a"] = Fct.NewId("ocadj");
                dr["flogseq"] = ln_flogseq;
                dr["fdutype"] = "1";
                dr["ftrntype"] = "3";
                dr["fos01"] = ln_fos01_oe;
                dr["fos02"] = ln_fos02_oe;
                dr["fos03"] = ln_fos03_oe;
                dr["fos04"] = ln_fos04_oe;
                dr["fos05"] = ln_fos05_oe;
                dr["fos06"] = ln_fos06_oe;
                dr["fos07"] = ln_fos07_oe;
                dr["fos08"] = ln_fos08_oe;
                dr["fos09"] = ln_fos09_oe;
                dr["fos10"] = ln_fos10_oe;
                dr["fosA"] = ln_fosA_oe;
                dr["fosB"] = ln_fosB_oe;
                dr["fosC"] = ln_fosC_oe;
                dr["fos"] = ln_fos_oe;
                dr["fadj01"] = ln_adj01_oe;
                dr["fadj02"] = ln_adj02_oe;
                dr["fadj03"] = ln_adj03_oe;
                dr["fadj04"] = ln_adj04_oe;
                dr["fadj05"] = ln_adj05_oe;
                dr["fadj06"] = ln_adj06_oe;
                dr["fadj07"] = ln_adj07_oe;
                dr["fadj08"] = ln_adj08_oe;
                dr["fadj09"] = ln_adj09_oe;
                dr["fadj10"] = ln_adj10_oe;
                dr["fadjA"] = ln_adj01_oe + ln_adj02_oe + ln_adj03_oe + ln_adj04_oe + ln_adj05_oe + ln_adj06_oe;
                dr["fadjB"] = ln_adj07_oe;
                dr["fadjC"] = ln_adj08_oe + ln_adj09_oe + ln_adj10_oe;
                dr["fadj"] = Fct.sdFat(dr["fadjA"]) + Fct.sdFat(dr["fadjB"]) + Fct.sdFat(dr["fadjC"]);
                dr["frvos01"] = Fct.sdFat(dr["fos01"]) + Fct.sdFat(dr["fadj01"]);
                dr["frvos02"] = Fct.sdFat(dr["fos02"]) + Fct.sdFat(dr["fadj02"]);
                dr["frvos03"] = Fct.sdFat(dr["fos03"]) + Fct.sdFat(dr["fadj03"]);
                dr["frvos04"] = Fct.sdFat(dr["fos04"]) + Fct.sdFat(dr["fadj04"]);
                dr["frvos05"] = Fct.sdFat(dr["fos05"]) + Fct.sdFat(dr["fadj05"]);
                dr["frvos06"] = Fct.sdFat(dr["fos06"]) + Fct.sdFat(dr["fadj06"]);
                dr["frvos07"] = Fct.sdFat(dr["fos07"]) + Fct.sdFat(dr["fadj07"]);
                dr["frvos08"] = Fct.sdFat(dr["fos08"]) + Fct.sdFat(dr["fadj08"]);
                dr["frvos09"] = Fct.sdFat(dr["fos09"]) + Fct.sdFat(dr["fadj09"]);
                dr["frvos10"] = Fct.sdFat(dr["fos10"]) + Fct.sdFat(dr["fadj10"]);
                dr["frvosA"] = Fct.sdFat(dr["fosA"]) + Fct.sdFat(dr["fadjA"]);
                dr["frvosB"] = Fct.sdFat(dr["fosB"]) + Fct.sdFat(dr["fadjB"]);
                dr["frvosC"] = Fct.sdFat(dr["fosC"]) + Fct.sdFat(dr["fadjC"]);
                dr["frvos"] = Fct.sdFat(dr["fos"]) + Fct.sdFat(dr["fadj"]);
            }

            string sqlocabk_e = "INSERT INTO [dbo].[ocabk_e]([fctlid],[fctlid_c],[fctlid_a],[ftrntype],[flogseq],[fdutype],[fttlday],[fnilpay1],[fnilpay2],[ffrm01],[fto01],[fnofday01], " +
                                "[ffrm02],[fto02],[fnofday02],[ffrm03],[fto03],[fnofday03],[ffrm04],[fto04],[fnofday04],[ffrm05],[fto05],[fnofday05],[ffrm06],[fto06],[fnofday06],[ffrm07], " +
                                "[fto07],[fnofday07],[ffrm08],[fto08],[fnofday08],[ffrm09],[fto09],[fnofday09],[ffrm10],[fto10],[fnofday10],[ffrm11],[fto11],[fnofday11],[ffrm12],[fto12], " +
                                "[fnofday12],[ffrm13],[fto13],[fnofday13],[ffrm14],[fto14],[fnofday14],[ffrm15],[fto15],[fnofday15],[fwgtype],[ffactor],[fwage],[fnlength],[famt01],[fpay01], " +
                                "[fos01],[frvos01],[fadj01],[fincpamt],[fnlen2],[fincprate],[famt02],[fpay02],[fos02],[frvos02],[fadj02],[fos03],[frvos03],[fadj03],[fos04],[frvos04],[fadj04], " +
                                "[fos05],[frvos05],[fadj05],[fos06],[frvos06],[fadj06],[fos07],[frvos07],[fadj07],[fos08],[frvos08],[fadj08],[fos09],[frvos09],[fadj09],[fos10],[frvos10],[fadj10], " +
                                "[fosA],[frvosA],[fadjA],[fosB],[frvosB],[fadjB],[fosC],[frvosC],[fadjC],[fos],[frvos],[fadj],[fremark_t1]) " +
                                "VALUES('" + Fct.stFat(ocabk_edt.Rows[0]["fctlid"]) + "','" + Fct.stFat(ocabk_edt.Rows[0]["fctlid_c"]) + "','" + Fct.stFat(ocabk_edt.Rows[0]["fctlid_a"]) + "','3', " +
                                "'" + Fct.sdFat(ocabk_edt.Rows[0]["flogseq"]) + "','1','" + Fct.sdFat(ocabk_edt.Rows[0]["fttlday"]) + "','0','0', " +
                                "case when '" + ocabk_edt.Rows[0]["ffrm01"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["ffrm01"]) + "',120) end,case when '" + ocabk_edt.Rows[0]["fto01"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["fto01"]) + "',120) end,'" + ocabk_edt.Rows[0]["fnofday01"] + "', " +
                                "case when '" + ocabk_edt.Rows[0]["ffrm02"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["ffrm02"]) + "',120) end,case when '" + ocabk_edt.Rows[0]["fto02"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["fto02"]) + "',120) end,'" + ocabk_edt.Rows[0]["fnofday02"] + "', " +
                                "case when '" + ocabk_edt.Rows[0]["ffrm03"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["ffrm03"]) + "',120) end,case when '" + ocabk_edt.Rows[0]["fto03"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["fto03"]) + "',120) end,'" + ocabk_edt.Rows[0]["fnofday03"] + "', " +
                                "case when '" + ocabk_edt.Rows[0]["ffrm04"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["ffrm04"]) + "',120) end,case when '" + ocabk_edt.Rows[0]["fto04"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["fto04"]) + "',120) end,'" + ocabk_edt.Rows[0]["fnofday04"] + "', " +
                                "case when '" + ocabk_edt.Rows[0]["ffrm05"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["ffrm05"]) + "',120) end,case when '" + ocabk_edt.Rows[0]["fto05"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["fto05"]) + "',120) end,'" + ocabk_edt.Rows[0]["fnofday05"] + "', " +
                                "case when '" + ocabk_edt.Rows[0]["ffrm06"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["ffrm06"]) + "',120) end,case when '" + ocabk_edt.Rows[0]["fto06"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["fto06"]) + "',120) end,'" + ocabk_edt.Rows[0]["fnofday06"] + "', " +
                                "case when '" + ocabk_edt.Rows[0]["ffrm07"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["ffrm07"]) + "',120) end,case when '" + ocabk_edt.Rows[0]["fto07"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["fto07"]) + "',120) end,'" + ocabk_edt.Rows[0]["fnofday07"] + "', " +
                                "case when '" + ocabk_edt.Rows[0]["ffrm08"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["ffrm08"]) + "',120) end,case when '" + ocabk_edt.Rows[0]["fto08"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["fto08"]) + "',120) end,'" + ocabk_edt.Rows[0]["fnofday08"] + "', " +
                                "case when '" + ocabk_edt.Rows[0]["ffrm09"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["ffrm09"]) + "',120) end,case when '" + ocabk_edt.Rows[0]["fto09"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["fto09"]) + "',120) end,'" + ocabk_edt.Rows[0]["fnofday09"] + "', " +
                                "case when '" + ocabk_edt.Rows[0]["ffrm10"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["ffrm10"]) + "',120) end,case when '" + ocabk_edt.Rows[0]["fto10"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["fto10"]) + "',120) end,'" + ocabk_edt.Rows[0]["fnofday10"] + "', " +
                                "case when '" + ocabk_edt.Rows[0]["ffrm11"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["ffrm11"]) + "',120) end,case when '" + ocabk_edt.Rows[0]["fto11"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["fto11"]) + "',120) end,'" + ocabk_edt.Rows[0]["fnofday11"] + "', " +
                                "case when '" + ocabk_edt.Rows[0]["ffrm12"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["ffrm12"]) + "',120) end,case when '" + ocabk_edt.Rows[0]["fto12"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["fto12"]) + "',120) end,'" + ocabk_edt.Rows[0]["fnofday12"] + "', " +
                                "case when '" + ocabk_edt.Rows[0]["ffrm13"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["ffrm13"]) + "',120) end,case when '" + ocabk_edt.Rows[0]["fto13"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["fto13"]) + "',120) end,'" + ocabk_edt.Rows[0]["fnofday13"] + "', " +
                                "case when '" + ocabk_edt.Rows[0]["ffrm14"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["ffrm14"]) + "',120) end,case when '" + ocabk_edt.Rows[0]["fto14"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["fto14"]) + "',120) end,'" + ocabk_edt.Rows[0]["fnofday14"] + "', " +
                                "case when '" + ocabk_edt.Rows[0]["ffrm15"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["ffrm15"]) + "',120) end,case when '" + ocabk_edt.Rows[0]["fto15"] + "' = '' then null else convert(datetime,'" + Fct.dateFat(ocabk_edt.Rows[0]["fto15"]) + "',120) end,'" + ocabk_edt.Rows[0]["fnofday15"] + "', " +
                                "'" + Fct.stFat(ocabk_edt.Rows[0]["fwgtype"]) + "','" + Fct.stFat(ocabk_edt.Rows[0]["ffactor"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["fwage"]) + "', " +
                                "'" + Fct.sdFat(ocabk_edt.Rows[0]["fnlength"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["famt01"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["fpay01"]) + "', " +
                                "'" + Fct.sdFat(ocabk_edt.Rows[0]["fos01"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["frvos01"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["fadj01"]) + "', " +
                                "'" + Fct.sdFat(ocabk_edt.Rows[0]["fincpamt"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["fnlen2"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["fincprate"]) + "', " +
                                "'" + Fct.sdFat(ocabk_edt.Rows[0]["famt02"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["fpay02"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["fos02"]) + "', " +
                                "'" + Fct.sdFat(ocabk_edt.Rows[0]["frvos02"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["fadj02"]) + "', '" + Fct.sdFat(ocabk_edt.Rows[0]["fos03"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["frvos03"]) + "', " +
                                "'" + Fct.sdFat(ocabk_edt.Rows[0]["fadj03"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["fos04"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["frvos04"]) + "', " +
                                "'" + Fct.sdFat(ocabk_edt.Rows[0]["fadj04"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["fos05"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["frvos05"]) + "', " +
                                "'" + Fct.sdFat(ocabk_edt.Rows[0]["fadj05"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["fos06"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["frvos06"]) + "', " +
                                "'" + Fct.sdFat(ocabk_edt.Rows[0]["fadj06"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["fos07"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["frvos07"]) + "', " +
                                "'" + Fct.sdFat(ocabk_edt.Rows[0]["fadj07"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["fos08"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["frvos08"]) + "', " +
                                "'" + Fct.sdFat(ocabk_edt.Rows[0]["fadj08"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["fos09"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["frvos09"]) + "', " +
                                "'" + Fct.sdFat(ocabk_edt.Rows[0]["fadj09"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["fos10"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["frvos10"]) + "', " +
                                "'" + Fct.sdFat(ocabk_edt.Rows[0]["fadj10"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["fosA"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["frvosA"]) + "', " +
                                "'" + Fct.sdFat(ocabk_edt.Rows[0]["fadjA"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["fosB"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["frvosB"]) + "', " +
                                "'" + Fct.sdFat(ocabk_edt.Rows[0]["fadjB"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["fosC"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["frvosC"]) + "', " +
                                "'" + Fct.sdFat(ocabk_edt.Rows[0]["fadjC"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["fos"]) + "','" + Fct.sdFat(ocabk_edt.Rows[0]["frvos"]) + "', " +
                                "'" + Fct.sdFat(ocabk_edt.Rows[0]["fadj"]) + "','')";
            Addsql.Add(sqlocabk_e);
            string sqlfctlid3 = "update " + Dbm + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='OCABK_E'";
            Addsql.Add(sqlfctlid3);

            string str5 = "select * from ocabk_oe where fctlid_c=(select fctlid from oclaim where fclmno ='" + fclmno + "')";
            DataTable tcabk_oe = DBHelper.GetDataSet(str5);
            foreach (DataRow dr in tcabk_oe.Rows)
            {
                dr["fadj01"] = Fct.sdFat(dr["fadj01"]) + ln_adj01_oe;
                dr["fadj02"] = Fct.sdFat(dr["fadj02"]) + ln_adj02_oe;
                dr["fadj03"] = Fct.sdFat(dr["fadj03"]) + ln_adj03_oe;
                dr["fadj04"] = Fct.sdFat(dr["fadj04"]) + ln_adj04_oe;
                dr["fadj05"] = Fct.sdFat(dr["fadj05"]) + ln_adj05_oe;
                dr["fadj06"] = Fct.sdFat(dr["fadj06"]) + ln_adj06_oe;
                dr["fadj07"] = Fct.sdFat(dr["fadj07"]) + ln_adj07_oe;
                dr["fadj08"] = Fct.sdFat(dr["fadj08"]) + ln_adj08_oe;
                dr["fadj09"] = Fct.sdFat(dr["fadj09"]) + ln_adj09_oe;
                dr["fadj10"] = Fct.sdFat(dr["fadj10"]) + ln_adj10_oe;
                dr["fadjA"] = Fct.sdFat(dr["fadj01"]) + Fct.sdFat(dr["fadj02"]) + Fct.sdFat(dr["fadj03"]) + Fct.sdFat(dr["fadj04"]) + Fct.sdFat(dr["fadj05"]) + Fct.sdFat(dr["fadj06"]);
                dr["fadjB"] = Fct.sdFat(dr["fadj07"]);
                dr["fadjC"] = Fct.sdFat(dr["fadj08"]) + Fct.sdFat(dr["fadj09"]) + Fct.sdFat(dr["fadj10"]);
                dr["fadj"] = Fct.sdFat(dr["fadjA"]) + Fct.sdFat(dr["fadjB"]) + Fct.sdFat(dr["fadjC"]);

                dr["fos01"] = Fct.sdFat(dr["fos01"]) + ln_adj01_oe;
                dr["fos02"] = Fct.sdFat(dr["fos02"]) + ln_adj02_oe;
                dr["fos03"] = Fct.sdFat(dr["fos03"]) + ln_adj03_oe;
                dr["fos04"] = Fct.sdFat(dr["fos04"]) + ln_adj04_oe;
                dr["fos05"] = Fct.sdFat(dr["fos05"]) + ln_adj05_oe;
                dr["fos06"] = Fct.sdFat(dr["fos06"]) + ln_adj06_oe;
                dr["fos07"] = Fct.sdFat(dr["fos07"]) + ln_adj07_oe;
                dr["fos08"] = Fct.sdFat(dr["fos08"]) + ln_adj08_oe;
                dr["fos09"] = Fct.sdFat(dr["fos09"]) + ln_adj09_oe;
                dr["fos10"] = Fct.sdFat(dr["fos10"]) + ln_adj10_oe;
                dr["fosA"] = Fct.sdFat(dr["fosA"]) + ln_adj01_oe + ln_adj02_oe + ln_adj03_oe + ln_adj04_oe + ln_adj05_oe + ln_adj06_oe;
                dr["fosB"] = Fct.sdFat(dr["fosB"]) + ln_adj07_oe;
                dr["fosC"] = Fct.sdFat(dr["fosC"]) + ln_adj08_oe + ln_adj09_oe + ln_adj10_oe;
                dr["fos"] = Fct.sdFat(dr["fos"]) + ln_adj01_oe + ln_adj02_oe + ln_adj03_oe + ln_adj04_oe + ln_adj05_oe + ln_adj06_oe + ln_adj07_oe + ln_adj08_oe + ln_adj09_oe + ln_adj10_oe;
            }
            string sqlocabk_oe = "update ocabk_oe set fadj01 = '" + Fct.sdFat(tcabk_oe.Rows[0]["fadj01"]) + "', fadj02 ='" + Fct.sdFat(tcabk_oe.Rows[0]["fadj02"]) + "', " +
                   "fadj03 = '" + Fct.sdFat(tcabk_oe.Rows[0]["fadj03"]) + "', fadj04 = '" + Fct.sdFat(tcabk_oe.Rows[0]["fadj04"]) + "',fadj05 = '" + Fct.sdFat(tcabk_oe.Rows[0]["fadj05"]) + "', fadj06 = '" + Fct.sdFat(tcabk_oe.Rows[0]["fadj06"]) + "', " +
                   "fadj07 = '" + Fct.sdFat(tcabk_oe.Rows[0]["fadj07"]) + "', fadj08 = '" + Fct.sdFat(tcabk_oe.Rows[0]["fadj08"]) + "',fadj09 = '" + Fct.sdFat(tcabk_oe.Rows[0]["fadj09"]) + "', fadj10 = '" + Fct.sdFat(tcabk_oe.Rows[0]["fadj10"]) + "', " +
                   "fadjA = '" + Fct.sdFat(tcabk_oe.Rows[0]["fadjA"]) + "', fadjB = '" + Fct.sdFat(tcabk_oe.Rows[0]["fadjB"]) + "',fadjC = '" + Fct.sdFat(tcabk_oe.Rows[0]["fadjC"]) + "', fadj = '" + Fct.sdFat(tcabk_oe.Rows[0]["fadj"]) + "', " +
                   "fos01 = '" + Fct.sdFat(tcabk_oe.Rows[0]["fos01"]) + "', fos02 = '" + Fct.sdFat(tcabk_oe.Rows[0]["fos02"]) + "', " +
                   "fos03 = '" + Fct.sdFat(tcabk_oe.Rows[0]["fos03"]) + "', fos04 = '" + Fct.sdFat(tcabk_oe.Rows[0]["fos04"]) + "',fos05 = '" + Fct.sdFat(tcabk_oe.Rows[0]["fos05"]) + "', fos06 = '" + Fct.sdFat(tcabk_oe.Rows[0]["fos06"]) + "', " +
                   "fos07 = '" + Fct.sdFat(tcabk_oe.Rows[0]["fos07"]) + "', fos08 = '" + Fct.sdFat(tcabk_oe.Rows[0]["fos08"]) + "',fos09 ='" + Fct.sdFat(tcabk_oe.Rows[0]["fos09"]) + "', fos10 ='" + Fct.sdFat(tcabk_oe.Rows[0]["fos10"]) + "', " +
                   "fosA = '" + Fct.sdFat(tcabk_oe.Rows[0]["fosA"]) + "', fosB = '" + Fct.sdFat(tcabk_oe.Rows[0]["fosB"]) + "',fosC = '" + Fct.sdFat(tcabk_oe.Rows[0]["fosC"]) + "', fos = '" + Fct.sdFat(tcabk_oe.Rows[0]["fos"]) + "' where fctlid_c=(select fctlid from oclaim where fclmno ='" + fclmno + "') ";
            Addsql.Add(sqlocabk_oe);

            string ocabk_csql = "select a.*, b.fzeropay from ocabk_c a "+
            "left join ocabk_oc b on a.fctlid_c= b.fctlid_c where a.fctlid = (select max(fctlid) from ocabk_c where fctlid_c =(select fctlid from oclaim where fclmno= '" + fclmno + "'))";
            DataTable ocabk_cdt = DBHelper.GetDataSet(ocabk_csql);
            foreach (DataRow dr in ocabk_cdt.Rows)
            {
                ln_flogseq = Fct.sdFat(dr["flogseq"]) + 1;
                dr["fctlid"] = Fct.NewId("ocabk_c");
                dr["fctlid_a"] = Fct.NewId("ocadj");
                dr["flogseq"] = ln_flogseq;
                dr["ftrntype"] = "3";
                dr["fos01"] = ln_fos01_oc;
                dr["fos02"] = ln_fos02_oc;
                dr["fos03"] = ln_fos03_oc;
                dr["fos04"] = ln_fos04_oc;
                dr["fos05"] = ln_fos05_oc;
                dr["fos06"] = ln_fos06_oc;
                dr["fos07"] = ln_fos07_oc;
                dr["fos08"] = ln_fos08_oc;
                dr["fos09"] = ln_fos09_oc;
                dr["fos10"] = ln_fos10_oc;
                dr["fos11"] = ln_fos11_oc;
                dr["fos12"] = ln_fos12_oc;
                dr["fos13"] = ln_fos13_oc;
                dr["fosA"] = ln_fosA_oc;
                dr["fosA2"] = ln_fosA2_oc;
                dr["fosB"] = ln_fosB_oc;
                dr["fosC"] = ln_fosC_oc;
                dr["fos"] = ln_fos_oc;
                if (Fct.stFat(dr["fzeropay"]) == "1")
                {
                    dr["fadj01"] = ln_adj01_oc;
                    dr["fadj02"] = ln_adj02_oc;
                    dr["fadj03"] = ln_adj03_oc;
                    dr["fadj04"] = ln_adj04_oc;
                    dr["fadj05"] = ln_adj05_oc;
                    dr["fadj06"] = ln_adj06_oc;
                    dr["fadj07"] = ln_adj07_oc;
                    dr["fadj10"] = ln_adj10_oc;
                }
                dr["fadj08"] = ln_adj08_oc;
                dr["fadj09"] = ln_adj09_oc;
                dr["fadj11"] = ln_adj11_oc;
                dr["fadj12"] = ln_adj12_oc;
                dr["fadj13"] = ln_adj13_oc;
                dr["fadjA"] = ln_adjA_oc;
                dr["fadjA2"] = ln_adj08_oc;
                dr["fadjB"] = ln_adj09_oc;
                dr["fadjC"] = ln_adj11_oc + ln_adj12_oc + ln_adj13_oc;
                dr["fadj"] = Fct.sdFat(dr["fadjA"]) + Fct.sdFat(dr["fadjA2"]) + Fct.sdFat(dr["fadjB"]) + Fct.sdFat(dr["fadjC"]);

                dr["frvos01"] = Fct.sdFat(dr["fos01"]) + Fct.sdFat(dr["fadj01"]);
                dr["frvos02"] = Fct.sdFat(dr["fos02"]) + Fct.sdFat(dr["fadj02"]);
                dr["frvos03"] = Fct.sdFat(dr["fos03"]) + Fct.sdFat(dr["fadj03"]);
                dr["frvos04"] = Fct.sdFat(dr["fos04"]) + Fct.sdFat(dr["fadj04"]);
                dr["frvos05"] = Fct.sdFat(dr["fos05"]) + Fct.sdFat(dr["fadj05"]);
                dr["frvos06"] = Fct.sdFat(dr["fos06"]) + Fct.sdFat(dr["fadj06"]);
                dr["frvos07"] = Fct.sdFat(dr["fos07"]) + Fct.sdFat(dr["fadj07"]);
                dr["frvos08"] = Fct.sdFat(dr["fos08"]) + Fct.sdFat(dr["fadj08"]);
                dr["frvos09"] = Fct.sdFat(dr["fos09"]) + Fct.sdFat(dr["fadj09"]);
                dr["frvos10"] = Fct.sdFat(dr["fos10"]) + Fct.sdFat(dr["fadj10"]);
                dr["frvos11"] = Fct.sdFat(dr["fos11"]) + Fct.sdFat(dr["fadj11"]);
                dr["frvos12"] = Fct.sdFat(dr["fos12"]) + Fct.sdFat(dr["fadj12"]);
                dr["frvos13"] = Fct.sdFat(dr["fos13"]) + Fct.sdFat(dr["fadj13"]);
                dr["frvosA"] = Fct.sdFat(dr["fosA"]) + Fct.sdFat(dr["fadjA"]);
                dr["frvosA2"] = Fct.sdFat(dr["fosA2"]) + Fct.sdFat(dr["fadjA2"]);
                dr["frvosB"] = Fct.sdFat(dr["fosB"]) + Fct.sdFat(dr["fadjB"]);
                dr["frvosC"] = Fct.sdFat(dr["fosC"]) + Fct.sdFat(dr["fadjC"]);
                dr["frvos"] = Fct.sdFat(dr["fos"]) + Fct.sdFat(dr["fadj"]);
            }

            if (ocabk_cdt.Rows.Count > 0) {
                string sqlocabk_c = "INSERT INTO [dbo].[ocabk_c]([fctlid],[fctlid_c],[fctlid_a],[ftrntype],[flogseq],[fos01],[frvos01],[fadj01],[fos02],[frvos02],[fadj02], " +
                                    "[fos03],[frvos03],[fadj03],[fos04],[frvos04],[fadj04],[fos05],[frvos05],[fadj05],[fos06],[frvos06],[fadj06],[fos07],[frvos07],[fadj07],[fos08],[frvos08], " +
                                    "[fadj08],[fos09],[frvos09],[fadj09],[fos10],[frvos10],[fadj10],[fos11],[frvos11],[fadj11],[fos12],[frvos12],[fadj12],[fos13],[frvos13],[fadj13],[fosA], " +
                                    "[frvosA],[fadjA],[fosA2],[frvosA2],[fadjA2],[fosB],[frvosB],[fadjB],[fosC],[frvosC],[fadjC],[fos],[frvos],[fadj],[fremark_t1]) " +
                                    "VALUES('" + Fct.stFat(ocabk_cdt.Rows[0]["fctlid"]) + "','" + Fct.stFat(ocabk_cdt.Rows[0]["fctlid_c"]) + "','" + Fct.stFat(ocabk_cdt.Rows[0]["fctlid_a"]) + "','3', " +
                                    "'" + Fct.sdFat(ocabk_cdt.Rows[0]["flogseq"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["fos01"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["frvos01"]) + "', " +
                                    "'" + Fct.sdFat(ocabk_cdt.Rows[0]["fadj01"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["fos02"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["frvos02"]) + "', " +
                                    "'" + Fct.sdFat(ocabk_cdt.Rows[0]["fadj02"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["fos03"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["frvos03"]) + "', " +
                                    "'" + Fct.sdFat(ocabk_cdt.Rows[0]["fadj03"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["fos04"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["frvos04"]) + "', " +
                                    "'" + Fct.sdFat(ocabk_cdt.Rows[0]["fadj04"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["fos05"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["frvos05"]) + "', " +
                                    "'" + Fct.sdFat(ocabk_cdt.Rows[0]["fadj05"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["fos06"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["frvos06"]) + "', " +
                                    "'" + Fct.sdFat(ocabk_cdt.Rows[0]["fadj06"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["fos07"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["frvos07"]) + "', " +
                                    "'" + Fct.sdFat(ocabk_cdt.Rows[0]["fadj07"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["fos08"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["frvos08"]) + "', " +
                                    "'" + Fct.sdFat(ocabk_cdt.Rows[0]["fadj08"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["fos09"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["frvos09"]) + "', " +
                                    "'" + Fct.sdFat(ocabk_cdt.Rows[0]["fadj09"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["fos10"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["frvos10"]) + "', " +
                                    "'" + Fct.sdFat(ocabk_cdt.Rows[0]["fadj10"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["fos11"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["frvos11"]) + "', " +
                                    "'" + Fct.sdFat(ocabk_cdt.Rows[0]["fadj11"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["fos12"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["frvos12"]) + "', " +
                                    "'" + Fct.sdFat(ocabk_cdt.Rows[0]["fadj12"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["fos13"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["frvos13"]) + "', " +
                                    "'" + Fct.sdFat(ocabk_cdt.Rows[0]["fadj13"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["fosA"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["frvosA"]) + "', " +
                                    "'" + Fct.sdFat(ocabk_cdt.Rows[0]["fadjA"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["fos08"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["frvos08"]) + "', " +
                                    "'" + Fct.sdFat(ocabk_cdt.Rows[0]["fadj08"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["fos09"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["frvos09"]) + "', " +
                                    "'" + Fct.sdFat(ocabk_cdt.Rows[0]["fadj09"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["fosC"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["frvosC"]) + "', " +
                                    "'" + Fct.sdFat(ocabk_cdt.Rows[0]["fadjC"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["fos"]) + "','" + Fct.sdFat(ocabk_cdt.Rows[0]["frvos"]) + "', " +
                                    "'" + Fct.sdFat(ocabk_cdt.Rows[0]["fadj"]) + "','')";
                Addsql.Add(sqlocabk_c);
            }
            
            string sqlfctlid4 = "update " + Dbm + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='OCABK_C'";
            Addsql.Add(sqlfctlid4);

            string str6 = "select * from ocabk_oc where fctlid_c=(select fctlid from oclaim where fclmno ='" + fclmno + "')";
            DataTable tcabk_oc = DBHelper.GetDataSet(str6);
            foreach (DataRow dr in tcabk_oc.Rows)
            {
                if (Fct.stFat(dr["fzeropay"]) == "1")
                {
                    dr["fadj01"] = Fct.sdFat(dr["fadj01"]) + ln_adj01_oc;
                    dr["fadj02"] = Fct.sdFat(dr["fadj02"]) + ln_adj02_oc;
                    dr["fadj03"] = Fct.sdFat(dr["fadj03"]) + ln_adj03_oc;
                    dr["fadj04"] = Fct.sdFat(dr["fadj04"]) + ln_adj04_oc;
                    dr["fadj05"] = Fct.sdFat(dr["fadj05"]) + ln_adj05_oc;
                    dr["fadj06"] = Fct.sdFat(dr["fadj06"]) + ln_adj06_oc;
                    dr["fadj07"] = Fct.sdFat(dr["fadj07"]) + ln_adj07_oc;
                    dr["fadj10"] = Fct.sdFat(dr["fadj10"]) + ln_adj10_oc;
                }
                dr["fadj08"] = Fct.sdFat(dr["fadj08"]) + ln_adj08_oc;
                dr["fadj09"] = Fct.sdFat(dr["fadj09"]) + ln_adj09_oc;
                dr["fadj11"] = Fct.sdFat(dr["fadj11"]) + ln_adj11_oc;
                dr["fadj12"] = Fct.sdFat(dr["fadj12"]) + ln_adj12_oc;
                dr["fadj13"] = Fct.sdFat(dr["fadj13"]) + ln_adj13_oc;
                dr["fadjA"] = Fct.sdFat(dr["fadjA"]) + ln_adjA_oc;
                dr["fadjA2"] = Fct.sdFat(dr["fadj08"]);
                dr["fadjB"] = Fct.sdFat(dr["fadj09"]);
                dr["fadjC"] = Fct.sdFat(dr["fadj11"]) + Fct.sdFat(dr["fadj12"]) + Fct.sdFat(dr["fadj13"]);
                dr["fadj"] = Fct.sdFat(dr["fadjA"]) + Fct.sdFat(dr["fadjA2"]) + Fct.sdFat(dr["fadjB"]) + Fct.sdFat(dr["fadjC"]);

                if (Fct.stFat(dr["fzeropay"]) == "1")
                {
                    dr["fos01"] = Fct.sdFat(dr["fos01"]) + ln_adj01_oc;
                    dr["fos02"] = Fct.sdFat(dr["fos02"]) + ln_adj02_oc;
                    dr["fos03"] = Fct.sdFat(dr["fos03"]) + ln_adj03_oc;
                    dr["fos04"] = Fct.sdFat(dr["fos04"]) + ln_adj04_oc;
                    dr["fos05"] = Fct.sdFat(dr["fos05"]) + ln_adj05_oc;
                    dr["fos06"] = Fct.sdFat(dr["fos06"]) + ln_adj06_oc;
                    dr["fos07"] = Fct.sdFat(dr["fos07"]) + ln_adj07_oc;
                    dr["fos10"] = Fct.sdFat(dr["fos10"]) + ln_adj10_oc;
                }
                dr["fos08"] = Fct.sdFat(dr["fos08"]) + ln_adj08_oc;
                dr["fos09"] = Fct.sdFat(dr["fos09"]) + ln_adj09_oc;
                dr["fos11"] = Fct.sdFat(dr["fos11"]) + ln_adj11_oc;
                dr["fos12"] = Fct.sdFat(dr["fos12"]) + ln_adj12_oc;
                dr["fos13"] = Fct.sdFat(dr["fos13"]) + ln_adj13_oc;
                dr["fosA"] = Fct.sdFat(dr["fosA"]) + ln_adjA_oc;
                dr["fosA2"] = Fct.sdFat(dr["fosA2"]) + ln_adj08_oc;
                dr["fosB"] = Fct.sdFat(dr["fosB"]) + ln_adj09_oc;
                dr["fosC"] = Fct.sdFat(dr["fosC"]) + ln_adj11_oc + ln_adj12_oc + ln_adj13_oc;
                dr["fos"] = Fct.sdFat(dr["fos"]) + ln_adjA_oc + ln_adj08_oc + ln_adj09_oc + ln_adj11_oc + ln_adj12_oc + ln_adj13_oc;
            }
            string sqlocabk_oc = "update ocabk_oc set fadj01 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fadj01"]) + "', fadj02 ='" + Fct.sdFat(tcabk_oc.Rows[0]["fadj02"]) + "', " +
                    "fadj03 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fadj03"]) + "', fadj04 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fadj04"]) + "',fadj05 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fadj05"]) + "', fadj06 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fadj06"]) + "', " +
                    "fadj07 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fadj07"]) + "', fadj08 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fadj08"]) + "',fadj09 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fadj09"]) + "', fadj10 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fadj10"]) + "', " +
                   "fadjA = '" + Fct.sdFat(tcabk_oc.Rows[0]["fadjA"]) + "',fadjA2 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fadjA2"]) + "', fadjB = '" + Fct.sdFat(tcabk_oc.Rows[0]["fadjB"]) + "',fadjC = '" + Fct.sdFat(tcabk_oc.Rows[0]["fadjC"]) + "', fadj = '" + Fct.sdFat(tcabk_oc.Rows[0]["fadj"]) + "', " +
                   "fadj11 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fadj11"]) + "',fadj12 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fadj12"]) + "', fadj13 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fadj13"]) + "',fos01 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fos01"]) + "', fos02 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fos02"]) + "', " +
                   "fos03 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fos03"]) + "', fos04 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fos04"]) + "',fos05 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fos05"]) + "', fos06 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fos06"]) + "', " +
                   "fos07 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fos07"]) + "', fos08 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fos08"]) + "',fos09 ='" + Fct.sdFat(tcabk_oc.Rows[0]["fos09"]) + "', fos10 ='" + Fct.sdFat(tcabk_oc.Rows[0]["fos10"]) + "', " +
                   "fos11 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fos11"]) + "',fos12 ='" + Fct.sdFat(tcabk_oc.Rows[0]["fos12"]) + "', fos13 ='" + Fct.sdFat(tcabk_oc.Rows[0]["fos13"]) + "',  " +
                   "fosA = '" + Fct.sdFat(tcabk_oc.Rows[0]["fosA"]) + "', fosA2 = '" + Fct.sdFat(tcabk_oc.Rows[0]["fosA2"]) + "', fosB = '" + Fct.sdFat(tcabk_oc.Rows[0]["fosB"]) + "',fosC = '" + Fct.sdFat(tcabk_oc.Rows[0]["fosC"]) + "', fos = '" + Fct.sdFat(tcabk_oc.Rows[0]["fos"]) + "' where fctlid_c=(select fctlid from oclaim where fclmno ='" + fclmno + "') ";
            Addsql.Add(sqlocabk_oc);

            string sqlclaim = "update oclaim set fosamt = fosamt + " + ln_famount + ", fincamt = fincamt + " + ln_famount + ", " +
                   "fosretn = fosretn + " + ln_fretn + ", fincretn = fincretn + " + ln_fretn + ",fostty = fostty + " + ln_ftty + ", finctty = finctty + " + ln_ftty + ", " +
                   "fosfacb = fosfacb + " + ln_ffacb + ", fincfacb = fincfacb + " + ln_ffacb + ",fosfac = fosfac + " + ln_ffac + ", fincfac = fincfac +" + ln_ffac + ", " +
                   "fosfacnp = fosfacnp + " + ln_ffacnp + ", fincfacnp = fincfacnp - " + ln_ffacnp + ",fosxol = fosxol + " + ln_fxol + ", fincxol = fincxol + " + ln_fxol + " where fctlid= (select fctlid from oclaim where fclmno ='" + fclmno + "') ";
            Addsql.Add(sqlclaim);
            string sqlclaims = "update oclaim_s set fosamt = fosamt + " + ln_famount + ", fincamt = fincamt + " + ln_famount + ", " +
                "fosretn = fosretn + " + ln_fretn + ", fincretn = fincretn + " + ln_fretn + ",fostty = fostty + " + ln_ftty + ", finctty = finctty + " + ln_ftty + ", " +
                "fosfacb = fosfacb + " + ln_ffacb + ", fincfacb = fincfacb + " + ln_ffacb + ",fosfac = fosfac + " + ln_ffac + ", fincfac = fincfac +" + ln_ffac + ", " +
                "fosfacnp = fosfacnp + " + ln_ffacnp + ", fincfacnp = fincfacnp - " + ln_ffacnp + ",fosxol = fosxol + " + ln_fxol + ", fincxol = fincxol + " + ln_fxol + " where fctlid_1= (select fctlid from oclaim where fclmno ='" + fclmno + "') ";
            Addsql.Add(sqlclaims);

            return (string[])Addsql.ToArray(typeof(string));
            
        }
    }
}
