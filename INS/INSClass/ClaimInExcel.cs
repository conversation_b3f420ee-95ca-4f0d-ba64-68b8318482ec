using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;

using Excel = Microsoft.Office.Interop.Excel;

namespace INS.INSClass
{
    partial class ClaimInExcel
    {
        public static Int16 WsColTitleClmPay(Excel.Worksheet xlWorkSheet,DataTable dt,
            string optid, string z_company, string z_title, string z_currency, string z_criterion,
            string z_greq)
        {
            xlWorkSheet.Cells.Font.Name = "arial";
            xlWorkSheet.Cells.Font.Size = 8;

            xlWorkSheet.Columns["A"].ColumnWidth = 9;
            xlWorkSheet.Columns["B"].ColumnWidth = 10;
            xlWorkSheet.Columns["D"].ColumnWidth = 10;
            xlWorkSheet.Columns["E:G"].ColumnWidth = 9;
            xlWorkSheet.Columns["H:I"].ColumnWidth = 33;
            xlWorkSheet.Columns["J:O"].ColumnWidth = 11;

            xlWorkSheet.Columns["A"].HorizontalAlignment = 2;
            xlWorkSheet.Columns["E:G"].HorizontalAlignment = 2;

            xlWorkSheet.Columns["A"].NumberFormat = "yyyy/mm/dd";
            xlWorkSheet.Columns["E:G"].NumberFormat = "yyyy/mm/dd";
            xlWorkSheet.Columns["J:O"].NumberFormat = "#,###,###.00";

            xlWorkSheet.get_Range("N3", "N3").HorizontalAlignment = 4;
            xlWorkSheet.get_Range("O3", "O3").HorizontalAlignment = 2;
            xlWorkSheet.get_Range("O3", "O3").NumberFormat = "yyyy/mm/dd";

            xlWorkSheet.get_Range("A8", "O8").Font.Underline = 2;

            Int16 i = 1;

            xlWorkSheet.Cells[i++, 1] = z_company;
            xlWorkSheet.Cells[i++, 1] = z_title;
            xlWorkSheet.Cells[i, 1] = z_currency;
            xlWorkSheet.Cells[i, 14] = "Date: ";
            xlWorkSheet.Cells[i++, 15] = DateTime.Now;
            xlWorkSheet.Cells[i++, 1] = z_criterion;
            xlWorkSheet.Cells[i, 1] = z_greq;

            i += 3;

            if (dt.Rows.Count == 0)
            {
                xlWorkSheet.Cells[i, 1] = "No record found!";
                xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                return i;
            }

            xlWorkSheet.Cells[i, 1] = "Bill Date";
            xlWorkSheet.Cells[i, 2] = "C/N No";
            xlWorkSheet.Cells[i, 3] = "Claim No";
            xlWorkSheet.Cells[i, 4] = "Policy No";
            xlWorkSheet.Cells[i, 5] = "Reg Date";
            xlWorkSheet.Cells[i, 6] = "Report Date";
            xlWorkSheet.Cells[i, 7] = "Date of Loss";
            xlWorkSheet.Cells[i, 8] = "Insured";
            xlWorkSheet.Cells[i, 9] = "Loss Location / Particulars";
            xlWorkSheet.Cells[i, 10] = "Total";
            xlWorkSheet.Cells[i, 11] = "Retention";
            xlWorkSheet.Cells[i, 12] = "Surplus";
            xlWorkSheet.Cells[i, 13] = "Facultative";
            xlWorkSheet.Cells[i, 14] = "Fac Oblig";
            xlWorkSheet.Cells[i, 15] = "Excess of Loss";

            i = WsClmPayDetail(xlWorkSheet, dt, optid, i += 2);

            return i;
        }

        public static Int16 WsClmPayDetail(Excel.Worksheet xlWorkSheet, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fclsseq = "";

            decimal ln_samount = 0, ln_sretn = 0, ln_stty = 0, ln_sfac = 0, ln_sfacb = 0, ln_sxol = 0;
            decimal ln_tamount = 0, ln_tretn = 0, ln_ttty = 0, ln_tfac = 0, ln_tfacb = 0, ln_txol = 0;

            decimal ln_scase = 0, ln_tcase = 0;

            foreach (DataRow dr in dt.Rows)
            {
                if (lc_fclsseq != dr["fclsseq"].ToString().Trim())
                {
                    if (iRow > startRow)
                    {
                        xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");
                        
                        k = 9;
                        xlWorkSheet.Cells[iRow, k++] = "Total:";
                        xlWorkSheet.Cells[iRow, k++] = ln_samount;
                        xlWorkSheet.Cells[iRow, k++] = ln_sretn;
                        xlWorkSheet.Cells[iRow, k++] = ln_stty;
                        xlWorkSheet.Cells[iRow, k++] = ln_sfac;
                        xlWorkSheet.Cells[iRow, k++] = ln_sfacb;
                        xlWorkSheet.Cells[iRow, k++] = ln_sxol;

                        xlWorkSheet.get_Range(String.Format("I{0}", iRow)).Columns.HorizontalAlignment = 4;

                        iRow += 2;
                    }

                    lc_fclsseq = dr["fclsseq"].ToString().Trim();
                    xlWorkSheet.Cells[iRow, 1] = dr["fclsname"].ToString();

                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                    ln_samount = 0; ln_sretn = 0; ln_stty = 0; ln_sfac = 0; ln_sfacb = 0; ln_sxol = 0;
                    ln_scase = 0;

                    iRow += 2;
                }

                decimal famount = Convert.ToDecimal(dr["famount"]);
                decimal fretn = Convert.ToDecimal(dr["fretn"]);
                decimal ftty = Convert.ToDecimal(dr["ftty"]);
                decimal ffac = Convert.ToDecimal(dr["ffac"]) + Convert.ToDecimal(dr["ffacnp"]);
                decimal ffacb = Convert.ToDecimal(dr["ffacb"]);
                decimal fxol = Convert.ToDecimal(dr["fxol"]);

                k = 1;

                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["finvdate"]);
                xlWorkSheet.Cells[iRow, k++] = dr["finvno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fclmno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fpolno"];
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fregdate"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["frepdate"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["flosdate"]);
                xlWorkSheet.Cells[iRow, k++] = dr["finsd"] + ((char)13).ToString() + ((char)10).ToString();
                xlWorkSheet.Cells[iRow, k++] = dr["flosloc"].ToString().Trim() + ((char)13).ToString() +
                    ((char)10).ToString() + dr["fpartr_t1"].ToString().TrimEnd() + 
                    dr["fpartr_t2"].ToString().TrimEnd() + ((char)10).ToString();

                xlWorkSheet.get_Range(String.Format("H{0}", iRow), String.Format("H{0}", iRow + 2)).Merge();
                xlWorkSheet.get_Range(String.Format("I{0}", iRow), String.Format("I{0}", iRow + 2)).Merge();

                xlWorkSheet.get_Range(String.Format("H{0}", iRow), String.Format("I{0}", iRow + 2)).HorizontalAlignment = 1;
                xlWorkSheet.get_Range(String.Format("H{0}", iRow), String.Format("I{0}", iRow + 2)).VerticalAlignment = 1;
                xlWorkSheet.get_Range(String.Format("H{0}", iRow), String.Format("I{0}", iRow)).RowHeight = 11.25;

                xlWorkSheet.Cells[iRow, k++] = famount;
                xlWorkSheet.Cells[iRow, k++] = fretn;
                xlWorkSheet.Cells[iRow, k++] = ftty;
                xlWorkSheet.Cells[iRow, k++] = ffac;
                xlWorkSheet.Cells[iRow, k++] = ffacb;
                xlWorkSheet.Cells[iRow, k++] = fxol;

                ln_samount = ln_samount + famount;
                ln_sretn = ln_sretn + fretn;
                ln_stty = ln_stty + ftty;
                ln_sfac = ln_sfac + ffac;
                ln_sfacb = ln_sfacb + ffacb;
                ln_sxol = ln_sxol + fxol;

                ln_tamount = ln_tamount + famount;
                ln_tretn = ln_tretn + fretn;
                ln_ttty = ln_ttty + ftty;
                ln_tfac = ln_tfac + ffac;
                ln_tfacb = ln_tfacb + ffacb;
                ln_txol = ln_txol + fxol;

                ln_scase++; ln_tcase++;

                iRow+=4;
            }

            xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");

            k = 9;
            xlWorkSheet.Cells[iRow, k++] = "Total:";
            xlWorkSheet.Cells[iRow, k++] = ln_samount;
            xlWorkSheet.Cells[iRow, k++] = ln_sretn;
            xlWorkSheet.Cells[iRow, k++] = ln_stty;
            xlWorkSheet.Cells[iRow, k++] = ln_sfac;
            xlWorkSheet.Cells[iRow, k++] = ln_sfacb;
            xlWorkSheet.Cells[iRow, k++] = ln_sxol;

            xlWorkSheet.get_Range(String.Format("I{0}", iRow)).Columns.HorizontalAlignment = 4;

            iRow += 2;

            xlWorkSheet.Cells[iRow, 1] = "Total of Cases: " + ln_tcase.ToString("N0");

            k = 9;
            xlWorkSheet.Cells[iRow, k++] = "Grand Total:";
            xlWorkSheet.Cells[iRow, k++] = ln_tamount;
            xlWorkSheet.Cells[iRow, k++] = ln_tretn;
            xlWorkSheet.Cells[iRow, k++] = ln_ttty;
            xlWorkSheet.Cells[iRow, k++] = ln_tfac;
            xlWorkSheet.Cells[iRow, k++] = ln_tfacb;
            xlWorkSheet.Cells[iRow, k++] = ln_txol;

            xlWorkSheet.get_Range(String.Format("I{0}", iRow)).Columns.HorizontalAlignment = 4;

            return iRow;
        }

        public static Int16 WsColTitleNewClm(Excel.Worksheet xlWorkSheet, DataTable dt,
            string optid, string z_company, string z_title, string z_currency, string z_criterion)
        {
            xlWorkSheet.Cells.Font.Name = "arial";
            xlWorkSheet.Cells.Font.Size = 8;

            xlWorkSheet.Columns["A:B"].ColumnWidth = 9;
            xlWorkSheet.Columns["C:D"].ColumnWidth = 10;
            xlWorkSheet.Columns["E"].ColumnWidth = 7;
            xlWorkSheet.Columns["F"].ColumnWidth = 9;
            xlWorkSheet.Columns["G:H"].ColumnWidth = 33;
            xlWorkSheet.Columns["I:N"].ColumnWidth = 11;

            xlWorkSheet.Columns["A:B"].HorizontalAlignment = 2;
            xlWorkSheet.Columns["F"].HorizontalAlignment = 2;

            xlWorkSheet.Columns["A:B"].NumberFormat = "yyyy/mm/dd";
            xlWorkSheet.Columns["F"].NumberFormat = "yyyy/mm/dd";
            xlWorkSheet.Columns["I:N"].NumberFormat = "#,###,###.00";

            xlWorkSheet.get_Range("M3", "M3").HorizontalAlignment = 4;
            xlWorkSheet.get_Range("N3", "N3").HorizontalAlignment = 2;
            xlWorkSheet.get_Range("N3", "N3").NumberFormat = "yyyy/mm/dd";

            xlWorkSheet.get_Range("A6", "N6").Font.Underline = 2;

            Int16 i = 1, k = 1;

            xlWorkSheet.Cells[i++, 1] = z_company;
            xlWorkSheet.Cells[i++, 1] = z_title;
            xlWorkSheet.Cells[i, 1] = z_currency;
            xlWorkSheet.Cells[i, 13] = "Date: ";
            xlWorkSheet.Cells[i++, 14] = DateTime.Now;
            xlWorkSheet.Cells[i, 1] = z_criterion;

            i += 2;

            if (dt.Rows.Count == 0)
            {
                xlWorkSheet.Cells[i, 1] = "No record found!";
                xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                return i;
            }

            xlWorkSheet.Cells[i, k++] = "Reg Date";
            xlWorkSheet.Cells[i, k++] = "Report Date";
            xlWorkSheet.Cells[i, k++] = "Claim No";
            xlWorkSheet.Cells[i, k++] = "Policy No";
            xlWorkSheet.Cells[i, k++] = "Producer";
            xlWorkSheet.Cells[i, k++] = "Date of Loss";
            xlWorkSheet.Cells[i, k++] = "Insured";
            xlWorkSheet.Cells[i, k++] = "Loss Location / Particulars";
            xlWorkSheet.Cells[i, k++] = "Reserve";
            xlWorkSheet.Cells[i, k++] = "Retention";
            xlWorkSheet.Cells[i, k++] = "Surplus";
            xlWorkSheet.Cells[i, k++] = "Facultative";
            xlWorkSheet.Cells[i, k++] = "Fac Oblig";
            xlWorkSheet.Cells[i, k++] = "Excess of Loss";

            i = WsClmNewClm(xlWorkSheet, dt, optid, i += 2);

            return i;
        }

        public static Int16 WsClmNewClm(Excel.Worksheet xlWorkSheet, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fclsseq = "";

            decimal ln_samount = 0, ln_sretn = 0, ln_stty = 0, ln_sfac = 0, ln_sfacb = 0, ln_sxol = 0;
            decimal ln_tamount = 0, ln_tretn = 0, ln_ttty = 0, ln_tfac = 0, ln_tfacb = 0, ln_txol = 0;

            decimal ln_scase = 0, ln_tcase = 0;

            foreach (DataRow dr in dt.Rows)
            {
                if (lc_fclsseq != dr["fclsseq"].ToString().Trim())
                {
                    if (iRow > startRow)
                    {
                        xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");

                        k = 8;
                        xlWorkSheet.Cells[iRow, k++] = "Total:";
                        xlWorkSheet.Cells[iRow, k++] = ln_samount;
                        xlWorkSheet.Cells[iRow, k++] = ln_sretn;
                        xlWorkSheet.Cells[iRow, k++] = ln_stty;
                        xlWorkSheet.Cells[iRow, k++] = ln_sfac;
                        xlWorkSheet.Cells[iRow, k++] = ln_sfacb;
                        xlWorkSheet.Cells[iRow, k++] = ln_sxol;

                        xlWorkSheet.get_Range(String.Format("H{0}", iRow)).Columns.HorizontalAlignment = 4;

                        iRow += 2;
                    }

                    lc_fclsseq = dr["fclsseq"].ToString().Trim();
                    xlWorkSheet.Cells[iRow, 1] = dr["fclsname"].ToString();

                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                    ln_samount = 0; ln_sretn = 0; ln_stty = 0; ln_sfac = 0; ln_sfacb = 0; ln_sxol = 0;
                    ln_scase = 0;

                    iRow += 2;
                }

                decimal famount = Convert.ToDecimal(dr["mres"]);
                decimal fretn = Convert.ToDecimal(dr["mretnres"]);
                decimal ftty = Convert.ToDecimal(dr["mttyres"]);
                decimal ffac = Convert.ToDecimal(dr["mfacres"]) + Convert.ToDecimal(dr["mfacnpres"]);
                decimal ffacb = Convert.ToDecimal(dr["mfacbres"]);
                decimal fxol = Convert.ToDecimal(dr["mxolres"]);

                k = 1;

                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fregdate"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["frepdate"]);
                xlWorkSheet.Cells[iRow, k++] = dr["fclmno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fpolno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fprdr"];
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["flosdate"]);
                xlWorkSheet.Cells[iRow, k++] = dr["finsd"] + ((char)13).ToString() + ((char)10).ToString();
                xlWorkSheet.Cells[iRow, k++] = dr["flosloc"].ToString().Trim() + ((char)13).ToString() +
                    ((char)10).ToString() + dr["fpartr_t1"].ToString().TrimEnd() +
                    dr["fpartr_t2"].ToString().TrimEnd() + ((char)10).ToString();

                xlWorkSheet.get_Range(String.Format("G{0}", iRow), String.Format("G{0}", iRow + 2)).Merge();
                xlWorkSheet.get_Range(String.Format("H{0}", iRow), String.Format("H{0}", iRow + 2)).Merge();

                xlWorkSheet.get_Range(String.Format("G{0}", iRow), String.Format("H{0}", iRow + 2)).HorizontalAlignment = 1;
                xlWorkSheet.get_Range(String.Format("G{0}", iRow), String.Format("H{0}", iRow + 2)).VerticalAlignment = 1;
                xlWorkSheet.get_Range(String.Format("G{0}", iRow), String.Format("H{0}", iRow)).RowHeight = 11.25;

                xlWorkSheet.Cells[iRow, k++] = famount;
                xlWorkSheet.Cells[iRow, k++] = fretn;
                xlWorkSheet.Cells[iRow, k++] = ftty;
                xlWorkSheet.Cells[iRow, k++] = ffac;
                xlWorkSheet.Cells[iRow, k++] = ffacb;
                xlWorkSheet.Cells[iRow, k++] = fxol;

                ln_samount = ln_samount + famount;
                ln_sretn = ln_sretn + fretn;
                ln_stty = ln_stty + ftty;
                ln_sfac = ln_sfac + ffac;
                ln_sfacb = ln_sfacb + ffacb;
                ln_sxol = ln_sxol + fxol;

                ln_tamount = ln_tamount + famount;
                ln_tretn = ln_tretn + fretn;
                ln_ttty = ln_ttty + ftty;
                ln_tfac = ln_tfac + ffac;
                ln_tfacb = ln_tfacb + ffacb;
                ln_txol = ln_txol + fxol;

                ln_scase++; ln_tcase++;

                iRow += 4;
            }

            xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");

            k = 8;
            xlWorkSheet.Cells[iRow, k++] = "Total:";
            xlWorkSheet.Cells[iRow, k++] = ln_samount;
            xlWorkSheet.Cells[iRow, k++] = ln_sretn;
            xlWorkSheet.Cells[iRow, k++] = ln_stty;
            xlWorkSheet.Cells[iRow, k++] = ln_sfac;
            xlWorkSheet.Cells[iRow, k++] = ln_sfacb;
            xlWorkSheet.Cells[iRow, k++] = ln_sxol;

            xlWorkSheet.get_Range(String.Format("H{0}", iRow)).Columns.HorizontalAlignment = 4;

            iRow += 2;

            xlWorkSheet.Cells[iRow, 1] = "Total of Cases: " + ln_tcase.ToString("N0");

            k = 8;
            xlWorkSheet.Cells[iRow, k++] = "Grand Total:";
            xlWorkSheet.Cells[iRow, k++] = ln_tamount;
            xlWorkSheet.Cells[iRow, k++] = ln_tretn;
            xlWorkSheet.Cells[iRow, k++] = ln_ttty;
            xlWorkSheet.Cells[iRow, k++] = ln_tfac;
            xlWorkSheet.Cells[iRow, k++] = ln_tfacb;
            xlWorkSheet.Cells[iRow, k++] = ln_txol;

            xlWorkSheet.get_Range(String.Format("H{0}", iRow)).Columns.HorizontalAlignment = 4;

            return iRow;
        }

        public static Int16 WsColTitleAdjClm(Excel.Worksheet xlWorkSheet, DataTable dt,
        string optid, string z_company, string z_title, string z_currency, string z_criterion)
        {
            xlWorkSheet.Cells.Font.Name = "arial";
            xlWorkSheet.Cells.Font.Size = 8;

            xlWorkSheet.Columns["A"].ColumnWidth = 9;
            xlWorkSheet.Columns["B:C"].ColumnWidth = 10;
            xlWorkSheet.Columns["D"].ColumnWidth = 7;
            xlWorkSheet.Columns["E:F"].ColumnWidth = 9;
            xlWorkSheet.Columns["G:H"].ColumnWidth = 33;
            xlWorkSheet.Columns["I:N"].ColumnWidth = 11;
            xlWorkSheet.Columns["O"].ColumnWidth = 5;
            xlWorkSheet.Columns["P:Q"].ColumnWidth = 11;

            xlWorkSheet.Columns["A"].HorizontalAlignment = 2;
            xlWorkSheet.Columns["E:F"].HorizontalAlignment = 2;

            xlWorkSheet.Columns["A"].NumberFormat = "yyyy/mm/dd";
            xlWorkSheet.Columns["E:F"].NumberFormat = "yyyy/mm/dd";
            xlWorkSheet.Columns["I:N"].NumberFormat = "#,###,###.00";
            xlWorkSheet.Columns["P:Q"].NumberFormat = "#,###,###.00";

            xlWorkSheet.get_Range("P3", "P3").HorizontalAlignment = 4;
            xlWorkSheet.get_Range("Q3", "Q3").HorizontalAlignment = 2;
            xlWorkSheet.get_Range("Q3", "Q3").NumberFormat = "yyyy/mm/dd";

            xlWorkSheet.get_Range("A6", "Q6").Font.Underline = 2;

            Int16 i = 1, k = 1;

            xlWorkSheet.Cells[i++, 1] = z_company;
            xlWorkSheet.Cells[i++, 1] = z_title;
            xlWorkSheet.Cells[i, 1] = z_currency;
            xlWorkSheet.Cells[i, 16] = "Date: ";
            xlWorkSheet.Cells[i++, 17] = DateTime.Now;
            xlWorkSheet.Cells[i, 1] = z_criterion;

            i += 2;

            if (dt.Rows.Count == 0)
            {
                xlWorkSheet.Cells[i, 1] = "No record found!";
                xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                return i;
            }

            xlWorkSheet.Cells[i, k++] = "Reg Date";
            xlWorkSheet.Cells[i, k++] = "Claim No";
            xlWorkSheet.Cells[i, k++] = "Policy No";
            xlWorkSheet.Cells[i, k++] = "Producer";
            xlWorkSheet.Cells[i, k++] = "Report Date";
            xlWorkSheet.Cells[i, k++] = "Date of Loss";
            xlWorkSheet.Cells[i, k++] = "Insured";
            xlWorkSheet.Cells[i, k++] = "Loss Location / Particulars";
            xlWorkSheet.Cells[i, k++] = "Total";
            xlWorkSheet.Cells[i, k++] = "Retention";
            xlWorkSheet.Cells[i, k++] = "Surplus";
            xlWorkSheet.Cells[i, k++] = "Facultative";
            xlWorkSheet.Cells[i, k++] = "Fac Oblig";
            xlWorkSheet.Cells[i, k] = "Excess of Loss";

            k += 2;

            xlWorkSheet.Cells[i, k++] = "Total";
            xlWorkSheet.Cells[i, k++] = "Retention";


            i = WsClmAdjClm(xlWorkSheet, dt, optid, i += 2);

            return i;
        }

        public static Int16 WsClmAdjClm(Excel.Worksheet xlWorkSheet, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fclsseq = "";

            decimal ln_samount = 0, ln_sretn = 0, ln_stty = 0, ln_sfac = 0, ln_sfacb = 0, ln_sxol = 0,
                ln_sos = 0, ln_sretnos = 0;
            decimal ln_tamount = 0, ln_tretn = 0, ln_ttty = 0, ln_tfac = 0, ln_tfacb = 0, ln_txol = 0,
                ln_tos = 0, ln_tretnos = 0;
            decimal ln_scase = 0, ln_tcase = 0;

            foreach (DataRow dr in dt.Rows)
            {
                if (lc_fclsseq != dr["fclsseq"].ToString().Trim())
                {
                    if (iRow > startRow)
                    {
                        xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");

                        k = 8;
                        xlWorkSheet.Cells[iRow, k++] = "Total:";
                        xlWorkSheet.Cells[iRow, k++] = ln_samount;
                        xlWorkSheet.Cells[iRow, k++] = ln_sretn;
                        xlWorkSheet.Cells[iRow, k++] = ln_stty;
                        xlWorkSheet.Cells[iRow, k++] = ln_sfac;
                        xlWorkSheet.Cells[iRow, k++] = ln_sfacb;
                        xlWorkSheet.Cells[iRow, k] = ln_sxol;

                        k += 2;

                        xlWorkSheet.Cells[iRow, k++] = ln_sos;
                        xlWorkSheet.Cells[iRow, k++] = ln_sretnos;

                        xlWorkSheet.get_Range(String.Format("H{0}", iRow)).Columns.HorizontalAlignment = 4;

                        iRow += 2;
                    }

                    lc_fclsseq = dr["fclsseq"].ToString().Trim();
                    xlWorkSheet.Cells[iRow, 1] = dr["fclsname"].ToString();

                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                    ln_samount = 0; ln_sretn = 0; ln_stty = 0; ln_sfac = 0; ln_sfacb = 0; ln_sxol = 0;
                    ln_sos = 0; ln_sretnos = 0;
                    ln_scase = 0;

                    iRow += 2;
                }

                decimal famount = Convert.ToDecimal(dr["mres"]);
                decimal fretn = Convert.ToDecimal(dr["mretnres"]);
                decimal ftty = Convert.ToDecimal(dr["mttyres"]);
                decimal ffac = Convert.ToDecimal(dr["mfacres"]) + Convert.ToDecimal(dr["mfacnpres"]);
                decimal ffacb = Convert.ToDecimal(dr["mfacbres"]);
                decimal fxol = Convert.ToDecimal(dr["mxolres"]);
                decimal yres = Convert.ToDecimal(dr["yres"]);
                decimal ypay = Convert.ToDecimal(dr["ypay"]);
                decimal yos = Convert.ToDecimal(dr["yos"]);
                decimal yretnres = Convert.ToDecimal(dr["yretnres"]);
                decimal yretnpay = Convert.ToDecimal(dr["yretnpay"]);
                decimal yretnos = Convert.ToDecimal(dr["yretnos"]);

                k = 1;

                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fregdate"]);
                xlWorkSheet.Cells[iRow, k++] = dr["fclmno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fpolno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fprdr"];
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["frepdate"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["flosdate"]);
                xlWorkSheet.Cells[iRow, k++] = dr["finsd"] + ((char)13).ToString() + ((char)10).ToString();
                xlWorkSheet.Cells[iRow, k++] = dr["flosloc"].ToString().Trim() + ((char)13).ToString() +
                    ((char)10).ToString() + dr["fpartr_t1"].ToString().TrimEnd() +
                    dr["fpartr_t2"].ToString().TrimEnd() + ((char)10).ToString();

                xlWorkSheet.get_Range(String.Format("G{0}", iRow), String.Format("G{0}", iRow + 2)).Merge();
                xlWorkSheet.get_Range(String.Format("H{0}", iRow), String.Format("H{0}", iRow + 2)).Merge();

                xlWorkSheet.get_Range(String.Format("G{0}", iRow), String.Format("H{0}", iRow + 2)).HorizontalAlignment = 1;
                xlWorkSheet.get_Range(String.Format("G{0}", iRow), String.Format("H{0}", iRow + 2)).VerticalAlignment = 1;
                xlWorkSheet.get_Range(String.Format("G{0}", iRow), String.Format("H{0}", iRow)).RowHeight = 11.25;

                xlWorkSheet.Cells[iRow, k++] = famount;
                xlWorkSheet.Cells[iRow, k++] = fretn;
                xlWorkSheet.Cells[iRow, k++] = ftty;
                xlWorkSheet.Cells[iRow, k++] = ffac;
                xlWorkSheet.Cells[iRow, k++] = ffacb;
                xlWorkSheet.Cells[iRow, k++] = fxol;

                xlWorkSheet.Cells[iRow, k] = "Prov";
                xlWorkSheet.Cells[iRow+1, k] = "Paid";
                xlWorkSheet.Cells[iRow+2, k++] = "O/S";

                xlWorkSheet.Cells[iRow, k] = yres;
                xlWorkSheet.Cells[iRow+1, k] = ypay;
                xlWorkSheet.Cells[iRow+2, k++] = yos;

                xlWorkSheet.Cells[iRow, k] = yretnres;
                xlWorkSheet.Cells[iRow+1, k] = yretnpay;
                xlWorkSheet.Cells[iRow+2, k++] = yretnos;

                ln_samount = ln_samount + famount;
                ln_sretn = ln_sretn + fretn;
                ln_stty = ln_stty + ftty;
                ln_sfac = ln_sfac + ffac;
                ln_sfacb = ln_sfacb + ffacb;
                ln_sxol = ln_sxol + fxol;
                ln_sos = ln_sos + yos;
                ln_sretnos = ln_sretnos + yretnos;

                ln_tamount = ln_tamount + famount;
                ln_tretn = ln_tretn + fretn;
                ln_ttty = ln_ttty + ftty;
                ln_tfac = ln_tfac + ffac;
                ln_tfacb = ln_tfacb + ffacb;
                ln_txol = ln_txol + fxol;
                ln_tos = ln_tos + yos;
                ln_tretnos = ln_tretnos + yretnos;

                ln_scase++; ln_tcase++;

                iRow += 4;
            }

            xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");

            k = 8;
            xlWorkSheet.Cells[iRow, k++] = "Total:";
            xlWorkSheet.Cells[iRow, k++] = ln_samount;
            xlWorkSheet.Cells[iRow, k++] = ln_sretn;
            xlWorkSheet.Cells[iRow, k++] = ln_stty;
            xlWorkSheet.Cells[iRow, k++] = ln_sfac;
            xlWorkSheet.Cells[iRow, k++] = ln_sfacb;
            xlWorkSheet.Cells[iRow, k] = ln_sxol;

            k += 2;

            xlWorkSheet.Cells[iRow, k++] = ln_sos;
            xlWorkSheet.Cells[iRow, k++] = ln_sretnos;

            xlWorkSheet.get_Range(String.Format("H{0}", iRow)).Columns.HorizontalAlignment = 4;

            iRow += 2;

            xlWorkSheet.Cells[iRow, 1] = "Total of Cases: " + ln_tcase.ToString("N0");

            k = 8;
            xlWorkSheet.Cells[iRow, k++] = "Grand Total:";
            xlWorkSheet.Cells[iRow, k++] = ln_tamount;
            xlWorkSheet.Cells[iRow, k++] = ln_tretn;
            xlWorkSheet.Cells[iRow, k++] = ln_ttty;
            xlWorkSheet.Cells[iRow, k++] = ln_tfac;
            xlWorkSheet.Cells[iRow, k++] = ln_tfacb;
            xlWorkSheet.Cells[iRow, k] = ln_txol;

            k += 2;

            xlWorkSheet.Cells[iRow, k++] = ln_tos;
            xlWorkSheet.Cells[iRow, k++] = ln_tretnos;

            xlWorkSheet.get_Range(String.Format("H{0}", iRow)).Columns.HorizontalAlignment = 4;

            return iRow;
        }

        public static Int16 WsColTitleOSClm(Excel.Worksheet xlWorkSheet, DataTable dt,
            string optid, string z_company, string z_title, string z_currency, string z_criterion,
            string z_greq)
        {
            xlWorkSheet.Cells.Font.Name = "arial";
            xlWorkSheet.Cells.Font.Size = 8;

            xlWorkSheet.Columns["A:B"].ColumnWidth = 9;
            xlWorkSheet.Columns["C:D"].ColumnWidth = 10;
            xlWorkSheet.Columns["E"].ColumnWidth = 7;
            xlWorkSheet.Columns["F"].ColumnWidth = 9;
            xlWorkSheet.Columns["G:H"].ColumnWidth = 33;

            xlWorkSheet.Columns["J:O"].ColumnWidth = 11;

            xlWorkSheet.Columns["A:B"].HorizontalAlignment = 2;
            xlWorkSheet.Columns["F"].HorizontalAlignment = 2;

            xlWorkSheet.Columns["A:B"].NumberFormat = "yyyy/mm/dd";
            xlWorkSheet.Columns["F"].NumberFormat = "yyyy/mm/dd";
            xlWorkSheet.Columns["J:O"].NumberFormat = "#,###,###.00";

            xlWorkSheet.get_Range("N3", "N3").HorizontalAlignment = 4;
            xlWorkSheet.get_Range("O3", "O3").HorizontalAlignment = 2;
            xlWorkSheet.get_Range("O3", "O3").NumberFormat = "yyyy/mm/dd";

            xlWorkSheet.get_Range("A7", "O7").Font.Underline = 2;

            Int16 i = 1, k = 1;

            xlWorkSheet.Cells[i++, 1] = z_company;
            xlWorkSheet.Cells[i++, 1] = z_title;
            xlWorkSheet.Cells[i, 1] = z_currency;
            xlWorkSheet.Cells[i, 14] = "Date: ";
            xlWorkSheet.Cells[i++, 15] = DateTime.Now;
            xlWorkSheet.Cells[i++, 1] = z_criterion;
            xlWorkSheet.Cells[i, 1] = z_greq;

            i += 2;

            if (dt.Rows.Count == 0)
            {
                xlWorkSheet.Cells[i, 1] = "No record found!";
                xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                return i;
            }

            xlWorkSheet.Cells[i, k++] = "Reg Date";
            xlWorkSheet.Cells[i, k++] = "Report Date";
            xlWorkSheet.Cells[i, k++] = "Claim No";
            xlWorkSheet.Cells[i, k++] = "Policy No";
            xlWorkSheet.Cells[i, k++] = "Producer";
            xlWorkSheet.Cells[i, k++] = "Date of Loss";
            xlWorkSheet.Cells[i, k++] = "Insured";
            xlWorkSheet.Cells[i, k++] = "Loss Location / Particulars";

            k++;
            xlWorkSheet.Cells[i, k++] = "Gross";
            xlWorkSheet.Cells[i, k++] = "Retention";
            xlWorkSheet.Cells[i, k++] = "Surplus";
            xlWorkSheet.Cells[i, k++] = "Facultative";
            xlWorkSheet.Cells[i, k++] = "Fac Oblig";
            xlWorkSheet.Cells[i, k++] = "Excess of Loss";

            if (optid == "osclm")
                i = WsClmOSClm(xlWorkSheet, dt, optid, i += 2);

            if (optid == "clsclm")
                i = WsClmClsClm(xlWorkSheet, dt, optid, i += 2);

            return i;
        }

        public static Int16 WsClmOSClm(Excel.Worksheet xlWorkSheet, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fclsseq = "";

            decimal ln_sres = 0, ln_spay = 0, ln_sos = 0, ln_sretnres = 0, ln_sretnpay = 0, ln_sretnos= 0,
                ln_sttyres = 0, ln_sttypay = 0, ln_sttyos = 0,ln_sfacres = 0, ln_sfacpay = 0, ln_sfacos = 0,
                ln_sfacbres = 0, ln_sfacbpay = 0, ln_sfacbos = 0, ln_sxolres = 0, ln_sxolpay = 0, 
                ln_sxolos = 0;

            decimal ln_tres = 0, ln_tpay = 0, ln_tos = 0, ln_tretnres = 0, ln_tretnpay = 0, ln_tretnos = 0,
                ln_tttyres = 0, ln_tttypay = 0, ln_tttyos = 0, ln_tfacres = 0, ln_tfacpay = 0, ln_tfacos = 0,
                ln_tfacbres = 0, ln_tfacbpay = 0, ln_tfacbos = 0, ln_txolres = 0, ln_txolpay = 0,
                ln_txolos = 0;

            decimal ln_scase = 0, ln_tcase = 0;

            foreach (DataRow dr in dt.Rows)
            {
                if (lc_fclsseq != dr["fclsseq"].ToString().Trim())
                {
                    if (iRow > startRow)
                    {
                        xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");

                        k = 8;
                        xlWorkSheet.Cells[iRow, k++] = "Total:";

                        xlWorkSheet.Cells[iRow, k] = "Provision";
                        xlWorkSheet.Cells[iRow+1, k] = "Paid";
                        xlWorkSheet.Cells[iRow+2, k++] = "Outstanding";

                        xlWorkSheet.Cells[iRow, k] = ln_sres;
                        xlWorkSheet.Cells[iRow + 1, k] = ln_spay;
                        xlWorkSheet.Cells[iRow + 2, k++] = ln_sos;

                        xlWorkSheet.Cells[iRow, k] = ln_sretnres;
                        xlWorkSheet.Cells[iRow + 1, k] = ln_sretnpay;
                        xlWorkSheet.Cells[iRow + 2, k++] = ln_sretnos;

                        xlWorkSheet.Cells[iRow, k] = ln_sttyres;
                        xlWorkSheet.Cells[iRow + 1, k] = ln_sttypay;
                        xlWorkSheet.Cells[iRow + 2, k++] = ln_sttyos;

                        xlWorkSheet.Cells[iRow, k] = ln_sfacres;
                        xlWorkSheet.Cells[iRow + 1, k] = ln_sfacpay;
                        xlWorkSheet.Cells[iRow + 2, k++] = ln_sfacos;

                        xlWorkSheet.Cells[iRow, k] = ln_sfacbres;
                        xlWorkSheet.Cells[iRow + 1, k] = ln_sfacbpay;
                        xlWorkSheet.Cells[iRow + 2, k++] = ln_sfacbos;

                        xlWorkSheet.Cells[iRow, k] = ln_sxolres;
                        xlWorkSheet.Cells[iRow + 1, k] = ln_sxolpay;
                        xlWorkSheet.Cells[iRow + 2, k++] = ln_sxolos;

                        xlWorkSheet.get_Range(String.Format("H{0}", iRow)).Columns.HorizontalAlignment = 4;

                        iRow += 4;
                    }

                    lc_fclsseq = dr["fclsseq"].ToString().Trim();
                    xlWorkSheet.Cells[iRow, 1] = dr["fclsname"].ToString();

                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                    ln_sres = 0; ln_spay = 0; ln_sos = 0; ln_sretnres = 0; ln_sretnpay = 0; ln_sretnos = 0;
                    ln_sttyres = 0; ln_sttypay = 0; ln_sttyos = 0; ln_sfacres = 0; ln_sfacpay = 0;
                    ln_sfacos = 0; ln_sfacbres = 0; ln_sfacbpay = 0; ln_sfacbos = 0; ln_sxolres = 0;
                    ln_sxolpay = 0; ln_sxolos = 0;
                    ln_scase = 0;

                    iRow += 2;
                }

                decimal yres = Convert.ToDecimal(dr["yres"]);
                decimal ypay = Convert.ToDecimal(dr["ypay"]);
                decimal yos = Convert.ToDecimal(dr["yos"]);
                decimal yretnres = Convert.ToDecimal(dr["yretnres"]);
                decimal yretnpay = Convert.ToDecimal(dr["yretnpay"]);
                decimal yretnos = Convert.ToDecimal(dr["yretnos"]);
                decimal yttyres = Convert.ToDecimal(dr["yttyres"]);
                decimal yttypay = Convert.ToDecimal(dr["yttypay"]);
                decimal yttyos = Convert.ToDecimal(dr["yttyos"]);
                decimal yfacres = Convert.ToDecimal(dr["yfacres"]) + Convert.ToDecimal(dr["yfacnpres"]);
                decimal yfacpay = Convert.ToDecimal(dr["yfacpay"]) + Convert.ToDecimal(dr["yfacnppay"]);
                decimal yfacos = Convert.ToDecimal(dr["yfacos"]) + Convert.ToDecimal(dr["yfacnpos"]);
                decimal yfacbres = Convert.ToDecimal(dr["yfacbres"]);
                decimal yfacbpay = Convert.ToDecimal(dr["yfacbpay"]);
                decimal yfacbos = Convert.ToDecimal(dr["yfacbos"]);
                decimal yxolres = Convert.ToDecimal(dr["yxolres"]);
                decimal yxolpay = Convert.ToDecimal(dr["yxolpay"]);
                decimal yxolos = Convert.ToDecimal(dr["yxolos"]);

                k = 1;

                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fregdate"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["frepdate"]);
                xlWorkSheet.Cells[iRow, k++] = dr["fclmno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fpolno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fprdr"];
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["flosdate"]);
                xlWorkSheet.Cells[iRow, k++] = dr["finsd"] + ((char)13).ToString() + ((char)10).ToString();
                xlWorkSheet.Cells[iRow, k++] = dr["flosloc"].ToString().Trim() + ((char)13).ToString() +
                    ((char)10).ToString() + dr["fpartr_t1"].ToString().TrimEnd() +
                    dr["fpartr_t2"].ToString().TrimEnd() + ((char)10).ToString();

                xlWorkSheet.get_Range(String.Format("G{0}", iRow), String.Format("G{0}", iRow + 2)).Merge();
                xlWorkSheet.get_Range(String.Format("H{0}", iRow), String.Format("H{0}", iRow + 2)).Merge();

                xlWorkSheet.get_Range(String.Format("G{0}", iRow), String.Format("H{0}", iRow + 2)).HorizontalAlignment = 1;
                xlWorkSheet.get_Range(String.Format("G{0}", iRow), String.Format("H{0}", iRow + 2)).VerticalAlignment = 1;
                xlWorkSheet.get_Range(String.Format("G{0}", iRow), String.Format("H{0}", iRow)).RowHeight = 11.25;

                xlWorkSheet.Cells[iRow, k] = "Provision";
                xlWorkSheet.Cells[iRow + 1, k] = "Paid";
                xlWorkSheet.Cells[iRow + 2, k++] = "Outstanding";

                xlWorkSheet.Cells[iRow, k] = yres;
                xlWorkSheet.Cells[iRow + 1, k] = ypay;
                xlWorkSheet.Cells[iRow + 2, k++] = yos;

                xlWorkSheet.Cells[iRow, k] = yretnres;
                xlWorkSheet.Cells[iRow + 1, k] = yretnpay;
                xlWorkSheet.Cells[iRow + 2, k++] = yretnos;

                xlWorkSheet.Cells[iRow, k] = yttyres;
                xlWorkSheet.Cells[iRow + 1, k] = yttypay;
                xlWorkSheet.Cells[iRow + 2, k++] = yttyos;

                xlWorkSheet.Cells[iRow, k] = yfacres;
                xlWorkSheet.Cells[iRow + 1, k] = yfacpay;
                xlWorkSheet.Cells[iRow + 2, k++] = yfacos;

                xlWorkSheet.Cells[iRow, k] = yfacbres;
                xlWorkSheet.Cells[iRow + 1, k] = yfacbpay;
                xlWorkSheet.Cells[iRow + 2, k++] = yfacbos;

                xlWorkSheet.Cells[iRow, k] = yxolres;
                xlWorkSheet.Cells[iRow + 1, k] = yxolpay;
                xlWorkSheet.Cells[iRow + 2, k++] = yxolos;

                ln_sres = ln_sres + yres;
                ln_spay = ln_spay + ypay;
                ln_sos = ln_sos + yos;
                ln_sretnres = ln_sretnres + yretnres;
                ln_sretnpay = ln_sretnpay + yretnpay;
                ln_sretnos = ln_sretnos + yretnos;
                ln_sttyres = ln_sttyres + yttyres;
                ln_sttypay = ln_sttypay + yttypay;
                ln_sttyos = ln_sttyos + yttyos;
                ln_sfacres = ln_sfacres + yfacres;
                ln_sfacpay = ln_sfacpay + yfacpay;
                ln_sfacos = ln_sfacos + yfacos;
                ln_sfacbres = ln_sfacbres + yfacbres;
                ln_sfacbpay = ln_sfacbpay + yfacbpay;
                ln_sfacbos = ln_sfacbos + yfacbos;
                ln_sxolres = ln_sxolres + yxolres;
                ln_sxolpay = ln_sxolpay + yxolpay;
                ln_sxolos = ln_sxolos + yxolos;

                ln_tres = ln_tres + yres;
                ln_tpay = ln_tpay + ypay;
                ln_tos = ln_tos + yos;
                ln_tretnres = ln_tretnres + yretnres;
                ln_tretnpay = ln_tretnpay + yretnpay;
                ln_tretnos = ln_tretnos + yretnos;
                ln_tttyres = ln_tttyres + yttyres;
                ln_tttypay = ln_tttypay + yttypay;
                ln_tttyos = ln_tttyos + yttyos;
                ln_tfacres = ln_tfacres + yfacres;
                ln_tfacpay = ln_tfacpay + yfacpay;
                ln_tfacos = ln_tfacos + yfacos;
                ln_tfacbres = ln_tfacbres + yfacbres;
                ln_tfacbpay = ln_tfacbpay + yfacbpay;
                ln_tfacbos = ln_tfacbos + yfacbos;
                ln_txolres = ln_txolres + yxolres;
                ln_txolpay = ln_txolpay + yxolpay;
                ln_txolos = ln_txolos + yxolos;

                ln_scase++; ln_tcase++;

                iRow += 4;
            }

            xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");

            k = 8;
            xlWorkSheet.Cells[iRow, k++] = "Total:";

            xlWorkSheet.Cells[iRow, k] = "Provision";
            xlWorkSheet.Cells[iRow + 1, k] = "Paid";
            xlWorkSheet.Cells[iRow + 2, k++] = "Outstanding";

            xlWorkSheet.Cells[iRow, k] = ln_sres;
            xlWorkSheet.Cells[iRow + 1, k] = ln_spay;
            xlWorkSheet.Cells[iRow + 2, k++] = ln_sos;

            xlWorkSheet.Cells[iRow, k] = ln_sretnres;
            xlWorkSheet.Cells[iRow + 1, k] = ln_sretnpay;
            xlWorkSheet.Cells[iRow + 2, k++] = ln_sretnos;

            xlWorkSheet.Cells[iRow, k] = ln_sttyres;
            xlWorkSheet.Cells[iRow + 1, k] = ln_sttypay;
            xlWorkSheet.Cells[iRow + 2, k++] = ln_sttyos;

            xlWorkSheet.Cells[iRow, k] = ln_sfacres;
            xlWorkSheet.Cells[iRow + 1, k] = ln_sfacpay;
            xlWorkSheet.Cells[iRow + 2, k++] = ln_sfacos;

            xlWorkSheet.Cells[iRow, k] = ln_sfacbres;
            xlWorkSheet.Cells[iRow + 1, k] = ln_sfacbpay;
            xlWorkSheet.Cells[iRow + 2, k++] = ln_sfacbos;

            xlWorkSheet.Cells[iRow, k] = ln_sxolres;
            xlWorkSheet.Cells[iRow + 1, k] = ln_sxolpay;
            xlWorkSheet.Cells[iRow + 2, k++] = ln_sxolos;

            xlWorkSheet.get_Range(String.Format("H{0}", iRow)).Columns.HorizontalAlignment = 4;

            iRow += 4;

            xlWorkSheet.Cells[iRow, 1] = "Total of Cases: " + ln_tcase.ToString("N0");

            k = 8;
            xlWorkSheet.Cells[iRow, k++] = "Grand Total:";
            xlWorkSheet.Cells[iRow, k] = "Provision";
            xlWorkSheet.Cells[iRow + 1, k] = "Paid";
            xlWorkSheet.Cells[iRow + 2, k++] = "Outstanding";

            xlWorkSheet.Cells[iRow, k] = ln_tres;
            xlWorkSheet.Cells[iRow + 1, k] = ln_tpay;
            xlWorkSheet.Cells[iRow + 2, k++] = ln_tos;

            xlWorkSheet.Cells[iRow, k] = ln_tretnres;
            xlWorkSheet.Cells[iRow + 1, k] = ln_tretnpay;
            xlWorkSheet.Cells[iRow + 2, k++] = ln_tretnos;

            xlWorkSheet.Cells[iRow, k] = ln_tttyres;
            xlWorkSheet.Cells[iRow + 1, k] = ln_tttypay;
            xlWorkSheet.Cells[iRow + 2, k++] = ln_tttyos;

            xlWorkSheet.Cells[iRow, k] = ln_tfacres;
            xlWorkSheet.Cells[iRow + 1, k] = ln_tfacpay;
            xlWorkSheet.Cells[iRow + 2, k++] = ln_tfacos;

            xlWorkSheet.Cells[iRow, k] = ln_tfacbres;
            xlWorkSheet.Cells[iRow + 1, k] = ln_tfacbpay;
            xlWorkSheet.Cells[iRow + 2, k++] = ln_tfacbos;

            xlWorkSheet.Cells[iRow, k] = ln_txolres;
            xlWorkSheet.Cells[iRow + 1, k] = ln_txolpay;
            xlWorkSheet.Cells[iRow + 2, k++] = ln_txolos;
            xlWorkSheet.get_Range(String.Format("H{0}", iRow)).Columns.HorizontalAlignment = 4;

            return iRow;
        }

        public static Int16 WsClmClsClm(Excel.Worksheet xlWorkSheet, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fclsseq = "";

            decimal ln_srcy = 0, ln_spay = 0, ln_sretnrcy = 0, ln_sretnpay = 0, 
                ln_sttyrcy = 0, ln_sttypay = 0, ln_sfacrcy = 0, ln_sfacpay = 0,
                ln_sfacbrcy = 0, ln_sfacbpay = 0, ln_sxolrcy = 0, ln_sxolpay = 0;

            decimal ln_trcy = 0, ln_tpay = 0, ln_tretnrcy = 0, ln_tretnpay = 0,
                ln_tttyrcy = 0, ln_tttypay = 0, ln_tfacrcy = 0, ln_tfacpay = 0,
                ln_tfacbrcy = 0, ln_tfacbpay = 0, ln_txolrcy = 0, ln_txolpay = 0;

            decimal ln_scase = 0, ln_tcase = 0;

            foreach (DataRow dr in dt.Rows)
            {
                if (lc_fclsseq != dr["fclsseq"].ToString().Trim())
                {
                    if (iRow > startRow)
                    {
                        xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");

                        k = 8;
                        xlWorkSheet.Cells[iRow, k++] = "Total:";

                        xlWorkSheet.Cells[iRow, k] = "Paid";
                        xlWorkSheet.Cells[iRow + 1, k++] = "Recovery";

                        xlWorkSheet.Cells[iRow, k] = ln_spay;
                        xlWorkSheet.Cells[iRow + 1, k++] = ln_srcy;

                        xlWorkSheet.Cells[iRow, k] = ln_sretnpay;
                        xlWorkSheet.Cells[iRow + 1, k++] = ln_sretnrcy;

                        xlWorkSheet.Cells[iRow, k] = ln_sttypay;
                        xlWorkSheet.Cells[iRow + 1, k++] = ln_sttyrcy;

                        xlWorkSheet.Cells[iRow, k] = ln_sfacpay;
                        xlWorkSheet.Cells[iRow + 1, k++] = ln_sfacrcy;

                        xlWorkSheet.Cells[iRow, k] = ln_sfacbpay;
                        xlWorkSheet.Cells[iRow + 1, k++] = ln_sfacbrcy;

                        xlWorkSheet.Cells[iRow, k] = ln_sxolpay;
                        xlWorkSheet.Cells[iRow + 1, k++] = ln_sxolrcy;
                        
                        xlWorkSheet.get_Range(String.Format("H{0}", iRow)).Columns.HorizontalAlignment = 4;

                        iRow += 3;
                    }

                    lc_fclsseq = dr["fclsseq"].ToString().Trim();
                    xlWorkSheet.Cells[iRow, 1] = dr["fclsname"].ToString();

                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                    ln_srcy = 0; ln_spay = 0; ln_sretnrcy = 0; ln_sretnpay = 0;
                    ln_sttyrcy = 0; ln_sttypay = 0; ln_sfacrcy = 0; ln_sfacpay = 0;
                    ln_sfacbrcy = 0; ln_sfacbpay = 0; ln_sxolrcy = 0; ln_sxolpay = 0;
                    ln_scase = 0;

                    iRow += 2;
                }

                decimal fpay = Convert.ToDecimal(dr["famount"]);
                decimal frcy = Convert.ToDecimal(dr["frcyamt"]);
                decimal fretnpay = Convert.ToDecimal(dr["fretn"]);
                decimal fretnrcy = Convert.ToDecimal(dr["frcyretn"]);
                decimal fttypay = Convert.ToDecimal(dr["ftty"]);
                decimal fttyrcy = Convert.ToDecimal(dr["frcytty"]);
                decimal ffacpay = Convert.ToDecimal(dr["ffac"]) + Convert.ToDecimal(dr["ffacnp"]);
                decimal ffacrcy = Convert.ToDecimal(dr["frcyfac"]) + Convert.ToDecimal(dr["frcyfacnp"]);
                decimal ffacbpay = Convert.ToDecimal(dr["ffacb"]);
                decimal ffacbrcy = Convert.ToDecimal(dr["frcyfacb"]);
                decimal fxolpay = Convert.ToDecimal(dr["fxol"]);
                decimal fxolrcy = Convert.ToDecimal(dr["frcyxol"]);

                k = 1;

                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fregdate"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["frepdate"]);
                xlWorkSheet.Cells[iRow, k++] = dr["fclmno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fpolno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fprdr"];
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["flosdate"]);
                xlWorkSheet.Cells[iRow, k++] = dr["finsd"] + ((char)13).ToString() + ((char)10).ToString();
                xlWorkSheet.Cells[iRow, k++] = dr["flosloc"].ToString().Trim() + ((char)13).ToString() +
                    ((char)10).ToString() + dr["fpartr_t1"].ToString().TrimEnd() +
                    dr["fpartr_t2"].ToString().TrimEnd() + ((char)10).ToString();

                xlWorkSheet.get_Range(String.Format("G{0}", iRow), String.Format("G{0}", iRow + 2)).Merge();
                xlWorkSheet.get_Range(String.Format("H{0}", iRow), String.Format("H{0}", iRow + 2)).Merge();

                xlWorkSheet.get_Range(String.Format("G{0}", iRow), String.Format("H{0}", iRow + 2)).HorizontalAlignment = 1;
                xlWorkSheet.get_Range(String.Format("G{0}", iRow), String.Format("H{0}", iRow + 2)).VerticalAlignment = 1;
                xlWorkSheet.get_Range(String.Format("G{0}", iRow), String.Format("H{0}", iRow)).RowHeight = 11.25;

                xlWorkSheet.Cells[iRow, k] = "Paid";
                xlWorkSheet.Cells[iRow + 1, k++] = "Recovery";

                xlWorkSheet.Cells[iRow, k] = fpay;
                xlWorkSheet.Cells[iRow + 1, k++] = frcy;

                xlWorkSheet.Cells[iRow, k] = fretnpay;
                xlWorkSheet.Cells[iRow + 1, k++] = fretnrcy;

                xlWorkSheet.Cells[iRow, k] = fttypay;
                xlWorkSheet.Cells[iRow + 1, k++] = fttyrcy;

                xlWorkSheet.Cells[iRow, k] = ffacpay;
                xlWorkSheet.Cells[iRow + 1, k++] = ffacrcy;

                xlWorkSheet.Cells[iRow, k] = ffacbpay;
                xlWorkSheet.Cells[iRow + 1, k++] = ffacbrcy;

                xlWorkSheet.Cells[iRow, k] = fxolpay;
                xlWorkSheet.Cells[iRow + 1, k++] = fxolrcy;

                ln_srcy = ln_srcy + frcy;
                ln_spay = ln_spay + fpay;
                ln_sretnrcy = ln_sretnrcy + fretnrcy;
                ln_sretnpay = ln_sretnpay + fretnpay;
                ln_sttyrcy = ln_sttyrcy + fttyrcy;
                ln_sttypay = ln_sttypay + fttypay;
                ln_sfacrcy = ln_sfacrcy + ffacrcy;
                ln_sfacpay = ln_sfacpay + ffacpay;
                ln_sfacbrcy = ln_sfacbrcy + ffacbrcy;
                ln_sfacbpay = ln_sfacbpay + ffacbpay;
                ln_sxolrcy = ln_sxolrcy + fxolrcy;
                ln_sxolpay = ln_sxolpay + fxolpay;

                ln_trcy = ln_trcy + frcy;
                ln_tpay = ln_tpay + fpay;
                ln_tretnrcy = ln_tretnrcy + fretnrcy;
                ln_tretnpay = ln_tretnpay + fretnpay;
                ln_tttyrcy = ln_tttyrcy + fttyrcy;
                ln_tttypay = ln_tttypay + fttypay;
                ln_tfacrcy = ln_tfacrcy + ffacrcy;
                ln_tfacpay = ln_tfacpay + ffacpay;
                ln_tfacbrcy = ln_tfacbrcy + ffacbrcy;
                ln_tfacbpay = ln_tfacbpay + ffacbpay;
                ln_txolrcy = ln_txolrcy + fxolrcy;
                ln_txolpay = ln_txolpay + fxolpay;

                ln_scase++; ln_tcase++;

                iRow += 4;
            }

            xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");

            k = 8;
            xlWorkSheet.Cells[iRow, k++] = "Total:";

            xlWorkSheet.Cells[iRow, k] = "Paid";
            xlWorkSheet.Cells[iRow + 1, k++] = "Recovery";

            xlWorkSheet.Cells[iRow, k] = ln_spay;
            xlWorkSheet.Cells[iRow + 1, k++] = ln_srcy;

            xlWorkSheet.Cells[iRow, k] = ln_sretnpay;
            xlWorkSheet.Cells[iRow + 1, k++] = ln_sretnrcy;

            xlWorkSheet.Cells[iRow, k] = ln_sttypay;
            xlWorkSheet.Cells[iRow + 1, k++] = ln_sttyrcy;

            xlWorkSheet.Cells[iRow, k] = ln_sfacpay;
            xlWorkSheet.Cells[iRow + 1, k++] = ln_sfacrcy;

            xlWorkSheet.Cells[iRow, k] = ln_sfacbpay;
            xlWorkSheet.Cells[iRow + 1, k++] = ln_sfacbrcy;

            xlWorkSheet.Cells[iRow, k] = ln_sxolpay;
            xlWorkSheet.Cells[iRow + 1, k++] = ln_sxolrcy;

            xlWorkSheet.get_Range(String.Format("H{0}", iRow)).Columns.HorizontalAlignment = 4;

            iRow += 3;

            xlWorkSheet.Cells[iRow, 1] = "Total of Cases: " + ln_tcase.ToString("N0");

            k = 8;
            xlWorkSheet.Cells[iRow, k++] = "Grand Total:";

            xlWorkSheet.Cells[iRow, k] = "Paid";
            xlWorkSheet.Cells[iRow + 1, k++] = "Recovery";

            xlWorkSheet.Cells[iRow, k] = ln_tpay;
            xlWorkSheet.Cells[iRow + 1, k++] = ln_trcy;

            xlWorkSheet.Cells[iRow, k] = ln_tretnpay;
            xlWorkSheet.Cells[iRow + 1, k++] = ln_tretnrcy;

            xlWorkSheet.Cells[iRow, k] = ln_tttypay;
            xlWorkSheet.Cells[iRow + 1, k++] = ln_tttyrcy;

            xlWorkSheet.Cells[iRow, k] = ln_tfacpay;
            xlWorkSheet.Cells[iRow + 1, k++] = ln_tfacrcy;

            xlWorkSheet.Cells[iRow, k] = ln_tfacbpay;
            xlWorkSheet.Cells[iRow + 1, k++] = ln_tfacbrcy;

            xlWorkSheet.Cells[iRow, k] = ln_txolpay;
            xlWorkSheet.Cells[iRow + 1, k++] = ln_txolrcy;

            xlWorkSheet.get_Range(String.Format("H{0}", iRow)).Columns.HorizontalAlignment = 4;

            return iRow;
        }

        public static Int16 WsColTitleClsEEC(Excel.Worksheet xlWorkSheet, DataTable dt,
                string optid, string z_company, string z_title, string z_currency, string z_criterion,
                string z_greq, string z_before, string z_period)
        {
            xlWorkSheet.Cells.Font.Name = "arial";
            xlWorkSheet.Cells.Font.Size = 8;

            xlWorkSheet.Columns["A:B"].ColumnWidth = 10;
            xlWorkSheet.Columns["C"].ColumnWidth =  9;
            xlWorkSheet.Columns["D"].ColumnWidth = 12;

            xlWorkSheet.Columns["E"].ColumnWidth = 33;
            xlWorkSheet.Columns["F"].ColumnWidth = 6;

            xlWorkSheet.Columns["G:O"].ColumnWidth = 11;

            xlWorkSheet.Columns["C:D"].HorizontalAlignment = Excel.XlHAlign.xlHAlignLeft; // 2
            xlWorkSheet.Columns["F"].HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;

            xlWorkSheet.Columns["C:D"].NumberFormat = "yyyy/mm/dd";
            xlWorkSheet.Columns["G:O"].NumberFormat = "#,###,###.00";

            xlWorkSheet.get_Range("N3", "N3").HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4;
            xlWorkSheet.get_Range("O3", "O3").HorizontalAlignment = Excel.XlHAlign.xlHAlignLeft; // 2;
            xlWorkSheet.get_Range("O3", "O3").NumberFormat = "yyyy/mm/dd";

            xlWorkSheet.get_Range("G8", "O8").HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4;
            xlWorkSheet.get_Range("A8", "O8").Font.Underline = 2;

            Int16 i = 1, k = 1;

            xlWorkSheet.Cells[i++, 1] = z_company;
            xlWorkSheet.Cells[i++, 1] = z_title;
            xlWorkSheet.Cells[i, 1] = z_currency;
            xlWorkSheet.Cells[i, 14] = "Date: ";
            xlWorkSheet.Cells[i++, 15] = DateTime.Now;
            xlWorkSheet.Cells[i++, 1] = z_criterion;
            xlWorkSheet.Cells[i, 1] = z_greq;

            i += 2;

            if (dt.Rows.Count == 0)
            {
                xlWorkSheet.Cells[i, 1] = "No record found!";
                xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                return i;
            }

            xlWorkSheet.get_Range("G7", "J7").Merge();
            xlWorkSheet.get_Range("K7", "N7").Merge();

            xlWorkSheet.Cells[i, 7] = "<----------    Before "+z_before+"    ---------->" ;
            xlWorkSheet.Cells[i,11] = "<----------    "+z_period +"    ---------->";

            xlWorkSheet.get_Range("G7", "G7").HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;
            xlWorkSheet.get_Range("K7", "K7").HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;
            
            i++;
            xlWorkSheet.Cells[i, k++] = "Claim No";
            xlWorkSheet.Cells[i, k++] = "Policy No";
            xlWorkSheet.Cells[i, k++] = "Date of Loss";
            xlWorkSheet.Cells[i, k++] = "Date of Closure";
            xlWorkSheet.Cells[i, k++] = "Insured";
            xlWorkSheet.Cells[i, k++] = "Reopen";
            xlWorkSheet.Cells[i, k++] = "EC Claim";
            xlWorkSheet.Cells[i, k++] = "CL Claim";
            xlWorkSheet.Cells[i, k++] = "Solicitor Fee";
            xlWorkSheet.Cells[i, k++] = "Other Expenses";
            xlWorkSheet.Cells[i, k++] = "EC Claim";
            xlWorkSheet.Cells[i, k++] = "CL Claim";
            xlWorkSheet.Cells[i, k++] = "Solicitor Fee";
            xlWorkSheet.Cells[i, k++] = "Other Expenses";
            xlWorkSheet.Cells[i, k++] = "Total Paid";

            i = WsClmClsEEC(xlWorkSheet, dt, optid, i += 2);

            return i;
        }

        public static Int16 WsClmClsEEC(Excel.Worksheet xlWorkSheet, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fclsseq = "";

            decimal ln_secamt1 = 0, ln_sclawamt1 = 0, ln_sothamt1 = 0, ln_secamt2 = 0, ln_sclawamt2 = 0,
                ln_sothamt2 = 0, ln_secamt3 = 0, ln_sclawamt3 = 0, ln_sothamt3 = 0, ln_secamt4 = 0,
                ln_sclawamt4 = 0, ln_sothamt4 = 0, ln_secamta = 0, ln_sclawamta = 0, ln_sothamta = 0,
                ln_secamtb = 0, ln_sclawamtb = 0, ln_sothamtb = 0, ln_secamtc = 0, ln_sclawamtc = 0,
                ln_sothamtc = 0, ln_secamtd = 0, ln_sclawamtd = 0, ln_sothamtd = 0, ln_samount = 0;

            decimal ln_tecamt1 = 0, ln_tclawamt1 = 0, ln_tothamt1 = 0, ln_tecamt2 = 0, ln_tclawamt2 = 0,
                ln_tothamt2 = 0, ln_tecamt3 = 0, ln_tclawamt3 = 0, ln_tothamt3 = 0, ln_tecamt4 = 0,
                ln_tclawamt4 = 0, ln_tothamt4 = 0, ln_tecamta = 0, ln_tclawamta = 0, ln_tothamta = 0,
                ln_tecamtb = 0, ln_tclawamtb = 0, ln_tothamtb = 0, ln_tecamtc = 0, ln_tclawamtc = 0,
                ln_tothamtc = 0, ln_tecamtd = 0, ln_tclawamtd = 0, ln_tothamtd = 0, ln_tamount = 0;

            decimal ln_scase = 0, ln_tcase = 0;

            foreach (DataRow dr in dt.Rows)
            {
                if (lc_fclsseq != dr["fclsseq"].ToString().Trim())
                {
                    if (iRow > startRow)
                    {
                        xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");

                        k = 6;
                        xlWorkSheet.Cells[iRow, k++] = "Total:";

                        xlWorkSheet.Cells[iRow, k++] = ln_secamt1;
                        xlWorkSheet.Cells[iRow, k++] = ln_sclawamt1;
                        xlWorkSheet.Cells[iRow, k++] = ln_secamt2+ln_sclawamt2+ln_sothamt2+
                            ln_secamt3+ln_sclawamt3+ln_sothamt3;
                        xlWorkSheet.Cells[iRow, k++] = ln_sothamt1+ln_secamt4+ln_sclawamt4+ln_sothamt4;
                        xlWorkSheet.Cells[iRow, k++] = ln_secamta;
                        xlWorkSheet.Cells[iRow, k++] = ln_sclawamta;
                        xlWorkSheet.Cells[iRow, k++] = ln_secamtb + ln_sclawamtb + ln_sothamtb +
                            ln_secamtc + ln_sclawamtc + ln_sothamtc;
                        xlWorkSheet.Cells[iRow, k++] = ln_sothamta + ln_secamtd + ln_sclawamtd + ln_sothamtd;
                        xlWorkSheet.Cells[iRow, k++] = ln_samount;

                        xlWorkSheet.get_Range(String.Format("H{0}", iRow)).Columns.HorizontalAlignment = 4;

                        iRow += 3;
                    }

                    lc_fclsseq = dr["fclsseq"].ToString().Trim();
                    xlWorkSheet.Cells[iRow, 1] = dr["fclsname"].ToString();

                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                    ln_secamt1 = 0; ln_sclawamt1 = 0; ln_sothamt1 = 0; ln_secamt2 = 0; ln_sclawamt2 = 0;
                    ln_sothamt2 = 0; ln_secamt3 = 0; ln_sclawamt3 = 0; ln_sothamt3 = 0; ln_secamt4 = 0;
                    ln_sclawamt4 = 0; ln_sothamt4 = 0; ln_secamta = 0; ln_sclawamta = 0; ln_sothamta = 0;
                    ln_secamtb = 0; ln_sclawamtb = 0; ln_sothamtb = 0; ln_secamtc = 0; ln_sclawamtc = 0;
                    ln_sothamtc = 0; ln_secamtd = 0; ln_sclawamtd = 0; ln_sothamtd = 0; ln_samount = 0;

                    ln_scase = 0;

                    iRow += 2;
                }

                decimal fecamt1 = Convert.ToDecimal(dr["fecamt1"]);
                decimal fclawamt1 = Convert.ToDecimal(dr["fclawamt1"]);
                decimal fothamt1 = Convert.ToDecimal(dr["fothamt1"]);
                decimal fecamt2 = Convert.ToDecimal(dr["fecamt2"]);
                decimal fclawamt2 = Convert.ToDecimal(dr["fclawamt2"]);
                decimal fothamt2 = Convert.ToDecimal(dr["fothamt2"]);
                decimal fecamt3 = Convert.ToDecimal(dr["fecamt3"]);
                decimal fclawamt3 = Convert.ToDecimal(dr["fclawamt3"]);
                decimal fothamt3 = Convert.ToDecimal(dr["fothamt3"]);
                decimal fecamt4 = Convert.ToDecimal(dr["fecamt4"]);
                decimal fclawamt4 = Convert.ToDecimal(dr["fclawamt4"]);
                decimal fothamt4 = Convert.ToDecimal(dr["fothamt4"]);
                decimal fecamta = Convert.ToDecimal(dr["fecamta"]);
                decimal fclawamta = Convert.ToDecimal(dr["fclawamta"]);
                decimal fothamta = Convert.ToDecimal(dr["fothamta"]);
                decimal fecamtb = Convert.ToDecimal(dr["fecamtb"]);
                decimal fclawamtb = Convert.ToDecimal(dr["fclawamtb"]);
                decimal fothamtb = Convert.ToDecimal(dr["fothamtb"]);
                decimal fecamtc = Convert.ToDecimal(dr["fecamtc"]);
                decimal fclawamtc = Convert.ToDecimal(dr["fclawamtc"]);
                decimal fothamtc = Convert.ToDecimal(dr["fothamtc"]);
                decimal fecamtd = Convert.ToDecimal(dr["fecamtd"]);
                decimal fclawamtd = Convert.ToDecimal(dr["fclawamtd"]);
                decimal fothamtd = Convert.ToDecimal(dr["fothamtd"]);
                decimal famount = Convert.ToDecimal(dr["famount"]);

                k = 1;

                xlWorkSheet.Cells[iRow, k++] = dr["fclmno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fpolno"];
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["flosdate"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fclsdate"]);
                xlWorkSheet.Cells[iRow, k++] = dr["finsd"] + ((char)13).ToString() + ((char)10).ToString();

                xlWorkSheet.get_Range(String.Format("E{0}", iRow), String.Format("E{0}", iRow + 2)).Merge();

                xlWorkSheet.get_Range(String.Format("E{0}", iRow), String.Format("E{0}", iRow + 2)).HorizontalAlignment = 1;
                xlWorkSheet.get_Range(String.Format("E{0}", iRow), String.Format("E{0}", iRow + 2)).VerticalAlignment = 1;
                xlWorkSheet.get_Range(String.Format("E{0}", iRow), String.Format("E{0}", iRow)).RowHeight = 11.25;

                xlWorkSheet.Cells[iRow, k++] = dr["freopen"];
                xlWorkSheet.Cells[iRow, k++] = fecamt1;
                xlWorkSheet.Cells[iRow, k++] = fclawamt1;
                xlWorkSheet.Cells[iRow, k++] = fecamt2+fclawamt2+fothamt2+fecamt3+fclawamt3+fothamt3;
                xlWorkSheet.Cells[iRow, k++] = fothamt1+fecamt4+fclawamt4+fothamt4;

                xlWorkSheet.Cells[iRow, k++] = fecamta;
                xlWorkSheet.Cells[iRow, k++] = fclawamta;
                xlWorkSheet.Cells[iRow, k++] = fecamtb+fclawamtb+fothamtb+fecamtc+fclawamtc+fothamtc;
                xlWorkSheet.Cells[iRow, k++] = fothamta+fecamtd+fclawamtd+fothamtd;
                xlWorkSheet.Cells[iRow, k++] = famount;

                ln_secamt1 = ln_secamt1 + fecamt1;
                ln_sclawamt1 = ln_sclawamt1 + fclawamt1;
                ln_sothamt1 = ln_sothamt1 + fothamt1;
                ln_secamt2 = ln_secamt2 + fecamt2;
                ln_sclawamt2 = ln_sclawamt2 + fclawamt2;
                ln_sothamt2 = ln_sothamt2 + fothamt2;
                ln_secamt3 = ln_secamt3 + fecamt3;
                ln_sclawamt3 = ln_sclawamt3 + fclawamt3;
                ln_sothamt3 = ln_sothamt3 + fothamt3;
                ln_secamt4 = ln_secamt4 + fecamt4;
                ln_sclawamt4 = ln_sclawamt4 + fclawamt4;
                ln_sothamt4 = ln_sothamt4 + fothamt4;

                ln_secamta = ln_secamta + fecamta;
                ln_sclawamta = ln_sclawamta + fclawamta;
                ln_sothamta = ln_sothamta + fothamta;
                ln_secamtb = ln_secamtb + fecamtb;
                ln_sclawamtb = ln_sclawamtb + fclawamtb;
                ln_sothamtb = ln_sothamtb + fothamtb;
                ln_secamtc = ln_secamtc + fecamtc;
                ln_sclawamtc = ln_sclawamtc + fclawamtc;
                ln_sothamtc = ln_sothamtc + fothamtc;
                ln_secamtd = ln_secamtd + fecamtd;
                ln_sclawamtd = ln_sclawamtd + fclawamtd;
                ln_sothamtd = ln_sothamtd + fothamtd;
                ln_samount = ln_samount + famount;

                ln_tecamt1 = ln_tecamt1 + fecamt1;
                ln_tclawamt1 = ln_tclawamt1 + fclawamt1;
                ln_tothamt1 = ln_tothamt1 + fothamt1;
                ln_tecamt2 = ln_tecamt2 + fecamt2;
                ln_tclawamt2 = ln_tclawamt2 + fclawamt2;
                ln_tothamt2 = ln_tothamt2 + fothamt2;
                ln_tecamt3 = ln_tecamt3 + fecamt3;
                ln_tclawamt3 = ln_tclawamt3 + fclawamt3;
                ln_tothamt3 = ln_tothamt3 + fothamt3;
                ln_tecamt4 = ln_tecamt4 + fecamt4;
                ln_tclawamt4 = ln_tclawamt4 + fclawamt4;
                ln_tothamt4 = ln_tothamt4 + fothamt4;

                ln_tecamta = ln_tecamta + fecamta;
                ln_tclawamta = ln_tclawamta + fclawamta;
                ln_tothamta = ln_tothamta + fothamta;
                ln_tecamtb = ln_tecamtb + fecamtb;
                ln_tclawamtb = ln_tclawamtb + fclawamtb;
                ln_tothamtb = ln_tothamtb + fothamtb;
                ln_tecamtc = ln_tecamtc + fecamtc;
                ln_tclawamtc = ln_tclawamtc + fclawamtc;
                ln_tothamtc = ln_tothamtc + fothamtc;
                ln_tecamtd = ln_tecamtd + fecamtd;
                ln_tclawamtd = ln_tclawamtd + fclawamtd;
                ln_tothamtd = ln_tothamtd + fothamtd;
                ln_tamount = ln_tamount + famount;

                ln_scase++; ln_tcase++;

                iRow += 4;
            }

            xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");

            k = 6;
            xlWorkSheet.Cells[iRow, k++] = "Total:";

            xlWorkSheet.Cells[iRow, k++] = ln_secamt1;
            xlWorkSheet.Cells[iRow, k++] = ln_sclawamt1;
            xlWorkSheet.Cells[iRow, k++] = ln_secamt2 + ln_sclawamt2 + ln_sothamt2 +
                ln_secamt3 + ln_sclawamt3 + ln_sothamt3;
            xlWorkSheet.Cells[iRow, k++] = ln_sothamt1 + ln_secamt4 + ln_sclawamt4 + ln_sothamt4;
            xlWorkSheet.Cells[iRow, k++] = ln_secamta;
            xlWorkSheet.Cells[iRow, k++] = ln_sclawamta;
            xlWorkSheet.Cells[iRow, k++] = ln_secamtb + ln_sclawamtb + ln_sothamtb +
                ln_secamtc + ln_sclawamtc + ln_sothamtc;
            xlWorkSheet.Cells[iRow, k++] = ln_sothamta + ln_secamtd + ln_sclawamtd + ln_sothamtd;
            xlWorkSheet.Cells[iRow, k++] = ln_samount;

            xlWorkSheet.get_Range(String.Format("H{0}", iRow)).Columns.HorizontalAlignment = 4;

            iRow += 2;

            xlWorkSheet.Cells[iRow, 1] = "Total of Cases: " + ln_tcase.ToString("N0");

            k = 6;
            xlWorkSheet.Cells[iRow, k++] = "Grand Total:";

            xlWorkSheet.Cells[iRow, k++] = ln_tecamt1;
            xlWorkSheet.Cells[iRow, k++] = ln_tclawamt1;
            xlWorkSheet.Cells[iRow, k++] = ln_tecamt2 + ln_tclawamt2 + ln_tothamt2 +
                ln_tecamt3 + ln_tclawamt3 + ln_tothamt3;
            xlWorkSheet.Cells[iRow, k++] = ln_tothamt1 + ln_tecamt4 + ln_tclawamt4 + ln_tothamt4;
            xlWorkSheet.Cells[iRow, k++] = ln_tecamta;
            xlWorkSheet.Cells[iRow, k++] = ln_tclawamta;
            xlWorkSheet.Cells[iRow, k++] = ln_tecamtb + ln_tclawamtb + ln_tothamtb +
                ln_tecamtc + ln_tclawamtc + ln_tothamtc;
            xlWorkSheet.Cells[iRow, k++] = ln_tothamta + ln_tecamtd + ln_tclawamtd + ln_tothamtd;
            xlWorkSheet.Cells[iRow, k++] = ln_tamount;

            xlWorkSheet.get_Range(String.Format("H{0}", iRow)).Columns.HorizontalAlignment = 4;

            return iRow;
        }

        public static Int16 WsColCall(Excel.Worksheet xlWorkSheet, DataTable dt)
        {
            xlWorkSheet.Cells.Font.Name = "arial";
            xlWorkSheet.Cells.Font.Size = 8;

            xlWorkSheet.Columns["A"].ColumnWidth = 6;
            xlWorkSheet.Columns["B"].ColumnWidth = 10;
            xlWorkSheet.Columns["D"].ColumnWidth = 10;
            xlWorkSheet.Columns["E:F"].ColumnWidth = 15;
            xlWorkSheet.Columns["G:H"].ColumnWidth = 33;
            xlWorkSheet.Columns["J:K"].ColumnWidth = 11;
            xlWorkSheet.Columns["L:L"].ColumnWidth = 33;
            xlWorkSheet.Columns["M:Q"].ColumnWidth = 11;

            xlWorkSheet.Columns["A"].HorizontalAlignment = 2;
            xlWorkSheet.Columns["E:G"].HorizontalAlignment = 2;

            xlWorkSheet.Columns["E"].NumberFormat = "yyyy/mm/dd";
            xlWorkSheet.Columns["K"].NumberFormat = "yyyy/mm/dd";
            xlWorkSheet.Columns["O"].NumberFormat = "yyyy/mm/dd";

            Int16 i = 1;

            xlWorkSheet.Cells[i, 17] = "Date: " + DateTime.Now;

            i += 1;

            if (dt.Rows.Count == 0)
            {
                xlWorkSheet.Cells[i, 1] = "No record found!";
                xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                return i;
            }

            xlWorkSheet.Cells[i, 1] = "Class";
            xlWorkSheet.Cells[i, 2] = "Claim No";
            xlWorkSheet.Cells[i, 3] = "Injured Worker";
            xlWorkSheet.Cells[i, 4] = "Flogseq";
            xlWorkSheet.Cells[i, 5] = "Bring Up Date";
            xlWorkSheet.Cells[i, 6] = "Bring Up Action";
            xlWorkSheet.Cells[i, 7] = "Claims Life Cycle";
            xlWorkSheet.Cells[i, 8] = "File Note";
            xlWorkSheet.Cells[i, 9] = "Claim Progress";
            xlWorkSheet.Cells[i, 10] = "Claims Status";
            xlWorkSheet.Cells[i, 11] = "Loss Date";
            xlWorkSheet.Cells[i, 12] = "Loss Particulars";
            xlWorkSheet.Cells[i, 13] = "Adjuster";
            xlWorkSheet.Cells[i, 14] = "Solicitors";
            xlWorkSheet.Cells[i, 15] = "Input Date";
            xlWorkSheet.Cells[i, 16] = "Create User";
            xlWorkSheet.Cells[i, 17] = "No. of Days Elapsed";
            xlWorkSheet.Cells[i, 18] = "Case Handler";
            i = WsCallDetail(xlWorkSheet, dt, i += 1);

            return i;
        }

        public static Int16 WsCallDetail(Excel.Worksheet xlWorkSheet, DataTable dt, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            foreach (DataRow dr in dt.Rows)
            {
                k = 1;

                xlWorkSheet.Cells[iRow, k++] = dr["fclass"];
                xlWorkSheet.Cells[iRow, k++] = dr["fclmno"];
                xlWorkSheet.Cells[iRow, k++] = dr["worker"];
                xlWorkSheet.Cells[iRow, k++] = dr["flogseq"];
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fbringdate"]);

                xlWorkSheet.Cells[iRow, k++] = dr["ftoken"] ;
                xlWorkSheet.Cells[iRow, k++] = dr["fcycle"];
                xlWorkSheet.Cells[iRow, k++] = dr["fnote"].ToString().Trim() + ((char)13).ToString() + ((char)10).ToString();
                xlWorkSheet.Cells[iRow, k++] = dr["fprogress"].ToString().Trim();

                xlWorkSheet.Cells[iRow, k++] = dr["status"];
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["flosdate"]);
                xlWorkSheet.Cells[iRow, k++] = dr["Particulars"].ToString().Trim();
                xlWorkSheet.Cells[iRow, k++] = dr["fadjr1N"].ToString().Trim();
                xlWorkSheet.Cells[iRow, k++] = dr["fsolr1N"].ToString().Trim();

                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["finpdate"]);
                xlWorkSheet.Cells[iRow, k++] = dr["finpuser"];
               
                if (Fct.snFat(dr["no_of_days"]) < 0) {
                    xlWorkSheet.Cells[iRow, k++] = dr["no_of_days"];
                    xlWorkSheet.Cells[iRow, 17].Interior.Color = System.Drawing.ColorTranslator.ToOle(System.Drawing.Color.LightBlue);
                }
                else
                {
                    xlWorkSheet.Cells[iRow, k++] = dr["no_of_days"];
                }

                xlWorkSheet.Cells[iRow, k++] = dr["casehandler"];
                iRow += 1;

               
            }
            


            return iRow;
        }


    }
}
