using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;

using Excel = Microsoft.Office.Interop.Excel;

namespace INS.INSClass
{
    partial class ClaimInExcel
    {

        public static Int16 WsColTitleClmStatus(Excel.Worksheet xlWorkSheet, DataTable dt,
            string optid, string z_company, string z_title, string z_currency, string z_criterion)
        {
            xlWorkSheet.Cells.Font.Name = "arial";
            xlWorkSheet.Cells.Font.Size = 8;

            xlWorkSheet.Columns["A:C"].ColumnWidth = 9;
            xlWorkSheet.Columns["D:E"].ColumnWidth = 10;
            xlWorkSheet.Columns["F"].ColumnWidth = 7;
            xlWorkSheet.Columns["G"].ColumnWidth = 9;
            xlWorkSheet.Columns["H"].ColumnWidth = 7;
            xlWorkSheet.Columns["I:J"].ColumnWidth = 33;
            xlWorkSheet.Columns["K:M"].ColumnWidth = 11;

            xlWorkSheet.Columns["A:C"].HorizontalAlignment = Excel.XlHAlign.xlHAlignLeft; // 2
            xlWorkSheet.Columns["G"].HorizontalAlignment = Excel.XlHAlign.xlHAlignLeft; // 2

            xlWorkSheet.Columns["A:C"].NumberFormat = "yyyy/mm/dd";
            xlWorkSheet.Columns["G"].NumberFormat = "yyyy/mm/dd";
            xlWorkSheet.Columns["K:M"].NumberFormat = "#,###,###.00";

            xlWorkSheet.get_Range("M3", "M3").HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4
            xlWorkSheet.get_Range("N3", "N3").HorizontalAlignment = Excel.XlHAlign.xlHAlignLeft; // 2
            xlWorkSheet.get_Range("N3", "N3").NumberFormat = "yyyy/mm/dd";

            xlWorkSheet.get_Range("A6", "N6").Font.Underline = 2;

            Int16 i = 1, k = 1;

            xlWorkSheet.Cells[i++, 1] = z_company;
            xlWorkSheet.Cells[i++, 1] = z_title;
            xlWorkSheet.Cells[i, 1] = z_currency;
            xlWorkSheet.Cells[i, 13] = "Date: ";
            xlWorkSheet.Cells[i++, 14] = DateTime.Now;
            xlWorkSheet.Cells[i, 1] = z_criterion;

            i += 2;

            if (dt.Rows.Count == 0)
            {
                xlWorkSheet.Cells[i, 1] = "No record found!";
                xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                return i;
            }

            xlWorkSheet.Cells[i, k++] = "Reg Date";
            xlWorkSheet.Cells[i, k++] = "Report Date";
            xlWorkSheet.Cells[i, k++] = "Form 2 Date";
            xlWorkSheet.Cells[i, k++] = "Claim No";
            xlWorkSheet.Cells[i, k++] = "Policy No";
            xlWorkSheet.Cells[i, k++] = "Producer";
            xlWorkSheet.Cells[i, k++] = "Date of Loss";
            xlWorkSheet.Cells[i, k++] = "Lap Days";
            xlWorkSheet.Cells[i, k++] = "Insured";
            xlWorkSheet.Cells[i, k++] = "Loss Location / Particulars";
            xlWorkSheet.Cells[i, k++] = "Reserve";
            xlWorkSheet.Cells[i, k++] = "Paid";
            xlWorkSheet.Cells[i, k++] = "Outstanding";
            xlWorkSheet.Cells[i, k++] = "Status";

            i = WsClmCStatus(xlWorkSheet, dt, optid, i += 2);

            return i;
        }

        public static Int16 WsClmCStatus(Excel.Worksheet xlWorkSheet, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fclsseq = "";

            decimal ln_sres = 0, ln_spay = 0, ln_sos = 0,
                    ln_tres = 0, ln_tpay = 0, ln_tos = 0;

            decimal ln_scase = 0, ln_tcase = 0;

            foreach (DataRow dr in dt.Rows)
            {
                if (lc_fclsseq != dr["fclsseq"].ToString().Trim())
                {
                    if (iRow > startRow)
                    {
                        xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");

                        k = 10;
                        xlWorkSheet.Cells[iRow, k++] = "Total:";
                        xlWorkSheet.Cells[iRow, k++] = ln_sres;
                        xlWorkSheet.Cells[iRow, k++] = ln_spay;
                        xlWorkSheet.Cells[iRow, k++] = ln_sos;

                        xlWorkSheet.get_Range(String.Format("J{0}", iRow)).Columns.HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4

                        iRow += 2;
                    }

                    lc_fclsseq = dr["fclsseq"].ToString().Trim();
                    xlWorkSheet.Cells[iRow, 1] = dr["fclsname"].ToString();

                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                    ln_sres = 0; ln_spay = 0; ln_sos = 0;
                    ln_scase = 0;

                    iRow += 2;
                }

                decimal mres = Convert.ToDecimal(dr["mres"]);
                decimal mpay = Convert.ToDecimal(dr["mpay"]);
                decimal yos = Convert.ToDecimal(dr["yos"]);
                decimal fage = Convert.ToDecimal(dr["fage"]);

                k = 1;
		
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fregdate"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["frepdate"]);

                if (dr["nullfrm2"].ToString().Trim() != "1")
                    xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["ffrm2date"]);
                else
                    k++;

                xlWorkSheet.Cells[iRow, k++] = dr["fclmno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fpolno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fprdr"];
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["flosdate"]);

          		if (dr["fclass"].ToString().Trim() == "EEC")
                {
                    if (mres == 100000)
                        xlWorkSheet.Cells[iRow, k++] = "Pending";
                    else if (dr["nullfrm2"].ToString().Trim() == "1")
                        xlWorkSheet.Cells[iRow, k++] = (Convert.ToDateTime(dr["frepdate"]) - 
                             Convert.ToDateTime(dr["flosdate"])).Days;
                    else
                        xlWorkSheet.Cells[iRow, k++] = (Convert.ToDateTime(dr["ffrm2date"]) - 
                             Convert.ToDateTime(dr["flosdate"])).Days;

                 }
                 else k++;

                xlWorkSheet.Cells[iRow, k++] = dr["finsd"] + ((char)13).ToString() + ((char)10).ToString();
                xlWorkSheet.Cells[iRow, k++] = dr["flosloc"].ToString().Trim() + ((char)13).ToString() +
                    ((char)10).ToString() + dr["fpartr_t1"].ToString().TrimEnd() +
                    dr["fpartr_t2"].ToString().TrimEnd() + 
                    (fage == 0m ? "" : " (Age: " + fage.ToString("#,#")+")")+ ((char)10).ToString();

                xlWorkSheet.get_Range(String.Format("I{0}", iRow), String.Format("I{0}", iRow + 2)).Merge();
                xlWorkSheet.get_Range(String.Format("J{0}", iRow), String.Format("J{0}", iRow + 2)).Merge();

                xlWorkSheet.get_Range(String.Format("I{0}", iRow), String.Format("J{0}", iRow + 2)).HorizontalAlignment = 1;
                xlWorkSheet.get_Range(String.Format("I{0}", iRow), String.Format("J{0}", iRow + 2)).VerticalAlignment = 1;
                xlWorkSheet.get_Range(String.Format("I{0}", iRow), String.Format("J{0}", iRow)).RowHeight = 11.25;

                xlWorkSheet.Cells[iRow, k++] = mres;
                xlWorkSheet.Cells[iRow, k++] = mpay;
                xlWorkSheet.Cells[iRow, k++] = yos;
                xlWorkSheet.Cells[iRow, k++] = dr["fmoddesc"];

                ln_sres = ln_sres + mres; ln_spay = ln_spay + mpay; ln_sos = ln_sos + yos;
                ln_tres = ln_tres + mres; ln_tpay = ln_tpay + mpay; ln_tos = ln_tos + yos;

                ln_scase++; ln_tcase++;

                iRow += 4;
            }

            xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");

            k = 10;
            xlWorkSheet.Cells[iRow, k++] = "Total:";
            xlWorkSheet.Cells[iRow, k++] = ln_sres;
            xlWorkSheet.Cells[iRow, k++] = ln_spay;
            xlWorkSheet.Cells[iRow, k++] = ln_sos;

            xlWorkSheet.get_Range(String.Format("J{0}", iRow)).Columns.HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4

            iRow += 2;

            xlWorkSheet.Cells[iRow, 1] = "Total of Cases: " + ln_tcase.ToString("N0");

            k = 10;
            xlWorkSheet.Cells[iRow, k++] = "Grand Total:";
            xlWorkSheet.Cells[iRow, k++] = ln_tres;
            xlWorkSheet.Cells[iRow, k++] = ln_tpay;
            xlWorkSheet.Cells[iRow, k++] = ln_tos;

            xlWorkSheet.get_Range(String.Format("H{0}", iRow)).Columns.HorizontalAlignment = 4;

            return iRow;
        }

        public static Int16 WsColTitleDevStat(Excel.Worksheet xlWorkSheet, DataTable dt,
            string optid, string z_company, string z_title, string z_currency)
        {
            xlWorkSheet.Cells.Font.Name = "arial";
            xlWorkSheet.Cells.Font.Size = 8;

            xlWorkSheet.Columns["A"].ColumnWidth = 10;
            xlWorkSheet.Columns["B"].ColumnWidth = 9;
            xlWorkSheet.Columns["C"].ColumnWidth = 33;
            xlWorkSheet.Columns["D"].ColumnWidth = 20;
            xlWorkSheet.Columns["E"].ColumnWidth = 10;
            xlWorkSheet.Columns["F:O"].ColumnWidth = 11;

            xlWorkSheet.Columns["B"].HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter; // 3
            xlWorkSheet.Columns["E"].HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter; // 3

            xlWorkSheet.Columns["B"].NumberFormat = "yyyy/mm/dd";
            xlWorkSheet.Columns["F:O"].NumberFormat = "#,###,###.00";

            xlWorkSheet.get_Range("N2", "N2").HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4
            xlWorkSheet.get_Range("O2", "O2").HorizontalAlignment = Excel.XlHAlign.xlHAlignLeft; // 2
            xlWorkSheet.get_Range("A6", "O7").HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter; // 2
            xlWorkSheet.get_Range("O2", "O2").NumberFormat = "yyyy/mm/dd";

            Int16 i = 1, k = 1;

            xlWorkSheet.Cells[i++, 1] = z_company;
            xlWorkSheet.Cells[i, 1] = z_title;
            xlWorkSheet.Cells[i, 14] = "Date: ";
            xlWorkSheet.Cells[i++, 15] = DateTime.Now;
            xlWorkSheet.Cells[i, 1] = z_currency;

            i += 2;

            if (dt.Rows.Count == 0)
            {
                xlWorkSheet.Cells[i, 1] = "No record found!";
                xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                return i;
            }

            xlWorkSheet.Cells[i, 1] = dt.DefaultView[0]["fclsname"];

            i ++;

            xlWorkSheet.Cells[i+1, k++] = "Claim No.";
            xlWorkSheet.Cells[i, k] = "Date of";
            xlWorkSheet.Cells[i+1, k++] = "Loss";

            xlWorkSheet.Cells[i+1, k++] = "Insured";
            xlWorkSheet.Cells[i+1, k++] = "Nature of Loss";
            xlWorkSheet.Cells[i+1, k++] = "Year";
            xlWorkSheet.Cells[i+1, k++] = "1st";
            xlWorkSheet.Cells[i+1, k++] = "2nd";

            xlWorkSheet.Cells[i+1, k++] = "3rd";
            xlWorkSheet.Cells[i+1, k++] = "4th";
            xlWorkSheet.Cells[i+1, k++] = "5th";
            xlWorkSheet.Cells[i+1, k++] = "6th";
            xlWorkSheet.Cells[i+1, k++] = "7th";
            xlWorkSheet.Cells[i+1, k++] = "8th";
            xlWorkSheet.Cells[i+1, k++] = "9th";
            xlWorkSheet.Cells[i+1, k] = "10th";

            xlWorkSheet.get_Range(String.Format("A{0}", i), String.Format("O{0}", i)).Borders[Excel.XlBordersIndex.xlEdgeTop].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("A{0}:O{1}", i, i + 1)).Borders[Excel.XlBordersIndex.xlEdgeLeft].LineStyle = 7;

            for (int j = 0; j < k; j++)
            {
                string xxx_str = ((char) ((int) 'A' + j)).ToString();
                xlWorkSheet.get_Range(String.Format("{0}{1}:{0}{2}",xxx_str, i, i + 1)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
            }            
            
            i = WsClmDevStat(xlWorkSheet, dt, optid, i += 2);

            return i;
        }

        public static Int16 WsClmDevStat(Excel.Worksheet xlWorkSheet, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            foreach (DataRow dr in dt.Rows)
            {
                decimal fpay01 = Convert.ToDecimal(dr["fpay01"]);
                decimal fpay02 = Convert.ToDecimal(dr["fpay02"]);
                decimal fpay03 = Convert.ToDecimal(dr["fpay03"]);
                decimal fpay04 = Convert.ToDecimal(dr["fpay04"]);
                decimal fpay05 = Convert.ToDecimal(dr["fpay05"]);
                decimal fpay06 = Convert.ToDecimal(dr["fpay06"]);
                decimal fpay07 = Convert.ToDecimal(dr["fpay07"]);
                decimal fpay08 = Convert.ToDecimal(dr["fpay08"]);
                decimal fpay09 = Convert.ToDecimal(dr["fpay09"]);
                decimal fpay10 = Convert.ToDecimal(dr["fpay10"]);

                decimal fos01 = Convert.ToDecimal(dr["fos01"]);
                decimal fos02 = Convert.ToDecimal(dr["fos02"]);
                decimal fos03 = Convert.ToDecimal(dr["fos03"]);
                decimal fos04 = Convert.ToDecimal(dr["fos04"]);
                decimal fos05 = Convert.ToDecimal(dr["fos05"]);
                decimal fos06 = Convert.ToDecimal(dr["fos06"]);
                decimal fos07 = Convert.ToDecimal(dr["fos07"]);
                decimal fos08 = Convert.ToDecimal(dr["fos08"]);
                decimal fos09 = Convert.ToDecimal(dr["fos09"]);
                decimal fos10 = Convert.ToDecimal(dr["fos10"]);

                decimal fres01 = Convert.ToDecimal(dr["fres01"]);
                decimal fres02 = Convert.ToDecimal(dr["fres02"]);
                decimal fres03 = Convert.ToDecimal(dr["fres03"]);
                decimal fres04 = Convert.ToDecimal(dr["fres04"]);
                decimal fres05 = Convert.ToDecimal(dr["fres05"]);
                decimal fres06 = Convert.ToDecimal(dr["fres06"]);
                decimal fres07 = Convert.ToDecimal(dr["fres07"]);
                decimal fres08 = Convert.ToDecimal(dr["fres08"]);
                decimal fres09 = Convert.ToDecimal(dr["fres09"]);
                decimal fres10 = Convert.ToDecimal(dr["fres10"]);

                k = 1;

                xlWorkSheet.Cells[iRow, k++] = dr["fclmno"];
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["flosdate"]);
                xlWorkSheet.Cells[iRow, k++] = dr["finsd"] + ((char)13).ToString() + ((char)10).ToString();
                xlWorkSheet.Cells[iRow, k++] = dr["fdstdesc"] + ((char)13).ToString() + ((char)10).ToString();

                xlWorkSheet.Cells[iRow, k] = "Paid";
                xlWorkSheet.Cells[iRow+1, k] = "Outstanding";
                xlWorkSheet.Cells[iRow+2, k] = "Incurred";

                k++;

                if (fpay01 != -999.00m && fos01 != -999.00m && fres01 != -999.00m)
                {
                    xlWorkSheet.Cells[iRow, k] = fpay01;
                    xlWorkSheet.Cells[iRow + 1, k] = fos01;
                    xlWorkSheet.Cells[iRow + 2, k] = fres01;
                }

                k++;

                if (fpay02 != -999.00m && fos02 != -999.00m && fres02 != -999.00m)
                {
                    xlWorkSheet.Cells[iRow, k] = fpay02;
                    xlWorkSheet.Cells[iRow + 1, k] = fos02;
                    xlWorkSheet.Cells[iRow + 2, k] = fres02;
                }

                k++;

                if (fpay03 != -999.00m && fos03 != -999.00m && fres03 != -999.00m)
                {
                    xlWorkSheet.Cells[iRow, k] = fpay03;
                    xlWorkSheet.Cells[iRow + 1, k] = fos03;
                    xlWorkSheet.Cells[iRow + 2, k] = fres03;
                }

                k++;

                if (fpay04 != -999.00m && fos04 != -999.00m && fres04 != -999.00m)
                {
                    xlWorkSheet.Cells[iRow, k] = fpay04;
                    xlWorkSheet.Cells[iRow + 1, k] = fos04;
                    xlWorkSheet.Cells[iRow + 2, k] = fres04;
                }

                k++;

                if (fpay05 != -999.00m && fos05 != -999.00m && fres05 != -999.00m)
                {
                    xlWorkSheet.Cells[iRow, k] = fpay05;
                    xlWorkSheet.Cells[iRow + 1, k] = fos05;
                    xlWorkSheet.Cells[iRow + 2, k] = fres05;
                }

                k++;

                if (fpay06 != -999.00m && fos06 != -999.00m && fres06 != -999.00m)
                {
                    xlWorkSheet.Cells[iRow, k] = fpay06;
                    xlWorkSheet.Cells[iRow + 1, k] = fos06;
                    xlWorkSheet.Cells[iRow + 2, k] = fres06;
                }

                k++;

                if (fpay07 != -999.00m && fos07 != -999.00m && fres07 != -999.00m)
                {
                    xlWorkSheet.Cells[iRow, k] = fpay07;
                    xlWorkSheet.Cells[iRow + 1, k] = fos07;
                    xlWorkSheet.Cells[iRow + 2, k] = fres07;
                }

                k++;

                if (fpay08 != -999.00m && fos08 != -999.00m && fres08 != -999.00m)
                {
                    xlWorkSheet.Cells[iRow, k] = fpay08;
                    xlWorkSheet.Cells[iRow + 1, k] = fos08;
                    xlWorkSheet.Cells[iRow + 2, k] = fres08;
                }

                k++;

                if (fpay09 != -999.00m && fos09 != -999.00m && fres09 != -999.00m)
                {
                    xlWorkSheet.Cells[iRow, k] = fpay09;
                    xlWorkSheet.Cells[iRow + 1, k] = fos09;
                    xlWorkSheet.Cells[iRow + 2, k] = fres09;
                }

                k++;

                if (fpay10 != -999.00m && fos10 != -999.00m && fres10 != -999.00m)
                {
                    xlWorkSheet.Cells[iRow, k] = fpay10;
                    xlWorkSheet.Cells[iRow + 1, k] = fos10;
                    xlWorkSheet.Cells[iRow + 2, k] = fres10;
                }

                xlWorkSheet.get_Range(String.Format("C{0}", iRow), String.Format("C{0}", iRow + 2)).Merge();
                xlWorkSheet.get_Range(String.Format("D{0}", iRow), String.Format("D{0}", iRow + 2)).Merge();

                xlWorkSheet.get_Range(String.Format("C{0}", iRow), String.Format("D{0}", iRow + 2)).HorizontalAlignment = 1;
                xlWorkSheet.get_Range(String.Format("C{0}", iRow), String.Format("D{0}", iRow + 2)).VerticalAlignment = 1;
                xlWorkSheet.get_Range(String.Format("C{0}", iRow), String.Format("D{0}", iRow)).RowHeight = 11.25;

                xlWorkSheet.get_Range(String.Format("A{0}", iRow), String.Format("O{0}", iRow)).Borders[Excel.XlBordersIndex.xlEdgeTop].LineStyle = 7;
                xlWorkSheet.get_Range(String.Format("A{0}", iRow), String.Format("O{0}", iRow + 2)).Borders[Excel.XlBordersIndex.xlEdgeLeft].LineStyle = 7;

                for (int j = 0; j < k; j++)
                {
                    string xxx_str = ((char)((int)'A' + j)).ToString();
                    xlWorkSheet.get_Range(String.Format("{0}{1}:{0}{2}", xxx_str, iRow, iRow + 2)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
                }            

                iRow += 3;
            }

            xlWorkSheet.get_Range(String.Format("A{0}", iRow), String.Format("O{0}", iRow)).Borders[Excel.XlBordersIndex.xlEdgeTop].LineStyle = 7;

            return iRow;
        }

        public static void WbClmSumClm(Excel.Workbook xlWorkBook, DataSet ds, string optid, string z_company,
                            string z_title, string z_currency)
        {
            for (Int16 k = 1; k <= 3; k++)
            {
                Int16 i, tableIndex;
                Excel.Worksheet xlWorkSheet;

                xlWorkSheet = xlWorkBook.Worksheets.get_Item(k);

                tableIndex = 0;

                if (k == 1) 
                {
                    xlWorkSheet.Name = "Loss Incurred";
                }

                if (k == 2)
                {
                    xlWorkSheet.Name = "Claims Paid I";
                }

                if (k == 3)
                {
                    xlWorkSheet.Name = "Claims Paid II";
                }


                i = WsColTitleSumClm(xlWorkSheet, ds.Tables[tableIndex], k, optid, z_company, z_title,
                        z_currency);
            }

            return;
        }

        public static Int16 WsColTitleSumClm(Excel.Worksheet xlWorkSheet, DataTable dt, Int16 sheetIndex,
                        string optid, string z_company, string z_title, string z_currency)
        {
            Int16 i = 1;

            if (sheetIndex == 1)
            {
                xlWorkSheet.Cells.Font.Name = "arial";
                xlWorkSheet.Cells.Font.Size = 8;

                xlWorkSheet.Columns["A"].ColumnWidth = 30;
                xlWorkSheet.Columns["B"].ColumnWidth = 10;
                xlWorkSheet.Columns["C:I"].ColumnWidth = 14;

                xlWorkSheet.Columns["C:I"].HorizontalAlignment = 4; ;
                xlWorkSheet.Columns["C:H"].NumberFormat = "#,##0.00_)";
                xlWorkSheet.Columns["I"].NumberFormat = "#,##0_)";

                xlWorkSheet.get_Range("C6", "I7").HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;
                xlWorkSheet.get_Range("I3", "I3").HorizontalAlignment = 2;
                xlWorkSheet.get_Range("I3", "I3").NumberFormat = "yyyy/mm/dd";

                xlWorkSheet.get_Range("A7", "I7").Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 7;

                xlWorkSheet.get_Range("A5", "A5").Font.Bold = true;
                xlWorkSheet.get_Range("I6", "I6").Font.Bold = true;
                xlWorkSheet.get_Range("A7", "I7").Font.Bold = true;

                xlWorkSheet.Cells[i++, 1] = z_company;
                xlWorkSheet.Cells[i++, 1] = z_title;
                xlWorkSheet.Cells[i, 1] = z_currency;
                xlWorkSheet.Cells[i, 8] = "Date: ";
                xlWorkSheet.Cells[i, 9] = DateTime.Now;

                i +=2;

                xlWorkSheet.Cells[i++, 1] = "Loss Incurred";

                Int16 k = 1;

                xlWorkSheet.Cells[i+1, k++] = "Class";
                xlWorkSheet.Cells[i+1, k++] = "Type";
                xlWorkSheet.Cells[i+1, k++] = "Total Amount";
                xlWorkSheet.Cells[i+1, k++] = "Retention";
                xlWorkSheet.Cells[i+1, k++] = "Surplus";
                xlWorkSheet.Cells[i+1, k++] = "Facultative";
                xlWorkSheet.Cells[i+1, k++] = "Fac Oblig";
                xlWorkSheet.Cells[i+1, k++] = "Excess of Loss";
                xlWorkSheet.Cells[i, k] = "No. of New";
                xlWorkSheet.Cells[i+1, k] = "Claim";

                i += 3;

                i = WsClmSumClm(xlWorkSheet, sheetIndex, dt, optid, i);

            }

            if (sheetIndex == 2)
            {
                xlWorkSheet.Cells.Font.Name = "arial";
                xlWorkSheet.Cells.Font.Size = 8;

                xlWorkSheet.Columns["A"].ColumnWidth = 30;
                xlWorkSheet.Columns["B:G"].ColumnWidth = 14;

                xlWorkSheet.Columns["B:G"].HorizontalAlignment = 4; ;
                xlWorkSheet.Columns["B:G"].NumberFormat = "#,##0.00_)";

                xlWorkSheet.get_Range("G3", "G3").HorizontalAlignment = 2;
                xlWorkSheet.get_Range("G3", "G3").NumberFormat = "yyyy/mm/dd";

                xlWorkSheet.get_Range("B7", "G7").HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;
                xlWorkSheet.get_Range("A7", "G7").Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 7;

                xlWorkSheet.get_Range("A5", "A5").Font.Bold = true;
                xlWorkSheet.get_Range("A7", "G7").Font.Bold = true;

                xlWorkSheet.Cells[i++, 1] = z_company;
                xlWorkSheet.Cells[i++, 1] = z_title;
                xlWorkSheet.Cells[i, 1] = z_currency;
                xlWorkSheet.Cells[i, 6] = "Date: ";
                xlWorkSheet.Cells[i, 7] = DateTime.Now;

                i += 2;

                xlWorkSheet.Cells[i, 1] = "Claims Settlement";

                i += 2;
                Int16 k = 1;

                xlWorkSheet.Cells[i, k++] = "Class";
                xlWorkSheet.Cells[i, k++] = "Total Amount";
                xlWorkSheet.Cells[i, k++] = "Retention";
                xlWorkSheet.Cells[i, k++] = "Surplus";
                xlWorkSheet.Cells[i, k++] = "Facultative";
                xlWorkSheet.Cells[i, k++] = "Fac Oblig";
                xlWorkSheet.Cells[i, k++] = "Excess of Loss";

                i += 2;

                i = WsClmSumClm(xlWorkSheet, sheetIndex, dt, optid, i);
            }

            if (sheetIndex == 3)
            {
                xlWorkSheet.Cells.Font.Name = "arial";
                xlWorkSheet.Cells.Font.Size = 8;

                xlWorkSheet.Columns["A"].ColumnWidth = 30;
                xlWorkSheet.Columns["B:H"].ColumnWidth = 14;

                xlWorkSheet.Columns["B:H"].HorizontalAlignment = 4; ;
                xlWorkSheet.Columns["B:H"].NumberFormat = "#,##0.00_)";

                xlWorkSheet.get_Range("H3", "H3").HorizontalAlignment = 2;
                xlWorkSheet.get_Range("H3", "H3").NumberFormat = "yyyy/mm/dd";

                xlWorkSheet.get_Range("B7", "H7").HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;
                xlWorkSheet.get_Range("A7", "H7").Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 7;

                xlWorkSheet.get_Range("A5", "A5").Font.Bold = true;
                xlWorkSheet.get_Range("A7", "H7").Font.Bold = true;

                xlWorkSheet.Cells[i++, 1] = z_company;
                xlWorkSheet.Cells[i++, 1] = z_title;
                xlWorkSheet.Cells[i, 1] = z_currency;
                xlWorkSheet.Cells[i, 7] = "Date: ";
                xlWorkSheet.Cells[i, 8] = DateTime.Now;

                i += 2;

                xlWorkSheet.Cells[i, 1] = "Claims Settlement";

                i += 2;
                Int16 k = 1;

                xlWorkSheet.Cells[i, k++] = "Class";
                xlWorkSheet.Cells[i, k++] = "Total Amount";
                xlWorkSheet.Cells[i, k++] = "Claim Amount";
                xlWorkSheet.Cells[i, k++] = "Own Solicitor";
                xlWorkSheet.Cells[i, k++] = "Clmnt Solicitor";
                xlWorkSheet.Cells[i, k++] = "Adjuster Fee";
                xlWorkSheet.Cells[i, k++] = "Health Care";
                xlWorkSheet.Cells[i, k++] = "Other";

                i += 2;

                i = WsClmSumClm(xlWorkSheet, sheetIndex, dt, optid, i);
            }

            return i;
        }

        public static Int16 WsClmSumClm(Excel.Worksheet xlWorkSheet, Int16 sheetIndex, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            if (sheetIndex == 1)
            {
                decimal ln_treserve = 0, ln_tretnres = 0, ln_tttyres = 0, ln_tfacres = 0, ln_tfacbres = 0,
                    ln_txolres = 0, ln_tvcount = 0;
                decimal ln_tadjamt = 0, ln_tretnadj = 0, ln_tttyadj = 0, ln_tfacadj = 0, ln_tfacbadj = 0,
                    ln_txoladj = 0, ln_trvy = 0, ln_tretnrvy = 0, ln_tttyrvy = 0, ln_tfacrvy = 0,
                    ln_tfacbrvy = 0, ln_txolrvy = 0;
                decimal ln_tincloss = 0, ln_tretnloss = 0, ln_tttyloss = 0, ln_tfacloss = 0, ln_tfacbloss = 0,
                    ln_txolloss = 0;

                foreach (DataRow dr in dt.Rows)
                {
                    if (dr["ftype"].ToString().Trim() != "01")
                        continue;

                    decimal freserve = Convert.ToDecimal(dr["freserve"]);
                    decimal fretnres = Convert.ToDecimal(dr["fretnres"]);
                    decimal fttyres = Convert.ToDecimal(dr["fttyres"]);
                    decimal ffacres = Convert.ToDecimal(dr["ffacres"]);
                    decimal ffacnpres = Convert.ToDecimal(dr["ffacnpres"]);
                    decimal ffacbres = Convert.ToDecimal(dr["ffacbres"]);
                    decimal fxolres = Convert.ToDecimal(dr["fxolres"]);
                    decimal vcount = Convert.ToDecimal(dr["vcount"]);

                    decimal fadjamt = Convert.ToDecimal(dr["fadjamt"]);
                    decimal fretnadj = Convert.ToDecimal(dr["fretnadj"]);
                    decimal fttyadj = Convert.ToDecimal(dr["fttyadj"]);
                    decimal ffacadj = Convert.ToDecimal(dr["ffacadj"]);
                    decimal ffacnpadj = Convert.ToDecimal(dr["ffacnpadj"]);
                    decimal ffacbadj = Convert.ToDecimal(dr["ffacbadj"]);
                    decimal fxoladj = Convert.ToDecimal(dr["fxoladj"]);

                    decimal frvy = Convert.ToDecimal(dr["frvy"]);
                    decimal fretnrvy = Convert.ToDecimal(dr["fretnrvy"]);
                    decimal fttyrvy = Convert.ToDecimal(dr["fttyrvy"]);
                    decimal ffacrvy = Convert.ToDecimal(dr["ffacrvy"]);
                    decimal ffacnprvy = Convert.ToDecimal(dr["ffacnprvy"]);
                    decimal ffacbrvy = Convert.ToDecimal(dr["ffacbrvy"]);
                    decimal fxolrvy = Convert.ToDecimal(dr["fxolrvy"]);

                    decimal fincloss = Convert.ToDecimal(dr["fincloss"]);
                    decimal fretnloss = Convert.ToDecimal(dr["fretnloss"]);
                    decimal fttyloss = Convert.ToDecimal(dr["fttyloss"]);
                    decimal ffacloss = Convert.ToDecimal(dr["ffacloss"]);
                    decimal ffacnploss = Convert.ToDecimal(dr["ffacnploss"]);
                    decimal ffacbloss = Convert.ToDecimal(dr["ffacbloss"]);
                    decimal fxolloss = Convert.ToDecimal(dr["fxolloss"]);

                    k = 1;
                    xlWorkSheet.Cells[iRow, k++] = dr["fclsname"];
                    xlWorkSheet.Cells[iRow, k++] = "Reserve";
                    xlWorkSheet.Cells[iRow, k++] = freserve;
                    xlWorkSheet.Cells[iRow, k++] = fretnres;
                    xlWorkSheet.Cells[iRow, k++] = fttyres;
                    xlWorkSheet.Cells[iRow, k++] = ffacres+ffacnpres;
                    xlWorkSheet.Cells[iRow, k++] = ffacbres;
                    xlWorkSheet.Cells[iRow, k++] = fxolres;
                    xlWorkSheet.Cells[iRow++, k++] = vcount;

                    k = 2;
                    xlWorkSheet.Cells[iRow, k++] = "Adjustment";
                    xlWorkSheet.Cells[iRow, k++] = fadjamt;
                    xlWorkSheet.Cells[iRow, k++] = fretnadj;
                    xlWorkSheet.Cells[iRow, k++] = fttyadj;
                    xlWorkSheet.Cells[iRow, k++] = ffacadj + ffacnpadj;
                    xlWorkSheet.Cells[iRow, k++] = ffacbadj;
                    xlWorkSheet.Cells[iRow++, k++] = fxoladj;

                    k = 2;
                    xlWorkSheet.Cells[iRow, k++] = "Recovery";
                    xlWorkSheet.Cells[iRow, k++] = frvy;
                    xlWorkSheet.Cells[iRow, k++] = fretnrvy;
                    xlWorkSheet.Cells[iRow, k++] = fttyrvy;
                    xlWorkSheet.Cells[iRow, k++] = ffacrvy + ffacnprvy;
                    xlWorkSheet.Cells[iRow, k++] = ffacbrvy;
                    xlWorkSheet.Cells[iRow, k++] = fxolrvy;

                    xlWorkSheet.get_Range(String.Format("C{0}", iRow), String.Format("I{0}", iRow)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 7;
                    iRow++;

                    k = 2;
                    xlWorkSheet.Cells[iRow, k++] = "Sub-Total";
                    xlWorkSheet.Cells[iRow, k++] = fincloss;
                    xlWorkSheet.Cells[iRow, k++] = fretnloss;
                    xlWorkSheet.Cells[iRow, k++] = fttyloss;
                    xlWorkSheet.Cells[iRow, k++] = ffacloss + ffacnploss;
                    xlWorkSheet.Cells[iRow, k++] = ffacbloss;
                    xlWorkSheet.Cells[iRow, k++] = fxolloss;

                    xlWorkSheet.get_Range(String.Format("C{0}", iRow), String.Format("I{0}", iRow)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 9;

                    ln_treserve = ln_treserve + freserve;
                    ln_tretnres	= ln_tretnres + fretnres;
                    ln_tttyres	= ln_tttyres + fttyres;
                    ln_tfacres	= ln_tfacres + ffacres + ffacnpres;
                    ln_tfacbres	= ln_tfacbres + ffacbres;
                    ln_txolres  = ln_txolres + fxolres;
                    ln_tvcount  = ln_tvcount + vcount;

                    ln_tadjamt  = ln_tadjamt + fadjamt;
                    ln_tretnadj	= ln_tretnadj + fretnadj;
                    ln_tttyadj	= ln_tttyadj + fttyadj;
                    ln_tfacadj	= ln_tfacadj + ffacadj + ffacnpadj;
                    ln_tfacbadj	= ln_tfacbadj + ffacbadj;
                    ln_txoladj  = ln_txoladj + fxoladj;

                    ln_trvy     = ln_trvy + frvy;
                    ln_tretnrvy	= ln_tretnrvy + fretnrvy;
                    ln_tttyrvy	= ln_tttyrvy + fttyrvy;
                    ln_tfacrvy	= ln_tfacrvy + ffacrvy + ffacnprvy;
                    ln_tfacbrvy	= ln_tfacbrvy + ffacbrvy;
                    ln_txolrvy  = ln_txolrvy + fxolrvy;

                    ln_tincloss  = ln_tincloss + fincloss;
                    ln_tretnloss = ln_tretnloss + fretnloss;
                    ln_tttyloss  = ln_tttyloss + fttyloss;
                    ln_tfacloss  = ln_tfacloss + ffacloss + ffacnploss;
                    ln_tfacbloss = ln_tfacbloss + ffacbloss;
                    ln_txolloss = ln_txolloss + fxolloss;

                    iRow += 2;
                }

                k = 2;
                xlWorkSheet.Cells[iRow, k++] = "Reserve";
                xlWorkSheet.Cells[iRow, k++] = ln_treserve;
                xlWorkSheet.Cells[iRow, k++] = ln_tretnres;
                xlWorkSheet.Cells[iRow, k++] = ln_tttyres;
                xlWorkSheet.Cells[iRow, k++] = ln_tfacres;
                xlWorkSheet.Cells[iRow, k++] = ln_tfacbloss;
                xlWorkSheet.Cells[iRow, k++] = ln_txolloss;
                xlWorkSheet.Cells[iRow, k++] = ln_tvcount;

                iRow++; k = 2;
                xlWorkSheet.Cells[iRow, k++] = "Adjustment";
                xlWorkSheet.Cells[iRow, k++] = ln_tadjamt;
                xlWorkSheet.Cells[iRow, k++] = ln_tretnadj;
                xlWorkSheet.Cells[iRow, k++] = ln_tttyadj;
                xlWorkSheet.Cells[iRow, k++] = ln_tfacadj;
                xlWorkSheet.Cells[iRow, k++] = ln_tfacbadj;
                xlWorkSheet.Cells[iRow, k++] = ln_txoladj;

                iRow++; k = 2;
                xlWorkSheet.Cells[iRow, k++] = "Recovery";
                xlWorkSheet.Cells[iRow, k++] = ln_trvy;
                xlWorkSheet.Cells[iRow, k++] = ln_tretnrvy;
                xlWorkSheet.Cells[iRow, k++] = ln_tttyrvy;
                xlWorkSheet.Cells[iRow, k++] = ln_tfacrvy;
                xlWorkSheet.Cells[iRow, k++] = ln_tfacbrvy;
                xlWorkSheet.Cells[iRow, k++] = ln_txolrvy;

                xlWorkSheet.get_Range(String.Format("C{0}", iRow), String.Format("I{0}", iRow)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 7;
                iRow++; k = 2;

                xlWorkSheet.Cells[iRow, k++] = "Grand Total";
                xlWorkSheet.Cells[iRow, k++] = ln_tincloss;
                xlWorkSheet.Cells[iRow, k++] = ln_tretnloss;
                xlWorkSheet.Cells[iRow, k++] = ln_tttyloss;
                xlWorkSheet.Cells[iRow, k++] = ln_tfacloss;
                xlWorkSheet.Cells[iRow, k++] = ln_tfacbloss;
                xlWorkSheet.Cells[iRow, k++] = ln_txolloss;

                xlWorkSheet.get_Range(String.Format("C{0}", iRow), String.Format("I{0}", iRow)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 9;
            }

            if (sheetIndex == 2)
            {
                decimal ln_tpay = 0, ln_tretnpay = 0, ln_tttypay = 0, ln_tfacpay = 0,
                    ln_tfacbpay = 0, ln_txolpay = 0;

                foreach (DataRow dr in dt.Rows)
                {
                    if (dr["ftype"].ToString().Trim() != "02")
                        continue;

                    decimal fpay = Convert.ToDecimal(dr["fpay"]);
                    decimal fretnpay = Convert.ToDecimal(dr["fretnpay"]);
                    decimal fttypay = Convert.ToDecimal(dr["fttypay"]);
                    decimal ffacpay	= Convert.ToDecimal(dr["ffacpay"]);
                    decimal ffacnppay = Convert.ToDecimal(dr["ffacnppay"]);
                    decimal ffacbpay	= Convert.ToDecimal(dr["ffacbpay"]);
                    decimal fxolpay = Convert.ToDecimal(dr["fxolpay"]);

                    k = 1;

                    xlWorkSheet.Cells[iRow, k++] = dr["fclsname"];
                    xlWorkSheet.Cells[iRow, k++] = fpay;
                    xlWorkSheet.Cells[iRow, k++] = fretnpay;
                    xlWorkSheet.Cells[iRow, k++] = fttypay;
                    xlWorkSheet.Cells[iRow, k++] = ffacpay + ffacnppay;
                    xlWorkSheet.Cells[iRow, k++] = ffacbpay;
                    xlWorkSheet.Cells[iRow, k++] = fxolpay;

                    ln_tpay     = ln_tpay + fpay;
		            ln_tretnpay	= ln_tretnpay + fretnpay;
		            ln_tttypay	= ln_tttypay + fttypay;
		            ln_tfacpay	= ln_tfacpay + ffacpay + ffacnppay;
		            ln_tfacbpay	= ln_tfacbpay + ffacbpay;
                    ln_txolpay = ln_txolpay + fxolpay;

                    iRow++;
                }

                xlWorkSheet.get_Range(String.Format("A{0}",iRow), String.Format("A{0}",iRow)).HorizontalAlignment = Excel.XlHAlign.xlHAlignRight;
                xlWorkSheet.get_Range(String.Format("B{0}",iRow), String.Format("G{0}",iRow)).Borders[Excel.XlBordersIndex.xlEdgeTop].LineStyle = 7;

                k = 1;

                xlWorkSheet.Cells[iRow, k++] = "Total:";
                xlWorkSheet.Cells[iRow, k++] = ln_tpay;
                xlWorkSheet.Cells[iRow, k++] = ln_tretnpay;
                xlWorkSheet.Cells[iRow, k++] = ln_tttypay;
                xlWorkSheet.Cells[iRow, k++] = ln_tfacpay;
                xlWorkSheet.Cells[iRow, k++] = ln_tfacbpay;
                xlWorkSheet.Cells[iRow, k++] = ln_txolpay;
		
                xlWorkSheet.get_Range(String.Format("B{0}",iRow), String.Format("G{0}",iRow)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 9;
            }

            if (sheetIndex == 3)
            {
                decimal ln_tpay = 0, ln_tpay1 = 0, ln_tpay2 = 0, ln_tpay7 = 0, ln_tpay3 = 0,
                    ln_tpay8 = 0, ln_tpay4 = 0;

                foreach (DataRow dr in dt.Rows)
                {
                    if (dr["ftype"].ToString().Trim() != "03")
                        continue;

                    decimal fpay = Convert.ToDecimal(dr["fpay"]);
                    decimal fpay1 = Convert.ToDecimal(dr["fpay1"]);
                    decimal fpay2 = Convert.ToDecimal(dr["fpay2"]);
                    decimal fpay7 = Convert.ToDecimal(dr["fpay7"]);
                    decimal fpay3 = Convert.ToDecimal(dr["fpay3"]);
                    decimal fpay8 = Convert.ToDecimal(dr["fpay8"]);
                    decimal fpay4 = Convert.ToDecimal(dr["fpay4"]);

                    k = 1;

                    xlWorkSheet.Cells[iRow, k++] = dr["fclsname"];
                    xlWorkSheet.Cells[iRow, k++] = fpay;
                    xlWorkSheet.Cells[iRow, k++] = fpay1;
                    xlWorkSheet.Cells[iRow, k++] = fpay2;
                    xlWorkSheet.Cells[iRow, k++] = fpay7;
                    xlWorkSheet.Cells[iRow, k++] = fpay3;
                    xlWorkSheet.Cells[iRow, k++] = fpay8;
                    xlWorkSheet.Cells[iRow, k++] = fpay4;

                    ln_tpay = ln_tpay + fpay;
                    ln_tpay1 = ln_tpay1 + fpay1;
                    ln_tpay2 = ln_tpay2 + fpay2;
                    ln_tpay7 = ln_tpay7 + fpay7;
                    ln_tpay3 = ln_tpay3 + fpay3;
                    ln_tpay8 = ln_tpay8 + fpay8;
                    ln_tpay4 = ln_tpay4 + fpay4;

                    iRow++;
                }

                xlWorkSheet.get_Range(String.Format("A{0}", iRow), String.Format("A{0}", iRow)).HorizontalAlignment = Excel.XlHAlign.xlHAlignRight;
                xlWorkSheet.get_Range(String.Format("B{0}", iRow), String.Format("H{0}", iRow)).Borders[Excel.XlBordersIndex.xlEdgeTop].LineStyle = 7;

                k = 1;

                xlWorkSheet.Cells[iRow, k++] = "Total:";
                xlWorkSheet.Cells[iRow, k++] = ln_tpay;
                xlWorkSheet.Cells[iRow, k++] = ln_tpay1;
                xlWorkSheet.Cells[iRow, k++] = ln_tpay2;
                xlWorkSheet.Cells[iRow, k++] = ln_tpay7;
                xlWorkSheet.Cells[iRow, k++] = ln_tpay3;
                xlWorkSheet.Cells[iRow, k++] = ln_tpay8;
                xlWorkSheet.Cells[iRow, k++] = ln_tpay4;

                xlWorkSheet.get_Range(String.Format("B{0}", iRow), String.Format("H{0}", iRow)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 9;
            }

            return iRow;
        }

        public static Int16 WsColTitleMveSum(Excel.Worksheet xlWorkSheet, DataTable dt, string optid,
            string z_company, string z_title, string z_currency, string z_criterion, string z_previous,
            string z_current)
        {
            xlWorkSheet.Cells.Font.Name = "arial";
            xlWorkSheet.Cells.Font.Size = 8;

            xlWorkSheet.Columns["A"].ColumnWidth = 30;
            xlWorkSheet.Columns["B"].ColumnWidth = 5;
            xlWorkSheet.Columns["C"].ColumnWidth = 12;
            xlWorkSheet.Columns["D"].ColumnWidth = 5;
            xlWorkSheet.Columns["E"].ColumnWidth = 12;
            xlWorkSheet.Columns["F:G"].ColumnWidth = 5;
            xlWorkSheet.Columns["H:J"].ColumnWidth = 12;
            xlWorkSheet.Columns["K:L"].ColumnWidth = 5;
            xlWorkSheet.Columns["M"].ColumnWidth = 12;
            xlWorkSheet.Columns["N"].ColumnWidth = 5;
            xlWorkSheet.Columns["O:P"].ColumnWidth = 12;

            xlWorkSheet.get_Range("O3", "O3").HorizontalAlignment = 4;
            xlWorkSheet.get_Range("P3", "P3").HorizontalAlignment = 2;

            xlWorkSheet.get_Range("C6:P8").HorizontalAlignment = 3;
            xlWorkSheet.get_Range("A8:P8").Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 7;

            xlWorkSheet.Columns["C:C"].NumberFormat = "#,##0.00_)";
            xlWorkSheet.Columns["D:D"].NumberFormat = "#,##0_)";
            xlWorkSheet.Columns["E:E"].NumberFormat = "#,##0.00_)";
            xlWorkSheet.Columns["F:G"].NumberFormat = "#,##0_)";
            xlWorkSheet.Columns["H:J"].NumberFormat = "#,##0.00_)";
            xlWorkSheet.Columns["K:L"].NumberFormat = "#,##0_)";
            xlWorkSheet.Columns["M:M"].NumberFormat = "#,##0.00_)";
            xlWorkSheet.Columns["N:N"].NumberFormat = "#,##0_)";
            xlWorkSheet.Columns["O:P"].NumberFormat = "#,##0.00_)";

            xlWorkSheet.get_Range("P3", "P3").NumberFormat = "yyyy/mm/dd";


            Int16 i = 1, k = 1;

            xlWorkSheet.Cells[i++, 1] = z_company;
            xlWorkSheet.Cells[i++, 1] = z_title;
            xlWorkSheet.Cells[i, 1] = z_criterion;
            xlWorkSheet.Cells[i, 15] = "Date: ";
            xlWorkSheet.Cells[i++, 16] = DateTime.Now;
            xlWorkSheet.Cells[i, 1] = z_currency;

            i += 2;

            if (dt.Rows.Count == 0)
            {
                xlWorkSheet.Cells[i, 1] = "No record found!";
                xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                return i;
            }

            xlWorkSheet.Cells[i, 3] = "O/S as at";
            xlWorkSheet.Cells[i, 5] = "Registered";
            xlWorkSheet.Cells[i, 8] = "Claim";
            xlWorkSheet.Cells[i, 13] = "O/S as at";

            i++;

            xlWorkSheet.Cells[i, 3] = z_previous;
            xlWorkSheet.Cells[i, 5] = "Claim";
            xlWorkSheet.Cells[i, 8] = "Adjustment";
            xlWorkSheet.Cells[i, 9] = "Claim Paid";
            xlWorkSheet.Cells[i, 10] = "Claim Paid";
            xlWorkSheet.Cells[i, 12] = "Closed";
            xlWorkSheet.Cells[i, 13] = z_current;
            xlWorkSheet.Cells[i, 15] = "Recovery";
            xlWorkSheet.Cells[i, 16] = "Loss Incurred";

            i++;

            xlWorkSheet.Cells[i, 1] = "Class";
            xlWorkSheet.Cells[i, 3] = "A";
            xlWorkSheet.Cells[i, 4] = "No.";
            xlWorkSheet.Cells[i, 5] = "B";
            xlWorkSheet.Cells[i, 6] = "No.";
            xlWorkSheet.Cells[i, 7] = "ReOpn";
            xlWorkSheet.Cells[i, 8] = "C";
            xlWorkSheet.Cells[i, 9] = "D";
            xlWorkSheet.Cells[i, 10] = "YTD";
            xlWorkSheet.Cells[i, 11] = "Trnsfer";
            xlWorkSheet.Cells[i, 12] = "Cases";
            xlWorkSheet.Cells[i, 13] = "E=A+B+C-D";
            xlWorkSheet.Cells[i, 14] = "No.";
            xlWorkSheet.Cells[i, 15] = "F";
            xlWorkSheet.Cells[i, 16] = "G=E-A+D+F";

            i = WsClmMveSum(xlWorkSheet, dt, optid, i += 2);

            return i;
        }

        public static Int16 WsClmMveSum(Excel.Worksheet xlWorkSheet, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            decimal ln_yosfr = 0, ln_yretnosfr = 0, ln_voscntfr = 0, ln_mres = 0, ln_mretnres = 0,
                ln_vnewcnt = 0, ln_vropen = 0, ln_madj = 0, ln_mretnadj = 0, ln_mpay = 0, ln_mretnpay = 0,
                ln_ypay = 0, ln_yretnpay = 0, ln_atrnsfer = 0, ln_vcntclsed = 0, ln_yosto = 0, ln_yretnosto = 0,
                ln_voscntto = 0, ln_mrvy = 0, ln_mretnrvy = 0, ln_mlos = 0, ln_mretnlos = 0;


            foreach (DataRow dr in dt.Rows)
            {
                decimal yosfr = Convert.ToDecimal(dr["yosfr"]);
                decimal yretnosfr = Convert.ToDecimal(dr["yretnosfr"]);
                decimal voscntfr = Convert.ToDecimal(dr["voscntfr"]);
                decimal vropen = Convert.ToDecimal(dr["vropen"]);
                decimal mres = Convert.ToDecimal(dr["mres"]);
                decimal mretnres = Convert.ToDecimal(dr["mretnres"]);
                decimal vnewcnt = Convert.ToDecimal(dr["vnewcnt"]);
                decimal madj = Convert.ToDecimal(dr["madj"]);
                decimal mretnadj = Convert.ToDecimal(dr["mretnadj"]);
                decimal mpay = Convert.ToDecimal(dr["mpay"]);
                decimal mretnpay = Convert.ToDecimal(dr["mretnpay"]);
                decimal ypay = Convert.ToDecimal(dr["ypay"]);
                decimal yretnpay = Convert.ToDecimal(dr["yretnpay"]);
                decimal atrnsfer = Convert.ToDecimal(dr["atrnsfer"]);
                decimal vcntclsed = Convert.ToDecimal(dr["vcntclsed"]);
                decimal yosto = Convert.ToDecimal(dr["yosto"]);
                decimal yretnosto = Convert.ToDecimal(dr["yretnosto"]);
                decimal voscntto = Convert.ToDecimal(dr["voscntto"]);
                decimal mrvy = Convert.ToDecimal(dr["mrvy"]);
                decimal mretnrvy = Convert.ToDecimal(dr["mretnrvy"]);
                decimal mlos = Convert.ToDecimal(dr["mlos"]);
                decimal mretnlos = Convert.ToDecimal(dr["mretnlos"]);

                k = 1;

                xlWorkSheet.Cells[iRow, k++] = dr["fclsname"];
                xlWorkSheet.Cells[iRow, k] = "G";
                xlWorkSheet.Cells[iRow + 1, k++] = "N";
                xlWorkSheet.Cells[iRow, k] = yosfr;
                xlWorkSheet.Cells[iRow + 1, k++] = yretnosfr;
                xlWorkSheet.Cells[iRow, k++] = voscntfr;
                xlWorkSheet.Cells[iRow, k] = mres;
                xlWorkSheet.Cells[iRow + 1, k++] = mretnres;
                xlWorkSheet.Cells[iRow, k++] = vnewcnt;
                xlWorkSheet.Cells[iRow, k++] = vropen;
                xlWorkSheet.Cells[iRow, k] = madj;
                xlWorkSheet.Cells[iRow + 1, k++] = mretnadj;
                xlWorkSheet.Cells[iRow, k] = mpay;
                xlWorkSheet.Cells[iRow + 1, k++] = mretnpay;
                xlWorkSheet.Cells[iRow, k] = ypay;
                xlWorkSheet.Cells[iRow + 1, k++] = yretnpay;
                xlWorkSheet.Cells[iRow, k++] = atrnsfer;
                xlWorkSheet.Cells[iRow, k++] = vcntclsed;
                xlWorkSheet.Cells[iRow, k] = yosto;
                xlWorkSheet.Cells[iRow + 1, k++] = yretnosto;
                xlWorkSheet.Cells[iRow, k++] = voscntto;
                xlWorkSheet.Cells[iRow, k] = mrvy;
                xlWorkSheet.Cells[iRow + 1, k++] = mretnrvy;
                xlWorkSheet.Cells[iRow, k] = mlos;
                xlWorkSheet.Cells[iRow + 1, k] = mretnlos;

                ln_yosfr = ln_yosfr + yosfr;
                ln_yretnosfr = ln_yretnosfr + yretnosfr;
                ln_voscntfr = ln_voscntfr + voscntfr;
                ln_vropen = ln_vropen + vropen;
                ln_mres = ln_mres + mres;
                ln_mretnres = ln_mretnres + mretnres;
                ln_vnewcnt = ln_vnewcnt + vnewcnt;
                ln_madj = ln_madj + madj;
                ln_mretnadj = ln_mretnadj + mretnadj;
                ln_mpay = ln_mpay + mpay;
                ln_mretnpay = ln_mretnpay + mretnpay;
                ln_ypay = ln_ypay + ypay;
                ln_yretnpay = ln_yretnpay + yretnpay;
                ln_atrnsfer = ln_atrnsfer + atrnsfer;
                ln_vcntclsed = ln_vcntclsed + vcntclsed;
                ln_yosto = ln_yosto + yosto;
                ln_yretnosto = ln_yretnosto + yretnosto;
                ln_voscntto = ln_voscntto + voscntto;
                ln_mrvy = ln_mrvy + mrvy;
                ln_mretnrvy = ln_mretnrvy + mretnrvy;
                ln_mlos = ln_mlos + mlos;
                ln_mretnlos = ln_mretnlos + mretnlos;

                iRow += 2;
            }

            xlWorkSheet.get_Range(String.Format("A{0}", iRow), String.Format("P{0}", iRow)).Borders[Excel.XlBordersIndex.xlEdgeTop].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("A{0}", iRow + 1), String.Format("P{0}", iRow + 1)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 9;

            k = 1;

            xlWorkSheet.Cells[iRow, k++] = "Grand Total: ";
            xlWorkSheet.Cells[iRow, k] = "G";
            xlWorkSheet.Cells[iRow + 1, k++] = "N";
            xlWorkSheet.Cells[iRow, k] = ln_yosfr;
            xlWorkSheet.Cells[iRow + 1, k++] = ln_yretnosfr;
            xlWorkSheet.Cells[iRow, k++] = ln_voscntfr;
            xlWorkSheet.Cells[iRow, k] = ln_mres;
            xlWorkSheet.Cells[iRow + 1, k++] = ln_mretnres;
            xlWorkSheet.Cells[iRow, k++] = ln_vnewcnt;
            xlWorkSheet.Cells[iRow, k++] = ln_vropen;
            xlWorkSheet.Cells[iRow, k] = ln_madj;
            xlWorkSheet.Cells[iRow + 1, k++] = ln_mretnadj;
            xlWorkSheet.Cells[iRow, k] = ln_mpay;
            xlWorkSheet.Cells[iRow + 1, k++] = ln_mretnpay;
            xlWorkSheet.Cells[iRow, k] = ln_ypay;
            xlWorkSheet.Cells[iRow + 1, k++] = ln_yretnpay;
            xlWorkSheet.Cells[iRow, k++] = ln_atrnsfer;
            xlWorkSheet.Cells[iRow, k++] = ln_vcntclsed;
            xlWorkSheet.Cells[iRow, k] = ln_yosto;
            xlWorkSheet.Cells[iRow + 1, k++] = ln_yretnosto;
            xlWorkSheet.Cells[iRow, k++] = ln_voscntto;
            xlWorkSheet.Cells[iRow, k] = ln_mrvy;
            xlWorkSheet.Cells[iRow + 1, k++] = ln_mretnrvy;
            xlWorkSheet.Cells[iRow, k] = ln_mlos;
            xlWorkSheet.Cells[iRow + 1, k] = ln_mretnlos;

            xlWorkSheet.get_Range(String.Format("O{0}", iRow), String.Format("O{0}", iRow)).HorizontalAlignment = 4;

            return iRow;
        }

        public static Int16 WsColTitlePMPMveSum(Excel.Worksheet xlWorkSheet, DataTable dt, string optid,
            string z_company, string z_title, string z_currency, string z_previous,
            string z_current)
        {
            xlWorkSheet.Cells.Font.Name = "arial";
            xlWorkSheet.Cells.Font.Size = 8;

            xlWorkSheet.Columns["A"].ColumnWidth = 8;
            xlWorkSheet.Columns["B"].ColumnWidth = 17;
            xlWorkSheet.Columns["C"].ColumnWidth = 15;
            xlWorkSheet.Columns["D:I"].ColumnWidth = 12;

            xlWorkSheet.Columns["D:I"].NumberFormat = "#,##0.00_)";

            xlWorkSheet.get_Range("H3", "H3").HorizontalAlignment = Excel.XlHAlign.xlHAlignRight;
            xlWorkSheet.get_Range("I3", "I3").HorizontalAlignment = Excel.XlHAlign.xlHAlignLeft;
            xlWorkSheet.get_Range("I3", "I3").NumberFormat = "yyyy/mm/dd";

            xlWorkSheet.get_Range("D5", "D5").Font.Bold = true;
            xlWorkSheet.get_Range("G5", "G5").Font.Bold = true;

		    xlWorkSheet.get_Range("D6:I8").HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;
            xlWorkSheet.get_Range("A8:I8").Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 7;

            xlWorkSheet.get_Range("D6:F6").Merge();
            xlWorkSheet.get_Range("G6:I6").Merge();

            Int16 i = 1, k;

            xlWorkSheet.Cells[i++, 1] = z_company;
            xlWorkSheet.Cells[i++, 1] = z_title;
            xlWorkSheet.Cells[i, 1] = z_currency;
            xlWorkSheet.Cells[i, 8] = "Date: ";
            xlWorkSheet.Cells[i++, 9] = DateTime.Now;

            i += 2;

            if (dt.Rows.Count == 0)
            {
                xlWorkSheet.Cells[i, 1] = "No record found!";
                xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                return i;
            }

            xlWorkSheet.Cells[i,4] = "<-------------------- Injury -------------------->";
            xlWorkSheet.Cells[i++,7] = "<----------------- Non Injury ------------------>";
            xlWorkSheet.Cells[i,4] = "O/S as at";
            xlWorkSheet.Cells[i++,7] = "O/S as at";

            k = 1;

            xlWorkSheet.Cells[i,k++] = "Yr of Loss";
            xlWorkSheet.Cells[i,k++] = "Class of Vehicles";
            xlWorkSheet.Cells[i,k++] = "Coverage";
            xlWorkSheet.Cells[i,k++] = z_previous;
            xlWorkSheet.Cells[i,k++] = "Claim Paid";
            xlWorkSheet.Cells[i,k++] = "Recovery";
            xlWorkSheet.Cells[i,k++] = z_current;
            xlWorkSheet.Cells[i,k++] = "Claim Paid";
            xlWorkSheet.Cells[i, k++] = "Recovery";
            
            i = WsClmPMPMveSum(xlWorkSheet, dt, optid, i += 2);

            return i;
        }

        public static Int16 WsClmPMPMveSum(Excel.Worksheet xlWorkSheet, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            decimal ln_yostoa = 0, ln_mpaya = 0, ln_mrvya = 0, ln_yostob = 0, ln_mpayb = 0, ln_mrvyb = 0;

            foreach (DataRow dr in dt.Rows)
            {
                decimal yostoa = Convert.ToDecimal(dr["yostoa"]);
                decimal mpaya = Convert.ToDecimal(dr["mpaya"]);
                decimal mrvya = Convert.ToDecimal(dr["mrvya"]);
                decimal yostob = Convert.ToDecimal(dr["yostob"]);
                decimal mpayb = Convert.ToDecimal(dr["mpayb"]);
                decimal mrvyb = Convert.ToDecimal(dr["mrvyb"]);

                k = 1;

                xlWorkSheet.Cells[iRow,k++] = "'" + dr["flosyr"].ToString().Trim();
                xlWorkSheet.Cells[iRow,k++] = dr["fclsname"];
                xlWorkSheet.Cells[iRow,k++] = dr["fcvrname"];
                xlWorkSheet.Cells[iRow,k++] = yostoa;
                xlWorkSheet.Cells[iRow,k++] = mpaya;
                xlWorkSheet.Cells[iRow,k++] = mrvya;
                xlWorkSheet.Cells[iRow,k++] = yostob;
                xlWorkSheet.Cells[iRow,k++] = mpayb;
                xlWorkSheet.Cells[iRow, k++] = mrvyb;

                ln_yostoa = ln_yostoa + yostoa;
                ln_mpaya  = ln_mpaya + mpaya;
                ln_mrvya  = ln_mrvya + mrvya;
                ln_yostob = ln_yostob + yostob;
                ln_mpayb  = ln_mpayb + mpayb;
                ln_mrvyb = ln_mrvyb + mrvyb;

                iRow ++;
            }

            xlWorkSheet.get_Range(String.Format("A{0}", iRow), String.Format("I{0}", iRow)).Borders[Excel.XlBordersIndex.xlEdgeTop].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("A{0}", iRow), String.Format("I{0}", iRow)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 9;

            k = 3;

            xlWorkSheet.Cells[iRow, k++] = "Grand Total: ";
            xlWorkSheet.Cells[iRow, k++] = ln_yostoa;
            xlWorkSheet.Cells[iRow, k++] = ln_mpaya;
            xlWorkSheet.Cells[iRow, k++] = ln_mrvya;
            xlWorkSheet.Cells[iRow, k++] = ln_yostob;
            xlWorkSheet.Cells[iRow, k++] = ln_mpayb;
            xlWorkSheet.Cells[iRow, k++] = ln_mrvyb;

            xlWorkSheet.get_Range(String.Format("C{0}", iRow), String.Format("C{0}", iRow)).HorizontalAlignment = Excel.XlHAlign.xlHAlignRight;

            return iRow;
        }

        public static void WbClmECAdjBd(Excel.Workbook xlWorkBook, DataSet ds, string optid, string z_company,
                    string z_title, string z_currency, string z_criterion, string z_prnsum)
        {
            for (int k = 1; k <= 2; k++)
            {
                int i, tableIndex= 0;
                Excel.Worksheet xlWorkSheet = null;

                if (k == 1 || z_prnsum == "Y")
                    xlWorkSheet = xlWorkBook.Worksheets.get_Item(k);

                //tableIndex = 0;

                //if (k == 2)
                //    tableIndex = 1;

                tableIndex = k - 1;
 
                if (k == 1)
                {
                    xlWorkSheet.Name = "Details";
                }

                if (k == 2 && z_prnsum == "Y")
                {
                    xlWorkSheet.Name = "Summary";
                }

                i = WsColTitleECAdjBd(xlWorkSheet, ds.Tables[tableIndex], k, optid, z_company, z_title,
                        z_currency, z_criterion, z_prnsum);
            }

            return;
        }

        public static Int16 WsColTitleECAdjBd(Excel.Worksheet xlWorkSheet, DataTable dt, int sheetIndex,
                        string optid, string z_company, string z_title, string z_currency, 
                        string z_criterion, string z_prnsum)
        {
            Int16 i = 1;

            if (sheetIndex == 1)
            {
                xlWorkSheet.Cells.Font.Name = "arial";
                xlWorkSheet.Cells.Font.Size = 8;

                xlWorkSheet.Columns["A"].ColumnWidth = 11;
                xlWorkSheet.Columns["B"].ColumnWidth = 9;
                xlWorkSheet.Columns["C"].ColumnWidth = 38;
                xlWorkSheet.Columns["D"].ColumnWidth = 15;
                xlWorkSheet.Columns["E:O"].ColumnWidth = 14;

                xlWorkSheet.Columns["B"].HorizontalAlignment = Excel.XlHAlign.xlHAlignLeft;
                xlWorkSheet.Columns["E:O"].HorizontalAlignment = Excel.XlHAlign.xlHAlignRight;
                xlWorkSheet.Columns["B"].NumberFormat = "yyyy/mm/dd";
                xlWorkSheet.Columns["E:O"].NumberFormat = "#,##0.00_)";

                xlWorkSheet.get_Range("N3", "N3").HorizontalAlignment = Excel.XlHAlign.xlHAlignRight;
                xlWorkSheet.get_Range("O3", "O3").HorizontalAlignment = Excel.XlHAlign.xlHAlignLeft;
                xlWorkSheet.get_Range("O3", "O3").NumberFormat = "yyyy/mm/dd";
                xlWorkSheet.get_Range("A7", "O7").Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 7;

                xlWorkSheet.get_Range("E5:H5").Merge();
                xlWorkSheet.get_Range("I5:L5").Merge();
                xlWorkSheet.get_Range("N5:O5").Merge();

                xlWorkSheet.get_Range("E5", "O7").HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter;

                xlWorkSheet.Cells[i++, 1] = z_company;
                xlWorkSheet.Cells[i++, 1] = z_title;
                xlWorkSheet.Cells[i, 1] = z_currency;
                xlWorkSheet.Cells[i, 14] = "Date: ";
                xlWorkSheet.Cells[i++, 15] = DateTime.Now;
                xlWorkSheet.Cells[i++, 1] = z_criterion;

                if (dt.Rows.Count == 0)
                {
                    i++;
                    xlWorkSheet.Cells[i, 1] = "No record found!";
                    xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;
                    return i;
                }
            
                xlWorkSheet.Cells[i,5]  = "<-------------------------- EC Claim -------------------------->";
                xlWorkSheet.Cells[i,9]  = "<------------------- Common Law Claim ------------------->";
                xlWorkSheet.Cells[i,14] = "<-------- Current Status -------->";
                xlWorkSheet.Cells[i+2,1] = "Claim No";
                xlWorkSheet.Cells[i+2,2] = "Date of Loss";
                xlWorkSheet.Cells[i + 2, 3] = "Insured / Loss Location / Particulars";

                xlWorkSheet.Cells[i+2,5] = "Damages (1)";
                xlWorkSheet.Cells[i+1,6] = "Claimant's";
                xlWorkSheet.Cells[i+2,6] = "Costs (2)";
                xlWorkSheet.Cells[i+1,7] = "Own Costs/";
                xlWorkSheet.Cells[i+2,7] = "Expense (3)";
                xlWorkSheet.Cells[i+1,8] = "Total";
                xlWorkSheet.Cells[i+2, 8] = "'(a) = (1) + (2) + (3)";
                xlWorkSheet.Cells[i+2, 9] = "Damages (4)";
                xlWorkSheet.Cells[i+1,10] = "Claimant's";
                xlWorkSheet.Cells[i+2,10] = "Costs (5)";
                xlWorkSheet.Cells[i+1,11] = "Own Costs/";
                xlWorkSheet.Cells[i+2,11] = "Expense (6)";
                xlWorkSheet.Cells[i+1, 12] = "Total";
                xlWorkSheet.Cells[i+2,12] = "'(b) = (4) + (5) + (6)";
                xlWorkSheet.Cells[i+1,13] = "Adjustment Total";
                xlWorkSheet.Cells[i+2, 13] = "During Period";
                xlWorkSheet.Cells[i+2, 15] = "Total";

                i += 4;

                i = WsClmECAdjBd(xlWorkSheet, sheetIndex, dt, optid, i);
            }

            if (sheetIndex == 2 && z_prnsum == "Y")
            {
                xlWorkSheet.Cells.Font.Name = "arial";
                xlWorkSheet.Cells.Font.Size = 10;

                xlWorkSheet.Columns["A"].ColumnWidth = 11;
                xlWorkSheet.Columns["H:I"].ColumnWidth = 14;

                xlWorkSheet.Columns["H:I"].HorizontalAlignment = Excel.XlHAlign.xlHAlignRight;
                xlWorkSheet.Columns["H:I"].NumberFormat = "#,##0.00_)";

                xlWorkSheet.get_Range("H3", "H3").HorizontalAlignment = Excel.XlHAlign.xlHAlignRight;
                xlWorkSheet.get_Range("I3", "I3").HorizontalAlignment = Excel.XlHAlign.xlHAlignLeft;
                xlWorkSheet.get_Range("I3", "I3").NumberFormat = "yyyy/mm/dd";
                //xlWorkSheet.get_Range("A7", "O7").Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 7;

                xlWorkSheet.Cells[i++, 1] = z_company;
                xlWorkSheet.Cells[i++, 1] = z_title;
                xlWorkSheet.Cells[i, 1] = z_currency;
                xlWorkSheet.Cells[i, 8] = "Date: ";
                xlWorkSheet.Cells[i++, 9] = DateTime.Now;
                xlWorkSheet.Cells[i++, 1] = z_criterion;

                if (dt.Rows.Count == 0)
                {
                    i++;
                    xlWorkSheet.Cells[i, 1] = "No record found!";
                    xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;
                    return i;
                }

                i += 2;
                xlWorkSheet.Cells[i++, 1] = "Summary by Year of Loss";

                i++;
                xlWorkSheet.Cells[i, 1] = "Year";
                xlWorkSheet.Cells[i, 8] = "MTD Adj";
                xlWorkSheet.Cells[i, 9] = "YTD Adj";
                xlWorkSheet.get_Range(String.Format("A{0}", i), String.Format("I{0}", i))
                    .Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 7;

                i +=2;

                i = WsClmECAdjBd(xlWorkSheet, sheetIndex, dt, optid, i);
            }

            return i;
        }

        public static Int16 WsClmECAdjBd(Excel.Worksheet xlWorkSheet, int sheetIndex, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fclsseq = "";

            if (sheetIndex == 1)
            {
                decimal ln_saeadja = 0, ln_saeadjb = 0, ln_saeadjc = 0, ln_saeadj = 0;
                decimal ln_sacadja = 0, ln_sacadjb = 0, ln_sacadjc = 0, ln_sacadj = 0;
                decimal ln_syeadja = 0, ln_syeadjb = 0, ln_syeadjc = 0, ln_syeadj = 0;
                decimal ln_sycadja = 0, ln_sycadjb = 0, ln_sycadjc = 0, ln_sycadj = 0;
                decimal ln_syepay = 0, ln_sycpay = 0;
                decimal ln_smeadja = 0, ln_smeadjb = 0, ln_smeadjc = 0, ln_smeadj = 0;
                decimal ln_smcadja = 0, ln_smcadjb = 0, ln_smcadjc = 0, ln_smcadj = 0;

                decimal ln_taeadja = 0, ln_taeadjb = 0, ln_taeadjc = 0, ln_taeadj = 0;
                decimal ln_tacadja = 0, ln_tacadjb = 0, ln_tacadjc = 0, ln_tacadj = 0;
                decimal ln_tyeadja = 0, ln_tyeadjb = 0, ln_tyeadjc = 0, ln_tyeadj = 0;
                decimal ln_tycadja = 0, ln_tycadjb = 0, ln_tycadjc = 0, ln_tycadj = 0;
                decimal ln_tyepay = 0, ln_tycpay = 0;
                decimal ln_tmeadja = 0, ln_tmeadjb = 0, ln_tmeadjc = 0, ln_tmeadj = 0;
                decimal ln_tmcadja = 0, ln_tmcadjb = 0, ln_tmcadjc = 0, ln_tmcadj = 0;
                                
                decimal ln_scase = 0, ln_tcase = 0;

                foreach (DataRow dr in dt.Rows)
                {
                    if (lc_fclsseq != dr["fclsseq"].ToString().Trim())
                    {
                        if (iRow > startRow)
                        {
                            xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");

                            k = 3;
                            xlWorkSheet.Cells[iRow, k++] = "Total:";
                            xlWorkSheet.Cells[iRow, k++] = "Last Reserve";
                            xlWorkSheet.Cells[iRow,k++] = ln_saeadja;
                            xlWorkSheet.Cells[iRow,k++] = ln_saeadjb;
                            xlWorkSheet.Cells[iRow,k++] = ln_saeadjc;
                            xlWorkSheet.Cells[iRow,k++] = ln_saeadj;
                            xlWorkSheet.Cells[iRow,k++] = ln_sacadja;
                            xlWorkSheet.Cells[iRow,k++] = ln_sacadjb;
                            xlWorkSheet.Cells[iRow,k++] = ln_sacadjc;
                            xlWorkSheet.Cells[iRow,k++] = ln_sacadj;

                            k++;
                            xlWorkSheet.Cells[iRow,k++] = "Prov";
                            xlWorkSheet.Cells[iRow,k++] = ln_syeadj+ln_sycadj;

                            k = 4;
                            xlWorkSheet.Cells[iRow+1,k++] = "Revised Reserve";
                            xlWorkSheet.Cells[iRow+1,k++] = ln_syeadja;
                            xlWorkSheet.Cells[iRow+1,k++] = ln_syeadjb;
                            xlWorkSheet.Cells[iRow+1,k++] = ln_syeadjc;
                            xlWorkSheet.Cells[iRow+1,k++] = ln_syeadj;
                            xlWorkSheet.Cells[iRow+1,k++] = ln_sycadja;
                            xlWorkSheet.Cells[iRow+1,k++] = ln_sycadjb;
                            xlWorkSheet.Cells[iRow+1,k++] = ln_sycadjc;
                            xlWorkSheet.Cells[iRow+1,k++] = ln_sycadj;

                            k++;
                            xlWorkSheet.Cells[iRow+1,k++] = "Paid";
                            xlWorkSheet.Cells[iRow+1,k++] = ln_syepay+ln_sycpay;

                            k = 4;
                            xlWorkSheet.Cells[iRow+2,k++] = "Adjustment";
                            xlWorkSheet.Cells[iRow+2,k++] = ln_smeadja;
                            xlWorkSheet.Cells[iRow+2,k++] = ln_smeadjb;
                            xlWorkSheet.Cells[iRow+2,k++] = ln_smeadjc;
                            xlWorkSheet.Cells[iRow+2,k++] = ln_smeadj;
                            xlWorkSheet.Cells[iRow+2,k++] = ln_smcadja;
                            xlWorkSheet.Cells[iRow+2,k++] = ln_smcadjb;
                            xlWorkSheet.Cells[iRow+2,k++] = ln_smcadjc;
                            xlWorkSheet.Cells[iRow+2,k++] = ln_smcadj;
                            xlWorkSheet.Cells[iRow+2,k++] = ln_smeadj+ln_smcadj;
                            xlWorkSheet.Cells[iRow+2,k++] = "O/S";
                            xlWorkSheet.Cells[iRow+2, k++] = ln_syeadj + ln_sycadj - ln_syepay - ln_sycpay;
                            xlWorkSheet.get_Range(String.Format("C{0}", iRow)).Columns.HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4
                            xlWorkSheet.get_Range(String.Format("N{0}", iRow),String.Format("N{0}", iRow+2)).Columns.HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4

                            iRow += 4;
                        }

                        lc_fclsseq = dr["fclsseq"].ToString().Trim();
                        xlWorkSheet.Cells[iRow, 1] = dr["fclsname"].ToString();

                        xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                        ln_saeadja = 0; ln_saeadjb = 0; ln_saeadjc = 0; ln_saeadj = 0;
                        ln_sacadja = 0; ln_sacadjb = 0; ln_sacadjc = 0; ln_sacadj = 0;
                        ln_syeadja = 0; ln_syeadjb = 0; ln_syeadjc = 0; ln_syeadj = 0;
                        ln_sycadja = 0; ln_sycadjb = 0; ln_sycadjc = 0; ln_sycadj = 0;
                        ln_syepay = 0; ln_sycpay = 0;
                        ln_smeadja = 0; ln_smeadjb = 0; ln_smeadjc = 0; ln_smeadj = 0;
                        ln_smcadja = 0; ln_smcadjb = 0; ln_smcadjc = 0; ln_smcadj = 0;
                        ln_scase = 0;

                        iRow += 2;
                    }

                    decimal aeadja = Convert.ToDecimal(dr["aeadja"]);
                    decimal aeadjb = Convert.ToDecimal(dr["aeadjb"]);
                    decimal aeadjc = Convert.ToDecimal(dr["aeadjc"]);
                    decimal aeadj = Convert.ToDecimal(dr["aeadj"]);

                    decimal acadja = Convert.ToDecimal(dr["acadja"]);
                    decimal acadjb = Convert.ToDecimal(dr["acadjb"]);
                    decimal acadjc = Convert.ToDecimal(dr["acadjc"]);
                    decimal acadj = Convert.ToDecimal(dr["acadj"]);

                    decimal yeadj = Convert.ToDecimal(dr["yeadj"]);
                    decimal ycadj = Convert.ToDecimal(dr["ycadj"]);
                    decimal yeadja = Convert.ToDecimal(dr["yeadja"]);
                    decimal yeadjb = Convert.ToDecimal(dr["yeadjb"]);
                    decimal yeadjc = Convert.ToDecimal(dr["yeadjc"]);
                    decimal ycadja = Convert.ToDecimal(dr["ycadja"]);
                    decimal ycadjb = Convert.ToDecimal(dr["ycadjb"]);
                    decimal ycadjc = Convert.ToDecimal(dr["ycadjc"]);

                    decimal yepay = Convert.ToDecimal(dr["yepay"]);
                    decimal ycpay = Convert.ToDecimal(dr["ycpay"]);

                    decimal meadja = Convert.ToDecimal(dr["meadja"]);
                    decimal meadjb = Convert.ToDecimal(dr["meadjb"]);
                    decimal meadjc = Convert.ToDecimal(dr["meadjc"]);
                    decimal meadj = Convert.ToDecimal(dr["meadj"]);

                    decimal mcadja = Convert.ToDecimal(dr["mcadja"]);
                    decimal mcadjb = Convert.ToDecimal(dr["mcadjb"]);
                    decimal mcadjc = Convert.ToDecimal(dr["mcadjc"]);
                    decimal mcadj = Convert.ToDecimal(dr["mcadj"]);
                    decimal fage = Convert.ToDecimal(dr["fage"]);

                    k = 1;
                    xlWorkSheet.Cells[iRow, k++] = dr["fclmno"];
                    xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["flosdate"]);
                    xlWorkSheet.Cells[iRow, k++] = dr["finsd"].ToString().Trim() + ((char)13).ToString() +
                        ((char)10).ToString()+dr["flosloc"].ToString().Trim() + ((char)13).ToString() +
                        ((char)10).ToString() + dr["fpartr_t1"].ToString().TrimEnd() +
                        dr["fpartr_t2"].ToString().TrimEnd() +
                        (fage == 0m ? "" : " (Age: " + fage.ToString("#,#") + ")") + ((char)10).ToString(); ;

                    xlWorkSheet.Cells[iRow, k++] = "Last Reserve";
                    xlWorkSheet.Cells[iRow, k++] = aeadja;
                    xlWorkSheet.Cells[iRow, k++] = aeadjb;
                    xlWorkSheet.Cells[iRow, k++] = aeadjc;
                    xlWorkSheet.Cells[iRow, k++] = aeadj;
                    xlWorkSheet.Cells[iRow, k++] = acadja;
                    xlWorkSheet.Cells[iRow, k++] = acadjb;
                    xlWorkSheet.Cells[iRow, k++] = acadjc;
                    xlWorkSheet.Cells[iRow, k++] = acadj;
                    k++;
                    xlWorkSheet.Cells[iRow, k++] = "Prov";
                    xlWorkSheet.Cells[iRow, k++] = yeadj + ycadj;

                    k = 4;
                    xlWorkSheet.Cells[iRow+1, k++] = "Revised Reserve";
                    xlWorkSheet.Cells[iRow+1, k++] = yeadja;
                    xlWorkSheet.Cells[iRow+1, k++] = yeadjb;
                    xlWorkSheet.Cells[iRow+1, k++] = yeadjc;
                    xlWorkSheet.Cells[iRow+1, k++] = yeadj;
                    xlWorkSheet.Cells[iRow+1, k++] = ycadja;
                    xlWorkSheet.Cells[iRow+1, k++] = ycadjb;
                    xlWorkSheet.Cells[iRow+1, k++] = ycadjc;
                    xlWorkSheet.Cells[iRow+1, k++] = ycadj;
                    k++;
                    xlWorkSheet.Cells[iRow+1, k++] = "Paid";
                    xlWorkSheet.Cells[iRow+1, k++] = yepay + ycpay;

                    k = 4;
                    xlWorkSheet.Cells[iRow+2, k++] = "Adjustment";
                    xlWorkSheet.Cells[iRow+2, k++] = meadja;
                    xlWorkSheet.Cells[iRow+2, k++] = meadjb;
                    xlWorkSheet.Cells[iRow+2, k++] = meadjc;
                    xlWorkSheet.Cells[iRow+2, k++] = meadj;
                    xlWorkSheet.Cells[iRow+2, k++] = mcadja;
                    xlWorkSheet.Cells[iRow+2, k++] = mcadjb;
                    xlWorkSheet.Cells[iRow+2, k++] = mcadjc;
                    xlWorkSheet.Cells[iRow+2, k++] = mcadj;
                    xlWorkSheet.Cells[iRow+2, k++] = meadj + mcadj;
                    xlWorkSheet.Cells[iRow+2, k++] = "O/S";
                    xlWorkSheet.Cells[iRow+2, k++] = yeadj + ycadj - yepay - ycpay;

                    xlWorkSheet.get_Range(String.Format("C{0}", iRow), String.Format("C{0}", iRow + 2)).Merge();
                    xlWorkSheet.get_Range(String.Format("C{0}", iRow), String.Format("C{0}", iRow + 2)).HorizontalAlignment = 1;
                    xlWorkSheet.get_Range(String.Format("C{0}", iRow), String.Format("C{0}", iRow + 2)).VerticalAlignment = 1;
                    xlWorkSheet.get_Range(String.Format("C{0}", iRow)).RowHeight = 11.25;

                    xlWorkSheet.get_Range(String.Format("N{0}", iRow), String.Format("N{0}", iRow + 2)).Columns.HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4

                    ln_saeadja = ln_saeadja + aeadja;
                    ln_saeadjb = ln_saeadjb + aeadjb;
                    ln_saeadjc = ln_saeadjc + aeadjc;
                    ln_saeadj = ln_saeadj + aeadj;
                    ln_sacadja = ln_sacadja + acadja;
                    ln_sacadjb = ln_sacadjb + acadjb;
                    ln_sacadjc = ln_sacadjc + acadjc;
                    ln_sacadj = ln_sacadj + acadj;
                    ln_syeadj = ln_syeadj + yeadj;
                    ln_sycadj = ln_sycadj + ycadj;
                    ln_syeadja = ln_syeadja + yeadja;
                    ln_syeadjb = ln_syeadjb + yeadjb;
                    ln_syeadjc = ln_syeadjc + yeadjc;
                    ln_sycadja = ln_sycadja + ycadja;
                    ln_sycadjb = ln_sycadjb + ycadjb;
                    ln_sycadjc = ln_sycadjc + ycadjc;
                    ln_syepay = ln_syepay + yepay;
                    ln_sycpay = ln_sycpay + ycpay;
                    ln_smeadja = ln_smeadja + meadja;
                    ln_smeadjb = ln_smeadjb + meadjb;
                    ln_smeadjc = ln_smeadjc + meadjc;
                    ln_smeadj = ln_smeadj + meadj;
                    ln_smcadja = ln_smcadja + mcadja;
                    ln_smcadjb = ln_smcadjb + mcadjb;
                    ln_smcadjc = ln_smcadjc + mcadjc;
                    ln_smcadj = ln_smcadj + mcadj;
                    
                    ln_taeadja = ln_taeadja + aeadja;
                    ln_taeadjb = ln_taeadjb + aeadjb;
                    ln_taeadjc = ln_taeadjc + aeadjc;
                    ln_taeadj = ln_taeadj + aeadj;
                    ln_tacadja = ln_tacadja + acadja;
                    ln_tacadjb = ln_tacadjb + acadjb;
                    ln_tacadjc = ln_tacadjc + acadjc;
                    ln_tacadj = ln_tacadj + acadj;
                    ln_tyeadj = ln_tyeadj + yeadj;
                    ln_tycadj = ln_tycadj + ycadj;
                    ln_tyeadja = ln_tyeadja + yeadja;
                    ln_tyeadjb = ln_tyeadjb + yeadjb;
                    ln_tyeadjc = ln_tyeadjc + yeadjc;
                    ln_tycadja = ln_tycadja + ycadja;
                    ln_tycadjb = ln_tycadjb + ycadjb;
                    ln_tycadjc = ln_tycadjc + ycadjc;
                    ln_tyepay = ln_tyepay + yepay;
                    ln_tycpay = ln_tycpay + ycpay;
                    ln_tmeadja = ln_tmeadja + meadja;
                    ln_tmeadjb = ln_tmeadjb + meadjb;
                    ln_tmeadjc = ln_tmeadjc + meadjc;
                    ln_tmeadj = ln_tmeadj + meadj;
                    ln_tmcadja = ln_tmcadja + mcadja;
                    ln_tmcadjb = ln_tmcadjb + mcadjb;
                    ln_tmcadjc = ln_tmcadjc + mcadjc;
                    ln_tmcadj = ln_tmcadj + mcadj;

                    ln_scase++; ln_tcase++;

                    iRow += 4;
                }

                xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");

                k = 3;
                xlWorkSheet.Cells[iRow, k++] = "Total:";
                xlWorkSheet.Cells[iRow, k++] = "Last Reserve";
                xlWorkSheet.Cells[iRow, k++] = ln_saeadja;
                xlWorkSheet.Cells[iRow, k++] = ln_saeadjb;
                xlWorkSheet.Cells[iRow, k++] = ln_saeadjc;
                xlWorkSheet.Cells[iRow, k++] = ln_saeadj;
                xlWorkSheet.Cells[iRow, k++] = ln_sacadja;
                xlWorkSheet.Cells[iRow, k++] = ln_sacadjb;
                xlWorkSheet.Cells[iRow, k++] = ln_sacadjc;
                xlWorkSheet.Cells[iRow, k++] = ln_sacadj;

                k++;
                xlWorkSheet.Cells[iRow, k++] = "Prov";
                xlWorkSheet.Cells[iRow, k++] = ln_syeadj + ln_sycadj;

                k = 4;
                xlWorkSheet.Cells[iRow+1, k++] = "Revised Reserve";
                xlWorkSheet.Cells[iRow+1, k++] = ln_syeadja;
                xlWorkSheet.Cells[iRow+1, k++] = ln_syeadjb;
                xlWorkSheet.Cells[iRow+1, k++] = ln_syeadjc;
                xlWorkSheet.Cells[iRow+1, k++] = ln_syeadj;
                xlWorkSheet.Cells[iRow+1, k++] = ln_sycadja;
                xlWorkSheet.Cells[iRow+1, k++] = ln_sycadjb;
                xlWorkSheet.Cells[iRow+1, k++] = ln_sycadjc;
                xlWorkSheet.Cells[iRow+1, k++] = ln_sycadj;

                k++;
                xlWorkSheet.Cells[iRow+1, k++] = "Paid";
                xlWorkSheet.Cells[iRow+1, k++] = ln_syepay + ln_sycpay;

                k = 4;
                xlWorkSheet.Cells[iRow+2, k++] = "Adjustment";
                xlWorkSheet.Cells[iRow+2, k++] = ln_smeadja;
                xlWorkSheet.Cells[iRow+2, k++] = ln_smeadjb;
                xlWorkSheet.Cells[iRow+2, k++] = ln_smeadjc;
                xlWorkSheet.Cells[iRow+2, k++] = ln_smeadj;
                xlWorkSheet.Cells[iRow+2, k++] = ln_smcadja;
                xlWorkSheet.Cells[iRow+2, k++] = ln_smcadjb;
                xlWorkSheet.Cells[iRow+2, k++] = ln_smcadjc;
                xlWorkSheet.Cells[iRow+2, k++] = ln_smcadj;
                xlWorkSheet.Cells[iRow+2, k++] = ln_smeadj + ln_smcadj;
                xlWorkSheet.Cells[iRow+2, k++] = "O/S";
                xlWorkSheet.Cells[iRow+2, k++] = ln_syeadj + ln_sycadj - ln_syepay - ln_sycpay;
                xlWorkSheet.get_Range(String.Format("C{0}", iRow)).Columns.HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4
                xlWorkSheet.get_Range(String.Format("N{0}", iRow),String.Format("N{0}", iRow + 2)).Columns.HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4

                iRow += 4;

                xlWorkSheet.Cells[iRow, 1] = "Total No. of Cases: " + ln_tcase.ToString("N0");

                k = 3;
                xlWorkSheet.Cells[iRow, k++] = "Grand Total:";
                xlWorkSheet.Cells[iRow, k++] = "Last Reserve";
                xlWorkSheet.Cells[iRow, k++] = ln_taeadja;
                xlWorkSheet.Cells[iRow, k++] = ln_taeadjb;
                xlWorkSheet.Cells[iRow, k++] = ln_taeadjc;
                xlWorkSheet.Cells[iRow, k++] = ln_taeadj;
                xlWorkSheet.Cells[iRow, k++] = ln_tacadja;
                xlWorkSheet.Cells[iRow, k++] = ln_tacadjb;
                xlWorkSheet.Cells[iRow, k++] = ln_tacadjc;
                xlWorkSheet.Cells[iRow, k++] = ln_tacadj;

                k++;
                xlWorkSheet.Cells[iRow, k++] = "Prov";
                xlWorkSheet.Cells[iRow, k++] = ln_tyeadj + ln_tycadj;

                k = 4;
                xlWorkSheet.Cells[iRow+1, k++] = "Revised Reserve";
                xlWorkSheet.Cells[iRow+1, k++] = ln_tyeadja;
                xlWorkSheet.Cells[iRow+1, k++] = ln_tyeadjb;
                xlWorkSheet.Cells[iRow+1, k++] = ln_tyeadjc;
                xlWorkSheet.Cells[iRow+1, k++] = ln_tyeadj;
                xlWorkSheet.Cells[iRow+1, k++] = ln_tycadja;
                xlWorkSheet.Cells[iRow+1, k++] = ln_tycadjb;
                xlWorkSheet.Cells[iRow+1, k++] = ln_tycadjc;
                xlWorkSheet.Cells[iRow+1, k++] = ln_tycadj;

                k++;
                xlWorkSheet.Cells[iRow+1, k++] = "Paid";
                xlWorkSheet.Cells[iRow+1, k++] = ln_tyepay + ln_tycpay;

                k = 4;
                xlWorkSheet.Cells[iRow+2, k++] = "Adjustment";
                xlWorkSheet.Cells[iRow+2, k++] = ln_tmeadja;
                xlWorkSheet.Cells[iRow+2, k++] = ln_tmeadjb;
                xlWorkSheet.Cells[iRow+2, k++] = ln_tmeadjc;
                xlWorkSheet.Cells[iRow+2, k++] = ln_tmeadj;
                xlWorkSheet.Cells[iRow+2, k++] = ln_tmcadja;
                xlWorkSheet.Cells[iRow+2, k++] = ln_tmcadjb;
                xlWorkSheet.Cells[iRow+2, k++] = ln_tmcadjc;
                xlWorkSheet.Cells[iRow+2, k++] = ln_tmcadj;
                xlWorkSheet.Cells[iRow+2, k++] = ln_tmeadj + ln_tmcadj;
                xlWorkSheet.Cells[iRow+2, k++] = "O/S";
                xlWorkSheet.Cells[iRow+2, k++] = ln_tyeadj + ln_tycadj - ln_tyepay - ln_tycpay;
                xlWorkSheet.get_Range(String.Format("C{0}", iRow)).Columns.HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4
                xlWorkSheet.get_Range(String.Format("N{0}", iRow),String.Format("N{0}", iRow + 2)).Columns.HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4

                //xlWorkSheet.get_Range(String.Format("C{0}", iRow), String.Format("I{0}", iRow)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 9;
            }

            if (sheetIndex == 2)
            {
                decimal ln_tmadj = 0, ln_tyadj = 0;

                foreach (DataRow dr in dt.Rows)
                {
                    decimal madj = Convert.ToDecimal(dr["madj"]);
                    decimal yadj = Convert.ToDecimal(dr["yadj"]);

                    xlWorkSheet.Cells[iRow, 1] = "'"+dr["flosyr"].ToString().Trim();
                    xlWorkSheet.Cells[iRow, 8] = madj;
                    xlWorkSheet.Cells[iRow, 9] = yadj;

                    ln_tmadj += madj;
                    ln_tyadj += yadj;

                    iRow++;
                }

                xlWorkSheet.get_Range(String.Format("A{0}", iRow), String.Format("A{0}", iRow)).HorizontalAlignment = Excel.XlHAlign.xlHAlignRight;
                xlWorkSheet.get_Range(String.Format("B{0}", iRow), String.Format("I{0}", iRow)).Borders[Excel.XlBordersIndex.xlEdgeTop].LineStyle = 7;

                xlWorkSheet.Cells[iRow, 1] = "Total:";
                xlWorkSheet.Cells[iRow, 8] = ln_tmadj;
                xlWorkSheet.Cells[iRow, 9] = ln_tyadj;

                xlWorkSheet.get_Range(String.Format("B{0}", iRow), String.Format("I{0}", iRow)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 9;
            }
            return iRow;
        }

        public static Int16 WsColTitleECLosAnaly(Excel.Worksheet xlWorkSheet, DataTable dt,
                string optid, string z_company, string z_title, string z_currency)
        {
            xlWorkSheet.Cells.Font.Name = "arial";
            xlWorkSheet.Cells.Font.Size = 8;

            xlWorkSheet.Columns["A"].ColumnWidth = 5;
            xlWorkSheet.Columns["B"].ColumnWidth = 13;
            xlWorkSheet.Columns["C"].ColumnWidth = 33;

            xlWorkSheet.Columns["D"].ColumnWidth = 15;
            xlWorkSheet.Columns["E:G"].ColumnWidth = 10;
            xlWorkSheet.Columns["H"].ColumnWidth = 3;
            xlWorkSheet.Columns["I"].ColumnWidth = 8;
            xlWorkSheet.Columns["J"].ColumnWidth = 3;

            xlWorkSheet.Columns["K:R"].ColumnWidth = 12;

            xlWorkSheet.Columns["A"].HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4
            xlWorkSheet.Columns["E:G"].HorizontalAlignment = Excel.XlHAlign.xlHAlignLeft; // 2

            xlWorkSheet.Columns["B"].NumberFormat = "dd mmm yyyy";
            xlWorkSheet.Columns["K:R"].NumberFormat = "#,###,###.00";
            xlWorkSheet.Columns["L"].NumberFormat = "0.00%";
            xlWorkSheet.Columns["N"].NumberFormat = "0.00%";
            xlWorkSheet.Columns["Q"].NumberFormat = "0.00%";
            
            xlWorkSheet.get_Range("A1", "A3").HorizontalAlignment = Excel.XlHAlign.xlHAlignLeft; // 2
            xlWorkSheet.get_Range("Q2", "Q2").HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4
            xlWorkSheet.get_Range("R2", "R2").HorizontalAlignment = Excel.XlHAlign.xlHAlignLeft; // 2
            xlWorkSheet.get_Range("R2", "R2").NumberFormat = "dd mmm yyyy";

            xlWorkSheet.get_Range("C5", "D5").Merge();
            xlWorkSheet.get_Range("F5", "K5").Merge();
            xlWorkSheet.get_Range("O5", "P5").Merge();

            xlWorkSheet.get_Range("C5", "D5").HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter; // 3
            xlWorkSheet.get_Range("E5", "R6").HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter; // 3

            Int16 i = 1, k = 1;

            xlWorkSheet.Cells[i++, 1] = z_company;
            xlWorkSheet.Cells[i, 1] = z_title;
            xlWorkSheet.Cells[i, 17] = "日期: ";
            xlWorkSheet.Cells[i++, 18] = DateTime.Now;
            xlWorkSheet.Cells[i, 1] = z_currency;

            i += 2;

            if (dt.Rows.Count == 0)
            {
                xlWorkSheet.Cells[i, 1] = "No record found!";
                xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                return i;
            }

            k = 2;

            xlWorkSheet.Cells[i + 1, k++] = "檔案編號";
            xlWorkSheet.Cells[i, k] = "傷者";
            xlWorkSheet.Cells[i + 1, k++] = "英文名";
            xlWorkSheet.Cells[i + 1, k++] = "中文名";
            xlWorkSheet.Cells[i + 1, k++] = "意外日期";

            xlWorkSheet.Cells[i, k] = "我方委任律師";
            xlWorkSheet.Cells[i + 1, k++] = "開始日期";
            xlWorkSheet.Cells[i + 1, k++] = "結束日期";
            k++;
            xlWorkSheet.Cells[i + 1, k++] = "所需時間(日)";
            k++;
            xlWorkSheet.Cells[i + 1, k++] = "費用";
            xlWorkSheet.Cells[i, k] = "費用佔總";
            xlWorkSheet.Cells[i + 1, k++] = "賠款比率%";
            xlWorkSheet.Cells[i, k] = "傷者律師";
            xlWorkSheet.Cells[i + 1, k++] = "費用";
            xlWorkSheet.Cells[i, k] = "費用佔總";
            xlWorkSheet.Cells[i + 1, k++] = "賠款比率%";
            xlWorkSheet.Cells[i, k] = "偒者";
            xlWorkSheet.Cells[i + 1, k++] = "索償額";
            xlWorkSheet.Cells[i + 1, k++] = "賠款額";
            xlWorkSheet.Cells[i, k] = "賠款額佔";
            xlWorkSheet.Cells[i + 1, k++] = "索償額比率%";
            xlWorkSheet.Cells[i + 1, k] = "賠款總額";


            xlWorkSheet.get_Range(String.Format("A{0}", i), String.Format("R{0}", i)).Borders[Excel.XlBordersIndex.xlEdgeTop].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("A{0}:R{1}", i, i + 1)).Borders[Excel.XlBordersIndex.xlEdgeLeft].LineStyle = 7;

            xlWorkSheet.get_Range(String.Format("A{0}:A{1}", i, i + 1)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("B{0}:B{1}", i, i + 1)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("D{0}:D{1}", i, i + 1)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("E{0}:E{1}", i, i + 1)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("L{0}:L{1}", i, i + 1)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("N{0}:N{1}", i, i + 1)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("Q{0}:Q{1}", i, i + 1)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("R{0}:R{1}", i, i + 1)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;

            xlWorkSheet.get_Range(String.Format("A{0}", i+1), String.Format("R{0}", i+1)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 7;

            xlWorkSheet.get_Range(String.Format("A{0}:A{1}", i, i + 1)).RowHeight = 15;

            //for (int j = 0; j < k; j++)
            //{
            //    string xxx_str = ((char)((int)'A' + j)).ToString();
            //    xlWorkSheet.get_Range(String.Format("{0}{1}:{0}{2}", xxx_str, i, i + 1)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
            //}

            i = WsClmECLosAnaly(xlWorkSheet, dt, optid, i += 2);

            return i;
        }

        public static Int16 WsClmECLosAnaly(Excel.Worksheet xlWorkSheet, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            decimal ln_seq = 0;
            decimal ln_tnodays = 0, ln_tptoowns = 0, ln_tptoclnts = 0, ln_tclmamt = 0, ln_tptoclnt = 0,
                    ln_tamount = 0;
            decimal ln_tper1 = 0, ln_tper2 = 0, ln_tper3 = 0, ln_tally1 = 0, ln_tally2 = 0, ln_tally3 = 0,
                    ln_tallyday = 0, ln_twsol = 0;

            xlWorkSheet.get_Range(String.Format("A{0}:R{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeLeft].LineStyle = 7;

            xlWorkSheet.get_Range(String.Format("A{0}:A{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("B{0}:B{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("C{0}:C{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("D{0}:D{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("E{0}:E{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("L{0}:L{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("N{0}:N{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("Q{0}:Q{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("R{0}:R{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;

            iRow++;

            foreach (DataRow dr in dt.Rows)
            {
                ln_seq = ln_seq + 1;

                decimal fptoowns = Convert.ToDecimal(dr["fptoowns"]);
                decimal fptoclnts = Convert.ToDecimal(dr["fptoclnts"]);
                decimal fclmamt = Convert.ToDecimal(dr["fclmamt"]);
                decimal fptoclnt = Convert.ToDecimal(dr["fptoclnt"]);
                decimal famount = Convert.ToDecimal(dr["famount"]);

                decimal fnodays = Convert.ToDecimal(dr["fnodays"]);

                decimal fper1 = Convert.ToDecimal(dr["fper1"]);
                decimal fper2 = Convert.ToDecimal(dr["fper2"]);
                decimal fper3 = Convert.ToDecimal(dr["fper3"]);
                decimal fwsol = Convert.ToDecimal(dr["fwsol"]);

                k = 1;

                xlWorkSheet.Cells[iRow, k++] = ln_seq;
                xlWorkSheet.Cells[iRow, k++] = dr["fclmno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fempyee"];
                xlWorkSheet.Cells[iRow, k++] = dr["fempyee_c"];
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["flosdate"]);

                if (dr["prcrdate"].ToString().Trim() == "Y")
                    xlWorkSheet.Cells[iRow, k] = Convert.ToDateTime(dr["frcrdate"]);

                k++;
                if (dr["prcrto"].ToString().Trim() == "Y")
                    xlWorkSheet.Cells[iRow, k] = Convert.ToDateTime(dr["frcrto"]);

                k+=2;
                if (dr["prcrdate"].ToString().Trim() == "Y" && dr["prcrto"].ToString().Trim() == "Y")
                {
                    xlWorkSheet.Cells[iRow, k] = fnodays;

                    ln_tallyday = ln_tallyday + 1;
                    ln_tnodays = ln_tnodays + fnodays;
                }

                k+=2;
                xlWorkSheet.Cells[iRow, k++] = fptoowns;
                xlWorkSheet.Cells[iRow, k++] = decimal.Round(fper1/100,4,MidpointRounding.AwayFromZero);
                xlWorkSheet.Cells[iRow, k++] = fptoclnts;
                xlWorkSheet.Cells[iRow, k++] = decimal.Round(fper2/100,4,MidpointRounding.AwayFromZero);

           		if (fclmamt !=0)
                    xlWorkSheet.Cells[iRow, k]   = fclmamt;

                k++;
                xlWorkSheet.Cells[iRow, k++]   = fptoclnt;
		
                if (fclmamt != 0)
                    xlWorkSheet.Cells[iRow, k] = decimal.Round(fper3/100,4,MidpointRounding.AwayFromZero);

                k++;
                xlWorkSheet.Cells[iRow, k++]   = famount;

                xlWorkSheet.Cells[iRow, k++] = dr["fsoldesc1"];
                xlWorkSheet.Cells[iRow, k++] = dr["fsoldesc2"];
        
                ln_tptoowns  += fptoowns;
                ln_tptoclnts += fptoclnts;
                ln_tclmamt   += fclmamt;
                ln_tptoclnt  += fptoclnt;
                ln_tamount   += famount;

                ln_tper1     += fper1;
                ln_tper2     += fper2;
                ln_tper3     += fper3;
                ln_twsol     += fwsol;

                xlWorkSheet.get_Range(String.Format("A{0}:R{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeLeft].LineStyle = 7;

                xlWorkSheet.get_Range(String.Format("A{0}:A{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
                xlWorkSheet.get_Range(String.Format("B{0}:B{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
                xlWorkSheet.get_Range(String.Format("C{0}:C{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
                xlWorkSheet.get_Range(String.Format("D{0}:D{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
                xlWorkSheet.get_Range(String.Format("E{0}:E{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
                xlWorkSheet.get_Range(String.Format("L{0}:L{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
                xlWorkSheet.get_Range(String.Format("N{0}:N{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
                xlWorkSheet.get_Range(String.Format("Q{0}:Q{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
                xlWorkSheet.get_Range(String.Format("R{0}:R{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
                xlWorkSheet.get_Range(String.Format("A{0}:A{1}", iRow, iRow)).RowHeight = 15;

                //xlWorkSheet.get_Range(String.Format("A{0}", iRow), String.Format("O{0}", iRow + 2)).Borders[Excel.XlBordersIndex.xlEdgeLeft].LineStyle = 7;

                //for (int j = 0; j < k; j++)
                //{
                //    string xxx_str = ((char)((int)'A' + j)).ToString();
                //    xlWorkSheet.get_Range(String.Format("{0}{1}:{0}{2}", xxx_str, iRow, iRow + 2)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
                //}

                iRow++;
            }

            xlWorkSheet.get_Range(String.Format("A{0}:R{0}", iRow)).Borders[Excel.XlBordersIndex.xlEdgeTop].LineStyle = 7;

            k = 7;
            xlWorkSheet.Cells[iRow, k++] = "共:";
            k += 3;
            xlWorkSheet.Cells[iRow, k++] = ln_tptoowns;
            k++;
            xlWorkSheet.Cells[iRow, k++] = ln_tptoclnts;
            k++;
            xlWorkSheet.Cells[iRow, k++] = ln_tclmamt;
            xlWorkSheet.Cells[iRow, k++] = ln_tptoclnt;
            k++;
            xlWorkSheet.Cells[iRow, k++] = ln_tamount;

            xlWorkSheet.get_Range(String.Format("G{0}:G{0}", iRow)).HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4;
            xlWorkSheet.get_Range(String.Format("A{0}:A{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeLeft].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("R{0}:R{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("A{0}:A{1}", iRow, iRow)).RowHeight = 15;

            iRow++;
            k = 7;
            xlWorkSheet.Cells[iRow, k++] = "總計:";
            k++;
            xlWorkSheet.Cells[iRow, k++] = ln_twsol == 0 ? 0: decimal.Round(ln_tnodays/ln_twsol,MidpointRounding.AwayFromZero);
            k+=2;
            xlWorkSheet.Cells[iRow, k++] = ln_tamount == 0 ? 0: decimal.Round(ln_tptoowns/ln_tamount,4,MidpointRounding.AwayFromZero);
            k++;
            xlWorkSheet.Cells[iRow, k++] = ln_tamount == 0 ? 0: decimal.Round(ln_tptoclnts/ln_tamount,4,MidpointRounding.AwayFromZero);
            k +=2;
            xlWorkSheet.Cells[iRow, k++] = ln_tclmamt == 0 ? 0: decimal.Round(ln_tptoclnt/ln_tclmamt,4,MidpointRounding.AwayFromZero);

            xlWorkSheet.get_Range(String.Format("G{0}:G{0}", iRow)).HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4;
            xlWorkSheet.get_Range(String.Format("A{0}:A{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeLeft].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("R{0}:R{1}", iRow, iRow)).Borders[Excel.XlBordersIndex.xlEdgeRight].LineStyle = 7;

            xlWorkSheet.get_Range(String.Format("A{0}:R{0}", iRow)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 7;
            xlWorkSheet.get_Range(String.Format("A{0}:A{1}", iRow, iRow)).RowHeight = 15;

            return iRow;
        }

        public static Int16 WsColTitleClmStatus2(Excel.Worksheet xlWorkSheet, DataTable dt,
            string optid, string z_company, string z_title, string z_currency, string z_criterion)
        {
            xlWorkSheet.Cells.Font.Name = "arial";
            xlWorkSheet.Cells.Font.Size = 8;

            xlWorkSheet.Columns["A:C"].ColumnWidth = 9;
            xlWorkSheet.Columns["D:E"].ColumnWidth = 10;
            xlWorkSheet.Columns["F"].ColumnWidth = 7;
            xlWorkSheet.Columns["G"].ColumnWidth = 9;
            xlWorkSheet.Columns["H"].ColumnWidth = 7;
            xlWorkSheet.Columns["I:J"].ColumnWidth = 33;
            xlWorkSheet.Columns["K:M"].ColumnWidth = 11;

            xlWorkSheet.Columns["A:C"].HorizontalAlignment = Excel.XlHAlign.xlHAlignLeft; // 2
            xlWorkSheet.Columns["G"].HorizontalAlignment = Excel.XlHAlign.xlHAlignLeft; // 2

            xlWorkSheet.Columns["A:C"].NumberFormat = "yyyy/mm/dd";
            xlWorkSheet.Columns["G"].NumberFormat = "yyyy/mm/dd";
            xlWorkSheet.Columns["K:M"].NumberFormat = "#,###,###.00";

            xlWorkSheet.get_Range("M3", "M3").HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4
            xlWorkSheet.get_Range("N3", "N3").HorizontalAlignment = Excel.XlHAlign.xlHAlignLeft; // 2
            xlWorkSheet.get_Range("N3", "N3").NumberFormat = "yyyy/mm/dd";

            xlWorkSheet.get_Range("A6", "N6").Font.Underline = 2;

            Int16 i = 1, k = 1;

            xlWorkSheet.Cells[i++, 1] = z_company;
            xlWorkSheet.Cells[i++, 1] = z_title;
            xlWorkSheet.Cells[i, 1] = z_currency;
            xlWorkSheet.Cells[i, 13] = "Date: ";
            xlWorkSheet.Cells[i++, 14] = DateTime.Now;
            xlWorkSheet.Cells[i, 1] = z_criterion;

            i += 2;

            if (dt.Rows.Count == 0)
            {
                xlWorkSheet.Cells[i, 1] = "No record found!";
                xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                return i;
            }

            xlWorkSheet.Cells[i, k++] = "Reg Date";
            xlWorkSheet.Cells[i, k++] = "Report Date";
            xlWorkSheet.Cells[i, k++] = "Form 2 Date";
            xlWorkSheet.Cells[i, k++] = "Claim No";
            xlWorkSheet.Cells[i, k++] = "Policy No";
            xlWorkSheet.Cells[i, k++] = "Producer";
            xlWorkSheet.Cells[i, k++] = "Date of Loss";
            xlWorkSheet.Cells[i, k++] = "Lap Days";
            xlWorkSheet.Cells[i, k++] = "Insured";
            xlWorkSheet.Cells[i, k++] = "Loss Location / Particulars";
            xlWorkSheet.Cells[i, k++] = "Reserve";
            xlWorkSheet.Cells[i, k++] = "Paid";
            xlWorkSheet.Cells[i, k++] = "Outstanding";
            xlWorkSheet.Cells[i, k++] = "Status";
            xlWorkSheet.Cells[i, k++] = "Subsidaries";
            xlWorkSheet.Cells[i, k++] = "EC Compensation";
            xlWorkSheet.Cells[i, k++] = "Other Expenses";
            xlWorkSheet.Cells[i, k++] = "Claimant's Costs";
            xlWorkSheet.Cells[i, k++] = "Own Legal Costs";
            xlWorkSheet.Cells[i, k++] = "Legal - Other Expenses";
            xlWorkSheet.Cells[i, k++] = "CL Damages";
            xlWorkSheet.Cells[i, k++] = "Other Expenses";
            xlWorkSheet.Cells[i, k++] = "Claimant's Costs";
            xlWorkSheet.Cells[i, k++] = "Own Legal Costs";
            xlWorkSheet.Cells[i, k++] = "Legal - Other Expenses";
            xlWorkSheet.Cells[i, k++] = "Claim Amount";
            xlWorkSheet.Cells[i, k++] = "Age";
            xlWorkSheet.Cells[i, k++] = "Sex";
           
            xlWorkSheet.Cells[i, k++] = "Job Nature";
            xlWorkSheet.Cells[i, k++] = "Monthly Salary";
            xlWorkSheet.Cells[i, k++] = "Sick Leave";
            xlWorkSheet.Cells[i, k++] = "PD";
            xlWorkSheet.Cells[i, k++] = "Adjuster";
            xlWorkSheet.Cells[i, k++] = "Solicitor";
            xlWorkSheet.Cells[i, k++] = "Recruit Date (Start)";
            xlWorkSheet.Cells[i, k++] = "Recruite Date (End)";
            xlWorkSheet.Cells[i, k++] = "Bring Up Date";
            xlWorkSheet.Cells[i, k++] = "Bring Up Action";
            xlWorkSheet.Cells[i, k++] = "Claims Life Cycle";
            xlWorkSheet.Cells[i, k++] = "Claims Progress";
            xlWorkSheet.Cells[i, k++] = "Case Log User";
            xlWorkSheet.Cells[i, k++] = "Case Handler";
            i = WsClmCStatus2(xlWorkSheet, dt, optid, i += 2);

            return i;
        }

        public static Int16 WsClmCStatus2(Excel.Worksheet xlWorkSheet, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fclsseq = "";

            decimal ln_sres = 0, ln_spay = 0, ln_sos = 0,
                    ln_tres = 0, ln_tpay = 0, ln_tos = 0;

            decimal ln_scase = 0, ln_tcase = 0;

            foreach (DataRow dr in dt.Rows)
            {
                if (lc_fclsseq != dr["fclsseq"].ToString().Trim())
                {
                    if (iRow > startRow)
                    {
                        xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");

                        k = 10;
                        xlWorkSheet.Cells[iRow, k++] = "Total:";
                        xlWorkSheet.Cells[iRow, k++] = ln_sres;
                        xlWorkSheet.Cells[iRow, k++] = ln_spay;
                        xlWorkSheet.Cells[iRow, k++] = ln_sos;

                        xlWorkSheet.get_Range(String.Format("J{0}", iRow)).Columns.HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4

                        iRow += 2;
                    }

                    lc_fclsseq = dr["fclsseq"].ToString().Trim();
                    xlWorkSheet.Cells[iRow, 1] = dr["fclsname"].ToString();

                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                    ln_sres = 0; ln_spay = 0; ln_sos = 0;
                    ln_scase = 0;

                    iRow += 2;
                }

                decimal mres = Convert.ToDecimal(dr["mres"]);
                decimal mpay = Convert.ToDecimal(dr["mpay"]);
                decimal yos = Convert.ToDecimal(dr["yos"]);
                decimal fage = Convert.ToDecimal(dr["fage"]);

                k = 1;

                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fregdate"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["frepdate"]);

                if (dr["nullfrm2"].ToString().Trim() != "1")
                    xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["ffrm2date"]);
                else
                    k++;

                xlWorkSheet.Cells[iRow, k++] = dr["fclmno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fpolno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fprdr"];
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["flosdate"]);

                if (dr["fclass"].ToString().Trim() == "EEC")
                {
                    if (mres == 100000)
                        xlWorkSheet.Cells[iRow, k++] = "Pending";
                    else if (dr["nullfrm2"].ToString().Trim() == "1")
                        xlWorkSheet.Cells[iRow, k++] = (Convert.ToDateTime(dr["frepdate"]) -
                             Convert.ToDateTime(dr["flosdate"])).Days;
                    else
                        xlWorkSheet.Cells[iRow, k++] = (Convert.ToDateTime(dr["ffrm2date"]) -
                             Convert.ToDateTime(dr["flosdate"])).Days;

                }
                else k++;

                xlWorkSheet.Cells[iRow, k++] = dr["finsd"] + ((char)13).ToString() + ((char)10).ToString();
                xlWorkSheet.Cells[iRow, k++] = dr["flosloc"].ToString().Trim() + ((char)13).ToString() +
                    ((char)10).ToString() + dr["fpartr_t1"].ToString().TrimEnd() +
                    dr["fpartr_t2"].ToString().TrimEnd() +
                    (fage == 0m ? "" : " (Age: " + fage.ToString("#,#") + ")") + ((char)10).ToString();

                xlWorkSheet.get_Range(String.Format("I{0}", iRow), String.Format("I{0}", iRow + 2)).Merge();
                xlWorkSheet.get_Range(String.Format("J{0}", iRow), String.Format("J{0}", iRow + 2)).Merge();

                xlWorkSheet.get_Range(String.Format("I{0}", iRow), String.Format("J{0}", iRow + 2)).HorizontalAlignment = 1;
                xlWorkSheet.get_Range(String.Format("I{0}", iRow), String.Format("J{0}", iRow + 2)).VerticalAlignment = 1;
                xlWorkSheet.get_Range(String.Format("I{0}", iRow), String.Format("J{0}", iRow)).RowHeight = 11.25;

                xlWorkSheet.Cells[iRow, k++] = mres;
                xlWorkSheet.Cells[iRow, k++] = mpay;
                xlWorkSheet.Cells[iRow, k++] = yos;
                xlWorkSheet.Cells[iRow, k++] = dr["fmoddesc"];
                xlWorkSheet.Cells[iRow, k++] = dr["fdept"];
                xlWorkSheet.Cells[iRow, k++] = dr["EPA1"];
                xlWorkSheet.Cells[iRow, k++] = dr["EPA2"];
                xlWorkSheet.Cells[iRow, k++] = dr["EPB"]; 
                xlWorkSheet.Cells[iRow, k++] = dr["EPC1"];
                xlWorkSheet.Cells[iRow, k++] = dr["EPC2"];
                xlWorkSheet.Cells[iRow, k++] = dr["CPA1"]; 
                xlWorkSheet.Cells[iRow, k++] = dr["CPA2"];
                xlWorkSheet.Cells[iRow, k++] = dr["CPB"];
                xlWorkSheet.Cells[iRow, k++] = dr["CPC1"];
                xlWorkSheet.Cells[iRow, k++] = dr["CPC2"];
                xlWorkSheet.Cells[iRow, k++] = dr["fclmamt"];
                xlWorkSheet.Cells[iRow, k++] = dr["fage"];
                xlWorkSheet.Cells[iRow, k++] = dr["fsex"];
                xlWorkSheet.Cells[iRow, k++] = dr["fjobn"];
                xlWorkSheet.Cells[iRow, k++] = dr["fsalary"];
                xlWorkSheet.Cells[iRow, k++] = dr["fdays"];
                xlWorkSheet.Cells[iRow, k++] = dr["fpdsh"];
                xlWorkSheet.Cells[iRow, k++] = dr["fadjr1N"].ToString().Trim();
                xlWorkSheet.Cells[iRow, k++] = dr["fsolr1N"].ToString().Trim();
                xlWorkSheet.Cells[iRow, k++] = dr["frcrdate"];
                xlWorkSheet.Cells[iRow, k++] = dr["frcrto"];
                xlWorkSheet.Cells[iRow, k++] = dr["fbringdate"];
                xlWorkSheet.Cells[iRow, k++] = dr["ftoken"].ToString().Trim();
                xlWorkSheet.Cells[iRow, k++] = dr["fcycle"].ToString().Trim();
                xlWorkSheet.Cells[iRow, k++] = dr["fprogress"].ToString().Trim();
                xlWorkSheet.Cells[iRow, k++] = dr["finpuser"].ToString().Trim();
                xlWorkSheet.Cells[iRow, k++] = dr["casehandler"].ToString().Trim();

                ln_sres = ln_sres + mres; ln_spay = ln_spay + mpay; ln_sos = ln_sos + yos;
                ln_tres = ln_tres + mres; ln_tpay = ln_tpay + mpay; ln_tos = ln_tos + yos;

                ln_scase++; ln_tcase++;

                iRow += 4;
            }

            xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");

            k = 10;
            xlWorkSheet.Cells[iRow, k++] = "Total:";
            xlWorkSheet.Cells[iRow, k++] = ln_sres;
            xlWorkSheet.Cells[iRow, k++] = ln_spay;
            xlWorkSheet.Cells[iRow, k++] = ln_sos;

            xlWorkSheet.get_Range(String.Format("J{0}", iRow)).Columns.HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4

            iRow += 2;

            xlWorkSheet.Cells[iRow, 1] = "Total of Cases: " + ln_tcase.ToString("N0");

            k = 10;
            xlWorkSheet.Cells[iRow, k++] = "Grand Total:";
            xlWorkSheet.Cells[iRow, k++] = ln_tres;
            xlWorkSheet.Cells[iRow, k++] = ln_tpay;
            xlWorkSheet.Cells[iRow, k++] = ln_tos;

            xlWorkSheet.get_Range(String.Format("H{0}", iRow)).Columns.HorizontalAlignment = 4;

            return iRow;
        }

    }
}
