using System;
using System.Collections.Generic;
using System.Data;
using System.Data.OleDb;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;

namespace INS.INSClass
{
    class XLS
    {
        public DataTable ReadExcelToTable(string path)
        {
            //string connstring = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + path + ";Extended Properties='Excel 8.0;HDR=NO;IMEX=1';";

            string connstring = "Provider=Microsoft.JET.OLEDB.4.0;Data Source=" + path + ";Extended Properties='Excel 8.0;HDR=NO;IMEX=1';";

            using (OleDbConnection conn = new OleDbConnection(connstring))
            {
                conn.Open();
                //Get All Sheets Name
                DataTable sheetsName = conn.GetOleDbSchemaTable(OleDbSchemaGuid.Tables, new object[] { null, null, null, "Table" });

                //Get the First Sheet Name
                string firstSheetName = sheetsName.Rows[0][2].ToString();

                //Query String 
                string sql = string.Format("SELECT * FROM [{0}]", firstSheetName);
                OleDbDataAdapter ada = new OleDbDataAdapter(sql, connstring);
                DataSet set = new DataSet();
                ada.Fill(set);
                conn.Close();
                return set.Tables[0];
            }
        }

        public Boolean ReadExcelToTable2(string strFilePath)
        {
            if (!File.Exists(strFilePath)) return false;
            String strExcelConn = "Provider=Microsoft.Jet.OLEDB.4.0;"
            + "Data Source=" + strFilePath + ";"
            + "Extended Properties='Excel 8.0;HDR=Yes'";
            OleDbConnection connExcel = new OleDbConnection(strExcelConn);
            OleDbCommand cmdExcel = new OleDbCommand();
            try
            {
                cmdExcel.Connection = connExcel;

                //Check if the Sheet Exists
                connExcel.Open();
                DataTable dtExcelSchema;
                //Get the Schema of the WorkBook
                dtExcelSchema = connExcel.GetOleDbSchemaTable(OleDbSchemaGuid.Tables, null);
                connExcel.Close();

                //Read Data from Sheet1
                connExcel.Open();
                OleDbDataAdapter da = new OleDbDataAdapter();
                DataSet ds = new DataSet();
                string SheetName = dtExcelSchema.Rows[0]["TABLE_NAME"].ToString();
                cmdExcel.CommandText = "SELECT * From [" + SheetName + "]";
                //Range Query
                //cmdExcel.CommandText = "SELECT * From [" + SheetName + "A3:B5]";

                da.SelectCommand = cmdExcel;
                da.Fill(ds);
                connExcel.Close();
                return true;
            }
            catch
            {
                return false;
            }
            finally
            {
                cmdExcel.Dispose();
                connExcel.Dispose();
            }
        }

        public string ReadExcelToTable3(string path)
        {

            string con = @"Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" + path + ";Extended Properties='Excel 8.0;HDR=Yes;'";
            string result = ",";
            using (OleDbConnection connection = new OleDbConnection(con))
            {
                connection.Open();

                DataTable sheetsName = connection.GetOleDbSchemaTable(OleDbSchemaGuid.Tables, new object[] { null, null, null, "Table" });
                string firstSheetName = sheetsName.Rows[0][2].ToString();
                string sql = string.Format("SELECT * FROM [{0}]", firstSheetName);

                OleDbCommand command = new OleDbCommand(sql, connection);
                using (OleDbDataReader dr = command.ExecuteReader())
                {
                    while (dr.Read())
                    {
                        var row1Col0 = dr[1];
                        result = row1Col0.ToString() + result;
                    }
                }
                connection.Close(); 
            }
            return result;
        }
    }
}
