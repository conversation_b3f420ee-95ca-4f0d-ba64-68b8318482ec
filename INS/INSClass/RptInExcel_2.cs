using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

using Excel = Microsoft.Office.Interop.Excel;

namespace INS.INSClass
{
    partial class RptInExcel
    {
        public static Int16 WsColTitlePrmReg(Excel.Worksheet xlWorkSheet, String vfbus,
                                Boolean noDetail, string optid, Int16 startRow)
        {
            Int16 i = startRow;

            if ("DEK".Contains(vfbus) && "prmreg|polprm".Contains(optid))
            {
                xlWorkSheet.Cells.Font.Name = "arial";
                xlWorkSheet.Cells.Font.Size = 8;

                xlWorkSheet.Columns["A:B"].ColumnWidth = 8;
                xlWorkSheet.Columns["C:D"].ColumnWidth = 11;
                xlWorkSheet.Columns["E"].ColumnWidth = 7;
                xlWorkSheet.Columns["F"].ColumnWidth = 10;
                xlWorkSheet.Columns["G:H"].ColumnWidth = 8;
                xlWorkSheet.Columns["K"].ColumnWidth = 38;
                xlWorkSheet.Columns["L:S"].ColumnWidth = 11;

                xlWorkSheet.Columns["A:B"].HorizontalAlignment = 2;
                xlWorkSheet.Columns["G:H"].HorizontalAlignment = 2;

                xlWorkSheet.Columns["A:B"].NumberFormat = "yyyy/mm/dd";
                xlWorkSheet.Columns["G:H"].NumberFormat = "yyyy/mm/dd";

                xlWorkSheet.Columns["L:S"].HorizontalAlignment = 4;
                xlWorkSheet.Columns["L:S"].NumberFormat = "#,###,###.00";

                xlWorkSheet.get_Range("R3", "R3").HorizontalAlignment = 4;
                xlWorkSheet.get_Range("S3", "S3").HorizontalAlignment = 2;
                xlWorkSheet.get_Range("S3", "S3").NumberFormat = "yyyy/mm/dd";
 
                xlWorkSheet.Cells[i, 18] = "Date: ";
                xlWorkSheet.Cells[i, 19] = DateTime.Now;

                if (noDetail)
                {
                    i += 2;
                    xlWorkSheet.Cells[i, 1] = "No Record Found!";
                    xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                    i++;

                    return i;
                }

                i +=2;
                Int16 k = 1;

                xlWorkSheet.get_Range("A5", "S5").Font.Underline = 2;

                xlWorkSheet.Cells[i, k++] = "prmreg".Contains(optid) ? "Bill Date" : "Book Date";
                xlWorkSheet.Cells[i, k++] = "prmreg".Contains(optid) ? "Book Date" : "";
                xlWorkSheet.Cells[i, k++] = "Policy No";
                xlWorkSheet.Cells[i, k++] = "Endt No";
                xlWorkSheet.Cells[i, k++] = "prmreg".Contains(optid) ? "Install" : "";
                xlWorkSheet.Cells[i, k++] = "prmreg".Contains(optid) ? "Dr/Cr No" : "";
                xlWorkSheet.Cells[i, k++] = "Effect From";
                xlWorkSheet.Cells[i, k++] = "Effect To";
                xlWorkSheet.Cells[i, k++] = "Producer#";
                xlWorkSheet.Cells[i, k++] = "Client#";
                xlWorkSheet.Cells[i, k++] = "Name";
                xlWorkSheet.Cells[i, k++] = "Gross Premium";
                xlWorkSheet.Cells[i, k++] = "Discount";
                xlWorkSheet.Cells[i, k++] = "R/I Cost";
                xlWorkSheet.Cells[i, k++] = "Comm/ASP Fee";
                xlWorkSheet.Cells[i, k++] = "IA Levy";
                xlWorkSheet.Cells[i, k++] = "Levy 1";
                xlWorkSheet.Cells[i, k++] = "Levy 2";
                xlWorkSheet.Cells[i, k++] = "Levy 3";

                i += 2;

                return i;
            }

            if (vfbus == "R" && "prmreg|polprm".Contains(optid))
            {
                xlWorkSheet.Cells.Font.Name = "arial";
                xlWorkSheet.Cells.Font.Size = 8;

                xlWorkSheet.Columns["A:B"].ColumnWidth = 8;
                xlWorkSheet.Columns["C:G"].ColumnWidth = 11;
                xlWorkSheet.Columns["H"].ColumnWidth = 7;
                xlWorkSheet.Columns["I"].ColumnWidth = 11;
                xlWorkSheet.Columns["J:M"].ColumnWidth = 8;
                xlWorkSheet.Columns["N"].ColumnWidth = 38;
                xlWorkSheet.Columns["O:R"].ColumnWidth = 11;

                xlWorkSheet.Columns["A:B"].HorizontalAlignment = 2;
                xlWorkSheet.Columns["J:K"].HorizontalAlignment = 2;
                xlWorkSheet.Columns["A:B"].NumberFormat = "yyyy/mm/dd";
                xlWorkSheet.Columns["J:K"].NumberFormat = "yyyy/mm/dd";
                
                xlWorkSheet.Columns["O:R"].HorizontalAlignment = 4;
                xlWorkSheet.Columns["O:R"].NumberFormat = "#,###,###.00";

                xlWorkSheet.get_Range("Q3", "Q3").HorizontalAlignment = 4;
                xlWorkSheet.get_Range("R3", "R3").HorizontalAlignment = 2;
                xlWorkSheet.get_Range("R3", "R3").NumberFormat = "yyyy/mm/dd";

                xlWorkSheet.Cells[i, 17] = "Date: ";
                xlWorkSheet.Cells[i, 18] = DateTime.Now;

                if (noDetail)
                {
                    i += 2;
                    xlWorkSheet.Cells[i, 1] = "No Record Found!";
                    xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                    i++;

                    return i;
                }

                i += 2;
                Int16 k = 1;

                xlWorkSheet.get_Range("A5", "R5").Font.Underline = 2;

                xlWorkSheet.Cells[i, k++] = "prmreg".Contains(optid) ? "Bill Date" : "Book Date";
                xlWorkSheet.Cells[i, k++] = "prmreg".Contains(optid) ? "Book Date" : "";
                xlWorkSheet.Cells[i, k++] = "Policy No";
                xlWorkSheet.Cells[i, k++] = "Endt No";
                xlWorkSheet.Cells[i, k++] = "Source Ref#";
                xlWorkSheet.Cells[i, k++] = "Cede Pol";
                xlWorkSheet.Cells[i, k++] = "Cede Endt";
                xlWorkSheet.Cells[i, k++] = "prmreg".Contains(optid) ? "Install" : "";
                xlWorkSheet.Cells[i, k++] = "prmreg".Contains(optid) ? "Dr/Cr No" : "";
                xlWorkSheet.Cells[i, k++] = "Effect From";
                xlWorkSheet.Cells[i, k++] = "Effect To";
                xlWorkSheet.Cells[i, k++] = "Source#";
                xlWorkSheet.Cells[i, k++] = "Cedent#";
                xlWorkSheet.Cells[i, k++] = "Name";
                xlWorkSheet.Cells[i, k++] = "Gross Premium";
                xlWorkSheet.Cells[i, k++] = "Org Brke";
                xlWorkSheet.Cells[i, k++] = "R/I Comm";
                xlWorkSheet.Cells[i, k++] = "Comm Payable";

                i += 2;

                return i;
            }

            if ("DEK".Contains(vfbus) && "prmdrcr".Contains(optid))
            {
                xlWorkSheet.Cells.Font.Name = "arial";
                xlWorkSheet.Cells.Font.Size = 8;

                xlWorkSheet.Columns["A:B"].ColumnWidth = 8;
                xlWorkSheet.Columns["C:D"].ColumnWidth = 11;
                xlWorkSheet.Columns["E"].ColumnWidth = 7;
                xlWorkSheet.Columns["F"].ColumnWidth = 10;
                xlWorkSheet.Columns["G:H"].ColumnWidth = 8;
                xlWorkSheet.Columns["I"].ColumnWidth = 38;
                xlWorkSheet.Columns["J:Q"].ColumnWidth = 11;

                xlWorkSheet.Columns["A:B"].HorizontalAlignment = 2;
                xlWorkSheet.Columns["G:H"].HorizontalAlignment = 2;

                xlWorkSheet.Columns["A:B"].NumberFormat = "yyyy/mm/dd";
                xlWorkSheet.Columns["G:H"].NumberFormat = "yyyy/mm/dd";

                xlWorkSheet.Columns["J:Q"].HorizontalAlignment = 4;
                xlWorkSheet.Columns["J:Q"].NumberFormat = "#,###,###.00";

                xlWorkSheet.get_Range("P3", "P3").HorizontalAlignment = 4;
                xlWorkSheet.get_Range("Q3", "Q3").HorizontalAlignment = 2;
                xlWorkSheet.get_Range("Q3", "Q3").NumberFormat = "yyyy/mm/dd";

                xlWorkSheet.Cells[i, 16] = "Date: ";
                xlWorkSheet.Cells[i, 17] = DateTime.Now;

                if (noDetail)
                {
                    i += 2;
                    xlWorkSheet.Cells[i, 1] = "No Record Found!";
                    xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                    i++;

                    return i;
                }

                i += 2;
                Int16 k = 1;

                xlWorkSheet.get_Range("A5", "Q5").Font.Underline = 2;

                xlWorkSheet.Cells[i, k++] = "Bill Date";
                xlWorkSheet.Cells[i, k++] = "Book Date";
                xlWorkSheet.Cells[i, k++] = "Policy No";
                xlWorkSheet.Cells[i, k++] = "Endt No";
                xlWorkSheet.Cells[i, k++] = "Install";
                xlWorkSheet.Cells[i, k++] = "Dr/Cr No";
                xlWorkSheet.Cells[i, k++] = "Effect From";
                xlWorkSheet.Cells[i, k++] = "Effect To";
                xlWorkSheet.Cells[i, k++] = "Insured";
                xlWorkSheet.Cells[i, k++] = "Gross Premium";
                xlWorkSheet.Cells[i, k++] = "Discount";
                xlWorkSheet.Cells[i, k++] = "R/I Cost";
                xlWorkSheet.Cells[i, k++] = "Premium Payable";
                xlWorkSheet.Cells[i, k++] = "Comm/ASP Fee";
                xlWorkSheet.Cells[i, k++] = "IA Levy";
                xlWorkSheet.Cells[i, k++] = "ECI/MIB Levy";
                xlWorkSheet.Cells[i, k++] = "Net Payable";

                i += 2;

                return i;
            }

            if (vfbus == "R" && "prmdrcr".Contains(optid))
            {
                xlWorkSheet.Cells.Font.Name = "arial";
                xlWorkSheet.Cells.Font.Size = 8;

                xlWorkSheet.Columns["A:B"].ColumnWidth = 8;
                xlWorkSheet.Columns["C:G"].ColumnWidth = 11;
                xlWorkSheet.Columns["H"].ColumnWidth = 7;
                xlWorkSheet.Columns["I"].ColumnWidth = 10;
                xlWorkSheet.Columns["J:K"].ColumnWidth = 8;
                xlWorkSheet.Columns["L"].ColumnWidth = 38;
                xlWorkSheet.Columns["M:Q"].ColumnWidth = 11;

                xlWorkSheet.Columns["A:B"].HorizontalAlignment = 2;
                //xlWorkSheet.Columns["J:K"].HorizontalAlignment = 2;

                xlWorkSheet.Columns["A:B"].NumberFormat = "yyyy/mm/dd";
                //xlWorkSheet.Columns["J:K"].NumberFormat = "yyyy/mm/dd";

                xlWorkSheet.Columns["M:Q"].HorizontalAlignment = 4;
                xlWorkSheet.Columns["M:Q"].NumberFormat = "#,###,###.00";

                xlWorkSheet.get_Range("P3", "P3").HorizontalAlignment = 4;
                xlWorkSheet.get_Range("Q3", "Q3").HorizontalAlignment = 2;
                xlWorkSheet.get_Range("Q3", "Q3").NumberFormat = "yyyy/mm/dd";

                xlWorkSheet.Cells[i, 16] = "Date: ";
                xlWorkSheet.Cells[i, 17] = DateTime.Now;

                if (noDetail)
                {
                    i += 2;
                    xlWorkSheet.Cells[i, 1] = "No Record Found!";
                    xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                    i++;

                    return i;
                }

                i += 2;
                Int16 k = 1;

                xlWorkSheet.get_Range("A5", "O5").Font.Underline = 2;

                xlWorkSheet.Cells[i, k++] = "Bill Date";
                xlWorkSheet.Cells[i, k++] = "Book Date";
                xlWorkSheet.Cells[i, k++] = "Policy No";
                xlWorkSheet.Cells[i, k++] = "Endt No";
                xlWorkSheet.Cells[i, k++] = "Source Ref#";
                xlWorkSheet.Cells[i, k++] = "Cede Pol";
                xlWorkSheet.Cells[i, k++] = "Cede Endt";
                xlWorkSheet.Cells[i, k++] = "Install";
                xlWorkSheet.Cells[i, k++] = "Dr/Cr No";
                //xlWorkSheet.Cells[i, k++] = "Effect From";
                //xlWorkSheet.Cells[i, k++] = "Effect To";
                xlWorkSheet.Cells[i, k++] = "Sourc#";
                xlWorkSheet.Cells[i, k++] = "Cedent";
                xlWorkSheet.Cells[i, k++] = "Name";
                xlWorkSheet.Cells[i, k++] = "Gross Premium";
                xlWorkSheet.Cells[i, k++] = "Org Brkge";
                xlWorkSheet.Cells[i, k++] = "R/I Comm";
                xlWorkSheet.Cells[i, k++] = "Comm Payable";
                xlWorkSheet.Cells[i, k++] = "Net Payable";

                i += 2;

                return i;
            }

            if ("DEK".Contains(vfbus) && "prmrel".Contains(optid))
            {
                xlWorkSheet.Cells.Font.Name = "arial";
                xlWorkSheet.Cells.Font.Size = 8;

                xlWorkSheet.Columns["A:C"].ColumnWidth = 11;
                xlWorkSheet.Columns["D"].ColumnWidth = 8;

                xlWorkSheet.Columns["E"].ColumnWidth = 7;
                xlWorkSheet.Columns["F"].ColumnWidth = 38;
                xlWorkSheet.Columns["G:J"].ColumnWidth = 11;

                xlWorkSheet.Columns["D"].NumberFormat = "yyyy/mm/dd";
                xlWorkSheet.Columns["G:J"].HorizontalAlignment = 4;
                xlWorkSheet.Columns["G:J"].NumberFormat = "#,###,###.00";

                xlWorkSheet.get_Range("I3", "I3").HorizontalAlignment = 4;
                xlWorkSheet.get_Range("J3", "J3").HorizontalAlignment = 2;
                xlWorkSheet.get_Range("J3", "J3").NumberFormat = "yyyy/mm/dd";

                xlWorkSheet.Cells[i, 9] = "Date: ";
                xlWorkSheet.Cells[i,10] = DateTime.Now;

                if (noDetail)
                {
                    i += 2;
                    xlWorkSheet.Cells[i, 1] = "No Record Found!";
                    xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                    i++;

                    return i;
                }

                i += 2;
                Int16 k = 1;

                xlWorkSheet.get_Range("A5", "J5").Font.Underline = 2;

                xlWorkSheet.Cells[i, k++] = "Policy No";
                xlWorkSheet.Cells[i, k++] = "Endt No";
                xlWorkSheet.Cells[i, k++] = "Dr/Cr No";
                xlWorkSheet.Cells[i, k++] = "Bill Date";
                xlWorkSheet.Cells[i, k++] = "Client";
                xlWorkSheet.Cells[i, k++] = "Name";
                xlWorkSheet.Cells[i, k++] = "Gross Premium";
                xlWorkSheet.Cells[i, k++] = "Discount";
                xlWorkSheet.Cells[i, k++] = "R/I Cost";
                xlWorkSheet.Cells[i, k++] = "Prem Payable";

                i += 2;

                return i;
            }

            if ("DEK".Contains(vfbus) && "plvyia".Contains(optid))
            {
                xlWorkSheet.Cells.Font.Name = "arial";
                xlWorkSheet.Cells.Font.Size = 9;

                xlWorkSheet.Columns["A"].ColumnWidth = 11;
                xlWorkSheet.Columns["B:C"].ColumnWidth = 12;
                xlWorkSheet.Columns["D:F"].ColumnWidth = 11;
                xlWorkSheet.Columns["G"].ColumnWidth = 13;

                xlWorkSheet.Columns["H:I"].ColumnWidth = 9;
                xlWorkSheet.Columns["J"].ColumnWidth = 10.5;
                xlWorkSheet.Columns["K"].ColumnWidth = 3;
			
                xlWorkSheet.Columns["L"].ColumnWidth = 12;
                xlWorkSheet.Columns["M"].ColumnWidth = 5;
                xlWorkSheet.Columns["N"].ColumnWidth = 12;
			
                xlWorkSheet.Columns["A:F"].HorizontalAlignment = 2; // Left
                xlWorkSheet.Columns["G:L"].HorizontalAlignment = 4; // Right

                xlWorkSheet.get_Range("M3", "M3").HorizontalAlignment = 4;  // Right
                xlWorkSheet.get_Range("N3", "N3").HorizontalAlignment = 2;  // Left
                xlWorkSheet.get_Range("H5", "N6").HorizontalAlignment = 3;  // Centre
	
                xlWorkSheet.Columns["A"].NumberFormat = "dd mmm yyyy";
                xlWorkSheet.Columns["D:F"].NumberFormat = "dd mmm yyyy";

            	xlWorkSheet.Columns["G:J"].NumberFormat = "#,###,###.00";
            	xlWorkSheet.Columns["L"].NumberFormat = "#,###,###.00";
            	xlWorkSheet.Columns["N"].NumberFormat = "#,###,###.00";
		    	xlWorkSheet.get_Range("N3","N3").NumberFormat = "dd mmm yyyy";

                xlWorkSheet.Cells[i, 13] = "Date: ";
                xlWorkSheet.Cells[i, 14] = DateTime.Now;

                if (noDetail)
                {
                    i += 2;
                    xlWorkSheet.Cells[i, 1] = "No Record Found!";
                    xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                    i++;

                    return i;
                }

                i += 2;
                Int16 k = 1;

				xlWorkSheet.Cells[i, 9]  = "'(1)";
				xlWorkSheet.Cells[i, 12]  = "'(2)";
				xlWorkSheet.Cells[i, 14]  = "(3) = (1) - (2)";

                i++;
				xlWorkSheet.Cells[i, k++]  = "Book Date";
				xlWorkSheet.Cells[i, k++]  = "Policy No";
				xlWorkSheet.Cells[i, k++]  = "Endt No";
				xlWorkSheet.Cells[i, k++]  = "Org Incept";
				xlWorkSheet.Cells[i, k++]  = "Effect Date";
				xlWorkSheet.Cells[i, k++]  = "Anniversary";
				xlWorkSheet.Cells[i, k++]  = "Policy Premium";
				xlWorkSheet.Cells[i, k++]  = "IA Levy (%)";
				xlWorkSheet.Cells[i, k++]  = "IA Levy ($)";
				xlWorkSheet.Cells[i, k++] = "Sys Rate (%)";

                k++;
				xlWorkSheet.Cells[i, k++]  = "Sys Amt ($)";
                k++;
                xlWorkSheet.Cells[i, k++] = "Difference ($)";
                xlWorkSheet.get_Range("A5", "N6").Font.Underline = 2;  //bottom

                i += 2;

                return i;
            }

            return i;
        }

        public static Int16 WsColTitlePrmExpl(Excel.Worksheet xlWorkSheet, String vfbus,
                        Boolean noDetail, string optid, Int16 startRow)
        {
            Int16 i = startRow;

            if (vfbus == "D")
            {
                xlWorkSheet.Cells.Font.Name = "arial";
                xlWorkSheet.Cells.Font.Size = 8;

                xlWorkSheet.Columns["A"].ColumnWidth = 9;
                xlWorkSheet.Columns["D"].ColumnWidth = 9;
                xlWorkSheet.Columns["E:F"].ColumnWidth = 8;
                xlWorkSheet.Columns["G:H"].ColumnWidth = 8;
                xlWorkSheet.Columns["I"].ColumnWidth = 38;
                xlWorkSheet.Columns["J"].ColumnWidth = 46;
                xlWorkSheet.Columns["K:M"].ColumnWidth = 12;

                xlWorkSheet.Columns["E:F"].HorizontalAlignment = 2;
                xlWorkSheet.Columns["K:M"].HorizontalAlignment = 4;

                xlWorkSheet.Columns["E:F"].NumberFormat = "yyyy/mm/dd";
                xlWorkSheet.Columns["K:M"].NumberFormat = "#,###,###.00";

                xlWorkSheet.get_Range("E5", "F6").HorizontalAlignment = 3;
                xlWorkSheet.get_Range("L3", "L3").HorizontalAlignment = 4;
                xlWorkSheet.get_Range("M3", "M3").HorizontalAlignment = 2;
                xlWorkSheet.get_Range("M3", "M3").NumberFormat = "yyyy/mm/dd";

                xlWorkSheet.Cells[i, 12] = "Date: ";
                xlWorkSheet.Cells[i, 13] = DateTime.Now;

                if (noDetail)
                {
                    i += 2;
                    xlWorkSheet.Cells[i, 1] = "No Record Found!";
                    xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                    i++;

                    return i;
                }

                i += 2;
                Int16 k = 1;

                xlWorkSheet.get_Range("A5", "M6").Font.Underline = 2;

                xlWorkSheet.Cells[i + 1, k++] = "Policy No";
                xlWorkSheet.Cells[i + 1, k++] = "VSeq#";
                xlWorkSheet.Cells[i + 1, k++] = "Var No";
                xlWorkSheet.Cells[i + 1, k++] = "Endt No";
                xlWorkSheet.Cells[i, k] = "Effect From";
                xlWorkSheet.Cells[i + 1, k++] = "Mnt From";
                xlWorkSheet.Cells[i, k] = "Effect To";
                xlWorkSheet.Cells[i + 1, k++] = "Mnt To";
                xlWorkSheet.Cells[i + 1, k++] = "Producer#";
                xlWorkSheet.Cells[i + 1, k++] = "Client#";
                xlWorkSheet.Cells[i + 1, k++] = "Name";
                xlWorkSheet.Cells[i + 1, k++] = "Remark";
                xlWorkSheet.Cells[i + 1, k++] = "Sum Insured";
                xlWorkSheet.Cells[i + 1, k++] = "TPL";
                xlWorkSheet.Cells[i + 1, k++] = "Gross Premium";

                i += 3;

                return i;
            }

            if (vfbus == "R")
            {
                xlWorkSheet.Cells.Font.Name = "arial";
                xlWorkSheet.Cells.Font.Size = 8;

                xlWorkSheet.Columns["A"].ColumnWidth = 9;
                xlWorkSheet.Columns["D"].ColumnWidth = 9;
                xlWorkSheet.Columns["E"].ColumnWidth = 8;
                xlWorkSheet.Columns["F:G"].ColumnWidth = 10;
                xlWorkSheet.Columns["H:I"].ColumnWidth = 8;
                xlWorkSheet.Columns["J:K"].ColumnWidth = 8;
                xlWorkSheet.Columns["L"].ColumnWidth = 45.5;
                xlWorkSheet.Columns["M:O"].ColumnWidth = 12;

                xlWorkSheet.Columns["H:I"].HorizontalAlignment = 2;
                xlWorkSheet.Columns["M:O"].HorizontalAlignment = 4;

                xlWorkSheet.Columns["H:I"].NumberFormat = "yyyy/mm/dd";
                xlWorkSheet.Columns["M:O"].NumberFormat = "#,###,###.00";

                xlWorkSheet.get_Range("H5", "I6").HorizontalAlignment = 3;

                xlWorkSheet.get_Range("N3", "N3").HorizontalAlignment = 4;
                xlWorkSheet.get_Range("O3", "O3").HorizontalAlignment = 2;
                xlWorkSheet.get_Range("O3", "O3").NumberFormat = "yyyy/mm/dd";

                xlWorkSheet.Cells[i, 14] = "Date: ";
                xlWorkSheet.Cells[i, 15] = DateTime.Now;

                if (noDetail)
                {
                    i += 2;
                    xlWorkSheet.Cells[i, 1] = "No Record Found!";
                    xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                    i++;

                    return i;
                }

                i += 2;
                Int16 k = 1;

                xlWorkSheet.get_Range("A5", "O6").Font.Underline = 2;

                xlWorkSheet.Cells[i + 1, k++] = "Policy No";
                xlWorkSheet.Cells[i + 1, k++] = "VSeq#";
                xlWorkSheet.Cells[i + 1, k++] = "Var No";
                xlWorkSheet.Cells[i + 1, k++] = "Endt No";

                xlWorkSheet.Cells[i + 1, k++] = "Source Ref#";
                xlWorkSheet.Cells[i + 1, k++] = "Cede Pol";
                xlWorkSheet.Cells[i + 1, k++] = "Cede Endt";

                xlWorkSheet.Cells[i, k] = "Effect From";
                xlWorkSheet.Cells[i + 1, k++] = "Mnt From";
                xlWorkSheet.Cells[i, k] = "Effect To";
                xlWorkSheet.Cells[i + 1, k++] = "Mnt To";
                xlWorkSheet.Cells[i + 1, k++] = "Source#";
                xlWorkSheet.Cells[i + 1, k++] = "Cedent";
                xlWorkSheet.Cells[i + 1, k++] = "Remark";
                xlWorkSheet.Cells[i + 1, k++] = "Sum Insured";
                xlWorkSheet.Cells[i + 1, k++] = "TPL";
                xlWorkSheet.Cells[i + 1, k++] = "Gross Premium";

                i += 3;

                return i;
            }

            return i;
        }


        public static Int16 WsColTitlePrmFacOut(Excel.Worksheet xlWorkSheet, Boolean noDetail, string optid, Int16 startRow)
        {
            Int16 i = startRow;

            xlWorkSheet.Cells.Font.Name = "arial";
            xlWorkSheet.Cells.Font.Size = 8;

            xlWorkSheet.Columns["A:C"].ColumnWidth = 11;
            xlWorkSheet.Columns["D:J"].ColumnWidth = 12;

            xlWorkSheet.Columns["D:J"].HorizontalAlignment = 4;

            xlWorkSheet.Columns["D:J"].NumberFormat = "#,###,###.00";

            xlWorkSheet.get_Range("I3", "I3").HorizontalAlignment = 4;
            xlWorkSheet.get_Range("J3", "J3").HorizontalAlignment = 2;
            xlWorkSheet.get_Range("J3", "J3").NumberFormat = "yyyy/mm/dd";

            xlWorkSheet.Cells[i, 9] = "Date: ";
            xlWorkSheet.Cells[i,10] = DateTime.Now;

            if (noDetail)
            {
                i += 2;
                xlWorkSheet.Cells[i, 1] = "No Record Found!";
                xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                i++;

                return i;
            }

            i += 2;
            Int16 k = 1;

            xlWorkSheet.get_Range("A5", "J5").Font.Underline = 2;
            xlWorkSheet.Cells[i, k++] = "Policy No";
            xlWorkSheet.Cells[i, k++] = "Endt No";
            xlWorkSheet.Cells[i, k++] = "Dr/Cr/No.";
            xlWorkSheet.Cells[i, k++] = "Gross Fac Prem";
            xlWorkSheet.Cells[i, k++] = "R/I Commission";
            xlWorkSheet.Cells[i, k++] = "Net Premium";
            xlWorkSheet.Cells[i, k++] = "Misc Charge (1)";
            xlWorkSheet.Cells[i, k++] = "Misc Charge (2)";
            xlWorkSheet.Cells[i, k++] = "Misc Charge (3)";
            xlWorkSheet.Cells[i, k++] = "Payable Amount";

            //i += 2;

            return i;
        }


        public static void WsPrmRegDetail(Excel.Worksheet xlWorkSheet, DataTable dt, string vfbus, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fclsseq = "", lc_fdbtr = "", lc_fpcode = "";

            if ("DEK".Contains(vfbus) && "prmreg|polprm".Contains(optid))
            {
                decimal ln_scase = 0, ln_sgpm = 0, ln_stdamt = 0, ln_sricost = 0, ln_scomamt = 0,
                        ln_slvyamt1 = 0, ln_slvyamt2 = 0, ln_slvyamt3 = 0, ln_slvyamta = 0;
                decimal ln_tcase = 0, ln_tgpm = 0, ln_ttdamt = 0, ln_tricost = 0, ln_tcomamt = 0,
                        ln_tlvyamt1 = 0, ln_tlvyamt2 = 0, ln_tlvyamt3 = 0, ln_tlvyamta = 0;

                foreach (DataRow dr in dt.Rows)
                {
                    if (lc_fclsseq != dr["fclsseq"].ToString().Trim())
                    {
                        if (iRow > startRow)
                        {
                            xlWorkSheet.Cells[iRow, 1] = "No. of Cases: "+ln_scase.ToString("N0");
                            k = 11;

                            xlWorkSheet.Cells[iRow, k++] = "Total: ";
                            xlWorkSheet.Cells[iRow, k++] = ln_sgpm;
                            xlWorkSheet.Cells[iRow, k++] = ln_stdamt;
                            xlWorkSheet.Cells[iRow, k++] = ln_sricost;
                            xlWorkSheet.Cells[iRow, k++] = ln_scomamt;
                            xlWorkSheet.Cells[iRow, k++] = ln_slvyamta;
                            xlWorkSheet.Cells[iRow, k++] = ln_slvyamt1;
                            xlWorkSheet.Cells[iRow, k++] = ln_slvyamt2;
                            xlWorkSheet.Cells[iRow, k++] = ln_slvyamt3;

                            xlWorkSheet.get_Range(String.Format("K{0}", iRow)).Columns.HorizontalAlignment = 4;

                            iRow += 2;
                        }

                        lc_fclsseq = dr["fclsseq"].ToString().Trim();
                        xlWorkSheet.Cells[iRow, 1] = dr["fclsname"].ToString();

                        xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

        				ln_scase = 0; ln_sgpm = 0; ln_stdamt = 0; ln_sricost = 0; ln_scomamt= 0;
                        ln_slvyamt1 = 0; ln_slvyamt2 = 0; ln_slvyamt3 = 0;

                        iRow += 2;
                    }

                    decimal fgpm = Convert.ToDecimal(dr["fgpm"]);
                    decimal ftdamt = Convert.ToDecimal(dr["ftdamt"]);
                    decimal fricost = Convert.ToDecimal(dr["fricost"]);
                    decimal fcomamt = Convert.ToDecimal(dr["fcomamt"]);
                    decimal flvyamta = Convert.ToDecimal(dr["flvyamta"]);
                    decimal flvyamt1 = Convert.ToDecimal(dr["flvyamt1"]);
                    decimal flvyamt2 = Convert.ToDecimal(dr["flvyamt2"]);
                    decimal flvyamt3 = Convert.ToDecimal(dr["flvyamt3"]);

                    k = 1;
                    xlWorkSheet.Cells[iRow, k++] = "prmreg".Contains(optid) ? dr["fbilldate"] : dr["fbkdate"];
                    xlWorkSheet.Cells[iRow, k++] = "prmreg".Contains(optid) ? dr["fbkdate"] : "";
                    xlWorkSheet.Cells[iRow, k] = dr["fpolno"];
                    xlWorkSheet.Cells[iRow+1, k++] = dr["fcedepol"];
                    xlWorkSheet.Cells[iRow, k] = dr["fendtno"];
                    xlWorkSheet.Cells[iRow+1, k++] = dr["fcedeendt"];
                    xlWorkSheet.Cells[iRow, k++] = "prmreg".Contains(optid) ? "'"+dr["finstall"].ToString().Trim() + "/" + dr["ftotinstal"].ToString().Trim() : "";
                    xlWorkSheet.Cells[iRow, k++] = "prmreg".Contains(optid) ? dr["finvno"] : "";
                    xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fefffr"]);
                    xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["feffto"]);
                    xlWorkSheet.Cells[iRow, k++] = dr["fprdr"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fclnt"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fcdesc"]+((char) 13).ToString()+((char) 10).ToString();
                    xlWorkSheet.Cells[iRow, k++] = fgpm;
                    xlWorkSheet.Cells[iRow, k++] = ftdamt;
                    xlWorkSheet.Cells[iRow, k++] = fricost;
                    xlWorkSheet.Cells[iRow, k++] = fcomamt;
                    xlWorkSheet.Cells[iRow, k++] = flvyamta;
                    xlWorkSheet.Cells[iRow, k++] = flvyamt1;
                    xlWorkSheet.Cells[iRow, k++] = flvyamt2;
                    xlWorkSheet.Cells[iRow, k++] = flvyamt3;

                    xlWorkSheet.get_Range(String.Format("K{0}", iRow),String.Format("K{0}", iRow+1)).Merge();
                    xlWorkSheet.get_Range(String.Format("K{0}", iRow),String.Format("K{0}", iRow+1)).HorizontalAlignment = 1;
                    xlWorkSheet.get_Range(String.Format("K{0}", iRow),String.Format("K{0}", iRow+1)).VerticalAlignment = 1;
                    xlWorkSheet.get_Range(String.Format("K{0}", iRow),String.Format("K{0}", iRow)).RowHeight = 11.25;

                    ln_scase   = ln_scase + 1;
                    ln_sgpm    = ln_sgpm + fgpm;
                    ln_stdamt  = ln_stdamt + ftdamt;
                    ln_sricost = ln_sricost + fricost;
                    ln_scomamt = ln_scomamt + fcomamt;
                    ln_slvyamta = ln_slvyamta + flvyamta;
                    ln_slvyamt1 = ln_slvyamt1 + flvyamt1;
                    ln_slvyamt2= ln_slvyamt2 + flvyamt2;
                    ln_slvyamt3= ln_slvyamt3 + flvyamt3;

                    ln_tcase   = ln_tcase + 1;
                    ln_tgpm    = ln_tgpm + fgpm;
                    ln_ttdamt  = ln_ttdamt + ftdamt;
                    ln_tricost = ln_tricost + fricost;
                    ln_tcomamt = ln_tcomamt + fcomamt;
                    ln_tlvyamta = ln_tlvyamta + flvyamta;
                    ln_tlvyamt1 = ln_tlvyamt1 + flvyamt1;
                    ln_tlvyamt2= ln_tlvyamt2 + flvyamt2;
                    ln_tlvyamt3= ln_tlvyamt3 + flvyamt3;

                    iRow += 3;
                }

                xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");
                k = 11;

                xlWorkSheet.Cells[iRow, k++] = "Total: ";
                xlWorkSheet.Cells[iRow, k++] = ln_sgpm;
                xlWorkSheet.Cells[iRow, k++] = ln_stdamt;
                xlWorkSheet.Cells[iRow, k++] = ln_sricost;
                xlWorkSheet.Cells[iRow, k++] = ln_scomamt;
                xlWorkSheet.Cells[iRow, k++] = ln_slvyamta;
                xlWorkSheet.Cells[iRow, k++] = ln_slvyamt1;
                xlWorkSheet.Cells[iRow, k++] = ln_slvyamt2;
                xlWorkSheet.Cells[iRow, k++] = ln_slvyamt3;

                xlWorkSheet.get_Range(String.Format("K{0}", iRow)).Columns.HorizontalAlignment = 4;

                iRow += 2;

                xlWorkSheet.Cells[iRow, 1] = "Total of Cases: " + ln_tcase.ToString("N0");
                k = 11;

                xlWorkSheet.Cells[iRow, k++] = "Grand Total: ";
                xlWorkSheet.Cells[iRow, k++] = ln_tgpm;
                xlWorkSheet.Cells[iRow, k++] = ln_ttdamt;
                xlWorkSheet.Cells[iRow, k++] = ln_tricost;
                xlWorkSheet.Cells[iRow, k++] = ln_tcomamt;
                xlWorkSheet.Cells[iRow, k++] = ln_tlvyamta;
                xlWorkSheet.Cells[iRow, k++] = ln_tlvyamt1;
                xlWorkSheet.Cells[iRow, k++] = ln_tlvyamt2;
                xlWorkSheet.Cells[iRow, k++] = ln_tlvyamt3;

                xlWorkSheet.get_Range(String.Format("K{0}", iRow)).Columns.HorizontalAlignment = 4;

                return;
            }

            if (vfbus == "R" && "prmreg|polprm".Contains(optid))
            {
                decimal ln_scase = 0, ln_sgpm = 0, ln_stdamt = 0, ln_scomamt = 0, ln_scompay = 0; 
                decimal ln_tcase = 0, ln_tgpm = 0, ln_ttdamt = 0, ln_tcomamt = 0, ln_tcompay = 0;

                foreach (DataRow dr in dt.Rows)
                {
                    if (lc_fclsseq != dr["fclsseq"].ToString().Trim())
                    {
                        if (iRow > startRow)
                        {
                            xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");
                            k = 14;

                            xlWorkSheet.Cells[iRow, k++] = "Total: ";
                            xlWorkSheet.Cells[iRow, k++] = ln_sgpm;
                            xlWorkSheet.Cells[iRow, k++] = ln_stdamt;
                            xlWorkSheet.Cells[iRow, k++] = ln_scomamt;
                            xlWorkSheet.Cells[iRow, k++] = ln_scompay;

                            xlWorkSheet.get_Range(String.Format("N{0}", iRow)).Columns.HorizontalAlignment = 4;

                            iRow += 2;
                        }

                        lc_fclsseq = dr["fclsseq"].ToString().Trim();
                        xlWorkSheet.Cells[iRow, 1] = dr["fclsname"].ToString();

                        xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                        ln_scase = 0; ln_sgpm = 0; ln_stdamt = 0; ln_scomamt = 0; ln_scompay = 0;

                        iRow += 2;
                    }

                    decimal fgpm = Convert.ToDecimal(dr["fgpm"]);
                    decimal ftdamt = Convert.ToDecimal(dr["ftdamt"]);
                    decimal fcomamt = Convert.ToDecimal(dr["fcomamt"]);

                    k = 1;
                    xlWorkSheet.Cells[iRow, k++] = "prmreg".Contains(optid) ? dr["fbilldate"] : dr["fbkdate"];
                    xlWorkSheet.Cells[iRow, k++] = "prmreg".Contains(optid) ? dr["fbkdate"] : "";
                    xlWorkSheet.Cells[iRow, k++] = dr["fpolno"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fendtno"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fsrcref"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fcedepol"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fcedeendt"];
                    xlWorkSheet.Cells[iRow, k++] = "prmreg".Contains(optid) ? "'" + dr["finstall"].ToString().Trim() + "/" + dr["ftotinstal"].ToString().Trim() : "";
                    xlWorkSheet.Cells[iRow, k++] = "prmreg".Contains(optid) ? dr["finvno"] : "";
                    xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fefffr"]);
                    xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["feffto"]);
                    xlWorkSheet.Cells[iRow, k++] = dr["fprdr"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fclnt"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fcdesc"] + ((char)13).ToString() + ((char)10).ToString();
                    xlWorkSheet.Cells[iRow, k++] = fgpm;
                    xlWorkSheet.Cells[iRow, k++] = ftdamt;
                    xlWorkSheet.Cells[iRow, k++] = fcomamt;
                    xlWorkSheet.Cells[iRow, k++] = ftdamt+fcomamt;

                    xlWorkSheet.get_Range(String.Format("N{0}", iRow), String.Format("N{0}", iRow + 1)).Merge();
                    xlWorkSheet.get_Range(String.Format("N{0}", iRow), String.Format("N{0}", iRow + 1)).HorizontalAlignment = 1;
                    xlWorkSheet.get_Range(String.Format("N{0}", iRow), String.Format("N{0}", iRow + 1)).VerticalAlignment = 1;
                    xlWorkSheet.get_Range(String.Format("N{0}", iRow), String.Format("N{0}", iRow)).RowHeight = 11.25;

                    ln_scase = ln_scase + 1;
                    ln_sgpm = ln_sgpm + fgpm;
                    ln_stdamt = ln_stdamt + ftdamt;
                    ln_scomamt = ln_scomamt + fcomamt;
                    ln_scompay = ln_scompay + ftdamt + fcomamt;

                    ln_tcase = ln_tcase + 1;
                    ln_tgpm = ln_tgpm + fgpm;
                    ln_ttdamt = ln_ttdamt + ftdamt;
                    ln_tcomamt = ln_tcomamt + fcomamt;
                    ln_tcompay = ln_tcompay + ftdamt + fcomamt;

                    iRow += 3;
                }

                xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");
                k = 14;

                xlWorkSheet.Cells[iRow, k++] = "Total: ";
                xlWorkSheet.Cells[iRow, k++] = ln_sgpm;
                xlWorkSheet.Cells[iRow, k++] = ln_stdamt;
                xlWorkSheet.Cells[iRow, k++] = ln_scomamt;
                xlWorkSheet.Cells[iRow, k++] = ln_scompay;

                xlWorkSheet.get_Range(String.Format("N{0}", iRow)).Columns.HorizontalAlignment = 4;

                iRow += 2;

                xlWorkSheet.Cells[iRow, 1] = "Total of Cases: " + ln_tcase.ToString("N0");
                k = 14;

                xlWorkSheet.Cells[iRow, k++] = "Grand Total: ";
                xlWorkSheet.Cells[iRow, k++] = ln_tgpm;
                xlWorkSheet.Cells[iRow, k++] = ln_ttdamt;
                xlWorkSheet.Cells[iRow, k++] = ln_tcomamt;
                xlWorkSheet.Cells[iRow, k++] = ln_tcompay;

                xlWorkSheet.get_Range(String.Format("N{0}", iRow)).Columns.HorizontalAlignment = 4;

                return;
            }

            if ("DEK".Contains(vfbus) && "prmdrcr".Contains(optid))
            {
                decimal ln_scase = 0, ln_sgpm = 0, ln_stdamt = 0, ln_sricost = 0, ln_scomamt = 0,
                        ln_slvy = 0, ln_spayable = 0, ln_sppay = 0, ln_slvya = 0;
                decimal ln_tcase = 0, ln_tgpm = 0, ln_ttdamt = 0, ln_tricost = 0, ln_tcomamt = 0,
                        ln_tlvy = 0, ln_tpayable = 0, ln_tppay = 0, ln_tlvya = 0;

                int ln_pos;

                String lc_fdesc = "";

                foreach (DataRow dr in dt.Rows)
                {
                    if (lc_fdbtr != dr["fdbtr"].ToString().Trim())
                    {
                        if (iRow > startRow)
                        {
                            xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");
                            k = 9;

                            xlWorkSheet.Cells[iRow, k++] = "Total: ";
                            xlWorkSheet.Cells[iRow, k++] = ln_sgpm;
                            xlWorkSheet.Cells[iRow, k++] = ln_stdamt;
                            xlWorkSheet.Cells[iRow, k++] = ln_sricost;
                            xlWorkSheet.Cells[iRow, k++] = ln_sppay;
                            xlWorkSheet.Cells[iRow, k++] = ln_scomamt;
                            xlWorkSheet.Cells[iRow, k++] = ln_slvya;
                            xlWorkSheet.Cells[iRow, k++] = ln_slvy;
                            xlWorkSheet.Cells[iRow, k++] = ln_spayable;

                            xlWorkSheet.get_Range(String.Format("I{0}", iRow)).Columns.HorizontalAlignment = 4;

                            iRow += 2;
                        }

                        lc_fdbtr = dr["fdbtr"].ToString().Trim();
                        lc_fdesc = dr["fdesc"].ToString().Trim();
                        ln_pos = lc_fdesc.IndexOf(((char)13).ToString());
                        lc_fdesc = ln_pos < 0 ? lc_fdesc : lc_fdesc.Substring(0, ln_pos);
                        
                        xlWorkSheet.Cells[iRow, 1] = dr["fdbtr"].ToString().Trim() + " - " + lc_fdesc;

                        xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                        ln_scase = 0; ln_sgpm = 0; ln_stdamt = 0; ln_sricost = 0; ln_scomamt = 0;
                        ln_slvy = 0; ln_spayable = 0;

                        iRow += 2;
                    }

                    decimal fgpm = Convert.ToDecimal(dr["fgpm"]);
                    decimal ftdamt = Convert.ToDecimal(dr["ftdamt"]);
                    decimal fricost = Convert.ToDecimal(dr["fricost"]);
                    decimal fcomamt = Convert.ToDecimal(dr["fcomamt"]);
                    decimal flvyamta = Convert.ToDecimal(dr["flvyamta"]);
                    decimal flvyamt1 = Convert.ToDecimal(dr["flvyamt1"]);
                    decimal flvyamt2 = Convert.ToDecimal(dr["flvyamt2"]);
                    decimal flvyamt3 = Convert.ToDecimal(dr["flvyamt3"]);

                    k = 1;
                    xlWorkSheet.Cells[iRow, k++] = dr["fbilldate"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fbkdate"];
                    xlWorkSheet.Cells[iRow, k] = dr["fpolno"];
                    xlWorkSheet.Cells[iRow + 1, k++] = dr["fcedepol"];
                    xlWorkSheet.Cells[iRow, k] = dr["fendtno"];
                    xlWorkSheet.Cells[iRow + 1, k++] = dr["fcedeendt"];

                    xlWorkSheet.Cells[iRow, k++] = "'" + dr["finstall"].ToString().Trim() + "/" + dr["ftotinstal"].ToString().Trim();
                    xlWorkSheet.Cells[iRow, k++] = dr["finvno"];
                    xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fefffr"]);
                    xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["feffto"]);
                    xlWorkSheet.Cells[iRow, k++] = dr["fcdesc"] + ((char)13).ToString() + ((char)10).ToString();
                    xlWorkSheet.Cells[iRow, k++] = fgpm;
                    xlWorkSheet.Cells[iRow, k++] = ftdamt;
                    xlWorkSheet.Cells[iRow, k++] = fricost;
                    xlWorkSheet.Cells[iRow, k++] = fgpm - ftdamt - fricost;
                    xlWorkSheet.Cells[iRow, k++] = fcomamt;
                    xlWorkSheet.Cells[iRow, k++] = flvyamta;
                    xlWorkSheet.Cells[iRow, k++] = flvyamt1 + flvyamt2 + flvyamt3;
                    xlWorkSheet.Cells[iRow, k++] = fgpm - ftdamt - fricost - fcomamt + flvyamt1 + flvyamt2 + flvyamt3 + flvyamta;

                    xlWorkSheet.get_Range(String.Format("I{0}", iRow), String.Format("I{0}", iRow + 1)).Merge();
                    xlWorkSheet.get_Range(String.Format("I{0}", iRow), String.Format("I{0}", iRow + 1)).HorizontalAlignment = 1;
                    xlWorkSheet.get_Range(String.Format("I{0}", iRow), String.Format("I{0}", iRow + 1)).VerticalAlignment = 1;
                    xlWorkSheet.get_Range(String.Format("I{0}", iRow), String.Format("I{0}", iRow)).RowHeight = 11.25;

                    ln_scase = ln_scase + 1;
                    ln_sgpm = ln_sgpm + fgpm;
                    ln_stdamt = ln_stdamt + ftdamt;
                    ln_sricost = ln_sricost + fricost;
                    ln_sppay = ln_sppay + fgpm - ftdamt - fricost;
                    ln_scomamt = ln_scomamt + fcomamt;
                    ln_slvya = ln_slvya + flvyamta;
                    ln_slvy = ln_slvy + flvyamt1 + flvyamt2 + flvyamt3;
                    ln_spayable = ln_spayable + fgpm - ftdamt - fricost - fcomamt + flvyamt1 + flvyamt2 + flvyamt3 + flvyamta;

                    ln_tcase = ln_tcase + 1;
                    ln_tgpm = ln_tgpm + fgpm;
                    ln_ttdamt = ln_ttdamt + ftdamt;
                    ln_tricost = ln_tricost + fricost;
                    ln_tppay = ln_tppay + fgpm - ftdamt - fricost;
                    ln_tcomamt = ln_tcomamt + fcomamt;
                    ln_tlvya = ln_tlvya + flvyamta;
                    ln_tlvy = ln_tlvy + flvyamt1 + flvyamt2 + flvyamt3;
                    ln_tpayable = ln_tpayable + fgpm - ftdamt - fricost - fcomamt + flvyamt1 + flvyamt2 + flvyamt3 + flvyamta;

                    iRow += 3;
                }

                xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");
                k = 9;

                xlWorkSheet.Cells[iRow, k++] = "Total: ";
                xlWorkSheet.Cells[iRow, k++] = ln_sgpm;
                xlWorkSheet.Cells[iRow, k++] = ln_stdamt;
                xlWorkSheet.Cells[iRow, k++] = ln_sricost;
                xlWorkSheet.Cells[iRow, k++] = ln_sppay;
                xlWorkSheet.Cells[iRow, k++] = ln_scomamt;
                xlWorkSheet.Cells[iRow, k++] = ln_slvya;
                xlWorkSheet.Cells[iRow, k++] = ln_slvy;
                xlWorkSheet.Cells[iRow, k++] = ln_spayable;

                xlWorkSheet.get_Range(String.Format("I{0}", iRow)).Columns.HorizontalAlignment = 4;

                iRow += 2;

                xlWorkSheet.Cells[iRow, 1] = "Total of Cases: " + ln_tcase.ToString("N0");
                k = 9;

                xlWorkSheet.Cells[iRow, k++] = "Grand Total: ";
                xlWorkSheet.Cells[iRow, k++] = ln_tgpm;
                xlWorkSheet.Cells[iRow, k++] = ln_ttdamt;
                xlWorkSheet.Cells[iRow, k++] = ln_tricost;
                xlWorkSheet.Cells[iRow, k++] = ln_tppay;
                xlWorkSheet.Cells[iRow, k++] = ln_tcomamt;
                xlWorkSheet.Cells[iRow, k++] = ln_tlvya;
                xlWorkSheet.Cells[iRow, k++] = ln_tlvy;
                xlWorkSheet.Cells[iRow, k++] = ln_tpayable;

                xlWorkSheet.get_Range(String.Format("I{0}", iRow)).Columns.HorizontalAlignment = 4;

                return;
            }

            if (vfbus == "R" && "prmdrcr".Contains(optid))
            {
                decimal ln_scase = 0, ln_sgpm = 0, ln_stdamt = 0, ln_scomamt = 0, ln_scompay = 0, ln_spayable = 0;
                decimal ln_tcase = 0, ln_tgpm = 0, ln_ttdamt = 0, ln_tcomamt = 0, ln_tcompay = 0, ln_tpayable = 0;

                int ln_pos;

                String lc_fdesc = "";


                foreach (DataRow dr in dt.Rows)
                {
                    if (lc_fdbtr != dr["fdbtr"].ToString().Trim())
                    {
                        if (iRow > startRow)
                        {
                            xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");
                            k = 12;

                            xlWorkSheet.Cells[iRow, k++] = "Total: ";
                            xlWorkSheet.Cells[iRow, k++] = ln_sgpm;
                            xlWorkSheet.Cells[iRow, k++] = ln_stdamt;
                            xlWorkSheet.Cells[iRow, k++] = ln_scomamt;
                            xlWorkSheet.Cells[iRow, k++] = ln_scompay;
                            xlWorkSheet.Cells[iRow, k++] = ln_spayable;
                            
                            xlWorkSheet.get_Range(String.Format("L{0}", iRow)).Columns.HorizontalAlignment = 4;

                            iRow += 2;
                        }

                        lc_fdbtr = dr["fdbtr"].ToString().Trim();
                        lc_fdesc = dr["fdesc"].ToString().Trim();
                        ln_pos = lc_fdesc.IndexOf(((char)13).ToString());
                        lc_fdesc = ln_pos < 0 ? lc_fdesc : lc_fdesc.Substring(0, ln_pos);

                        xlWorkSheet.Cells[iRow, 1] = dr["fdbtr"].ToString().Trim() + " - " + lc_fdesc;

                        xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                        ln_scase = 0; ln_sgpm = 0; ln_stdamt = 0; ln_scomamt = 0; ln_scompay = 0;
                        ln_spayable = 0;

                        iRow += 2;
                    }

                    decimal fgpm = Convert.ToDecimal(dr["fgpm"]);
                    decimal ftdamt = Convert.ToDecimal(dr["ftdamt"]);
                    decimal fcomamt = Convert.ToDecimal(dr["fcomamt"]);

                    k = 1;
                    xlWorkSheet.Cells[iRow, k++] = dr["fbilldate"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fbkdate"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fpolno"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fendtno"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fsrcref"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fcedepol"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fcedeendt"];
                    xlWorkSheet.Cells[iRow, k++] = "'"+dr["finstall"].ToString().Trim() + "/" + dr["ftotinstal"].ToString().Trim();
                    xlWorkSheet.Cells[iRow, k++] = dr["finvno"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fprdr"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fclnt"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fcdesc"] + ((char)13).ToString() + ((char)10).ToString();
                    xlWorkSheet.Cells[iRow, k++] = fgpm;
                    xlWorkSheet.Cells[iRow, k++] = ftdamt;
                    xlWorkSheet.Cells[iRow, k++] = fcomamt;
                    xlWorkSheet.Cells[iRow, k++] = ftdamt + fcomamt;
                    xlWorkSheet.Cells[iRow, k++] = fgpm - ftdamt - fcomamt;

                    xlWorkSheet.get_Range(String.Format("L{0}", iRow), String.Format("L{0}", iRow + 1)).Merge();
                    xlWorkSheet.get_Range(String.Format("L{0}", iRow), String.Format("L{0}", iRow + 1)).HorizontalAlignment = 1;
                    xlWorkSheet.get_Range(String.Format("L{0}", iRow), String.Format("L{0}", iRow + 1)).VerticalAlignment = 1;
                    xlWorkSheet.get_Range(String.Format("L{0}", iRow), String.Format("L{0}", iRow)).RowHeight = 11.25;

                    ln_scase = ln_scase + 1;
                    ln_sgpm = ln_sgpm + fgpm;
                    ln_stdamt = ln_stdamt + ftdamt;
                    ln_scomamt = ln_scomamt + fcomamt;
                    ln_scompay = ln_scompay + ftdamt + fcomamt;
                    ln_spayable = ln_spayable + fgpm - ftdamt - fcomamt;

                    ln_tcase = ln_tcase + 1;
                    ln_tgpm = ln_tgpm + fgpm;
                    ln_ttdamt = ln_ttdamt + ftdamt;
                    ln_tcomamt = ln_tcomamt + fcomamt;
                    ln_tcompay = ln_tcompay + ftdamt + fcomamt;
                    ln_tpayable = ln_tpayable + fgpm - ftdamt - fcomamt;

                    iRow += 3;
                }

                xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");
                k = 12;

                xlWorkSheet.Cells[iRow, k++] = "Total: ";
                xlWorkSheet.Cells[iRow, k++] = ln_sgpm;
                xlWorkSheet.Cells[iRow, k++] = ln_stdamt;
                xlWorkSheet.Cells[iRow, k++] = ln_scomamt;
                xlWorkSheet.Cells[iRow, k++] = ln_scompay;
                xlWorkSheet.Cells[iRow, k++] = ln_spayable;

                xlWorkSheet.get_Range(String.Format("L{0}", iRow)).Columns.HorizontalAlignment = 4;

                iRow += 2;

                xlWorkSheet.Cells[iRow, 1] = "Total of Cases: " + ln_tcase.ToString("N0");
                k = 12;

                xlWorkSheet.Cells[iRow, k++] = "Grand Total: ";
                xlWorkSheet.Cells[iRow, k++] = ln_tgpm;
                xlWorkSheet.Cells[iRow, k++] = ln_ttdamt;
                xlWorkSheet.Cells[iRow, k++] = ln_tcomamt;
                xlWorkSheet.Cells[iRow, k++] = ln_tcompay;
                xlWorkSheet.Cells[iRow, k++] = ln_tpayable;

                xlWorkSheet.get_Range(String.Format("L{0}", iRow)).Columns.HorizontalAlignment = 4;

                return;
            }

            if ("DEK".Contains(vfbus) && "prmrel".Contains(optid))
            {
                decimal ln_scase = 0, ln_sgpm = 0, ln_stdamt = 0, ln_sricost = 0, ln_spayable = 0;
                decimal ln_tcase = 0, ln_tgpm = 0, ln_ttdamt = 0, ln_tricost = 0, ln_tpayable = 0;

                String lc_fdesc = "";

                foreach (DataRow dr in dt.Rows)
                {
                    if (lc_fpcode != dr["finter"].ToString().Trim()+dr["fsubside"].ToString())
                    {
                        if (iRow > startRow)
                        {
                            xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");
                            k = 6;

                            xlWorkSheet.Cells[iRow, k++] = "Total: ";
                            xlWorkSheet.Cells[iRow, k++] = ln_sgpm;
                            xlWorkSheet.Cells[iRow, k++] = ln_stdamt;
                            xlWorkSheet.Cells[iRow, k++] = ln_sricost;
                            xlWorkSheet.Cells[iRow, k++] = ln_spayable;

                            xlWorkSheet.get_Range(String.Format("F{0}", iRow)).Columns.HorizontalAlignment = 4;

                            iRow += 2;
                        }

                        lc_fpcode = dr["finter"].ToString().Trim() + dr["fsubside"].ToString();
                        lc_fdesc = dr["fintdesc"].ToString().Trim()+": "+dr["fsubsdesc"].ToString().Trim();

                         xlWorkSheet.Cells[iRow, 1] = lc_fdesc;

                        xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                        ln_scase = 0; ln_sgpm = 0; ln_stdamt = 0; ln_sricost = 0; ln_spayable = 0;

                        iRow += 2;
                    }

                    decimal fgpm = Convert.ToDecimal(dr["fgpm"]);
                    decimal ftdamt = Convert.ToDecimal(dr["ftdamt"]);
                    decimal fricost = Convert.ToDecimal(dr["fricost"]);
                    decimal fpayable = Convert.ToDecimal(dr["fpayable"]);

                    k = 1;
                    xlWorkSheet.Cells[iRow, k] = dr["fpolno"];
                    xlWorkSheet.Cells[iRow + 1, k++] = dr["fcedepol"];
                    xlWorkSheet.Cells[iRow, k] = dr["fendtno"];
                    xlWorkSheet.Cells[iRow + 1, k++] = dr["fcedeendt"];
                    xlWorkSheet.Cells[iRow, k++] = dr["finvno"];
                    xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fbilldate"]);
                    xlWorkSheet.Cells[iRow, k++] = dr["fclnt"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fcdesc"] + ((char)13).ToString() + ((char)10).ToString();
                    xlWorkSheet.Cells[iRow, k++] = fgpm;
                    xlWorkSheet.Cells[iRow, k++] = ftdamt;
                    xlWorkSheet.Cells[iRow, k++] = fricost;
                    xlWorkSheet.Cells[iRow, k++] = fpayable;
                                        
                    xlWorkSheet.get_Range(String.Format("F{0}", iRow), String.Format("F{0}", iRow + 1)).Merge();
                    xlWorkSheet.get_Range(String.Format("F{0}", iRow), String.Format("F{0}", iRow + 1)).HorizontalAlignment = 1;
                    xlWorkSheet.get_Range(String.Format("F{0}", iRow), String.Format("F{0}", iRow + 1)).VerticalAlignment = 1;
                    xlWorkSheet.get_Range(String.Format("F{0}", iRow), String.Format("F{0}", iRow)).RowHeight = 11.25;

                    ln_scase = ln_scase + 1;
                    ln_sgpm = ln_sgpm + fgpm;
                    ln_stdamt = ln_stdamt + ftdamt;
                    ln_sricost = ln_sricost + fricost;
                    ln_spayable = ln_spayable + fpayable;

                    ln_tcase = ln_tcase + 1;
                    ln_tgpm = ln_tgpm + fgpm;
                    ln_ttdamt = ln_ttdamt + ftdamt;
                    ln_tricost = ln_tricost + fricost;
                    ln_tpayable = ln_tpayable + fpayable;

                    iRow += 3;
                }

                xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");
                k = 6;

                xlWorkSheet.Cells[iRow, k++] = "Total: ";
                xlWorkSheet.Cells[iRow, k++] = ln_sgpm;
                xlWorkSheet.Cells[iRow, k++] = ln_stdamt;
                xlWorkSheet.Cells[iRow, k++] = ln_sricost;
                xlWorkSheet.Cells[iRow, k++] = ln_spayable;

                xlWorkSheet.get_Range(String.Format("F{0}", iRow)).Columns.HorizontalAlignment = 4;

                iRow += 2;

                xlWorkSheet.Cells[iRow, 1] = "Total of Cases: " + ln_tcase.ToString("N0");
                k = 6;

                xlWorkSheet.Cells[iRow, k++] = "Grand Total: ";
                xlWorkSheet.Cells[iRow, k++] = ln_tgpm;
                xlWorkSheet.Cells[iRow, k++] = ln_ttdamt;
                xlWorkSheet.Cells[iRow, k++] = ln_tricost;
                xlWorkSheet.Cells[iRow, k++] = ln_tpayable;

                xlWorkSheet.get_Range(String.Format("F{0}", iRow)).Columns.HorizontalAlignment = 4;

                return;
            }

            if ("DEK".Contains(vfbus) && "plvyia".Contains(optid))
            {
                decimal ln_scase = 0, ln_sgpm = 0, ln_slvyamta = 0, ln_ssysamta = 0, ln_slvydiffa = 0;
                decimal ln_tcase = 0, ln_tgpm = 0, ln_tlvyamta = 0, ln_tsysamta = 0, ln_tlvydiffa = 0;

                String lc_plvya = "";

                foreach (DataRow dr in dt.Rows)
                {
                    if (lc_plvya != dr["plvya"].ToString().Trim())
                    {
                        if (iRow > startRow)
                        {
                            xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");
                            k = 6;

                            xlWorkSheet.Cells[iRow, k++] = "Sub Total: ";
                            xlWorkSheet.Cells[iRow, k] = ln_sgpm;
                            k += 2;
                            xlWorkSheet.Cells[iRow, k] = ln_slvyamta;
                            k += 3;
                            xlWorkSheet.Cells[iRow, k] = ln_ssysamta;
                            k += 2;
                            xlWorkSheet.Cells[iRow, k++] = ln_slvydiffa;

                            xlWorkSheet.get_Range(String.Format("F{0}", iRow)).Columns.HorizontalAlignment = 4;

                            iRow += 2;
                        }

                        lc_plvya = dr["plvya"].ToString().Trim();

                        xlWorkSheet.Cells[iRow, 1] = lc_plvya;

                        xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                        ln_scase = 0; ln_sgpm = 0; ln_slvyamta = 0; ln_ssysamta = 0; ln_slvydiffa = 0;

                        iRow += 2;
                    }

                    decimal fgpm = Convert.ToDecimal(dr["fgpm"]);
                    decimal flvya = Convert.ToDecimal(dr["flvya"]);
                    decimal flvyamta = Convert.ToDecimal(dr["flvyamta"]);
                    decimal fsysrate = Convert.ToDecimal(dr["fsysrate"]);
                    decimal fsysamta = Convert.ToDecimal(dr["fsysamta"]);
                    decimal flvydiffa = Convert.ToDecimal(dr["flvydiffa"]);
                    decimal fsyslmt = Convert.ToDecimal(dr["fsyslmt"]);

                    k = 1;
                    xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fbkdate"]);
                    xlWorkSheet.Cells[iRow, k++] = dr["fpolno"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fendtno"];
                    xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fincfr"]);
                    xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fendtdate"]);
                    xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fanvdate"]);
                    xlWorkSheet.Cells[iRow, k++] = fgpm;
                    xlWorkSheet.Cells[iRow, k++] = flvya;
                    xlWorkSheet.Cells[iRow, k++] = flvyamta;
                    xlWorkSheet.Cells[iRow, k++] = fsysrate;

                    if (flvya != fsysrate)
                        xlWorkSheet.Cells[iRow, k++] = "(*R)";
                    else
                        k++;

                    xlWorkSheet.Cells[iRow, k++] = fsysamta;

                    if (fsysamta != 0 && fsysamta == fsyslmt)
                        xlWorkSheet.Cells[iRow, k++] = "(*C)";
                    else
                        k++;

                    xlWorkSheet.Cells[iRow, k++] = flvydiffa;

                    ln_scase = ln_scase + 1;
                    ln_sgpm = ln_sgpm + fgpm;
                    ln_slvyamta = ln_slvyamta + flvyamta;
                    ln_ssysamta = ln_ssysamta + fsysamta;
                    ln_slvydiffa = ln_slvydiffa + flvydiffa;

                    ln_tcase = ln_tcase + 1;
                    ln_tgpm = ln_tgpm + fgpm;
                    ln_tlvyamta = ln_tlvyamta + flvyamta;
                    ln_tsysamta = ln_tsysamta + fsysamta;
                    ln_tlvydiffa = ln_tlvydiffa + flvydiffa;

                    iRow++;
                }

                xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");
                k = 6;

                xlWorkSheet.Cells[iRow, k++] = "Sub Total: ";
                xlWorkSheet.Cells[iRow, k] = ln_sgpm;
                k += 2;
                xlWorkSheet.Cells[iRow, k] = ln_slvyamta;
                k += 3;
                xlWorkSheet.Cells[iRow, k] = ln_ssysamta;
                k += 2;
                xlWorkSheet.Cells[iRow, k++] = ln_slvydiffa;

                xlWorkSheet.get_Range(String.Format("F{0}", iRow)).Columns.HorizontalAlignment = 4;

                iRow++;

                xlWorkSheet.Cells[iRow, 1] = "Total Cases: " + ln_tcase.ToString("N0");
                k = 6;

                xlWorkSheet.Cells[iRow, k++] = "Grand Total: ";
                xlWorkSheet.Cells[iRow, k] = ln_tgpm;
                k += 2;
                xlWorkSheet.Cells[iRow, k] = ln_tlvyamta;
                k += 3;
                xlWorkSheet.Cells[iRow, k] = ln_tsysamta;
                k += 2;
                xlWorkSheet.Cells[iRow, k++] = ln_tlvydiffa;

                xlWorkSheet.get_Range(String.Format("F{0}", iRow)).Columns.HorizontalAlignment = 4;

                return;
            }
        }

        public static void WsPrmExplDetail(Excel.Worksheet xlWorkSheet, DataTable dt, string vfbus, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fclsseq = "";

            decimal ln_scase = 0, ln_sgpm = 0;
            decimal ln_tcase = 0, ln_tgpm = 0;

            if (vfbus == "D")
            {
                foreach (DataRow dr in dt.Rows)
                {
                    if (lc_fclsseq != dr["fclsseq"].ToString().Trim())
                    {
                        if (iRow > startRow)
                        {
                            xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");
                            k = 12;

                            xlWorkSheet.Cells[iRow, k++] = "Total: ";
                            xlWorkSheet.Cells[iRow, k++] = ln_sgpm;

                            xlWorkSheet.get_Range(String.Format("L{0}", iRow)).Columns.HorizontalAlignment = 4;

                            iRow += 2;
                        }

                        lc_fclsseq = dr["fclsseq"].ToString().Trim();
                        xlWorkSheet.Cells[iRow, 1] = dr["fclsname"].ToString();

                        xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                        ln_scase = 0; ln_sgpm = 0;

                        iRow += 2;
                    }

                    decimal fsi = Convert.ToDecimal(dr["fsi"]);
                    decimal fliab = Convert.ToDecimal(dr["fliab"]);
                    decimal fgpm = Convert.ToDecimal(dr["fgpm"]);

                    k = 1;
                    xlWorkSheet.Cells[iRow, k++] = dr["fpolno"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fvseq"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fvarno"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fendtno"];

                    xlWorkSheet.Cells[iRow, k] = Convert.ToDateTime(dr["fefffr"]);
                    xlWorkSheet.Cells[iRow + 1, k++] = dr["fmntfr"].Equals(System.DBNull.Value) ? "" : Convert.ToDateTime(dr["fmntfr"]).ToString();
                    xlWorkSheet.Cells[iRow, k] = Convert.ToDateTime(dr["feffto"]);
                    xlWorkSheet.Cells[iRow + 1, k++] = dr["fmntto"].Equals(System.DBNull.Value) ? "" : Convert.ToDateTime(dr["fmntto"]).ToString();
                
                    xlWorkSheet.Cells[iRow, k++] = dr["fprdr"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fclnt"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fcdesc"] + ((char)13).ToString() + ((char)10).ToString();
                    xlWorkSheet.Cells[iRow, k++] = dr["fremark"] + ((char)13).ToString() + ((char)10).ToString();
                    xlWorkSheet.Cells[iRow, k++] = fsi;
                    xlWorkSheet.Cells[iRow, k++] = fliab;
                    xlWorkSheet.Cells[iRow, k++] = fgpm;

                    xlWorkSheet.get_Range(String.Format("I{0}", iRow), String.Format("I{0}", iRow + 1)).Merge();
                    xlWorkSheet.get_Range(String.Format("J{0}", iRow), String.Format("J{0}", iRow + 1)).Merge();
                    xlWorkSheet.get_Range(String.Format("I{0}", iRow), String.Format("J{0}", iRow + 1)).HorizontalAlignment = 1;
                    xlWorkSheet.get_Range(String.Format("I{0}", iRow), String.Format("J{0}", iRow + 1)).VerticalAlignment = 1;
                    xlWorkSheet.get_Range(String.Format("I{0}", iRow), String.Format("J{0}", iRow)).RowHeight = 11.25;

                    ln_scase = ln_scase + 1;
                    ln_sgpm = ln_sgpm + fgpm;

                    ln_tcase = ln_tcase + 1;
                    ln_tgpm = ln_tgpm + fgpm;

                    iRow += 3;
                }

                xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");
                k = 12;

                xlWorkSheet.Cells[iRow, k++] = "Total: ";
                xlWorkSheet.Cells[iRow, k++] = ln_sgpm;

                xlWorkSheet.get_Range(String.Format("L{0}", iRow)).Columns.HorizontalAlignment = 4;

                iRow += 2;

                xlWorkSheet.Cells[iRow, 1] = "Total of Cases: " + ln_tcase.ToString("N0");
                k = 12;

                xlWorkSheet.Cells[iRow, k++] = "Grand Total: ";
                xlWorkSheet.Cells[iRow, k++] = ln_tgpm;

                xlWorkSheet.get_Range(String.Format("L{0}", iRow)).Columns.HorizontalAlignment = 4;

                return;
            }

            if (vfbus == "R")
            {
                foreach (DataRow dr in dt.Rows)
                {
                    if (lc_fclsseq != dr["fclsseq"].ToString().Trim())
                    {
                        if (iRow > startRow)
                        {
                            xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");
                            k = 14;

                            xlWorkSheet.Cells[iRow, k++] = "Total: ";
                            xlWorkSheet.Cells[iRow, k++] = ln_sgpm;

                            xlWorkSheet.get_Range(String.Format("L{0}", iRow)).Columns.HorizontalAlignment = 4;

                            iRow += 2;
                        }

                        lc_fclsseq = dr["fclsseq"].ToString().Trim();
                        xlWorkSheet.Cells[iRow, 1] = dr["fclsname"].ToString();

                        xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                        ln_scase = 0; ln_sgpm = 0;

                        iRow += 2;
                    }

                    decimal fsi = Convert.ToDecimal(dr["fsi"]);
                    decimal fliab = Convert.ToDecimal(dr["fliab"]);
                    decimal fgpm = Convert.ToDecimal(dr["fgpm"]);

                    k = 1;
                    xlWorkSheet.Cells[iRow, k++] = dr["fpolno"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fvseq"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fvarno"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fendtno"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fsrcref"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fcedepol"];
                    xlWorkSheet.Cells[iRow, k++] = "'" + dr["fcedeendt"];

                    xlWorkSheet.Cells[iRow, k] = Convert.ToDateTime(dr["fefffr"]);
                    xlWorkSheet.Cells[iRow + 1, k++] = dr.IsNull("fmntfr") ? "" : Convert.ToDateTime(dr["fmntfr"]).ToString();
                    xlWorkSheet.Cells[iRow, k] = Convert.ToDateTime(dr["feffto"]);
                    xlWorkSheet.Cells[iRow + 1, k++] = dr.IsNull("fmntto") ? "" : Convert.ToDateTime(dr["fmntto"]).ToString();

                    xlWorkSheet.Cells[iRow, k++] = dr["fprdr"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fclnt"];
                    xlWorkSheet.Cells[iRow, k++] = dr["fremark"] + ((char)13).ToString() + ((char)10).ToString();
                    xlWorkSheet.Cells[iRow, k++] = fsi;
                    xlWorkSheet.Cells[iRow, k++] = fliab;
                    xlWorkSheet.Cells[iRow, k++] = fgpm;

                    xlWorkSheet.get_Range(String.Format("L{0}", iRow), String.Format("L{0}", iRow + 1)).Merge();
                    xlWorkSheet.get_Range(String.Format("L{0}", iRow), String.Format("L{0}", iRow + 1)).HorizontalAlignment = 1;
                    xlWorkSheet.get_Range(String.Format("L{0}", iRow), String.Format("L{0}", iRow + 1)).VerticalAlignment = 1;
                    xlWorkSheet.get_Range(String.Format("L{0}", iRow), String.Format("L{0}", iRow)).RowHeight = 11.25;

                    ln_scase = ln_scase + 1;
                    ln_sgpm = ln_sgpm + fgpm;

                    ln_tcase = ln_tcase + 1;
                    ln_tgpm = ln_tgpm + fgpm;

                    iRow += 3;
                }

                xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");
                k = 14;

                xlWorkSheet.Cells[iRow, k++] = "Total: ";
                xlWorkSheet.Cells[iRow, k++] = ln_sgpm;

                xlWorkSheet.get_Range(String.Format("L{0}", iRow)).Columns.HorizontalAlignment = 4;

                iRow += 2;

                xlWorkSheet.Cells[iRow, 1] = "Total of Cases: " + ln_tcase.ToString("N0");
                k = 14;

                xlWorkSheet.Cells[iRow, k++] = "Grand Total: ";
                xlWorkSheet.Cells[iRow, k++] = ln_tgpm;

                xlWorkSheet.get_Range(String.Format("L{0}", iRow)).Columns.HorizontalAlignment = 4;

                return;
            }
        }

        public static void WsPrmFacOutDetail(Excel.Worksheet xlWorkSheet, DataTable dt, string vfbus, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fdbtr = "", lc_fdbdesc = "", lc_fclsseq = "", lc_fclsname = "";

            Boolean ll_ChangeClass, ll_ChangeDebtor;

            decimal ln_s1case = 0, ln_s1gpm = 0, ln_s1comamt = 0, ln_s1npm = 0, ln_s1miscamt1 = 0,
                    ln_s1miscamt2 = 0, ln_s1miscamt3 = 0, ln_s1payable = 0;
            decimal ln_s2case = 0, ln_s2gpm = 0, ln_s2comamt = 0, ln_s2npm = 0, ln_s2miscamt1 = 0,
                    ln_s2miscamt2 = 0, ln_s2miscamt3 = 0, ln_s2payable = 0;
            decimal ln_ttcase = 0, ln_ttgpm = 0, ln_ttcomamt = 0, ln_ttnpm = 0, ln_ttmiscamt1 = 0,
                    ln_ttmiscamt2 = 0, ln_ttmiscamt3 = 0, ln_ttpayable = 0;

            foreach (DataRow dr in dt.Rows)
            {
                ll_ChangeClass = lc_fclsseq != dr["fclsseq"].ToString().Trim() || lc_fdbtr != dr["fdbtr"].ToString().Trim();
                ll_ChangeDebtor = lc_fdbtr != dr["fdbtr"].ToString().Trim();

                if (ll_ChangeClass)
                {
                    if (iRow > startRow)
                    {
                        //xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_s1case.ToString("N0");
                        k = 3;

                        xlWorkSheet.Cells[iRow, k++] = "Class Total: ";
                        xlWorkSheet.Cells[iRow, k++] = ln_s1gpm;
                        xlWorkSheet.Cells[iRow, k++] = ln_s1comamt;
                        xlWorkSheet.Cells[iRow, k++] = ln_s1npm;
                        xlWorkSheet.Cells[iRow, k++] = ln_s1miscamt1;
                        xlWorkSheet.Cells[iRow, k++] = ln_s1miscamt2;
                        xlWorkSheet.Cells[iRow, k++] = ln_s1miscamt3;
                        xlWorkSheet.Cells[iRow, k++] = ln_s1payable;

                        xlWorkSheet.get_Range(String.Format("C{0}", iRow)).Columns.HorizontalAlignment = 4;
                    }

                    lc_fclsseq = dr["fclsseq"].ToString().Trim();
                    lc_fclsname = dr["fclsname"].ToString().Trim();

                    ln_s1case = 0; ln_s1gpm = 0; ln_s1comamt = 0; ln_s1npm = 0; ln_s1miscamt1 = 0; ln_s1miscamt2 = 0; ln_s1miscamt3 = 0;
                    ln_s1payable = 0;
                }

                if (ll_ChangeDebtor)
                {
                    if (iRow > startRow)
                    {
                        iRow++;
                        //xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_s2case.ToString("N0");
                        k = 3;

                        xlWorkSheet.Cells[iRow, k++] = "Reinsurer Total: ";
                        xlWorkSheet.Cells[iRow, k++] = ln_s2gpm;
                        xlWorkSheet.Cells[iRow, k++] = ln_s2comamt;
                        xlWorkSheet.Cells[iRow, k++] = ln_s2npm;
                        xlWorkSheet.Cells[iRow, k++] = ln_s2miscamt1;
                        xlWorkSheet.Cells[iRow, k++] = ln_s2miscamt2;
                        xlWorkSheet.Cells[iRow, k++] = ln_s2miscamt3;
                        xlWorkSheet.Cells[iRow, k++] = ln_s2payable;

                        xlWorkSheet.get_Range(String.Format("C{0}", iRow)).Columns.HorizontalAlignment = 4;
                    }

                    lc_fdbtr = dr["fdbtr"].ToString().Trim();
                    lc_fdbdesc = dr["fdbdesc"].ToString().Trim();

                    ln_s2case = 0; ln_s2gpm = 0; ln_s2comamt = 0; ln_s2npm = 0; ln_s2miscamt1 = 0; ln_s2miscamt2 = 0; ln_s2miscamt3 = 0;
                    ln_s2payable = 0;

                    iRow += 2;

                    xlWorkSheet.Cells[iRow, 1] = lc_fdbtr + " - " + lc_fdbdesc;
                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;
                }

                if (ll_ChangeClass)
                {
                    iRow ++;
                    xlWorkSheet.Cells[iRow, 1] = lc_fclsname;
                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;
                    iRow++;
                }

                decimal fgpm = Convert.ToDecimal(dr["fgpm"]);
                decimal fcomamt = Convert.ToDecimal(dr["fcomamt"]);
                decimal fmiscamt1 = Convert.ToDecimal(dr["fmiscamt1"]);
                decimal fmiscamt2 = Convert.ToDecimal(dr["fmiscamt2"]);
                decimal fmiscamt3 = Convert.ToDecimal(dr["fmiscamt3"]);
                decimal fpayable = Convert.ToDecimal(dr["fpayable"]);

                k = 1;
                xlWorkSheet.Cells[iRow, k++] = dr["fpolno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fendtno"];
                xlWorkSheet.Cells[iRow, k++] = dr["finvno"];

                xlWorkSheet.Cells[iRow, k++] = fgpm;
                xlWorkSheet.Cells[iRow, k++] = fcomamt;
                xlWorkSheet.Cells[iRow, k++] = fgpm-fcomamt;
                xlWorkSheet.Cells[iRow, k++] = fmiscamt1;
                xlWorkSheet.Cells[iRow, k++] = fmiscamt2;
                xlWorkSheet.Cells[iRow, k++] = fmiscamt3;
                xlWorkSheet.Cells[iRow, k++] = fpayable;

                ln_s1case++;
                ln_s1gpm = ln_s1gpm + fgpm;
                ln_s1comamt = ln_s1comamt + fcomamt;
                ln_s1npm = ln_s1npm + fgpm - fcomamt;
                ln_s1miscamt1 = ln_s1miscamt1 + fmiscamt1;
                ln_s1miscamt2 = ln_s1miscamt2 + fmiscamt2;
                ln_s1miscamt3 = ln_s1miscamt3 + fmiscamt3;
                ln_s1payable = ln_s1payable + fpayable;

                ln_s2case++;
                ln_s2gpm = ln_s2gpm + fgpm;
                ln_s2comamt = ln_s2comamt + fcomamt;
                ln_s2npm = ln_s2npm + fgpm - fcomamt;
                ln_s2miscamt1 = ln_s2miscamt1 + fmiscamt1;
                ln_s2miscamt2 = ln_s2miscamt2 + fmiscamt2;
                ln_s2miscamt3 = ln_s2miscamt3 + fmiscamt3;
                ln_s2payable = ln_s2payable + fpayable;

                ln_ttcase++;
                ln_ttgpm = ln_ttgpm + fgpm;
                ln_ttcomamt = ln_ttcomamt + fcomamt;
                ln_ttnpm = ln_ttnpm + fgpm - fcomamt;
                ln_ttmiscamt1 = ln_ttmiscamt1 + fmiscamt1;
                ln_ttmiscamt2 = ln_ttmiscamt2 + fmiscamt2;
                ln_ttmiscamt3 = ln_ttmiscamt3 + fmiscamt3;
                ln_ttpayable = ln_ttpayable + fpayable;

                iRow ++;
            }

            k = 3;

            xlWorkSheet.Cells[iRow, k++] = "Class Total: ";
            xlWorkSheet.Cells[iRow, k++] = ln_s1gpm;
            xlWorkSheet.Cells[iRow, k++] = ln_s1comamt;
            xlWorkSheet.Cells[iRow, k++] = ln_s1npm;
            xlWorkSheet.Cells[iRow, k++] = ln_s1miscamt1;
            xlWorkSheet.Cells[iRow, k++] = ln_s1miscamt2;
            xlWorkSheet.Cells[iRow, k++] = ln_s1miscamt3;
            xlWorkSheet.Cells[iRow, k++] = ln_s1payable;

            xlWorkSheet.get_Range(String.Format("C{0}", iRow)).Columns.HorizontalAlignment = 4;

            iRow++;
            k = 3;

            xlWorkSheet.Cells[iRow, k++] = "Reinsurer Total: ";
            xlWorkSheet.Cells[iRow, k++] = ln_s2gpm;
            xlWorkSheet.Cells[iRow, k++] = ln_s2comamt;
            xlWorkSheet.Cells[iRow, k++] = ln_s2npm;
            xlWorkSheet.Cells[iRow, k++] = ln_s2miscamt1;
            xlWorkSheet.Cells[iRow, k++] = ln_s2miscamt2;
            xlWorkSheet.Cells[iRow, k++] = ln_s2miscamt3;
            xlWorkSheet.Cells[iRow, k++] = ln_s2payable;

            xlWorkSheet.get_Range(String.Format("C{0}", iRow)).Columns.HorizontalAlignment = 4;

            iRow++;
            k = 3;

            xlWorkSheet.Cells[iRow, k++] = "Grand Total: ";
            xlWorkSheet.Cells[iRow, k++] = ln_ttgpm;
            xlWorkSheet.Cells[iRow, k++] = ln_ttcomamt;
            xlWorkSheet.Cells[iRow, k++] = ln_ttnpm;
            xlWorkSheet.Cells[iRow, k++] = ln_ttmiscamt1;
            xlWorkSheet.Cells[iRow, k++] = ln_ttmiscamt2;
            xlWorkSheet.Cells[iRow, k++] = ln_ttmiscamt3;
            xlWorkSheet.Cells[iRow, k++] = ln_ttpayable;

            xlWorkSheet.get_Range(String.Format("C{0}", iRow)).Columns.HorizontalAlignment = 4;

            return;
        }

        public static void WsPrmFacOutSumm(Excel.Worksheet xlWorkSheet, DataTable dt, string vfbus, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fdbtr = "", lc_fdbdesc = "", lc_fclsseq = "", lc_fclsname = "";

            Boolean ll_ChangeClass, ll_ChangeDebtor;

            decimal ln_s1case = 0, ln_s1gpm = 0, ln_s1comamt = 0, ln_s1npm = 0, ln_s1miscamt1 = 0,
                    ln_s1miscamt2 = 0, ln_s1miscamt3 = 0, ln_s1payable = 0;
            decimal ln_s2case = 0, ln_s2gpm = 0, ln_s2comamt = 0, ln_s2npm = 0, ln_s2miscamt1 = 0,
                    ln_s2miscamt2 = 0, ln_s2miscamt3 = 0, ln_s2payable = 0;
            decimal ln_ttcase = 0, ln_ttgpm = 0, ln_ttcomamt = 0, ln_ttnpm = 0, ln_ttmiscamt1 = 0,
                    ln_ttmiscamt2 = 0, ln_ttmiscamt3 = 0, ln_ttpayable = 0;

            foreach (DataRow dr in dt.Rows)
            {
                ll_ChangeClass = lc_fclsseq != dr["fclsseq"].ToString().Trim() || lc_fdbtr != dr["fdbtr"].ToString().Trim();
                ll_ChangeDebtor = lc_fdbtr != dr["fdbtr"].ToString().Trim();

                if (ll_ChangeClass)
                {
                    if (iRow > startRow)
                    {
                        xlWorkSheet.Cells[iRow, 1] = lc_fclsname;
                        //xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;
                        
                        k = 4;

                        xlWorkSheet.Cells[iRow, k++] = ln_s1gpm;
                        xlWorkSheet.Cells[iRow, k++] = ln_s1comamt;
                        xlWorkSheet.Cells[iRow, k++] = ln_s1npm;
                        xlWorkSheet.Cells[iRow, k++] = ln_s1miscamt1;
                        xlWorkSheet.Cells[iRow, k++] = ln_s1miscamt2;
                        xlWorkSheet.Cells[iRow, k++] = ln_s1miscamt3;
                        xlWorkSheet.Cells[iRow, k++] = ln_s1payable;
                        iRow++;
                    }

                    lc_fclsseq = dr["fclsseq"].ToString().Trim();
                    lc_fclsname = dr["fclsname"].ToString().Trim();

                    ln_s1case = 0; ln_s1gpm = 0; ln_s1comamt = 0; ln_s1npm = 0; ln_s1miscamt1 = 0; ln_s1miscamt2 = 0; ln_s1miscamt3 = 0;
                    ln_s1payable = 0;
                }

                if (ll_ChangeDebtor)
                {
                    if (iRow > startRow)
                    {
                        //xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_s2case.ToString("N0");
                        k = 3;

                        xlWorkSheet.Cells[iRow, k++] = "Reinsurer Total: ";
                        xlWorkSheet.Cells[iRow, k++] = ln_s2gpm;
                        xlWorkSheet.Cells[iRow, k++] = ln_s2comamt;
                        xlWorkSheet.Cells[iRow, k++] = ln_s2npm;
                        xlWorkSheet.Cells[iRow, k++] = ln_s2miscamt1;
                        xlWorkSheet.Cells[iRow, k++] = ln_s2miscamt2;
                        xlWorkSheet.Cells[iRow, k++] = ln_s2miscamt3;
                        xlWorkSheet.Cells[iRow, k++] = ln_s2payable;

                        xlWorkSheet.get_Range(String.Format("C{0}", iRow)).Columns.HorizontalAlignment = 4;
                    }

                    lc_fdbtr = dr["fdbtr"].ToString().Trim();
                    lc_fdbdesc = dr["fdbdesc"].ToString().Trim();

                    ln_s2case = 0; ln_s2gpm = 0; ln_s2comamt = 0; ln_s2npm = 0; ln_s2miscamt1 = 0; ln_s2miscamt2 = 0; ln_s2miscamt3 = 0;
                    ln_s2payable = 0;

                    iRow += 2;

                    xlWorkSheet.Cells[iRow, 1] = lc_fdbtr + " - " + lc_fdbdesc;
                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                    iRow ++;
                }


                decimal fgpm = Convert.ToDecimal(dr["fgpm"]);
                decimal fcomamt = Convert.ToDecimal(dr["fcomamt"]);
                decimal fmiscamt1 = Convert.ToDecimal(dr["fmiscamt1"]);
                decimal fmiscamt2 = Convert.ToDecimal(dr["fmiscamt2"]);
                decimal fmiscamt3 = Convert.ToDecimal(dr["fmiscamt3"]);
                decimal fpayable = Convert.ToDecimal(dr["fpayable"]);

                ln_s1case++;
                ln_s1gpm = ln_s1gpm + fgpm;
                ln_s1comamt = ln_s1comamt + fcomamt;
                ln_s1npm = ln_s1npm + fgpm - fcomamt;
                ln_s1miscamt1 = ln_s1miscamt1 + fmiscamt1;
                ln_s1miscamt2 = ln_s1miscamt2 + fmiscamt2;
                ln_s1miscamt3 = ln_s1miscamt3 + fmiscamt3;
                ln_s1payable = ln_s1payable + fpayable;

                ln_s2case++;
                ln_s2gpm = ln_s2gpm + fgpm;
                ln_s2comamt = ln_s2comamt + fcomamt;
                ln_s2npm = ln_s2npm + fgpm - fcomamt;
                ln_s2miscamt1 = ln_s2miscamt1 + fmiscamt1;
                ln_s2miscamt2 = ln_s2miscamt2 + fmiscamt2;
                ln_s2miscamt3 = ln_s2miscamt3 + fmiscamt3;
                ln_s2payable = ln_s2payable + fpayable;

                ln_ttcase++;
                ln_ttgpm = ln_ttgpm + fgpm;
                ln_ttcomamt = ln_ttcomamt + fcomamt;
                ln_ttnpm = ln_ttnpm + fgpm - fcomamt;
                ln_ttmiscamt1 = ln_ttmiscamt1 + fmiscamt1;
                ln_ttmiscamt2 = ln_ttmiscamt2 + fmiscamt2;
                ln_ttmiscamt3 = ln_ttmiscamt3 + fmiscamt3;
                ln_ttpayable = ln_ttpayable + fpayable;
            }

            xlWorkSheet.Cells[iRow, 1] = lc_fclsname;
            //xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

            k = 4;

            xlWorkSheet.Cells[iRow, k++] = ln_s1gpm;
            xlWorkSheet.Cells[iRow, k++] = ln_s1comamt;
            xlWorkSheet.Cells[iRow, k++] = ln_s1npm;
            xlWorkSheet.Cells[iRow, k++] = ln_s1miscamt1;
            xlWorkSheet.Cells[iRow, k++] = ln_s1miscamt2;
            xlWorkSheet.Cells[iRow, k++] = ln_s1miscamt3;
            xlWorkSheet.Cells[iRow, k++] = ln_s1payable;
            iRow++;
  
            k = 3;

            xlWorkSheet.Cells[iRow, k++] = "Reinsurer Total: ";
            xlWorkSheet.Cells[iRow, k++] = ln_s2gpm;
            xlWorkSheet.Cells[iRow, k++] = ln_s2comamt;
            xlWorkSheet.Cells[iRow, k++] = ln_s2npm;
            xlWorkSheet.Cells[iRow, k++] = ln_s2miscamt1;
            xlWorkSheet.Cells[iRow, k++] = ln_s2miscamt2;
            xlWorkSheet.Cells[iRow, k++] = ln_s2miscamt3;
            xlWorkSheet.Cells[iRow, k++] = ln_s2payable;

            xlWorkSheet.get_Range(String.Format("C{0}", iRow)).Columns.HorizontalAlignment = 4;

            iRow++;
            k = 3;

            xlWorkSheet.Cells[iRow, k++] = "Grand Total: ";
            xlWorkSheet.Cells[iRow, k++] = ln_ttgpm;
            xlWorkSheet.Cells[iRow, k++] = ln_ttcomamt;
            xlWorkSheet.Cells[iRow, k++] = ln_ttnpm;
            xlWorkSheet.Cells[iRow, k++] = ln_ttmiscamt1;
            xlWorkSheet.Cells[iRow, k++] = ln_ttmiscamt2;
            xlWorkSheet.Cells[iRow, k++] = ln_ttmiscamt3;
            xlWorkSheet.Cells[iRow, k++] = ln_ttpayable;

            xlWorkSheet.get_Range(String.Format("C{0}", iRow)).Columns.HorizontalAlignment = 4;

            return;
        }
    }
}
