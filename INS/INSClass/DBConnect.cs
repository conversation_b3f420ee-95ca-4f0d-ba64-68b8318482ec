using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Data;
using System.Data.Sql;
using System.Data.SqlClient;
using System.Data.Common;
using System.Data.OleDb;
using InsEnvironment;
using System.Configuration;

namespace INS.INSClass
{
    public class DBConnect
    {
        private static SqlConnection m_dbConn = null;
        private static SqlConnection Conn = null;
        public static SqlConnection mstdbConn = null;
        public static SqlConnection opdbConn = null;
        public static string connectionString = ConfigurationManager.ConnectionStrings["mdata"].ConnectionString;
        
        public static SqlConnection dbConn
        {
            get
            {
                if (m_dbConn == null)
                {
                    SetDbConnect();
                    m_dbConn = DBConnect.DbConnect("");
                }
                return m_dbConn;
            }
        }


        public static SqlConnection DbConnect(string connString)
        {
            if (connString == string.Empty)
            {
                m_dbConn = new SqlConnection(connectionString);
            }
            else
            {
                m_dbConn = new SqlConnection(connString);
            }
            dbConn.Open();
            return m_dbConn;
        }

        public int OperateData(string strSql)

        {
            Conn = DBConnect.dbConn;
            if (Conn.State == ConnectionState.Closed)
            {
                Conn.Open();
            }

            SqlCommand cmd = new SqlCommand(strSql, Conn);
            int i = (int)cmd.ExecuteNonQuery();
            Conn.Close();
            return i;
        }

        public int OperateData(string strSql, SqlConnection sqlConn)
        {
            if (sqlConn.State == ConnectionState.Closed)
            {
                sqlConn.Open();
            }

            SqlCommand cmd = new SqlCommand(strSql, sqlConn);
            int i = (int)cmd.ExecuteNonQuery();
            sqlConn.Close();
            return i;
        }

        public int SelectData(string strSql)
        {
            Conn = DBConnect.dbConn;
            if (Conn.State == ConnectionState.Closed)
            {
                Conn.Open();
            }

            SqlCommand cmd = new SqlCommand(strSql, Conn);
            int i = (int)cmd.ExecuteScalar();
            Conn.Close();
            return i;
        }

        public int SelectData(string strSql, SqlConnection sqlConn)
        {
            if (sqlConn.State == ConnectionState.Closed)
            {
                sqlConn.Open();
            }

            SqlCommand cmd = new SqlCommand(strSql, sqlConn);
            int i = (int)cmd.ExecuteScalar();
            sqlConn.Close();
            return i;
        }

        public DataTable GetTable(string strSql)
        {
            DataTable ds = new DataTable();
            SqlDataAdapter da = new SqlDataAdapter(strSql, DBConnect.dbConn);
            da.Fill(ds);

            return ds;
        }

        public DataTable GetTable(string strSql, SqlConnection sqlConn)
        {
            DataTable ds = new DataTable();
            SqlDataAdapter da = new SqlDataAdapter(strSql, sqlConn);
            da.Fill(ds);

            return ds;
        }

        public static DataTable GetDataSetProd(string sql, string db, params SqlParameter[] values)
        {
            SqlConnection conn = new SqlConnection(connectionString);
            try
            {
                DataTable ds = new DataTable();
                SqlCommand cmd = new SqlCommand(sql, conn);
                cmd.Parameters.AddRange(values);
                cmd.CommandType = CommandType.StoredProcedure;
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public static void SetDbConnect()
        {
            string sqlstring = "Data Source=cob-server-090;User ID=common;PassWord=**********;Initial Catalog={0};";
            string conn_to_mstdb = string.Format(sqlstring, EnvSettings.GetMstDbname());
            string conn_to_opdb = string.Format(sqlstring, EnvSettings.GetOpDbname());

            if (mstdbConn == null)
                mstdbConn = new SqlConnection(conn_to_mstdb);

            if (opdbConn == null)
                opdbConn = new SqlConnection(conn_to_opdb);

            mstdbConn.Open();
            opdbConn.Open();

            return;
        }

 
    }
}
