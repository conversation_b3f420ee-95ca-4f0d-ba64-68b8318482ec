using Microsoft.Office.Interop.Word;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INS.INSClass
{
    class Word2PDF
    {
       public void word2PDF(string docpath, string pdfpath)
        {
            // word 檔案位置
            string sourcedocx = docpath;
            // PDF 儲存位置
            string targetpdf = pdfpath;

            //建立 word application instance
            Microsoft.Office.Interop.Word.Application appWord = new Microsoft.Office.Interop.Word.Application();
            //開啟 word 檔案
            var wordDocument = appWord.Documents.Open(sourcedocx);
            //匯出為 pdf
            wordDocument.ExportAsFixedFormat(targetpdf, WdExportFormat.wdExportFormatPDF);

            //關閉 word 檔
            wordDocument.Close();
            //結束 word
            appWord.Quit();

            try
            {
                if (File.Exists(Path.Combine(docpath)))
                {
                    File.Delete(Path.Combine(docpath));
                }
                else Console.WriteLine("File not found");
            }
            catch (IOException ioExp)
            {
                Console.WriteLine(ioExp.Message);
            }
        }
    }
}
