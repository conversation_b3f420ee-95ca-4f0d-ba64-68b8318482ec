using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.INSClass
{
    public partial class NNumericTextBox : TextBox
    {
        public NNumericTextBox()
        {
            InitializeComponent();
        }
                
        protected override void OnPaint(PaintEventArgs pe)
        {
            base.OnPaint(pe);
        }

        public string InputMask { get ; set; }

        public Decimal NumericValue 
        
        { 
            get { return numericvalue; }
            set
            { 
                numericvalue = value;
                inputmask = InputMask == null ? "" : InputMask.Trim();
                inputlength = inputmask.Trim().Length;

                decimalplace = inputmask.IndexOf('.');
                decimalplace = decimalplace < 0 ? 0 : inputlength - decimalplace - 1;
                decimaloffset = decimalplace ==0 ? 0 : decimalplace + 1;

                nformat = "N" + (decimalplace).ToString().Trim();
                Text = numericvalue.ToString(nformat);
            }
        }

        private Decimal numericvalue;
        private String inputmask;
        private Int32 inputlength;
        private Int32 decimalplace, decimalpos, decimaloffset, cursorfromright;

        private String nformat;

        private string decimalSeparator = ".";
        private string groupSeparator = ",";
        private string negativeSign = "-";


        private Int32 selectionstart,selectionlength;

         // Restricts the entry of characters to digits (including hex), the negative sign, 
        // the decimal point, and editing keystrokes (backspace). 

        protected override void OnKeyPress(KeyPressEventArgs e)
        {
            base.OnKeyPress(e);
            e.Handled = true;
            
            //selectionstart = SelectionStart;
            //selectionlength = SelectionLength;

            //decimalpos = Text.Trim().Length - decimaloffset;
            //cursorfromright = Text.Length - (selectionstart + selectionlength);

            string keyInput = e.KeyChar.ToString();


            if (!"1234567890-.".Contains(keyInput))
            {
                return;
            }

            updatechange(keyInput);
        }

        private void updatechange(string keyInput)
        {
            selectionstart = SelectionStart;
            selectionlength = SelectionLength;

            decimalpos = Text.Trim().Length - decimaloffset;
            cursorfromright = Text.Length - (selectionstart + selectionlength);

            string text_begin, text_end, text_full;

            if (keyInput == negativeSign)
            {
                ////text_begin = "";
                ////text_end = Text.Substring(selectionstart + selectionlength, Text.Length - selectionstart - selectionlength);

                ////if (selectionstart + selectionlength > decimalpos)
                ////{
                ////    text_end = text_end.PadRight(decimalplace,'0');
                ////    text_end = decimalSeparator + text_end;
                ////}


                ////text_full = negativeSign + text_begin + text_end;
                ////text_full = text_full == negativeSign ? text_full + "0" : text_full;
                ////text_full = text_full.Substring(1, 1) == negativeSign ? text_full.Substring(1, text_full.Length - 1) : text_full;

                //  added on 20151116 (begin);
                text_full = "";

                if (numericvalue == 0)
                    text_full = Text;   // added 20151116;

                if (numericvalue < 0)
                    text_full = Text.Substring(1, Text.Length - 1);

                if (numericvalue  > 0 && Text.Length < inputlength)
                    text_full = negativeSign + Text;

                //  added on 20151116 (end);

                text_full = text_full.Replace(",", "");
                numericvalue = Convert.ToDecimal(text_full);
                text_full = numericvalue.ToString(nformat);


                ////text_full = text_full.Length > inputlength ? text_full.Remove(1, 1) : text_full;

                ////Text = text_full;
                ////selectionstart = selectionstart + selectionlength <= decimalpos ? 1 : text_full.Length - cursorfromright;
                ////SelectionStart = selectionstart >= text_full.Length ? text_full.Length - 1 : selectionstart;

                // added on 20151116 (begin)

                Text = text_full;

                selectionstart = text_full.Length - cursorfromright - selectionlength;
                SelectionStart = selectionstart;

                // added on 20151116 (end)

                return;
            }

            if (keyInput == decimalSeparator)
            {
                text_begin = selectionstart < decimalpos ? Text.Substring(0, selectionstart) :
                    Text.Substring(0, decimalpos);

                text_end = decimalplace == 0 ? "" : selectionlength == 0 ||
                    selectionstart + selectionlength <= decimalpos ? Text.Substring(decimalpos + 1, decimalplace) :
                    Text.Substring(selectionstart + selectionlength, Text.Length - selectionstart - selectionlength);
                
                //text_end = selectionstart + selectionlength >= Text.Length || decimalplace == 0 ? "" :
                //    selectionstart + selectionlength > decimalpos ? 
                //    Text.Substring (selectionstart + selectionlength, Text.Length - selectionstart - selectionlength) :
                //    Text.Substring(decimalpos+1, decimalplace);

                if (decimalplace != 0)
                {
                    text_end = text_end.PadRight(decimalplace, '0');
                    text_end = decimalSeparator + text_end;
                }

                text_begin = text_begin == "" ? "0" : text_begin;
                text_begin = text_begin == "-" ? "-0" : text_begin;
                
                text_full = text_begin +text_end;

                text_full = text_full.Replace(",", "");
                numericvalue = Convert.ToDecimal(text_full);
                text_full = numericvalue.ToString(nformat);

                Text = text_full;

                decimalpos = text_full.Length - decimaloffset;
                
                SelectionStart = decimalplace == 0 ? text_full.Length : decimalpos + 1;

                return;
            }

            if ("1234567890".Contains(keyInput))
            {
                string text_int1, text_int2, text_dec1, text_dec2, text_decimal="";

                text_int1 = selectionstart < decimalpos ? Text.Substring(0,selectionstart) :
                    Text.Substring(0, decimalpos);   // integer part I

                text_int2 = selectionstart + selectionlength >= decimalpos ? "" :
                    Text.Substring(selectionstart + selectionlength, decimalpos - selectionstart - selectionlength);
                    // integer part II

                text_dec1 = decimalplace == 0 ? "" :
                    selectionstart + selectionlength <= decimalpos ? Text.Substring(decimalpos + 1, decimalplace) :
                    selectionstart <= decimalpos ? Text.Substring(selectionstart+selectionlength,Text.Length-selectionstart-selectionlength) :
                    Text.Substring(decimalpos+1,selectionstart-decimalpos-1); // decimal part I

                text_dec2 = decimalplace == 0 ? "" :
                    selectionstart + selectionlength <= decimalpos ? "" :
                    selectionstart <= decimalpos ? "" :
                    Text.Substring(selectionstart+selectionlength, Text.Length-selectionstart-selectionlength);
                        // decimal part II

                if (selectionstart <= decimalpos)
                {
                    text_int2 = keyInput + text_int2;
                    if (text_int2.Length >= 2 && text_int2.Substring(1, 1) == "-")
                        text_int2 = text_int2.Remove(1, 1);
                }
                else
                    text_dec2 = keyInput + text_dec2;

                if (decimalplace > 0)
                {
                    text_decimal = (text_dec1 + text_dec2).Trim();

                    if (text_decimal.Length < decimalplace)
                        text_decimal = text_decimal.PadRight(decimalplace, '0');

                    text_decimal = text_decimal.Length > decimalplace && selectionstart >= Text.Length ?
                        text_decimal.Substring(1, decimalplace) : text_decimal.Substring(0, decimalplace);
               }

                text_full = decimalplace == 0 ? text_int1 + text_int2 : text_int1 + text_int2 + decimalSeparator + text_decimal;

                text_full = text_full.Replace(",", "");
                numericvalue = Convert.ToDecimal(text_full);
                text_full = numericvalue.ToString(nformat);

                if (text_full.Length > inputlength && text_full.Substring(0, 1) != negativeSign)
                    text_full = text_full.Remove(0, 1);

                if (text_full.Length > inputlength && text_full.Substring(0, 1) == negativeSign)
                    text_full = text_full.Remove(1, 1);

                text_full = text_full.Replace(",", "");
                numericvalue = Convert.ToDecimal(text_full);
                text_full = numericvalue.ToString(nformat);

                Text = text_full;

                Boolean ll_assigned = false;

                if (selectionstart + selectionlength <= decimalpos && selectionlength != 0 && !ll_assigned)
                {
                    selectionstart = text_full.Length - cursorfromright;
                    ll_assigned = true;
                }

                if (selectionstart + selectionlength <= decimalpos && selectionlength == 0 && !ll_assigned)
                {
                    selectionstart = text_full.Length - cursorfromright + selectionlength;
                    ll_assigned = true;
                }

                if (selectionstart <= decimalpos && !ll_assigned)
                {
                    selectionstart = text_full.Length - decimalplace;
                    ll_assigned = true;
                }

                if (selectionstart > decimalpos && !ll_assigned)
                {
                    selectionstart = selectionstart+1;
                    ll_assigned = true;
                }

                SelectionStart = selectionstart;
                return;
            }
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            base.OnKeyDown(e);

            string keyCode = e.KeyCode.ToString();

            if ("Delete|Back".Contains(keyCode))
            {
                e.Handled = true;
                deletetext(keyCode);
            }
            
            return;
            //this place
        }

        private void deletetext (string keyCode)
        {
            selectionstart = SelectionStart;
            selectionlength = SelectionLength;

            string handle_method = "";

            if (selectionlength != 0)
                handle_method = "1";

            if (keyCode == "Delete" && selectionlength == 0)
                handle_method = "2";

            if (keyCode == "Back" && selectionlength == 0)
                handle_method = "3";

            if (handle_method == "2" && selectionstart < Text.Trim().Length)
            {
                selectionlength = 1;
                if (Text.Trim().Substring(selectionstart, selectionlength) == groupSeparator)
                    selectionlength = 2;
            }

            if (handle_method == "3" && selectionstart > 0)
            {
                selectionstart = selectionstart - 1;
                selectionlength = 1;
                if (Text.Trim().Substring(selectionstart, selectionlength) == groupSeparator)
                {
                    selectionstart = selectionstart - 1;
                    selectionlength = 2;
                }
            }

            decimalpos = Text.Trim().Length - decimaloffset;
            cursorfromright = Text.Length - (selectionstart + selectionlength);

            string text_int1, text_int2, text_dec1, text_dec2, text_decimal = "", text_full;

            text_int1 = selectionstart < decimalpos ? Text.Substring(0, selectionstart) :
                Text.Substring(0, decimalpos);   // integer part I

            text_int2 = selectionstart + selectionlength >= decimalpos ? "" :
                Text.Substring(selectionstart + selectionlength, decimalpos - selectionstart - selectionlength);
            // integer part II

            text_dec1 = decimalplace == 0 ? "" :
                selectionstart + selectionlength <= decimalpos ? Text.Substring(decimalpos + 1, decimalplace) :
                selectionstart <= decimalpos ? Text.Substring(selectionstart + selectionlength, Text.Length - selectionstart - selectionlength) :
                Text.Substring(decimalpos + 1, selectionstart - decimalpos - 1); // decimal part I

            text_dec2 = decimalplace == 0 ? "" :
                selectionstart + selectionlength <= decimalpos ? "" :
                selectionstart <= decimalpos ? "" :
                Text.Substring(selectionstart + selectionlength, Text.Length - selectionstart - selectionlength);
            // decimal part II

            if (decimalplace > 0)
            {
                text_decimal = (text_dec1 + text_dec2).Trim();

                if (text_decimal.Length < decimalplace)
                    text_decimal = text_decimal.PadRight(decimalplace, '0');
            }

            text_full = decimalplace == 0 ? text_int1 + text_int2 : text_int1 + text_int2 + decimalSeparator + text_decimal;

            text_full = text_full.Replace(",", "");
            numericvalue = Convert.ToDecimal(text_full);
            text_full = numericvalue.ToString(nformat);

            Text = text_full;

            Boolean ll_assigned = false;

            if (selectionstart + selectionlength <= decimalpos && selectionlength != 0 && !ll_assigned)
            {
                selectionstart = text_full.Length - cursorfromright;
                ll_assigned = true;
            }

            if (selectionstart + selectionlength <= decimalpos && selectionlength == 0 && !ll_assigned)
            {
                selectionstart = text_full.Length - cursorfromright + selectionlength;
                ll_assigned = true;
            }

            if (selectionstart <= decimalpos && !ll_assigned)
            {
                selectionstart = text_full.Length - decimalplace;
                ll_assigned = true;
            }

            if (selectionstart > decimalpos && !ll_assigned)
            {
                //selectionstart = selectionstart;
                ll_assigned = true;
            }

            selectionstart = selectionstart < 0 ? 0 : selectionstart;
            selectionstart = selectionstart > text_full.Length ? text_full.Length : selectionstart;

            SelectionStart = selectionstart;
            SelectionLength = 0;

            return;
        }
    }
}
