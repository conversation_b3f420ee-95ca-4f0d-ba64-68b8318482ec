using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Configuration;
using System.Data.SqlClient;
using System.Data;
using System.Data.Common;

namespace INS.INSClass
{
    public static class DBHelper
    {

        private static SqlConnection connection;
        public static string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
               
        public static SqlConnection Connection
        {
            get
            {
                 if (connection == null)
                {
                    connection = new SqlConnection(connectionString);
                    connection.Open();
                }
                else if (connection.State == System.Data.ConnectionState.Closed)
                {
                    connection.Open();
                }
                else if (connection.State == System.Data.ConnectionState.Broken)
                {
                    connection.Close();
                    connection.Open();
                }
                return connection;
            }
        }

        //执行不带参数数据记录的增、删、改操作，并返回执行后的结果值
        public static Boolean ExecuteCommand(string safeSql)
        {
            try
            {
                SqlCommand cmd = new SqlCommand(safeSql, Connection);
                int result = cmd.ExecuteNonQuery();
                if (result != 0)
                {
                    return true;
                }
                else { return false; }
            }
            catch (Exception)
            {
                return false; 
            }
        }


        //执行不带参数数据记录的增、删、改操作，并返回执行后的结果值
        public static Boolean ExecuteCommandPAR(string safeSql)
        {
            try
            {
                SqlCommand cmd = new SqlCommand(safeSql, Connection);
                int result = cmd.ExecuteNonQuery();
                if (result != 0)
                {
                    return true;
                }
                else { return true; }
            }
            catch (Exception)
            {
                return false;
            }
        }

        //执行带参数数据记录的增、删、改操作，返回执行后的结果值
        public static int ExecuteCommand(string sql, params SqlParameter[] values)
        {
            try
            {
                SqlCommand cmd = new SqlCommand(sql, Connection);
                cmd.Parameters.AddRange(values);
                return cmd.ExecuteNonQuery();
            }
            catch (Exception)
            {
                throw;
            }
        }

        //返回执行不带参数SQL语句后中结果集的第一行第一列，但结果必须是int型
        public static int GetScalar(string safeSql)
        {
            try
            {
                SqlCommand cmd = new SqlCommand(safeSql, Connection);
                int result = Convert.ToInt32(cmd.ExecuteScalar());
                return result;
            }
            catch (Exception)
            {
                throw;
            }
        }

        //返回执行带参数SQL语句后中结果集的第一行第一列，但结果必须是int型
        public static int GetScalar(string sql, params SqlParameter[] values)
        {
            try
            {
                SqlCommand cmd = new SqlCommand(sql, Connection);
                cmd.Parameters.AddRange(values);
                int result = Convert.ToInt32(cmd.ExecuteScalar());
                return result;
            }
            catch (Exception)
            {
                throw;
            }
        }

        //返回执行不带参数SQL语句后的阅读器
        public static SqlDataReader GetReader(string safeSql)
        {
            SqlDataReader reader = null;
            using (SqlCommand cmd = new SqlCommand(safeSql, Connection))
                {
                    try
                    {
                        reader = cmd.ExecuteReader();
                    }
                    catch (Exception)
                    {
                        Connection.Close();
                    }
                }
            return reader;
        }

        //返回执行带参数SQL语句后的阅读器
        public static SqlDataReader GetReader(string sql, params SqlParameter[] values)
        {
            SqlDataReader reader = null;
            try
            {
                SqlCommand cmd = new SqlCommand(sql, Connection);
                cmd.Parameters.AddRange(values);
                reader = cmd.ExecuteReader();
            }
            catch (Exception)
            {
                throw;
            }
            return reader;
        }

        //返回执行不带参数SQL语句后的结果数据表
        public static DataTable GetDataSet(string safeSql)
        {
            DataTable ds = new DataTable();
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand(safeSql, con))
                {
                    try
                    {
                        con.Open();
                        cmd.CommandTimeout = 1000;
                        SqlDataAdapter da = new SqlDataAdapter(cmd);
                        da.Fill(ds);
                    }
                    catch (Exception)
                    {
                        throw;
                    }
                }
            }
            return ds;
        }

        //返回执行带参数SQL语句后的结果数据表
        public static DataTable GetDataSet(string sql, params SqlParameter[] values)
        {
            try
            {
                DataSet ds = new DataSet();
                SqlCommand cmd = new SqlCommand(sql, Connection);
                cmd.Parameters.AddRange(values);
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (Exception)
            {
                throw;
            }
        }

        public static DataTable GetDataSetProd(string sql,string db,params SqlParameter[] values)
        {
            SqlConnection conn = new SqlConnection(connectionString); 
            try
            {
                DataTable ds = new DataTable();
                SqlCommand cmd = new SqlCommand(sql, conn);
                cmd.Parameters.AddRange(values);
                cmd.CommandType = CommandType.StoredProcedure;
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public static DataSet GetDtSetProd(string sql, string db, params SqlParameter[] values)
        {
            SqlConnection conn = new SqlConnection(connectionString);
            try
            {
                DataSet ds = new DataSet();
                SqlCommand cmd = new SqlCommand(sql, conn);
                cmd.Parameters.AddRange(values);
                cmd.CommandType = CommandType.StoredProcedure;
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public static Boolean BulkInsertDataTable(string tableName, DataTable dataTable)
        {
            bool isSuccuss;
            try
            {
                SqlConnection SqlConnectionObj = new SqlConnection(connectionString);
                SqlConnectionObj.Open();
                SqlBulkCopy bulkCopy = new SqlBulkCopy(SqlConnectionObj, SqlBulkCopyOptions.TableLock | SqlBulkCopyOptions.FireTriggers | SqlBulkCopyOptions.UseInternalTransaction, null);
                bulkCopy.DestinationTableName = tableName;
                bulkCopy.WriteToServer(dataTable);
                isSuccuss = true;
                SqlConnectionObj.Close();
            }
            catch (Exception ex)
            {
                isSuccuss = false;
            }
            return isSuccuss;
        }

        public static Boolean ExecuteSqlTransaction(string[] arr)
        {
            bool isSuccuss;
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                SqlCommand command = connection.CreateCommand();
                SqlTransaction transaction;

                // Start a local transaction.
                transaction = connection.BeginTransaction();

                // Must assign both transaction object and connection
                // to Command object for a pending local transaction
                command.Connection = connection;
                command.Transaction = transaction;

                try
                {
                    if (arr != null)
                    {
                        for (int i = 0; i < arr.Length; i++)
                        {
                            if (arr[i] != "" && arr[i] != null)
                            {
                                command.CommandText = arr[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }

                    // Attempt to commit the transaction.
                    transaction.Commit();
                    isSuccuss = true;
                }
                catch (Exception ex)
                {
                    isSuccuss = false;
                    Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                    Console.WriteLine("  Message: {0}", ex.Message);

                    // Attempt to roll back the transaction.
                    try
                    {
                        transaction.Rollback();
                    }
                    catch (Exception ex2)
                    {
                        // This catch block will handle any errors that may have occurred
                        // on the server that would cause the rollback to fail, such as
                        // a closed connection.
                        Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                        Console.WriteLine("  Message: {0}", ex2.Message);
                    }
                }
            }
            return isSuccuss;
        }

        public static string CheckValue(string fid, string flag)
        {
            string str = "";
            if (flag == "Broker")
            {
                str = "select fid,fdesc from mprdr where ftype='B' and fid='" + fid + "'";
            }
            if (flag == "Reinsurer")
            {
                str = "select fid,fdesc from mprdr where ftype='R' and fid='" + fid + "'";
            }
            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["mdata"].ConnectionString);
            SqlCommand cmd = new SqlCommand(str, con);
            con.Open();
            using (SqlDataReader sdr = cmd.ExecuteReader())
            {
                if (sdr.HasRows && sdr.Read())
                {
                    string fdesc = sdr["fdesc"].ToString().Trim();
                    sdr.Close();
                    return fdesc;
                }
                else { return null; }
            }
        }

        public static string seek(string code, string sec, string flag)
        {
            string str = "";
            if (flag == "ftty")
            {
                str = "select distinct a.fid as [Treaty#], c.fid as [Sec], "+
                "fefffr as [Eff Fr], feffto as [Eff To], b.fdesc as [Desc]  "+
                "from mty a   "+
                "left join mtysec c on c.fctlid_1 =a.fctlid "+ 
                "left join mtyclass b on b.fctlid_1=a.fctlid and b.fctlid_2= c.fctlid  "+
                "where b.ftype ='2' and a.fid='" + code + "' and c.fid ='" + sec + "'";
            }
            if (flag == "ffacb")
            {
                str = "select distinct a.fid as [Treaty#], c.fid as [Sec], " +
                 "fefffr as [Eff Fr], feffto as [Eff To], b.fdesc as [Desc]  " +
                 "from mfb a   " +
                 "left join mfbsec c on c.fctlid_1 =a.fctlid " +
                 "left join mfbclass b on b.fctlid_1=a.fctlid and b.fctlid_2= c.fctlid  " +
                 "where b.ftype ='2' and a.fid='" + code + "' and c.fid ='" + sec + "'";
            }
            if (flag == "fxol")
            {
                str = "select distinct a.fid as [Xol#], c.fid as [Layer], fefffr as [Eff Fr], feffto as [Eff To], b.fdesc as [Desc] " +
                        "from mxl a  " +
                        "left join mxllayr c on c.fctlid_1 =a.fctlid " +
                        "left join mxlclass b on b.fctlid_1=a.fctlid and b.fctlid_2= c.fctlid " +
                        "where b.ftype ='2' and a.fid='" + code + "' and c.fid ='" + sec + "'";
            }
            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["mdata"].ConnectionString);
            SqlCommand cmd = new SqlCommand(str, con);
            con.Open();
            using (SqlDataReader sdr = cmd.ExecuteReader())
            {
                if (sdr.HasRows && sdr.Read())
                {
                    string fdesc = sdr["Desc"].ToString().Trim();
                    sdr.Close();
                    return fdesc;
                }
                else { return null; }
            }
        }
    }
}
