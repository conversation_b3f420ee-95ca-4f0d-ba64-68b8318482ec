using System;
using System.Collections.Generic;
using System.Data;
using System.Data.OleDb;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;

namespace INS.INSClass
{
    class ExportDBF
    {
        public string CreateDBF(string fileName,DataTable dt)
        {
            try
            {
                string path = fileName.Substring(0, fileName.LastIndexOf('\\'));
                string name = fileName.Substring(fileName.LastIndexOf("\\") + 1).Replace(".dbf", "");

                using (OleDbConnection con = new OleDbConnection(@"Provider=Microsoft.Jet.OLEDB.4.0;Data Source=d:\;Extended Properties=dBASE IV;"))
                {
                    con.Open();
                    OleDbCommand cmd = null;
                    if (File.Exists(fileName))//文件存在，删除数据
                    {
                        File.Delete(fileName);
                        cmd = new OleDbCommand("delete from " + name, con);
                        cmd.ExecuteNonQuery();
                        con.Close();
                    }
                    else//不存在，创建表     
                    {
                        string sqlCreat = "create table " + name + " ({0})";
                        string columns = "";
                        foreach (DataColumn col in dt.Columns)
                        {
                            columns += col.ColumnName;
                            columns += " char(100),";
                        }
                        columns = columns.Remove(columns.Length - 1, 1);
                        sqlCreat = string.Format(sqlCreat, columns);
                        cmd = new OleDbCommand(sqlCreat, con);
                        cmd.ExecuteNonQuery();

                        //插入数据
                        OleDbCommand cmdInsert = new OleDbCommand();
                        cmdInsert.Connection = con;
                        foreach (DataRow row in dt.Rows)
                        {
                            string sqlInsert = "insert into " + name + " values({0})";
                            string invalues = "";
                            foreach (DataColumn col in dt.Columns)
                            {
                                invalues += "'" + row[col].ToString() + "',";
                            }
                            invalues = invalues.Remove(invalues.Length - 1, 1);
                            sqlInsert = string.Format(sqlInsert, invalues);
                            cmdInsert.CommandText = sqlInsert;
                            cmdInsert.ExecuteNonQuery();
                        }
                        con.Close();
                    }
                    return "0";

                }
            }
            catch (Exception ex)
            {
                return ex.ToString ();
            }

        }
    }


}
