using Microsoft.Office.Interop.Word;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace INS.INSClass
{
    class CopyFile
    {
        public void readfiles() { }
        public string readfiles(string Filename)
        {
            int filesum = 0;  //更新文件数
            string LocalPath = "M:\\PushToA8\\";

            //从配置文件App中取文件服务器更新目录：
            string ServerPath = "M:\\COIL\\Business Department\\Policy Administration\\Policy Attachment\\2020 Policy";
            if (Directory.Exists(ServerPath))
            {
                foreach (string SourceFile in Directory.GetFiles(ServerPath))  //循环取服务器更新路径文件
                {
                    string FileName = System.IO.Path.GetFileName(SourceFile);//取更新文件名   
                    //本地目录有相同文件名就需要判断是否为可用更新文件   
                    if (FileName == Filename)
                    {
                        if (File.Exists(LocalPath + FileName) == true)
                        {
                            DateTime dtLocal = File.GetLastWriteTime(LocalPath + FileName);//本地文件修改日期   
                            DateTime dtUpdate = File.GetLastWriteTime(SourceFile);//更新目录文件的修改日期   
                            if (dtUpdate != dtLocal)//可用更新   
                            {
                                ++filesum;
                                File.Copy(SourceFile, LocalPath + FileName, true);
                            }
                        }
                        else
                        {
                            ++filesum;
                            File.Copy(SourceFile, LocalPath + FileName, true);
                        }
                    }
                }
                if (filesum > 0)
                {
                    return "刚才从服务器更新文件" + filesum.ToString() + "个";
                }
                else return "";
            }
            else
            {
                return "";
            }
        }

        public string WordToPdfSechedule(string Filename)
        {
            int filesum = 0;  //更新文件数

            try
            {  // word 檔案位置
                string sourcedocx = "M:\\COIL\\Business Department\\Policy Administration\\Policy Attachment\\2020 Policy\\" + Filename + "_Schedule.doc";
                // PDF 儲存位置
                string targetpdf = "M:\\PushToA8\\" + Filename + ".pdf";

                //建立 word application instance
                Microsoft.Office.Interop.Word.Application appWord = new Microsoft.Office.Interop.Word.Application();
                //開啟 word 檔案
                var wordDocument = appWord.Documents.Open(sourcedocx);
                //匯出為 pdf
                wordDocument.ExportAsFixedFormat(targetpdf, WdExportFormat.wdExportFormatPDF);

                //關閉 word 檔
                wordDocument.Close();
                //結束 word
                appWord.Quit();
                ++filesum;
                return "刚才从服务器更新文件" + filesum.ToString() + "个";
            }
            catch (Exception ex) {
                return "";
            }
        }
    }
}
