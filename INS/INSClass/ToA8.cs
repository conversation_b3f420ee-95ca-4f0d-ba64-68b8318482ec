using iTextSharp.text.pdf;
using ScanToSign.PDF;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web.Script.Serialization;

namespace INS.INSClass
{
    class ToA8
    {
        public static string ToA8process(string Class, string fconfirm, string policyNo, string fctlid)
        {
            var fileBytes = new byte[0];
            var extraFileBytes = new byte[0];
            if ((Class == "PMP" || Class == "CMP") && (fconfirm == "2"))
            {
                //extraFileBytes = ReadfileToByte.ReadFileToByte("M:\\PushToA8\\" + policyNo.Trim() + "_CI" + ".pdf");
            }
            else if ((Class == "EEC") && (fconfirm == "2"))
            {
                //extraFileBytes = ReadfileToByte.ReadFileToByte("M:\\PushToA8\\" + policyNo.Trim() + "_NI" + ".pdf");
            }
            else if ((Class == "EEC") && (fconfirm == "3"))
            {
                fileBytes = ReadfileToByte.ReadFileToByte("M:\\PushToA8\\" + policyNo.Trim() + ".pdf");
                extraFileBytes = ReadfileToByte.ReadFileToByte("M:\\PushToA8\\" + policyNo.Trim() + "_NI" + ".pdf");
            }
            else if ((Class == "PMP" || Class == "CMP") && (fconfirm == "3" || fconfirm == "Confirmed"))
            {
                fileBytes = ReadfileToByte.ReadFileToByte("M:\\PushToA8\\" + policyNo.Trim() + ".pdf");
                extraFileBytes = ReadfileToByte.ReadFileToByte("M:\\PushToA8\\" + policyNo.Trim() + "_CI" + ".pdf");
            }
            else if ((Class == "Business"))
            {
                fileBytes = ReadfileToByte.ReadFileToByte("M:\\PushToA8\\Business\\" + fconfirm + "\\" + policyNo.Trim() + ".pdf");
            }
            else if ((Class == "Claims"))
            {
                fileBytes = ReadfileToByte.ReadFileToByte("M:\\PushToA8\\Claims\\" + fconfirm + "\\" + policyNo.Trim() + ".pdf");
            }
            else if ((Class == "CoverNote"))
            {
                fileBytes = ReadfileToByte.ReadFileToByte("M:\\PushToA8\\" + policyNo.Trim() + ".pdf");
            }
            else
            {
                fileBytes = ReadfileToByte.ReadFileToByte("M:\\PushToA8\\" + policyNo.Trim() + ".pdf");
                extraFileBytes = ReadfileToByte.ReadFileToByte("M:\\PushToA8\\" + policyNo.Trim() + "_RI" + ".pdf");
            }
            String res = "";
            try
            {
                if ((Class == "Business") && fileBytes.Length > 0)
                {
                    res = PostFileToServer("https://pur.csci.com.hk/push/api/push", fileBytes, extraFileBytes, Encoding.UTF8, policyNo, fctlid, Class);
                }
                if ((Class == "Claims") && fileBytes.Length > 0)
                {
                    res = PostFileToServer("https://pur.csci.com.hk/push/api/push", fileBytes, extraFileBytes, Encoding.UTF8, policyNo, fctlid, Class);
                }
                if ((Class == "CoverNote"))
                {
                    res = PostFileToServer("https://pur.csci.com.hk/push/api/push", fileBytes, extraFileBytes, Encoding.UTF8, policyNo, fctlid, Class);
                }
                if ((Class != "Business") && (Class != "Claims"))
                {
                    res = PostFileToServer("https://pur.csci.com.hk/push/api/push", fileBytes, extraFileBytes, Encoding.UTF8, policyNo, fctlid, Class);
                }
            }
            catch (Exception ex)
            {
                return ex.ToString();
            }
            return res.ToString();
        }

        public static string PostFileToServer(string url, byte[] filedata, byte[] extraFileData, Encoding encoder, string policyNo, string fctlid, string Class)
        {
            HttpClient client = new HttpClient();
            var data = new { Fctlid = fctlid, Polno = policyNo, UserId = InsEnvironment.LoginUser.GetUserADCode(), 
                Postype = Class, FileContent = filedata, ExtraFileContent = extraFileData };
            var json = Newtonsoft.Json.JsonConvert.SerializeObject(data);
            HttpContent httpContent = new StringContent(json, Encoding.UTF8, "application/json");
            HttpResponseMessage response = client.PostAsync(url, httpContent).Result;
            var result = response.Content.ReadAsStringAsync().Result;
            var s = Newtonsoft.Json.JsonConvert.DeserializeObject(result);
            JavaScriptSerializer js = new JavaScriptSerializer();//实例化一个能够序列化数据的类
            JsonOutput oput = js.Deserialize<JsonOutput>(s.ToString()); //将json数据转化为对象类型并赋值给list
            return oput.Message;
        }

        public static string urlPushToA8 = @"\\*********\保險公司\COIL_DATA\PushToA8\";
        public static string urlSendToCustomer = "";
        public static string SignPloNote(string Filename, string fclass, string fpolno)
        {

            string pdfname = urlPushToA8 + Filename;
            urlSendToCustomer = @"\\*********\保險公司\COIL_DATA\COIL_ePolicy\" + fclass + @"\" + fpolno + @"\";
            string keyword = "Note No.";
            if (!System.IO.Directory.Exists(urlSendToCustomer))
            {
                System.IO.Directory.CreateDirectory(urlSendToCustomer);
            }

            if (readfilesSchedule(Filename) != "")
            {
                //Read file using PdfReader
                PdfReader pdfReader = new PdfReader(pdfname);
                int pages = pdfReader.NumberOfPages;
                //Modify file using PdfReader
                PdfStamper pdfStamper = new PdfStamper(pdfReader, new FileStream(urlSendToCustomer + Filename, FileMode.Open));

                for (int i = 1; i <= pages; i++)
                {

                    float[] position = PdfKeywordFinder.getAddImagePositionXY(pdfname, keyword);
                    iTextSharp.text.Image image = iTextSharp.text.Image.GetInstance(urlPushToA8 + "header.png");
                    //Fixed Positioning
                    image.ScaleAbsolute(550, 37);
                    //Scale to new height and new width of image
                    image.SetAbsolutePosition(position[1] - 70, position[2] + 120);
                    if (i % 2 != 0)
                    {
                        PdfContentByte header = pdfStamper.GetUnderContent(i);
                        //header.AddImage(image);
                    }
                }
                pdfStamper.Close();
                pdfReader.Close();
            }
            return "";
        }

        public static string SignClmNote(string Filename, string fclass, string fclmno)
        {

            string pdfname = urlPushToA8 + Filename;
            urlSendToCustomer = @"\\*********\保險公司\COIL_DATA\COIL_eClaim\" + fclass + @"\" + fclmno + @"\";
            string keyword = "Note No.";

            if (!System.IO.Directory.Exists(urlSendToCustomer))
            {
                System.IO.Directory.CreateDirectory(urlSendToCustomer);
            }

            if (readfilesSchedule(Filename) != "")
            {
                //Read file using PdfReader
                PdfReader pdfReader = new PdfReader(pdfname);
                int pages = pdfReader.NumberOfPages;
                //Modify file using PdfReader
                PdfStamper pdfStamper = new PdfStamper(pdfReader, new FileStream(urlSendToCustomer + Filename, FileMode.Open));

                for (int i = 1; i <= pages; i++)
                {

                    float[] position = PdfKeywordFinder.getAddImagePositionXY(pdfname, keyword);
                    iTextSharp.text.Image image = iTextSharp.text.Image.GetInstance(urlPushToA8 + "header.png");
                    //Fixed Positioning
                    image.ScaleAbsolute(550, 37);
                    //Scale to new height and new width of image
                    image.SetAbsolutePosition(position[1] - 70, position[2] + 120);
                    PdfContentByte header = pdfStamper.GetUnderContent(i);
                   // header.AddImage(image);
                }
                pdfStamper.Close();
                pdfReader.Close();
            }
            return "";
        }

        public static string CopyClmNote(string Filename, string fclass, string fclmno)
        {

            string pdfname = urlPushToA8 + Filename;
            urlSendToCustomer = @"\\*********\保險公司\COIL_DATA\COIL_eClaim\" + fclass + @"\" + fclmno + @"\";
            string keyword = "Note No.";

            if (!System.IO.Directory.Exists(urlSendToCustomer))
            {
                System.IO.Directory.CreateDirectory(urlSendToCustomer);
            }

            if (readfilesSchedule(Filename) != "")
            {
                //Read file using PdfReader
                PdfReader pdfReader = new PdfReader(pdfname);
                int pages = pdfReader.NumberOfPages;
                //Modify file using PdfReader
                PdfStamper pdfStamper = new PdfStamper(pdfReader, new FileStream(urlSendToCustomer + Filename, FileMode.Open));

                for (int i = 1; i <= pages; i++)
                {

                    float[] position = PdfKeywordFinder.getAddImagePositionXY(pdfname, keyword);
                    iTextSharp.text.Image image = iTextSharp.text.Image.GetInstance(urlPushToA8 + "header.png");
                    //Fixed Positioning
                    image.ScaleAbsolute(550, 37);
                    //Scale to new height and new width of image
                    image.SetAbsolutePosition(position[1] - 70, position[2] + 120);
                    if (i % 2 != 0)
                    {
                        PdfContentByte header = pdfStamper.GetUnderContent(i);
                        //header.AddImage(image);
                    }
                }
                pdfStamper.Close();
                pdfReader.Close();
            }
            return "";
        }
        public static string readfilesSchedule(string Filename)
        {
            int filesum = 0;  //更新文件数
            string LocalPath = urlSendToCustomer;
            //从配置文件App中取文件服务器更新目录：
            string ServerPath = urlPushToA8;
            if (Directory.Exists(ServerPath))
            {
                foreach (string SourceFile in Directory.GetFiles(ServerPath))  //循环取服务器更新路径文件
                {
                    string FileName = System.IO.Path.GetFileName(SourceFile);//取更新文件名   
                    //本地目录有相同文件名就需要判断是否为可用更新文件   
                    if (FileName == Filename)
                    {
                        if (File.Exists(LocalPath + FileName) == true)
                        {
                            DateTime dtLocal = File.GetLastWriteTime(LocalPath + FileName);//本地文件修改日期   
                            DateTime dtUpdate = File.GetLastWriteTime(SourceFile);//更新目录文件的修改日期   
                            if (dtUpdate != dtLocal)//可用更新   
                            {
                                ++filesum;
                                //File.Copy(SourceFile, LocalPath + FileName, true);
                                //File.Copy(SourceFile, LocalPath + FileName.Replace(".pdf", "_Copy.pdf"), true);
                            }
                        }
                        else
                        {
                            ++filesum;
                            File.Copy(SourceFile, LocalPath + FileName, true);
                        }
                    }
                }
                if (filesum > 0)
                {
                    return "刚才从服务器更新文件" + filesum.ToString() + "个";
                }
                else return "";
            }
            else
            {
                return "";
            }
        }
    }
}
