using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;

namespace INS.INSClass
{
    class SendEmail
    {
        public static string sendtoCus(string policyNo, string mailto)
        {
            try
            {
                SmtpClient mailServer = new SmtpClient("10.1.8.169", 587);
                mailServer.EnableSsl = false;
                mailServer.Credentials = new System.Net.NetworkCredential("<EMAIL>", "!3311!csci2019");

                string from = "<EMAIL>";
                string to = "<EMAIL>";
                MailMessage msg = new MailMessage(from, to);
                msg.Subject = "中國海外保險-電子保單";
                msg.Body = "尊敬的客戶，您好：附件中为您的电子保单，保单号:'" + policyNo +"'。如有疑问，请拨打電話28237324进行咨询。感谢您对我司的支持与理解！祝您生活愉快！";
                msg.Attachments.Add(new Attachment("M:\\SendToCustomer\\" + policyNo.Trim() + ".pdf"));
                mailServer.Send(msg);
                return  "發送成功";
            }
            catch (Exception ex)
            {
                return "發送失敗";
            }
        
        }
    }
}
