using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ScanToSign.PDF
{
    public class CharPosition
    {
        private int pageNum = 0;
        private float x = 0;
        private float y = 0;
        private float charWidth = 0; //单个文字的宽度

        public CharPosition(int pageNum, float x, float y, float charWidth)
        {
            this.pageNum = pageNum;
            this.x = x;
            this.y = y;
            this.charWidth = charWidth;
        }


        public virtual int PageNum
        {
            get
            {
                return pageNum;
            }
        }


        public virtual float X
        {
            get
            {
                return x;
            }
        }


        public virtual float Y
        {
            get
            {
                return y;
            }
        }

        public virtual float CharWidth
        {
            get
            {
                return charWidth;
            }
        }

        public override string ToString()
        {
            return "[pageNum=" + this.pageNum + ",x=" + this.x + ",y=" + this.y + "]";
        }
    }

}
