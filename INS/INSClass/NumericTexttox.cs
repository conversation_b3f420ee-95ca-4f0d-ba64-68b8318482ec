using System;
using System.Collections.Generic;
using System.Windows.Forms;
using System.Text;
using System.Threading;
using System.ComponentModel;
using System.Media;
using System.Globalization;

namespace INS.INSClass
{
    public class NumericTextBox : TextBox
    {
        bool allowSpace = false;
        private char sepratedChar = ',';
        private bool isDelKeyPress = false;
        private bool isKeyPress = false;
        private bool isBackPress = false;
        private Int64 maxValue = Int64.MaxValue;
        private InvalidSoundEnum invalidSound;


        public enum InvalidSoundEnum
        {
            None, Asterisk, Exclamation, Beep, Hand, Question
        }

        private void SetSepratedChar(int index)
        {
            isKeyPress = false;
            isDelKeyPress = false;
            string mins = "";
            if (Value.Contains("-")) {
                mins = "-";
            }
            if (Value.ToString().Replace("-", "") != "" && Value.ToString().Replace("-", "") != ".0")
            { 
            Int64 intValue = long.Parse(Value.ToString().Replace ("-","").Split('.')[0]);
            string decValue = "0";
            if (Value.ToString().Contains("."))
            {
                decValue = Value.ToString().Split('.')[1];
            }
            int selectionStart = this.SelectionStart;
            this.Clear();

            while (intValue >= 1000)
            {
                Int64 mod = Convert.ToInt64(intValue) % 1000;
                if (mod == 0)
                    this.Text = this.Text.Insert(0, sepratedChar.ToString() + "000");
                else if (mod.ToString().Length == 1)
                    this.Text = this.Text.Insert(0, sepratedChar.ToString() + "00" + mod.ToString());
                else if (mod.ToString().Length == 2)
                    this.Text = this.Text.Insert(0, sepratedChar.ToString() + "0" + mod.ToString());
                else
                    this.Text = this.Text.Insert(0, sepratedChar.ToString() + mod.ToString());

                intValue = intValue / 1000;
            }
            if (mins == "-")
            {
                this.Text = this.Text.Insert(0, mins.ToString()); 
                this.Text = this.Text.Insert(1, intValue.ToString()); }
            else { 
                this.Text = this.Text.Insert(0, intValue.ToString()); }
            this.Text = this.Text.Insert(this.Text.Length, "." + decValue.ToString());
            if (decValue.ToString() == "0" || decValue.ToString() == "")
            {
                this.SelectionStart = this.Text.LastIndexOf(".");
            }
            else { this.SelectionStart = this.Text.Length + index; }
            }
        }

        protected override void OnTextChanged(EventArgs e)
        {
            base.OnTextChanged(e);

            if (isKeyPress)
            {
                if (isBackPress)
                {
                    isBackPress = false;
                    if (this.SelectionStart == 0)
                    {
                        if (this.Text.Length == 0)
                            SetSepratedChar(1);
                        else
                            SetSepratedChar(0);
                    }
                    else if (this.Text.Replace(sepratedChar.ToString(), "").Length % 3 == 0)
                        SetSepratedChar(-1);
                    else
                        SetSepratedChar(0);
                }
                else if (this.Text.Replace(sepratedChar.ToString(), "").Length % 3 == 1 || this.Text.Length == 0)
                    SetSepratedChar(1);
                else
                    SetSepratedChar(0);
            }
            else if (isDelKeyPress)
            {
                if (this.SelectionStart == 0)
                {
                    if (this.Text.Length == 0)
                        SetSepratedChar(1);
                    else
                        SetSepratedChar(0);
                }
                else if (this.Text.Replace(sepratedChar.ToString(), "").Length % 3 == 0)
                    SetSepratedChar(-1);
                else
                    SetSepratedChar(0);
            }

        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            base.OnKeyDown(e);
            if (e.KeyData == Keys.Delete)
            {
                isDelKeyPress = true;
                if (this.SelectionStart < this.Text.Length - 1 && this.Text[this.SelectionStart] == sepratedChar)
                    this.SelectionStart += 1;
                return;
            }
            if (e.KeyData == Keys.Back)
            {
                if (this.SelectionStart >= 1 && this.Text[this.SelectionStart - 1] == sepratedChar)
                    this.SelectionStart -= 1;
                else
                    isBackPress = true;

                return;
            }
            if (e.KeyData == Keys.Left)
            {
                if (this.SelectionStart >= 2 && this.Text[this.SelectionStart - 2] == sepratedChar)
                    this.SelectionStart -= 1;
                return;
            }
            if (e.KeyData == Keys.Right)
            {
                if (this.SelectionStart < this.Text.Length - 1 && this.Text[this.SelectionStart + 1] == sepratedChar)
                    this.SelectionStart += 1;
                return;
            }

        }

        protected override void OnKeyPress(KeyPressEventArgs e)
        {
            isKeyPress = true;
            base.OnKeyPress(e);
            NumberFormatInfo numberFormatInfo = System.Globalization.CultureInfo.CurrentCulture.NumberFormat;
            string decimalSeparator = numberFormatInfo.NumberDecimalSeparator;
            string groupSeparator = numberFormatInfo.NumberGroupSeparator;
            string negativeSign = numberFormatInfo.NegativeSign;
            if (e.KeyChar.ToString().Replace("-", "") != "")
            {
                double val;
                if (this.Text.ToString().Replace("-", "") == "")
                {
                    val = 0.0;
                }
                else
                { 
                    val = (this.Text.Length == 0 ? 0 : double.Parse(this.Text.Replace(sepratedChar.ToString(), "").Replace("-", ""))); 
                }
                if (Char.IsDigit(e.KeyChar) && val * 10 + int.Parse(e.KeyChar.ToString()) <= maxValue)
                {
                    // Digits are OK
                }
                else if (e.KeyChar == '\b')
                {
                    // Backspace key is OK
                }
                else if ((ModifierKeys & (Keys.Control | Keys.Alt)) != 0)
                {
                    //ModifierKeys + ( Alt & Ctrl ) are OK
                }
                else if (this.SelectionLength > 0)
                {
                    // Selected Text OK
                }
                else if (e.KeyChar.Equals(negativeSign))
                {
                    // Decimal separator is OK
                }
                else
                {
                    e.Handled = true;
                    switch (InvalidSound)
                    {
                        case InvalidSoundEnum.None:
                            break;
                        case InvalidSoundEnum.Asterisk:
                            SystemSounds.Asterisk.Play();
                            break;
                        case InvalidSoundEnum.Beep:
                            SystemSounds.Beep.Play();
                            break;
                        case InvalidSoundEnum.Exclamation:
                            SystemSounds.Exclamation.Play();
                            break;
                        case InvalidSoundEnum.Hand:
                            SystemSounds.Hand.Play();
                            break;
                        case InvalidSoundEnum.Question:
                            SystemSounds.Question.Play();
                            break;
                        default:
                            break;

                    }
                }
            }
        }

        [Description("number was entered in textBox as type of Int64")]
        [Browsable(false)]
        public string Value
        {
            get
            {
                if (this.Text.Length != 0)
                    return this.Text.Replace(sepratedChar.ToString(), string.Empty);
                else
                    return "0";
            }
        }


        /// <summary>
        /// Gets or sets the Max value Betwwen 0 and Int64.MaxValue
        /// </summary>
        /// 
        [Description("Max of number you needed")]
        public Int64 MaxValue
        {
            set { maxValue = value; }
            get { return maxValue; }
        }

        [Description("Invalid sound that user heard when pressed invalid button")]
        public InvalidSoundEnum InvalidSound
        {
            set { invalidSound = value; }
            get { return invalidSound; }
        }

        [Description("This character placed between number")]
        public char SepratedChar
        {
            set { sepratedChar = value; }
            get { return sepratedChar; }
        }


        public bool AllowSpace
        {
            set
            {
                this.allowSpace = value;
            }

            get
            {
                return this.allowSpace;
            }
        }
    }
}
