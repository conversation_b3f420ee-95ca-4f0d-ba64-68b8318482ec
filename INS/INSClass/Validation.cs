using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Forms;


namespace INS.INSClass
{
    public static class Validation
    {
        public static Boolean CheckValidInt(TextBox text_obj)
        {

            String testedString = text_obj.Text.Trim();
            Int32 strLength;
            Boolean ll_ok;
            String pattern = @"^[0-9]*$";

            strLength = testedString.Length;

            ll_ok = Regex.IsMatch(testedString, pattern);
            if (!ll_ok)
                return ll_ok;

            for (int i = 0; i < strLength; i++)
            {
                String xChar = testedString.Substring(i, 1);

                ll_ok = i != 0 ? true : xChar != "0" ? true : false;
                if (!ll_ok)
                    break;
            }
            return ll_ok;
        }

        public static Boolean CheckValidYear(TextBox text_obj)
        {
            String testedString = text_obj.Text.Trim();
            Int32 strLength;
            Boolean ll_ok;

            strLength = testedString.Length;

            ll_ok = strLength == 4;
            if (!ll_ok)
                return ll_ok;

            for (int i = 0; i < strLength; i++)
            {
                String xChar = testedString.Substring(i, 1);

                ll_ok = "0123456789".Contains(xChar);
                if (!ll_ok)
                    break;

                ll_ok = i != 0 ? true : xChar != "0" ? true : false;
                if (!ll_ok)
                    break;

                ll_ok = i != 1 ? true : xChar == "9" ? true : testedString.Substring(0, 1) != "1";
                if (!ll_ok)
                    break;
            }

            if (!ll_ok)
                return ll_ok;

            return ll_ok;
        }

        public static Boolean CheckEmptyString(Control text_obj)
        {
            if (text_obj.Text.Trim() == "" || text_obj.Text.Trim() == "    .  .")
            {
                MessageBox.Show("Invalid Value");
                text_obj.Focus();
                text_obj.Select();
                return false;
            }
            return true;
        }
    }
}
