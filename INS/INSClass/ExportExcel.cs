using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Threading;
using System.Windows.Forms;

namespace INS.INSClass
{
    public class ExpExl {
        public static string DtToExcel(System.Data.DataTable dt, string[] captions, string[] fieldNames, string sheetName, bool isOpen)
        {
            //返回的信息  
            string str = "";
            //保存对话框  
            SaveFileDialog sfd = new SaveFileDialog();
            sfd.Filter = "(*.xls,*.xlsx)|*.xls;*.xlsx";
            //保存的文件名称  
            string fileName = "";
            if (sfd.ShowDialog() != DialogResult.OK)
            {
                return "";
            }
            if (dt == null)
            {
                str = "表不能为空";
                return str;
            }
            if (captions.Length != fieldNames.Length)
            {
                str = "列名数目和列标题数目不匹配";
                return str;
            }
            foreach (string aa in fieldNames)
            {
                if (!dt.Columns.Contains(aa))
                {
                    str = "表中不存在列名为 " + aa + " 的列";
                    return str;
                }
            }
            //如果不是全部列都要导出，改变一下样式  
            DataView dv = new DataView(dt);
            dt = dv.ToTable(false, fieldNames);

            //导出路径  
            fileName = sfd.FileName;
            //定义Excel程序  
            Microsoft.Office.Interop.Excel.Application excel = new Microsoft.Office.Interop.Excel.Application();
            //判断是否安装Excel  
            if (excel == null)
            {
                str = "无法创建Excel对象,可能您的计算机未安装Excel!";
                return str;
            }
            //是否显示导出过程 ，建议关闭，否则在导出过程中鼠标点击Excel文件时会出错  
            excel.Visible = false;
            //定义一个Excel文件  
            Microsoft.Office.Interop.Excel.Workbook wBook = excel.Workbooks.Add(System.Reflection.Missing.Value);
            //定义工作表  
            Microsoft.Office.Interop.Excel.Worksheet wSheet = (Microsoft.Office.Interop.Excel.Worksheet)wBook.Worksheets[1];
            Microsoft.Office.Interop.Excel.Range range;
            //获取行数、列数  
            int rowCount = dt.Rows.Count;
            int colCount = captions.Length;

            //设置表格名称  
            if (sheetName != null && sheetName.Length > 0)
            {
                wSheet.Name = sheetName;
            }
            else
            {
                wSheet.Name = dt.TableName;
            }

            if (rowCount < 1)
            {
                str = "没有记录可以导出";
                return str;
            }
            try
            {
                excel.Cells[1, 1] = "Sample Test Data";
                excel.Cells[2, 1] = "Date : " + DateTime.Now.ToShortDateString();
                //写标题,标题居中  
                for (int j = 1; j < captions.Length + 1; j++)
                {
                    excel.Cells[3, j] = captions[j - 1];
                }

                //设置对齐方式  
                wSheet.Cells.HorizontalAlignment = Microsoft.Office.Interop.Excel.XlHAlign.xlHAlignCenter;
                //设置第一行高度，即标题栏  
                ((Microsoft.Office.Interop.Excel.Range)wSheet.Rows["1:1", System.Type.Missing]).RowHeight = 20;
                //设置数据行行高度  
                ((Microsoft.Office.Interop.Excel.Range)wSheet.Rows["2:" + dt.Rows.Count + 3, System.Type.Missing]).RowHeight = 16;
                //设置字体大小（10号字体）  
                wSheet.Range[wSheet.Cells[1, 1], wSheet.Cells[dt.Rows.Count + 3, dt.Columns.Count]].Font.Size = 10;
                //设置单元格边框  
                range = wSheet.Range[wSheet.Cells[1, 1], wSheet.Cells[dt.Rows.Count + 3, dt.Columns.Count]];
                Microsoft.Office.Interop.Excel.Borders border = range.Borders;
                border.LineStyle = Microsoft.Office.Interop.Excel.XlLineStyle.xlContinuous;
                border.Weight = 2d;

                FormattingExcelCells(wSheet.Range[wSheet.Cells[1, 1], wSheet.Cells[1, dt.Columns.Count]], true, "GRAY", 10, "", "", true);
                FormattingExcelCells(wSheet.Range[wSheet.Cells[2, 1], wSheet.Cells[2, dt.Columns.Count]], true, "YELLOW", 10, "", "", true);
                FormattingExcelCells(wSheet.Range[wSheet.Cells[3, 1], wSheet.Cells[3, dt.Columns.Count]], false, "Turquoise", 10, "", "", true);
                FormattingExcelCells(wSheet.Range[wSheet.Cells[4, 3], wSheet.Cells[dt.Rows.Count + 3, 3]], false, "", 10, "", "#,##0", false);
                FormattingExcelCells(wSheet.Range[wSheet.Cells[4, 2], wSheet.Cells[dt.Rows.Count + 3, 2]], false, "", 5, "", "", false);
                //往Excel写数据  
                for (int m = 0; m < rowCount; m++)
                {
                    for (int n = 0; n < colCount; n++)
                    {
                        if (m % 2 != 0) { FormattingExcelAlterRows(wSheet.Range[wSheet.Cells[m + 4, n + 1], wSheet.Cells[m + 4, n + 1]], "PeachPuff", false); }
                        excel.Cells[m + 4, n + 1] = (object)dt.Rows[m][n] is DBNull ? "" : dt.Rows[m][n].ToString().Trim();
                    }
                }

                excel.Cells[dt.Rows.Count + 4, 2] = "Sub Total:";
                excel.Cells[dt.Rows.Count + 4, 3] = "=Sum(C4:C"+(dt.Rows.Count + 3)+")";
                
                //保存工作表  
                wSheet.SaveAs(fileName, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing, Type.Missing);
                wBook.Close(Type.Missing, Type.Missing, Type.Missing);

                ////设置EXLCE最大化  
                //excel.WindowState = XlWindowState.xlMaximized;  

                //提示信息  
                str = "导出成功！ 共导出 " + rowCount + " 条数据";
                return str;
            }
            catch (Exception ex)
            {
                str = "导出异常，请重试";
                return str;
            }
            finally
            {
                if (isOpen)
                {
                    //前台显示给用户  
                    excel.Visible = true;
                    wSheet = null;
                    excel = null;
                }
                else
                {
                    excel.Visible = false;
                    excel.Quit();
                    // excel = null;  
                    //杀死Excel 进程  
                   // KillProcess(excel);
                }

                GC.Collect();
            }

        }

        public static void FormattingExcelCells(Microsoft.Office.Interop.Excel.Range range,bool IsMergebool, string InteriorColor,int Size, string fontColor, string Formart,bool IsFontbool)
        {
            range.MergeCells = IsMergebool;
            range.Interior.Color = GetColorValue(InteriorColor);
            range.Borders.Color = System.Drawing.Color.Black.ToArgb();
            range.ColumnWidth = Size;
            range.Font.Color = string.IsNullOrEmpty(fontColor) ? System.Drawing.Color.White.ToArgb() : System.Drawing.Color.Black.ToArgb();
            range.NumberFormat = Formart;
            if (IsFontbool == true)
            {
                range.Font.Bold = IsFontbool;
            }
        }

        public static void FormattingExcelAlterRows(Microsoft.Office.Interop.Excel.Range range, string InteriorColor, bool IsFontbool)
        {
            range.Interior.Color = GetColorValue(InteriorColor);
            range.Borders.Color = System.Drawing.Color.Black.ToArgb();
            if (IsFontbool == true)
            {
                range.Font.Bold = IsFontbool;
            }
        }

        private static int GetColorValue(string interiorColor)
        {
            switch (interiorColor)
            {
                case "YELLOW":
                    return System.Drawing.Color.Yellow.ToArgb();
                case "GRAY":
                    return System.Drawing.Color.Gray.ToArgb();
                case "GAINSBORO":
                    return System.Drawing.Color.Gainsboro.ToArgb();
                case "Turquoise":
                    return System.Drawing.Color.Turquoise.ToArgb();
                case "PeachPuff":
                    return System.Drawing.Color.PeachPuff.ToArgb();

                default:
                    return System.Drawing.Color.White.ToArgb();
            }
        }

    } 

}
