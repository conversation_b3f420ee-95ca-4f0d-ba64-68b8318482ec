using System;
//using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Data.SqlClient;
using System.Collections;

namespace INS.INSClass
{
    class RptSetFunc
    {
        public static void SetQueryParam(ref String vfbus, ref String wfbus, ref String vttytype, ref String vrowfilter,
            String vfnew, String vclasssel, String vfdbtr)
        {
            if (vfbus == "A")
            {
                vfbus = "%";
                wfbus = "%";
            }
            else if (vfbus == "D")
                wfbus = "K";
            else if (vfbus == "E")
            {
                vfbus = "D";
                wfbus = "D";
            }
            else if (vfbus == "K")
                wfbus = "K";
            else if (vfbus == "R")
                wfbus = "R";

            if (vclasssel == "A")
                vrowfilter = "";
            else if ("PMP|CMP".Contains(vclasssel))
                vrowfilter = string.Format("(fclass= '{0}' or fclass= '{1}')", "PMP", "CMP");
            else vrowfilter = string.Format("(fclass= '{0}')", vclasssel);

            if (vfdbtr != "")
            {
                string tstr;
                if (vclasssel == "A")
                    tstr = string.Format("fdbtr = '{0}'", vfdbtr);
                else
                    tstr = string.Format(" and fdbtr = '{0}'", vfdbtr);

                vrowfilter = vrowfilter + tstr;
            }

            if (vfnew == "A")
                vfnew = "%";
            else if (vfnew == "1")
                vfnew = "Y";
            else
                vfnew = "N";

            if (vfnew != "%")
            {
                string tstr;
                if (vrowfilter == "")
                    tstr = string.Format("fnew = '{0}'", vfnew);
                else
                    tstr = string.Format(" and fnew = '{0}'", vfnew);

                vrowfilter = vrowfilter + tstr;
            }

            vttytype = vttytype.Equals("1") ? "02" :  vttytype.Equals("2") ? "03" : "05" ;
        }

        public static void SetQueryParam_Clm(ref String vfbus, ref String wfbus, ref String vrowfilter, String vclasssel, 
            DateTime vflos_fr, DateTime vflos_to, String rpoptid, Boolean vfrngechk, Decimal vfNrnge_fr, Decimal vfNrnge_to, 
            String vfpolno, String vfinsd, String vfbrkr, String vTtyCode)
        {
            if (vfbus == "A")
            {
                vfbus = "%";
                wfbus = "%";
            }
            else if (vfbus == "D")
                wfbus = "K";
            else if (vfbus == "E")
            {
                vfbus = "D";
                wfbus = "D";
            }
            else if (vfbus == "K")
                wfbus = "K";
            else if (vfbus == "R")
                wfbus = "R";

            if (vclasssel == "A")
                vrowfilter = "";
            else if ("PMP|CMP".Contains(vclasssel))
                vrowfilter = string.Format("(fclass= '{0}' or fclass= '{1}')", "PMP", "CMP");
            else vrowfilter = string.Format("(fclass= '{0}')", vclasssel);

            if (!"ttypyclm|ttyosclm".Contains(rpoptid))
            {
                string tstr;
                tstr = (vrowfilter == "" ? "" : " and ") + String.Format("flosdate >= #{0}# and flosdate <= #{1}#",
                    vflos_fr.ToString("yyyy-MM-dd"),vflos_to.ToString("yyyy-MM-dd"));
                vrowfilter = vrowfilter + tstr;
            }

            if (vfpolno != "")
            {
                string tstr;
                tstr = (vrowfilter == "" ? "" : " and ") + String.Format("fpolno like '{0}'",vfpolno.Trim());
                vrowfilter = vrowfilter + tstr;
            }

            if (vfinsd != "")
            {
                string tstr;
                tstr = (vrowfilter == "" ? "" : " and ") + String.Format("finsd like '{0}'", vfinsd.Trim());
                vrowfilter = vrowfilter + tstr;
            }

            if (vfbrkr != "")
            {
                string tstr;
                tstr = (vrowfilter == "" ? "" : " and ") + String.Format("fdbtr like '{0}'", vfbrkr.Trim());
                vrowfilter = vrowfilter + tstr;
            }

            if (vTtyCode != "A")
            {
                string tstr;
                tstr = (vrowfilter == "" ? "" : " and ") + String.Format("fttycode like '{0}'", vTtyCode.Trim());
                vrowfilter = vrowfilter + tstr;
            }

            if (vfrngechk)
            {
                string tstr,rngeVar="";
                if (rpoptid == "clmpay") rngeVar = "famount";
                if (rpoptid == "osclm") rngeVar = "yos";
                if (rpoptid == "clsclm") rngeVar = "famount";
                if (rpoptid == "clseec") rngeVar = "famount";
                if (rpoptid == "cstatus") rngeVar = "mres";

                tstr = (vrowfilter == "" ? "" : " and ") + String.Format("{0} > = {1} and {0} <= {2}", rngeVar,
                            vfNrnge_fr, vfNrnge_to);
                vrowfilter = vrowfilter + tstr;
            }

            //if (rpoptid == "devstat")
            //{
            //    string tstr;
            //    tstr = (vrowfilter == "" ? "" : " and ") + String.Format("fdstat like '{0}'", "1");
            //    vrowfilter = vrowfilter + tstr;
            //}
        }

        public static void SetRptFormulae(ref String rpBusiness, ref String rpClasssel, 
                                          ref String rpDatefr,ref String rpDateto, ref String rpTitle,
                                          String vfbus, String wfbus, String vclasssel, DateTime vfbk_fr, DateTime vfbk_to,
                                          String vttytype,String vperiodtype, String rpoptid)
        {
            if (vfbus == "%")
                rpBusiness = "(Direct + Fac Inward)";
            else if (vfbus == "D" && wfbus == "K")
                rpBusiness = "(Direct Business)";
            else if (vfbus == "D" && wfbus == "D")
                rpBusiness = "(Direct Only)";
            else if (vfbus == "K" && wfbus == "K")
                rpBusiness = "(Co In Only)";
            else
                rpBusiness = "(Facultative Inward)";




            if (rpoptid == "polprmsm")
            {
                if (vfbus == "%")
                    rpBusiness = "(直接 + 分入業務)";
                else if (vfbus == "D")
                    rpBusiness = "(直接業務)";
                else
                    rpBusiness = "(分入業務)";
            }

            if (vclasssel == "A")
                rpClasssel = "";
            else if ("PMP|CMP".Contains(vclasssel))
                rpClasssel = "Motor";
            else
                rpClasssel = vclasssel;

            rpDatefr = String.Format("{0:dd MMM yyyy}", vfbk_fr);
            rpDateto = String.Format("{0:dd MMM yyyy}", vfbk_to);

            String lastasat = String.Format("{0:dd MMM yyyy}", vfbk_fr.AddDays(-1));

            String treaty_desc = vttytype.Equals("02") ? "Surplus Treaty" : vttytype.Equals("03") ? "XOL" : 
                            "Facultative Obligatory";

            if (rpoptid == "prmreg")
                rpTitle = String.Format("{0} Premium Register I {1} from {2} to {3} - {4}" ,
                                            rpClasssel, rpBusiness, rpDatefr, rpDateto, vperiodtype == "1" ? "Billing Period" : "Booking Period").Trim();
            else if (rpoptid == "polprm")
                rpTitle = String.Format("{0} Policy Premium Register {1} from {2} to {3} - {4}",
                                            rpClasssel, rpBusiness, rpDatefr, rpDateto, vperiodtype == "1" ? "Billing Period" : "Booking Period").Trim();
            else if (rpoptid == "prmrel")
                rpTitle = "Related Party Transactions";
            else if (rpoptid == "prmdrcr")
                rpTitle = String.Format("{0} Premium Register by DR/CR No. {1} from {2} to {3} - {4}",
                                            rpClasssel, rpBusiness, rpDatefr, rpDateto, vperiodtype == "1" ? "Billing Period" : "Booking Period").Trim();
            else if (rpoptid == "plvyia")
                rpTitle = String.Format("{0} IA Levy on Policy Premium Basis {1} from {2} to {3} - {4}",
                            rpClasssel, rpBusiness, rpDatefr, rpDateto, vperiodtype == "1" ? "Billing Period" : "Booking Period").Trim();
            else if (rpoptid == "prmuniv")
                rpTitle = String.Format("Un-Invoiced Premium Analysis for the Period: {0} to {1}",
                                            rpDatefr, rpDateto).Trim();
            else if (rpoptid == "prmgrnd")
                rpTitle = String.Format("Grand Premium Report {0} from {1} to {2}",
                                            rpBusiness, rpDatefr, rpDateto).Trim();
            else if (rpoptid == "daqcost")
                rpTitle = String.Format("Deferred Acquisition Costs {0} from {1} to {2}",
                                            rpBusiness, rpDatefr, rpDateto).Trim();
            else if (rpoptid == "prmunern")
                rpTitle = String.Format("Unearned Premium Summary {0} as at {1}",
                                            rpBusiness, lastasat).Trim();
            else if (rpoptid == "prmexpl")
                rpTitle = String.Format("Expiry List {0} from {1} to {2}",
                                            rpBusiness, rpDatefr, rpDateto).Trim();
            else if (rpoptid == "effpmp")
                rpTitle = String.Format("Effective Motor Policy {0} as at {1}",
                                            rpBusiness, rpDateto).Trim();
            else if (rpoptid == "prmstat")
                rpTitle = String.Format("Written Premium Statistics by Underwriting Year {0}",
                                            rpBusiness).Trim();
            else if (rpoptid == "prmfacout")
                rpTitle = String.Format("Facultative Outward Reinsurance {0} from {1} to {2}",
                                            rpBusiness, rpDatefr, rpDateto).Trim();
            else if (rpoptid == "prmtty")
                rpTitle = String.Format("{0} Reinsurance {1} from {2} to {3}",
                                            treaty_desc, rpBusiness, rpDatefr, rpDateto).Trim();
            else if (rpoptid == "prmbord" && vttytype == "02")
                rpTitle = "Non Marine Surplus Treaty";
            else if (rpoptid == "prmbord" && vttytype == "05")
                rpTitle = "Engineering Facultative Obligatory";
            else if (rpoptid == "prmanly")
                rpTitle = String.Format("Premium Register II {0} from {1} to {2}",
                                            rpBusiness, rpDatefr, rpDateto).Trim();
            else if (rpoptid == "eecanly")
                rpTitle = String.Format("EEC Statistics for IA {0} from {1} to {2}",
                                            rpBusiness, rpDatefr, rpDateto).Trim();
            else if (rpoptid == "polprmsm")
                rpTitle = String.Format("{0:yyyy}年至{1:MM}月成交額統計表 {2}",
                                            vfbk_to,vfbk_to,rpBusiness);
        }


        public static void SetRptFormulaeClm(ref String rpBusiness, ref String rpClasssel,
                                      ref String rpDatefr, ref String rpDateto, ref String rpTitle, ref String rpCriterion,
                                      ref String rpGrEq, String vfbus, String wfbus, String vclasssel, DateTime vfbk_fr,
                                      DateTime vfbk_to, DateTime vflos_fr, DateTime vflos_to, String vfpolno, String vfinsd, 
                                      Boolean vfrngechk, Decimal vfNrnge_fr, Decimal vfNrnge_to, String vGRN,
                                      String vSumType, String vSumYr, String vRepType,String vTtyType, String rpCurrency, String rpoptid)
        {
            if (vfbus == "%")
                rpBusiness = "(Direct + Fac Inward)";
            else if (vfbus == "D" && wfbus == "K")
                rpBusiness = "(Direct Business)";
            else if (vfbus == "D" && wfbus == "D")
                rpBusiness = "(Direct Only)";
            else if (vfbus == "K" && wfbus == "K")
                rpBusiness = "(Co In Only)";
            else
                rpBusiness = "(Facultative Inward)";

            if (vclasssel == "A")
                rpClasssel = "All";
            else if ("PMP|CMP".Contains(vclasssel))
                rpClasssel = "Motor";
            else
                rpClasssel = vclasssel;

            rpCriterion = String.Format("Class: {0}", rpClasssel);
            
            rpDatefr = String.Format("{0:dd MMM yyyy}", vfbk_fr);
            rpDateto = String.Format("{0:dd MMM yyyy}", vfbk_to);

            String chiDatefr = String.Format("{0}年{1}月{2}日", vfbk_fr.Year,vfbk_fr.Month,vfbk_fr.Day);
            String chiDateto = String.Format("{0}年{1}月{2}日", vfbk_to.Year, vfbk_to.Month, vfbk_to.Day);

            String lastasat = String.Format("{0:dd MMM yyyy}", vfbk_fr.AddDays(-1));

            String lc_regpd = vfbk_fr.ToString("yyyyMMdd") == "19000101" ? "" :
                    String.Format("     Reg Period: {0} to {1}", vfbk_fr.ToString("dd MMM yyyy"), vfbk_to.ToString("dd MMM yyyy"));

            if (rpoptid != "cstatus")
                lc_regpd = "";

            String lc_losspd = vflos_fr.ToString("yyyyMMdd") == "19000101" && vflos_to.ToString("yyyyMMdd") == "99991231" ? "":
                String.Format("     Loss Period: {0} to {1}", vflos_fr.ToString("dd MMM yyyy"), vflos_to.ToString("dd MMM yyyy"));

            String lc_fpolno = vfpolno.Trim() == "" || vfpolno.Trim() == "%" ? "" : "     Policy No.: " + vfpolno.Trim();
            String lc_finsd = vfinsd.Trim() == "" || vfinsd.Trim() == "%" ? "" : "     Insured: " + vfinsd.Trim();

            if (rpoptid == "clmpay")
                rpTitle = String.Format("Claims Paid Bordereaux {0} from {1} to {2}", rpBusiness, rpDatefr, rpDateto).Trim();
            else if (rpoptid == "clmrcv")
                rpTitle = String.Format("Claims Recovery Bordereaux {0} from {1} to {2}", rpBusiness, rpDatefr, rpDateto).Trim();
            else if (rpoptid == "newclm")
                rpTitle = String.Format("Registered Claims {0} from {1} to {2}", rpBusiness, rpDatefr, rpDateto).Trim();
            else if (rpoptid == "adjclm")
                rpTitle = String.Format("Adjustment Bordereaux {0} from {1} to {2}", rpBusiness, rpDatefr, rpDateto).Trim();
            else if (rpoptid == "osclm")
                rpTitle = String.Format("Outstanding Claims Bordereaux {0} as at {1}", rpBusiness, rpDateto).Trim();
            else if (rpoptid == "clsclm")
                rpTitle = String.Format("Closed Claims {0} from {1} to {2}", rpBusiness, rpDatefr, rpDateto).Trim();
            else if (rpoptid == "clseec")
                rpTitle = String.Format("Closed Claims (Direct EC Only) from {0} to {1}", rpDatefr, rpDateto).Trim();
            else if (rpoptid == "cstatus"|| rpoptid == "cstatus2")
                rpTitle = String.Format("Claims Status Report {0} as at {1}", rpBusiness, rpDateto);
            else if (rpoptid == "devstat")
                rpTitle = String.Format("Development Statistics {0} as at {1}", rpBusiness, rpDateto);
            else if (rpoptid == "sumclm")
                rpTitle = String.Format("Claims Transaction Summary {0} from {1} to {2}", rpBusiness, rpDatefr, rpDateto).Trim();
            else if (rpoptid == "mvesum")
                rpTitle = String.Format("Claims Movement {0} from {1} to {2}", rpBusiness, rpDatefr, rpDateto).Trim();
            else if (rpoptid == "pmpmvesum")
                rpTitle = String.Format("Motor Claims Analysis {0} from {1} to {2}", rpBusiness, rpDatefr, rpDateto).Trim();
            else if (rpoptid == "ecadjbd")
                rpTitle = String.Format("Adjustment Breakdown {0} from {1} to {2}", rpBusiness, rpDatefr, rpDateto).Trim();
            else if (rpoptid == "eclosanly")
                rpTitle = String.Format("訴訟費用統計表 (理賠個案結束日期: {0} 至 {1})", chiDatefr, chiDateto).Trim();
            else if (rpoptid == "ecclm(cohl)")
                rpTitle = String.Format("中海建築項目索償 (本期: {0} 至 {1})", chiDatefr, chiDateto).Trim();
            else if (rpoptid == "clmstat")
                rpTitle = String.Format("{0} {1} Statistics by {2} {3}", vGRN == "1" ? "Gross" : "Net",
                    vSumType == "1" ? "Claims Paid" : vSumType == "2" ? "Incurred Loss" : "O/S Claims",
                    vSumYr == "1" ? "Underwriting Year" : "Year of Loss", rpBusiness).Trim();
            else if (rpoptid == "facopaid")
                rpTitle = String.Format("Facultative Outward Claims Paid {0} from {1} to {2} - {3}", rpBusiness, rpDatefr, rpDateto,
                    vRepType == "1" ? "Summary" : "Details").Trim();
            else if (rpoptid == "facorcv")
                rpTitle = String.Format("Facultative Outward Claims Recovery {0} from {1} to {2} - {3}", rpBusiness, rpDatefr, rpDateto,
                    vRepType == "1" ? "Summary" : "Details").Trim();
            else if (rpoptid == "facoos")
                rpTitle = String.Format("Outstanding Claims Bordereaux {0}as at {1}", vfbus == "%" ? "" : rpBusiness + " ", rpDateto);
            else if (rpoptid == "ttypaid")
                rpTitle = String.Format("{0} Claims Paid {1} from {2} to {3} - {4}",
                    vTtyType == "1" ? "Surplus Treaty" : vTtyType == "2" ? "XOL" : "Facultative Obligatory",
                    rpBusiness, rpDatefr, rpDateto,
                    vRepType == "1" ? "Summary" : "Details").Trim();
            else if (rpoptid == "ttyrcv")
                rpTitle = String.Format("{0} Claims Recovery {1} from {2} to {3} - {4}",
                    vTtyType == "1" ? "Surplus Treaty" : vTtyType == "2" ? "XOL" : "Facultative Obligatory",
                    rpBusiness, rpDatefr, rpDateto,
                    vRepType == "1" ? "Summary" : "Details").Trim();
            else if (rpoptid == "ttypyclm")
                rpTitle = String.Format("Claims Paid Bordereaux: {0} to {1}", rpDatefr, rpDateto).Trim();
            else if (rpoptid == "ttyosclm")
                rpTitle = String.Format("Outstanding Claims Bordereaux as at {0}", rpDateto).Trim();

            rpCriterion = (rpoptid == "clseec" ? "" : rpCriterion) + lc_regpd + lc_losspd + lc_fpolno + lc_finsd;
            rpGrEq = "";

            if (vfrngechk && "clmpay".Contains(rpoptid))
            {
                if (vfNrnge_fr == vfNrnge_to)
                    rpGrEq = String.Format("Paid Amount = {0}{1:N0}", rpCurrency, vfNrnge_fr);
                else
                    rpGrEq = String.Format("Paid Amount between {0}{1:N0} and {2:N0}", rpCurrency, vfNrnge_fr, vfNrnge_to);
            }

            if (vfrngechk && "osclm".Contains(rpoptid))
            {
                if (vfNrnge_fr == vfNrnge_to)
                    rpGrEq = String.Format("O/S Amount = {0}{1:N0}", rpCurrency, vfNrnge_fr);
                else
                    rpGrEq = String.Format("O/S Amount between {0}{1:N0} and {2:N0}", rpCurrency, vfNrnge_fr, vfNrnge_to);
            }

            if (vfrngechk && "clsclm|clseec".Contains(rpoptid))
            {
                if (vfNrnge_fr == vfNrnge_to)
                    rpGrEq = String.Format("Loss Amount = {0}{1:N0}", rpCurrency, vfNrnge_fr);
                else
                    rpGrEq = String.Format("Loss Amount between {0}{1:N0} and {2:N0}", rpCurrency, vfNrnge_fr, vfNrnge_to);
            }

            if (vfrngechk && "cstatus".Contains(rpoptid))
            {
                if (vfNrnge_fr == vfNrnge_to)
                    rpGrEq = String.Format("Incurred Loss = {0}{1:N0}", rpCurrency, vfNrnge_fr);
                else
                    rpGrEq = String.Format("Incurred Loss between {0}{1:N0} and {2:N0}", rpCurrency, vfNrnge_fr, vfNrnge_to);
            }

            if (vfrngechk && "cstatus2".Contains(rpoptid))
            {
                if (vfNrnge_fr == vfNrnge_to)
                    rpGrEq = String.Format("Incurred Loss = {0}{1:N0}", rpCurrency, vfNrnge_fr);
                else
                    rpGrEq = String.Format("Incurred Loss between {0}{1:N0} and {2:N0}", rpCurrency, vfNrnge_fr, vfNrnge_to);
            }
        }
    }
        
    class MastDataFunc
    {
    }

    partial class PremDataFunc
    {
        public static DateTime Get_Annivery(DateTime p_incpdate, DateTime p_effdate)
        {
            DateTime ld_annivery = DateTime.ParseExact(p_incpdate.ToString("yyyy.MM.dd"),
                                "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);
            DateTime ld_effdate = DateTime.ParseExact(p_effdate.ToString("yyyy.MM.dd"),
                                "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);

            while (true)
            {
                if (ld_effdate < ld_annivery.AddMonths(12))
                    break;

                ld_annivery = ld_annivery.AddMonths(12);
            }

            return ld_annivery;
        }

        public static Decimal Get_LevyRate(DataView dvmlvydet, String p_lvytype, String p_class,
            DateTime p_effdate)
        {
            dvmlvydet.RowFilter = string.Format("flvytype = '{0}' and fclass = '{1}' and "+
                "fefffr <= #{2}# and feffto >= #{2}#", p_lvytype, p_class, p_effdate.ToString("yyyy-MM-dd"));
            
            Decimal ln_rate = 0;

            if (dvmlvydet.Count != 0)
                ln_rate = Convert.ToDecimal(dvmlvydet[0]["frate"]);

            return ln_rate;
        }

        public static Decimal Get_LevyLimit(DataView dvmlvydet, String p_lvytype, String p_class,
            DateTime p_effdate)
        {
            dvmlvydet.RowFilter = string.Format("flvytype = '{0}' and fclass = '{1}' and " +
                "fefffr <= #{2}# and feffto >= #{2}#", p_lvytype, p_class, p_effdate.ToString("yyyy-MM-dd"));

            Decimal ln_upperlmt = 0;

            if (dvmlvydet.Count != 0)
                ln_upperlmt = Convert.ToDecimal(dvmlvydet[0]["fupperlmt"]);

            return ln_upperlmt;
        }

        public static DataTable GenEff_PMP(DataTable dtpolh, DataTable dtinsint_e)
        {
            DataView dvpolh = dtpolh.DefaultView;

            foreach (DataRow dr in dtinsint_e.Rows)
            {
                String fctlid_p = dr["fctlid_p"].ToString().Trim(), fclass = dr["fclass"].ToString().Trim(),
                       fsclass = dr["fsclass"].ToString().Trim(), fkind = dr["fkind"].ToString().Trim();

                dr["fcover"] = fkind.Equals("1") ? "Comprehensive" : "Third Party";

                dvpolh.RowFilter = string.Format("fctlid_p = '{0}'", fctlid_p);

                if (dvpolh.Count != 0)
                {
                    fclass = dvpolh[0]["fclass"].ToString().Trim();
                    fsclass = dvpolh[0]["fsclass"].ToString().Trim();

                    dr["fclass"] = fclass;
                    dr["fsclass"] = fsclass;
                    dr["finsd"] = dvpolh[0]["finsd"];
                    dr["fprdr"] = dvpolh[0]["fprdr"];
                    dr["fclnt"] = dvpolh[0]["fclnt"];
                    dr["fissdate"] = Convert.ToDateTime(dvpolh[0]["fissdate"]);
                    dr["fincfr"] = Convert.ToDateTime(dvpolh[0]["fincfr"]);
                    dr["fincto"] = Convert.ToDateTime(dvpolh[0]["fincto"]);
                    dr["ftpbi"] = Convert.ToDecimal(dvpolh[0]["fupdlmt1"]);
                    dr["ftppd"] = Convert.ToDecimal(dvpolh[0]["fupdlmt2"]);
                }
                
                dr["fclsseq"] = UtilityFunc.ClsSeq(fclass, fsclass); ;
                dr["fclsname"] = UtilityFunc.ClsName(fclass, fsclass);
            }

            return dtinsint_e;
        }
        
        public static DataTable GenPolPremDet(DataTable dtpolh, DataTable dtmin, DataTable dtmprdr, DateTime vfbk_fr, DateTime vfbk_to)
        {
            DataTable dttmp = dtpolh.Copy();

            DataView dvmprdr = dtmprdr.DefaultView, dvmin = dtmin.DefaultView, dvtmp = dttmp.DefaultView;

            foreach (DataRow dr in dttmp.Rows)
            {
                string fclass, fsclass, fbus, fclnt, fprdr, fctlid, ftype, ftype2;

                fclass = dr["fclass"].ToString().Trim();
                fsclass = dr["fsclass"].ToString().Trim();

                fbus = dr["fbus"].ToString().Trim();
                fclnt = dr["fclnt"].ToString().Trim();
                fprdr = dr["fprdr"].ToString().Trim();

                fctlid = dr["fctlid"].ToString().Trim();

                dr["fclsseq"] = UtilityFunc.ClsSeq(fclass, fsclass); ;
                dr["fclsname"] = UtilityFunc.ClsName(fclass, fsclass);
               
                if ("D|K".Contains(fbus))
                {
                    ftype = "C";
                    ftype2 = "P";
                }
                else
                {
                    ftype = "R";
                    ftype2 = "B";
                }

                dvmprdr.RowFilter = string.Format("ftype = '{0}' and fid = '{1}'", ftype, fclnt);

                if ("D|K".Contains(fbus))
                {
                    dr["fcdesc"] = dvmprdr[0]["fdesc"];
                    dr["finter"] = dvmprdr[0]["finter"];
                }
                else
                {
                    dr["fcdesc"] = dr["finsd"];
                    dr["finter"] = "2";
                }

                dvmprdr.RowFilter = string.Format("ftype = '{0}' and fid = '{1}'", ftype2, fprdr);
                dr["fpdesc"] = dvmprdr[0]["fdesc"];

                dvmin.RowFilter = string.Format("fctlid_1 = '{0}'", fctlid);

                if (dvmin.Count != 0)
                {
                    dr["finstall"] = dvmin[0]["finstall"];
                    dr["fissdate"] = dvmin[0]["fbilldate"];
                }

                if (dr["fendtno"].ToString().Trim() == "")
                    dr["fpcnt"] = 1;
                else
                    dr["fecnt"] = 1;
            }

            dvtmp.RowFilter = string.Format("fissdate >= {0} and fissdate <= {1}",
                                        csCommon.DbDateFormat(vfbk_fr), csCommon.DbDateFormat(vfbk_to));

            return dvtmp.ToTable();
        }

        public static DataTable GenPolPremSm(DataTable dtdetail, DataTable dtminsc, DateTime vfbk_to)
        {
            DataTable dt = new DataTable();

            dt.Columns.Add("fclsseq", typeof(String));
            dt.Columns.Add("fclsname", typeof(String));

            DataColumn fseqnoCol = dt.Columns.Add("fseqno", typeof(Int32)),
                       mtcnt_gCol = dt.Columns.Add("mtcnt_g", typeof(Int32)),
                       mtgpm_gCol = dt.Columns.Add("mtgpm_g", typeof(Decimal)),
                       mtcnt_nCol = dt.Columns.Add("mtcnt_n", typeof(Int32)),
                       mtgpm_nCol = dt.Columns.Add("mtgpm_n", typeof(Decimal)),
                       mtcntCol = dt.Columns.Add("mtcnt", typeof(Int32)),
                       mtgpmCol = dt.Columns.Add("mtgpm", typeof(Decimal)),
                       ytcnt_gCol = dt.Columns.Add("ytcnt_g", typeof(Int32)),
                       ytgpm_gCol = dt.Columns.Add("ytgpm_g", typeof(Decimal)),
                       ytcnt_nCol = dt.Columns.Add("ytcnt_n", typeof(Int32)),
                       ytgpm_nCol = dt.Columns.Add("ytgpm_n", typeof(Decimal)),
                       ytcntCol = dt.Columns.Add("ytcnt", typeof(Int32)),
                       ytgpmCol = dt.Columns.Add("ytgpm", typeof(Decimal)),
                       mpcnt_gCol = dt.Columns.Add("mpcnt_g", typeof(Int32)),
                       mpgpm_gCol = dt.Columns.Add("mpgpm_g", typeof(Decimal)),
                       mpcnt_nCol = dt.Columns.Add("mpcnt_n", typeof(Int32)),
                       mpgpm_nCol = dt.Columns.Add("mpgpm_n", typeof(Decimal)),
                       mpcntCol = dt.Columns.Add("mpcnt", typeof(Int32)),
                       mpgpmCol = dt.Columns.Add("mpgpm", typeof(Decimal)),
                       ypcnt_gCol = dt.Columns.Add("ypcnt_g", typeof(Int32)),
                       ypgpm_gCol = dt.Columns.Add("ypgpm_g", typeof(Decimal)),
                       ypcnt_nCol = dt.Columns.Add("ypcnt_n", typeof(Int32)),
                       ypgpm_nCol = dt.Columns.Add("ypgpm_n", typeof(Decimal)),
                       ypcntCol = dt.Columns.Add("ypcnt", typeof(Int32)),
                       ypgpmCol = dt.Columns.Add("ypgpm", typeof(Decimal)),
                       mecnt_gCol = dt.Columns.Add("mecnt_g", typeof(Int32)),
                       megpm_gCol = dt.Columns.Add("megpm_g", typeof(Decimal)),
                       mecnt_nCol = dt.Columns.Add("mecnt_n", typeof(Int32)),
                       megpm_nCol = dt.Columns.Add("megpm_n", typeof(Decimal)),
                       mecntCol = dt.Columns.Add("mecnt", typeof(Int32)),
                       megpmCol = dt.Columns.Add("megpm", typeof(Decimal)),
                       yecnt_gCol = dt.Columns.Add("yecnt_g", typeof(Int32)),
                       yegpm_gCol = dt.Columns.Add("yegpm_g", typeof(Decimal)),
                       yecnt_nCol = dt.Columns.Add("yecnt_n", typeof(Int32)),
                       yegpm_nCol = dt.Columns.Add("yegpm_n", typeof(Decimal)),
                       yecntCol = dt.Columns.Add("yecnt", typeof(Int32)),
                       yegpmCol = dt.Columns.Add("yegpm", typeof(Decimal));

            fseqnoCol.DefaultValue = 0;
            mtcnt_gCol.DefaultValue = 0;
            mtgpm_gCol.DefaultValue = 0;
            mtcnt_nCol.DefaultValue = 0;
            mtgpm_nCol.DefaultValue = 0;
            mtcntCol.DefaultValue = 0;
            mtgpmCol.DefaultValue = 0;
            ytcnt_gCol.DefaultValue = 0;
            ytgpm_gCol.DefaultValue = 0;
            ytcnt_nCol.DefaultValue = 0;
            ytgpm_nCol.DefaultValue = 0;
            ytcntCol.DefaultValue = 0;
            ytgpmCol.DefaultValue = 0;
            mpcnt_gCol.DefaultValue = 0;
            mpgpm_gCol.DefaultValue = 0;
            mpcnt_nCol.DefaultValue = 0;
            mpgpm_nCol.DefaultValue = 0;
            mpcntCol.DefaultValue = 0;
            mpgpmCol.DefaultValue = 0;
            ypcnt_gCol.DefaultValue = 0;
            ypgpm_gCol.DefaultValue = 0;
            ypcnt_nCol.DefaultValue = 0;
            ypgpm_nCol.DefaultValue = 0;
            ypcntCol.DefaultValue = 0;
            ypgpmCol.DefaultValue = 0;
            mecnt_gCol.DefaultValue = 0;
            megpm_gCol.DefaultValue = 0;
            mecnt_nCol.DefaultValue = 0;
            megpm_nCol.DefaultValue = 0;
            mecntCol.DefaultValue = 0;
            megpmCol.DefaultValue = 0;
            yecnt_gCol.DefaultValue = 0;
            yegpm_gCol.DefaultValue = 0;
            yecnt_nCol.DefaultValue = 0;
            yegpm_nCol.DefaultValue = 0;
            yecntCol.DefaultValue = 0;
            yegpmCol.DefaultValue = 0;

            String cmonth = String.Format("{0:yyyyMM}", vfbk_to);
            DataView dv = dt.DefaultView;

            foreach (DataRow dr in dtdetail.Rows)
            {
                string fclsseq = dr["fclsseq"].ToString().Trim(), fclsname = dr["fclsname"].ToString().Trim(),
                        finter = dr["finter"].ToString().Trim();
                Int32 fpcnt = Convert.ToInt32(dr["fpcnt"]), fecnt = Convert.ToInt32(dr["fecnt"]);
                Decimal pgpm = Convert.ToDecimal(dr["pgpm"]);

                String fissmonth = String.Format("{0:yyyyMM}", Convert.ToDateTime(dr["fissdate"]));

                dv.RowFilter = String.Format("fclsseq = '{0}'", fclsseq);

                if (dv.Count == 0)
                {
                    DataRowView rowView = dv.AddNew();

                    rowView["fclsseq"] = fclsseq;
                    rowView["fclsname"] = fclsname;

                    rowView.EndEdit();
                }

                String mtcnt_s, mtgpm_s, ytcnt_s, ytgpm_s, mpcnt_s,mpgpm_s, ypcnt_s, ypgpm_s, mecnt_s, megpm_s,
                       yecnt_s, yegpm_s;

                if (finter == "1")
                {
                    mtcnt_s = "mtcnt_g";
                    mtgpm_s = "mtgpm_g";
                    ytcnt_s = "ytcnt_g";
                    ytgpm_s = "ytgpm_g";
                    mpcnt_s = "mpcnt_g";
                    mpgpm_s = "mpgpm_g";
                    ypcnt_s = "ypcnt_g";
                    ypgpm_s = "ypgpm_g";
                    mecnt_s = "mecnt_g";
                    megpm_s = "megpm_g";
                    yecnt_s = "yecnt_g";
                    yegpm_s = "yegpm_g";
                }
                else
                {
                    mtcnt_s = "mtcnt_n";
                    mtgpm_s = "mtgpm_n";
                    ytcnt_s = "ytcnt_n";
                    ytgpm_s = "ytgpm_n";
                    mpcnt_s = "mpcnt_n";
                    mpgpm_s = "mpgpm_n";
                    ypcnt_s = "ypcnt_n";
                    ypgpm_s = "ypgpm_n";
                    mecnt_s = "mecnt_n";
                    megpm_s = "megpm_n";
                    yecnt_s = "yecnt_n";
                    yegpm_s = "yegpm_n";
                }

                dv[0][ytcnt_s] = Convert.ToInt32(dv[0][ytcnt_s]) + fpcnt + fecnt;
                dv[0][ytgpm_s] = Convert.ToDecimal(dv[0][ytgpm_s]) + pgpm;

                if (fissmonth == cmonth)
                {
                    dv[0][mtcnt_s] = Convert.ToInt32(dv[0][mtcnt_s]) + fpcnt + fecnt;
                    dv[0][mtgpm_s] = Convert.ToDecimal(dv[0][mtgpm_s]) + pgpm;
                }

                if (fpcnt == 1)
                {
                    dv[0][ypcnt_s] = Convert.ToInt32(dv[0][ypcnt_s]) + fpcnt;
                    dv[0][ypgpm_s] = Convert.ToDecimal(dv[0][ypgpm_s]) + pgpm;

                    if (fissmonth == cmonth)
                    {
                        dv[0][mpcnt_s] = Convert.ToInt32(dv[0][mpcnt_s]) + fpcnt;
                        dv[0][mpgpm_s] = Convert.ToDecimal(dv[0][mpgpm_s]) + pgpm;
                    }
                }

                if (fecnt == 1)
                {
                    dv[0][yecnt_s] = Convert.ToInt32(dv[0][yecnt_s]) + fecnt;
                    dv[0][yegpm_s] = Convert.ToDecimal(dv[0][yegpm_s]) + pgpm;

                    if (fissmonth == cmonth)
                    {
                        dv[0][mecnt_s] = Convert.ToInt32(dv[0][mecnt_s]) + fecnt;
                        dv[0][megpm_s] = Convert.ToDecimal(dv[0][megpm_s]) + pgpm;
                    }
                }
            }

            dv.RowFilter = "";
            dv.Sort = "fclsseq";

            for (int i = 0; i < dv.Count; i++)
            {
                Decimal mtgpm_g = Convert.ToDecimal(dv[i]["mtgpm_g"]),mtgpm_n = Convert.ToDecimal(dv[i]["mtgpm_n"]),
                        ytgpm_g = Convert.ToDecimal(dv[i]["ytgpm_g"]),ytgpm_n = Convert.ToDecimal(dv[i]["ytgpm_n"]),
                        mpgpm_g = Convert.ToDecimal(dv[i]["mpgpm_g"]),mpgpm_n = Convert.ToDecimal(dv[i]["mpgpm_n"]),
                        ypgpm_g = Convert.ToDecimal(dv[i]["ypgpm_g"]),ypgpm_n = Convert.ToDecimal(dv[i]["ypgpm_n"]),
                        megpm_g = Convert.ToDecimal(dv[i]["megpm_g"]),megpm_n = Convert.ToDecimal(dv[i]["megpm_n"]),
                        yegpm_g = Convert.ToDecimal(dv[i]["yegpm_g"]),yegpm_n = Convert.ToDecimal(dv[i]["yegpm_n"]);

                dv[i]["fseqno"] = i+1;

                dv[i]["mtgpm_g"] = Decimal.Round(mtgpm_g / 10000, MidpointRounding.AwayFromZero);
                dv[i]["mtgpm_n"] = Decimal.Round(mtgpm_n / 10000, MidpointRounding.AwayFromZero);
                dv[i]["mtgpm"] = Decimal.Round(mtgpm_g / 10000, MidpointRounding.AwayFromZero) +
                                 Decimal.Round(mtgpm_n / 10000, MidpointRounding.AwayFromZero);
                dv[i]["mtcnt"] = Convert.ToInt32(dv[i]["mtcnt_g"]) + Convert.ToInt32(dv[i]["mtcnt_n"]);

                dv[i]["ytgpm_g"] = Decimal.Round(ytgpm_g / 10000, MidpointRounding.AwayFromZero);
                dv[i]["ytgpm_n"] = Decimal.Round(ytgpm_n / 10000, MidpointRounding.AwayFromZero);
                dv[i]["ytgpm"] = Decimal.Round(ytgpm_g / 10000, MidpointRounding.AwayFromZero) +
                                 Decimal.Round(ytgpm_n / 10000, MidpointRounding.AwayFromZero);
                dv[i]["ytcnt"] = Convert.ToInt32(dv[i]["ytcnt_g"]) + Convert.ToInt32(dv[i]["ytcnt_n"]);

                dv[i]["mpgpm_g"] = Decimal.Round(mpgpm_g / 10000, MidpointRounding.AwayFromZero);
                dv[i]["mpgpm_n"] = Decimal.Round(mpgpm_n / 10000, MidpointRounding.AwayFromZero);
                dv[i]["mpgpm"] = Decimal.Round(mpgpm_g / 10000, MidpointRounding.AwayFromZero) +
                                 Decimal.Round(mpgpm_n / 10000, MidpointRounding.AwayFromZero);
                dv[i]["mpcnt"] = Convert.ToInt32(dv[i]["mpcnt_g"]) + Convert.ToInt32(dv[i]["mpcnt_n"]);

                dv[i]["ypgpm_g"] = Decimal.Round(ypgpm_g / 10000, MidpointRounding.AwayFromZero);
                dv[i]["ypgpm_n"] = Decimal.Round(ypgpm_n / 10000, MidpointRounding.AwayFromZero);
                dv[i]["ypgpm"] = Decimal.Round(ypgpm_g / 10000, MidpointRounding.AwayFromZero) +
                                 Decimal.Round(ypgpm_n / 10000, MidpointRounding.AwayFromZero);
                dv[i]["ypcnt"] = Convert.ToInt32(dv[i]["ypcnt_g"]) + Convert.ToInt32(dv[i]["ypcnt_n"]);

                dv[i]["megpm_g"] = Decimal.Round(megpm_g / 10000, MidpointRounding.AwayFromZero);
                dv[i]["megpm_n"] = Decimal.Round(megpm_n / 10000, MidpointRounding.AwayFromZero);
                dv[i]["megpm"] = Decimal.Round(megpm_g / 10000, MidpointRounding.AwayFromZero) +
                                 Decimal.Round(megpm_n / 10000, MidpointRounding.AwayFromZero);
                dv[i]["mecnt"] = Convert.ToInt32(dv[i]["mecnt_g"]) + Convert.ToInt32(dv[i]["mecnt_n"]);

                dv[i]["yegpm_g"] = Decimal.Round(yegpm_g / 10000, MidpointRounding.AwayFromZero);
                dv[i]["yegpm_n"] = Decimal.Round(yegpm_n / 10000, MidpointRounding.AwayFromZero);
                dv[i]["yegpm"] = Decimal.Round(yegpm_g / 10000, MidpointRounding.AwayFromZero) +
                                 Decimal.Round(yegpm_n / 10000, MidpointRounding.AwayFromZero);
                dv[i]["yecnt"] = Convert.ToInt32(dv[i]["yecnt_g"]) + Convert.ToInt32(dv[i]["yecnt_n"]);
            }

            //foreach (DataRow dr in dtminsc.Rows)
            //{
            //    string fclsseq = dr["fclsseq"].ToString().Trim(), fclsname = dr["fclsname"].ToString().Trim(); 
            //    dv.RowFilter = String.Format("Trim(fclsseq) = '{0}'",fclsseq);

            //    if (dv.Count == 0)
            //    {
            //        DataRowView rowView = dv.AddNew();

            //        rowView["fclsseq"] = fclsseq;
            //        rowView["fclsname"] = fclsname;

            //        rowView.EndEdit();
            //    }

            //    dv.RowFilter = "";
            //}

            //for (int i = 0; i < dv.Count; i++)
            //{
            //    dv[i]["fseqno"] = i + 1;
            //}

            return dv.ToTable();
        }

        public static DataTable GenPrmAnly(DataTable dtinvh, DataTable dtinvh_b, DataTable dtmprdr, DataTable dtpolh, String vford, String vfsumm)
        {
            DataView dvinvh_b = dtinvh_b.DefaultView, dvpolh = dtpolh.DefaultView, dvmprdr = dtmprdr.DefaultView;


            foreach (DataRow dr in dtinvh.Rows)
            {
                string fctlid_1 = dr["fctlid_1"].ToString().Trim();
                decimal finstall = Convert.ToDecimal(dr["finstall"]);

                dr["fnew"] = finstall == 1 ? "Y" : "N";

                dvinvh_b.RowFilter = string.Format("fctlid_1 = '{0}'", fctlid_1);

                dr["agpm"] = dvinvh_b[0]["agpm"];
                dr["atdamt"] = dvinvh_b[0]["atdamt"];
                dr["acomamt"] = dvinvh_b[0]["acomamt"];
                dr["alvyamt1"] = dvinvh_b[0]["alvyamt1"];
                dr["alvyamt2"] = dvinvh_b[0]["alvyamt2"];
                dr["alvyamt3"] = dvinvh_b[0]["alvyamt3"];
                dr["anpm"] = dvinvh_b[0]["anpm"];

                dvpolh.RowFilter = string.Format("fctlid = '{0}'", fctlid_1);

                string fclass = dvpolh[0]["fclass"].ToString().Trim(), fsclass = dvpolh[0]["fsclass"].ToString().Trim(),
                    ftrade = dvpolh[0]["ftrade"].ToString().Trim(), fsite = dvpolh[0]["fsite"].ToString().Trim(),
                    fsite_t1 = dvpolh[0]["fsite_t1"].ToString().Trim(),fsdesc;

                string fprdr = dvpolh[0]["fprdr"].ToString().Trim(),fbus = dvpolh[0]["fbus"].ToString().Trim(),
                    fclnt = dvpolh[0]["fclnt"].ToString().Trim(),ftype;

                dr["fpolno"] = dvpolh[0]["fpolno"];
                dr["fendtno"] = dvpolh[0]["fendtno"];
                dr["fclass"] = dvpolh[0]["fclass"];
                dr["fsclass"] = dvpolh[0]["fsclass"];
                dr["fclsseq"] = UtilityFunc.ClsSeq(fclass, fsclass); ;
                dr["fclsname"] = UtilityFunc.ClsName(fclass, fsclass);
                dr["ftrade"] = dvpolh[0]["ftrade"];
                dr["ftrdesc"] = dvpolh[0]["ftrdesc"];
                dr["ftrdseq"] = UtilityFunc.ClsTrdSeq(fclass, fsclass, ftrade);
                dr["ftrdname"] = UtilityFunc.ClsTrdName(fclass, fsclass, ftrade);
                dr["fextend"] = dvpolh[0]["fextend"];
                dr["fissdate"] = dvpolh[0]["fissdate"];
                dr["fincfr"] = dvpolh[0]["fincfr"];
                dr["fincto"] = dvpolh[0]["fincto"];
                dr["fefffr"] = dvpolh[0]["fefffr"];
                dr["feffto"] = dvpolh[0]["feffto"];
                dr["finsd"] = dvpolh[0]["finsd"];
                dr["finsd_t1"] = dvpolh[0]["finsd_t1"];
                dr["finsd_t2"] = dvpolh[0]["finsd_t2"];
                dr["fcontrt"] = dvpolh[0]["fcontrt"];
                dr["fcontrt_t1"] = dvpolh[0]["fcontrt_t1"];
                dr["fsiteid"] = dvpolh[0]["fsiteid"];
                dr["fsite"] = dvpolh[0]["fsite"];
                dr["fsite_t1"] = dvpolh[0]["fsite_t1"];

                fsdesc = fsite;
                fsdesc = (fsdesc+" "+fsite_t1).Trim();

                dr["fsdesc"] = fsdesc;
                dr["fprdr"] = dvpolh[0]["fprdr"];

                ftype = "D|K".Contains(fbus) ? "P" : "B";
                dvmprdr.RowFilter = string.Format("ftype = '{0}' and fid = '{1}'",ftype,fprdr);
                dr["fpdesc"] = dvmprdr[0]["fdesc"];

                ftype = "D|K".Contains(fbus) ? "C" : "R";
                dvmprdr.RowFilter = string.Format("ftype = '{0}' and fid = '{1}'", ftype, fclnt);

                if ("D|K".Contains(fbus))
                {
                    String finter = dvmprdr[0]["finter"].ToString().Trim(),
                        fsubside = dvmprdr[0]["fsubside"].ToString().Trim();

                    dr["finter"] = finter;
                    dr["fintdesc"] = finter == "1" ? "Inter-Company" : "Non Inter-Company";
                    dr["fpcode"] = finter + fsubside + (finter == "1" ? fclnt : "");
                    dr["fsubside"] = fsubside;

                    if ("123".Contains(fsubside))
                    {
                        if (finter == "1")
                            dr["fsubsdesc"] = fsubside == "1" ? "*COLI" : fsubside == "2" ? "*COXX" : "*MISC";
                        else dr["fsubsdesc"] = "*Non Group";
                    }
                    dr["fcdesc"] = dvmprdr[0]["fdesc"];
                } else
                {
                    dr["fcdesc"] = dvpolh[0]["finsd"];
                }
                
                dr["fclnt"] = dvpolh[0]["fclnt"];
                dr["fsum"] = dvpolh[0]["fsum"];
                dr["fupdsum"] = dvpolh[0]["fupdsum"];
                dr["fsi"] = dvpolh[0]["fsi"];
                dr["fupdsi"] = dvpolh[0]["fupdsi"];
                dr["fliab"] = dvpolh[0]["fliab"];
                dr["fupdliab"] = dvpolh[0]["fupdliab"];
                dr["pgpm"] = dvpolh[0]["fgpm"];
                dr["ptdamt"] = dvpolh[0]["ftdamt"];
                dr["pcomamt"] = dvpolh[0]["fcomamt"];
                dr["plvyamt1"] = dvpolh[0]["flvyamt1"];
                dr["plvyamt2"] = dvpolh[0]["flvyamt2"];
                dr["plvyamt3"] = dvpolh[0]["flvyamt3"];
                dr["pnpm"] = dvpolh[0]["fnpm"];
                dr["plgpm"] = dvpolh[0]["flgpm"];
                dr["pltdamt"] = dvpolh[0]["fltdamt"];
                dr["plcomamt"] = dvpolh[0]["flcomamt"];
                dr["plnpm"] = dvpolh[0]["flnpm"];
                dr["fbus"] = dvpolh[0]["fbus"];

                dr["fpcnt"] = dvpolh[0]["fendtno"].ToString().Trim() == "" ? 1 : 0;
                dr["fecnt"] = dvpolh[0]["fendtno"].ToString().Trim() == "" ? 0 : 1;

                dr["stcode1"] = "1|2".Contains(vford) ?  dr["fclsseq"] : dr["fcdesc"].ToString().Substring(0,60)+ dr["fclnt"].ToString().Trim();
                dr["sthd1"] = "1|2".Contains(vford) ? "Class: "+dr["fclsname"].ToString().Trim() : "Client: "+
                                dr["fcdesc"].ToString().Substring(0,254).Trim();
                dr["stdesc1"] = "1|2".Contains(vford) ? dr["fclsname"] : dr["fcdesc"].ToString().Substring(0, 254).Trim();
                dr["stft1"] = "1|2".Contains(vford) ? "Class Total:" : "Client Total:";

                dr["stcode2"] = "1|3".Contains(vford) ? "" : vford == "4" ? dr["fclsseq"] :
                                dr["fcdesc"].ToString().Substring(0,60)+ dr["fclnt"].ToString().Trim();
                dr["sthd2"] = "1|3".Contains(vford) ? "" : vford == "4" ? "Class: " + dr["fclsname"].ToString().Trim() : "Client: " +
                                dr["fcdesc"].ToString().Substring(0, 254).Trim();
                dr["stdesc2"] = "1|3".Contains(vford) ? "" : vford == "4" ? dr["fclsname"] : dr["fcdesc"].ToString().Substring(0, 254).Trim();
                dr["stft2"] = "1|3".Contains(vford) ? "" : vford == "4" ? "Class Total:" : "Client Total:";

                dr["sumcode1"] = vfsumm == "2" ? "" : dr["fclsseq"];
                dr["sumdesc1"] = vfsumm == "2" ? "" : dr["fclsname"];
                dr["sumcode1b"] = vfsumm == "2" ? "" : dr["fclsseq"];
                dr["sumdesc1b"] = vfsumm == "2" ? "" : dr["fclsname"];

                dr["sumcode2"] = vfsumm == "2" ? "" : dr["fpcode"].ToString().Substring(0,1);
                dr["sumdesc2"] = vfsumm == "2" ? "" : dr["fpcode"].ToString().Substring(0,1) == "1" ? "Group": "Non Group";
                dr["sumcode2b"] = vfsumm == "2" ? "" : dr["fpcode"].ToString().Substring(0,2);
                dr["sumdesc2b"] = vfsumm == "2" ? "" : dr["fsubsdesc"];

                decimal pgpm = Convert.ToDecimal(dr["pgpm"]), fsi = Convert.ToDecimal(dr["fsi"]), 
                    fliab = Convert.ToDecimal(dr["fliab"]), mgpm = Convert.ToDecimal(dr["mgpm"]);

                dr["msi"] = pgpm == 0 ? fsi : Decimal.Round(fsi * mgpm / pgpm, 2, MidpointRounding.AwayFromZero);  // 20160518
                dr["mliab"] = pgpm == 0 ? fliab : Decimal.Round(fliab * mgpm / pgpm, 2, MidpointRounding.AwayFromZero); // 20160518
            }

            return dtinvh;
        }
        
        public static DataTable GenPrmExpl(DataTable dtoinvh, DataTable dtmprdr, DataTable dtpolh, DataTable dtmax)
        {
            DataTable dt = dtpolh.Copy();

            DataView dvmprdr = dtmprdr.DefaultView, dvoinvh = dtoinvh.DefaultView, dv = dt.DefaultView;

            foreach (DataRow dr in dt.Rows)
            {
                string fclass,fsclass,fbus,fclnt,fprdr,fctlid,ftype,ftype2;

                fclass = dr["fclass"].ToString().Trim();
                fsclass = dr["fsclass"].ToString().Trim();

                fbus = dr["fbus"].ToString().Trim();
                fclnt = dr["fclnt"].ToString().Trim();
                fprdr = dr["fprdr"].ToString().Trim();

                fctlid = dr["fctlid"].ToString().Trim();

                dr["fclsseq"] = UtilityFunc.ClsSeq(fclass, fsclass); ;
                dr["fclsname"] = UtilityFunc.ClsName(fclass, fsclass);

                if (String.IsNullOrEmpty(dr["fmntfr"].ToString()))
                    dr["prmnt"] = "N";
                else
                    dr["prmnt"] = "Y";

                if ("D|K".Contains(fbus))
                {
                    ftype = "C";
                    ftype2 = "P";
                }
                else
                {
                    ftype = "R";
                    ftype2 = "B";
                }

                dvmprdr.RowFilter = string.Format("ftype = '{0}' and fid = '{1}'", ftype, fclnt);
                dr["fcdesc"] = dvmprdr[0]["fdesc"];

                dvmprdr.RowFilter = string.Format("ftype = '{0}' and fid = '{1}'", ftype2, fprdr);
                dr["fpdesc"] = dvmprdr[0]["fdesc"];

                dvoinvh.RowFilter = string.Format("fctlid_1 = '{0}'", fctlid);

                if (dvoinvh.Count != 0)
                    dr["fremark"] = dvoinvh[0]["fremark"];
                else
                    dr["fremark"] = dr["fcontrt_t1"];
            }

                foreach (DataRow dr in dtmax.Rows)
                {
                    String fctlid_p = dr["fctlid_p"].ToString().Trim(), fctlid_v = dr["fctlid_v"].ToString().Trim(),
                           fctlid = dr["fctlid"].ToString().Trim();

                    dv.RowFilter = string.Format("fctlid_p = '{0}' and fctlid_v = '{1}' and fctlid < '{2}'",
                                        fctlid_p, fctlid_v, fctlid);

                    //while (dv.Count != 0)
                    //    dv[0].Delete();             these two lines have to be release and executed if one report line and not past historey per policy+Vo is desirable

                    dv.RowFilter = string.Format("fctlid_p = '{0}' and fctlid_v = '{1}' and fctlid >= '{2}'",
                            fctlid_p, fctlid_v, fctlid);

                    if (dv.Count > 1)
                    {
                        dv[0]["fnewendt"] = dv[1]["fendtno"];
                        dv[0]["fnextend"] = dv[1]["fextend"];
                    }

                    dv.RowFilter = string.Format("fctlid_p = '{0}' and fctlid_v = '{1}' and fctlid > '{2}'",
                        fctlid_p, fctlid_v, fctlid);

                    while (dv.Count != 0)
                        dv[0].Delete();
                }

            dv.RowFilter = "fbus <> 'K'";
            dv.Sort = "fclsseq,fpolno,fvseq,fendtno";

            if (dv.Count == 0)
            {
                DataRowView rowView = dv.AddNew();

                rowView["fbus"] = "?";
                rowView["fclass"] = "???";

                rowView.EndEdit();
            }

            return dv.ToTable();
        }

        public static DataTable GenPrmBord(DataTable dtoriinvd, DataTable dtoriinvd_s, DataTable dtpolh,
                                    DataTable dtorid1, DataTable dtmprdr, DataTable dtmtyinsr, String vttytype)
        {
            dtoriinvd.DefaultView.RowFilter = String.Format("fritype ='{0}'", vttytype);
            dtoriinvd.DefaultView.Sort = "fttycode,fdbtrtype,fdbtr,fttysec,fpolno,fendtno";

            DataTable dtresult = dtoriinvd.DefaultView.ToTable();

            DataView dvoriinvd_s = dtoriinvd_s.DefaultView, dvmprdr = dtmprdr.DefaultView,
                dvmtyinsr = dtmtyinsr.DefaultView, dvpolh = dtpolh.DefaultView, dvorid1 = dtorid1.DefaultView;

            foreach (DataRow dr in dtresult.Rows)
            {
                String fctlid_e = dr["fctlid_e"].ToString().Trim(), fctlid_i = dr["fctlid_i"].ToString().Trim(),
                    fctlid_1 = dr["fctlid_1"].ToString().Trim(),
                    fdbtrtype = dr["fdbtrtype"].ToString().Trim(), fdbtr = dr["fdbtr"].ToString().Trim(),
                    fritype = dr["fritype"].ToString().Trim(), fttycode = dr["fttycode"].ToString().Trim(),
                    fttysec = dr["fttysec"].ToString().Trim(), fgrnet = dr["fgrnet"].ToString().Trim(),
                    fkind = dr["fkind"].ToString().Trim();

                Decimal fgpm_b = Convert.ToDecimal(dr["fgpm_b"]), fnpm_b = Convert.ToDecimal(dr["fnpm_b"]);

                dvpolh.RowFilter = string.Format("fctlid = '{0}'",fctlid_e);

                if (dvpolh.Count != 0)
                {
                    dr["fpolno"] = dvpolh[0]["fpolno"];
                    dr["fendtno"] = dvpolh[0]["fendtno"];
                    dr["fsi"] = fkind == "1" && dvpolh[0]["fclass"].ToString().Trim() != "FGP" ? dvpolh[0]["fsi"] : 0;
                    dr["ftl"] = fkind == "1" && dvpolh[0]["fclass"].ToString().Trim() != "FGP" ? 0 : dvpolh[0]["fliab"];

                    dr["fefffr"] = dvpolh[0]["fefffr"];
                    dr["feffto"] = dvpolh[0]["feffto"];
                    dr["fmntfr"] = dvpolh[0]["fmntfr"];
                    dr["fmntto"] = dvpolh[0]["fmntto"];
                    dr["finsd"] = dvpolh[0]["finsd"];
                    dr["finsd_t1"] = dvpolh[0]["finsd_t1"];
                    dr["finsd_t2"] = dvpolh[0]["finsd_t2"];
                    dr["fcontrt"] = dvpolh[0]["fcontrt"];
                    dr["fcontrt_t1"] = dvpolh[0]["fcontrt_t1"];
                }

                dvmprdr.RowFilter = string.Format("ftype = '{0}' and fid = '{1}'",fdbtrtype,fdbtr);

                dr["fdbdesc"] = dvmprdr.Count.Equals(0) ? "" : dvmprdr[0]["fdesc"];

                dvoriinvd_s.RowFilter = string.Format(
                        "fctlid_i = '{0}' and fritype = '{1}' and fttycode = '{2}' and fttysec = '{3}'",
                         fctlid_i, vttytype, fttycode, fttysec);

                if (dvoriinvd_s.Count != 0)
                {
                     dr["fgpm"] = dvoriinvd_s[0]["fgpm"];
                     dr["fnpm"] = dvoriinvd_s[0]["fnpm"];
                     dr["fttygm"] = fgrnet.Equals("1") ? dvoriinvd_s[0]["fgpm"] : 0;
                     dr["fttyprem"] = fgrnet.Equals("1") ? 0 : dvoriinvd_s[0]["fnpm"];
                     dr["fttycom"] = dvoriinvd_s[0]["fttycom"];
                     dr["fttybrk"] = dvoriinvd_s[0]["fttybrk"];
                     dr["fpaybrk"] = Convert.ToDecimal(dr["fttygm"]) + Convert.ToDecimal(dr["fttyprem"]) -
                            Convert.ToDecimal(dr["fttycom"]);
                     dr["fttynett"] = Convert.ToDecimal(dr["fpaybrk"]) - Convert.ToDecimal(dr["fttybrk"]);
                }

                dvorid1.RowFilter = string.Format("fctlid_i = '{0}' and fctlid_2 = '{1}' and fritype = '{2}'",
                    fctlid_i, fctlid_1, vttytype);

                if (dvorid1.Count !=0)
                {
                    if (dvorid1[0]["fkind"].ToString().Trim() == "1" )
                        dr["fttysi"] = dvorid1[0]["fclass"].ToString().Trim().Equals("MYP") ? dvorid1[0]["fsi2"] : dvorid1[0]["fsi"];
                    else
                        dr["fttytl"] = dvorid1[0]["fclass"].ToString().Trim().Equals("MYP") ? dvorid1[0]["fsi2"] : dvorid1[0]["fsi"];
                }

                dvmtyinsr.RowFilter = string.Format("fid ='{0}' and fdbtrtype ='{1}' and fdbtr ='{2}'",
                        fttycode, fdbtrtype, fdbtr);

                if (dvmtyinsr.Count != 0)
                    dr["fshare"] = dvmtyinsr[0]["fshare"];

                dr["fttygm_b"] = fgrnet.Equals("1") ? dr["fgpm_b"] : 0;
                dr["fttyprem_b"] = fgrnet.Equals("1") ? 0 : dr["fnpm_b"];
                dr["fpaybrk_b"] = Convert.ToDecimal(dr["fttygm_b"]) + Convert.ToDecimal(dr["fttyprem_b"]) -
                    Convert.ToDecimal(dr["fttycom_b"]);
                dr["fttynett_b"] = Convert.ToDecimal(dr["fpaybrk_b"]) - Convert.ToDecimal(dr["fttybrk_b"]);
                dr["pcode"] = fttycode.PadRight(10)+fdbtrtype+fdbtr;
            }

            //foreach (DataRow dr in dtmtyinsr.Rows)
            //{
            //    String fttycode = dr["fid"].ToString().Trim(), fdbtrtype = dr["fdbtrtype"].ToString().Trim(),
            //        fdbtr = dr["fdbtr"].ToString().Trim(), fdbdesc, lc_prntotal;

            //    Decimal fshare = Convert.ToDecimal(dr["fshare"]);

            //    dvmprdr.RowFilter = string.Format("ftype = '{0}' and fid = '{1}'", fdbtrtype, fdbtr);
            //    fdbdesc = dvmprdr.Count.Equals(0) ? "" : dvmprdr[0]["fdesc"].ToString().Trim();

            //    if (vttytype == "02")
            //    {
            //        dtresult.DefaultView.RowFilter = string.Format("fttycode = '{0}' and fdbtrtype ='{1}' and " +
            //            "fdbtr = '{2}' and fttysec in ('01','02','03')", fttycode, fdbtrtype, fdbtr);

            //        lc_prntotal = dtresult.DefaultView.Count.Equals(0) ? "N" : "Y";

            //        String[] ttysec_arr = { "01", "02", "03" };

            //        foreach (String ttysec in ttysec_arr)
            //        {
            //            dtresult.DefaultView.RowFilter = string.Format("fttycode = '{0}' and fdbtrtype ='{1}' and " +
            //                "fdbtr = '{2}' and fttysec = '{3}'", fttycode, fdbtrtype, fdbtr, ttysec);

            //            if (dtresult.DefaultView.Count == 0)
            //            {
            //                DataRowView rowView = dtresult.DefaultView.AddNew();
            //                rowView["fttycode"] = fttycode;
            //                rowView["fttysec"] = ttysec;
            //                rowView["fdbtrtype"] = fdbtrtype;
            //                rowView["fdbtr"] = fdbtr;
            //                rowView["fshare"] = fshare;

            //                rowView["ftoprn"] = "N";
            //                rowView["fdbdesc"] = fdbdesc;
            //                rowView["fprntotal"] = lc_prntotal;
            //                rowView["pcode"] = fttycode.PadRight(10) + fdbtrtype + fdbtr;

            //                rowView.EndEdit();
            //            }
            //        }
            //    }
            //}

            if(vttytype == "05")
            {
                DataTable dtfob;
                dtfob = dtresult.Copy();
                dtfob.Clear();

                DataView dvfob = dtfob.DefaultView;

                foreach (DataRow dr in dtresult.Rows)
                {
                    String fctlid_i = dr["fctlid_i"].ToString().Trim(), pcode = dr["pcode"].ToString().Trim();

                    dvfob.RowFilter = String.Format("fctlid_i='{0}' and pcode ='{1}'", fctlid_i, pcode);
                    if (dvfob.Count == 0)
                    {
                        DataRow newRow = dtfob.NewRow();
                        newRow.ItemArray = dr.ItemArray;

                        newRow["fttysec"] = "01";
                        newRow["fttyprem"] =  Convert.ToDecimal(newRow["fttyprem"]) + 
                                               Convert.ToDecimal(newRow["fttygm"]);
                        newRow["fttyprem_b"] = Convert.ToDecimal(newRow["fttyprem_b"]) +
                                               Convert.ToDecimal(newRow["fttygm_b"]);
                        dtfob.Rows.Add(newRow);
                    } else
                    {
                        dvfob[0]["fsi"] = Convert.ToDecimal(dvfob[0]["fsi"]) + Convert.ToDecimal(dr["fsi"]);
                        dvfob[0]["ftl"] = Convert.ToDecimal(dvfob[0]["ftl"]) + Convert.ToDecimal(dr["ftl"]);
                        dvfob[0]["fttysi"] = Convert.ToDecimal(dvfob[0]["fttysi"]) + Convert.ToDecimal(dr["fttysi"]);
                        dvfob[0]["fttytl"] = Convert.ToDecimal(dvfob[0]["fttytl"]) + Convert.ToDecimal(dr["fttytl"]);

                        dvfob[0]["fgpm"] = Convert.ToDecimal(dvfob[0]["fgpm"]) + Convert.ToDecimal(dr["fgpm"]);
                        dvfob[0]["fnpm"] = Convert.ToDecimal(dvfob[0]["fnpm"]) + Convert.ToDecimal(dr["fnpm"]);
                        dvfob[0]["fttygm"] = Convert.ToDecimal(dvfob[0]["fttygm"]) + Convert.ToDecimal(dr["fttygm"]);
                        dvfob[0]["fttyprem"] = Convert.ToDecimal(dvfob[0]["fttyprem"]) + Convert.ToDecimal(dr["fttyprem"])
                            + Convert.ToDecimal(dr["fttygm"]);
                        dvfob[0]["fttycom"] = Convert.ToDecimal(dvfob[0]["fttycom"]) + Convert.ToDecimal(dr["fttycom"]);
                        dvfob[0]["fttybrk"] = Convert.ToDecimal(dvfob[0]["fttybrk"]) + Convert.ToDecimal(dr["fttybrk"]);
                        dvfob[0]["fpaybrk"] = Convert.ToDecimal(dvfob[0]["fpaybrk"]) + Convert.ToDecimal(dr["fpaybrk"]);
                        dvfob[0]["fttynett"] = Convert.ToDecimal(dvfob[0]["fttynett"]) + Convert.ToDecimal(dr["fttynett"]);

                        dvfob[0]["fgpm_b"] = Convert.ToDecimal(dvfob[0]["fgpm_b"]) + Convert.ToDecimal(dr["fgpm_b"]);
                        dvfob[0]["fnpm_b"] = Convert.ToDecimal(dvfob[0]["fnpm_b"]) + Convert.ToDecimal(dr["fnpm_b"]);
                        dvfob[0]["fttygm_b"] = Convert.ToDecimal(dvfob[0]["fttygm_b"]) + Convert.ToDecimal(dr["fttygm_b"]);
                        dvfob[0]["fttyprem_b"] = Convert.ToDecimal(dvfob[0]["fttyprem_b"]) + Convert.ToDecimal(dr["fttyprem_b"])
                            + Convert.ToDecimal(dr["fttygm_b"]);
                        dvfob[0]["fttycom_b"] = Convert.ToDecimal(dvfob[0]["fttycom_b"]) + Convert.ToDecimal(dr["fttycom_b"]);
                        dvfob[0]["fttybrk_b"] = Convert.ToDecimal(dvfob[0]["fttybrk_b"]) + Convert.ToDecimal(dr["fttybrk_b"]);
                        dvfob[0]["fpaybrk_b"] = Convert.ToDecimal(dvfob[0]["fpaybrk_b"]) + Convert.ToDecimal(dr["fpaybrk_b"]);
                        dvfob[0]["fttynett_b"] = Convert.ToDecimal(dvfob[0]["fttynett_b"]) + Convert.ToDecimal(dr["fttynett_b"]);
                    }
                }

                dvfob.RowFilter = "";
                dtresult = dtfob.Copy();
            }

            foreach (DataRow dr in dtmtyinsr.Rows)
            {
                String fttycode = dr["fid"].ToString().Trim(), fdbtrtype = dr["fdbtrtype"].ToString().Trim(),
                    fdbtr = dr["fdbtr"].ToString().Trim(), fdbdesc, lc_prntotal;

                Decimal fshare = Convert.ToDecimal(dr["fshare"]);

                dvmprdr.RowFilter = string.Format("ftype = '{0}' and fid = '{1}'", fdbtrtype, fdbtr);
                fdbdesc = dvmprdr.Count.Equals(0) ? "" : dvmprdr[0]["fdesc"].ToString().Trim();

                if ("02|05".Contains(vttytype))
                {
                    dtresult.DefaultView.RowFilter = string.Format("fttycode = '{0}' and fdbtrtype ='{1}' and " +
                        "fdbtr = '{2}' and fttysec in ('01','02','03')", fttycode, fdbtrtype, fdbtr);

                    lc_prntotal = dtresult.DefaultView.Count.Equals(0) ? "N" : "Y";

                    String ttysec_str;
                    ttysec_str = vttytype == "02" ? "010203" : "01";

                    Int32 noofTTYSec;

                    noofTTYSec= vttytype == "02" ? 3 : 1;

                    for (Int32 p = 0; p < noofTTYSec; p++)
                    {
                        String ttysec = ttysec_str.Substring(p*2, 2);
                        dtresult.DefaultView.RowFilter = string.Format("fttycode = '{0}' and fdbtrtype ='{1}' and " +
                            "fdbtr = '{2}' and fttysec = '{3}'", fttycode, fdbtrtype, fdbtr, ttysec);

                        if (dtresult.DefaultView.Count == 0)
                        {
                            DataRowView rowView = dtresult.DefaultView.AddNew();
                            rowView["fttycode"] = fttycode;
                            rowView["fttysec"] = ttysec;
                            rowView["fdbtrtype"] = fdbtrtype;
                            rowView["fdbtr"] = fdbtr;
                            rowView["fshare"] = fshare;

                            rowView["ftoprn"] = "N";
                            rowView["fdbdesc"] = fdbdesc;
                            rowView["fprntotal"] = lc_prntotal;
                            rowView["pcode"] = fttycode.PadRight(10) + fdbtrtype + fdbtr;

                            rowView.EndEdit();
                        }
                    }
                }
            }

            dtresult.DefaultView.RowFilter = "";
            dtresult.DefaultView.Sort = "pcode,fpolno,fendtno";
            return dtresult.DefaultView.ToTable();
        }

        public static DataTable GenPrmTty(DataTable dtoriinvd,DataTable dtmprdr,DataTable dtmtty)
        {
            DataView dv = dtmprdr.DefaultView, dvmtty = dtmtty.DefaultView;

            foreach (DataRow dr in dtoriinvd.Rows)
            {
                String fclass = dr["fclass"].ToString().Trim(), fsclass = dr["fsclass"].ToString().Trim(),
                    fkind = dr["fkind"].ToString().Trim(),
                    fdbtrtype = dr["fdbtrtype"].ToString().Trim(), fdbtr = dr["fdbtr"].ToString().Trim(),
                    fritype = dr["fritype"].ToString().Trim(), fttycode = dr["fttycode"].ToString().Trim(),
                    fgrnet = dr["fgrnet"].ToString().Trim();
                   
                Decimal fgpm = Convert.ToDecimal(dr["fgpm"]),fnpm = Convert.ToDecimal(dr["fnpm"]), fttypm = 0;

                fsclass = !fclass.Equals("CAR") ? fsclass : fkind.Equals("1") ? "SEC1" : "SEC2";
                fttypm = fgrnet.Equals("1") ? fgpm : fnpm;

                dv.RowFilter = string.Format("ftype = '{0}' and fid = '{1}'", fdbtrtype, fdbtr);

                if (dv.Count != 0)
                    dr["fdbdesc"] = dv[0]["fdesc"];

                dvmtty.RowFilter = string.Format("fid = '{0}'", fttycode);

                if (dvmtty.Count != 0)
                    dr["fttydesc"] = dvmtty[0]["fdesc"];

                dr["fsclass"] = fsclass;
                dr["fclsseq"] = UtilityFunc.ClsSeq(fclass, fsclass);
                dr["fclsname"] = UtilityFunc.ClsName(fclass, fsclass);
                dr["acc_class"] = UtilityFunc.AcCode(fclass, fsclass, fritype);
                dr["acc_desc"] = UtilityFunc.AccDescB(fclass, fsclass, fritype);
                dr["pcode"] = fclass.PadRight(10)+UtilityFunc.AcCode(fclass, fsclass, fritype);

                String acc_class = dr["acc_class"].ToString().Trim();

                dr["sec_desc"] = UtilityFunc.SecName(fclass, acc_class);

                dr["fttypm"] = fttypm;
            }

            if (dtoriinvd.DefaultView.Count == 0)
            {
                DataRow dr = dtoriinvd.NewRow();
                dr["fclass"] = "???";
                dtoriinvd.Rows.Add(dr);
            }

            dtoriinvd.DefaultView.Sort = "fttycode,fdbtr,fclsseq,fsec,fpolno,fendtno,finstall";
            return dtoriinvd.DefaultView.ToTable(false,
                "fpolno", "fendtno", "fclass", "fsclass","fritype","fttycode","fsec","fttydesc",
                "fdbtr","fdbdesc","fgpm","fttypm","fcomamt","fpayable",
                "acc_class","acc_desc","pcode","fclsseq","fclsname","sec_desc");
        }

        public static DataTable GenPrmExpn(DataTable dtpolh, DataTable dtmprdr, DateTime vfbk_fr, DateTime vfbk_to,
            String p_type)
        {
            DataView dv = dtmprdr.DefaultView;

            foreach (DataRow dr in dtpolh.Rows)
            {
                String fclass = dr["fclass"].ToString().Trim(), fsclass = dr["fsclass"].ToString().Trim(),
                    fprdr = dr["fprdr"].ToString().Trim(), fclnt = dr["fclnt"].ToString().Trim(),
                    fbus = dr["fbus"].ToString().Trim(), fpolno = dr["fpolno"].ToString().Trim(),
                    fendtno = dr["fendtno"].ToString().Trim();

                DateTime fincto = Convert.ToDateTime(dr["fincto"]), feffto = fincto = Convert.ToDateTime(dr["fincto"]);
                DateTime? fmntto;

                 if (String.IsNullOrEmpty(dr["fmntto"].ToString()))
                    fmntto = null;
                else
                    fmntto = Convert.ToDateTime(dr["fmntto"]);

                dr["fclsseq"] = UtilityFunc.ClsSeq(fclass, fsclass);
                dr["fclsname"] = UtilityFunc.ClsName(fclass, fsclass);

                if (fincto.CompareTo(vfbk_fr) >= 0 && fincto.CompareTo(vfbk_to) <= 0)
                    dr["fexpire"] = fincto;
                else if (feffto.CompareTo(vfbk_fr) >= 0 && feffto.CompareTo(vfbk_to) <= 0)
                    dr["fexpire"] = feffto;
                else
                    dr["fexpire"] = dr["fmntto"];

                if (String.IsNullOrEmpty(dr["fmntfr"].ToString()))
                    dr["prmnt"] = "N";
                else
                    dr["prmnt"] = "Y";

                dv.RowFilter = string.Format("ftype = '{0}' and fid = '{1}'", fbus == "D" ? "P" : "B", fprdr);
                if (dv.Count != 0)
                {
                    dr["fpdesc"] = dv[0]["fdesc"];
                    dr["fpadd1"] = dv[0]["fadd1"];
                    dr["fpadd2"] = dv[0]["fadd2"];
                    dr["fpadd3"] = dv[0]["fadd3"];
                    dr["fpadd4"] = dv[0]["fadd4"];
                }

                dv.RowFilter = string.Format("ftype = '{0}' and fid = '{1}'", fbus == "D" ? "C" : "R", fclnt);
                if (dv.Count != 0)
                {
                    dr["fcdesc"] = dv[0]["fdesc"];
                    dr["fcadd1"] = dv[0]["fadd1"];
                    dr["fcadd2"] = dv[0]["fadd2"];
                    dr["fcadd3"] = dv[0]["fadd3"];
                    dr["fcadd4"] = dv[0]["fadd4"];
                }
            }

            return dtpolh;
        }
       
        public static DataTable GenPrmFacOut(DataTable dtoriinvh, DataTable dtmprdr)
        {
            DataView dv = dtmprdr.DefaultView;

            foreach (DataRow dr in dtoriinvh.Rows)
            {
                String fclass = dr["fclass"].ToString().Trim(), fsclass = dr["fsclass"].ToString().Trim(),
                    fdbtrtype = dr["fdbtrtype"].ToString().Trim(), fdbtr = dr["fdbtr"].ToString().Trim();

                dv.RowFilter = string.Format("ftype = '{0}' and fid = '{1}'", fdbtrtype, fdbtr);

                if (dv.Count != 0)
                    dr["fdbdesc"] = dv[0]["fdesc"];

                dr["fclsseq"] = UtilityFunc.ClsSeq(fclass, fsclass);
                dr["fclsname"] = UtilityFunc.ClsName(fclass, fsclass);
            }

            if (dtoriinvh.DefaultView.Count == 0)
            {
                DataRow dr = dtoriinvh.NewRow();
                dr["fclass"] = "???";
                dtoriinvh.Rows.Add(dr);
            }

            dtoriinvh.DefaultView.Sort = "fdbtr,fclsseq,fpolno,fendtno";
            return dtoriinvh.DefaultView.ToTable(false,
                "fpolno", "fendtno", "fclass", "fsclass", "finstall", "ftotinstal", "finvdate", "fbilldate", "fbkdate",
                "finvno", "fdbtrtype", "fdbtr", "fdbdesc", "fclnt", "fsclnt", "fpmcur", "fgpm", "fcomamt", "fmiscamt1",
                "fmiscamt2", "fmiscamt3", "fmiscamt", "fpayable", "fdoctype", "fclsseq", "fclsname");
        }

        public static DataTable GenPrmReg(DataTable dtoinvh, DataTable dtmprdr, DataTable dtpolh,
            DataTable dtmlvydet, String rpOptid, String baseDrCr)
        {
            DataTable dt = new DataTable();
            dt = dtoinvh;
            dtmprdr.DefaultView.Sort = "fid,ftype";
            dtpolh.DefaultView.Sort = "fctlid";

            DataView dvmprdr = dtmprdr.DefaultView, dvpolh = dtpolh.DefaultView, 
                dvmlvydet = dtmlvydet.DefaultView;

//            dvmlvydet.RowFilter = string.Format("flvytype = '{0}'", "IAL");

            foreach (DataRow dr in dt.Rows)
            {
                string fclass, fsclass, fdbtrtype, fdbtr, fbus, fclnt, ftype, fctlid, fsubside;

                fclass = dr["fclass"].ToString().Trim();
                fsclass = dr["fsclass"].ToString().Trim();

                fdbtrtype = dr["fdbtrtype"].ToString().Trim();
                fdbtr = dr["fdbtr"].ToString().Trim();

                fbus = dr["fbus"].ToString().Trim();
                fclnt = dr["fclnt"].ToString().Trim();

                fctlid = dr["fctlid"].ToString().Trim();

                dr["fclsseq"] = UtilityFunc.ClsSeq(fclass, fsclass); ;
                dr["fclsname"] = UtilityFunc.ClsName(fclass, fsclass);

                dr["fdesc"] = "";

                dvmprdr.RowFilter = string.Format("fid = '{0}'", fdbtr);
                for (int i = 0; i < dvmprdr.Count; i++)
                {
                    if (dvmprdr[i]["ftype"].ToString().Trim() == fdbtrtype)
                    {
                        dr["fdesc"] = dvmprdr[i]["fdesc"].ToString().Trim();
                        break;
                    }

                }

                dvmprdr.RowFilter = string.Format("fid = '{0}'", fclnt);

                if ("DK".Contains(fbus))
                    ftype = "C";
                else ftype = "R";

                for (int i = 0; i < dvmprdr.Count; i++)
                {
                    if (dvmprdr[i]["ftype"].ToString().Trim() == ftype)
                    {
                        dr["finter"] = dvmprdr[i]["finter"].ToString().Trim();

                        if (dvmprdr[i]["finter"].ToString().Trim() == "1")
                        {
                            dr["fintdesc"] = "Inter-Company";
                            dr["fpcode"] = dvmprdr[i]["finter"].ToString() + dvmprdr[i]["fsubside"].ToString() + dr["fclnt"];
                        }
                        else
                        {
                            dr["fintdesc"] = "Non Inter-Company";
                            dr["fpcode"] = dvmprdr[i]["finter"].ToString() + dvmprdr[i]["fsubside"].ToString();
                        }

                        fsubside = dvmprdr[i]["fsubside"].ToString().Trim();
                        dr["fsubside"] = fsubside;

                        if (fsubside != "" && "123".Contains(fsubside))
                            dr["fsubsdesc"] = UtilityFunc.G_Subsiarr[Convert.ToInt32(fsubside), 0];
                        else
                            dr["fsubsdesc"] = "";

                        dr["fcdesc"] = dvmprdr[i]["fdesc"];

                        break;
                    }
                }

                if (dr["fclnt"].ToString().Trim() == UtilityFunc.G_Misclnt)
                {
                    dvpolh.RowFilter = string.Format("fctlid = '{0}'", fctlid);
                    if (dvpolh.Count != 0)
                        dr["fcdesc"] = dvpolh[0]["finsd"];
                }

                if (baseDrCr == "2")
                {
                    dvpolh.RowFilter = string.Format("fctlid = '{0}'", fctlid);
                    if (dvpolh.Count != 0)
                    {
                        DateTime fincfr = Convert.ToDateTime(dvpolh[0]["fincfr"]),
                            fefffr = Convert.ToDateTime(dr["fefffr"]),
                            fendtdate = Convert.ToDateTime(dvpolh[0]["fendtdate"]);

                        String tran_type = dvpolh[0]["ftype"].ToString().Trim(),
                            fextend = dvpolh[0]["fextend"].ToString().Trim();

                        dr["fgpm"] = dvpolh[0]["fgpm"];
                        dr["ftdamt"] = dvpolh[0]["ftdamt"];
                        dr["fricost"] = dvpolh[0]["fricost"];
                        dr["fpayable"] = dvpolh[0]["fpayable"];

                        dr["fcomamt"] = dvpolh[0]["fcomamt"];
                        dr["fnpm"] = dvpolh[0]["fnpm"];
                        dr["flvyamt1"] = dvpolh[0]["flvyamt1"];
                        dr["flvyamt2"] = dvpolh[0]["flvyamt2"];
                        dr["flvyamt3"] = dvpolh[0]["flvyamt3"];
                        dr["flvyamta"] = dvpolh[0]["flvyamta"];
                        dr["famount"] = dvpolh[0]["famount"];

                        dr["fextend"] = dvpolh[0]["fextend"];
                        dr["ftype"] = dvpolh[0]["ftype"];
                        dr["fautolvy"] = dvpolh[0]["fautolvy"];
                        dr["fautoedate"] = dvpolh[0]["fautoedate"];
                        dr["fendtdate"] = fendtdate;
                        dr["flvya"] = dvpolh[0]["flvya"];
                        dr["fincfr"] = fincfr;

                        DateTime fanvdate = Get_Annivery(fincfr, tran_type == "P" || "13".Contains(fextend)
                            ? fefffr : fendtdate);
                        Decimal fsysrate = Get_LevyRate(dvmlvydet, "IAL", fclass,fanvdate);
                        Decimal fsyslmt = Get_LevyLimit(dvmlvydet, "IAL", fclass,fanvdate);

                        Decimal fgpm = Convert.ToDecimal(dvpolh[0]["fgpm"]),
                            flvya = Convert.ToDecimal(dvpolh[0]["flvya"]),
                            flvyamta = Convert.ToDecimal(dvpolh[0]["flvyamta"]);

                        Decimal fsysamta = Decimal.Round(fgpm * fsysrate / 100, 2, MidpointRounding.AwayFromZero);

                        if (fsysamta >= fsyslmt)
                            fsysamta = fsyslmt;

                        if (fsysamta <= -fsyslmt)
                            fsysamta = -fsyslmt;

                        Decimal flvydiffa = flvyamta - fsysamta;

                        dr["fanvdate"] = fanvdate;
                        dr["fsysrate"] = fsysrate;
                        dr["fsyslmt"] = fsyslmt;
                        dr["fsysamta"] = fsysamta;
                        dr["flvydiffa"] = flvydiffa;

                        String plvya;

                        if (flvya == 0.0000m)
                            plvya = "Rate: Nil".PadRight(15);
                        else
                            plvya = ("Rate: " + flvya.ToString("0.0000")+"%").PadRight(15);

                        dr["plvya"] = plvya;
                    }
                    else
                    {
                        dr["fgpm"] = 0.00;
                        dr["ftdamt"] = 0.00;
                        dr["fricost"] = 0.00;
                        dr["fpayable"] = 0.00;

                        dr["fcomamt"] = 0.00;
                        dr["fnpm"] = 0.00;
                        dr["flvyamt1"] = 0.00;
                        dr["flvyamt2"] = 0.00;
                        dr["flvyamt3"] = 0.00;
                        dr["flvyamta"] = 0.00;
                        dr["famount"] = 0.00;
                    }
                }

                continue;
            }

            return dt;
        }

        public static DataTable GenPrmStat(DataTable dtminsc, DataTable dtgrand, DateTime vfbk_to, String prmtype)
        {
            DataTable dt = dtminsc.DefaultView.ToTable();
            DataView dv = dt.DefaultView;

            String prm_var = prmtype.Equals("1") ? "fgpm_di" : "fnetpm_i";
            Int32 refyear = vfbk_to.Year;

            dv.RowFilter = String.Format("trim(fclass) = '{0}'", "CAR");

            if (dv.Count != 0)
                dv[0]["fsclass"] = "SEC1";

            DataRowView rowView = dv.AddNew();

            rowView["fclass"] = "CAR";
            rowView["fsclass"] = "SEC2";
            rowView["famt1"] = 0; rowView["famt2"] = 0; rowView["famt3"] = 0; rowView["famt4"] = 0;
            rowView["famt5"] = 0; rowView["famt6"] = 0; rowView["famt7"] = 0; rowView["famt8"] = 0;
            rowView["famt8b"] = 0; rowView["famt8c"] = 0;
            rowView.EndEdit();

            rowView = dv.AddNew();
            rowView["fclass"] = "EEC";
            rowView["fsclass"] = "CONTRACTOR";
            rowView["famt1"] = 0; rowView["famt2"] = 0; rowView["famt3"] = 0; rowView["famt4"] = 0;
            rowView["famt5"] = 0; rowView["famt6"] = 0; rowView["famt7"] = 0; rowView["famt8"] = 0;
            rowView["famt8b"] = 0; rowView["famt8c"] = 0;
            rowView.EndEdit();

            rowView = dv.AddNew();
            rowView["fclass"] = "EEC";
            rowView["fsclass"] = "HOTEL";
            rowView["famt1"] = 0; rowView["famt2"] = 0; rowView["famt3"] = 0; rowView["famt4"] = 0;
            rowView["famt5"] = 0; rowView["famt6"] = 0; rowView["famt7"] = 0; rowView["famt8"] = 0;
            rowView["famt8b"] = 0; rowView["famt8c"] = 0;
            rowView.EndEdit();

            dv.RowFilter = "";

            for (Int32 i = 0; i < dv.Count; i++)
            {
                String fclass = dv[i]["fclass"].ToString().Trim(), fsclass = dv[i]["fsclass"].ToString().Trim();

                dv[i]["fclsseq"] = UtilityFunc.ClsSeq(fclass, fsclass);
                dv[i]["fclsname"] = UtilityFunc.ClsName(fclass, fsclass);
            }

            foreach (DataRow dr in dtgrand.Rows)
            {
                if (dr["fclass"].ToString().Trim() == "???")
                    continue;
                
                Int32 fuwyr = int.Parse(dr["fuwyr"].ToString());
                Int32 offset = refyear - fuwyr;

                String fclsseq = dr["fclsseq"].ToString().Trim();
                Decimal famt = Convert.ToDecimal(dr[prm_var]);

                if (offset <= 0)
                    offset = 0;

                if (offset >= 7)
                    offset = 7;

                offset = 8 - offset;

                dv.RowFilter = String.Format("fclsseq = '{0}'", fclsseq);
                String tarvar = "famt"+offset.ToString().Trim();

                dv[0][tarvar] = Convert.ToDecimal(dv[0][tarvar]) + famt;

                { // for output to excel, split the last column (with offset = 8) into two
                    offset = refyear - fuwyr;
                    String fieldStr = "";

                    if (offset <= 0 )
                        fieldStr = offset == 0 ? "famt8b" : "famt8c";
                    else
                        continue;

                    dv[0][fieldStr] = Convert.ToDecimal(dv[0][fieldStr]) + famt;
                }
            }

            dv.RowFilter = "";
            dv.Sort = "fclsseq";
            return dv.ToTable(false,
                "fclsseq", "fclsname", "famt1", "famt2", "famt3", "famt4", "famt5", "famt6", "famt7", "famt8", "famt8b", "famt8c");
        }

        public static DataTable GenUnInvPolh(DataTable dtpolh, DataTable dtorih)
        {
            DataTable dt = new DataTable();

            dt.Columns.Add("fctlid", typeof(String));
            dt.Columns.Add("fpolno", typeof(String));
            dt.Columns.Add("fendtno", typeof(String));
            dt.Columns.Add("fsum", typeof(Decimal));
            dt.Columns.Add("fefffr", typeof(DateTime));
            dt.Columns.Add("feffto", typeof(DateTime));
            dt.Columns.Add("fclass", typeof(String));
            dt.Columns.Add("fclsseq", typeof(String));
            dt.Columns.Add("fclsname", typeof(String));
            dt.Columns.Add("fsec", typeof(String));
            dt.Columns.Add("finsd", typeof(String));
            dt.Columns.Add("fxol", typeof(String));
            dt.Columns.Add("pgpm1", typeof(Decimal));
            dt.Columns.Add("pgpm2", typeof(Decimal));
            dt.Columns.Add("pcomamt1", typeof(Decimal));
            dt.Columns.Add("pcomamt2", typeof(Decimal));

            DataView dvorih = dtorih.DefaultView;
            dvorih.Sort = "fctlid DESC";

            foreach (DataRow dr in dtpolh.Rows)
            {
                String fclass, fsclass;
                DataRow dtrow = dt.NewRow();

                fclass = dr["fclass"].ToString().Trim();
                fsclass = dr["fsclass"].ToString().Trim();

                dtrow["fctlid"] = dr["fctlid"];
                dtrow["fpolno"] = dr["fpolno"];
                dtrow["fendtno"] = dr["fendtno"];

                dtrow["fsum"] = dr["fsum"];
                dtrow["fefffr"] = dr["fefffr"];
                dtrow["feffto"] = dr["feffto"];
                dtrow["fclass"] = dr["fclass"];

                dtrow["fsec"] = "1";
                dtrow["finsd"] = dr["finsd"];
                dtrow["pgpm1"] = Convert.ToDecimal(dr["fgpm"]) - Convert.ToDecimal(dr["flgpm"]);
                dtrow["pgpm2"] = Convert.ToDecimal(dr["flgpm"]);

                dtrow["pcomamt1"] = Convert.ToDecimal(dr["fcomamt"]) + Convert.ToDecimal(dr["ftdamt"]) -
                                    Convert.ToDecimal(dr["flcomamt"]) + Convert.ToDecimal(dr["fltdamt"]);

                dtrow["pcomamt2"] = Convert.ToDecimal(dr["flcomamt"]) + Convert.ToDecimal(dr["fltdamt"]);

                if (fclass == "CAR")
                {
                    dtrow["fclsseq"] = UtilityFunc.ClsSeq(fclass, "SEC1");
                    dtrow["fclsname"] = UtilityFunc.ClsName(fclass, "SEC1");

                    dvorih.RowFilter = string.Format("fctlid_1 = '{0}' and fkind = '{1}'", dr["fctlid"],"1");
                }
                else
                {
                    dtrow["fclsseq"] = UtilityFunc.ClsSeq(fclass, fsclass); ;
                    dtrow["fclsname"] = UtilityFunc.ClsName(fclass, fsclass);

                    dvorih.RowFilter = string.Format("fctlid_1 = '{0}'", dr["fctlid"]);
                }

                if (dvorih.Count != 0)
                {
                    dtrow["fxol"] = dvorih[0]["fxol"];
                    dt.Rows.Add(dtrow);
                }

                if (fclass == "CAR")
                {
                    DataRow dtrow2 = dt.NewRow();

                    dtrow2.ItemArray = dtrow.ItemArray;

                    dtrow2["fclsseq"] = UtilityFunc.ClsSeq(fclass, "SEC2");
                    dtrow2["fclsname"] = UtilityFunc.ClsName(fclass, "SEC2");
                    dtrow2["fsec"] = "2";

                    dvorih.RowFilter = string.Format("fctlid_1 = '{0}' and fkind = '{1}'", dr["fctlid"], "2");

                    if (dvorih.Count != 0)
                    {
                        dtrow2["fxol"] = dvorih[0]["fxol"];
                        dt.Rows.Add(dtrow2);
                    }
                }
            }

            return dt;
        }

        public static DataTable GenUnInvBrkDn(DataTable dtoinvh, DataTable dtpolh, DataTable dtoriinstl,
                                              DataTable dtxol, DateTime refdate, DateTime datefr, DateTime dateto)
        {
            DataTable dt = new DataTable();

            dt.Columns.Add("fctlid_e", typeof(String));
            dt.Columns.Add("fctlid_i", typeof(String));
            dt.Columns.Add("fpolno", typeof(String));
            dt.Columns.Add("fendtno", typeof(String));
            dt.Columns.Add("fsum", typeof(Decimal));
            dt.Columns.Add("fclass", typeof(String));
            dt.Columns.Add("fefffr", typeof(DateTime));
            dt.Columns.Add("feffto", typeof(DateTime));
            dt.Columns.Add("fclsseq", typeof(String));
            dt.Columns.Add("fclsname", typeof(String));
            dt.Columns.Add("fsec", typeof(String));
            dt.Columns.Add("finsd", typeof(String));
            dt.Columns.Add("fxol", typeof(String));
            dt.Columns.Add("finvno", typeof(String));
            dt.Columns.Add("fbkdate", typeof(DateTime));
            dt.Columns.Add("finstall", typeof(Int32));
            dt.Columns.Add("finvdate", typeof(DateTime));
            dt.Columns.Add("pgpm1", typeof(Decimal));
            dt.Columns.Add("pgpm2", typeof(Decimal));
            dt.Columns.Add("pcomamt1", typeof(Decimal));
            dt.Columns.Add("pcomamt2", typeof(Decimal));
            dt.Columns.Add("fgpm", typeof(Decimal));
            dt.Columns.Add("fcomamt", typeof(Decimal));
            dt.Columns.Add("fnpm", typeof(Decimal));
            dt.Columns.Add("flvyamt1", typeof(Decimal));
            dt.Columns.Add("flvyamt2", typeof(Decimal));
            dt.Columns.Add("flvyamt3", typeof(Decimal));
            dt.Columns.Add("fgpretn", typeof(Decimal));
            dt.Columns.Add("fcomretn", typeof(Decimal));
            dt.Columns.Add("fpayretn", typeof(Decimal));
            dt.Columns.Add("fgptty", typeof(Decimal));
            dt.Columns.Add("fcomtty", typeof(Decimal));
            dt.Columns.Add("fpaytty", typeof(Decimal));
            dt.Columns.Add("fgpfac", typeof(Decimal));
            dt.Columns.Add("fcomfac", typeof(Decimal));
            dt.Columns.Add("fpayfac", typeof(Decimal));
            dt.Columns.Add("fgpfacb", typeof(Decimal));
            dt.Columns.Add("fcomfacb", typeof(Decimal));
            dt.Columns.Add("fpayfacb", typeof(Decimal));
            dt.Columns.Add("fgpfacnp", typeof(Decimal));
            dt.Columns.Add("fcomfacnp", typeof(Decimal));
            dt.Columns.Add("fpayfacnp", typeof(Decimal));
            dt.Columns.Add("bgpxol", typeof(Decimal));
            dt.Columns.Add("fgpxol", typeof(Decimal));

            DataView dvpolh = dtpolh.DefaultView, dvoriinstl = dtoriinstl.DefaultView, dvxol = dtxol.DefaultView;
            Decimal fprate = 0;

            dvxol.RowFilter = string.Format("{0} >= fefffr and {0} <= feffto", csCommon.DbDateFormat(refdate));

            if (dvxol.Count != 0)
                fprate = Convert.ToDecimal(dvxol[0]["fprate"]);

            foreach (DataRow dr in dtoinvh.Rows)
            {
                dvpolh.RowFilter = string.Format("fctlid = '{0}'", dr["fctlid_1"]);

                for (Int32 i = 0; i < dvpolh.Count; i++)
                {
                    DataRow dtrow = dt.NewRow();

                    dtrow["fctlid_e"] = dvpolh[i]["fctlid"];
                    dtrow["fctlid_i"] = dr["fctlid"];
                    dtrow["fpolno"] = dvpolh[i]["fpolno"];
                    dtrow["fendtno"] = dvpolh[i]["fendtno"];
                    dtrow["fsum"] = dvpolh[i]["fsum"];
                    dtrow["fefffr"] = dvpolh[i]["fefffr"];
                    dtrow["feffto"] = dvpolh[i]["feffto"];
                    dtrow["fclass"] = dvpolh[i]["fclass"];
                    dtrow["fclsseq"] = dvpolh[i]["fclsseq"];
                    dtrow["fclsname"] = dvpolh[i]["fclsname"];
                    dtrow["fsec"] = dvpolh[i]["fsec"];
                    dtrow["finsd"] = dvpolh[i]["finsd"];
                    dtrow["fxol"] = dvpolh[i]["fxol"];
                    dtrow["finvno"] = dr["finvno"];
                    dtrow["fbkdate"] = dr["fbkdate"];
                    dtrow["finstall"] = dr["finstall"];
                    dtrow["finvdate"] = dr["finvdate"];
                    dtrow["pgpm1"] = dvpolh[i]["pgpm1"];
                    dtrow["pgpm2"] = dvpolh[i]["pgpm2"];
                    dtrow["pcomamt1"] = dvpolh[i]["pcomamt1"];
                    dtrow["pcomamt2"] = dvpolh[i]["pcomamt2"];
                    dtrow["fgpretn"] = 0;
                    dtrow["fcomretn"] = 0;
                    dtrow["fpayretn"] = 0;
                    dtrow["fgptty"] = 0;
                    dtrow["fcomtty"] = 0;
                    dtrow["fpaytty"] = 0;
                    dtrow["fgpfac"] = 0;
                    dtrow["fcomfac"] = 0;
                    dtrow["fpayfac"] = 0;
                    dtrow["fgpfacb"] = 0;
                    dtrow["fcomfacb"] = 0;
                    dtrow["fpayfacb"] = 0;
                    dtrow["fgpfacnp"] = 0;
                    dtrow["fcomfacnp"] = 0;
                    dtrow["fpayfacnp"] = 0;
                    dtrow["bgpxol"] = 0;
                    dtrow["fgpxol"] = 0;

                    Decimal fgpm = Convert.ToDecimal(dr["fgpm"]), fcomamt = Convert.ToDecimal(dr["fcomamt"]),
                            fnpm = Convert.ToDecimal(dr["fnpm"]),
                            flvyamt1 = Convert.ToDecimal(dr["flvyamt1"]), flvyamt2 = Convert.ToDecimal(dr["flvyamt2"]),
                            flvyamt3 = Convert.ToDecimal(dr["flvyamt3"]);

                    Decimal pgpm1 = Convert.ToDecimal(dvpolh[i]["pgpm1"]),
                            pgpm2 = Convert.ToDecimal(dvpolh[i]["pgpm2"]);

                    if (dtrow["fclass"].ToString().Trim() != "CAR")
                    {
                        dtrow["fgpm"] = fgpm;
                        dtrow["fcomamt"] = fcomamt;
                        dtrow["fnpm"] = fnpm;
                        dtrow["flvyamt1"] = flvyamt1;
                        dtrow["flvyamt2"] = flvyamt2;
                        dtrow["flvyamt3"] = flvyamt3;

                        dvoriinstl.RowFilter = string.Format("fctlid_i = '{0}'", dtrow["fctlid_i"]);
                    }

                    if (dtrow["fclass"].ToString().Trim() == "CAR" && dtrow["fsec"].ToString().Trim() == "1")
                    {
                        dtrow["fgpm"] = Math.Round(fgpm * pgpm1 / (pgpm1 + pgpm2), 2, MidpointRounding.AwayFromZero);
                        dtrow["fcomamt"] = Math.Round(fcomamt * pgpm1 / (pgpm1 + pgpm2), 2, MidpointRounding.AwayFromZero);
                        dtrow["fnpm"] = Math.Round(fnpm * pgpm1 / (pgpm1 + pgpm2), 2, MidpointRounding.AwayFromZero);
                        dtrow["flvyamt1"] = Math.Round(flvyamt1 * pgpm1 / (pgpm1 + pgpm2), 2, MidpointRounding.AwayFromZero);
                        dtrow["flvyamt2"] = Math.Round(flvyamt2 * pgpm1 / (pgpm1 + pgpm2), 2, MidpointRounding.AwayFromZero);
                        dtrow["flvyamt3"] = Math.Round(flvyamt3 * pgpm1 / (pgpm1 + pgpm2), 2, MidpointRounding.AwayFromZero);

                        dvoriinstl.RowFilter = string.Format("fctlid_i = '{0}' and fkind = '{1}'",
                                                             dtrow["fctlid_i"],
                                                             dtrow["fsec"].ToString().Trim());
                    }

                    if (dtrow["fclass"].ToString().Trim() == "CAR" && dtrow["fsec"].ToString().Trim() == "2")
                    {
                        dtrow["fgpm"] = fgpm - Math.Round(fgpm * pgpm1 / (pgpm1 + pgpm2), 2, MidpointRounding.AwayFromZero);
                        dtrow["fcomamt"] = fcomamt - Math.Round(fcomamt * pgpm1 / (pgpm1 + pgpm2), 2, MidpointRounding.AwayFromZero);
                        dtrow["fnpm"] = fnpm - Math.Round(fnpm * pgpm1 / (pgpm1 + pgpm2), 2, MidpointRounding.AwayFromZero);
                        dtrow["flvyamt1"] = flvyamt1 - Math.Round(flvyamt1 * pgpm1 / (pgpm1 + pgpm2), 2, MidpointRounding.AwayFromZero);
                        dtrow["flvyamt2"] = flvyamt2 - Math.Round(flvyamt2 * pgpm1 / (pgpm1 + pgpm2), 2, MidpointRounding.AwayFromZero);
                        dtrow["flvyamt3"] = flvyamt3 - Math.Round(flvyamt3 * pgpm1 / (pgpm1 + pgpm2), 2, MidpointRounding.AwayFromZero);

                        dvoriinstl.RowFilter = string.Format("fctlid_i = '{0}' and fkind = '{1}'",
                                         dtrow["fctlid_i"],
                                         dtrow["fsec"].ToString().Trim());
                    }

                    for (Int32 j = 0; j < dvoriinstl.Count; j++)
                    {
                        Decimal rigpm = Convert.ToDecimal(dvoriinstl[j]["fgpm"]),
                                ricomamt = Convert.ToDecimal(dvoriinstl[j]["fcomamt"]),
                                ripayable = Convert.ToDecimal(dvoriinstl[j]["fpayable"]);

                        String fmodule = dvoriinstl[j]["fmodule"].ToString().Trim().Substring(1,4);

                        DateTime? fbkdate;

                        if (!String.IsNullOrEmpty(dvoriinstl[j]["fbkdate"].ToString()))
                            dtrow["fbkdate"] = Convert.ToDateTime(dvoriinstl[j]["fbkdate"]);

                        if (String.IsNullOrEmpty(dtrow["fbkdate"].ToString()))
                            fbkdate = null;
                        else 
                            fbkdate = Convert.ToDateTime(dtrow["fbkdate"]);

                        if (fmodule == "0101")
                        {
                            dtrow["fgpretn"] = Convert.ToDecimal(dtrow["fgpretn"])-rigpm;
                            dtrow["fcomretn"] = Convert.ToDecimal(dtrow["fcomretn"]) - ricomamt;
                            dtrow["fpayretn"] = Convert.ToDecimal(dtrow["fpayretn"]) - ripayable;
                        }
                        else if (fmodule == "0102")
                        {
                            dtrow["fgptty"] = Convert.ToDecimal(dtrow["fgptty"]) - rigpm;
                            dtrow["fcomtty"] = Convert.ToDecimal(dtrow["fcomtty"]) - ricomamt;
                            dtrow["fpaytty"] = Convert.ToDecimal(dtrow["fpaytty"]) - ripayable;
                        }
                        else if (fmodule == "0103" && fbkdate >= datefr && fbkdate <= dateto)
                        {
                            dtrow["bgpxol"] = Convert.ToDecimal(dtrow["bgpxol"]) - rigpm;
                        }
                        else if (fmodule == "0104")
                        {
                            dtrow["fgpfac"] = Convert.ToDecimal(dtrow["fgpfac"]) - rigpm;
                            dtrow["fcomfac"] = Convert.ToDecimal(dtrow["fcomfac"]) - ricomamt;
                            dtrow["fpayfac"] = Convert.ToDecimal(dtrow["fpayfac"]) - ripayable;
                        }
                        else if (fmodule == "0105")
                        {
                            dtrow["fgpfacb"] = Convert.ToDecimal(dtrow["fgpfacb"]) - rigpm;
                            dtrow["fcomfacb"] = Convert.ToDecimal(dtrow["fcomfacb"]) - ricomamt;
                            dtrow["fpayfacb"] = Convert.ToDecimal(dtrow["fpayfacb"]) - ripayable;
                        }
                        else if (fmodule == "0106")
                        {
                            dtrow["fgpfacnp"] = Convert.ToDecimal(dtrow["fgpfacnp"]) - rigpm;
                            dtrow["fcomfacnp"] = Convert.ToDecimal(dtrow["fcomfacnp"]) - ricomamt;
                            dtrow["fpayfacnp"] = Convert.ToDecimal(dtrow["fpayfacnp"]) - ripayable;
                        }
                    }

                    if (dtrow["fxol"].ToString().Trim() == "1")
                    {
                        Decimal fgpretn = Convert.ToDecimal(dtrow["fgpretn"]), fgpfacnp = Convert.ToDecimal(dtrow["fgpfacnp"]);
                        dtrow["fgpxol"] = Math.Round((fgpretn - fgpfacnp) * (fprate / 100), 2, MidpointRounding.AwayFromZero);
                    }

                    dt.Rows.Add(dtrow);
                }
            }
            //fbkdate, dvoriinstl
            return dt;
        }

        public static DataTable GenUnInvPrem(DataTable dtbrkdnsrc1, DataTable dtbrkdnsrc2)
        {
            DataTable dtmerge;

            dtmerge = dtbrkdnsrc1.Copy();

            dtmerge.Columns.Add("pgpm", typeof(Decimal));
            dtmerge.Columns.Add("pcomamt", typeof(Decimal));
            dtmerge.Columns.Add("fstat_a", typeof(String));
            dtmerge.Columns.Add("fstat_b", typeof(String));
            dtmerge.Columns.Add("fgpxol_a", typeof(Decimal));
            dtmerge.Columns.Add("fgpxol_b", typeof(Decimal));
            dtmerge.Columns.Add("fcsum", typeof(Decimal));
            dtmerge.Columns.Add("fstatus", typeof(String));

            foreach (DataRow dr in dtmerge.Rows)
            {
                dr["fstat_a"] = dr["fctlid_i"];
                dr["fstat_b"] = "";
                dr["fgpxol_a"] = dr["fgpxol"];
                dr["fgpxol_b"] = 0.00;
            }

            DataView dvmerge = dtmerge.DefaultView;

            foreach (DataRow dr in dtbrkdnsrc2.Rows)
            {
                dvmerge.RowFilter = string.Format("fctlid_i = '{0}' and fsec = '{1}'",
                                         dr["fctlid_i"],
                                         dr["fsec"].ToString().Trim());

                if (dvmerge.Count != 0)
                { 
                    dvmerge[0]["fstat_b"] = dr["fctlid_i"];
                    dvmerge[0]["fgpxol_b"] = Convert.ToDecimal(dr["fgpxol"]);
                }
                else
                {
                    DataRowView rowView = dvmerge.AddNew();

                    rowView["fctlid_e"] = dr["fctlid_e"];
                    rowView["fctlid_i"] = dr["fctlid_i"];
                    rowView["fstat_a"] = "";
                    rowView["fstat_b"] = dr["fctlid_i"];
                    rowView["fpolno"] = dr["fpolno"];
                    rowView["fendtno"] = dr["fendtno"];

                    rowView["fsum"] = dr["fsum"];
                    rowView["fefffr"] = dr["fefffr"];
                    rowView["feffto"] = dr["feffto"];
                    rowView["fclass"] = dr["fclass"];
                    rowView["fclsseq"] = dr["fclsseq"];
                    rowView["fclsname"] = dr["fclsname"];
                    rowView["fsec"] = dr["fsec"];
                    rowView["finsd"] = dr["finsd"];
                    rowView["fxol"] = dr["fxol"];
                    rowView["finstall"] = dr["finstall"];
                    rowView["finvdate"] = dr["finvdate"];
                    rowView["fbkdate"] = dr["fbkdate"];
                    rowView["pgpm1"] = dr["pgpm1"];
                    rowView["pgpm2"] = dr["pgpm2"];
                    rowView["pcomamt1"] = dr["pcomamt1"];
                    rowView["pcomamt2"] = dr["pcomamt2"];

                    rowView["fgpm"] = dr["fgpm"];
                    rowView["fcomamt"] = dr["fcomamt"];
                    rowView["fnpm"] = dr["fnpm"];

                    rowView["flvyamt1"] = dr["flvyamt1"];
                    rowView["flvyamt2"] = dr["flvyamt2"];
                    rowView["flvyamt3"] = dr["flvyamt3"];

                    rowView["fgpretn"] = dr["fgpretn"];
                    rowView["fcomretn"] = dr["fcomretn"];
                    rowView["fpayretn"] = dr["fpayretn"];

                    rowView["fgptty"] = dr["fgptty"];
                    rowView["fcomtty"] = dr["fcomtty"];
                    rowView["fpaytty"] = dr["fpaytty"];

                    rowView["fgpfac"] = dr["fgpfac"];
                    rowView["fcomfac"] = dr["fcomfac"];
                    rowView["fpayfac"] = dr["fpayfac"];

                    rowView["fgpfacb"] = dr["fgpfacb"];
                    rowView["fcomfacb"] = dr["fcomfacb"];
                    rowView["fpayfacb"] = dr["fpayfacb"];

                    rowView["fgpfacnp"] = dr["fgpfacnp"];
                    rowView["fcomfacnp"] = dr["fcomfacnp"];
                    rowView["fpayfacnp"] = dr["fpayfacnp"];

                    rowView["fgpxol_a"] = 0.00;
                    rowView["fgpxol_b"] = dr["fgpxol"];
                    rowView["bgpxol"] = 0.00;

                    rowView.EndEdit();
                }
            }

            dvmerge.RowFilter = "";

            foreach (DataRow dr in dtmerge.Rows)
            {
                String fstat_a = dr["fstat_a"].ToString().Trim(), fstat_b = dr["fstat_b"].ToString().Trim();
                String fclass = dr["fclass"].ToString().Trim(), fsec = dr["fsec"].ToString().Trim();

                if (fstat_a != "" && fstat_b != "")
                    dr["fstatus"] = "1A";
                else if (fstat_a != "")
                    dr["fstatus"] = "1B";
                else
                    dr["fstatus"] = "2";

                //if(fclass != "CAR" || (fclass == "CAR" && fsec == "1"))  && this is replaced by the following line

                if (!"CAR|CLL".Contains(fclass.Trim()) || (fclass == "CAR" && fsec == "1"))
                    {
                        dr["pgpm"] = dr["pgpm1"];
                    dr["pcomamt"] = dr["pcomamt1"];
                } else
                {
                    dr["pgpm"] = dr["pgpm2"];
                    dr["pcomamt"] = dr["pcomamt2"];
                }

                dr["fcsum"] = Math.Round(Convert.ToDecimal(dr["fsum"]) * Convert.ToDecimal(dr["fgpm"]) /
                              Convert.ToDecimal(dr["pgpm"]), 2, MidpointRounding.AwayFromZero);
            }

            return dtmerge;
        }

        public static DataTable GenUnInvSum(DataTable dtmerge, DateTime vlastasat, DateTime vthisasat)
        {
            String lastasat = String.Format("{0:dd MMMM yyyy}", vlastasat),
                   thisasat = String.Format("{0:dd MMMM yyyy}", vthisasat);
            
            DataTable dtsummary = new DataTable();

            dtsummary.Columns.Add("frectype", typeof(String));
            dtsummary.Columns.Add("fclsseq", typeof(String));
            dtsummary.Columns.Add("fclass", typeof(String));
            dtsummary.Columns.Add("fclsname", typeof(String));
            dtsummary.Columns.Add("fdesc", typeof(String));


            DataColumn gpmCol = dtsummary.Columns.Add("fgpm", typeof(Decimal)),
                       gpriCol = dtsummary.Columns.Add("fgpri", typeof(Decimal)),
                       netprmCol = dtsummary.Columns.Add("fnetprm", typeof(Decimal)),
                       comamtCol = dtsummary.Columns.Add("fcomamt", typeof(Decimal)),
                       ricomCol = dtsummary.Columns.Add("fricom", typeof(Decimal)),
                       netcomCol = dtsummary.Columns.Add("fnetcom", typeof(Decimal));

            gpmCol.DefaultValue = 0.00m;
            gpriCol.DefaultValue = 0.00m;
            netprmCol.DefaultValue = 0.00m;
            comamtCol.DefaultValue = 0.00m;
            ricomCol.DefaultValue = 0.00m;
            netcomCol.DefaultValue = 0.00m;

            if (dtmerge.Rows.Count != 0)
            {
                AppendUnInvSumRow(dtsummary, "01", "ZZ", "ZZ", "All Classes", "As at " + lastasat);
                AppendUnInvSumRow(dtsummary, "02", "ZZ", "ZZ", "All Classes", "Adjustment to Old Uninvoiced Entries");
                AppendUnInvSumRow(dtsummary, "03", "ZZ", "ZZ", "All Classes", "New Uninvoiced Entries during Period");
                AppendUnInvSumRow(dtsummary, "04", "ZZ", "ZZ", "All Classes", "Invoicing Old Uninvoiced Entries");
                AppendUnInvSumRow(dtsummary, "05", "ZZ", "ZZ", "All Classes", "Change in Uninvoiced Premium");
                AppendUnInvSumRow(dtsummary, "06", "ZZ", "ZZ", "All Classes", "As at " + thisasat);
            }

            foreach (DataRow dr in dtmerge.Rows)
            {
                String fstatus = dr["fstatus"].ToString().Trim(), fclsseq = dr["fclsseq"].ToString().Trim(),
                       fclass = dr["fclass"].ToString().Trim(),fclsname = dr["fclsname"].ToString().Trim();

                dtsummary.DefaultView.RowFilter = String.Format("fclsseq = '{0}'", fclsseq);

                if (dtsummary.DefaultView.Count == 0)
                {
                    AppendUnInvSumRow(dtsummary, "01", fclsseq,fclass, fclsname, "As at " + lastasat);
                    AppendUnInvSumRow(dtsummary, "02", fclsseq,fclass, fclsname, "Adjustment to Old Uninvoiced Entries");
                    AppendUnInvSumRow(dtsummary, "03", fclsseq,fclass, fclsname, "New Uninvoiced Entries during Period");
                    AppendUnInvSumRow(dtsummary, "04", fclsseq,fclass, fclsname, "Invoicing Old Uninvoiced Entries");
                    AppendUnInvSumRow(dtsummary, "05", fclsseq,fclass, fclsname, "Change in Uninvoiced Premium");
                    AppendUnInvSumRow(dtsummary, "06", fclsseq,fclass, fclsname, "As at " + thisasat);
                }

                if (fstatus.Substring(0,1) == "1")
                {
                    UpdUnInvSumRow(dr, dtsummary, "01", fclsseq);
                    UpdUnInvSumRow(dr, dtsummary, "02", fclsseq);
                }

                if (fstatus.Substring(0, 1) == "2")
                {
                    UpdUnInvSumRow(dr, dtsummary, "03", fclsseq);
                }

                if (fstatus == "1B")
                {
                    UpdUnInvSumRow(dr, dtsummary, "04", fclsseq);
                }

                UpdUnInvSumRow(dr, dtsummary, "05", fclsseq);

                if (fstatus == "1A" || fstatus.Substring(0, 1) == "2")
                {
                    UpdUnInvSumRow(dr, dtsummary, "06", fclsseq);
                }
            }

            return dtsummary;
        }

        private static void AppendUnInvSumRow(DataTable dttar, String frectype, String fclsseq, 
                                              String fclass, String fclsname, String fdesc)
        {
            DataView dvtar = dttar.DefaultView;
            DataRowView rowView = dvtar.AddNew();

            rowView["frectype"] = frectype;
            rowView["fclsseq"] = fclsseq;
            rowView["fclass"] = fclass;
            rowView["fclsname"] = fclsname;
            rowView["fdesc"] = fdesc;

            rowView.EndEdit();
        }

        private static void UpdUnInvSumRow(DataRow drsrc, DataTable dttar, String frectype, String fclsseq)
        {
            String fstatus = drsrc["fstatus"].ToString().Trim();
            Decimal fgpm = Convert.ToDecimal(drsrc["fgpm"]), fgptty = Convert.ToDecimal(drsrc["fgptty"]),
                    fgpfac = Convert.ToDecimal(drsrc["fgpfac"]), fgpfacnp = Convert.ToDecimal(drsrc["fgpfacnp"]),
                    fgpfacb = Convert.ToDecimal(drsrc["fgpfacb"]), fgpxol_a = Convert.ToDecimal(drsrc["fgpxol_a"]),
                    fcomamt = Convert.ToDecimal(drsrc["fcomamt"]), fcomtty = Convert.ToDecimal(drsrc["fcomtty"]),
                    fcomfac = Convert.ToDecimal(drsrc["fcomfac"]), fcomfacnp = Convert.ToDecimal(drsrc["fcomfacnp"]),
                    fcomfacb = Convert.ToDecimal(drsrc["fcomfacb"]),
                    fgpxol_b = Convert.ToDecimal(drsrc["fgpxol_b"]), bgpxol = Convert.ToDecimal(drsrc["bgpxol"]);

            DataView dvtar = dttar.DefaultView;
            dvtar.RowFilter = String.Format("frectype = '{0}' and fclsseq = '{1}'", frectype, fclsseq);

            Decimal zgpm = 0.00m, zgpri = 0.00m, znetprm = 0.00m, zcomamt = 0.00m, zricom = 0.00m, znetcom = 0.00m;

            if ("01|03|04|06".Contains(frectype) || (frectype == "05" && fstatus !="1A"))
            {
                Decimal fgpxol;
                Int32 multifac;

                if (frectype == "01")
                    fgpxol = fgpxol_a;
                else if ("03|06".Contains(frectype))
                    fgpxol = fgpxol_b;
                else if (frectype == "04")
                    fgpxol = bgpxol;
                else if (fstatus == "1B")
                    fgpxol = fgpxol_a;
                else
                    fgpxol = fgpxol_b;

                if ("01|03|06".Contains(frectype))
                    multifac = 1;
                else if (frectype == "04")
                    multifac = -1;
                else if (fstatus == "1B")
                    multifac = -1;
                else
                    multifac = 1;

                zgpm = fgpm * multifac;
                zgpri = (fgptty + fgpfac + fgpfacnp + fgpfacb + fgpxol) * multifac;
                znetprm = (fgpm - fgptty - fgpfac - fgpfacnp - fgpfacb - fgpxol) * multifac;
                zcomamt = fcomamt * multifac;
                zricom = (fcomtty + fcomfac + fcomfacnp + fcomfacb) * multifac;
                znetcom = (fcomamt - fcomtty - fcomfac - fcomfacnp - fcomfacb) * multifac;

                dvtar[0]["fgpm"] = Convert.ToDecimal(dvtar[0]["fgpm"]) + zgpm;
                dvtar[0]["fgpri"] = Convert.ToDecimal(dvtar[0]["fgpri"]) + zgpri;
                dvtar[0]["fnetprm"] = Convert.ToDecimal(dvtar[0]["fnetprm"]) + znetprm;
                dvtar[0]["fcomamt"] = Convert.ToDecimal(dvtar[0]["fcomamt"]) + zcomamt;
                dvtar[0]["fricom"] = Convert.ToDecimal(dvtar[0]["fricom"]) + zricom;
                dvtar[0]["fnetcom"] = Convert.ToDecimal(dvtar[0]["fnetcom"]) + znetcom;
            }

            if (frectype == "02" || (frectype == "05" && fstatus == "1A"))
            {
                Decimal fgpxol;

                if (frectype == "05" || (frectype == "02" && fstatus == "1A"))
                    fgpxol = fgpxol_b - fgpxol_a;
                else 
                    fgpxol = bgpxol - fgpxol_a;

                zgpri = fgpxol;
                znetprm = -fgpxol;

                dvtar[0]["fgpri"] = Convert.ToDecimal(dvtar[0]["fgpri"]) +
                                        fgpxol;

                dvtar[0]["fnetprm"] = Convert.ToDecimal(dvtar[0]["fnetprm"]) -
                                        fgpxol;
            }

            dvtar.RowFilter = String.Format("frectype = '{0}' and fclsseq = '{1}'", frectype, "ZZ");

            dvtar[0]["fgpm"] = Convert.ToDecimal(dvtar[0]["fgpm"]) + zgpm;
            dvtar[0]["fgpri"] = Convert.ToDecimal(dvtar[0]["fgpri"]) + zgpri;
            dvtar[0]["fnetprm"] = Convert.ToDecimal(dvtar[0]["fnetprm"]) + znetprm;
            dvtar[0]["fcomamt"] = Convert.ToDecimal(dvtar[0]["fcomamt"]) + zcomamt;
            dvtar[0]["fricom"] = Convert.ToDecimal(dvtar[0]["fricom"]) + zricom;
            dvtar[0]["fnetcom"] = Convert.ToDecimal(dvtar[0]["fnetcom"]) + znetcom;
        }

        public static DataTable GenGrndInvh(DataTable dtinvhsrc, DataTable dtpolhsrc, DateTime vthisasat)
        {
            DataTable dt = new DataTable();

            dt.Columns.Add("fctlid_e", typeof(String));
            dt.Columns.Add("fctlid_i", typeof(String));
            dt.Columns.Add("fbkdate", typeof(DateTime));
            dt.Columns.Add("fefffr", typeof(DateTime));
            dt.Columns.Add("finstall", typeof(Int32));
            dt.Columns.Add("ftotinstal", typeof(Int32));
            dt.Columns.Add("fbus", typeof(String));
            dt.Columns.Add("fclnt", typeof(String));
            dt.Columns.Add("fsclnt", typeof(String));
            dt.Columns.Add("fdbtr", typeof(String));
            dt.Columns.Add("fclass", typeof(String));
            dt.Columns.Add("fcomtype", typeof(String));
            dt.Columns.Add("fgpm_i", typeof(Decimal));
            dt.Columns.Add("ftdamt_i", typeof(Decimal));
            dt.Columns.Add("fcomamt_i", typeof(Decimal));
            dt.Columns.Add("fgpm_p1", typeof(Decimal));
            dt.Columns.Add("ftdamt_p1", typeof(Decimal));
            dt.Columns.Add("fcomamt_p1", typeof(Decimal));
            dt.Columns.Add("fgpm_p2", typeof(Decimal));
            dt.Columns.Add("ftdamt_p2", typeof(Decimal));
            dt.Columns.Add("fcomamt_p2", typeof(Decimal));

            DataView dvinvhsrc = dtinvhsrc.DefaultView, dvpolhsrc = dtpolhsrc.DefaultView;
            dvinvhsrc.RowFilter = String.Format("fbkdate <= {0}", csCommon.DbDateFormat(vthisasat));

            for (int i = 0; i < dvinvhsrc.Count; i++)
            {
                String fctlid_1 = dvinvhsrc[i]["fctlid_1"].ToString(), fbus = dvinvhsrc[i]["fbus"].ToString().Trim(),
                       fclass = dvinvhsrc[i]["fclass"].ToString().Trim();

                dvpolhsrc.RowFilter = String.Format("fctlid = '{0}'", fctlid_1);

                DataRow dtrow = dt.NewRow();

                dtrow["fctlid_e"] = dvinvhsrc[i]["fctlid_1"];
                dtrow["fctlid_i"] = dvinvhsrc[i]["fctlid"];
                dtrow["fbkdate"] = Convert.ToDateTime(dvinvhsrc[i]["fbkdate"]);
                dtrow["fefffr"] = Convert.ToDateTime(dvinvhsrc[i]["fefffr"]);
                dtrow["finstall"] = Convert.ToInt32(dvinvhsrc[i]["finstall"]);
                dtrow["ftotinstal"] = Convert.ToInt32(dvinvhsrc[i]["ftotinstal"]);
                dtrow["fbus"] = dvinvhsrc[i]["fbus"];
                dtrow["fclnt"] = dvinvhsrc[i]["fclnt"];
                dtrow["fsclnt"] = dvinvhsrc[i]["fsclnt"];
                dtrow["fdbtr"] = dvinvhsrc[i]["fdbtr"];
                dtrow["fclass"] = dvinvhsrc[i]["fclass"];
                dtrow["fcomtype"] = dvinvhsrc[i]["fcomtype"];
                dtrow["fgpm_i"] = Convert.ToDecimal(dvinvhsrc[i]["fgpm"]);
                dtrow["ftdamt_i"] = Convert.ToDecimal(dvinvhsrc[i]["ftdamt"]);

                if (fbus == "R")
                    dtrow["fcomamt_i"] = Convert.ToDecimal(dvinvhsrc[i]["fcomamt"]) + Convert.ToDecimal(dvinvhsrc[i]["ftdamt"]);
                else
                    dtrow["fcomamt_i"] = Convert.ToDecimal(dvinvhsrc[i]["fcomamt"]);

                if (fclass == "CAR")
                {
                    dtrow["fgpm_p1"] = Convert.ToDecimal(dvpolhsrc[0]["fgpm"]) - Convert.ToDecimal(dvpolhsrc[0]["flgpm"]);
                    dtrow["ftdamt_p1"] = Convert.ToDecimal(dvpolhsrc[0]["ftdamt"]) - Convert.ToDecimal(dvpolhsrc[0]["fltdamt"]);
                    dtrow["fgpm_p2"] = Convert.ToDecimal(dvpolhsrc[0]["flgpm"]);
                    dtrow["ftdamt_p2"] = Convert.ToDecimal(dvpolhsrc[0]["fltdamt"]);

                    if (fbus == "R")
                    {
                        dtrow["fcomamt_p1"] = Convert.ToDecimal(dvpolhsrc[0]["fcomamt"]) - Convert.ToDecimal(dvpolhsrc[0]["flcomamt"]) +
                                              Convert.ToDecimal(dvpolhsrc[0]["ftdamt"]) - Convert.ToDecimal(dvpolhsrc[0]["fltdamt"]);
                        dtrow["fcomamt_p2"] = Convert.ToDecimal(dvpolhsrc[0]["flcomamt"]) + Convert.ToDecimal(dvpolhsrc[0]["fltdamt"]);
                    }
                    else
                    {
                        dtrow["fcomamt_p1"] = Convert.ToDecimal(dvpolhsrc[0]["fcomamt"]) - Convert.ToDecimal(dvpolhsrc[0]["flcomamt"]);
                        dtrow["fcomamt_p2"] = Convert.ToDecimal(dvpolhsrc[0]["flcomamt"]);
                    }
                }
                else
                {
                    dtrow["fgpm_p1"] = Convert.ToDecimal(dvpolhsrc[0]["fgpm"]);
                    dtrow["ftdamt_p1"] = Convert.ToDecimal(dvpolhsrc[0]["ftdamt"]);
                    dtrow["fgpm_p2"] = 0.00;
                    dtrow["ftdamt_p2"] = 0.00;
                    dtrow["fcomamt_p2"] = 0.00;

                    if (fbus == "R")
                        dtrow["fcomamt_p1"] = Convert.ToDecimal(dvpolhsrc[0]["fcomamt"]) + Convert.ToDecimal(dvpolhsrc[0]["ftdamt"]);
                    else
                        dtrow["fcomamt_p1"] = Convert.ToDecimal(dvpolhsrc[0]["fcomamt"]);
                }
                dt.Rows.Add(dtrow);
            }

            return dt;
        }

        public static DataTable GenGrndDirInfo(DataTable dtinvhsrc, DataTable dtpolhsrc, DataTable dtmprdr,
                                               DateTime vfbk_fr, DateTime vfbk_to, DateTime vlastasat)
        {
            DataTable dt = CreateDirPremStruc();

            DataView dvinvhsrc = dtinvhsrc.DefaultView, dv = dt.DefaultView;
            dvinvhsrc.RowFilter = String.Format("fbkdate <= {0}", csCommon.DbDateFormat(vfbk_to));
            dvinvhsrc.Sort = "fctlid_e";

            for (int i = 0; i < dvinvhsrc.Count; i++)
            {
                String fctlid_e = dvinvhsrc[i]["fctlid_e"].ToString();
                DateTime fbkdate = Convert.ToDateTime(dvinvhsrc[i]["fbkdate"]);

                dv.RowFilter = String.Format("fctlid_e = '{0}'", fctlid_e);

                if (dv.Count == 0)
                {
                    DataRow dtrow = dt.NewRow();

                    dtrow["fctlid_e"] = dvinvhsrc[i]["fctlid_e"];
                    dtrow["ftotinstal"] = dvinvhsrc[i]["ftotinstal"];
                    dtrow["fbus"] = dvinvhsrc[i]["fbus"];
                    dtrow["fclnt"] = dvinvhsrc[i]["fclnt"];
                    dtrow["fsclnt"] = dvinvhsrc[i]["fsclnt"];
                    dtrow["fdbtr"] = dvinvhsrc[i]["fdbtr"];
                    dtrow["fclass"] = dvinvhsrc[i]["fclass"];
                    dtrow["fcomtype"] = dvinvhsrc[i]["fcomtype"];
                    dtrow["fgpm_p1"] = dvinvhsrc[i]["fgpm_p1"];
                    dtrow["ftdamt_p1"] = dvinvhsrc[i]["ftdamt_p1"];
                    dtrow["fcomamt_p1"] = dvinvhsrc[i]["fcomamt_p1"];
                    dtrow["fgpm_p2"] = dvinvhsrc[i]["fgpm_p2"];
                    dtrow["ftdamt_p2"] = dvinvhsrc[i]["ftdamt_p2"];
                    dtrow["fcomamt_p2"] = dvinvhsrc[i]["fcomamt_p2"];

                    dt.Rows.Add(dtrow);
                }

                if (fbkdate.Date >= vfbk_fr && fbkdate.Date <= vfbk_to)
                {
                    if (String.IsNullOrEmpty(dv[0]["finstall"].ToString()))
                    {
                        dv[0]["finstall"] = dvinvhsrc[i]["finstall"];
                        dv[0]["fbkdate"] = dvinvhsrc[i]["fbkdate"];
                    }

                    if (Convert.ToInt32(dvinvhsrc[i]["finstall"]) > Convert.ToInt32(dv[0]["finstall"]))
                        dv[0]["finstall"] = dvinvhsrc[i]["finstall"];

                    if (Convert.ToDateTime(dvinvhsrc[i]["fbkdate"]).Date < Convert.ToDateTime(dv[0]["fbkdate"]).Date)
                        dv[0]["fbkdate"] = dvinvhsrc[i]["fbkdate"];
                   
                }

                if (fbkdate.Date <= vlastasat)
                {
                    if (String.IsNullOrEmpty(dv[0]["finstall_a"].ToString()))
                    {
                        dv[0]["finstall_a"] = dvinvhsrc[i]["finstall"];
                        dv[0]["fbkdate_a"] = dvinvhsrc[i]["fbkdate"];
                    }

                    if (Convert.ToInt32(dvinvhsrc[i]["finstall"]) > Convert.ToInt32(dv[0]["finstall_a"]))
                        dv[0]["finstall_a"] = dvinvhsrc[i]["finstall"];

                    if (Convert.ToDateTime(dvinvhsrc[i]["fbkdate"]).Date < Convert.ToDateTime(dv[0]["fbkdate_a"]).Date)
                        dv[0]["fbkdate_a"] = dvinvhsrc[i]["fbkdate"];

                    dv[0]["fgpm_ia"] = Convert.ToDecimal(dv[0]["fgpm_ia"]) + Convert.ToDecimal(dvinvhsrc[i]["fgpm_i"]);
                    dv[0]["ftdamt_ia"] = Convert.ToDecimal(dv[0]["ftdamt_ia"]) + Convert.ToDecimal(dvinvhsrc[i]["ftdamt_i"]);
                    dv[0]["fcomamt_ia"] = Convert.ToDecimal(dv[0]["fcomamt_ia"]) + Convert.ToDecimal(dvinvhsrc[i]["fcomamt_i"]);
                }

                if (fbkdate.Date <= vfbk_to)
                {
                    dv[0]["fgpm_iy"] = Convert.ToDecimal(dv[0]["fgpm_iy"]) + Convert.ToDecimal(dvinvhsrc[i]["fgpm_i"]);
                    dv[0]["ftdamt_iy"] = Convert.ToDecimal(dv[0]["ftdamt_iy"]) + Convert.ToDecimal(dvinvhsrc[i]["ftdamt_i"]);
                    dv[0]["fcomamt_iy"] = Convert.ToDecimal(dv[0]["fcomamt_iy"]) + Convert.ToDecimal(dvinvhsrc[i]["fcomamt_i"]);
                }
            }

            dv.RowFilter = "";

            for (int i = 0; i < dv.Count; i++)
            {
                if (String.IsNullOrEmpty(dv[i]["finstall"].ToString()))
                {
                    dv[i]["finstall"] = dv[i]["finstall_a"];
                    dv[i]["fbkdate"] = dv[i]["fbkdate_a"];
                }

                String fclass = dv[i]["fclass"].ToString().Trim();
                Int32 ftotinstal = Convert.ToInt32(dv[i]["ftotinstal"]);
                Decimal fgpm_ia = Convert.ToDecimal(dv[i]["fgpm_ia"]),
                        fgpm_iy = Convert.ToDecimal(dv[i]["fgpm_iy"]),
                        fgpm_p1 = Convert.ToDecimal(dv[i]["fgpm_p1"]), fgpm_p2 = Convert.ToDecimal(dv[i]["fgpm_p2"]);
                Decimal ftdamt_ia = Convert.ToDecimal(dv[i]["ftdamt_ia"]),
                        ftdamt_iy = Convert.ToDecimal(dv[i]["ftdamt_iy"]),
                        ftdamt_p1 = Convert.ToDecimal(dv[i]["ftdamt_p1"]), ftdamt_p2 = Convert.ToDecimal(dv[i]["ftdamt_p2"]);
                Decimal fcomamt_ia = Convert.ToDecimal(dv[i]["fcomamt_ia"]),
                        fcomamt_iy = Convert.ToDecimal(dv[i]["fcomamt_iy"]),
                        fcomamt_p1 = Convert.ToDecimal(dv[i]["fcomamt_p1"]), fcomamt_p2 = Convert.ToDecimal(dv[i]["fcomamt_p2"]);

                dv[i]["fgpm_ia1"] = UtilityFunc.CompSecFig(fclass, ftotinstal, fgpm_ia, fgpm_p1, fgpm_p1, fgpm_p2);
                dv[i]["ftdamt_ia1"] = UtilityFunc.CompSecFig(fclass, ftotinstal, ftdamt_ia, ftdamt_p1, ftdamt_p1, ftdamt_p2);
                dv[i]["fcomamt_ia1"] = UtilityFunc.CompSecFig(fclass, ftotinstal, fcomamt_ia, fcomamt_p1, fcomamt_p1, fcomamt_p2);

                dv[i]["fgpm_ia2"] = fgpm_ia - UtilityFunc.CompSecFig(fclass, ftotinstal, fgpm_ia, fgpm_p1, fgpm_p1, fgpm_p2);
                dv[i]["ftdamt_ia2"] = ftdamt_ia - UtilityFunc.CompSecFig(fclass, ftotinstal, ftdamt_ia, ftdamt_p1, ftdamt_p1, ftdamt_p2);
                dv[i]["fcomamt_ia2"] = fcomamt_ia - UtilityFunc.CompSecFig(fclass, ftotinstal, fcomamt_ia, fcomamt_p1, fcomamt_p1, fcomamt_p2);

                dv[i]["fgpm_iy1"] = UtilityFunc.CompSecFig(fclass, ftotinstal, fgpm_iy, fgpm_p1, fgpm_p1, fgpm_p2);
                dv[i]["ftdamt_iy1"] = UtilityFunc.CompSecFig(fclass, ftotinstal, ftdamt_iy, ftdamt_p1, ftdamt_p1, ftdamt_p2);
                dv[i]["fcomamt_iy1"] = UtilityFunc.CompSecFig(fclass, ftotinstal, fcomamt_iy, fcomamt_p1, fcomamt_p1, fcomamt_p2);

                dv[i]["fgpm_iy2"] = fgpm_iy - UtilityFunc.CompSecFig(fclass, ftotinstal, fgpm_iy, fgpm_p1, fgpm_p1, fgpm_p2);
                dv[i]["ftdamt_iy2"] = ftdamt_iy - UtilityFunc.CompSecFig(fclass, ftotinstal, ftdamt_iy, ftdamt_p1, ftdamt_p1, ftdamt_p2);
                dv[i]["fcomamt_iy2"] = fcomamt_iy - UtilityFunc.CompSecFig(fclass, ftotinstal, fcomamt_iy, fcomamt_p1, fcomamt_p1, fcomamt_p2);
            }

            dt.Columns.Remove("finstall_a");
            dt.Columns.Remove("fbkdate_a");

            DataTable dtresult = CreateDirInfoStruc();

            foreach (DataRow dr in dt.Rows)
            {
                String fclass = dr["fclass"].ToString().Trim();

                Decimal fgpm_ia1 = Convert.ToDecimal(dr["fgpm_ia1"]),ftdamt_ia1 = Convert.ToDecimal(dr["ftdamt_ia1"]);
                Decimal fcomamt_ia1 = Convert.ToDecimal(dr["fcomamt_ia1"]);
                Decimal fgpm_ia2 = Convert.ToDecimal(dr["fgpm_ia2"]),ftdamt_ia2 = Convert.ToDecimal(dr["ftdamt_ia2"]);
                Decimal fcomamt_ia2 = Convert.ToDecimal(dr["fcomamt_ia2"]);

                Decimal fgpm_iy1 = Convert.ToDecimal(dr["fgpm_iy1"]), ftdamt_iy1 = Convert.ToDecimal(dr["ftdamt_iy1"]);
                Decimal fcomamt_iy1 = Convert.ToDecimal(dr["fcomamt_iy1"]);
                Decimal fgpm_iy2 = Convert.ToDecimal(dr["fgpm_iy2"]), ftdamt_iy2 = Convert.ToDecimal(dr["ftdamt_iy2"]);
                Decimal fcomamt_iy2 = Convert.ToDecimal(dr["fcomamt_iy2"]);

                Decimal fgpm_p1 = Convert.ToDecimal(dr["fgpm_p1"]),ftdamt_p1 = Convert.ToDecimal(dr["ftdamt_p1"]);
                Decimal fcomamt_p1 = Convert.ToDecimal(dr["fcomamt_p1"]);
                Decimal fgpm_p2 = Convert.ToDecimal(dr["fgpm_p2"]),ftdamt_p2 = Convert.ToDecimal(dr["ftdamt_p2"]);
                Decimal fcomamt_p2 = Convert.ToDecimal(dr["fcomamt_p2"]);

                if (fclass != "CAR")
                    AppendDirInfoRow(dtresult, dtpolhsrc, dtmprdr, dr, fclass, UtilityFunc.GetKindFrmClass(fclass), fgpm_ia1, ftdamt_ia1, fcomamt_ia1,
                        fgpm_iy1, ftdamt_iy1, fcomamt_iy1, fgpm_p1, ftdamt_p1, fcomamt_p1);

                if (fclass == "CAR")
                {
                    AppendDirInfoRow(dtresult, dtpolhsrc, dtmprdr, dr, fclass, "1", fgpm_ia1, ftdamt_ia1, fcomamt_ia1,
                                        fgpm_iy1, ftdamt_iy1, fcomamt_iy1, fgpm_p1, ftdamt_p1, fcomamt_p1);
                    AppendDirInfoRow(dtresult, dtpolhsrc, dtmprdr, dr, fclass, "2", fgpm_ia2, ftdamt_ia2, fcomamt_ia2,
                                        fgpm_iy2, ftdamt_iy2, fcomamt_iy2, fgpm_p2, ftdamt_p2, fcomamt_p2);
                }

            }

            return dtresult;
        }

        public static DataTable GenGrndRIInvh(DataTable dtinvhsrc, DataTable dtoriinstl, DataTable dtoriinvf, DataTable dtoriinvlf,
                                              DataTable dtpolh, DateTime vfbk_fr, DateTime vfbk_to, DateTime vlastasat)
        {
            DataTable dt = CreateRIPremStruc();
            DataView dvoriinstl = dtoriinstl.DefaultView, dvoriinvf = dtoriinvf.DefaultView, dvoriinvlf = dtoriinvlf.DefaultView,
                     dvinvhsrc = dtinvhsrc.DefaultView;

            dvoriinstl.RowFilter = String.Format("fritype <> '{0}' and fritype <> '{1}'", "04", "06");
            for (int i = 0; i < dvoriinstl.Count; i++)
            {
                String fctlid_i = dvoriinstl[i]["fctlid_i"].ToString().Trim(), fctlid_ri = dvoriinstl[i]["fctlid_1"].ToString().Trim();
                dvinvhsrc.RowFilter = String.Format("fctlid_i = '{0}'", fctlid_i);

                if (dvinvhsrc.Count > 0)
                    AppendRIPremRow(dt, dvinvhsrc[0].Row, dvoriinstl[i].Row, fctlid_ri);
            }

            dvoriinvf.RowFilter = String.Format("trim(fclass) <> '{0}' and trim(fclass) <> '{1}'", "CPM", "PAR");
            for (int i = 0; i < dvoriinvf.Count; i++)
            {
                String fctlid_i = dvoriinvf[i]["fctlid_i"].ToString().Trim(), fctlid_ri = dvoriinvf[i]["fctlid_1"].ToString().Trim();
                dvinvhsrc.RowFilter = String.Format("fctlid_i = '{0}'", fctlid_i);

                if (dvinvhsrc.Count > 0)
                    AppendRIPremRow(dt, dvinvhsrc[0].Row, dvoriinvf[i].Row, fctlid_ri);
            }

            for (int i = 0; i < dvoriinvlf.Count; i++)
            {
                String fctlid_i = dvoriinvlf[i]["fctlid_i"].ToString().Trim(), fctlid_ri = dvoriinvlf[i]["fctlid_ri"].ToString().Trim();
                dvinvhsrc.RowFilter = String.Format("fctlid_i = '{0}'", fctlid_i);

                if (dvinvhsrc.Count > 0)
                    AppendRIPremRow(dt, dvinvhsrc[0].Row, dvoriinvlf[i].Row, fctlid_ri);
            }

            DataTable dtsum = CreateRIPremStruc();

            foreach (DataRow dr in dt.Rows)
            {
                AppendGrndRIPremSum(dtsum, dr, vfbk_fr, vfbk_to, vlastasat);
            }

            dtsum.DefaultView.RowFilter = "";

            foreach (DataRow dr in dtsum.Rows)
            {
                if (String.IsNullOrEmpty(dr["finstall"].ToString()))
                {
                    dr["finstall"] = dr["finstall_a"];
                    dr["fbkdate"] = dr["fbkdate_a"];
                }
            }

            dtsum.Columns.Remove("finstall_a");
            dtsum.Columns.Remove("fbkdate_a");

            return dtsum;
        }

        private static void AppendDirInfoRow(DataTable dtresult, DataTable dtpolhsrc, DataTable dtmprdr,
                                          DataRow dr, String fclass, String fkind, 
                                          Decimal fgpm_ia, Decimal ftdamt_ia, Decimal fcomamt_ia,
                                          Decimal fgpm_iy, Decimal ftdamt_iy, Decimal fcomamt_iy,
                                          Decimal fgpm_p, Decimal ftdamt_p, Decimal fcomamt_p)
        {
            Boolean append_ok = false;

            if (!append_ok)
                append_ok = fgpm_ia != 0.00m;

            if (!append_ok)
                append_ok = ftdamt_ia != 0.00m;

            if (!append_ok)
                append_ok = fcomamt_ia != 0.00m;

            if (!append_ok)
                append_ok = fgpm_iy != 0.00m;

            if (!append_ok)
                append_ok = ftdamt_iy != 0.00m;

            if (!append_ok)
                append_ok = fcomamt_iy != 0.00m;

            if (!append_ok)
                return;

            DataRow dtrow = dtresult.NewRow();
            String fctlid_e = dr["fctlid_e"].ToString().Trim(), fbus = dr["fbus"].ToString().Trim(),
                   fclnt = dr["fclnt"].ToString().Trim();
            DataView dvpolhsrc = dtpolhsrc.DefaultView, dvmprdr = dtmprdr.DefaultView;

            dvpolhsrc.RowFilter = String.Format("fctlid = '{0}'", fctlid_e);
            dvmprdr.RowFilter = String.Format("ftype = '{0}' and fid = '{1}'", "DK".Contains(fbus) ? "C" :"R", fclnt);

            String fsclass = dvpolhsrc[0]["fsclass"].ToString().Trim();

            if (fclass == "CAR" && fkind == "1")
                fsclass = "SEC1";

            if (fclass == "CAR" && fkind == "2")
                fsclass = "SEC2";

            dtrow["fctlid_p"] = dvpolhsrc[0]["fctlid_p"];
            dtrow["fctlid_e"] = fctlid_e;
            dtrow["fctlid_v"] = dvpolhsrc[0]["fctlid_v"];
            dtrow["fbus"] = dr["fbus"];
            dtrow["fclnt"] = dr["fclnt"];
            dtrow["fsclnt"] = dr["fsclnt"];
            dtrow["fdbtr"] = dr["fdbtr"];
            dtrow["fkind"] = fkind;
            dtrow["fclsseq"] = UtilityFunc.ClsSeq(fclass, fsclass);
            dtrow["fclass"] = fclass;
            dtrow["fsclass"] = dvpolhsrc[0]["fsclass"];
            dtrow["ftrade"] = dvpolhsrc[0]["ftrade"];
            
            if (fclass == "CAR")
                dtrow["fsec"] = fkind;
            else
                dtrow["fsec"] = "";

            dtrow["finstall"] = dr["finstall"];
            dtrow["fbkdate"] = dr["fbkdate"];
            dtrow["ftotinstal"] = dr["ftotinstal"];
            dtrow["fpolno"] = dvpolhsrc[0]["fpolno"];
            dtrow["fendtno"] = dvpolhsrc[0]["fendtno"];
            dtrow["fcomtype"] = dvpolhsrc[0]["fcomtype"];
            dtrow["fuwyr"] = dvpolhsrc[0]["fuwyr"];
            dtrow["finsd"] = dvpolhsrc[0]["finsd"];
            dtrow["fsite"] = dvpolhsrc[0]["fsite"];
            dtrow["fefffr"] = dvpolhsrc[0]["fefffr"];
            dtrow["feffto"] = dvpolhsrc[0]["feffto"];

            dtrow["fgpm_p"] = fgpm_p;
            dtrow["ftdamt_p"] = ftdamt_p;
            dtrow["fcomamt_p"] = fcomamt_p;

            dtrow["fgpm_di"] = fgpm_iy - fgpm_ia;
            dtrow["ftdamt_di"] = ftdamt_iy -ftdamt_ia;
            dtrow["fcomamt_di"] = fcomamt_iy - fcomamt_ia;
            
            dtrow["fgpm_ia"] = fgpm_ia;
            dtrow["ftdamt_ia"] = ftdamt_ia;
            dtrow["fcomamt_ia"] = fcomamt_ia;

            dtrow["fgpm_iy"] = fgpm_iy;
            dtrow["ftdamt_iy"] = ftdamt_iy;
            dtrow["fcomamt_iy"] = fcomamt_iy;

            dtrow["fcdesc"] = dvmprdr[0]["fdesc"];

            dtresult.Rows.Add(dtrow);

            return;
        }
        
        private static void AppendRIPremRow(DataTable dtresult, DataRow dr_invhsrc, DataRow dr_riinv, String fctlid_ri)
        {
            DataRow dtrow = dtresult.NewRow();
            String fgrnet = dr_riinv["fgrnet"].ToString().Trim();

            dtrow["fctlid_e"] = dr_invhsrc["fctlid_e"];
            dtrow["fctlid_i"] = dr_invhsrc["fctlid_i"];
            dtrow["fbus"] = dr_invhsrc["fbus"];
            dtrow["fsclnt"] = dr_invhsrc["fsclnt"];
            dtrow["fdbtr"] = dr_invhsrc["fdbtr"];
            dtrow["fclass"] = dr_invhsrc["fclass"];
            dtrow["fbkdate"] = dr_invhsrc["fbkdate"];
            dtrow["fefffr"] = dr_invhsrc["fefffr"];
            dtrow["fctlid_ri"] = fctlid_ri;
            dtrow["fridate"] = dr_riinv["fbkdate"];
            dtrow["fkind"] = dr_riinv["fkind"];
            dtrow["fritype"] = dr_riinv["fritype"];
            dtrow["finstall"] = dr_riinv["finstall"];
            dtrow["ftotinstal"] = dr_riinv["ftotinstal"];
            dtrow["fripm"] = -Convert.ToDecimal(dr_riinv["fgpm"]);

            if (fgrnet == "2")
                dtrow["fricom"] = -Convert.ToDecimal(dr_riinv["fgpm"]) + Convert.ToDecimal(dr_riinv["fnpm"]) - Convert.ToDecimal(dr_riinv["fcomamt"]);
            else
                dtrow["fricom"] = -Convert.ToDecimal(dr_riinv["fcomamt"]);

            dtresult.Rows.Add(dtrow);

            return;
        }
        
        public static DataTable CreateDirPremStruc()
        {
            DataTable dt = new DataTable();

            dt.Columns.Add("fctlid_e", typeof(String));
            dt.Columns.Add("finstall", typeof(Int32));
            dt.Columns.Add("fbkdate", typeof(DateTime));
            dt.Columns.Add("finstall_a", typeof(Int32));
            dt.Columns.Add("fbkdate_a", typeof(DateTime));
            dt.Columns.Add("ftotinstal", typeof(Int32));
            dt.Columns.Add("fbus", typeof(String));
            dt.Columns.Add("fclnt", typeof(String));
            dt.Columns.Add("fsclnt", typeof(String));
            dt.Columns.Add("fdbtr", typeof(String));
            dt.Columns.Add("fclass", typeof(String));
            dt.Columns.Add("fcomtype", typeof(String));
            dt.Columns.Add("fgpm_p1", typeof(Decimal));
            dt.Columns.Add("ftdamt_p1", typeof(Decimal));
            dt.Columns.Add("fcomamt_p1", typeof(Decimal));
            dt.Columns.Add("fgpm_p2", typeof(Decimal));
            dt.Columns.Add("ftdamt_p2", typeof(Decimal));
            dt.Columns.Add("fcomamt_p2", typeof(Decimal));

            DataColumn agpmCol = dt.Columns.Add("fgpm_ia", typeof(Decimal)),
                       atdamtCol = dt.Columns.Add("ftdamt_ia", typeof(Decimal)),
                       acomamtCol = dt.Columns.Add("fcomamt_ia", typeof(Decimal));

            agpmCol.DefaultValue = 0.00m;
            atdamtCol.DefaultValue = 0.00m;
            acomamtCol.DefaultValue = 0.00m;

            DataColumn ygpmCol = dt.Columns.Add("fgpm_iy", typeof(Decimal)),
                       ytdamtCol = dt.Columns.Add("ftdamt_iy", typeof(Decimal)),
                       ycomamtCol = dt.Columns.Add("fcomamt_iy", typeof(Decimal));

            ygpmCol.DefaultValue = 0.00m;
            ytdamtCol.DefaultValue = 0.00m;
            ycomamtCol.DefaultValue = 0.00m;
            
            dt.Columns.Add("fgpm_ia1", typeof(Decimal));
            dt.Columns.Add("ftdamt_ia1", typeof(Decimal));
            dt.Columns.Add("fcomamt_ia1", typeof(Decimal));
            dt.Columns.Add("fgpm_ia2", typeof(Decimal));
            dt.Columns.Add("ftdamt_ia2", typeof(Decimal));
            dt.Columns.Add("fcomamt_ia2", typeof(Decimal));

            dt.Columns.Add("fgpm_iy1", typeof(Decimal));
            dt.Columns.Add("ftdamt_iy1", typeof(Decimal));
            dt.Columns.Add("fcomamt_iy1", typeof(Decimal));
            dt.Columns.Add("fgpm_iy2", typeof(Decimal));
            dt.Columns.Add("ftdamt_iy2", typeof(Decimal));
            dt.Columns.Add("fcomamt_iy2", typeof(Decimal));

            return dt;
        }


        public static DataTable CreateDirInfoStruc()
        {
            DataTable dt = new DataTable();

            dt.Columns.Add("fctlid_p", typeof(String));
            dt.Columns.Add("fctlid_e", typeof(String));
            dt.Columns.Add("fctlid_v", typeof(String));
            dt.Columns.Add("fbus", typeof(String));
            dt.Columns.Add("fclnt", typeof(String));
            dt.Columns.Add("fsclnt", typeof(String));
            dt.Columns.Add("fdbtr", typeof(String));
            dt.Columns.Add("fkind", typeof(String));
            dt.Columns.Add("fclsseq", typeof(String));
            dt.Columns.Add("fclass", typeof(String));
            dt.Columns.Add("fsclass", typeof(String));
            dt.Columns.Add("ftrade", typeof(String));
            dt.Columns.Add("fsec", typeof(String));
            dt.Columns.Add("finstall", typeof(Int32));
            dt.Columns.Add("fbkdate", typeof(DateTime));
            dt.Columns.Add("ftotinstal", typeof(Int32));
            dt.Columns.Add("fpolno", typeof(String));
            dt.Columns.Add("fendtno", typeof(String));
            dt.Columns.Add("fcomtype", typeof(String));
            dt.Columns.Add("fuwyr", typeof(String));
            dt.Columns.Add("finsd", typeof(String));
            dt.Columns.Add("fsite", typeof(String));
            dt.Columns.Add("fefffr", typeof(DateTime));
            dt.Columns.Add("feffto", typeof(DateTime));
            dt.Columns.Add("fgpm_p", typeof(Decimal));
            dt.Columns.Add("ftdamt_p", typeof(Decimal));
            dt.Columns.Add("fcomamt_p", typeof(Decimal));
            dt.Columns.Add("fgpm_di", typeof(Decimal));
            dt.Columns.Add("ftdamt_di", typeof(Decimal));
            dt.Columns.Add("fcomamt_di", typeof(Decimal));
            dt.Columns.Add("fgpm_ia", typeof(Decimal));
            dt.Columns.Add("ftdamt_ia", typeof(Decimal));
            dt.Columns.Add("fcomamt_ia", typeof(Decimal));

            dt.Columns.Add("fgpm_iy", typeof(Decimal));
            dt.Columns.Add("ftdamt_iy", typeof(Decimal));
            dt.Columns.Add("fcomamt_iy", typeof(Decimal));

            dt.Columns.Add("fincfr_a", typeof(DateTime));
            dt.Columns.Add("fincto_a", typeof(DateTime));
            dt.Columns.Add("fincfr_y", typeof(DateTime));
            dt.Columns.Add("fincto_y", typeof(DateTime));

            dt.Columns.Add("frtnpm_ri", typeof(Decimal));
            dt.Columns.Add("fttypm_ri", typeof(Decimal));
            dt.Columns.Add("fxolpm_ri", typeof(Decimal));
            dt.Columns.Add("ffacpm_ri", typeof(Decimal));
            dt.Columns.Add("ffobpm_ri", typeof(Decimal));
            dt.Columns.Add("frtncom_ri", typeof(Decimal));
            dt.Columns.Add("fttycom_ri", typeof(Decimal));
            dt.Columns.Add("ffaccom_ri", typeof(Decimal));
            dt.Columns.Add("ffobcom_ri", typeof(Decimal));
            dt.Columns.Add("frtnpm_ra", typeof(Decimal));
            dt.Columns.Add("fttypm_ra", typeof(Decimal));
            dt.Columns.Add("fxolpm_ra", typeof(Decimal));
            dt.Columns.Add("ffacpm_ra", typeof(Decimal));
            dt.Columns.Add("ffobpm_ra", typeof(Decimal));
            dt.Columns.Add("frtncom_ra", typeof(Decimal));
            dt.Columns.Add("fttycom_ra", typeof(Decimal));
            dt.Columns.Add("ffaccom_ra", typeof(Decimal));
            dt.Columns.Add("ffobcom_ra", typeof(Decimal));

            dt.Columns.Add("frtnpm_ry", typeof(Decimal));
            dt.Columns.Add("fttypm_ry", typeof(Decimal));
            dt.Columns.Add("fxolpm_ry", typeof(Decimal));
            dt.Columns.Add("ffacpm_ry", typeof(Decimal));
            dt.Columns.Add("ffobpm_ry", typeof(Decimal));
            dt.Columns.Add("frtncom_ry", typeof(Decimal));
            dt.Columns.Add("fttycom_ry", typeof(Decimal));
            dt.Columns.Add("ffaccom_ry", typeof(Decimal));
            dt.Columns.Add("ffobcom_ry", typeof(Decimal));

            dt.Columns.Add("fthisasat", typeof(DateTime));
            dt.Columns.Add("flastasat", typeof(DateTime));

            dt.Columns.Add("fpolday_y", typeof(Int32));
            dt.Columns.Add("fexpday_y", typeof(Int32));
            dt.Columns.Add("fpolday_a", typeof(Int32));
            dt.Columns.Add("fexpday_a", typeof(Int32));

            dt.Columns.Add("fricost_di", typeof(Decimal));
            dt.Columns.Add("fricost_ia", typeof(Decimal));
            dt.Columns.Add("fricost_iy", typeof(Decimal));

            dt.Columns.Add("fripm_ri", typeof(Decimal));
            dt.Columns.Add("fxripm_ri", typeof(Decimal));
            dt.Columns.Add("fricom_ri", typeof(Decimal));

            dt.Columns.Add("fripm_ra", typeof(Decimal));
            dt.Columns.Add("fxripm_ra", typeof(Decimal));
            dt.Columns.Add("fricom_ra", typeof(Decimal));

            dt.Columns.Add("fripm_ry", typeof(Decimal));
            dt.Columns.Add("fxripm_ry", typeof(Decimal));
            dt.Columns.Add("fricom_ry", typeof(Decimal));

            dt.Columns.Add("funexp_a", typeof(Int32));
            dt.Columns.Add("funexp_y", typeof(Int32));
            dt.Columns.Add("finsratio", typeof(Decimal));

            dt.Columns.Add("gernpm_y", typeof(Decimal));
            dt.Columns.Add("gernpm_a", typeof(Decimal));
            dt.Columns.Add("rernpm_y", typeof(Decimal));
            dt.Columns.Add("rernpm_a", typeof(Decimal));
            dt.Columns.Add("fernpm_y", typeof(Decimal));
            dt.Columns.Add("fernpm_a", typeof(Decimal));
            dt.Columns.Add("fexpcom_y", typeof(Decimal));
            dt.Columns.Add("fexpcom_a", typeof(Decimal));
            dt.Columns.Add("fexpcom_ry", typeof(Decimal));
            dt.Columns.Add("fexpcom_ra", typeof(Decimal));

            dt.Columns.Add("fnetpm_i", typeof(Decimal));
            dt.Columns.Add("fnetpm_y", typeof(Decimal));
            dt.Columns.Add("fnetpm_a", typeof(Decimal));
            dt.Columns.Add("fnetcom_i", typeof(Decimal));
            dt.Columns.Add("fnetcom_y", typeof(Decimal));
            dt.Columns.Add("fnetcom_a", typeof(Decimal));

            dt.Columns.Add("fclsname", typeof(String));
            dt.Columns.Add("ftrdseq", typeof(String));
            dt.Columns.Add("acc_class", typeof(String));
            dt.Columns.Add("acc_desc", typeof(String));
            dt.Columns.Add("ftrdname", typeof(String));
            dt.Columns.Add("pcode", typeof(String));
            dt.Columns.Add("sec_desc", typeof(String));

            dt.Columns.Add("fgpm_y", typeof(Decimal));
            dt.Columns.Add("fgpm_a", typeof(Decimal));
            dt.Columns.Add("fripm_y", typeof(Decimal));
            dt.Columns.Add("fripm_a", typeof(Decimal));
            dt.Columns.Add("fcomamt_y", typeof(Decimal));
            dt.Columns.Add("fcomamt_a", typeof(Decimal));
            dt.Columns.Add("fricom_y", typeof(Decimal));
            dt.Columns.Add("fricom_a", typeof(Decimal));

            dt.Columns.Add("gernpm_fy", typeof(Decimal));
            dt.Columns.Add("gernpm_fa", typeof(Decimal));
            dt.Columns.Add("rernpm_fy", typeof(Decimal));
            dt.Columns.Add("rernpm_fa", typeof(Decimal));
            dt.Columns.Add("fernpm_fy", typeof(Decimal));
            dt.Columns.Add("fernpm_fa", typeof(Decimal));
            dt.Columns.Add("fexpcom_fy", typeof(Decimal));
            dt.Columns.Add("fexpcom_fa", typeof(Decimal));
            dt.Columns.Add("ericom_fy", typeof(Decimal));
            dt.Columns.Add("ericom_fa", typeof(Decimal));

            dt.Columns.Add("funernpm", typeof(Decimal));
            dt.Columns.Add("gernpm", typeof(Decimal));
            dt.Columns.Add("rernpm", typeof(Decimal));
            dt.Columns.Add("fernpm", typeof(Decimal));
            dt.Columns.Add("fadjcom", typeof(Decimal));
            dt.Columns.Add("fadjricom", typeof(Decimal));
            dt.Columns.Add("fdac", typeof(Decimal));
            dt.Columns.Add("fdricom", typeof(Decimal));
            dt.Columns.Add("fdacqcost", typeof(Decimal));
            dt.Columns.Add("fnetcompay", typeof(Decimal));
            dt.Columns.Add("funnpm_fy", typeof(Decimal));
            dt.Columns.Add("funnpm_fa", typeof(Decimal));

            dt.Columns.Add("fcdesc", typeof(String));

            DataColumn newCol = dt.Columns.Add("fnew", typeof(String));
                
            newCol.DefaultValue = "N";

            return dt;
        }

        public static DataTable CreateRIPremStruc()
        {
            DataTable dt = new DataTable();

            dt.Columns.Add("fctlid_e", typeof(String));
            dt.Columns.Add("fctlid_i", typeof(String));
            dt.Columns.Add("fbus", typeof(String));
            dt.Columns.Add("fsclnt", typeof(String));
            dt.Columns.Add("fdbtr", typeof(String));
            dt.Columns.Add("fclass", typeof(String));
            dt.Columns.Add("fbkdate", typeof(DateTime));
            dt.Columns.Add("fefffr", typeof(DateTime));
            dt.Columns.Add("fctlid_ri", typeof(String));
            dt.Columns.Add("fridate", typeof(DateTime));
            dt.Columns.Add("fkind", typeof(String));
            dt.Columns.Add("fritype", typeof(String));
            dt.Columns.Add("finstall", typeof(Int32));
            dt.Columns.Add("ftotinstal", typeof(Int32));
            dt.Columns.Add("fripm", typeof(Decimal));
            dt.Columns.Add("fricom", typeof(Decimal));
            dt.Columns.Add("fbkdate_a", typeof(DateTime));
            dt.Columns.Add("finstall_a", typeof(Int32));

            DataColumn rtnpmCol = dt.Columns.Add("frtnpm_ri", typeof(Decimal)),
                       ttypmCol = dt.Columns.Add("fttypm_ri", typeof(Decimal)),
                       xolpmCol = dt.Columns.Add("fxolpm_ri", typeof(Decimal)),
                       facpmCol = dt.Columns.Add("ffacpm_ri", typeof(Decimal)),
                       fobpmCol = dt.Columns.Add("ffobpm_ri", typeof(Decimal)),
                       rtncomCol = dt.Columns.Add("frtncom_ri", typeof(Decimal)),
                       ttycomCol = dt.Columns.Add("fttycom_ri", typeof(Decimal)),
                       faccomCol = dt.Columns.Add("ffaccom_ri", typeof(Decimal)),
                       fobcomCol = dt.Columns.Add("ffobcom_ri", typeof(Decimal));

            rtnpmCol.DefaultValue = 0.00m;
            ttypmCol.DefaultValue = 0.00m;
            xolpmCol.DefaultValue = 0.00m;
            facpmCol.DefaultValue = 0.00m;
            fobpmCol.DefaultValue = 0.00m;

            rtncomCol.DefaultValue = 0.00m;
            ttycomCol.DefaultValue = 0.00m;
            faccomCol.DefaultValue = 0.00m;
            fobcomCol.DefaultValue = 0.00m;

            DataColumn artnpmCol = dt.Columns.Add("frtnpm_ra", typeof(Decimal)),
                       attypmCol = dt.Columns.Add("fttypm_ra", typeof(Decimal)),
                       axolpmCol = dt.Columns.Add("fxolpm_ra", typeof(Decimal)),
                       afacpmCol = dt.Columns.Add("ffacpm_ra", typeof(Decimal)),
                       afobpmCol = dt.Columns.Add("ffobpm_ra", typeof(Decimal)),
                       artncomCol = dt.Columns.Add("frtncom_ra", typeof(Decimal)),
                       attycomCol = dt.Columns.Add("fttycom_ra", typeof(Decimal)),
                       afaccomCol = dt.Columns.Add("ffaccom_ra", typeof(Decimal)),
                       afobcomCol = dt.Columns.Add("ffobcom_ra", typeof(Decimal));

            artnpmCol.DefaultValue = 0.00m;
            attypmCol.DefaultValue = 0.00m;
            axolpmCol.DefaultValue = 0.00m;
            afacpmCol.DefaultValue = 0.00m;
            afobpmCol.DefaultValue = 0.00m;

            artncomCol.DefaultValue = 0.00m;
            attycomCol.DefaultValue = 0.00m;
            afaccomCol.DefaultValue = 0.00m;
            afobcomCol.DefaultValue = 0.00m;

            return dt;
        }

        private static void AppendGrndRIPremSum(DataTable dttarget, DataRow drsrc, DateTime vfbk_fr, DateTime vfbk_to, DateTime vlastasat)
        {
            String fritype = drsrc["fritype"].ToString().Trim(), prmstr = "", comstr = "", suffix = "";
            Boolean withinperiod = false, beforelast = false;

            DateTime? fbkdate , fridate;

            if (String.IsNullOrEmpty(drsrc["fbkdate"].ToString()))
                fbkdate = null;
            else
                fbkdate = Convert.ToDateTime(drsrc["fbkdate"]);

            if (String.IsNullOrEmpty(drsrc["fridate"].ToString()))
                fridate = null;
            else
                fridate = Convert.ToDateTime(drsrc["fridate"]);

            if (fbkdate >= vfbk_fr && fbkdate <= vfbk_to && fridate == null)
                withinperiod = true;
            else
                if (fridate >= vfbk_fr && fridate <= vfbk_to)
                    withinperiod = true;

            if (fbkdate <= vlastasat && fridate == null)
                beforelast = true;
            else
                if (fridate <= vlastasat)
                    beforelast = true;

            if (!withinperiod && !beforelast)
                return;

            if (withinperiod)
                suffix = "ri";

            if (beforelast)
                suffix = "ra";

            if (fritype == "01")
            {
                prmstr = "frtnpm_";
                comstr = "frtncom_";
            }

            if (fritype == "02")
            {
                prmstr = "fttypm_";
                comstr = "fttycom_";
            }

            if (fritype == "03")
            {
                prmstr = "fxolpm_";
                comstr = "";
            }

            if ("04|06".Contains(fritype))
            {
                prmstr = "ffacpm_";
                comstr = "ffaccom_";
            }

            if (fritype == "05")
            {
                prmstr = "ffobpm_";
                comstr = "ffobcom_";
            }

            if (prmstr == "")
                return;

            String fctlid_e = drsrc["fctlid_e"].ToString().Trim(), fkind = drsrc["fkind"].ToString().Trim();
            DataView dvtarget = dttarget.DefaultView;

            dvtarget.RowFilter = String.Format("fctlid_e = '{0}' and fkind = '{1}'", fctlid_e, fkind);

            if (dvtarget.Count == 0)
            {
                DataRow dtrow = dttarget.NewRow();

                dtrow["fctlid_e"] = fctlid_e;
                dtrow["fkind"] = fkind;
                dtrow["fbus"] = drsrc["fbus"];
                dtrow["fsclnt"] = drsrc["fsclnt"];
                dtrow["fdbtr"] = drsrc["fdbtr"];
                dtrow["fclass"] = drsrc["fclass"];
                dtrow["ftotinstal"] = drsrc["ftotinstal"];

                dttarget.Rows.Add(dtrow);
            }

            if (withinperiod)
            {
                if (String.IsNullOrEmpty(dvtarget[0]["fbkdate"].ToString()))
                    dvtarget[0]["fbkdate"] = fbkdate;
                else if(fbkdate < Convert.ToDateTime(dvtarget[0]["fbkdate"]))
                    dvtarget[0]["fbkdate"] = fbkdate;

                if (String.IsNullOrEmpty(dvtarget[0]["finstall"].ToString()))
                    dvtarget[0]["finstall"] = drsrc["finstall"];
                else if ( Convert.ToInt32(drsrc["finstall"]) > Convert.ToInt32(dvtarget[0]["finstall"]))
                    dvtarget[0]["finstall"] = drsrc["finstall"];
            }

            if (beforelast)
            {
                if (String.IsNullOrEmpty(dvtarget[0]["fbkdate_a"].ToString()))
                    dvtarget[0]["fbkdate_a"] = fbkdate;
                else if (fbkdate < Convert.ToDateTime(dvtarget[0]["fbkdate_a"]))
                    dvtarget[0]["fbkdate_a"] = fbkdate;

                if (String.IsNullOrEmpty(dvtarget[0]["finstall_a"].ToString()))
                    dvtarget[0]["finstall_a"] = drsrc["finstall"];
                else if (Convert.ToInt32(drsrc["finstall"]) > Convert.ToInt32(dvtarget[0]["finstall_a"]))
                    dvtarget[0]["finstall_a"] = drsrc["finstall"];
            }


            dvtarget[0][prmstr + suffix] = Convert.ToDecimal(dvtarget[0][prmstr + suffix]) + Convert.ToDecimal(drsrc["fripm"]);

            if (comstr == "")
                return;

            dvtarget[0][comstr + suffix] = Convert.ToDecimal(dvtarget[0][comstr + suffix]) + Convert.ToDecimal(drsrc["fricom"]);

            return;
        }
    
        public static void MergeDirRIInfo(DataTable dtref, DataTable dttarget, DataTable dtpolh, DataTable dtmotor,
                                          DataTable dtnewbus, DateTime vfbk_fr, DateTime vfbk_to, DateTime vlastasat)
        {
            DataView dvtarget = dttarget.DefaultView, dvpolh = dtpolh.DefaultView, dvmotor = dtmotor.DefaultView,
                     dvnewbus = dtnewbus.DefaultView ;
            DateTime dt20061231 = DateTime.ParseExact("2006.12.31", "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture),
                     dt20081130 = DateTime.ParseExact("2008.11.30", "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture),
                     dt20121231 = DateTime.ParseExact("2012.12.31", "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture),
                     dt20130101 = DateTime.ParseExact("2013.01.01", "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture),
                     dt20130131 = DateTime.ParseExact("2013.01.31", "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);

            // filter = "fextend = '3' and vlastasat  > g_refdate2 (dt20081130) and fbkdate < vfbk_fr" (apolh)
            // filter = "fextend = '3' and vfbk_to > g_refdate2 (dt20081130) and fbkdate < vfbk_to" (ypolh)

            foreach (DataRow dr in dtref.Rows)
            {
                String fctlid_e = dr["fctlid_e"].ToString().Trim(), fkind = dr["fkind"].ToString().Trim();
                dvtarget.RowFilter = String.Format("fctlid_e = '{0}' and fkind = '{1}'", fctlid_e, fkind);

                if (dvtarget.Count == 0)
                    continue;

                String fctlid_p = dvtarget[0]["fctlid_p"].ToString().Trim(), fctlid_v = dvtarget[0]["fctlid_v"].ToString().Trim();

                dvtarget[0]["frtnpm_ri"] = dr["frtnpm_ri"];
                dvtarget[0]["fttypm_ri"] = dr["fttypm_ri"];
                dvtarget[0]["fxolpm_ri"] = dr["fxolpm_ri"];
                dvtarget[0]["ffacpm_ri"] = dr["ffacpm_ri"];
                dvtarget[0]["ffobpm_ri"] = dr["ffobpm_ri"];

                dvtarget[0]["frtncom_ri"] = dr["frtncom_ri"];
                dvtarget[0]["fttycom_ri"] = dr["fttycom_ri"];
                dvtarget[0]["ffaccom_ri"] = dr["ffaccom_ri"];
                dvtarget[0]["ffobcom_ri"] = dr["ffobcom_ri"];

                dvtarget[0]["frtnpm_ra"] = dr["frtnpm_ra"];
                dvtarget[0]["fttypm_ra"] = dr["fttypm_ra"];
                dvtarget[0]["fxolpm_ra"] = dr["fxolpm_ra"];
                dvtarget[0]["ffacpm_ra"] = dr["ffacpm_ra"];
                dvtarget[0]["ffobpm_ra"] = dr["ffobpm_ra"];

                dvtarget[0]["frtncom_ra"] = dr["frtncom_ra"];
                dvtarget[0]["fttycom_ra"] = dr["fttycom_ra"];
                dvtarget[0]["ffaccom_ra"] = dr["ffaccom_ra"];
                dvtarget[0]["ffobcom_ra"] = dr["ffobcom_ra"];
            }

            if (vlastasat > dt20081130)
            {
                dvpolh.RowFilter = String.Format("fextend = '3' and fbkdate < {0}", csCommon.DbDateFormat(vfbk_fr));

                for (int i = 0; i < dvpolh.Count; i++)
                {
                    UpdInceptionPeriod_1(dvpolh[i], dvtarget, "fincfr_a", "fincto_a","fpolday_a", "fexpday_a",dt20121231,dt20130101,dt20130131,vfbk_fr);
                }
            }

            if (vfbk_to > dt20081130)
            {
                dvpolh.RowFilter = String.Format("fextend = '3' and fbkdate < {0}", csCommon.DbDateFormat(vfbk_to));

                for (int i = 0; i < dvpolh.Count; i++)
                {
                    UpdInceptionPeriod_1(dvpolh[i], dvtarget, "fincfr_y", "fincto_y", "fpolday_y", "fexpday_y", dt20121231, dt20130101, dt20130131, vfbk_to);
                }
            }

            //dvtarget.RowFilter = "fpolno = 'CMP-12-0039'";
            dvtarget.RowFilter = "";


            for (int i = 0; i < dvtarget.Count; i++)
            {
                String fctlid_e = dvtarget[i]["fctlid_e"].ToString().Trim();
                dvmotor.RowFilter = String.Format("fctlid_1 = '{0}'", fctlid_e);

                if (dvmotor.Count != 0)
                    dvtarget[i]["fkind"] = dvmotor[0]["fkind"];

                if (String.IsNullOrEmpty(dvtarget[i]["fincfr_a"].ToString()) && 
                     String.IsNullOrEmpty(dvtarget[i]["fpolday_a"].ToString()))
                    dvtarget[i]["fincfr_a"] = dvtarget[i]["fefffr"];
                if (String.IsNullOrEmpty(dvtarget[i]["fincto_a"].ToString()) &&
                     String.IsNullOrEmpty(dvtarget[i]["fpolday_a"].ToString()))
                    dvtarget[i]["fincto_a"] = dvtarget[i]["feffto"];
                if (String.IsNullOrEmpty(dvtarget[i]["fincfr_y"].ToString()) &&
                     String.IsNullOrEmpty(dvtarget[i]["fpolday_y"].ToString()))
                    dvtarget[i]["fincfr_y"] = dvtarget[i]["fefffr"];
                if (String.IsNullOrEmpty(dvtarget[i]["fincto_y"].ToString()) &&
                     String.IsNullOrEmpty(dvtarget[i]["fpolday_y"].ToString()))
                    dvtarget[i]["fincto_y"] = dvtarget[i]["feffto"];

                dvtarget[i]["fthisasat"] = vfbk_to;
                dvtarget[i]["flastasat"] = vlastasat;

                if (String.IsNullOrEmpty(dvtarget[i]["fincto_a"].ToString()))
                {
                    dvtarget[i]["fpolday_a"] = 1;
                    dvtarget[i]["fexpday_a"] = 1;
                }
                else
                {
                    dvtarget[i]["fpolday_a"] = (Convert.ToDateTime(dvtarget[i]["fincto_a"]) - 
                                                Convert.ToDateTime(dvtarget[i]["fincfr_a"])).Days+1;
                    if(Convert.ToDateTime(dvtarget[i]["fincto_a"]) > vlastasat)
                        dvtarget[i]["fexpday_a"] = (vlastasat - Convert.ToDateTime(dvtarget[i]["fincfr_a"])).Days+1;
                    else
                        dvtarget[i]["fexpday_a"] = dvtarget[i]["fpolday_a"];
                }
                    
                if (String.IsNullOrEmpty(dvtarget[i]["fincto_y"].ToString()))
                {
                    dvtarget[i]["fpolday_y"] = 1;
                    dvtarget[i]["fexpday_y"] = 1;
                }
                else
                {
                    dvtarget[i]["fpolday_y"] = (Convert.ToDateTime(dvtarget[i]["fincto_y"]) - 
                                                Convert.ToDateTime(dvtarget[i]["fincfr_y"])).Days + 1;
                    if (Convert.ToDateTime(dvtarget[i]["fincto_y"]) > vfbk_to)
                        dvtarget[i]["fexpday_y"] = (vfbk_to - Convert.ToDateTime(dvtarget[i]["fincfr_y"])).Days + 1;
                    else
                        dvtarget[i]["fexpday_y"] = dvtarget[i]["fpolday_y"];
                }

                if (Convert.ToInt32(dvtarget[i]["fexpday_a"]) < 0)
                    dvtarget[i]["fexpday_a"] = 0;

                if (Convert.ToInt32(dvtarget[i]["fexpday_y"]) < 0)
                    dvtarget[i]["fexpday_y"] = 0;

                dvtarget[i]["frtnpm_ri"] = dvtarget[i]["frtnpm_ri"].Equals(DBNull.Value) ? 0 : Convert.ToDecimal(dvtarget[i]["frtnpm_ri"]);
                dvtarget[i]["fttypm_ri"] = dvtarget[i]["fttypm_ri"].Equals(DBNull.Value) ? 0 : Convert.ToDecimal(dvtarget[i]["fttypm_ri"]);
                dvtarget[i]["fxolpm_ri"] = dvtarget[i]["fxolpm_ri"].Equals(DBNull.Value) ? 0 : Convert.ToDecimal(dvtarget[i]["fxolpm_ri"]);
                dvtarget[i]["ffacpm_ri"] = dvtarget[i]["ffacpm_ri"].Equals(DBNull.Value) ? 0 : Convert.ToDecimal(dvtarget[i]["ffacpm_ri"]);
                dvtarget[i]["ffobpm_ri"] = dvtarget[i]["ffobpm_ri"].Equals(DBNull.Value) ? 0 : Convert.ToDecimal(dvtarget[i]["ffobpm_ri"]);

                dvtarget[i]["frtncom_ri"] = dvtarget[i]["frtncom_ri"].Equals(DBNull.Value) ? 0 : Convert.ToDecimal(dvtarget[i]["frtncom_ri"]);
                dvtarget[i]["fttycom_ri"] = dvtarget[i]["fttycom_ri"].Equals(DBNull.Value) ? 0 : Convert.ToDecimal(dvtarget[i]["fttycom_ri"]);
                dvtarget[i]["ffaccom_ri"] = dvtarget[i]["ffaccom_ri"].Equals(DBNull.Value) ? 0 : Convert.ToDecimal(dvtarget[i]["ffaccom_ri"]);
                dvtarget[i]["ffobcom_ri"] = dvtarget[i]["ffobcom_ri"].Equals(DBNull.Value) ? 0 : Convert.ToDecimal(dvtarget[i]["ffobcom_ri"]);

                dvtarget[i]["frtnpm_ra"] = dvtarget[i]["frtnpm_ra"].Equals(DBNull.Value) ? 0 : Convert.ToDecimal(dvtarget[i]["frtnpm_ra"]);
                dvtarget[i]["fttypm_ra"] = dvtarget[i]["fttypm_ra"].Equals(DBNull.Value) ? 0 : Convert.ToDecimal(dvtarget[i]["fttypm_ra"]);
                dvtarget[i]["fxolpm_ra"] = dvtarget[i]["fxolpm_ra"].Equals(DBNull.Value) ? 0 : Convert.ToDecimal(dvtarget[i]["fxolpm_ra"]);
                dvtarget[i]["ffacpm_ra"] = dvtarget[i]["ffacpm_ra"].Equals(DBNull.Value) ? 0 : Convert.ToDecimal(dvtarget[i]["ffacpm_ra"]);
                dvtarget[i]["ffobpm_ra"] = dvtarget[i]["ffobpm_ra"].Equals(DBNull.Value) ? 0 : Convert.ToDecimal(dvtarget[i]["ffobpm_ra"]);

                dvtarget[i]["frtncom_ra"] = dvtarget[i]["frtncom_ra"].Equals(DBNull.Value) ? 0 : Convert.ToDecimal(dvtarget[i]["frtncom_ra"]);
                dvtarget[i]["fttycom_ra"] = dvtarget[i]["fttycom_ra"].Equals(DBNull.Value) ? 0 : Convert.ToDecimal(dvtarget[i]["fttycom_ra"]);
                dvtarget[i]["ffaccom_ra"] = dvtarget[i]["ffaccom_ra"].Equals(DBNull.Value) ? 0 : Convert.ToDecimal(dvtarget[i]["ffaccom_ra"]);
                dvtarget[i]["ffobcom_ra"] = dvtarget[i]["ffobcom_ra"].Equals(DBNull.Value) ? 0 : Convert.ToDecimal(dvtarget[i]["ffobcom_ra"]);

                dvtarget[i]["fripm_ri"] = Convert.ToDecimal(dvtarget[i]["fttypm_ri"]) + Convert.ToDecimal(dvtarget[i]["fxolpm_ri"]) +
                                          Convert.ToDecimal(dvtarget[i]["ffacpm_ri"]) + Convert.ToDecimal(dvtarget[i]["ffobpm_ri"]);

                dvtarget[i]["fripm_ra"] = Convert.ToDecimal(dvtarget[i]["fttypm_ra"]) + Convert.ToDecimal(dvtarget[i]["fxolpm_ra"]) +
                                          Convert.ToDecimal(dvtarget[i]["ffacpm_ra"]) + Convert.ToDecimal(dvtarget[i]["ffobpm_ra"]);

                if (dvtarget[i]["fcomtype"].ToString().Trim() == "2")
                {
                    dvtarget[i]["fricost_di"] = dvtarget[i]["ftdamt_di"];
                    dvtarget[i]["fricost_ia"] = dvtarget[i]["ftdamt_ia"];
                    dvtarget[i]["fricost_iy"] = Convert.ToDecimal(dvtarget[i]["ftdamt_di"]) + 
                                                Convert.ToDecimal(dvtarget[i]["ftdamt_ia"]);

                    dvtarget[i]["fripm_ri"] =  Convert.ToDecimal(dvtarget[i]["fripm_ri"]) +  
                                               Convert.ToDecimal(dvtarget[i]["ftdamt_di"]);

                    dvtarget[i]["fripm_ra"] = Convert.ToDecimal(dvtarget[i]["fripm_ra"]) +
                                              Convert.ToDecimal(dvtarget[i]["ftdamt_ia"]);
                } else
                {
                    dvtarget[i]["fricost_di"] = 0;
                    dvtarget[i]["fricost_ia"] = 0;
                    dvtarget[i]["fricost_iy"] = 0;
                }

                dvtarget[i]["fxripm_ri"] = Convert.ToDecimal(dvtarget[i]["fttypm_ri"]) + Convert.ToDecimal(dvtarget[i]["ffacpm_ri"]) +
                                           Convert.ToDecimal(dvtarget[i]["ffobpm_ri"]);

                dvtarget[i]["fxripm_ra"] = Convert.ToDecimal(dvtarget[i]["fttypm_ra"]) + Convert.ToDecimal(dvtarget[i]["ffacpm_ra"]) +
                                           Convert.ToDecimal(dvtarget[i]["ffobpm_ra"]);

                dvtarget[i]["fricom_ri"] = Convert.ToDecimal(dvtarget[i]["fttycom_ri"]) + Convert.ToDecimal(dvtarget[i]["ffaccom_ri"]) +
                                           Convert.ToDecimal(dvtarget[i]["ffobcom_ri"]);

                dvtarget[i]["fricom_ra"] = Convert.ToDecimal(dvtarget[i]["fttycom_ra"]) + Convert.ToDecimal(dvtarget[i]["ffaccom_ra"]) +
                                           Convert.ToDecimal(dvtarget[i]["ffobcom_ra"]);

                dvtarget[i]["frtnpm_ry"] = Convert.ToDecimal(dvtarget[i]["frtnpm_ri"])+Convert.ToDecimal(dvtarget[i]["frtnpm_ra"]);
                dvtarget[i]["fttypm_ry"] = Convert.ToDecimal(dvtarget[i]["fttypm_ri"])+Convert.ToDecimal(dvtarget[i]["fttypm_ra"]);
                dvtarget[i]["fxolpm_ry"] = Convert.ToDecimal(dvtarget[i]["fxolpm_ri"])+Convert.ToDecimal(dvtarget[i]["fxolpm_ra"]);
                dvtarget[i]["ffacpm_ry"] = Convert.ToDecimal(dvtarget[i]["ffacpm_ri"])+Convert.ToDecimal(dvtarget[i]["ffacpm_ra"]);
                dvtarget[i]["ffobpm_ry"] = Convert.ToDecimal(dvtarget[i]["ffobpm_ri"])+Convert.ToDecimal(dvtarget[i]["ffobpm_ra"]);

                dvtarget[i]["frtncom_ry"] = Convert.ToDecimal(dvtarget[i]["frtncom_ri"])+Convert.ToDecimal(dvtarget[i]["frtncom_ra"]);
                dvtarget[i]["fttycom_ry"] = Convert.ToDecimal(dvtarget[i]["fttycom_ri"])+Convert.ToDecimal(dvtarget[i]["fttycom_ra"]);
                dvtarget[i]["ffaccom_ry"] = Convert.ToDecimal(dvtarget[i]["ffaccom_ri"])+Convert.ToDecimal(dvtarget[i]["ffaccom_ra"]);
                dvtarget[i]["ffobcom_ry"] = Convert.ToDecimal(dvtarget[i]["ffobcom_ri"])+Convert.ToDecimal(dvtarget[i]["ffobcom_ra"]);

                dvtarget[i]["fripm_ry"] = Convert.ToDecimal(dvtarget[i]["fripm_ri"]) + Convert.ToDecimal(dvtarget[i]["fripm_ra"]);
                dvtarget[i]["fxripm_ry"] = Convert.ToDecimal(dvtarget[i]["fxripm_ri"]) + Convert.ToDecimal(dvtarget[i]["fxripm_ra"]);
                dvtarget[i]["fricom_ry"] = Convert.ToDecimal(dvtarget[i]["fricom_ri"]) + Convert.ToDecimal(dvtarget[i]["fricom_ra"]);

                dvtarget[i]["funexp_a"] = Convert.ToInt32(dvtarget[i]["fpolday_a"]) - Convert.ToInt32(dvtarget[i]["fexpday_a"]);
                dvtarget[i]["funexp_y"] = Convert.ToInt32(dvtarget[i]["fpolday_y"]) - Convert.ToInt32(dvtarget[i]["fexpday_y"]);

                Decimal fgpm_di = Convert.ToDecimal(dvtarget[i]["fgpm_di"]),
                        fgpm_p = Convert.ToDecimal(dvtarget[i]["fgpm_p"]),
                        fgpm_iy = Convert.ToDecimal(dvtarget[i]["fgpm_iy"]),
                        fgpm_ia = Convert.ToDecimal(dvtarget[i]["fgpm_ia"]),
                        fripm_ri = Convert.ToDecimal(dvtarget[i]["fripm_ri"]),
                        fripm_ry = Convert.ToDecimal(dvtarget[i]["fripm_ry"]),
                        fripm_ra = Convert.ToDecimal(dvtarget[i]["fripm_ra"]),
                        fcomamt_di = Convert.ToDecimal(dvtarget[i]["fcomamt_di"]),
                        fcomamt_iy = Convert.ToDecimal(dvtarget[i]["fcomamt_iy"]),
                        fcomamt_ia = Convert.ToDecimal(dvtarget[i]["fcomamt_ia"]),
                        fricom_ri = Convert.ToDecimal(dvtarget[i]["fricom_ri"]),
                        fricom_ry = Convert.ToDecimal(dvtarget[i]["fricom_ry"]),
                        fricom_ra = Convert.ToDecimal(dvtarget[i]["fricom_ra"]);

                Int32 fexpday_y = Convert.ToInt32(dvtarget[i]["fexpday_y"]),
                      fexpday_a = Convert.ToInt32(dvtarget[i]["fexpday_a"]),
                      fpolday_y = Convert.ToInt32(dvtarget[i]["fpolday_y"]),
                      fpolday_a = Convert.ToInt32(dvtarget[i]["fpolday_a"]);

           //             CompEarnedFig(Decimal prinfig, Int32 fexpday, Int32 fpolday, Decimal fgpm_p, Decimal asatprm)

                if (fgpm_p == 0.00m)
                    dvtarget[i]["finsratio"] = 1.00;
                else
                    dvtarget[i]["finsratio"] = Math.Round(fgpm_di/fgpm_p, 4, MidpointRounding.AwayFromZero);

                dvtarget[i]["gernpm_y"] = UtilityFunc.CompEarnedFig(fgpm_iy, fexpday_y, fpolday_y, fgpm_p, fgpm_iy);
                dvtarget[i]["gernpm_a"] = UtilityFunc.CompEarnedFig(fgpm_ia, fexpday_a, fpolday_a, fgpm_p, fgpm_ia);
                dvtarget[i]["rernpm_y"] = UtilityFunc.CompEarnedFig(fripm_ry, fexpday_y, fpolday_y, fgpm_p, fgpm_iy);
                dvtarget[i]["rernpm_a"] = UtilityFunc.CompEarnedFig(fripm_ra, fexpday_a, fpolday_a, fgpm_p, fgpm_ia);
                dvtarget[i]["fernpm_y"] = UtilityFunc.CompEarnedFig(fgpm_iy - fripm_ry, fexpday_y, fpolday_y, fgpm_p, fgpm_iy);
                dvtarget[i]["fernpm_a"] = UtilityFunc.CompEarnedFig(fgpm_ia - fripm_ra, fexpday_a, fpolday_a, fgpm_p, fgpm_ia);
                //dvtarget[i]["fernpm_y"] = Convert.ToDecimal(dvtarget[i]["gernpm_y"]) - Convert.ToDecimal(dvtarget[i]["rernpm_y"]);
                //dvtarget[i]["fernpm_a"] = Convert.ToDecimal(dvtarget[i]["gernpm_a"]) - Convert.ToDecimal(dvtarget[i]["rernpm_a"]); ;
                dvtarget[i]["fexpcom_y"] = UtilityFunc.CompEarnedFig(fcomamt_iy, fexpday_y, fpolday_y, fgpm_p, fgpm_iy);
                dvtarget[i]["fexpcom_a"] = UtilityFunc.CompEarnedFig(fcomamt_ia, fexpday_a, fpolday_a, fgpm_p, fgpm_ia);
                dvtarget[i]["fexpcom_ry"] = UtilityFunc.CompEarnedFig(fricom_ry, fexpday_y, fpolday_y, fgpm_p, fgpm_iy);
                dvtarget[i]["fexpcom_ra"] = UtilityFunc.CompEarnedFig(fricom_ra, fexpday_a, fpolday_a, fgpm_p, fgpm_ia);

                dvtarget[i]["fnetpm_i"] = fgpm_di-fripm_ri;
                dvtarget[i]["fnetpm_y"] = fgpm_iy-fripm_ry;
                dvtarget[i]["fnetpm_a"] = fgpm_ia-fripm_ra;
                dvtarget[i]["fnetcom_i"] = fcomamt_di-fricom_ri;
                dvtarget[i]["fnetcom_y"] = fcomamt_iy - fricom_ry;
                dvtarget[i]["fnetcom_a"] = fcomamt_ia - fricom_ra;

                String fclass = dvtarget[i]["fclass"].ToString().Trim(),fkind = dvtarget[i]["fkind"].ToString().Trim(),
                       ftrade = dvtarget[i]["ftrade"].ToString().Trim(),fsclass;

                if (fclass != "CAR")
                    fsclass = dvtarget[i]["fsclass"].ToString().Trim();
                else if(fkind == "1")
                    fsclass = "SEC1";
                else
                    fsclass = "SEC2";

                dvtarget[i]["fclsname"] = UtilityFunc.ClsName(fclass,fsclass);
                dvtarget[i]["ftrdseq"] = UtilityFunc.ClsTrdSeq(fclass,fsclass,ftrade);
                dvtarget[i]["acc_class"] = UtilityFunc.AcCode(fclass,fsclass,fkind);
                dvtarget[i]["acc_desc"] = UtilityFunc.AccDesc(fclass, fsclass,fkind);
                dvtarget[i]["ftrdname"] = UtilityFunc.ClsTrdName(fclass, fsclass, ftrade);
                dvtarget[i]["pcode"] = dvtarget[i]["fclsseq"].ToString()+fclass.PadRight(10)+dvtarget[i]["acc_class"].ToString();
                dvtarget[i]["sec_desc"] = UtilityFunc.SecName(fclass, dvtarget[i]["acc_class"].ToString().Trim());

                dvnewbus.RowFilter = String.Format("fpolno = '{0}' and fendtno = '{1}'", dvtarget[i]["fpolno"].ToString().Trim(),
                                                   dvtarget[i]["fendtno"].ToString().Trim());

                if (dvnewbus.Count != 0)
                    dvtarget[i]["fnew"] = dvnewbus[0]["fnew"];

                dvtarget[i]["fgpm_y"] = dvtarget[i]["fgpm_iy"];
                dvtarget[i]["fgpm_a"] = dvtarget[i]["fgpm_ia"];
                dvtarget[i]["fripm_y"] = dvtarget[i]["fripm_ry"];
                dvtarget[i]["fripm_a"] = dvtarget[i]["fripm_ra"];

                dvtarget[i]["fcomamt_y"] = dvtarget[i]["fcomamt_iy"];
                dvtarget[i]["fcomamt_a"] = dvtarget[i]["fcomamt_ia"];
                dvtarget[i]["fricom_y"] = dvtarget[i]["fricom_ry"];
                dvtarget[i]["fricom_a"] = dvtarget[i]["fricom_ra"];
//dt20061231
                Int32 funexp_y = Convert.ToInt32(dvtarget[i]["funexp_y"]), funexp_a = Convert.ToInt32(dvtarget[i]["funexp_a"]);
                DateTime fthisasat = Convert.ToDateTime(dvtarget[i]["fthisasat"]),
                         flastasat = Convert.ToDateTime(dvtarget[i]["flastasat"]);
                Decimal gernpm_y = Convert.ToDecimal(dvtarget[i]["gernpm_y"]), gernpm_a = Convert.ToDecimal(dvtarget[i]["gernpm_a"]),
                        rernpm_y = Convert.ToDecimal(dvtarget[i]["rernpm_y"]), rernpm_a = Convert.ToDecimal(dvtarget[i]["rernpm_a"]),
                        fernpm_y = Convert.ToDecimal(dvtarget[i]["fernpm_y"]), fernpm_a = Convert.ToDecimal(dvtarget[i]["fernpm_a"]),
                        fnetpm_y = Convert.ToDecimal(dvtarget[i]["fnetpm_y"]), fnetpm_a = Convert.ToDecimal(dvtarget[i]["fnetpm_a"]),
                        fexpcom_y = Convert.ToDecimal(dvtarget[i]["fexpcom_y"]), fexpcom_a = Convert.ToDecimal(dvtarget[i]["fexpcom_a"]),
                        fexpcom_ry = Convert.ToDecimal(dvtarget[i]["fexpcom_ry"]), fexpcom_ra = Convert.ToDecimal(dvtarget[i]["fexpcom_ra"]);

                String cpath_y = "", cpath_a = "";

                if (funexp_y == 0)
                    cpath_y = "1";
                else if (fthisasat <= dt20061231)
                    cpath_y = "2";

                if (cpath_y == "")
                    if ((fgpm_iy >= 0 && gernpm_y >= fgpm_iy) || (fgpm_iy < 0 && gernpm_y <= fgpm_iy))
                        cpath_y = "1";

                if (cpath_y == "")
                    cpath_y = "2";

                if (funexp_a == 0)
                    cpath_a = "1";
                else if (flastasat <= dt20061231)
                    cpath_a = "2";

                if (cpath_a == "")
                    if ((fgpm_ia >= 0 && gernpm_a >= fgpm_ia) || (fgpm_ia < 0 && gernpm_a <= fgpm_ia))
                        cpath_a = "1";

                if (cpath_a == "")
                    cpath_a = "2";

                if (cpath_y == "1")
                {
                    dvtarget[i]["gernpm_fy"] = fgpm_iy;
                    dvtarget[i]["rernpm_fy"] = fripm_ry;
                    dvtarget[i]["fernpm_fy"] = fnetpm_y;
                    dvtarget[i]["fexpcom_fy"] = fcomamt_iy;
                    dvtarget[i]["ericom_fy"] = fricom_ry;
                }

                if (cpath_y == "2")
                {
                    dvtarget[i]["gernpm_fy"] = gernpm_y;
                    dvtarget[i]["rernpm_fy"] = rernpm_y;
                    dvtarget[i]["fernpm_fy"] = fernpm_y;
                    dvtarget[i]["fexpcom_fy"] = fexpcom_y;
                    dvtarget[i]["ericom_fy"] = fexpcom_ry;
                }

                if (cpath_a == "1")
                {
                    dvtarget[i]["gernpm_fa"] = fgpm_ia;
                    dvtarget[i]["rernpm_fa"] = fripm_ra;
                    dvtarget[i]["fernpm_fa"] = fnetpm_a;
                    dvtarget[i]["fexpcom_fa"] = fcomamt_ia;
                    dvtarget[i]["ericom_fa"] = fricom_ra;
                }

                if (cpath_a == "2")
                {
                    dvtarget[i]["gernpm_fa"] = gernpm_a;
                    dvtarget[i]["rernpm_fa"] = rernpm_a;
                    dvtarget[i]["fernpm_fa"] = fernpm_a;
                    dvtarget[i]["fexpcom_fa"] = fexpcom_a;
                    dvtarget[i]["ericom_fa"] = fexpcom_ra;
                }

                dvtarget[i]["ffacpm_ri"] = Convert.ToDecimal(dvtarget[i]["ffacpm_ri"]) + Convert.ToDecimal(dvtarget[i]["ffobpm_ri"]);
                dvtarget[i]["funernpm"] = Convert.ToDecimal(dvtarget[i]["fnetpm_i"]) - Convert.ToDecimal(dvtarget[i]["fernpm_fy"]) +
                                          Convert.ToDecimal(dvtarget[i]["fernpm_fa"]);
                dvtarget[i]["gernpm"] = Convert.ToDecimal(dvtarget[i]["gernpm_fy"]) - Convert.ToDecimal(dvtarget[i]["gernpm_fa"]);
                dvtarget[i]["rernpm"] = Convert.ToDecimal(dvtarget[i]["rernpm_fy"]) - Convert.ToDecimal(dvtarget[i]["rernpm_fa"]);
                dvtarget[i]["fernpm"] = Convert.ToDecimal(dvtarget[i]["fernpm_fy"]) - Convert.ToDecimal(dvtarget[i]["fernpm_fa"]);
                dvtarget[i]["fadjcom"] = Convert.ToDecimal(dvtarget[i]["fexpcom_fy"]) - Convert.ToDecimal(dvtarget[i]["fexpcom_fa"]);
                dvtarget[i]["ffaccom_ri"] = Convert.ToDecimal(dvtarget[i]["ffaccom_ri"]) + Convert.ToDecimal(dvtarget[i]["ffobcom_ri"]);
                dvtarget[i]["fadjricom"] = Convert.ToDecimal(dvtarget[i]["ericom_fy"]) - Convert.ToDecimal(dvtarget[i]["ericom_fa"]);

                dvtarget[i]["fdac"] = Convert.ToDecimal(dvtarget[i]["fcomamt_di"]) - Convert.ToDecimal(dvtarget[i]["fexpcom_fy"]) +
                                      Convert.ToDecimal(dvtarget[i]["fexpcom_fa"]);
                dvtarget[i]["fdricom"] = Convert.ToDecimal(dvtarget[i]["fricom_ri"]) - Convert.ToDecimal(dvtarget[i]["ericom_fy"]) +
                                         Convert.ToDecimal(dvtarget[i]["ericom_fa"]);
                dvtarget[i]["fdacqcost"] = Convert.ToDecimal(dvtarget[i]["fnetcom_i"])-Convert.ToDecimal(dvtarget[i]["fexpcom_fy"])+
                        Convert.ToDecimal(dvtarget[i]["fexpcom_fa"]) - Convert.ToDecimal(dvtarget[i]["ericom_fa"]) +
                        Convert.ToDecimal(dvtarget[i]["ericom_fy"]);
                dvtarget[i]["fnetcompay"] = Convert.ToDecimal(dvtarget[i]["fexpcom_fy"])-Convert.ToDecimal(dvtarget[i]["fexpcom_fa"])-
                        Convert.ToDecimal(dvtarget[i]["ericom_fy"]) + Convert.ToDecimal(dvtarget[i]["ericom_fa"]);
                dvtarget[i]["funnpm_fy"] = Convert.ToDecimal(dvtarget[i]["fnetpm_y"]) - Convert.ToDecimal(dvtarget[i]["fernpm_fy"]);
                dvtarget[i]["funnpm_fa"] = Convert.ToDecimal(dvtarget[i]["fnetpm_a"]) - Convert.ToDecimal(dvtarget[i]["fernpm_fa"]);

                //////

                //dvtarget[i]["fgpm_di"] = dvtarget[i]["gernpm_fy"];
                //dvtarget[i]["fttypm_ri"] = dvtarget[i]["gernpm_fa"];
                //dvtarget[i]["fxolpm_ri"] = dvtarget[i]["rernpm_fy"];
                //dvtarget[i]["ffacpm_ri"] = dvtarget[i]["rernpm_fa"];

                //////
            }

            return;
        }

        public static void UpdInceptionPeriod_1(DataRowView dvrow, DataView dvtarget, String fincfr, String fincto,
                                                String fpolday, String fexpday, 
                                                DateTime dt20121231, DateTime dt20130101, DateTime dt20130131,
                                                DateTime refdate)
        {
            String fctlid_p = dvrow["fctlid_p"].ToString().Trim(), fctlid_v = dvrow["fctlid_v"].ToString().Trim();
            dvtarget.RowFilter = String.Format("fctlid_p = '{0}' and fctlid_v = '{1}'", fctlid_p, fctlid_v);

            for (int i = 0; i < dvtarget.Count; i++)
            {
                DateTime tarbkdate = Convert.ToDateTime(dvtarget[i]["fbkdate"]),
                         srcbkdate = Convert.ToDateTime(dvrow["fbkdate"]);

                if (tarbkdate <= dt20121231 || (tarbkdate >= dt20130101 && tarbkdate <= dt20130131 &&
                    srcbkdate >= dt20130101 && srcbkdate <= dt20130131))
                {
                    if (String.IsNullOrEmpty(dvrow["fincto"].ToString()))
                    {
                        dvtarget[i][fpolday] = 1;
                        dvtarget[i][fexpday] = 1;

                        continue;
                    }

                    if (Convert.ToDateTime(dvrow["fincto"]) <= refdate)
                    {
                        dvtarget[i][fpolday] = 1;
                        dvtarget[i][fexpday] = 1;

                        continue;     // add on 2016.05.19
                    }
                    //if (Convert.ToDateTime(dvrow["fbkdate"]) <= refdate)
                    //    continue;  // change to comment on 2016.05.19

                    if (!String.IsNullOrEmpty(dvtarget[i]["fefffr"].ToString()))
                        if (Convert.ToDateTime(dvrow["fincto"]) < Convert.ToDateTime(dvtarget[i]["fefffr"]))
                        {
                            dvtarget[i][fincfr] = dvrow["fincfr"];
                            dvtarget[i][fincto] = dvrow["fincto"];

                            continue;
                        }

                    if (!String.IsNullOrEmpty(dvtarget[i]["feffto"].ToString()))
                        if (Convert.ToDateTime(dvrow["fincto"]) < Convert.ToDateTime(dvtarget[i]["feffto"]))
                            dvtarget[i][fincto] = dvrow["fincto"];

                }

            }

        }

        public static void RefineUnearnData(DataTable dt)
        {
            DateTime dt20061231 = DateTime.ParseExact("2006.12.31", "yyyy.MM.dd", System.Globalization.CultureInfo.InvariantCulture);
            
            dt.Columns.Add("xernpm_y", typeof(Decimal));
            dt.Columns.Add("xernpm_a", typeof(Decimal));
            dt.Columns.Add("rxernpm_y", typeof(Decimal));
            dt.Columns.Add("rxernpm_a", typeof(Decimal));
            dt.Columns.Add("fexpcom_ny", typeof(Decimal));
            dt.Columns.Add("fexpcom_na", typeof(Decimal));

            dt.Columns.Add("xernpm_fy",typeof(Decimal));
            dt.Columns.Add("xernpm_fa",typeof(Decimal));
            dt.Columns.Add("rxernpm_fy",typeof(Decimal));
            dt.Columns.Add("rxernpm_fa",typeof(Decimal));
            dt.Columns.Add("enetcom_fy",typeof(Decimal));
            dt.Columns.Add("enetcom_fa",typeof(Decimal));

            //dt.Columns.Add("fgpm_y", typeof(Decimal));
            //dt.Columns.Add("fgpm_a", typeof(Decimal));
            dt.Columns.Add("fricost_y", typeof(Decimal));
            dt.Columns.Add("fricost_a", typeof(Decimal));
            //dt.Columns.Add("fripm_y", typeof(Decimal));
            //dt.Columns.Add("fripm_a", typeof(Decimal));

            dt.Columns.Add("fxolpm_y", typeof(Decimal));
            dt.Columns.Add("fxolpm_a", typeof(Decimal));
            dt.Columns.Add("fxripm_y", typeof(Decimal));
            dt.Columns.Add("fxripm_a", typeof(Decimal));
            //dt.Columns.Add("fcomamt_y", typeof(Decimal));
            //dt.Columns.Add("fcomamt_a", typeof(Decimal));
            //dt.Columns.Add("fricom_y", typeof(Decimal));
            //dt.Columns.Add("fricom_a", typeof(Decimal));

            foreach (DataRow dr in dt.Rows)
            {
                dr["fgpm_iy"] = dr["fgpm_ia"];
                dr["ftdamt_iy"] = dr["ftdamt_ia"];
                dr["fricost_iy"] = dr["fricost_ia"];
                dr["fcomamt_iy"] = dr["fcomamt_ia"];

                dr["frtnpm_ry"] = dr["frtnpm_ra"];
                dr["fttypm_ry"] = dr["fttypm_ra"];
                dr["fxolpm_ry"] = dr["fxolpm_ra"];
                dr["ffacpm_ry"] = dr["ffacpm_ra"];
                dr["ffobpm_ry"] = dr["ffobpm_ra"];
                dr["frtncom_ry"] = dr["frtncom_ra"];
                dr["fttycom_ry"] = dr["fttycom_ra"];
                dr["ffaccom_ry"] = dr["ffaccom_ra"];
                dr["ffobcom_ry"] = dr["ffobcom_ra"];

                dr["fripm_ry"] = dr["fripm_ra"];
                dr["fxripm_ry"] = dr["fxripm_ra"];
                dr["fricom_ry"] = dr["fricom_ra"];

                //Decimal fgpm_di = Convert.ToDecimal(dr["fgpm_di"]), 
                //        fgpm_p = Convert.ToDecimal(dr["fgpm_p"]),
                //        fgpm_iy = Convert.ToDecimal(dr["fgpm_iy"]),
                //        fgpm_ia = Convert.ToDecimal(dr["fgpm_ia"]),
                //        fripm_ri = Convert.ToDecimal(dr["fripm_ri"]),
                //        fripm_ry = Convert.ToDecimal(dr["fripm_ry"]),
                //        fripm_ra = Convert.ToDecimal(dr["fripm_ra"]),
                //        fcomamt_di = Convert.ToDecimal(dr["fcomamt_di"]),
                //        fcomamt_iy = Convert.ToDecimal(dr["fcomamt_iy"]),
                //        fcomamt_ia = Convert.ToDecimal(dr["fcomamt_ia"]),
                //        fricom_ri = Convert.ToDecimal(dr["fricom_ri"]),
                //        fricom_ry = Convert.ToDecimal(dr["fricom_ry"]),
                //        fricom_ra = Convert.ToDecimal(dr["fricom_ra"]),
                //        fxolpm_ry = Convert.ToDecimal(dr["fxolpm_ry"]),
                //        fxolpm_ra = Convert.ToDecimal(dr["fxolpm_ra"]),
                //        fricost_iy = Convert.ToDecimal(dr["fricost_iy"]),
                //        fricost_ia = Convert.ToDecimal(dr["fricost_ia"]),
                //        fxripm_ry = Convert.ToDecimal(dr["fxripm_ry"]),
                //        fxripm_ra = Convert.ToDecimal(dr["fxripm_ra"]);

                Decimal fgpm_p = Convert.ToDecimal(dr["fgpm_p"]),
                        fgpm_iy = Convert.ToDecimal(dr["fgpm_iy"]),
                        fgpm_ia = Convert.ToDecimal(dr["fgpm_ia"]),
                        fripm_ry = Convert.ToDecimal(dr["fripm_ry"]),
                        fripm_ra = Convert.ToDecimal(dr["fripm_ra"]),
                        fcomamt_iy = Convert.ToDecimal(dr["fcomamt_iy"]),
                        fcomamt_ia = Convert.ToDecimal(dr["fcomamt_ia"]),
                        fricom_ry = Convert.ToDecimal(dr["fricom_ry"]),
                        fricom_ra = Convert.ToDecimal(dr["fricom_ra"]),
                        fxolpm_ry = Convert.ToDecimal(dr["fxolpm_ry"]),
                        fxolpm_ra = Convert.ToDecimal(dr["fxolpm_ra"]),
                        fricost_iy = Convert.ToDecimal(dr["fricost_iy"]),
                        fricost_ia = Convert.ToDecimal(dr["fricost_ia"]),
                        fxripm_ry = Convert.ToDecimal(dr["fxripm_ry"]),
                        fxripm_ra = Convert.ToDecimal(dr["fxripm_ra"]);
                
                Int32 fexpday_y = Convert.ToInt32(dr["fexpday_y"]),
                      fexpday_a = Convert.ToInt32(dr["fexpday_a"]),
                      fpolday_y = Convert.ToInt32(dr["fpolday_y"]),
                      fpolday_a = Convert.ToInt32(dr["fpolday_a"]);

                dr["gernpm_y"] = UtilityFunc.CompEarnedFig(fgpm_iy, fexpday_y, fpolday_y, fgpm_p, fgpm_iy); ;
                dr["rernpm_y"] = UtilityFunc.CompEarnedFig(fripm_ry, fexpday_y, fpolday_y, fgpm_p, fgpm_iy);

                dr["xernpm_y"] = UtilityFunc.CompEarnedFig(fxolpm_ry+fricost_iy, fexpday_y, fpolday_y, fgpm_p, fgpm_iy);
                dr["xernpm_a"] = UtilityFunc.CompEarnedFig(fxolpm_ra+fricost_ia, fexpday_a, fpolday_a, fgpm_p, fgpm_ia);

                dr["rxernpm_y"] = UtilityFunc.CompEarnedFig(fxripm_ry, fexpday_y, fpolday_y, fgpm_p, fgpm_iy);
                dr["rxernpm_a"] = UtilityFunc.CompEarnedFig(fxripm_ra, fexpday_a, fpolday_a, fgpm_p, fgpm_ia);
                
                dr["fernpm_y"] = UtilityFunc.CompEarnedFig(fgpm_iy - fripm_ry, fexpday_y, fpolday_y, fgpm_p, fgpm_iy);
                
                dr["fexpcom_y"] = UtilityFunc.CompEarnedFig(fcomamt_iy, fexpday_y, fpolday_y, fgpm_p, fgpm_iy);
                dr["fexpcom_ry"] = UtilityFunc.CompEarnedFig(fricom_ry, fexpday_y, fpolday_y, fgpm_p, fgpm_iy);

                dr["fexpcom_ny"] = UtilityFunc.CompEarnedFig(fcomamt_iy-fricom_ry, fexpday_y, fpolday_y, fgpm_p, fgpm_iy);
                dr["fexpcom_na"] = UtilityFunc.CompEarnedFig(fcomamt_ia-fricom_ra, fexpday_a, fpolday_a, fgpm_p, fgpm_ia);
                
                dr["fnetpm_y"] = fgpm_iy - fripm_ry;
                dr["fnetcom_y"] = fcomamt_iy - fricom_ry;

                dr["fgpm_y"] = fgpm_iy;
                dr["fricost_y"] = fricost_iy;
                dr["fripm_y"] = fripm_ry;
                dr["fxolpm_y"] = fxolpm_ry;
                dr["fxripm_y"] = fxripm_ry;
                dr["fcomamt_y"] = fcomamt_iy;
                dr["fricom_y"] = fricom_ry;

                dr["fricost_a"] = fricost_ia;
                dr["fxolpm_a"] = fxolpm_ra;
                dr["fxripm_a"] = fxripm_ra;

                //dt20061231
                Int32 funexp_y = Convert.ToInt32(dr["funexp_y"]), funexp_a = Convert.ToInt32(dr["funexp_a"]);
                DateTime fthisasat = Convert.ToDateTime(dr["fthisasat"]),
                         flastasat = Convert.ToDateTime(dr["flastasat"]);
                Decimal gernpm_y = Convert.ToDecimal(dr["gernpm_y"]), gernpm_a = Convert.ToDecimal(dr["gernpm_a"]),
                        rernpm_y = Convert.ToDecimal(dr["rernpm_y"]), rernpm_a = Convert.ToDecimal(dr["rernpm_a"]),
                        fernpm_y = Convert.ToDecimal(dr["fernpm_y"]), fernpm_a = Convert.ToDecimal(dr["fernpm_a"]),
                        fnetpm_y = Convert.ToDecimal(dr["fnetpm_y"]), fnetpm_a = Convert.ToDecimal(dr["fnetpm_a"]),
                        fexpcom_y = Convert.ToDecimal(dr["fexpcom_y"]), fexpcom_a = Convert.ToDecimal(dr["fexpcom_a"]),
                        fexpcom_ry = Convert.ToDecimal(dr["fexpcom_ry"]), fexpcom_ra = Convert.ToDecimal(dr["fexpcom_ra"]),
                        xernpm_y = Convert.ToDecimal(dr["xernpm_y"]), xernpm_a = Convert.ToDecimal(dr["xernpm_a"]),
                        rxernpm_y = Convert.ToDecimal(dr["rxernpm_y"]), rxernpm_a = Convert.ToDecimal(dr["rxernpm_a"]),
                        fnetcom_y = Convert.ToDecimal(dr["fnetcom_y"]), fnetcom_a = Convert.ToDecimal(dr["fnetcom_a"]),
                        fexpcom_ny = Convert.ToDecimal(dr["fexpcom_ny"]), fexpcom_na = Convert.ToDecimal(dr["fexpcom_na"]);

                String cpath_y = "", cpath_a = "";

                if (funexp_y == 0)
                    cpath_y = "1";
                else if (fthisasat <= dt20061231)
                    cpath_y = "2";

                if (cpath_y == "")
                    if ((fgpm_iy >= 0 && gernpm_y >= fgpm_iy) || (fgpm_iy < 0 && gernpm_y <= fgpm_iy))
                        cpath_y = "1";

                if (cpath_y == "")
                    cpath_y = "2";

                if (funexp_a == 0)
                    cpath_a = "1";
                else if (flastasat <= dt20061231)
                    cpath_a = "2";

                if (cpath_a == "")
                    if ((fgpm_ia >= 0 && gernpm_a >= fgpm_ia) || (fgpm_ia < 0 && gernpm_a <= fgpm_ia))
                        cpath_a = "1";

                if (cpath_a == "")
                    cpath_a = "2";

                if (cpath_y == "1")
                {
                    dr["gernpm_fy"] = fgpm_iy;
                    dr["rernpm_fy"] = fripm_ry;
                    dr["fernpm_fy"] = fnetpm_y;
                    dr["fexpcom_fy"] = fcomamt_iy;
                    dr["ericom_fy"] = fricom_ry;

                    dr["xernpm_fy"] = fxolpm_ry + fricost_iy;
                    dr["rxernpm_fy"] = fxripm_ry;
                    dr["enetcom_fy"] = fnetcom_y;
                }

                if (cpath_y == "2")
                {
                    dr["gernpm_fy"] = gernpm_y;
                    dr["rernpm_fy"] = rernpm_y;
                    dr["fernpm_fy"] = fernpm_y;
                    dr["fexpcom_fy"] = fexpcom_y;
                    dr["ericom_fy"] = fexpcom_ry;

                    dr["xernpm_fy"] = xernpm_y;
                    dr["rxernpm_fy"] = rxernpm_y;
                    dr["enetcom_fy"] = fexpcom_ny;
                }

                if (cpath_a == "1")
                {
                    dr["xernpm_fa"] = fxolpm_ra + fricost_ia;
                    dr["rxernpm_fa"] = fxripm_ra;
                    dr["enetcom_fa"] = fnetcom_a;
                }

                if (cpath_a == "2")
                {
                    dr["xernpm_fa"] = xernpm_a;
                    dr["rxernpm_fa"] = rxernpm_a;
                    dr["enetcom_fa"] = fexpcom_na;
                }
            }
        }

        public static DataTable GenUnErnDetail(DataTable dt)
        {
            dt.Columns.Add("fpolday", typeof(Int32));
            dt.Columns.Add("gunern_fa", typeof(Decimal));
            dt.Columns.Add("runern_fa", typeof(Decimal));
            dt.Columns.Add("xunern_fa", typeof(Decimal));
            dt.Columns.Add("rxunern_fa", typeof(Decimal));
            dt.Columns.Add("nunern_fa", typeof(Decimal));
            dt.Columns.Add("guncom_fa", typeof(Decimal));
            dt.Columns.Add("runcom_fa", typeof(Decimal));
            dt.Columns.Add("nuncom_fa", typeof(Decimal));

            //dt.Columns.Add("gcunern", typeof(Decimal));
            //dt.Columns.Add("gnunern", typeof(Decimal));
            //dt.Columns.Add("rcunern", typeof(Decimal));
            //dt.Columns.Add("rnunern", typeof(Decimal));

            //dt.Columns.Add("xcunern", typeof(Decimal));
            //dt.Columns.Add("xnunern", typeof(Decimal));
            //dt.Columns.Add("rxcunern", typeof(Decimal));
            //dt.Columns.Add("rxnunern", typeof(Decimal));
            //dt.Columns.Add("ncunern", typeof(Decimal));
            //dt.Columns.Add("nnunern", typeof(Decimal));

            //dt.Columns.Add("gcuncom", typeof(Decimal));
            //dt.Columns.Add("gnuncom", typeof(Decimal));
            //dt.Columns.Add("rcuncom", typeof(Decimal));
            //dt.Columns.Add("rnuncom", typeof(Decimal));
            //dt.Columns.Add("ncuncom", typeof(Decimal));
            //dt.Columns.Add("nnuncom", typeof(Decimal));
            
            DataColumn gcunern1Col = dt.Columns.Add("gcunern1", typeof(Decimal)),
                       gcunern2Col = dt.Columns.Add("gcunern2", typeof(Decimal)),
                       gnunern1Col = dt.Columns.Add("gnunern1", typeof(Decimal)),
                       gnunern2Col = dt.Columns.Add("gnunern2", typeof(Decimal)),
                       rcunern1Col = dt.Columns.Add("rcunern1", typeof(Decimal)),
                       rcunern2Col = dt.Columns.Add("rcunern2", typeof(Decimal)),
                       rnunern1Col = dt.Columns.Add("rnunern1", typeof(Decimal)),
                       rnunern2Col = dt.Columns.Add("rnunern2", typeof(Decimal)),
                       xcunern1Col = dt.Columns.Add("xcunern1", typeof(Decimal)),
                       xcunern2Col = dt.Columns.Add("xcunern2", typeof(Decimal)),
                       xnunern1Col = dt.Columns.Add("xnunern1", typeof(Decimal)),
                       xnunern2Col = dt.Columns.Add("xnunern2", typeof(Decimal)),
                       rxcunern1Col = dt.Columns.Add("rxcunern1", typeof(Decimal)),
                       rxcunern2Col = dt.Columns.Add("rxcunern2", typeof(Decimal)),
                       rxnunern1Col = dt.Columns.Add("rxnunern1", typeof(Decimal)),
                       rxnunern2Col = dt.Columns.Add("rxnunern2", typeof(Decimal)),
                       ncunern1Col = dt.Columns.Add("ncunern1", typeof(Decimal)),
                       ncunern2Col = dt.Columns.Add("ncunern2", typeof(Decimal)),
                       nnunern1Col = dt.Columns.Add("nnunern1", typeof(Decimal)),
                       nnunern2Col = dt.Columns.Add("nnunern2", typeof(Decimal)),
                       gcuncom1Col = dt.Columns.Add("gcuncom1", typeof(Decimal)),
                       gcuncom2Col = dt.Columns.Add("gcuncom2", typeof(Decimal)),
                       gnuncom1Col = dt.Columns.Add("gnuncom1", typeof(Decimal)),
                       gnuncom2Col = dt.Columns.Add("gnuncom2", typeof(Decimal)),
                       rcuncom1Col = dt.Columns.Add("rcuncom1", typeof(Decimal)),
                       rcuncom2Col = dt.Columns.Add("rcuncom2", typeof(Decimal)),
                       rnuncom1Col = dt.Columns.Add("rnuncom1", typeof(Decimal)),
                       rnuncom2Col = dt.Columns.Add("rnuncom2", typeof(Decimal)),
                       ncuncom1Col = dt.Columns.Add("ncuncom1", typeof(Decimal)),
                       ncuncom2Col = dt.Columns.Add("ncuncom2", typeof(Decimal)),
                       nnuncom1Col = dt.Columns.Add("nnuncom1", typeof(Decimal)),
                       nnuncom2Col = dt.Columns.Add("nnuncom2", typeof(Decimal));

            gcunern1Col.DefaultValue = 0.00m;
            gcunern2Col.DefaultValue = 0.00m;
            gnunern1Col.DefaultValue = 0.00m;
            gnunern2Col.DefaultValue = 0.00m;

            rcunern1Col.DefaultValue = 0.00m;
            rcunern2Col.DefaultValue = 0.00m;
            rnunern1Col.DefaultValue = 0.00m;
            rnunern2Col.DefaultValue = 0.00m;

            xcunern1Col.DefaultValue = 0.00m;
            xcunern2Col.DefaultValue = 0.00m;
            xnunern1Col.DefaultValue = 0.00m;
            xnunern2Col.DefaultValue = 0.00m;

            rxcunern1Col.DefaultValue = 0.00m;
            rxcunern2Col.DefaultValue = 0.00m;
            rxnunern1Col.DefaultValue = 0.00m;
            rxnunern2Col.DefaultValue = 0.00m;

            ncunern1Col.DefaultValue = 0.00m;
            ncunern2Col.DefaultValue = 0.00m;
            nnunern1Col.DefaultValue = 0.00m;
            nnunern2Col.DefaultValue = 0.00m;

            gcuncom1Col.DefaultValue = 0.00m;
            gcuncom2Col.DefaultValue = 0.00m;
            gnuncom1Col.DefaultValue = 0.00m;
            gnuncom2Col.DefaultValue = 0.00m;

            rcuncom1Col.DefaultValue = 0.00m;
            rcuncom2Col.DefaultValue = 0.00m;
            rnuncom1Col.DefaultValue = 0.00m;
            rnuncom2Col.DefaultValue = 0.00m;

            ncuncom1Col.DefaultValue = 0.00m;
            ncuncom2Col.DefaultValue = 0.00m;
            nnuncom1Col.DefaultValue = 0.00m;
            nnuncom2Col.DefaultValue = 0.00m;

            DataColumn gcunernCol = dt.Columns.Add("gcunern", typeof(Decimal)),
                       gnunernCol = dt.Columns.Add("gnunern", typeof(Decimal)),
                       rcunernCol = dt.Columns.Add("rcunern", typeof(Decimal)),
                       rnunernCol = dt.Columns.Add("rnunern", typeof(Decimal)),
                       xcunernCol = dt.Columns.Add("xcunern", typeof(Decimal)),
                       xnunernCol = dt.Columns.Add("xnunern", typeof(Decimal)),
                       rxcunernCol = dt.Columns.Add("rxcunern", typeof(Decimal)),
                       rxnunernCol = dt.Columns.Add("rxnunern", typeof(Decimal)),
                       ncunernCol = dt.Columns.Add("ncunern", typeof(Decimal)),
                       nnunernCol = dt.Columns.Add("nnunern", typeof(Decimal)),
                       gcuncomCol = dt.Columns.Add("gcuncom", typeof(Decimal)),
                       gnuncomCol = dt.Columns.Add("gnuncom", typeof(Decimal)),
                       rcuncomCol = dt.Columns.Add("rcuncom", typeof(Decimal)),
                       rnuncomCol = dt.Columns.Add("rnuncom", typeof(Decimal)),
                       ncuncomCol = dt.Columns.Add("ncuncom", typeof(Decimal)),
                       nnuncomCol = dt.Columns.Add("nnuncom", typeof(Decimal));

            gcunernCol.DefaultValue = 0.00m;
            gnunernCol.DefaultValue = 0.00m;
            rcunernCol.DefaultValue = 0.00m;
            rnunernCol.DefaultValue = 0.00m;
            xcunernCol.DefaultValue = 0.00m;
            xnunernCol.DefaultValue = 0.00m;
            rxcunernCol.DefaultValue = 0.00m;
            rxnunernCol.DefaultValue = 0.00m;
            ncunernCol.DefaultValue = 0.00m;
            nnunernCol.DefaultValue = 0.00m;
            gcuncomCol.DefaultValue = 0.00m;
            gnuncomCol.DefaultValue = 0.00m;
            rcuncomCol.DefaultValue = 0.00m;
            rnuncomCol.DefaultValue = 0.00m;
            ncuncomCol.DefaultValue = 0.00m;
            nnuncomCol.DefaultValue = 0.00m;

            DataView dv = dt.DefaultView;

            dv.RowFilter = "fnetpm_a <> 0.00";

            for (int i = 0; i < dv.Count; i++)
            {
                dv[i]["fpolday"] = dv[i]["fpolday_a"];

                Decimal fgpm_a = Convert.ToDecimal(dv[i]["fgpm_a"]), gernpm_fa = Convert.ToDecimal(dv[i]["gernpm_fa"]),
                        fripm_a = Convert.ToDecimal(dv[i]["fripm_a"]), rernpm_fa = Convert.ToDecimal(dv[i]["rernpm_fa"]),
                        fxolpm_a = Convert.ToDecimal(dv[i]["fxolpm_a"]), fricost_a = Convert.ToDecimal(dv[i]["fricost_a"]),
                        xernpm_fa = Convert.ToDecimal(dv[i]["xernpm_fa"]), fxripm_a = Convert.ToDecimal(dv[i]["fxripm_a"]),
                        rxernpm_fa = Convert.ToDecimal(dv[i]["rxernpm_fa"]), fnetpm_a = Convert.ToDecimal(dv[i]["fnetpm_a"]),
                        fernpm_fa = Convert.ToDecimal(dv[i]["fernpm_fa"]),
                        fcomamt_a = Convert.ToDecimal(dv[i]["fcomamt_a"]), fexpcom_fa = Convert.ToDecimal(dv[i]["fexpcom_fa"]),
                        fricom_a = Convert.ToDecimal(dv[i]["fricom_a"]), ericom_fa = Convert.ToDecimal(dv[i]["ericom_fa"]),
                        fnetcom_a = Convert.ToDecimal(dv[i]["fnetcom_a"]), enetcom_fa = Convert.ToDecimal(dv[i]["enetcom_fa"]);

                Decimal fgpm_y = Convert.ToDecimal(dv[i]["fgpm_y"]), gernpm_fy = Convert.ToDecimal(dv[i]["gernpm_fy"]),
                        fripm_y = Convert.ToDecimal(dv[i]["fripm_y"]), rernpm_fy = Convert.ToDecimal(dv[i]["rernpm_fy"]),
                        fxolpm_y = Convert.ToDecimal(dv[i]["fxolpm_y"]), fricost_y = Convert.ToDecimal(dv[i]["fricost_y"]),
                        xernpm_fy = Convert.ToDecimal(dv[i]["xernpm_fy"]), fxripm_y = Convert.ToDecimal(dv[i]["fxripm_y"]),
                        rxernpm_fy = Convert.ToDecimal(dv[i]["rxernpm_fy"]), fnetpm_y = Convert.ToDecimal(dv[i]["fnetpm_y"]),
                        fernpm_fy = Convert.ToDecimal(dv[i]["fernpm_fy"]),
                        fcomamt_y = Convert.ToDecimal(dv[i]["fcomamt_y"]), fexpcom_fy = Convert.ToDecimal(dv[i]["fexpcom_fy"]),
                        fricom_y = Convert.ToDecimal(dv[i]["fricom_y"]), ericom_fy = Convert.ToDecimal(dv[i]["ericom_fy"]),
                        fnetcom_y = Convert.ToDecimal(dv[i]["fnetcom_y"]), enetcom_fy = Convert.ToDecimal(dv[i]["enetcom_fy"]);

                dv[i]["gunern_fa"] = fgpm_a - gernpm_fa;
                dv[i]["runern_fa"] = fripm_a - rernpm_fa;
                dv[i]["xunern_fa"] = fxolpm_a+fricost_a-xernpm_fa;
                dv[i]["rxunern_fa"] = fxripm_a - rxernpm_fa;
                dv[i]["nunern_fa"] = fnetpm_a - fernpm_fa;

                dv[i]["guncom_fa"] = fcomamt_a - fexpcom_fa;
                dv[i]["runcom_fa"] = fricom_a - ericom_fa;
                dv[i]["nuncom_fa"] = fnetcom_a - enetcom_fa;

                String curpath, ncurpath;

                if (fnetpm_a == 0.00m)
                    curpath = "1";
                else if ((fnetpm_a > 0.00m && fnetpm_a >= fernpm_fy) || (fnetpm_a < 0.00m && fnetpm_a <= fernpm_fy))
                    curpath = "2";
                else
                    curpath = "3";

                if ((fnetpm_a > fernpm_fy && fnetpm_a > 0) || (fnetpm_a <= fernpm_fy && fnetpm_a < 0))
                    ncurpath = "2";
                else
                    ncurpath = "1";

                if (curpath == "1")
                {
                    dv[i]["gcunern"] = 0.00m;
                    dv[i]["rcunern"] = 0.00m;
                    dv[i]["xcunern"] = 0.00m;
                    dv[i]["rxcunern"] = 0.00m;
                    dv[i]["ncunern"] = 0.00m;

                    dv[i]["gcuncom"] = 0.00m;
                    dv[i]["rcuncom"] = 0.00m;
                    dv[i]["ncuncom"] = 0.00m;
                }

                if (curpath == "2")
                {
                    dv[i]["gcunern"] = gernpm_fy-gernpm_fa;
                    dv[i]["rcunern"] = rernpm_fy - rernpm_fa;
                    dv[i]["xcunern"] = xernpm_fy - xernpm_fa;
                    dv[i]["rxcunern"] = rxernpm_fy - rxernpm_fa;
                    dv[i]["ncunern"] = fernpm_fy - fernpm_fa;

                    dv[i]["gcuncom"] = fexpcom_fy - fexpcom_fa;
                    dv[i]["rcuncom"] = ericom_fy - ericom_fa;
                    dv[i]["ncuncom"] = enetcom_fy - enetcom_fa;
                }

                if (curpath == "3")
                {
                    dv[i]["gcunern"] = fgpm_a - gernpm_fa;
                    dv[i]["rcunern"] = fripm_a - rernpm_fa;
                    dv[i]["xcunern"] = fxolpm_a+fricost_a - xernpm_fa;
                    dv[i]["rxcunern"] = fxripm_a - rxernpm_fa;
                    dv[i]["ncunern"] = fnetpm_a - fernpm_fa;

                    dv[i]["gcuncom"] = fcomamt_a - fexpcom_fa;
                    dv[i]["rcuncom"] = fricom_a - ericom_fa;
                    dv[i]["ncuncom"] = fnetcom_a - enetcom_fa;
                }

                if (ncurpath == "1")
                {
                    dv[i]["gnunern"] = 0.00m;
                    dv[i]["rnunern"] = 0.00m;
                    dv[i]["xnunern"] = 0.00m;
                    dv[i]["rxnunern"] = 0.00m;
                    dv[i]["nnunern"] = 0.00m;

                    dv[i]["gnuncom"] = 0.00m;
                    dv[i]["rnuncom"] = 0.00m;
                    dv[i]["nnuncom"] = 0.00m;
                }

                
                
                
                if (ncurpath == "2")
                {
                    dv[i]["gnunern"] = fgpm_a - gernpm_fy;
                    dv[i]["rnunern"] = fripm_a - rernpm_fy;
                    dv[i]["xnunern"] = fxolpm_a + fricost_a - xernpm_fy;
                    dv[i]["rxnunern"] = fxripm_a - rxernpm_fy;
                    dv[i]["nnunern"] = fnetpm_a - fernpm_fy;

                    dv[i]["gnuncom"] = fcomamt_a - fexpcom_fy;
                    dv[i]["rnuncom"] = fricom_a - ericom_fy;
                    dv[i]["nnuncom"] = fnetcom_a - enetcom_fy;
                }
            }

            dv.Sort = "fclsseq,fpolno,fendtno";

            DataTable dtresult = dv.ToTable(false, "fbus","fclass","fclsseq","fclsname","fpolno", "fendtno","fefffr", "feffto", 
                      "fpolday","fgpm_p","fgpm_a","fripm_a","fnetpm_a","fexpday_a","fexpday_y","gernpm_fa","rernpm_fa",
                      "fernpm_fa", "gernpm_fy", "rernpm_fy", "fernpm_fy","gunern_fa","runern_fa","xunern_fa","rxunern_fa",
                      "nunern_fa","gcunern","gnunern","rcunern","rnunern","xcunern","xnunern","rxcunern","rxnunern","ncunern",
                      "nnunern","guncom_fa","runcom_fa","nuncom_fa","gcuncom","gnuncom","rcuncom","rnuncom","ncuncom","nnuncom",
                      "finsd","fclnt","fsclnt","fdbtr","fcdesc"
                      );

            return dtresult;
        }

        public static DataTable GenUnErnSummary(DataTable dtsrc)
        {
            DataTable dt = new DataTable();

            dt.Columns.Add("fclsseq", typeof(String));
            dt.Columns.Add("fclass", typeof(String));
            dt.Columns.Add("fclsname", typeof(String));

            DataColumn gunern_faCol = dt.Columns.Add("gunern_fa", typeof(Decimal)),
                       runern_faCol = dt.Columns.Add("runern_fa", typeof(Decimal)),
                       xunern_faCol = dt.Columns.Add("xunern_fa", typeof(Decimal)),
                       rxunern_faCol = dt.Columns.Add("rxunern_fa", typeof(Decimal)),
                       nunern_faCol = dt.Columns.Add("nunern_fa", typeof(Decimal)),
                       gcunernCol = dt.Columns.Add("gcunern", typeof(Decimal)),
                       gnunernCol = dt.Columns.Add("gnunern", typeof(Decimal)),
                       rcunernCol = dt.Columns.Add("rcunern", typeof(Decimal)),
                       rnunernCol = dt.Columns.Add("rnunern", typeof(Decimal)),
                       xcunernCol = dt.Columns.Add("xcunern", typeof(Decimal)),
                       xnunernCol = dt.Columns.Add("xnunern", typeof(Decimal)),
                       rxcunernCol = dt.Columns.Add("rxcunern", typeof(Decimal)),
                       rxnunernCol = dt.Columns.Add("rxnunern", typeof(Decimal)),
                       ncunernCol = dt.Columns.Add("ncunern", typeof(Decimal)),
                       nnunernCol = dt.Columns.Add("nnunern", typeof(Decimal)),
                       guncom_faCol = dt.Columns.Add("guncom_fa", typeof(Decimal)),
                       runcom_faCol = dt.Columns.Add("runcom_fa", typeof(Decimal)),
                       nuncom_faCol = dt.Columns.Add("nuncom_fa", typeof(Decimal)),
                       gcuncomCol = dt.Columns.Add("gcuncom", typeof(Decimal)),
                       gnuncomCol = dt.Columns.Add("gnuncom", typeof(Decimal)),
                       rcuncomCol = dt.Columns.Add("rcuncom", typeof(Decimal)),
                       rnuncomCol = dt.Columns.Add("rnuncom", typeof(Decimal)),
                       ncuncomCol = dt.Columns.Add("ncuncom", typeof(Decimal)),
                       nnuncomCol = dt.Columns.Add("nnuncom", typeof(Decimal));

            gunern_faCol.DefaultValue = 0.00m;
            runern_faCol.DefaultValue = 0.00m;
            xunern_faCol.DefaultValue = 0.00m;
            rxunern_faCol.DefaultValue = 0.00m;
            nunern_faCol.DefaultValue = 0.00m;
            gcunernCol.DefaultValue = 0.00m;
            gnunernCol.DefaultValue = 0.00m;
            rcunernCol.DefaultValue = 0.00m;
            rnunernCol.DefaultValue = 0.00m;
            xcunernCol.DefaultValue = 0.00m;
            xnunernCol.DefaultValue = 0.00m;
            rxcunernCol.DefaultValue = 0.00m;
            rxnunernCol.DefaultValue = 0.00m;
            ncunernCol.DefaultValue = 0.00m;
            nnunernCol.DefaultValue = 0.00m;
            guncom_faCol.DefaultValue = 0.00m;
            runcom_faCol.DefaultValue = 0.00m;
            nuncom_faCol.DefaultValue = 0.00m;
            gcuncomCol.DefaultValue = 0.00m;
            gnuncomCol.DefaultValue = 0.00m;
            rcuncomCol.DefaultValue = 0.00m;
            rnuncomCol.DefaultValue = 0.00m;
            ncuncomCol.DefaultValue = 0.00m;
            nnuncomCol.DefaultValue = 0.00m;

            DataView dv = dt.DefaultView;

            foreach (DataRow dr in dtsrc.Rows)
            {
                String fclsseq = dr["fclsseq"].ToString().Trim();
                dv.RowFilter = String.Format("fclsseq = '{0}'", fclsseq);

                if (dv.Count == 0)
                {
                    DataRowView rowView = dv.AddNew();

                    rowView["fclsseq"] = dr["fclsseq"];
                    rowView["fclass"] = dr["fclass"];
                    rowView["fclsname"] = dr["fclsname"];

                    rowView.EndEdit();
                }

                dv[0]["gunern_fa"] = Convert.ToDecimal(dv[0]["gunern_fa"]) + Convert.ToDecimal(dr["gunern_fa"]);
                dv[0]["runern_fa"] = Convert.ToDecimal(dv[0]["runern_fa"]) + Convert.ToDecimal(dr["runern_fa"]);
                dv[0]["xunern_fa"] = Convert.ToDecimal(dv[0]["xunern_fa"]) + Convert.ToDecimal(dr["xunern_fa"]);
                dv[0]["rxunern_fa"] = Convert.ToDecimal(dv[0]["rxunern_fa"]) + Convert.ToDecimal(dr["rxunern_fa"]);
                dv[0]["nunern_fa"] = Convert.ToDecimal(dv[0]["nunern_fa"]) + Convert.ToDecimal(dr["nunern_fa"]);
                dv[0]["gcunern"] = Convert.ToDecimal(dv[0]["gcunern"]) + Convert.ToDecimal(dr["gcunern"]);
                dv[0]["gnunern"] = Convert.ToDecimal(dv[0]["gnunern"]) + Convert.ToDecimal(dr["gnunern"]);
                dv[0]["rcunern"] = Convert.ToDecimal(dv[0]["rcunern"]) + Convert.ToDecimal(dr["rcunern"]);
                dv[0]["rnunern"] = Convert.ToDecimal(dv[0]["rnunern"]) + Convert.ToDecimal(dr["rnunern"]);
                dv[0]["xcunern"] = Convert.ToDecimal(dv[0]["xcunern"]) + Convert.ToDecimal(dr["xcunern"]);
                dv[0]["xnunern"] = Convert.ToDecimal(dv[0]["xnunern"]) + Convert.ToDecimal(dr["xnunern"]);
                dv[0]["rxcunern"] = Convert.ToDecimal(dv[0]["rxcunern"]) + Convert.ToDecimal(dr["rxcunern"]);
                dv[0]["rxnunern"] = Convert.ToDecimal(dv[0]["rxnunern"]) + Convert.ToDecimal(dr["rxnunern"]);
                dv[0]["ncunern"] = Convert.ToDecimal(dv[0]["ncunern"]) + Convert.ToDecimal(dr["ncunern"]);
                dv[0]["nnunern"] = Convert.ToDecimal(dv[0]["nnunern"]) + Convert.ToDecimal(dr["nnunern"]);
                dv[0]["guncom_fa"] = Convert.ToDecimal(dv[0]["guncom_fa"]) + Convert.ToDecimal(dr["guncom_fa"]);
                dv[0]["runcom_fa"] = Convert.ToDecimal(dv[0]["runcom_fa"]) + Convert.ToDecimal(dr["runcom_fa"]);
                dv[0]["nuncom_fa"] = Convert.ToDecimal(dv[0]["nuncom_fa"]) + Convert.ToDecimal(dr["nuncom_fa"]);
                dv[0]["gcuncom"] = Convert.ToDecimal(dv[0]["gcuncom"]) + Convert.ToDecimal(dr["gcuncom"]);
                dv[0]["gnuncom"] = Convert.ToDecimal(dv[0]["gnuncom"]) + Convert.ToDecimal(dr["gnuncom"]);
                dv[0]["rcuncom"] = Convert.ToDecimal(dv[0]["rcuncom"]) + Convert.ToDecimal(dr["rcuncom"]);
                dv[0]["rnuncom"] = Convert.ToDecimal(dv[0]["rnuncom"]) + Convert.ToDecimal(dr["rnuncom"]);
                dv[0]["ncuncom"] = Convert.ToDecimal(dv[0]["ncuncom"]) + Convert.ToDecimal(dr["ncuncom"]);
                dv[0]["nnuncom"] = Convert.ToDecimal(dv[0]["nnuncom"]) + Convert.ToDecimal(dr["nnuncom"]);
            }

            dv.RowFilter = "";

            return dt;
        }
    }

    class AccDataFunc
    {
    }
}
