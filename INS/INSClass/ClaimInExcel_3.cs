using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;

using Excel = Microsoft.Office.Interop.Excel;

namespace INS.INSClass
{
    partial class ClaimInExcel
    {
        public static Int16 WsColTitleECCOHL(Excel.Worksheet xlWorkSheet, DataTable dt,
                string optid, string z_company, string z_title, string z_currency)
        {
            Int16 i = 1;

            xlWorkSheet.Cells.Font.Name = "arial";
            xlWorkSheet.Cells.Font.Size = 8;

            xlWorkSheet.Columns["A"].ColumnWidth = 20;
            xlWorkSheet.Columns["B"].ColumnWidth = 40;
            xlWorkSheet.Columns["C"].ColumnWidth = 8;
            xlWorkSheet.Columns["D:F"].ColumnWidth = 12;
            xlWorkSheet.Columns["G:I"].ColumnWidth = 8;
            xlWorkSheet.Columns["J:L"].ColumnWidth = 12;
            xlWorkSheet.Columns["C"].NumberFormat = "###,##0";
            xlWorkSheet.Columns["D:F"].NumberFormat = "#,###,##0.00";
            xlWorkSheet.Columns["G:I"].NumberFormat = "###,##0";
            xlWorkSheet.Columns["J:L"].NumberFormat = "#,###,##0.00";
 
            xlWorkSheet.Columns["A:B"].HorizontalAlignment = Excel.XlHAlign.xlHAlignLeft; // 2
            xlWorkSheet.get_Range("K3", "K3").HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4
            xlWorkSheet.get_Range("L3", "L3").HorizontalAlignment = Excel.XlHAlign.xlHAlignLeft; // 2
            xlWorkSheet.get_Range("C6", "L6").HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4
            xlWorkSheet.get_Range("A6:L6").Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 7;

            xlWorkSheet.Cells[i++, 1] = z_company;
            xlWorkSheet.Cells[i++, 1] = z_title;
            xlWorkSheet.Cells[i, 1] = z_currency;
            xlWorkSheet.Cells[i, 11] = "日期: ";
            xlWorkSheet.Cells[i++, 12] = DateTime.Now.ToString("yyyy") + "年" +
                DateTime.Now.ToString("mm") + "月" + DateTime.Now.ToString("dd")+"日";

            if (dt.Rows.Count == 0)
            {
                i++;
                xlWorkSheet.Cells[i, 1] = "No record found!";
                xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;
                return i;
            }

            i++;
            xlWorkSheet.Cells[i, 1] = "保單號碼/";
            xlWorkSheet.Cells[i + 1, 1] = "保期(包括保養期)";
            xlWorkSheet.Cells[i, 2] = "項目/";
            xlWorkSheet.Cells[i + 1, 2] = "合約金額/保費(成交額)";

            xlWorkSheet.get_Range(String.Format("C{0}:F{0}", i)).Merge();
            xlWorkSheet.get_Range(String.Format("G{0}:L{0}", i)).Merge();

            xlWorkSheet.get_Range(String.Format("C{0}:L{0}", i)).HorizontalAlignment = Excel.XlHAlign.xlHAlignCenter; //3
            xlWorkSheet.Cells[i, 3] = "<--------------------      本期個案      -------------------->";
            xlWorkSheet.Cells[i, 7] = "<-------------------------------------       累積個案       ------------------------------------->";

            xlWorkSheet.Cells[i + 1, 3]  = "賠案";
            xlWorkSheet.Cells[i + 1, 4]  = "賠償準備金";
            xlWorkSheet.Cells[i + 1, 5]  = "己賠金額";
            xlWorkSheet.Cells[i + 1, 6]  = "未賠金額";
            xlWorkSheet.Cells[i + 1, 7]  = "賠案";
            xlWorkSheet.Cells[i + 1, 8]  = "已結案";
            xlWorkSheet.Cells[i + 1, 9]  = "未結案";
            xlWorkSheet.Cells[i + 1,10] = "賠償準備金";
            xlWorkSheet.Cells[i + 1,11] = "己賠金額";
            xlWorkSheet.Cells[i + 1, 12] = "未賠金額";

            i = WsClmECCOHL(xlWorkSheet, dt, optid, i+=3);
 
            return i;
        }

        public static Int16 WsClmECCOHL(Excel.Worksheet xlWorkSheet, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fclsseq = "";

            decimal ln_tmcount = 0, ln_tmres = 0, ln_tmpay = 0, ln_tmos = 0, ln_tycount = 0;
            decimal ln_tyclosed = 0, ln_tyopen = 0, ln_tyres = 0, ln_typay = 0, ln_tyos = 0;

            foreach (DataRow dr in dt.Rows)
            {
                decimal fupdsum = Convert.ToDecimal(dr["fupdsum"]);
                decimal fgpm = Convert.ToDecimal(dr["fgpm"]);
                decimal mcount = Convert.ToDecimal(dr["mcount"]);
                decimal mres = Convert.ToDecimal(dr["mres"]);
                decimal mpay = Convert.ToDecimal(dr["mpay"]);
                decimal mos = Convert.ToDecimal(dr["mos"]);
                decimal ycount = Convert.ToDecimal(dr["ycount"]);
                decimal yclosed = Convert.ToDecimal(dr["yclosed"]);
                decimal yopen = Convert.ToDecimal(dr["yopen"]);
                decimal yres = Convert.ToDecimal(dr["yres"]);
                decimal ypay = Convert.ToDecimal(dr["ypay"]);
                decimal yos = Convert.ToDecimal(dr["yos"]);

                k = 1;
                xlWorkSheet.Cells[iRow, k] = dr["fpolno"];
                xlWorkSheet.Cells[iRow+1, k++] = Convert.ToDateTime(dr["fincfr"]).ToString("dd/mm/yyyy")+
                    " - "+Convert.ToDateTime(dr["fincto"]).ToString("dd/mm/yyyy");
                xlWorkSheet.Cells[iRow, k] = dr["fsite"];
                xlWorkSheet.Cells[iRow+1, k++] = "'"+fupdsum.ToString("#,###.00")+"/"+fgpm.ToString("#,###.00");

                xlWorkSheet.Cells[iRow, k++]    = mcount;
                xlWorkSheet.Cells[iRow, k++]    = mres;
                xlWorkSheet.Cells[iRow, k++]    = mpay;
                xlWorkSheet.Cells[iRow, k++]    = mos;
                xlWorkSheet.Cells[iRow, k++]    = ycount;
                xlWorkSheet.Cells[iRow, k++]    = yclosed;
                xlWorkSheet.Cells[iRow, k++]    = yopen;
                xlWorkSheet.Cells[iRow, k++]   = yres;
                xlWorkSheet.Cells[iRow, k++]   = ypay;
                xlWorkSheet.Cells[iRow, k++] = yos;

                ln_tmcount  = ln_tmcount + mcount;
                ln_tmres    = ln_tmres + mres;
                ln_tmpay    = ln_tmpay + mpay;
                ln_tmos     = ln_tmos + mos;
                ln_tycount  = ln_tycount + ycount;
                ln_tyclosed = ln_tyclosed + yclosed;
                ln_tyopen   = ln_tyopen + yopen;
                ln_tyres    = ln_tyres + yres;
                ln_typay    = ln_typay + ypay;
                ln_tyos = ln_tyos + yos;

                    iRow += 3;
            }

            k = 2;
            xlWorkSheet.Cells[iRow, k++]    = "總計:";
            xlWorkSheet.Cells[iRow, k++]    = ln_tmcount;
            xlWorkSheet.Cells[iRow, k++]    = ln_tmres;
            xlWorkSheet.Cells[iRow, k++]    = ln_tmpay;
            xlWorkSheet.Cells[iRow, k++]    = ln_tmos;
            xlWorkSheet.Cells[iRow, k++]    = ln_tycount;
            xlWorkSheet.Cells[iRow, k++]    = ln_tyclosed;
            xlWorkSheet.Cells[iRow, k++]    = ln_tyopen;
            xlWorkSheet.Cells[iRow, k++]   = ln_tyres;
            xlWorkSheet.Cells[iRow, k++]   = ln_typay;
            xlWorkSheet.Cells[iRow, k++] = ln_tyos;

            xlWorkSheet.get_Range(String.Format("B{0}", iRow)).Columns.HorizontalAlignment = Excel.XlHAlign.xlHAlignRight; // 4
 
            //xlWorkSheet.get_Range(String.Format("C{0}", iRow), String.Format("I{0}", iRow)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 9;

            return iRow;
        }
    }
}
