using System;
using System.Collections.Generic;
using System.Text;


namespace INS.INSClass
{
    /// <summary>
    /// </summary>
    public class NumGetString
    {
        private static String[] Ls_ShZ = { "零", "壹", "貳", "叁", "肆", "伍", "陸", "柒", "捌", "玖", "拾" };
        private static String[] Ls_DW_Zh = { "圓", "拾", "佰", "仟", "萬", "拾", "佰", "仟", "亿", "拾", "佰", "仟", "萬" };
        private static String[] Num_DW = { "", "拾", "佰", "仟", "萬", "拾", "佰", "仟", "亿", "拾", "佰", "仟", "萬" };
        private static String[] Ls_DW_X = { "角", "仙" };


        public static String NumGetStr(Double Num)
        {
            Boolean iXSh_bool = false;
            Boolean iZhSh_bool = true;

            string NumStr;
            string NumStr_Zh;
            string NumSr_X = "";
            string NumStr_DQ;
            string NumStr_R = "";

            Num = Math.Round(Num, 2);

            if (Num < 0)
                return "NO Cheque";
            if (Num > 9999999999999.99)
                return "Over Range";
            if (Num == 0)
                return Ls_ShZ[0];

            if (Num < 1.00)
                iZhSh_bool = false;

            NumStr = Num.ToString();

            NumStr_Zh = NumStr;
            if (NumStr_Zh.Contains("."))
            {
                NumStr_Zh = NumStr.Substring(0, NumStr.IndexOf("."));
                NumSr_X = NumStr.Substring((NumStr.IndexOf(".") + 1), (NumStr.Length - NumStr.IndexOf(".") - 1));
                iXSh_bool = true;
            }


            if (NumSr_X == "" || int.Parse(NumSr_X) <= 0)
            {
                iXSh_bool = false;
            }

            if (iZhSh_bool)
            {
                NumStr_Zh = Reversion_Str(NumStr_Zh);

                for (int a = 0; a < NumStr_Zh.Length; a++)
                {
                    NumStr_DQ = NumStr_Zh.Substring(a, 1);
                    if (int.Parse(NumStr_DQ) != 0)
                        if (Ls_ShZ[int.Parse(NumStr_DQ)] == "壹" && Ls_DW_Zh[a] =="拾"){
                            NumStr_R = Ls_DW_Zh[a] + NumStr_R;
                        }
                        else { NumStr_R = Ls_ShZ[int.Parse(NumStr_DQ)] + Ls_DW_Zh[a] + NumStr_R; }
                    else if (a == 0 || a == 4 || a == 8)
                    {
                        if (NumStr_Zh.Length > 8 && a == 4)
                            continue;
                        NumStr_R = Ls_DW_Zh[a] + NumStr_R;
                    }
                    else if (int.Parse(NumStr_Zh.Substring(a - 1, 1)) != 0)
                        NumStr_R = Ls_ShZ[int.Parse(NumStr_DQ)] + NumStr_R;

                }

                if (!iXSh_bool)
                    return NumStr_R + "正";

            }

            for (int b = 0; b < NumSr_X.Length; b++)
            {
                NumStr_DQ = NumSr_X.Substring(b, 1);
                if (int.Parse(NumStr_DQ) != 0)
                    NumStr_R += Ls_ShZ[int.Parse(NumStr_DQ)] + Ls_DW_X[b];
                else if (b != 1 && iZhSh_bool)
                    NumStr_R += Ls_ShZ[int.Parse(NumStr_DQ)];
            }

            return NumStr_R;

        }


        public static String LowercaseGetCap(String NumStr,Boolean Dw)
        {
            String CapStr="";
            String NumStr_LS;

            if (NumStr == String.Empty)
                return String.Empty;

            if (Dw)
                NumStr = Reversion_Str(NumStr);

            try
            {
                for (Int32 c = 0; c < NumStr.Length; c++)
                {
                    NumStr_LS = NumStr.Substring(c, 1);
                    if (Dw)
                    {
                        if (int.Parse(NumStr_LS) != 0)
                            CapStr = Ls_ShZ[int.Parse(NumStr_LS)] + Num_DW[c] + CapStr;
                        else if (c == 0 || c == 4 || c == 8)
                        {
                            if (NumStr_LS.Length > 8 && c == 4)
                                continue;
                            CapStr = Num_DW[c] + CapStr;
                        }
                        else if (int.Parse(NumStr.Substring(c - 1, 1)) != 0)
                            CapStr = Ls_ShZ[int.Parse(NumStr_LS)] + CapStr;
                    }
                    else
                        CapStr += Ls_ShZ[int.Parse(NumStr_LS)];
                }

                return CapStr;
            }
            catch (Exception Err)
            {
                return "Error: "+Err.Message;
            }
        }


        private static String Reversion_Str(String Rstr)
        {
            Char[] LS_Str = Rstr.ToCharArray();
            Array.Reverse(LS_Str);
            String ReturnSte = "";
            ReturnSte = new String(LS_Str);

            return ReturnSte;
        }
    }
}
