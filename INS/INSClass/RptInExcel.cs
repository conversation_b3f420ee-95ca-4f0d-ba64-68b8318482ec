using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;

using Excel = Microsoft.Office.Interop.Excel;

namespace INS.INSClass
{
    partial class RptInExcel
    {
        public static void releaseObject(object obj)
        {
            try
            {
                System.Runtime.InteropServices.Marshal.ReleaseComObject(obj);
                obj = null;
            }
            catch (Exception ex)
            {
                obj = null;
                MessageBox.Show("Exception Occured while releasing object " + ex.ToString());
            }
            finally
            {
                GC.Collect();
            }
        }
        
        public static Int16 WsHeader(Excel.Worksheet xlWorkSheet, 
            string optid, string z_company, string z_title, string z_currency, string z_period)
        {
            Int16 i = 1;

            xlWorkSheet.Cells[i++, 1] = z_company;
            xlWorkSheet.Cells[i++, 1] = z_title;

            if (optid == "prmunern")
                xlWorkSheet.Cells[i++, 1] = z_period;

            if (optid == "prmbord")
                xlWorkSheet.Cells[i++, 1] = z_period;

            if (optid == "prmstat")
                xlWorkSheet.Cells[i++, 1] = z_period;

            xlWorkSheet.Cells[i, 1] = z_currency;

            return i;
        }

        public static Int16 WsColTitlePrmGrnd(Excel.Worksheet xlWorkSheet, DateTime vlastasat,
                                        DateTime vthisasat, Boolean noDetail, string optid, Int16 startRow)
        {
            Int16 i = startRow;
            
            if (optid == "prmgrnd")
            {
                xlWorkSheet.Cells.Font.Name = "arial";
                xlWorkSheet.Cells.Font.Size = 8;

                xlWorkSheet.Columns["A:C"].ColumnWidth = 11;
                xlWorkSheet.Columns["D"].ColumnWidth = 13;
                xlWorkSheet.Columns["E:L"].ColumnWidth = 12;
                xlWorkSheet.Columns["M:S"].ColumnWidth = 11;


                xlWorkSheet.Columns["D:S"].HorizontalAlignment = 4;
                xlWorkSheet.Columns["D:S"].NumberFormat = "#,###,###.00";

                xlWorkSheet.get_Range("S3", "S3").HorizontalAlignment = 2;
                xlWorkSheet.get_Range("S3", "S3").NumberFormat = "yyyy/mm/dd";

                xlWorkSheet.Cells[i, 18] = "Date: ";
                xlWorkSheet.Cells[i, 19] = DateTime.Now;

                if (noDetail)
                {
                    i += 2;
                    xlWorkSheet.Cells[i, 1] = "No Record Found!";
                    xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                    i++;

                    return i;
                }

                Int16 k = 1;

                xlWorkSheet.get_Range("A4", "S7").Font.Underline = 2;
                xlWorkSheet.Cells[i + 4, k++] = "Policy No";

                xlWorkSheet.Cells[i + 4, k] = "Endt No";
                k += 2;

                xlWorkSheet.Cells[i + 3, k] = "(1) Gross Premium";
                xlWorkSheet.Cells[i + 4, k++] = "Receivable";

                xlWorkSheet.Cells[i + 2, k] = "Reinsurance";
                xlWorkSheet.Cells[i + 3, k] = "Premium Payable";
                xlWorkSheet.Cells[i + 4, k++] = "(Surplus)";

                xlWorkSheet.Cells[i + 2, k] = "Reinsurance";
                xlWorkSheet.Cells[i + 3, k] = "Premium Payable";
                xlWorkSheet.Cells[i + 4, k++] = "(XOL)";

                xlWorkSheet.Cells[i + 2, k] = "Reinsurance";
                xlWorkSheet.Cells[i + 3, k] = "Premium Payable";
                xlWorkSheet.Cells[i + 4, k++] = "(Fac+Fac Oblig)";

                xlWorkSheet.Cells[i + 4, k++] = "Other R/I Cost";

                xlWorkSheet.Cells[i + 2, k] = "(2) Reinsurance";
                xlWorkSheet.Cells[i + 3, k] = "Premium Payable";
                xlWorkSheet.Cells[i + 4, k++] = "+R/I Cost";

                xlWorkSheet.Cells[i + 1, k] = "(3) Premium Net";
                xlWorkSheet.Cells[i + 2, k] = "of Reinsurance";
                xlWorkSheet.Cells[i + 3, k] = "& R/I Cost";
                xlWorkSheet.Cells[i + 4, k++] = "'= (1) - (2)";

                xlWorkSheet.Cells[i + 2, k] = "(4) Unearned";
                xlWorkSheet.Cells[i + 3, k] = "Premium";
                xlWorkSheet.Cells[i + 4, k++] = "Adjustments";

                xlWorkSheet.Cells[i + 2, k] = "(5) Net Earned";
                xlWorkSheet.Cells[i + 3, k] = "Premium =";
                xlWorkSheet.Cells[i + 4, k++] = "(3) - (4)";

                xlWorkSheet.Cells[i + 3, k] = "Gross Comm";
                xlWorkSheet.Cells[i + 4, k++] = "Payable";

                xlWorkSheet.Cells[i + 3, k] = "R/I Comm";
                xlWorkSheet.Cells[i + 4, k++] = "(Surplus)";

                xlWorkSheet.Cells[i + 3, k] = "R/I Comm";
                xlWorkSheet.Cells[i + 4, k++] = "(Fac+Fac Oblig)";

                xlWorkSheet.Cells[i + 4, k++] = "Total R/I Comm";

                xlWorkSheet.Cells[i + 4, k++] = "Net Comm";

                xlWorkSheet.Cells[i + 3, k] = "Change in";
                xlWorkSheet.Cells[i + 4, k++] = "Net DAC";

                xlWorkSheet.Cells[i + 3, k] = "Net Comm";
                xlWorkSheet.Cells[i + 4, k] = "Payable";

                i += 6;
            }
            return i;
        }

        public static Int16 WsColTitleDaqCost(Excel.Worksheet xlWorkSheet, DateTime vlastasat,
                            DateTime vthisasat, Boolean noDetail, string optid, Int16 startRow)
        {
            Int16 i = startRow;

            if (optid == "daqcost")
            {
                xlWorkSheet.Cells.Font.Name = "arial";
                xlWorkSheet.Cells.Font.Size = 8;

                xlWorkSheet.Columns["A:B"].ColumnWidth = 11;
                xlWorkSheet.Columns["C:M"].ColumnWidth = 13;

                xlWorkSheet.Columns["C:M"].HorizontalAlignment = 4;
                xlWorkSheet.Columns["C:M"].NumberFormat = "#,###,###.00";

                xlWorkSheet.get_Range("M3", "M3").HorizontalAlignment = 2;
                xlWorkSheet.get_Range("M3", "M3").NumberFormat = "yyyy/mm/dd";

                xlWorkSheet.Cells[i, 12] = "Date: ";
                xlWorkSheet.Cells[i, 13] = DateTime.Now;

                if (noDetail)
                {
                    i += 2;
                    xlWorkSheet.Cells[i, 1] = "No Record Found!";
                    xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                    i++;

                    return i;
                }

                i += 2;
                Int16 k = 1;

                xlWorkSheet.get_Range("A5", "M6").Font.Underline = 2;
                xlWorkSheet.Cells[i + 1, k++] = "Policy No";

                xlWorkSheet.Cells[i + 1, k++] = "Endt No";

                xlWorkSheet.Cells[i, k] = "Gross Comm";
                xlWorkSheet.Cells[i + 1, k++] = "Payable";

                xlWorkSheet.Cells[i, k] = "Change in";
                xlWorkSheet.Cells[i + 1, k++] = "Gross DAC";

                xlWorkSheet.Cells[i, k] = "Adjusted Comm";
                xlWorkSheet.Cells[i + 1, k++] = "Payable";

                xlWorkSheet.Cells[i, k] = "R/I Comm";
                xlWorkSheet.Cells[i + 1, k++] = "(Surplus)";

                xlWorkSheet.Cells[i, k] = "R/I Comm";
                xlWorkSheet.Cells[i + 1, k++] = "(Fac+Fac Oblig)";

                xlWorkSheet.Cells[i + 1, k++] = "Total R/I Comm";

                xlWorkSheet.Cells[i, k] = "Change in DAC";
                xlWorkSheet.Cells[i + 1, k++] = "on R/I Comm";

                xlWorkSheet.Cells[i, k] = "Adjusted Total";
                xlWorkSheet.Cells[i + 1, k++] = "R/I Comm";

                xlWorkSheet.Cells[i + 1, k++] = "Net Comm";

                xlWorkSheet.Cells[i, k] = "Change in";
                xlWorkSheet.Cells[i + 1, k++] = "Net DAC";

                xlWorkSheet.Cells[i, k] = "Net Comm";
                xlWorkSheet.Cells[i + 1, k] = "Payable";

                i += 3;
            }
            return i;
        }

        public static Int16 WsColTitlePrmUnern(Excel.Worksheet xlWorkSheet,Int16 sheetIndex, DateTime vlastasat, 
                                        DateTime vthisasat, Boolean noDetail, string optid, Int16 startRow)
        {
            Int16 i = startRow;

            if (optid == "prmunern" && sheetIndex == 1)
            {
                xlWorkSheet.Cells.Font.Name = "arial";
                xlWorkSheet.Cells.Font.Size = 10;

                xlWorkSheet.Columns["A"].ColumnWidth = 50;
                xlWorkSheet.Columns["B:J"].ColumnWidth = 14;

                xlWorkSheet.Columns["B:J"].HorizontalAlignment = 4;
                xlWorkSheet.Columns["B:J"].NumberFormat = "#,##0.00_)";

                xlWorkSheet.get_Range("J4", "J4").HorizontalAlignment = 2;
                xlWorkSheet.get_Range("J4", "J4").NumberFormat = "yyyy/mm/dd";

                xlWorkSheet.get_Range("B6", "J7").HorizontalAlignment = 3;
                xlWorkSheet.get_Range("B6", "J7").Font.Bold = true;

                xlWorkSheet.Cells[i, 9] = "Date: ";
                xlWorkSheet.Cells[i,10] = DateTime.Now;

                if (noDetail)
                {
                    i += 2;
                    xlWorkSheet.Cells[i, 1] = "No Record Found!";
                    xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                    i++;

                    return i;
                }

                i += 2;
                Int16 k = 2;

                xlWorkSheet.Cells[i, k] = "Gross";
                xlWorkSheet.Cells[i + 1, k++] = "Unearned";
   
                xlWorkSheet.Cells[i, k] = "Reinsurance";
                xlWorkSheet.Cells[i + 1, k++] = "Unearned";

                xlWorkSheet.Cells[i, k] = "Net";
                xlWorkSheet.Cells[i + 1, k++] = "Unearned";

                xlWorkSheet.Cells[i, k] = "Gross";
                xlWorkSheet.Cells[i + 1, k++] = "Current";

                xlWorkSheet.Cells[i, k] = "Gross";
                xlWorkSheet.Cells[i + 1, k++] = "Non Current";

                xlWorkSheet.Cells[i, k] = "Reinsurance";
                xlWorkSheet.Cells[i + 1, k++] = "Current";

                xlWorkSheet.Cells[i, k] = "Reinsurance";
                xlWorkSheet.Cells[i + 1, k++] = "Non Current";

                xlWorkSheet.Cells[i, k] = "Net";
                xlWorkSheet.Cells[i + 1, k++] = "Current";

                xlWorkSheet.Cells[i, k] = "Net";
                xlWorkSheet.Cells[i + 1, k] = "Non Current";

                i += 3;
            }

            if (optid == "prmunern" && sheetIndex == 2)
            {
                xlWorkSheet.Cells.Font.Name = "arial";
                xlWorkSheet.Cells.Font.Size = 8;

                xlWorkSheet.Columns["C:G"].ColumnWidth = 9;
                xlWorkSheet.Columns["H:Z"].ColumnWidth = 12;

                xlWorkSheet.Columns["E:Z"].HorizontalAlignment = 4;
                xlWorkSheet.Columns["C:D"].NumberFormat = "yyyy/mm/dd";
                xlWorkSheet.Columns["E:G"].NumberFormat = "#,##0_)";
                xlWorkSheet.Columns["H:Z"].NumberFormat = "#,##0.00_)";

                xlWorkSheet.get_Range("Z4", "Z4").HorizontalAlignment = 2;
                xlWorkSheet.get_Range("Z4", "Z4").NumberFormat = "yyyy/mm/dd";

                xlWorkSheet.get_Range("C6", "Z8").HorizontalAlignment = 3;
                xlWorkSheet.get_Range("A6", "Z8").Font.Bold = true;

                xlWorkSheet.Cells[i, 25] = "Date: ";
                xlWorkSheet.Cells[i, 26] = DateTime.Now;

                if (noDetail)
                {
                    i += 2;
                    xlWorkSheet.Cells[i, 1] = "No Record Found!";
                    xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                    i++;

                    return i;
                }

                i += 2;
                Int16 k = 1;

                String lc_as1 = vlastasat.ToString("@yyyy/MM/dd"), lc_as2 = vthisasat.ToString("@yyyy/MM/dd");
                
                xlWorkSheet.Cells[i + 2, k++] = "Policy No.";
                xlWorkSheet.Cells[i + 2, k++] = "Endt. No.";

                xlWorkSheet.Cells[i + 2, k++] = "Eff Fr";
                xlWorkSheet.Cells[i + 2, k++] = "Eff To";

                xlWorkSheet.Cells[i + 1, k] = "No. of Days";
                xlWorkSheet.Cells[i + 2, k++] = "Full Period";

                xlWorkSheet.Cells[i + 1, k] = "No. of Days";
                xlWorkSheet.Cells[i + 2, k++] = lc_as1;

                xlWorkSheet.Cells[i + 1, k] = "No. of Days";
                xlWorkSheet.Cells[i + 2, k++] = lc_as2;

                xlWorkSheet.Cells[i + 2, k++] = "Policy Gross";

                xlWorkSheet.Cells[i, k] = "'(1)";
                xlWorkSheet.Cells[i + 1, k] = "Inv Gross Prem";
                xlWorkSheet.Cells[i + 2, k++] = lc_as1;

                xlWorkSheet.Cells[i, k] = "'(2)";
                xlWorkSheet.Cells[i + 1, k] = "Inv R/I Prem";
                xlWorkSheet.Cells[i + 2, k++] = lc_as1;

                xlWorkSheet.Cells[i, k] = "'(3)";
                xlWorkSheet.Cells[i + 1, k] = "Inv Net Prem";
                xlWorkSheet.Cells[i + 2, k++] = lc_as1;

                xlWorkSheet.Cells[i, k] = "'(4)";
                xlWorkSheet.Cells[i + 1, k] = "Gross Earned";
                xlWorkSheet.Cells[i + 2, k++] = lc_as1;

                xlWorkSheet.Cells[i, k] = "'(5)";
                xlWorkSheet.Cells[i + 1, k] = "R/I Earned";
                xlWorkSheet.Cells[i + 2, k++] = lc_as1;

                xlWorkSheet.Cells[i, k] = "'(6)";
                xlWorkSheet.Cells[i + 1, k] = "Net Earned";
                xlWorkSheet.Cells[i + 2, k++] = lc_as1;

                xlWorkSheet.Cells[i, k] = "'(7)";
                xlWorkSheet.Cells[i + 1, k] = "Gross Earned";
                xlWorkSheet.Cells[i + 2, k++] = lc_as2;

                xlWorkSheet.Cells[i, k] = "'(8)";
                xlWorkSheet.Cells[i + 1, k] = "R/I Earned";
                xlWorkSheet.Cells[i + 2, k++] = lc_as2;

                xlWorkSheet.Cells[i, k] = "'(9)";
                xlWorkSheet.Cells[i + 1, k] = "Net Earned";
                xlWorkSheet.Cells[i + 2, k++] = lc_as2;

                xlWorkSheet.Cells[i, k] = "(a) = (1) - (4)";
                xlWorkSheet.Cells[i + 1, k] = "Gross Unearned";
                xlWorkSheet.Cells[i + 2, k++] = lc_as1;

                xlWorkSheet.Cells[i, k] = "(b) = (2) - (5)";
                xlWorkSheet.Cells[i + 1, k] = "R/I Unearned";
                xlWorkSheet.Cells[i + 2, k++] = lc_as1;

                xlWorkSheet.Cells[i, k] = "(c) = (3) - (6)";
                xlWorkSheet.Cells[i + 1, k] = "Net Unearned";
                xlWorkSheet.Cells[i + 2, k++] = lc_as1;

                xlWorkSheet.Cells[i, k] = "(d) = (7) - (4)";
                xlWorkSheet.Cells[i + 1, k] = "Gross Unearned";
                xlWorkSheet.Cells[i + 2, k++] = "Current";

                xlWorkSheet.Cells[i, k] = "(e) = (a) - (d)";
                xlWorkSheet.Cells[i + 1, k] = "Gross Unearned";
                xlWorkSheet.Cells[i + 2, k++] = "Non Current";

                xlWorkSheet.Cells[i, k] = "(f) = (8) - (5)";
                xlWorkSheet.Cells[i + 1, k] = "R/I Unearned";
                xlWorkSheet.Cells[i + 2, k++] = "Current";

                xlWorkSheet.Cells[i, k] = "(g) = (b) - (f)";
                xlWorkSheet.Cells[i + 1, k] = "R/I Unearned";
                xlWorkSheet.Cells[i + 2, k++] = "Non Current";

                xlWorkSheet.Cells[i, k] = "(h) = (9) - (6)";
                xlWorkSheet.Cells[i + 1, k] = "Net Unearned";
                xlWorkSheet.Cells[i + 2, k++] = "Current";

                xlWorkSheet.Cells[i, k] = "(i) = (c) - (h)";
                xlWorkSheet.Cells[i + 1, k] = "Net Unearned";
                xlWorkSheet.Cells[i + 2, k++] = "Non Current";

                i += 4;
            }

            return i;
        }

        public static Int16 WsColTitlePrmUniv(Excel.Worksheet xlWorkSheet, Int16 sheetIndex, DateTime vlastasat,
                                DateTime vthisasat, Boolean noDetail, string optid, Int16 startRow)
        {
            Int16 i = startRow;

            if (optid == "prmuniv" && sheetIndex == 1)
            {
                xlWorkSheet.Cells.Font.Name = "arial";
                xlWorkSheet.Cells.Font.Size = 10;

                xlWorkSheet.Columns["A"].ColumnWidth = 50;
                xlWorkSheet.Columns["B:G"].ColumnWidth = 14;

                xlWorkSheet.Columns["B:G"].HorizontalAlignment = 4; ;
                xlWorkSheet.Columns["B:G"].NumberFormat = "#,##0.00_)";

                xlWorkSheet.get_Range("G3", "G3").HorizontalAlignment = 2;
                xlWorkSheet.get_Range("G3", "G3").NumberFormat = "yyyy/mm/dd";

                xlWorkSheet.get_Range("B5", "G6").HorizontalAlignment = 3;
                xlWorkSheet.get_Range("B5", "G6").Font.Bold = true;

                xlWorkSheet.Cells[i, 6] = "Date: ";
                xlWorkSheet.Cells[i, 7] = DateTime.Now;

                if (noDetail)
                {
                    i += 2;
                    xlWorkSheet.Cells[i, 1] = "No Record Found!";
                    xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                    i++;

                    return i;
                }

                i += 2;
                Int16 k = 2;

                xlWorkSheet.Cells[i, k] = "Gross";
                xlWorkSheet.Cells[i + 1, k++] = "Premium";

                xlWorkSheet.Cells[i, k] = "Reinsurance";
                xlWorkSheet.Cells[i + 1, k++] = "Premium";

                xlWorkSheet.Cells[i, k] = "Net";
                xlWorkSheet.Cells[i + 1, k++] = "Premium";

                xlWorkSheet.Cells[i, k] = "Gross";
                xlWorkSheet.Cells[i + 1, k++] = "Commission";

                xlWorkSheet.Cells[i, k] = "R/I";
                xlWorkSheet.Cells[i + 1, k++] = "Commission";

                xlWorkSheet.Cells[i, k] = "Net";
                xlWorkSheet.Cells[i + 1, k++] = "Commission";

                i += 2;
            }

            if (optid == "prmuniv" && sheetIndex != 1)
            {
                xlWorkSheet.Cells.Font.Name = "arial";
                xlWorkSheet.Cells.Font.Size = 8;

                xlWorkSheet.Columns["A:B"].ColumnWidth = 10;
                xlWorkSheet.Columns["C:D"].ColumnWidth = 8;
                xlWorkSheet.Columns["E"].ColumnWidth = 13;
                xlWorkSheet.Columns["F"].ColumnWidth = 12;
                xlWorkSheet.Columns["G:H"].ColumnWidth = 4;
                xlWorkSheet.Columns["I"].ColumnWidth = 8;
                xlWorkSheet.Columns["J"].ColumnWidth = 12;
                xlWorkSheet.Columns["K:AI"].ColumnWidth = 12;

                xlWorkSheet.Columns["E:F"].HorizontalAlignment = 4;
                xlWorkSheet.Columns["J:AI"].HorizontalAlignment = 4;
                xlWorkSheet.Columns["E:F"].NumberFormat = "#,##0.00_)";
                xlWorkSheet.Columns["J:AI"].NumberFormat = "#,##0.00_)";

                xlWorkSheet.get_Range("AI3", "AI3").HorizontalAlignment = 2;
                xlWorkSheet.get_Range("AI3", "AI3").NumberFormat = "yyyy/mm/dd";

                xlWorkSheet.get_Range("C5", "AI6").HorizontalAlignment = 3;
                xlWorkSheet.get_Range("A5", "AJ6").Font.Bold = true;

                xlWorkSheet.get_Range("R5", "T5").Merge();
                xlWorkSheet.get_Range("V5", "X5").Merge();
                xlWorkSheet.get_Range("Y5", "AA5").Merge();
                xlWorkSheet.get_Range("AB5", "AD5").Merge();
                xlWorkSheet.get_Range("AG5", "AI5").Merge();

                xlWorkSheet.Cells[i, 34] = "Date: ";
                xlWorkSheet.Cells[i, 35] = DateTime.Now;

                if (noDetail)
                {
                    i += 2;
                    xlWorkSheet.Cells[i, 1] = "No Record Found!";
                    xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                    i++;

                    return i;
                }

                i += 2;
                Int16 k = 1;

                xlWorkSheet.Cells[i + 1, k++] = "Policy No.";
                xlWorkSheet.Cells[i + 1, k++] = "Endt. No.";
                xlWorkSheet.Cells[i + 1, k++] = "Eff Fr";
                xlWorkSheet.Cells[i + 1, k++] = "Eff To";

			    xlWorkSheet.Cells[i, k]  = "Policy";
                xlWorkSheet.Cells[i + 1, k++] = "Contract Sum";

			    xlWorkSheet.Cells[i, k]  = "Policy";
			    xlWorkSheet.Cells[i + 1, k++]  = "Premium";
                k++;
			
			    xlWorkSheet.Cells[i, k]  = "Install";
			    xlWorkSheet.Cells[i + 1, k++]  = "No.";

			    xlWorkSheet.Cells[i, k]  = "Scheduled";
			    xlWorkSheet.Cells[i + 1, k++]  = "Inv Date";

			    xlWorkSheet.Cells[i, k] = "Pro Rata";
			    xlWorkSheet.Cells[i + 1, k++] = "Contract Sum";

			    xlWorkSheet.Cells[i, k] = "Gross";
			    xlWorkSheet.Cells[i + 1, k++] = "Premium";

			    xlWorkSheet.Cells[i, k] = "Original";
			    xlWorkSheet.Cells[i + 1, k++] = "Brokage";

			    xlWorkSheet.Cells[i, k] = "Net";
			    xlWorkSheet.Cells[i + 1, k++] = "Premium";

                xlWorkSheet.Cells[i, k] = "ECILMB";
			    xlWorkSheet.Cells[i + 1, k++] = "(Levy 1)";

                xlWorkSheet.Cells[i, k] = "GTFC";
			    xlWorkSheet.Cells[i + 1, k++] = "(Levy 2)";

                xlWorkSheet.Cells[i, k] = "ECIIB";
			    xlWorkSheet.Cells[i + 1, k++] = "(Levy 3)";

                xlWorkSheet.Cells[i, k] = "Retained";
                xlWorkSheet.Cells[i + 1, k++] = "Net";

                xlWorkSheet.Cells[i, k]  = "Surplus Treaty";
                xlWorkSheet.Cells[i + 1, k++]  = "Gross";
			    xlWorkSheet.Cells[i + 1, k++] = "R/I Com";
			    xlWorkSheet.Cells[i + 1, k++] = "Net";
		
			    xlWorkSheet.Cells[i, k] = "XOL";
			    xlWorkSheet.Cells[i + 1, k++] = "Gross";
		
    			xlWorkSheet.Cells[i, k] = "Facultative";
	    		xlWorkSheet.Cells[i + 1, k++] = "Gross";
		    	xlWorkSheet.Cells[i + 1, k++] = "R/I Com";
			    xlWorkSheet.Cells[i + 1, k++] = "Net";
		
			    xlWorkSheet.Cells[i, k]= "Fac Obligatory";
			    xlWorkSheet.Cells[i + 1, k++] = "Gross";
			    xlWorkSheet.Cells[i + 1, k++] = "R/I Com";
			    xlWorkSheet.Cells[i + 1, k++] = "Net";

			    xlWorkSheet.Cells[i, k] = "Fac Non prop";
			    xlWorkSheet.Cells[i + 1, k++] = "Gross";
			    xlWorkSheet.Cells[i + 1, k++] = "R/I Com";
			    xlWorkSheet.Cells[i + 1, k++] = "Net";

			    xlWorkSheet.Cells[i, k] = "Total";
			    xlWorkSheet.Cells[i + 1, k++] = "R/I Premium";

			    xlWorkSheet.Cells[i, k] = "Net";
			    xlWorkSheet.Cells[i + 1, k++] = "Premium";

			    xlWorkSheet.Cells[i, k] = "Deferred Acquisition Cost";
			    xlWorkSheet.Cells[i + 1, k++] = "Brokage";
			    xlWorkSheet.Cells[i + 1, k++] = "R/I Comm";
			    xlWorkSheet.Cells[i + 1, k++] = "Net Comm";

                xlWorkSheet.Cells[i + 1, k++] = "Insured";

                i += 3;
            }

            return i;
        }

              
        public static void WsPrmGrndDetail(Excel.Worksheet xlWorkSheet,DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fclsseq = "";
            
            decimal ln_sgpm_di = 0, ln_sttypm_ri = 0, ln_sxolpm_ri = 0, ln_sfacpm_ri = 0, ln_sricost_di = 0,
                    ln_sripm_ri = 0, ln_snetpm_i = 0, ln_sunernpm = 0, ln_sernpm = 0, ln_scomamt_di = 0,
                    ln_sttycom_ri = 0, ln_sfaccom_ri = 0, ln_sricom_ri = 0, ln_snetcom_i = 0, ln_sdacqcost = 0,
                    ln_snetcompay = 0;

            decimal ln_tgpm_di = 0, ln_tttypm_ri = 0, ln_txolpm_ri = 0, ln_tfacpm_ri = 0, ln_tricost_di = 0,
                    ln_tripm_ri = 0, ln_tnetpm_i = 0, ln_tunernpm = 0, ln_ternpm = 0, ln_tcomamt_di = 0,
                    ln_tttycom_ri = 0, ln_tfaccom_ri = 0, ln_tricom_ri = 0, ln_tnetcom_i = 0, ln_tdacqcost = 0,
                    ln_tnetcompay = 0;

            foreach (DataRow dr in dt.Rows)
            {
                if (lc_fclsseq != dr["fclsseq"].ToString().Trim())
                {
                    if (iRow > startRow)
                    {
                        k = 3;
                        xlWorkSheet.Cells[iRow, k++] = "Sub Total:";
                        xlWorkSheet.Cells[iRow, k++] = ln_sgpm_di;
                        xlWorkSheet.Cells[iRow, k++] = ln_sttypm_ri;
                        xlWorkSheet.Cells[iRow, k++] = ln_sxolpm_ri;
                        xlWorkSheet.Cells[iRow, k++] = ln_sfacpm_ri;
                        xlWorkSheet.Cells[iRow, k++] = ln_sricost_di;
                        xlWorkSheet.Cells[iRow, k++] = ln_sripm_ri;
                        xlWorkSheet.Cells[iRow, k++] = ln_snetpm_i;
                        xlWorkSheet.Cells[iRow, k++] = ln_sunernpm;
                        xlWorkSheet.Cells[iRow, k++] = ln_sernpm;
                        xlWorkSheet.Cells[iRow, k++] = ln_scomamt_di;
                        xlWorkSheet.Cells[iRow, k++] = ln_sttycom_ri;
                        xlWorkSheet.Cells[iRow, k++] = ln_sfaccom_ri;
                        xlWorkSheet.Cells[iRow, k++] = ln_sricom_ri;
                        xlWorkSheet.Cells[iRow, k++] = ln_snetcom_i;
                        xlWorkSheet.Cells[iRow, k++] = ln_sdacqcost;
                        xlWorkSheet.Cells[iRow, k] = ln_snetcompay;

                        xlWorkSheet.get_Range(String.Format("C{0}", iRow)).Columns.HorizontalAlignment = 4;

                        iRow += 2;
                    }

                    lc_fclsseq = dr["fclsseq"].ToString().Trim();
                    xlWorkSheet.Cells[iRow, 1] = dr["fclsname"].ToString();

                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                    ln_sgpm_di = 0; ln_sttypm_ri = 0; ln_sxolpm_ri = 0; ln_sfacpm_ri = 0; ln_sricost_di = 0;
                    ln_sripm_ri = 0; ln_snetpm_i = 0; ln_sunernpm = 0; ln_sernpm = 0; ln_scomamt_di = 0;
                    ln_sttycom_ri = 0; ln_sfaccom_ri = 0; ln_sricom_ri = 0; ln_snetcom_i = 0; ln_sdacqcost = 0;
                    ln_snetcompay = 0;

                    iRow += 2;
                }

                String fclass = dr["fclass"].ToString().Trim(), fkind = dr["fkind"].ToString().Trim();

                decimal fgpm_di = Convert.ToDecimal(dr["fgpm_di"]);
                decimal fttypm_ri = Convert.ToDecimal(dr["fttypm_ri"]);
                decimal fxolpm_ri = Convert.ToDecimal(dr["fxolpm_ri"]);
                decimal ffacpm_ri = Convert.ToDecimal(dr["ffacpm_ri"]);
                decimal fricost_di = Convert.ToDecimal(dr["fricost_di"]);
                decimal fripm_ri = Convert.ToDecimal(dr["fripm_ri"]);
                decimal fnetpm_i = Convert.ToDecimal(dr["fnetpm_i"]);
                decimal funernpm = Convert.ToDecimal(dr["funernpm"]);
                decimal fernpm = Convert.ToDecimal(dr["fernpm"]);
                decimal fcomamt_di = Convert.ToDecimal(dr["fcomamt_di"]);
                decimal fttycom_ri = Convert.ToDecimal(dr["fttycom_ri"]);
                decimal ffaccom_ri = Convert.ToDecimal(dr["ffaccom_ri"]);
                decimal fricom_ri = Convert.ToDecimal(dr["fricom_ri"]);
                decimal fnetcom_i = Convert.ToDecimal(dr["fnetcom_i"]);
                decimal fdacqcost = Convert.ToDecimal(dr["fdacqcost"]);
                decimal fnetcompay = Convert.ToDecimal(dr["fnetcompay"]);

                k = 1;
                xlWorkSheet.Cells[iRow, k++] = dr["fpolno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fendtno"];
                xlWorkSheet.Cells[iRow, k++] = !"PMP|CMP".Contains(fclass) ? "": fkind == "1" ? "Comprehensive" : "Third Party";
                xlWorkSheet.Cells[iRow, k++] = fgpm_di;
                xlWorkSheet.Cells[iRow, k++] = fttypm_ri;
                xlWorkSheet.Cells[iRow, k++] = fxolpm_ri;
                xlWorkSheet.Cells[iRow, k++] = ffacpm_ri;
                xlWorkSheet.Cells[iRow, k++] = fricost_di;
                xlWorkSheet.Cells[iRow, k++] = fripm_ri;
                xlWorkSheet.Cells[iRow, k++] = fnetpm_i;
                xlWorkSheet.Cells[iRow, k++] = funernpm;
                xlWorkSheet.Cells[iRow, k++] = fernpm;
                xlWorkSheet.Cells[iRow, k++] = fcomamt_di;
                xlWorkSheet.Cells[iRow, k++] = fttycom_ri;
                xlWorkSheet.Cells[iRow, k++] = ffaccom_ri;
                xlWorkSheet.Cells[iRow, k++] = fricom_ri;
                xlWorkSheet.Cells[iRow, k++] = fnetcom_i;
                xlWorkSheet.Cells[iRow, k++] = fdacqcost;
                xlWorkSheet.Cells[iRow, k] = fnetcompay;

                ln_sgpm_di = ln_sgpm_di + fgpm_di;
                ln_sttypm_ri = ln_sttypm_ri + fttypm_ri;
                ln_sxolpm_ri = ln_sxolpm_ri + fxolpm_ri;
                ln_sfacpm_ri = ln_sfacpm_ri + ffacpm_ri;
                ln_sricost_di = ln_sricost_di + fricost_di;
                ln_sripm_ri = ln_sripm_ri + fripm_ri;
                ln_snetpm_i = ln_snetpm_i + fnetpm_i;
                ln_sunernpm = ln_sunernpm + funernpm;
                ln_sernpm = ln_sernpm + fernpm;
                ln_scomamt_di = ln_scomamt_di + fcomamt_di;
                ln_sttycom_ri = ln_sttycom_ri + fttycom_ri;
                ln_sfaccom_ri = ln_sfaccom_ri + ffaccom_ri;
                ln_sricom_ri = ln_sricom_ri + fricom_ri;
                ln_snetcom_i = ln_snetcom_i + fnetcom_i;
                ln_sdacqcost = ln_sdacqcost + fdacqcost;
                ln_snetcompay = ln_snetcompay + fnetcompay;

                ln_tgpm_di = ln_tgpm_di + fgpm_di;
                ln_tttypm_ri = ln_tttypm_ri + fttypm_ri;
                ln_txolpm_ri = ln_txolpm_ri + fxolpm_ri;
                ln_tfacpm_ri = ln_tfacpm_ri + ffacpm_ri;
                ln_tricost_di = ln_tricost_di + fricost_di;
                ln_tripm_ri = ln_tripm_ri + fripm_ri;
                ln_tnetpm_i = ln_tnetpm_i + fnetpm_i;
                ln_tunernpm = ln_tunernpm + funernpm;
                ln_ternpm = ln_ternpm + fernpm;
                ln_tcomamt_di = ln_tcomamt_di + fcomamt_di;
                ln_tttycom_ri = ln_tttycom_ri + fttycom_ri;
                ln_tfaccom_ri = ln_tfaccom_ri + ffaccom_ri;
                ln_tricom_ri = ln_tricom_ri + fricom_ri;
                ln_tnetcom_i = ln_tnetcom_i + fnetcom_i;
                ln_tdacqcost = ln_tdacqcost + fdacqcost;
                ln_tnetcompay = ln_tnetcompay + fnetcompay;

                iRow++;
            }

            k = 3;
            xlWorkSheet.Cells[iRow, k++] = "Sub Total:";
            xlWorkSheet.Cells[iRow, k++] = ln_sgpm_di;
            xlWorkSheet.Cells[iRow, k++] = ln_sttypm_ri;
            xlWorkSheet.Cells[iRow, k++] = ln_sxolpm_ri;
            xlWorkSheet.Cells[iRow, k++] = ln_sfacpm_ri;
            xlWorkSheet.Cells[iRow, k++] = ln_sricost_di;
            xlWorkSheet.Cells[iRow, k++] = ln_sripm_ri;
            xlWorkSheet.Cells[iRow, k++] = ln_snetpm_i;
            xlWorkSheet.Cells[iRow, k++] = ln_sunernpm;
            xlWorkSheet.Cells[iRow, k++] = ln_sernpm;
            xlWorkSheet.Cells[iRow, k++] = ln_scomamt_di;
            xlWorkSheet.Cells[iRow, k++] = ln_sttycom_ri;
            xlWorkSheet.Cells[iRow, k++] = ln_sfaccom_ri;
            xlWorkSheet.Cells[iRow, k++] = ln_sricom_ri;
            xlWorkSheet.Cells[iRow, k++] = ln_snetcom_i;
            xlWorkSheet.Cells[iRow, k++] = ln_sdacqcost;
            xlWorkSheet.Cells[iRow, k] = ln_snetcompay;

            xlWorkSheet.get_Range(String.Format("C{0}", iRow)).Columns.HorizontalAlignment = 4;

            iRow += 2;

            k = 3;
            xlWorkSheet.Cells[iRow, k++] = "Grand Total:";
            xlWorkSheet.Cells[iRow, k++] = ln_tgpm_di;
            xlWorkSheet.Cells[iRow, k++] = ln_tttypm_ri;
            xlWorkSheet.Cells[iRow, k++] = ln_txolpm_ri;
            xlWorkSheet.Cells[iRow, k++] = ln_tfacpm_ri;
            xlWorkSheet.Cells[iRow, k++] = ln_tricost_di;
            xlWorkSheet.Cells[iRow, k++] = ln_tripm_ri;
            xlWorkSheet.Cells[iRow, k++] = ln_tnetpm_i;
            xlWorkSheet.Cells[iRow, k++] = ln_tunernpm;
            xlWorkSheet.Cells[iRow, k++] = ln_ternpm;
            xlWorkSheet.Cells[iRow, k++] = ln_tcomamt_di;
            xlWorkSheet.Cells[iRow, k++] = ln_tttycom_ri;
            xlWorkSheet.Cells[iRow, k++] = ln_tfaccom_ri;
            xlWorkSheet.Cells[iRow, k++] = ln_tricom_ri;
            xlWorkSheet.Cells[iRow, k++] = ln_tnetcom_i;
            xlWorkSheet.Cells[iRow, k++] = ln_tdacqcost;
            xlWorkSheet.Cells[iRow, k] = ln_tnetcompay;

            xlWorkSheet.get_Range(String.Format("C{0}", iRow)).Columns.HorizontalAlignment = 4;
        }

        public static void WsDaqCostDetail(Excel.Worksheet xlWorkSheet, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fclsseq = "";

            decimal ln_scomamt_di = 0, ln_sdac = 0, ln_sadjcom = 0, ln_sttycom_ri = 0, ln_sfaccom_ri = 0,
                    ln_sricom_ri = 0, ln_sdricom = 0, ln_sadjricom = 0, ln_snetcom_i = 0, ln_sdacqcost = 0,
                    ln_snetcompay = 0;

            decimal ln_tcomamt_di = 0, ln_tdac = 0, ln_tadjcom = 0, ln_tttycom_ri = 0, ln_tfaccom_ri = 0,
                    ln_tricom_ri = 0, ln_tdricom = 0, ln_tadjricom = 0, ln_tnetcom_i = 0, ln_tdacqcost = 0,
                    ln_tnetcompay = 0;

            foreach (DataRow dr in dt.Rows)
            {
                if (lc_fclsseq != dr["fclsseq"].ToString().Trim())
                {
                    if (iRow > startRow)
                    {
                        k = 2;
                        xlWorkSheet.Cells[iRow, k++] = "Sub Total:";
                        xlWorkSheet.Cells[iRow, k++] = ln_scomamt_di;
                        xlWorkSheet.Cells[iRow, k++] = ln_sdac;
                        xlWorkSheet.Cells[iRow, k++] = ln_sadjcom;
                        xlWorkSheet.Cells[iRow, k++] = ln_sttycom_ri;
                        xlWorkSheet.Cells[iRow, k++] = ln_sfaccom_ri;
                        xlWorkSheet.Cells[iRow, k++] = ln_sricom_ri;
                        xlWorkSheet.Cells[iRow, k++] = ln_sdricom;
                        xlWorkSheet.Cells[iRow, k++] = ln_sadjricom;
                        xlWorkSheet.Cells[iRow, k++] = ln_snetcom_i;
                        xlWorkSheet.Cells[iRow, k++] = ln_sdacqcost;
                        xlWorkSheet.Cells[iRow, k] = ln_snetcompay;

                        xlWorkSheet.get_Range(String.Format("B{0}", iRow)).Columns.HorizontalAlignment = 4;

                        iRow += 2;
                    }

                    lc_fclsseq = dr["fclsseq"].ToString().Trim();
                    xlWorkSheet.Cells[iRow, 1] = dr["fclsname"].ToString();

                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                    ln_scomamt_di = 0; ln_sdac = 0; ln_sadjcom = 0; ln_sttycom_ri = 0; ln_sfaccom_ri = 0;
                    ln_sricom_ri = 0; ln_sdricom = 0; ln_sadjricom = 0; ln_snetcom_i = 0; ln_sdacqcost = 0;
                    ln_snetcompay = 0;

                    iRow += 2;
                }

                String fclass = dr["fclass"].ToString().Trim(), fkind = dr["fkind"].ToString().Trim();

                decimal fcomamt_di = Convert.ToDecimal(dr["fcomamt_di"]);
                decimal fdac = Convert.ToDecimal(dr["fdac"]);
                decimal fadjcom = Convert.ToDecimal(dr["fadjcom"]);
                decimal fttycom_ri = Convert.ToDecimal(dr["fttycom_ri"]);
                decimal ffaccom_ri = Convert.ToDecimal(dr["ffaccom_ri"]);
                decimal fricom_ri = Convert.ToDecimal(dr["fricom_ri"]);
                decimal fdricom = Convert.ToDecimal(dr["fdricom"]);
                decimal fadjricom = Convert.ToDecimal(dr["fadjricom"]);
                decimal fnetcom_i = Convert.ToDecimal(dr["fnetcom_i"]);
                decimal fdacqcost = Convert.ToDecimal(dr["fdacqcost"]);
                decimal fnetcompay = Convert.ToDecimal(dr["fnetcompay"]);

                k = 1;
                xlWorkSheet.Cells[iRow, k++] = dr["fpolno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fendtno"];
                xlWorkSheet.Cells[iRow, k++] = fcomamt_di;
                xlWorkSheet.Cells[iRow, k++] = fdac;
                xlWorkSheet.Cells[iRow, k++] = fadjcom;
                xlWorkSheet.Cells[iRow, k++] = fttycom_ri;
                xlWorkSheet.Cells[iRow, k++] = ffaccom_ri;
                xlWorkSheet.Cells[iRow, k++] = fricom_ri;
                xlWorkSheet.Cells[iRow, k++] = fdricom;
                xlWorkSheet.Cells[iRow, k++] = fadjricom;
                xlWorkSheet.Cells[iRow, k++] = fnetcom_i;
                xlWorkSheet.Cells[iRow, k++] = fdacqcost;
                xlWorkSheet.Cells[iRow, k] = fnetcompay;

                ln_scomamt_di = ln_scomamt_di + fcomamt_di;
                ln_sdac = ln_sdac + fdac;
                ln_sadjcom = ln_sadjcom + fadjcom;
                ln_sttycom_ri = ln_sttycom_ri + fttycom_ri;
                ln_sfaccom_ri = ln_sfaccom_ri + ffaccom_ri;
                ln_sricom_ri = ln_sricom_ri + fricom_ri;
                ln_sdricom = ln_sdricom + fdricom;
                ln_sadjricom = ln_sadjricom + fadjricom;
                ln_snetcom_i = ln_snetcom_i + fnetcom_i;
                ln_sdacqcost = ln_sdacqcost + fdacqcost;
                ln_snetcompay = ln_snetcompay + fnetcompay;

                ln_tcomamt_di = ln_tcomamt_di + fcomamt_di;
                ln_tdac = ln_tdac + fdac;
                ln_tadjcom = ln_tadjcom + fadjcom;
                ln_tttycom_ri = ln_tttycom_ri + fttycom_ri;
                ln_tfaccom_ri = ln_tfaccom_ri + ffaccom_ri;
                ln_tricom_ri = ln_tricom_ri + fricom_ri;
                ln_tdricom = ln_tdricom + fdricom;
                ln_tadjricom = ln_tadjricom + fadjricom;
                ln_tnetcom_i = ln_tnetcom_i + fnetcom_i;
                ln_tdacqcost = ln_tdacqcost + fdacqcost;
                ln_tnetcompay = ln_tnetcompay + fnetcompay;

                iRow++;
            }

            k = 2;
            xlWorkSheet.Cells[iRow, k++] = "Sub Total:";
            xlWorkSheet.Cells[iRow, k++] = ln_scomamt_di;
            xlWorkSheet.Cells[iRow, k++] = ln_sdac;
            xlWorkSheet.Cells[iRow, k++] = ln_sadjcom;
            xlWorkSheet.Cells[iRow, k++] = ln_sttycom_ri;
            xlWorkSheet.Cells[iRow, k++] = ln_sfaccom_ri;
            xlWorkSheet.Cells[iRow, k++] = ln_sricom_ri;
            xlWorkSheet.Cells[iRow, k++] = ln_sdricom;
            xlWorkSheet.Cells[iRow, k++] = ln_sadjricom;
            xlWorkSheet.Cells[iRow, k++] = ln_snetcom_i;
            xlWorkSheet.Cells[iRow, k++] = ln_sdacqcost;
            xlWorkSheet.Cells[iRow, k] = ln_snetcompay;

            xlWorkSheet.get_Range(String.Format("B{0}", iRow)).Columns.HorizontalAlignment = 4;

            iRow += 2;

            k = 2;
            xlWorkSheet.Cells[iRow, k++] = "Grand Total:";
            xlWorkSheet.Cells[iRow, k++] = ln_tcomamt_di;
            xlWorkSheet.Cells[iRow, k++] = ln_tdac;
            xlWorkSheet.Cells[iRow, k++] = ln_tadjcom;
            xlWorkSheet.Cells[iRow, k++] = ln_tttycom_ri;
            xlWorkSheet.Cells[iRow, k++] = ln_tfaccom_ri;
            xlWorkSheet.Cells[iRow, k++] = ln_tricom_ri;
            xlWorkSheet.Cells[iRow, k++] = ln_tdricom;
            xlWorkSheet.Cells[iRow, k++] = ln_tadjricom;
            xlWorkSheet.Cells[iRow, k++] = ln_tnetcom_i;
            xlWorkSheet.Cells[iRow, k++] = ln_tdacqcost;
            xlWorkSheet.Cells[iRow, k] = ln_tnetcompay;

            xlWorkSheet.get_Range(String.Format("B{0}", iRow)).Columns.HorizontalAlignment = 4;
        }

        public static void WsPrmUnearnSumm(Excel.Worksheet xlWorkSheet, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            decimal ln_tgunern_fa = 0, ln_trunern_fa = 0, ln_tnunern_fa = 0, ln_tgcunern = 0, ln_tgnunern = 0,
                    ln_trcunern = 0, ln_trnunern = 0, ln_tncunern = 0, ln_tnnunern = 0;

            foreach (DataRow dr in dt.Rows)
            {
                decimal gunern_fa = Convert.ToDecimal(dr["gunern_fa"]),
                        runern_fa = Convert.ToDecimal(dr["runern_fa"]),
                        nunern_fa = Convert.ToDecimal(dr["nunern_fa"]),
                        gcunern = Convert.ToDecimal(dr["gcunern"]),
                        gnunern = Convert.ToDecimal(dr["gnunern"]),
                        rcunern = Convert.ToDecimal(dr["rcunern"]),
                        rnunern = Convert.ToDecimal(dr["rnunern"]),
                        ncunern = Convert.ToDecimal(dr["ncunern"]),
                        nnunern = Convert.ToDecimal(dr["nnunern"]);
                
                k = 1;
                xlWorkSheet.Cells[iRow, k++] = dr["fclsname"];
                xlWorkSheet.Cells[iRow, k++] = gunern_fa;
                xlWorkSheet.Cells[iRow, k++] = runern_fa;
                xlWorkSheet.Cells[iRow, k++] = nunern_fa;
                xlWorkSheet.Cells[iRow, k++] = gcunern;
                xlWorkSheet.Cells[iRow, k++] = gnunern;
                xlWorkSheet.Cells[iRow, k++] = rcunern;
                xlWorkSheet.Cells[iRow, k++] = rnunern;
                xlWorkSheet.Cells[iRow, k++] = ncunern;
                xlWorkSheet.Cells[iRow, k] = nnunern;

                ln_tgunern_fa = ln_tgunern_fa+gunern_fa;
                ln_trunern_fa = ln_trunern_fa+runern_fa;
                ln_tnunern_fa = ln_tnunern_fa+nunern_fa;
                ln_tgcunern = ln_tgcunern+gcunern;
                ln_tgnunern = ln_tgnunern+gnunern;
                ln_trcunern = ln_trcunern+rcunern;
                ln_trnunern = ln_trnunern+rnunern;
                ln_tncunern = ln_tncunern+ncunern;
                ln_tnnunern = ln_tnnunern + nnunern;

                iRow++;
            }

            k = 1;
            iRow++;
            xlWorkSheet.Cells[iRow, k++] = "Grand Total:";
            xlWorkSheet.Cells[iRow, k++] = ln_tgunern_fa;
            xlWorkSheet.Cells[iRow, k++] = ln_trunern_fa;
            xlWorkSheet.Cells[iRow, k++] = ln_tnunern_fa;
            xlWorkSheet.Cells[iRow, k++] = ln_tgcunern;
            xlWorkSheet.Cells[iRow, k++] = ln_tgnunern;
            xlWorkSheet.Cells[iRow, k++] = ln_trcunern;
            xlWorkSheet.Cells[iRow, k++] = ln_trnunern;
            xlWorkSheet.Cells[iRow, k++] = ln_tncunern;
            xlWorkSheet.Cells[iRow, k] = ln_tnnunern;

            xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Columns.HorizontalAlignment = 4;
        }

        public static void WsPrmUnearnDetail(Excel.Worksheet xlWorkSheet, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fclsseq = "";

            decimal	ls_fgpm_a = 0, ls_fripm_a = 0, ls_fnetpm_a = 0, ls_gernpm_fa = 0, ls_rernpm_fa = 0, 
                    ls_fernpm_fa = 0, ls_gernpm_fy = 0, ls_rernpm_fy = 0, ls_fernpm_fy = 0,
                    ls_gunern_fa = 0, ls_runern_fa = 0, ls_nunern_fa = 0, ls_gcunern = 0, ls_gnunern= 0,
                    ls_rcunern = 0, ls_rnunern = 0, ls_ncunern = 0, ls_nnunern = 0;

            decimal lt_fgpm_a = 0, lt_fripm_a = 0, lt_fnetpm_a = 0, lt_gernpm_fa = 0, lt_rernpm_fa = 0,
                    lt_fernpm_fa = 0, lt_gernpm_fy = 0, lt_rernpm_fy = 0, lt_fernpm_fy = 0,
                    lt_gunern_fa = 0, lt_runern_fa = 0, lt_nunern_fa = 0, lt_gcunern = 0, lt_gnunern = 0,
                    lt_rcunern = 0, lt_rnunern = 0, lt_ncunern = 0, lt_nnunern = 0;

            foreach (DataRow dr in dt.Rows)
            {
                if (lc_fclsseq != dr["fclsseq"].ToString().Trim())
                {
                    if (iRow > startRow)
                    {
                        k = 8;
                        xlWorkSheet.Cells[iRow, k++] = "Sub Total:";
                        xlWorkSheet.Cells[iRow, k++] = ls_fgpm_a;
                        xlWorkSheet.Cells[iRow, k++] = ls_fripm_a;
                        xlWorkSheet.Cells[iRow, k++] = ls_fnetpm_a;
                        xlWorkSheet.Cells[iRow, k++] = ls_gernpm_fa;
                        xlWorkSheet.Cells[iRow, k++] = ls_rernpm_fa;
                        xlWorkSheet.Cells[iRow, k++] = ls_fernpm_fa;
                        xlWorkSheet.Cells[iRow, k++] = ls_gernpm_fy;
                        xlWorkSheet.Cells[iRow, k++] = ls_rernpm_fy;
                        xlWorkSheet.Cells[iRow, k++] = ls_fernpm_fy;

                        xlWorkSheet.Cells[iRow, k++] = ls_gunern_fa;
                        xlWorkSheet.Cells[iRow, k++] = ls_runern_fa;
                        xlWorkSheet.Cells[iRow, k++] = ls_nunern_fa;
                        xlWorkSheet.Cells[iRow, k++] = ls_gcunern;
                        xlWorkSheet.Cells[iRow, k++] = ls_gnunern;
                        xlWorkSheet.Cells[iRow, k++] = ls_rcunern;
                        xlWorkSheet.Cells[iRow, k++] = ls_rnunern;
                        xlWorkSheet.Cells[iRow, k++] = ls_ncunern;
                        xlWorkSheet.Cells[iRow, k++] = ls_nnunern;

                        xlWorkSheet.get_Range(String.Format("H{0}", iRow)).Columns.HorizontalAlignment = 4;

                        iRow += 2;
                    }

                    lc_fclsseq = dr["fclsseq"].ToString().Trim();
                    xlWorkSheet.Cells[iRow, 1] = dr["fclsname"].ToString();

                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                    ls_fgpm_a = 0; ls_fripm_a = 0; ls_fnetpm_a = 0; ls_gernpm_fa = 0; ls_rernpm_fa = 0;
                    ls_fernpm_fa = 0; ls_gernpm_fy = 0; ls_rernpm_fy = 0; ls_fernpm_fy = 0;
                    ls_gunern_fa = 0; ls_runern_fa = 0; ls_nunern_fa = 0; ls_gcunern = 0; ls_gnunern = 0;
                    ls_rcunern = 0; ls_rnunern = 0; ls_ncunern = 0; ls_nnunern = 0;

                    iRow += 2;
                }

                decimal fgpm_a = Convert.ToDecimal(dr["fgpm_a"]);
                decimal fripm_a = Convert.ToDecimal(dr["fripm_a"]);
                decimal fnetpm_a = Convert.ToDecimal(dr["fnetpm_a"]);
                decimal gernpm_fa = Convert.ToDecimal(dr["gernpm_fa"]);
                decimal rernpm_fa = Convert.ToDecimal(dr["rernpm_fa"]);
                decimal fernpm_fa = Convert.ToDecimal(dr["fernpm_fa"]);
                decimal gernpm_fy = Convert.ToDecimal(dr["gernpm_fy"]);
                decimal rernpm_fy = Convert.ToDecimal(dr["rernpm_fy"]);
                decimal fernpm_fy = Convert.ToDecimal(dr["fernpm_fy"]);
                decimal gunern_fa = Convert.ToDecimal(dr["gunern_fa"]);
                decimal runern_fa = Convert.ToDecimal(dr["runern_fa"]);
                decimal nunern_fa = Convert.ToDecimal(dr["nunern_fa"]);
                decimal gcunern = Convert.ToDecimal(dr["gcunern"]);
                decimal gnunern = Convert.ToDecimal(dr["gnunern"]);
                decimal rcunern = Convert.ToDecimal(dr["rcunern"]);
                decimal rnunern = Convert.ToDecimal(dr["rnunern"]);
                decimal ncunern = Convert.ToDecimal(dr["ncunern"]);
                decimal nnunern = Convert.ToDecimal(dr["nnunern"]);


                k = 1;
                xlWorkSheet.Cells[iRow, k++] = dr["fpolno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fendtno"];
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fefffr"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["feffto"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToInt16(dr["fpolday"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToInt16(dr["fexpday_a"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToInt16(dr["fexpday_y"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDecimal(dr["fgpm_p"]);
                xlWorkSheet.Cells[iRow, k++] = fgpm_a;
                xlWorkSheet.Cells[iRow, k++] = fripm_a;
                xlWorkSheet.Cells[iRow, k++] = fnetpm_a;
                xlWorkSheet.Cells[iRow, k++] = gernpm_fa;
                xlWorkSheet.Cells[iRow, k++] = rernpm_fa;
                xlWorkSheet.Cells[iRow, k++] = fernpm_fa;
                xlWorkSheet.Cells[iRow, k++] = gernpm_fy;
                xlWorkSheet.Cells[iRow, k++] = rernpm_fy;
                xlWorkSheet.Cells[iRow, k++] = fernpm_fy;
                xlWorkSheet.Cells[iRow, k++] = gunern_fa;
                xlWorkSheet.Cells[iRow, k++] = runern_fa;
                xlWorkSheet.Cells[iRow, k++] = nunern_fa;
                xlWorkSheet.Cells[iRow, k++] = gcunern;
                xlWorkSheet.Cells[iRow, k++] = gnunern;
                xlWorkSheet.Cells[iRow, k++] = rcunern;
                xlWorkSheet.Cells[iRow, k++] = rnunern;
                xlWorkSheet.Cells[iRow, k++] = ncunern;
                xlWorkSheet.Cells[iRow, k++] = nnunern;

                xlWorkSheet.Cells[iRow, k++] = dr["fbus"];
                xlWorkSheet.Cells[iRow, k++] = dr["fclnt"];
                xlWorkSheet.Cells[iRow, k++] = dr["fsclnt"];
                xlWorkSheet.Cells[iRow, k++] = dr["fcdesc"];

                ls_fgpm_a     = ls_fgpm_a + fgpm_a;
                ls_fripm_a    = ls_fripm_a + fripm_a;
                ls_fnetpm_a   = ls_fnetpm_a + fnetpm_a;
                ls_gernpm_fa  = ls_gernpm_fa + gernpm_fa;
                ls_rernpm_fa  = ls_rernpm_fa + rernpm_fa;
                ls_fernpm_fa  = ls_fernpm_fa + fernpm_fa;
                ls_gernpm_fy  = ls_gernpm_fy + gernpm_fy;
                ls_rernpm_fy  = ls_rernpm_fy + rernpm_fy;
                ls_fernpm_fy  = ls_fernpm_fy + fernpm_fy;
                ls_gunern_fa  = ls_gunern_fa + gunern_fa;
                ls_runern_fa  = ls_runern_fa + runern_fa;
                ls_nunern_fa  = ls_nunern_fa + nunern_fa;
                ls_gcunern    = ls_gcunern + gcunern;
                ls_gnunern    = ls_gnunern + gnunern;
                ls_rcunern    = ls_rcunern + rcunern;
                ls_rnunern    = ls_rnunern + rnunern;
                ls_ncunern    = ls_ncunern + ncunern;
                ls_nnunern = ls_nnunern + nnunern;

                lt_fgpm_a = lt_fgpm_a + fgpm_a;
                lt_fripm_a = lt_fripm_a + fripm_a;
                lt_fnetpm_a = lt_fnetpm_a + fnetpm_a;
                lt_gernpm_fa = lt_gernpm_fa + gernpm_fa;
                lt_rernpm_fa = lt_rernpm_fa + rernpm_fa;
                lt_fernpm_fa = lt_fernpm_fa + fernpm_fa;
                lt_gernpm_fy = lt_gernpm_fy + gernpm_fy;
                lt_rernpm_fy = lt_rernpm_fy + rernpm_fy;
                lt_fernpm_fy = lt_fernpm_fy + fernpm_fy;
                lt_gunern_fa = lt_gunern_fa + gunern_fa;
                lt_runern_fa = lt_runern_fa + runern_fa;
                lt_nunern_fa = lt_nunern_fa + nunern_fa;
                lt_gcunern = lt_gcunern + gcunern;
                lt_gnunern = lt_gnunern + gnunern;
                lt_rcunern = lt_rcunern + rcunern;
                lt_rnunern = lt_rnunern + rnunern;
                lt_ncunern = lt_ncunern + ncunern;
                lt_nnunern = lt_nnunern + nnunern;

                iRow++;
            }

            k = 8;
            xlWorkSheet.Cells[iRow, k++] = "Sub Total:";
            xlWorkSheet.Cells[iRow, k++] = ls_fgpm_a;
            xlWorkSheet.Cells[iRow, k++] = ls_fripm_a;
            xlWorkSheet.Cells[iRow, k++] = ls_fnetpm_a;
            xlWorkSheet.Cells[iRow, k++] = ls_gernpm_fa;
            xlWorkSheet.Cells[iRow, k++] = ls_rernpm_fa;
            xlWorkSheet.Cells[iRow, k++] = ls_fernpm_fa;
            xlWorkSheet.Cells[iRow, k++] = ls_gernpm_fy;
            xlWorkSheet.Cells[iRow, k++] = ls_rernpm_fy;
            xlWorkSheet.Cells[iRow, k++] = ls_fernpm_fy;

            xlWorkSheet.Cells[iRow, k++] = ls_gunern_fa;
            xlWorkSheet.Cells[iRow, k++] = ls_runern_fa;
            xlWorkSheet.Cells[iRow, k++] = ls_nunern_fa;
            xlWorkSheet.Cells[iRow, k++] = ls_gcunern;
            xlWorkSheet.Cells[iRow, k++] = ls_gnunern;
            xlWorkSheet.Cells[iRow, k++] = ls_rcunern;
            xlWorkSheet.Cells[iRow, k++] = ls_rnunern;
            xlWorkSheet.Cells[iRow, k++] = ls_ncunern;
            xlWorkSheet.Cells[iRow, k++] = ls_nnunern;

            xlWorkSheet.get_Range(String.Format("H{0}", iRow)).Columns.HorizontalAlignment = 4;

            iRow += 2;

            k = 8;
            xlWorkSheet.Cells[iRow, k++] = "Grand Total:";
            xlWorkSheet.Cells[iRow, k++] = lt_fgpm_a;
            xlWorkSheet.Cells[iRow, k++] = lt_fripm_a;
            xlWorkSheet.Cells[iRow, k++] = lt_fnetpm_a;
            xlWorkSheet.Cells[iRow, k++] = lt_gernpm_fa;
            xlWorkSheet.Cells[iRow, k++] = lt_rernpm_fa;
            xlWorkSheet.Cells[iRow, k++] = lt_fernpm_fa;
            xlWorkSheet.Cells[iRow, k++] = lt_gernpm_fy;
            xlWorkSheet.Cells[iRow, k++] = lt_rernpm_fy;
            xlWorkSheet.Cells[iRow, k++] = lt_fernpm_fy;

            xlWorkSheet.Cells[iRow, k++] = lt_gunern_fa;
            xlWorkSheet.Cells[iRow, k++] = lt_runern_fa;
            xlWorkSheet.Cells[iRow, k++] = lt_nunern_fa;
            xlWorkSheet.Cells[iRow, k++] = lt_gcunern;
            xlWorkSheet.Cells[iRow, k++] = lt_gnunern;
            xlWorkSheet.Cells[iRow, k++] = lt_rcunern;
            xlWorkSheet.Cells[iRow, k++] = lt_rnunern;
            xlWorkSheet.Cells[iRow, k++] = lt_ncunern;
            xlWorkSheet.Cells[iRow, k++] = lt_nnunern;

            xlWorkSheet.get_Range(String.Format("H{0}", iRow)).Columns.HorizontalAlignment = 4;
        }

        public static void WbPrmUnearn(Excel.Workbook xlWorkBook, DataSet ds, DateTime vlastasat, 
                                        DateTime vthisasat, string optid, string z_company, 
                                        string z_title, string z_currency, string z_period)
        {
            for (Int16 k = 0; k < ds.Tables.Count; k++)
            {
                Int16 i, sheetIndex = k;
                Excel.Worksheet xlWorkSheet;

                Boolean noDetail = false;

                if (ds.Tables[k].Rows.Count == 0)
                    noDetail = true;
                else if (ds.Tables[k].DefaultView[0]["fclass"].ToString().Trim() == "???")
                    noDetail = true;


                sheetIndex++;
                xlWorkSheet = xlWorkBook.Worksheets.get_Item(sheetIndex);

                if (k == 0)
                    xlWorkSheet.Name = "Summary";

                if (k == 1)
                    xlWorkSheet.Name = "Details";

                i = RptInExcel.WsHeader(xlWorkSheet, optid, z_company, z_title, z_currency, z_period);
                i = RptInExcel.WsColTitlePrmUnern(xlWorkSheet, sheetIndex, vlastasat, vthisasat, noDetail, optid, i);

                if (!noDetail)
                {
                    if (k == 0)
                        RptInExcel.WsPrmUnearnSumm(xlWorkSheet, ds.Tables[k], optid, i);

                    if (k == 1)
                        RptInExcel.WsPrmUnearnDetail(xlWorkSheet, ds.Tables[k], optid, i);
                }
            }
        }

        public static void WsPrmUnIvSumm(Excel.Worksheet xlWorkSheet, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;
            String lc_fclsseq = "";

            foreach (DataRow dr in dt.Rows)
            {
                if (lc_fclsseq != dr["fclsseq"].ToString().Trim())
                {
                    iRow++;
                    k = 1;

                    lc_fclsseq = dr["fclsseq"].ToString().Trim();

                    xlWorkSheet.Cells[iRow, k] = dr["fclsname"];
                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;
                    iRow++;
                }

          		if ("02|06".Contains(dr["frectype"].ToString().Trim()))
                    iRow++;

                k = 1;

        		xlWorkSheet.Cells[iRow, k++] = dr["fdesc"];
		        xlWorkSheet.Cells[iRow, k++] = Convert.ToDecimal(dr["fgpm"]);
		        xlWorkSheet.Cells[iRow, k++] = Convert.ToDecimal(dr["fgpri"]);
		        xlWorkSheet.Cells[iRow, k++] = Convert.ToDecimal(dr["fnetprm"]);
		        xlWorkSheet.Cells[iRow, k++] = Convert.ToDecimal(dr["fcomamt"]);
		        xlWorkSheet.Cells[iRow, k++] = Convert.ToDecimal(dr["fricom"]);
		        xlWorkSheet.Cells[iRow, k] = Convert.ToDecimal(dr["fnetcom"]);

                if (dr["frectype"].ToString().Trim() == "04")
                    xlWorkSheet.get_Range(String.Format("B{0}", iRow), String.Format("G{0}", iRow)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = 1;

                iRow++;
            }

            return;
        }

        public static void WsPrmUnIvDetail(Excel.Worksheet xlWorkSheet, DataTable dt, string optid, Int16 sheetNo, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fclsseq = "", lc_fsec = "";

            decimal ln_sgpm = 0, ln_scomamt = 0, ln_snpm = 0, ln_sgpretn = 0, ln_sgptty = 0, ln_scomtty = 0,
                    ln_spaytty = 0, ln_sgpxol = 0, ln_sgpfac = 0, ln_scomfac = 0, ln_spayfac = 0,
                    ln_sgpfacb = 0, ln_scomfacb = 0, ln_spayfacb = 0, ln_sgpfacnp = 0, ln_scomfacnp = 0,
                    ln_spayfacnp = 0;

            decimal ln_tgpm = 0, ln_tcomamt = 0, ln_tnpm = 0, ln_tgpretn = 0, ln_tgptty = 0, ln_tcomtty = 0,
                    ln_tpaytty = 0, ln_tgpxol = 0, ln_tgpfac = 0, ln_tcomfac = 0, ln_tpayfac = 0,
                    ln_tgpfacb = 0, ln_tcomfacb = 0, ln_tpayfacb = 0, ln_tgpfacnp = 0, ln_tcomfacnp = 0,
                    ln_tpayfacnp = 0;

            decimal ln_adjxol = 0;

            decimal ln_slvyamt1 = 0, ln_slvyamt2 = 0, ln_slvyamt3 = 0;
            decimal ln_tlvyamt1 = 0, ln_tlvyamt2 = 0, ln_tlvyamt3 = 0;

            foreach (DataRow dr in dt.Rows)
            {
                if (lc_fclsseq != dr["fclsseq"].ToString().Trim() || lc_fsec != dr["fsec"].ToString().Trim())
                {
                    if (iRow > startRow)
                    {
                        iRow++;
                        k = 10;
                        xlWorkSheet.Cells[iRow, k++] = "Sub Total:";
                        xlWorkSheet.Cells[iRow, k++] = ln_sgpm;
                        xlWorkSheet.Cells[iRow, k++] = ln_scomamt;
                        xlWorkSheet.Cells[iRow, k++] = ln_snpm;
                        xlWorkSheet.Cells[iRow, k++] = ln_slvyamt1;
                        xlWorkSheet.Cells[iRow, k++] = ln_slvyamt2;
                        xlWorkSheet.Cells[iRow, k++] = ln_slvyamt3;
                        xlWorkSheet.Cells[iRow, k++] = ln_sgpretn - ln_sgpfacnp;
                        xlWorkSheet.Cells[iRow, k++] = ln_sgptty;
                        xlWorkSheet.Cells[iRow, k++] = ln_scomtty;
                        xlWorkSheet.Cells[iRow, k++] = ln_spaytty;

                        xlWorkSheet.Cells[iRow, k++] = ln_sgpxol;

                        xlWorkSheet.Cells[iRow, k++] = ln_sgpfac;
                        xlWorkSheet.Cells[iRow, k++] = ln_scomfac;
                        xlWorkSheet.Cells[iRow, k++] = ln_spayfac;
                        xlWorkSheet.Cells[iRow, k++] = ln_sgpfacb;
                        xlWorkSheet.Cells[iRow, k++] = ln_scomfacb;
                        xlWorkSheet.Cells[iRow, k++] = ln_spayfacb;

                        xlWorkSheet.Cells[iRow, k++] = ln_sgpfacnp;
                        xlWorkSheet.Cells[iRow, k++] = ln_scomfacnp;
                        xlWorkSheet.Cells[iRow, k++] = ln_spayfacnp;

                        xlWorkSheet.Cells[iRow, k++] = ln_sgptty + ln_sgpxol + ln_sgpfac + ln_sgpfacb + ln_sgpfacnp;
                        xlWorkSheet.Cells[iRow, k++] = ln_sgpm - (ln_sgptty + ln_sgpxol + ln_sgpfac + ln_sgpfacb + ln_sgpfacnp);
                        xlWorkSheet.Cells[iRow, k++] = ln_scomamt;
                        xlWorkSheet.Cells[iRow, k++] = ln_scomtty + ln_scomfac + ln_scomfacb + ln_scomfacnp;
                        xlWorkSheet.Cells[iRow, k++] = ln_scomamt - (ln_scomtty + ln_scomfac + ln_scomfacb + ln_scomfacnp);

                        xlWorkSheet.get_Range(String.Format("J{0}", iRow)).Columns.HorizontalAlignment = 4;
                        iRow += 2;
                    }

                    lc_fclsseq = dr["fclsseq"].ToString().Trim();
                    lc_fsec = dr["fsec"].ToString().Trim();

                    k = 1;

                    xlWorkSheet.Cells[iRow, k] = dr["fclsname"].ToString().Trim();
                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = 1;

                    ln_sgpm = 0; ln_scomamt = 0; ln_snpm = 0; ln_sgpretn = 0; ln_sgptty = 0; ln_scomtty = 0;
                    ln_spaytty = 0; ln_sgpxol = 0; ln_sgpfac = 0; ln_scomfac = 0; ln_spayfac = 0;
                    ln_sgpfacb = 0; ln_scomfacb = 0; ln_spayfacb = 0; ln_sgpfacnp = 0; ln_scomfacnp = 0;
                    ln_spayfacnp = 0;

                    ln_slvyamt1 = 0; ln_slvyamt2 = 0; ln_slvyamt3 = 0;
                    iRow += 2;
                }

                k = 1;

                xlWorkSheet.Cells[iRow, k++] = dr["fpolno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fendtno"];

                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fefffr"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["feffto"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDecimal(dr["fsum"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDecimal(dr["pgpm"]);

                string fstatus = dr["fstatus"].ToString().Trim();
                decimal fgpm = Convert.ToDecimal(dr["fgpm"]), fcomamt = Convert.ToDecimal(dr["fcomamt"]),
                        fnpm = Convert.ToDecimal(dr["fnpm"]), flvyamt1 = Convert.ToDecimal(dr["flvyamt1"]),
                        flvyamt2 = Convert.ToDecimal(dr["flvyamt2"]), flvyamt3 = Convert.ToDecimal(dr["flvyamt3"]),
                        fgpretn = Convert.ToDecimal(dr["fgpretn"]),
                        fgptty = Convert.ToDecimal(dr["fgptty"]), fcomtty = Convert.ToDecimal(dr["fcomtty"]),
                        fpaytty = Convert.ToDecimal(dr["fpaytty"]);

                decimal fgpfac = Convert.ToDecimal(dr["fgpfac"]), fcomfac = Convert.ToDecimal(dr["fcomfac"]),
                        fpayfac = Convert.ToDecimal(dr["fpayfac"]), fgpfacb = Convert.ToDecimal(dr["fgpfacb"]),
                        fcomfacb = Convert.ToDecimal(dr["fcomfacb"]), fpayfacb = Convert.ToDecimal(dr["fpayfacb"]),
                        fgpfacnp = Convert.ToDecimal(dr["fgpfacnp"]), fcomfacnp = Convert.ToDecimal(dr["fcomfacnp"]),
                        fpayfacnp = Convert.ToDecimal(dr["fpayfacnp"]);

                if (sheetNo >= 3)
                    xlWorkSheet.Cells[iRow, k++] = fstatus == "1A" ? "Old" : fstatus == "1B" ? "Billed" : "New";
                else
                    k++;

                xlWorkSheet.Cells[iRow, k++] = Convert.ToInt16(dr["finstall"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["finvdate"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDecimal(dr["fcsum"]);

                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fgpm : 0;
                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fcomamt : 0;
                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fnpm : 0;

                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? flvyamt1 : 0;
                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? flvyamt2 : 0;
                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? flvyamt3 : 0;

                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fgpretn - fgpfacnp : 0;
                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fgptty : 0;
                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fcomtty : 0;
                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fpaytty : 0;

                if (sheetNo == 2)
                    ln_adjxol = Convert.ToDecimal(dr["fgpxol_a"]);
                else if (sheetNo == 3 || fstatus.Substring(0, 1) == "2")
                    ln_adjxol = Convert.ToDecimal(dr["fgpxol_b"]);
                else if (sheetNo == 4)
                    ln_adjxol = Convert.ToDecimal(dr["bgpxol"]);
                else if (sheetNo == 5)
                    ln_adjxol = Convert.ToDecimal(dr["fgpxol_b"]);
                else if (fstatus == "1A")
                    ln_adjxol = Convert.ToDecimal(dr["fgpxol_b"]) - Convert.ToDecimal(dr["fgpxol_a"]);
                else if (fstatus == "1B")
                    ln_adjxol = Convert.ToDecimal(dr["bgpxol"]) - Convert.ToDecimal(dr["fgpxol_a"]);

                xlWorkSheet.Cells[iRow, k++] = ln_adjxol;
                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fgpfac : 0;
                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fcomfac : 0;
                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fpayfac : 0;
                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fgpfacb : 0;
                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fcomfacb : 0;
                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fpayfacb : 0;
                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fgpfacnp : 0;
                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fcomfacnp : 0;
                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fpayfacnp : 0;
                xlWorkSheet.Cells[iRow, k++] = (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fgptty + fgpfac + fgpfacb + fgpfacnp : 0) + ln_adjxol;
                xlWorkSheet.Cells[iRow, k++] = (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fgpm - (fgptty + fgpfac + fgpfacb + fgpfacnp) : 0) - ln_adjxol;
                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fcomamt : 0;
                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fcomtty + fcomfac + fcomfacb + fcomfacnp : 0;
                xlWorkSheet.Cells[iRow, k++] = sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fcomamt - (fcomtty + fcomfac + fcomfacb + fcomfacnp) : 0;
                xlWorkSheet.Cells[iRow, k++] = dr["finsd"];

                ln_sgpm = ln_sgpm + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fgpm : 0);
                ln_scomamt = ln_scomamt + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fcomamt : 0);
                ln_snpm = ln_snpm + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fnpm : 0);
                ln_slvyamt1 = ln_slvyamt1 + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? flvyamt1 : 0);
                ln_slvyamt2 = ln_slvyamt2 + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? flvyamt2 : 0);
                ln_slvyamt3 = ln_slvyamt3 + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? flvyamt3 : 0);
                ln_sgpretn = ln_sgpretn + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fgpretn : 0);
                ln_sgptty = ln_sgptty + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fgptty : 0);
                ln_scomtty = ln_scomtty + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fcomtty : 0);
                ln_spaytty = ln_spaytty + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fpaytty : 0);

                ln_sgpxol = ln_sgpxol + ln_adjxol;
                ln_sgpfac = ln_sgpfac + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fgpfac : 0);
                ln_scomfac = ln_scomfac + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fcomfac : 0);
                ln_spayfac = ln_spayfac + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fpayfac : 0);
                ln_sgpfacb = ln_sgpfacb + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fgpfacb : 0);
                ln_scomfacb = ln_scomfacb + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fcomfacb : 0);
                ln_spayfacb = ln_spayfacb + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fpayfacb : 0);

                ln_sgpfacnp = ln_sgpfacnp + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fgpfacnp : 0);
                ln_scomfacnp = ln_scomfacnp + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fcomfacnp : 0);
                ln_spayfacnp = ln_spayfacnp + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fpayfacnp : 0);

                ln_tgpm = ln_tgpm + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fgpm : 0);
                ln_tcomamt = ln_tcomamt + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fcomamt : 0);
                ln_tnpm = ln_tnpm + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fnpm : 0);
                ln_tlvyamt1 = ln_tlvyamt1 + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? flvyamt1 : 0);
                ln_tlvyamt2 = ln_tlvyamt2 + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? flvyamt2 : 0);
                ln_tlvyamt3 = ln_tlvyamt3 + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? flvyamt3 : 0);
                ln_tgpretn = ln_tgpretn + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fgpretn : 0);
                ln_tgptty = ln_tgptty + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fgptty : 0);
                ln_tcomtty = ln_tcomtty + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fcomtty : 0);
                ln_tpaytty = ln_tpaytty + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fpaytty : 0);

                ln_tgpxol = ln_tgpxol + ln_adjxol;
                ln_tgpfac = ln_tgpfac + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fgpfac : 0);
                ln_tcomfac = ln_tcomfac + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fcomfac : 0);
                ln_tpayfac = ln_tpayfac + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fpayfac : 0);
                ln_tgpfacb = ln_tgpfacb + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fgpfacb : 0);
                ln_tcomfacb = ln_tcomfacb + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fcomfacb : 0);
                ln_tpayfacb = ln_tpayfacb + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fpayfacb : 0);

                ln_tgpfacnp = ln_tgpfacnp + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fgpfacnp : 0);
                ln_tcomfacnp = ln_tcomfacnp + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fcomfacnp : 0);
                ln_tpayfacnp = ln_tpayfacnp + (sheetNo <= 5 || fstatus.Substring(0, 1) == "2" ? fpayfacnp : 0);

                iRow++;
            }

            iRow++;
            k = 10;
            xlWorkSheet.Cells[iRow, k++] = "Sub Total:";
            xlWorkSheet.Cells[iRow, k++] = ln_sgpm;
            xlWorkSheet.Cells[iRow, k++] = ln_scomamt;
            xlWorkSheet.Cells[iRow, k++] = ln_snpm;
            xlWorkSheet.Cells[iRow, k++] = ln_slvyamt1;
            xlWorkSheet.Cells[iRow, k++] = ln_slvyamt2;
            xlWorkSheet.Cells[iRow, k++] = ln_slvyamt3;
            xlWorkSheet.Cells[iRow, k++] = ln_sgpretn - ln_sgpfacnp;
            xlWorkSheet.Cells[iRow, k++] = ln_sgptty;
            xlWorkSheet.Cells[iRow, k++] = ln_scomtty;
            xlWorkSheet.Cells[iRow, k++] = ln_spaytty;

            xlWorkSheet.Cells[iRow, k++] = ln_sgpxol;

            xlWorkSheet.Cells[iRow, k++] = ln_sgpfac;
            xlWorkSheet.Cells[iRow, k++] = ln_scomfac;
            xlWorkSheet.Cells[iRow, k++] = ln_spayfac;
            xlWorkSheet.Cells[iRow, k++] = ln_sgpfacb;
            xlWorkSheet.Cells[iRow, k++] = ln_scomfacb;
            xlWorkSheet.Cells[iRow, k++] = ln_spayfacb;

            xlWorkSheet.Cells[iRow, k++] = ln_sgpfacnp;
            xlWorkSheet.Cells[iRow, k++] = ln_scomfacnp;
            xlWorkSheet.Cells[iRow, k++] = ln_spayfacnp;

            xlWorkSheet.Cells[iRow, k++] = ln_sgptty + ln_sgpxol + ln_sgpfac + ln_sgpfacb + ln_sgpfacnp;
            xlWorkSheet.Cells[iRow, k++] = ln_sgpm - (ln_sgptty + ln_sgpxol + ln_sgpfac + ln_sgpfacb + ln_sgpfacnp);
            xlWorkSheet.Cells[iRow, k++] = ln_scomamt;
            xlWorkSheet.Cells[iRow, k++] = ln_scomtty + ln_scomfac + ln_scomfacb + ln_scomfacnp;
            xlWorkSheet.Cells[iRow, k++] = ln_scomamt - (ln_scomtty + ln_scomfac + ln_scomfacb + ln_scomfacnp);

            xlWorkSheet.get_Range(String.Format("J{0}", iRow)).Columns.HorizontalAlignment = 4;
            iRow += 2;

            k = 10;
            xlWorkSheet.Cells[iRow, k++] = "Grand Total:";
            xlWorkSheet.Cells[iRow, k++] = ln_tgpm;
            xlWorkSheet.Cells[iRow, k++] = ln_tcomamt;
            xlWorkSheet.Cells[iRow, k++] = ln_tnpm;
            xlWorkSheet.Cells[iRow, k++] = ln_tlvyamt1;
            xlWorkSheet.Cells[iRow, k++] = ln_tlvyamt2;
            xlWorkSheet.Cells[iRow, k++] = ln_tlvyamt3;
            xlWorkSheet.Cells[iRow, k++] = ln_tgpretn - ln_tgpfacnp;
            xlWorkSheet.Cells[iRow, k++] = ln_tgptty;
            xlWorkSheet.Cells[iRow, k++] = ln_tcomtty;
            xlWorkSheet.Cells[iRow, k++] = ln_tpaytty;

            xlWorkSheet.Cells[iRow, k++] = ln_tgpxol;

            xlWorkSheet.Cells[iRow, k++] = ln_tgpfac;
            xlWorkSheet.Cells[iRow, k++] = ln_tcomfac;
            xlWorkSheet.Cells[iRow, k++] = ln_tpayfac;
            xlWorkSheet.Cells[iRow, k++] = ln_tgpfacb;
            xlWorkSheet.Cells[iRow, k++] = ln_tcomfacb;
            xlWorkSheet.Cells[iRow, k++] = ln_tpayfacb;

            xlWorkSheet.Cells[iRow, k++] = ln_tgpfacnp;
            xlWorkSheet.Cells[iRow, k++] = ln_tcomfacnp;
            xlWorkSheet.Cells[iRow, k++] = ln_tpayfacnp;

            xlWorkSheet.Cells[iRow, k++] = ln_tgptty + ln_tgpxol + ln_tgpfac + ln_tgpfacb + ln_tgpfacnp;
            xlWorkSheet.Cells[iRow, k++] = ln_tgpm - (ln_tgptty + ln_tgpxol + ln_tgpfac + ln_tgpfacb + ln_tgpfacnp);
            xlWorkSheet.Cells[iRow, k++] = ln_tcomamt;
            xlWorkSheet.Cells[iRow, k++] = ln_tcomtty + ln_tcomfac + ln_tcomfacb + ln_tcomfacnp;
            xlWorkSheet.Cells[iRow, k++] = ln_tcomamt - (ln_tcomtty + ln_tcomfac + ln_tcomfacb + ln_tcomfacnp);

            xlWorkSheet.get_Range(String.Format("J{0}", iRow)).Columns.HorizontalAlignment = 4;

            return;
        }
     
        public static void WbPrmUnIv(Excel.Workbook xlWorkBook, DataSet ds, DateTime vlastasat,
                                DateTime vthisasat,DateTime vfbk_fr, DateTime vfbk_to, string optid, string z_company,
                                string z_title, string z_currency, string z_period)
        {
            for (Int16 k = 1; k <= 6; k++)
            {
                Int16 i, tableIndex;
                Excel.Worksheet xlWorkSheet;
                Boolean noDetail = false;

                xlWorkSheet = xlWorkBook.Worksheets.get_Item(k);

                if (k == 1)
                    tableIndex = 0;
                else
                    tableIndex = 1;
                   
                if (k == 1)
                {
                    ds.Tables[tableIndex].DefaultView.RowFilter = "";
                    xlWorkSheet.Name = "Summary";
                }

                if (k == 2)
                {
                    ds.Tables[tableIndex].DefaultView.RowFilter = "fstatus like '1*'";
                    ds.Tables[tableIndex].DefaultView.Sort = "fclsseq, fpolno, fendtno, finstall";
                    xlWorkSheet.Name = "As at " + vlastasat.ToString("yyyy.MM.dd");
                    z_title = "Univoiced Premium as at " + vlastasat.ToString("dd MMM yyyy");
                }

                if (k == 3)
                {
                    ds.Tables[tableIndex].DefaultView.RowFilter = "fstatus = '1A' or fstatus like '2*'";
                    ds.Tables[tableIndex].DefaultView.Sort = "fclsseq, fpolno, fendtno, finstall";
                    xlWorkSheet.Name = "As at " + vthisasat.ToString("yyyy.MM.dd");
                    z_title = "Univoiced Premium as at " + vthisasat.ToString("dd MMM yyyy");
                }

                if (k == 4)
                {
                    ds.Tables[tableIndex].DefaultView.RowFilter = "fstatus = '1B'";
                    ds.Tables[tableIndex].DefaultView.Sort = "fclsseq, fpolno, fendtno, finstall";
                    xlWorkSheet.Name = "Billed";
                    z_title = String.Format("Billed Premium From {0} To {1}",
                                            vfbk_fr.ToString("dd MMM yyyy"), vfbk_to.ToString("dd MMM yyyy"));
                }

                if (k == 5)
                {
                    ds.Tables[tableIndex].DefaultView.RowFilter = "fstatus like '2*'";
                    ds.Tables[tableIndex].DefaultView.Sort = "fclsseq, fpolno, fendtno, finstall";
                    xlWorkSheet.Name = "New Entries";
                    z_title = String.Format("New Entries From {0} To {1}",
                                            vfbk_fr.ToString("dd MMM yyyy"), vfbk_to.ToString("dd MMM yyyy"));
                }

                if (k == 6)
                {
                    ds.Tables[tableIndex].DefaultView.RowFilter = "fstatus like '1*'";
                    ds.Tables[tableIndex].DefaultView.Sort = "fclsseq, fpolno, fendtno, finstall";
                    xlWorkSheet.Name = "Adjustment";
                    z_title = String.Format("Adjusted Premium From {0} To {1}",
                                            vfbk_fr.ToString("dd MMM yyyy"), vfbk_to.ToString("dd MMM yyyy"));
                }

                if (ds.Tables[tableIndex].DefaultView.Count == 0)
                    noDetail = true;
                else if (ds.Tables[tableIndex].DefaultView[0]["fclass"].ToString().Trim() == "???")
                    noDetail = true;

                i = RptInExcel.WsHeader(xlWorkSheet, optid, z_company, z_title, z_currency, z_period);
                i = RptInExcel.WsColTitlePrmUniv(xlWorkSheet, k, vlastasat, vthisasat, noDetail, optid, i);

                if (!noDetail)
                {
                    if (k == 1)
                        RptInExcel.WsPrmUnIvSumm(xlWorkSheet, ds.Tables[tableIndex], optid, i);

                    if (k != 1)
                    {

                        RptInExcel.WsPrmUnIvDetail(xlWorkSheet, ds.Tables[tableIndex].DefaultView.ToTable(), optid, k, i);
                    }
                }
            }
        
            return;
        }
     }
}
