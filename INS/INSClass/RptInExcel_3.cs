using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

using Excel = Microsoft.Office.Interop.Excel;

namespace INS.INSClass
{
    partial class RptInExcel
    {
        public static Int16 WsColTitleEffPMP(Excel.Worksheet xlWorkSheet, Boolean noDetail, string optid, Int16 startRow)
        {
            Int16 i = startRow;

            xlWorkSheet.Cells.Font.Name = "arial";
            xlWorkSheet.Cells.Font.Size = 8;

            xlWorkSheet.Columns["A"].ColumnWidth = 11;
            xlWorkSheet.Columns["B:C"].ColumnWidth = 8;
            xlWorkSheet.Columns["D"].ColumnWidth = 38;
            xlWorkSheet.Columns["E:H"].ColumnWidth = 8;
            xlWorkSheet.Columns["I:K"].ColumnWidth = 13;

            xlWorkSheet.Columns["I:K"].HorizontalAlignment = 4;
            xlWorkSheet.Columns["I:K"].NumberFormat = "#,###,###.00";

            xlWorkSheet.Columns["E:G"].NumberFormat = "yyyy/mm/dd";

            xlWorkSheet.get_Range("J3", "J3").HorizontalAlignment = 4;
            xlWorkSheet.get_Range("K3", "K3").HorizontalAlignment = 2;
            xlWorkSheet.get_Range("K3", "K3").NumberFormat = "yyyy/mm/dd";

            xlWorkSheet.Cells[i, 10] = "Date: ";
            xlWorkSheet.Cells[i, 11] = DateTime.Now;

            if (noDetail)
            {
                i += 2;
                xlWorkSheet.Cells[i, 1] = "No Record Found!";
                xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                i++;

                return i;
            }

            i += 2;
            Int16 k = 1;

            xlWorkSheet.get_Range(String.Format("A{0}", i), String.Format("K{0}", i)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = Excel.XlLineStyle.xlContinuous;
            xlWorkSheet.Cells[i, k++] = "Policy No";
            xlWorkSheet.Cells[i, k++] = "Prdr#";
            xlWorkSheet.Cells[i, k++] = "Clnt#";
            xlWorkSheet.Cells[i, k++] = "Insured";
            xlWorkSheet.Cells[i, k++] = "Issued Date";

            xlWorkSheet.Cells[i, k++] = "Incp From";
            xlWorkSheet.Cells[i, k++] = "Incp To";
            xlWorkSheet.Cells[i, k++] = "Reg No.";
            xlWorkSheet.Cells[i, k++] = "Sum Insured";
            xlWorkSheet.Cells[i, k++] = "TPBI";
            xlWorkSheet.Cells[i, k++] = "TPPD";

            i += 2;
            return i;
        }

        public static Int16 WsColTitlePrmStat(Excel.Worksheet xlWorkSheet, Boolean noDetail, string optid, Int32 refYear, Int16 startRow)
        {
            Int16 i = startRow;

            xlWorkSheet.Cells.Font.Name = "arial";
            xlWorkSheet.Cells.Font.Size = 10;

            xlWorkSheet.Columns["A"].ColumnWidth = 30;
            xlWorkSheet.Columns["B:K"].ColumnWidth = 15;

            xlWorkSheet.Columns["B:K"].HorizontalAlignment = 4;
            xlWorkSheet.Columns["B:K"].NumberFormat = "#,###,###.00";

            xlWorkSheet.get_Range(String.Format("J{0}", i), String.Format("J{0}", i)).HorizontalAlignment = 4;
            xlWorkSheet.get_Range(String.Format("K{0}", i), String.Format("K{0}", i)).HorizontalAlignment = 2;
            xlWorkSheet.get_Range(String.Format("K{0}", i), String.Format("K{0}", i)).NumberFormat = "yyyy/mm/dd";

            xlWorkSheet.Cells[i, 10] = "Date: ";
            xlWorkSheet.Cells[i, 11] = DateTime.Now;

            if (noDetail)
            {
                i += 2;
                xlWorkSheet.Cells[i, 1] = "No Record Found!";
                xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                i++;

                return i;
            }

            i += 2;
            Int16 k = 1;

            xlWorkSheet.get_Range(String.Format("A{0}", i), String.Format("K{0}", i)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = Excel.XlLineStyle.xlContinuous;
            xlWorkSheet.Cells[i, k++] = "Class";
            xlWorkSheet.Cells[i, k++] = "Prior Years";
            xlWorkSheet.Cells[i, k++] = String.Format("'{0:0000}", refYear - 6);
            xlWorkSheet.Cells[i, k++] = String.Format("'{0:0000}", refYear - 5);
            xlWorkSheet.Cells[i, k++] = String.Format("'{0:0000}", refYear - 4);

            xlWorkSheet.Cells[i, k++] = String.Format("'{0:0000}", refYear - 3);
            xlWorkSheet.Cells[i, k++] = String.Format("'{0:0000}", refYear - 2);
            xlWorkSheet.Cells[i, k++] = String.Format("'{0:0000}", refYear - 1);

            xlWorkSheet.Cells[i, k++] = String.Format("'{0:0000}", refYear);

            xlWorkSheet.Cells[i, k++] = String.Format("Beyond {0:0000}", refYear); ;
            xlWorkSheet.Cells[i, k++] = "Total";

            i += 2;
            return i;
        }


        public static Int16 WsColTitlePolPrmSm(Excel.Worksheet xlWorkSheet, Boolean noDetail, string optid, Int16 startRow)
        {
            Int16 i = startRow;

            xlWorkSheet.Cells.Font.Name = "arial";
            xlWorkSheet.Cells.Font.Size = 8;

            xlWorkSheet.Columns["A:A"].ColumnWidth = 5;
            xlWorkSheet.Columns["B:B"].ColumnWidth = 25;
            xlWorkSheet.Columns["C:C"].ColumnWidth = 13;

            xlWorkSheet.Columns["D:O"].ColumnWidth = 10;


            xlWorkSheet.Columns["D:O"].HorizontalAlignment = 4;

            xlWorkSheet.Columns["D:O"].NumberFormat = "#,##0_)";

            xlWorkSheet.get_Range("O3", "O3").HorizontalAlignment = 4;

            String dateStr;

            dateStr = String.Format("{0}年{1}月{2}日",DateTime.Now.ToString("yyyy"),
                DateTime.Now.ToString("MM"),DateTime.Now.ToString("dd"));

            xlWorkSheet.Cells[i, 15] = dateStr;

            if (noDetail)
            {
                i += 2;
                xlWorkSheet.Cells[i, 1] = "No Record Found!";
                xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                i++;

                return i;
            }

            i += 2;
            xlWorkSheet.Cells[i, 4] = "本月新增保單/批單";
            xlWorkSheet.Cells[i,10] = "本年累計新增保單/批單";

            xlWorkSheet.get_Range(String.Format("D{0}", i), String.Format("I{0}", i)).Merge();
            xlWorkSheet.get_Range(String.Format("J{0}", i), String.Format("O{0}", i)).Merge();
            xlWorkSheet.get_Range(String.Format("D{0}", i), String.Format("D{0}", i)).HorizontalAlignment = 3;
            xlWorkSheet.get_Range(String.Format("J{0}", i), String.Format("J{0}", i)).HorizontalAlignment = 3;

            i++;
            xlWorkSheet.Cells[i, 4] = "內部公司";
            xlWorkSheet.Cells[i, 6] = "外部客戶";
            xlWorkSheet.Cells[i, 8] = "小計";
            xlWorkSheet.Cells[i, 10] = "內部公司";
            xlWorkSheet.Cells[i, 12] = "外部客戶";
            xlWorkSheet.Cells[i, 14] = "合計";

            xlWorkSheet.get_Range(String.Format("D{0}", i), String.Format("E{0}", i)).Merge();
            xlWorkSheet.get_Range(String.Format("F{0}", i), String.Format("G{0}", i)).Merge();
            xlWorkSheet.get_Range(String.Format("H{0}", i), String.Format("I{0}", i)).Merge();
            xlWorkSheet.get_Range(String.Format("J{0}", i), String.Format("K{0}", i)).Merge();
            xlWorkSheet.get_Range(String.Format("L{0}", i), String.Format("M{0}", i)).Merge();
            xlWorkSheet.get_Range(String.Format("N{0}", i), String.Format("O{0}", i)).Merge();
            xlWorkSheet.get_Range(String.Format("D{0}", i), String.Format("D{0}", i)).HorizontalAlignment = 3;
            xlWorkSheet.get_Range(String.Format("F{0}", i), String.Format("F{0}", i)).HorizontalAlignment = 3;
            xlWorkSheet.get_Range(String.Format("H{0}", i), String.Format("H{0}", i)).HorizontalAlignment = 3;
            xlWorkSheet.get_Range(String.Format("J{0}", i), String.Format("J{0}", i)).HorizontalAlignment = 3;
            xlWorkSheet.get_Range(String.Format("L{0}", i), String.Format("L{0}", i)).HorizontalAlignment = 3;
            xlWorkSheet.get_Range(String.Format("N{0}", i), String.Format("N{0}", i)).HorizontalAlignment = 3;

            i++;
            Int16 k = 1;

            xlWorkSheet.Cells[i, k++] = "序";
            xlWorkSheet.Cells[i, k++] = "保險種類";
            k++;

            xlWorkSheet.Cells[i, k++] = "數量";
            xlWorkSheet.Cells[i, k++] = "保單保費";
            xlWorkSheet.Cells[i, k++] = "數量";
            xlWorkSheet.Cells[i, k++] = "保單保費";
            xlWorkSheet.Cells[i, k++] = "數量";
            xlWorkSheet.Cells[i, k++] = "保單保費";
            xlWorkSheet.Cells[i, k++] = "數量";
            xlWorkSheet.Cells[i, k++] = "保單保費";
            xlWorkSheet.Cells[i, k++] = "數量";
            xlWorkSheet.Cells[i, k++] = "保單保費";
            xlWorkSheet.Cells[i, k++] = "數量";
            xlWorkSheet.Cells[i, k++] = "保單保費";
            xlWorkSheet.get_Range(String.Format("D{0}", i), String.Format("O{0}", i)).HorizontalAlignment = 3;
            //xlWorkSheet.get_Range(String.Format("A{0}", i), String.Format("F{0}", i)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = Excel.XlLineStyle.xlContinuous;

            i += 2;

            return i;
        }

        public static Int16 WsColTitlePrmTty(Excel.Worksheet xlWorkSheet, Boolean noDetail, string optid, Int16 startRow)
        {
            Int16 i = startRow;

            xlWorkSheet.Cells.Font.Name = "arial";
            xlWorkSheet.Cells.Font.Size = 8;

            xlWorkSheet.Columns["A:B"].ColumnWidth = 15;
            xlWorkSheet.Columns["C:F"].ColumnWidth = 18;

            xlWorkSheet.Columns["C:F"].HorizontalAlignment = 4;

            xlWorkSheet.Columns["C:F"].NumberFormat = "#,###,###.00";

            xlWorkSheet.get_Range("E4", "E4").HorizontalAlignment = 4;
            xlWorkSheet.get_Range("F4", "F4").HorizontalAlignment = 2;
            xlWorkSheet.get_Range("F4", "F4").NumberFormat = "yyyy/mm/dd";

            i++;

            xlWorkSheet.Cells[i, 5] = "Date: ";
            xlWorkSheet.Cells[i, 6] = DateTime.Now;

            if (noDetail)
            {
                i += 2;
                xlWorkSheet.Cells[i, 1] = "No Record Found!";
                xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                i++;

                return i;
            }

            i += 2;
            Int16 k = 1;

            xlWorkSheet.get_Range(String.Format("A{0}", i), String.Format("F{0}", i)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = Excel.XlLineStyle.xlContinuous;
            xlWorkSheet.Cells[i, k++] = "Policy No";
            xlWorkSheet.Cells[i, k++] = "Endt No";
            xlWorkSheet.Cells[i, k++] = "Gross TTY Prem";
            xlWorkSheet.Cells[i, k++] = "Treaty Premium";
            xlWorkSheet.Cells[i, k++] = "R/I Commission";
            xlWorkSheet.Cells[i, k++] = "Net Amount";

            //i += 2;

            return i;
        }

        public static Int16 WsColTitlePrmBordSUR(Excel.Worksheet xlWorkSheet, Boolean noDetail, string fdbdesc, string optid, Int16 startRow)
        {
            Int16 i = startRow;

            xlWorkSheet.Cells.Font.Name = "arial";
            xlWorkSheet.Cells.Font.Size = 10;

            xlWorkSheet.Columns["A:C"].ColumnWidth = 16;
            xlWorkSheet.Columns["D:I"].ColumnWidth = 18;

            xlWorkSheet.Columns["D:I"].HorizontalAlignment = 4;
            xlWorkSheet.Columns["D:I"].NumberFormat = "#,###,###.00";

            xlWorkSheet.get_Range("H4", "H4").HorizontalAlignment = 4;
            xlWorkSheet.get_Range("I4", "I4").HorizontalAlignment = 2;
            xlWorkSheet.get_Range("I4", "I4").NumberFormat = "yyyy/mm/dd";

            xlWorkSheet.Cells[i, 8] = "Date: ";
            xlWorkSheet.Cells[i, 9] = DateTime.Now;

            i+=2;

            xlWorkSheet.Cells[i, 1] = fdbdesc;

            if (noDetail)
            {
                i += 2;
                xlWorkSheet.Cells[i, 1] = "No Record Found!";
                xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                i++;

                return i;
            }

            i += 2;
            Int16 k = 1;

            xlWorkSheet.get_Range(String.Format("A{0}", i + 2), String.Format("I{0}", i + 2)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = Excel.XlLineStyle.xlContinuous;
            xlWorkSheet.get_Range(String.Format("D{0}", i), String.Format("I{0}", i + 2)).HorizontalAlignment = 3;
           
            xlWorkSheet.Cells[i + 2, k++] = "Policy No";
            xlWorkSheet.Cells[i + 2, k++] = "Endt No";
            xlWorkSheet.Cells[i + 2, k++] = "Cession No";

            xlWorkSheet.Cells[i + 1, k] = "Original Sum";
            xlWorkSheet.Cells[i + 2, k++] = "Insured";

            xlWorkSheet.Cells[i + 1, k] = "Surplus Treaty";
            xlWorkSheet.Cells[i + 2, k++] = "Sum Insured";

            xlWorkSheet.Cells[i, k] = "Treaty";
            xlWorkSheet.Cells[i + 1, k] = "Premium on";
            xlWorkSheet.Cells[i + 2, k++] = "Gross";

            xlWorkSheet.Cells[i, k] = "Treaty";
            xlWorkSheet.Cells[i + 1, k] = "Premium on";
            xlWorkSheet.Cells[i + 2, k++] = "Net";
            
            xlWorkSheet.Cells[i+2, k++] = "R/I Comm";
            xlWorkSheet.Cells[i+2, k++] = "Net Amount";

            i += 2;

            return i;
        }

        public static Int16 WsColTitlePrmBordFOB(Excel.Worksheet xlWorkSheet, Boolean noDetail, string fdbdesc, string optid, Int16 startRow)
        {
            Int16 i = startRow;

            xlWorkSheet.Cells.Font.Name = "arial";
            xlWorkSheet.Cells.Font.Size = 8;

            xlWorkSheet.Columns["A:B"].ColumnWidth = 11;
            xlWorkSheet.Columns["C:F"].ColumnWidth = 8;
            xlWorkSheet.Columns["G:H"].ColumnWidth = 33;
            xlWorkSheet.Columns["I:O"].ColumnWidth = 13;

            xlWorkSheet.Columns["C:F"].NumberFormat = "yyyy/mm/dd"; ;
            xlWorkSheet.Columns["I:O"].HorizontalAlignment = 4;
            xlWorkSheet.Columns["I:O"].NumberFormat = "#,###,###.00";

            xlWorkSheet.get_Range("N4", "N4").HorizontalAlignment = 4;
            xlWorkSheet.get_Range("O4", "O4").HorizontalAlignment = 2;
            xlWorkSheet.get_Range("O4", "O4").NumberFormat = "yyyy/mm/dd";

            xlWorkSheet.Cells[i, 14] = "Date: ";
            xlWorkSheet.Cells[i, 15] = DateTime.Now;

            i += 2;

            xlWorkSheet.Cells[i, 1] = fdbdesc;

            if (noDetail)
            {
                i += 2;
                xlWorkSheet.Cells[i, 1] = "No Record Found!";
                xlWorkSheet.get_Range(String.Format("A{0}", i)).Font.Bold = true;

                i++;

                return i;
            }

            i += 2;
            Int16 k = 1;

            xlWorkSheet.get_Range(String.Format("A{0}", i + 1), String.Format("O{0}", i + 1)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = Excel.XlLineStyle.xlContinuous;
            xlWorkSheet.get_Range(String.Format("I{0}", i), String.Format("O{0}", i + 1)).HorizontalAlignment = 3;

            xlWorkSheet.Cells[i + 1, k++] = "Policy No";
            xlWorkSheet.Cells[i + 1, k++] = "Endt No";
            xlWorkSheet.Cells[i + 1, k++] = "Effect Fr";
            xlWorkSheet.Cells[i + 1, k++] = "Effect To";
            xlWorkSheet.Cells[i + 1, k++] = "Mnt Fr";
            xlWorkSheet.Cells[i + 1, k++] = "Mnt To";
            xlWorkSheet.Cells[i + 1, k++] = "Insured";
            xlWorkSheet.Cells[i + 1, k++] = "Contract";

            xlWorkSheet.Cells[i, k] = "Original Sum";
            xlWorkSheet.Cells[i + 1, k++] = "Insured";

            xlWorkSheet.Cells[i + 1, k++] = "Original TPL";

            xlWorkSheet.Cells[i, k] = "Treaty";
            xlWorkSheet.Cells[i + 1, k++] = "Sum Insured";

            xlWorkSheet.Cells[i + 1, k++] = "Treaty TPL";

            xlWorkSheet.Cells[i, k] = "Treaty";
            xlWorkSheet.Cells[i + 1, k++] = "Premium";

            xlWorkSheet.Cells[i + 1, k++] = "R/I Comm";
            xlWorkSheet.Cells[i + 1, k++] = "Net Amount";

            i ++;

            return i;
        }

        public static void WsEffPMPDetail(Excel.Worksheet xlWorkSheet, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fclsseq = "", lc_fkind = "", fclsname = "", fcover = "";
            decimal ln_scase = 0,ln_tcase = 0;

            foreach (DataRow dr in dt.Rows)
            {
                if (lc_fclsseq != dr["fclsseq"].ToString().Trim() ||
                    lc_fkind != dr["fkind"].ToString().Trim())
                {
                    if (iRow > startRow)
                    {
                        xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");

                        iRow += 2;
                    }

                    lc_fclsseq = dr["fclsseq"].ToString().Trim();
                    lc_fkind = dr["fkind"].ToString().Trim();
                    fclsname = dr["fclsname"].ToString().Trim();
                    fcover = dr["fcover"].ToString().Trim();

                    xlWorkSheet.Cells[iRow, 1] = String.Format("{0} - {1}", fclsname, fcover);
                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;

                    ln_scase = 0;

                    iRow += 2;
                }

                k = 1;

                xlWorkSheet.Cells[iRow, k++] = dr["fpolno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fprdr"];
                xlWorkSheet.Cells[iRow, k++] = dr["fclnt"];
                xlWorkSheet.Cells[iRow, k++] = dr["finsd"] + ((char)13).ToString() + ((char)10).ToString();
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fissdate"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fincfr"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dr["fincto"]);
                xlWorkSheet.Cells[iRow, k++] = dr["frno"];
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDecimal(dr["fupdsi"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDecimal(dr["ftpbi"]);
                xlWorkSheet.Cells[iRow, k++] = Convert.ToDecimal(dr["ftppd"]);

                xlWorkSheet.get_Range(String.Format("D{0}", iRow), String.Format("D{0}", iRow + 1)).Merge();
                xlWorkSheet.get_Range(String.Format("D{0}", iRow), String.Format("D{0}", iRow + 1)).HorizontalAlignment = 1;
                xlWorkSheet.get_Range(String.Format("D{0}", iRow), String.Format("D{0}", iRow + 1)).VerticalAlignment = 1;
                xlWorkSheet.get_Range(String.Format("D{0}", iRow), String.Format("D{0}", iRow)).RowHeight = 11.25;

                ln_scase = ln_scase + 1;
                ln_tcase = ln_tcase + 1;
                iRow += 2;
            }

            xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_scase.ToString("N0");

            iRow += 2;

            xlWorkSheet.Cells[iRow, 1] = "Total of Cases: " + ln_tcase.ToString("N0");

            return;
        }

        public static void WsPrmStatDetail(Excel.Worksheet xlWorkSheet, DataTable dt, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            decimal ln_tamt1 = 0, ln_tamt2 = 0, ln_tamt3 = 0, ln_tamt4 = 0, ln_tamt5 = 0, ln_tamt6 = 0,
                ln_tamt7 = 0, ln_tamt8 = 0, ln_tamt8b = 0, ln_tamt8c = 0, ln_trowamt = 0;

            foreach (DataRow dr in dt.Rows)
            {
                decimal famt1 = Convert.ToDecimal(dr["famt1"]), famt2 = Convert.ToDecimal(dr["famt2"]),
                    famt3 = Convert.ToDecimal(dr["famt3"]), famt4 = Convert.ToDecimal(dr["famt4"]),
                    famt5 = Convert.ToDecimal(dr["famt5"]), famt6 = Convert.ToDecimal(dr["famt6"]),
                    famt7 = Convert.ToDecimal(dr["famt7"]), famt8 = Convert.ToDecimal(dr["famt8"]),
                    famt8b = Convert.ToDecimal(dr["famt8b"]), famt8c = Convert.ToDecimal(dr["famt8c"]),
                    rowamt;

                rowamt = famt1 + famt2 + famt3 + famt4 + famt5 + famt6 + famt7 + famt8b + famt8c;
                
                xlWorkSheet.Cells[iRow, 1] = dr["fclsname"];
                k = 2;

                xlWorkSheet.Cells[iRow, k++] = famt1;
                xlWorkSheet.Cells[iRow, k++] = famt2;
                xlWorkSheet.Cells[iRow, k++] = famt3;
                xlWorkSheet.Cells[iRow, k++] = famt4;
                xlWorkSheet.Cells[iRow, k++] = famt5;
                xlWorkSheet.Cells[iRow, k++] = famt6;
                xlWorkSheet.Cells[iRow, k++] = famt7;
                xlWorkSheet.Cells[iRow, k++] = famt8b;
                xlWorkSheet.Cells[iRow, k++] = famt8c;
                xlWorkSheet.Cells[iRow, k++] = rowamt;
 
                ln_tamt1 = ln_tamt1 + famt1;
                ln_tamt2 = ln_tamt2 + famt2;
                ln_tamt3 = ln_tamt3 + famt3;
                ln_tamt4 = ln_tamt4 + famt4;
                ln_tamt5 = ln_tamt5 + famt5;
                ln_tamt6 = ln_tamt6 + famt6;
                ln_tamt7 = ln_tamt7 + famt7;
                ln_tamt8 = ln_tamt8 + famt8;
                ln_tamt8b = ln_tamt8b + famt8b;
                ln_tamt8c = ln_tamt8c + famt8c;
                ln_trowamt = ln_trowamt + rowamt;

                iRow ++;
            }

            xlWorkSheet.Cells[iRow, 1] = "Grand Total: ";
            k = 2;

            xlWorkSheet.Cells[iRow, k++] = ln_tamt1;
            xlWorkSheet.Cells[iRow, k++] = ln_tamt2;
            xlWorkSheet.Cells[iRow, k++] = ln_tamt3;
            xlWorkSheet.Cells[iRow, k++] = ln_tamt4;
            xlWorkSheet.Cells[iRow, k++] = ln_tamt5;
            xlWorkSheet.Cells[iRow, k++] = ln_tamt6;
            xlWorkSheet.Cells[iRow, k++] = ln_tamt7;
            xlWorkSheet.Cells[iRow, k++] = ln_tamt8b;
            xlWorkSheet.Cells[iRow, k++] = ln_tamt8c;
            xlWorkSheet.Cells[iRow, k++] = ln_trowamt;

            xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Columns.HorizontalAlignment = 4;
            xlWorkSheet.get_Range(String.Format("B{0}", iRow), String.Format("K{0}", iRow)).Borders[Excel.XlBordersIndex.xlEdgeTop].LineStyle = Excel.XlLineStyle.xlContinuous;
            xlWorkSheet.get_Range(String.Format("B{0}", iRow), String.Format("K{0}", iRow)).Borders[Excel.XlBordersIndex.xlEdgeBottom].LineStyle = Excel.XlLineStyle.xlDouble;
  
            return;
        }

        public static void WsPrmTtyDetail(Excel.Worksheet xlWorkSheet, DataTable dt, string vfbus, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fttycode = "", lc_fdbtr = "", lc_fdbdesc = "", lc_fclsseq = "", lc_fclsname = "";

            Boolean ll_ChangeClass, ll_ChangeDebtor, ll_ChangeTTY;

            decimal ln_s1case = 0, ln_s1gpm = 0, ln_s1ttypm = 0, ln_s1comamt = 0, ln_s1payable = 0;
            decimal ln_s2case = 0, ln_s2gpm = 0, ln_s2ttypm = 0, ln_s2comamt = 0, ln_s2payable = 0;
            decimal ln_s3case = 0, ln_s3gpm = 0, ln_s3ttypm = 0, ln_s3comamt = 0, ln_s3payable = 0;
            decimal ln_ttcase = 0, ln_ttgpm = 0, ln_ttttypm = 0, ln_ttcomamt = 0, ln_ttpayable = 0;

            foreach (DataRow dr in dt.Rows)
            {
                ll_ChangeClass = lc_fclsseq != dr["fclsseq"].ToString().Trim() || lc_fdbtr != dr["fdbtr"].ToString().Trim() ||
                    lc_fttycode != dr["fttycode"].ToString().Trim();
                ll_ChangeDebtor = lc_fdbtr != dr["fdbtr"].ToString().Trim() || lc_fttycode != dr["fttycode"].ToString().Trim();
                ll_ChangeTTY = lc_fttycode != dr["fttycode"].ToString().Trim();

                if (ll_ChangeClass)
                {
                    if (iRow > startRow)
                    {
                        //xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_s1case.ToString("N0");
                        k = 2;

                        xlWorkSheet.Cells[iRow, k++] = "Class Total: ";
                        xlWorkSheet.Cells[iRow, k++] = ln_s1gpm;
                        xlWorkSheet.Cells[iRow, k++] = ln_s1ttypm;
                        xlWorkSheet.Cells[iRow, k++] = ln_s1comamt;
                        xlWorkSheet.Cells[iRow, k++] = ln_s1payable;

                        xlWorkSheet.get_Range(String.Format("B{0}", iRow)).Columns.HorizontalAlignment = 4;
                    }

                    lc_fclsseq = dr["fclsseq"].ToString().Trim();
                    lc_fclsname = dr["fclsname"].ToString().Trim();

                    ln_s1case = 0; ln_s1gpm = 0; ln_s1ttypm = 0; ln_s1comamt = 0; ln_s1payable = 0;
                }

                if (ll_ChangeDebtor)
                {
                    if (iRow > startRow)
                    {
                        iRow++;
                        //xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_s2case.ToString("N0");
                        k = 2;

                        xlWorkSheet.Cells[iRow, k++] = "Reinsurer Total: ";
                        xlWorkSheet.Cells[iRow, k++] = ln_s2gpm;
                        xlWorkSheet.Cells[iRow, k++] = ln_s2ttypm;
                        xlWorkSheet.Cells[iRow, k++] = ln_s2comamt;
                        xlWorkSheet.Cells[iRow, k++] = ln_s2payable;

                        xlWorkSheet.get_Range(String.Format("B{0}", iRow)).Columns.HorizontalAlignment = 4;
                        iRow++;
                    }

                    lc_fdbtr = dr["fdbtr"].ToString().Trim();
                    lc_fdbdesc = dr["fdbdesc"].ToString().Trim();

                    ln_s2case = 0; ln_s2gpm = 0; ln_s2ttypm = 0; ln_s2comamt = 0; ln_s2payable = 0;
                }

                if (ll_ChangeTTY)
                {
                    if (iRow > startRow)
                    {
                        iRow++;
                        //xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_s2case.ToString("N0");
                        k = 2;

                        xlWorkSheet.Cells[iRow, k++] = "Treaty Total: ";
                        xlWorkSheet.Cells[iRow, k++] = ln_s3gpm;
                        xlWorkSheet.Cells[iRow, k++] = ln_s3ttypm;
                        xlWorkSheet.Cells[iRow, k++] = ln_s3comamt;
                        xlWorkSheet.Cells[iRow, k++] = ln_s3payable;

                        xlWorkSheet.get_Range(String.Format("B{0}", iRow)).Columns.HorizontalAlignment = 4;
                    }

                    lc_fttycode = dr["fttycode"].ToString().Trim();

                    ln_s3case = 0; ln_s3gpm = 0; ln_s3ttypm = 0; ln_s3comamt = 0; ln_s3payable = 0;

                    iRow += 2;

                    xlWorkSheet.Cells[iRow, 1] = lc_fttycode;
                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;
                }

                if (ll_ChangeDebtor)
                {
                    iRow++;
                    xlWorkSheet.Cells[iRow, 1] = lc_fdbtr + " - " + lc_fdbdesc;
                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;
                }

                if (ll_ChangeClass)
                {
                    iRow++;
                    xlWorkSheet.Cells[iRow, 1] = lc_fclsname;
                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;
                    iRow++;
                }

                decimal fgpm = Convert.ToDecimal(dr["fgpm"]);
                decimal fttypm = Convert.ToDecimal(dr["fttypm"]);
                decimal fcomamt = Convert.ToDecimal(dr["fcomamt"]);
                decimal fpayable = Convert.ToDecimal(dr["fpayable"]);

                k = 1;
                xlWorkSheet.Cells[iRow, k++] = dr["fpolno"];
                xlWorkSheet.Cells[iRow, k++] = dr["fendtno"];

                xlWorkSheet.Cells[iRow, k++] = fgpm;
                xlWorkSheet.Cells[iRow, k++] = fttypm;
                xlWorkSheet.Cells[iRow, k++] = fcomamt;
                xlWorkSheet.Cells[iRow, k++] = fpayable;

                ln_s1case++;
                ln_s1gpm = ln_s1gpm + fgpm;
                ln_s1ttypm = ln_s1ttypm + fttypm;
                ln_s1comamt = ln_s1comamt + fcomamt;
                ln_s1payable = ln_s1payable + fpayable;

                ln_s2case++;
                ln_s2gpm = ln_s2gpm + fgpm;
                ln_s2ttypm = ln_s2ttypm + fttypm;
                ln_s2comamt = ln_s2comamt + fcomamt;
                ln_s2payable = ln_s2payable + fpayable;

                ln_s3case++;
                ln_s3gpm = ln_s3gpm + fgpm;
                ln_s3ttypm = ln_s3ttypm + fttypm;
                ln_s3comamt = ln_s3comamt + fcomamt;
                ln_s3payable = ln_s3payable + fpayable;
               
                ln_ttcase++;
                ln_ttgpm = ln_ttgpm + fgpm;
                ln_ttttypm = ln_ttttypm + fttypm;
                ln_ttcomamt = ln_ttcomamt + fcomamt;
                ln_ttpayable = ln_ttpayable + fpayable;

                iRow++;
            }

            k = 2;

            xlWorkSheet.Cells[iRow, k++] = "Class Total: ";
            xlWorkSheet.Cells[iRow, k++] = ln_s1gpm;
            xlWorkSheet.Cells[iRow, k++] = ln_s1ttypm;
            xlWorkSheet.Cells[iRow, k++] = ln_s1comamt;
            xlWorkSheet.Cells[iRow, k++] = ln_s1payable;

            xlWorkSheet.get_Range(String.Format("B{0}", iRow)).Columns.HorizontalAlignment = 4;

            iRow++;
            k = 2;

            xlWorkSheet.Cells[iRow, k++] = "Reinsurer Total: ";
            xlWorkSheet.Cells[iRow, k++] = ln_s2gpm;
            xlWorkSheet.Cells[iRow, k++] = ln_s2ttypm;
            xlWorkSheet.Cells[iRow, k++] = ln_s2comamt;
            xlWorkSheet.Cells[iRow, k++] = ln_s2payable;

            xlWorkSheet.get_Range(String.Format("B{0}", iRow)).Columns.HorizontalAlignment = 4;

            iRow+=2;
            k = 2;

            xlWorkSheet.Cells[iRow, k++] = "Treaty Total: ";
            xlWorkSheet.Cells[iRow, k++] = ln_s3gpm;
            xlWorkSheet.Cells[iRow, k++] = ln_s3ttypm;
            xlWorkSheet.Cells[iRow, k++] = ln_s3comamt;
            xlWorkSheet.Cells[iRow, k++] = ln_s3payable;

            xlWorkSheet.get_Range(String.Format("B{0}", iRow)).Columns.HorizontalAlignment = 4;

            iRow += 2;
            k = 2;

            xlWorkSheet.Cells[iRow, k++] = "Grand Total: ";
            xlWorkSheet.Cells[iRow, k++] = ln_ttgpm;
            xlWorkSheet.Cells[iRow, k++] = ln_ttttypm;
            xlWorkSheet.Cells[iRow, k++] = ln_ttcomamt;
            xlWorkSheet.Cells[iRow, k++] = ln_ttpayable;

            xlWorkSheet.get_Range(String.Format("B{0}", iRow)).Columns.HorizontalAlignment = 4;

            return;
        }
        
        public static void WsPrmTtySumm(Excel.Worksheet xlWorkSheet, DataTable dt, string vfbus, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            string lc_fttycode = "", lc_fdbtr = "", lc_fdbdesc = "", lc_fclsseq = "", lc_fclsname = "";

            Boolean ll_ChangeClass, ll_ChangeDebtor, ll_ChangeTTY;

            decimal ln_s1case = 0, ln_s1gpm = 0, ln_s1ttypm = 0, ln_s1comamt = 0, ln_s1payable = 0;
            decimal ln_s2case = 0, ln_s2gpm = 0, ln_s2ttypm = 0, ln_s2comamt = 0, ln_s2payable = 0;
            decimal ln_s3case = 0, ln_s3gpm = 0, ln_s3ttypm = 0, ln_s3comamt = 0, ln_s3payable = 0;
            decimal ln_ttcase = 0, ln_ttgpm = 0, ln_ttttypm = 0, ln_ttcomamt = 0, ln_ttpayable = 0;

            foreach (DataRow dr in dt.Rows)
            {
                ll_ChangeClass = lc_fclsseq != dr["fclsseq"].ToString().Trim() || lc_fdbtr != dr["fdbtr"].ToString().Trim() ||
                    lc_fttycode != dr["fttycode"].ToString().Trim();
                ll_ChangeDebtor = lc_fdbtr != dr["fdbtr"].ToString().Trim() || lc_fttycode != dr["fttycode"].ToString().Trim();
                ll_ChangeTTY = lc_fttycode != dr["fttycode"].ToString().Trim();

                if (ll_ChangeClass)
                {
                    if (iRow > startRow)
                    {
                        xlWorkSheet.Cells[iRow, 1] = lc_fclsname;
                        k = 3;

                        xlWorkSheet.Cells[iRow, k++] = ln_s1gpm;
                        xlWorkSheet.Cells[iRow, k++] = ln_s1ttypm;
                        xlWorkSheet.Cells[iRow, k++] = ln_s1comamt;
                        xlWorkSheet.Cells[iRow, k++] = ln_s1payable;
                        iRow++;
                    }

                    lc_fclsseq = dr["fclsseq"].ToString().Trim();
                    lc_fclsname = dr["fclsname"].ToString().Trim();

                    ln_s1case = 0; ln_s1gpm = 0; ln_s1ttypm = 0; ln_s1comamt = 0; ln_s1payable = 0;
                }

                if (ll_ChangeDebtor)
                {
                    if (iRow > startRow)
                    {
                        k = 2;

                        xlWorkSheet.Cells[iRow, k++] = "Reinsurer Total: ";
                        xlWorkSheet.Cells[iRow, k++] = ln_s2gpm;
                        xlWorkSheet.Cells[iRow, k++] = ln_s2ttypm;
                        xlWorkSheet.Cells[iRow, k++] = ln_s2comamt;
                        xlWorkSheet.Cells[iRow, k++] = ln_s2payable;

                        xlWorkSheet.get_Range(String.Format("B{0}", iRow)).Columns.HorizontalAlignment = 4;
                        iRow++;
                    }

                    lc_fdbtr = dr["fdbtr"].ToString().Trim();
                    lc_fdbdesc = dr["fdbdesc"].ToString().Trim();

                    ln_s2case = 0; ln_s2gpm = 0; ln_s2ttypm = 0; ln_s2comamt = 0; ln_s2payable = 0;
                }

                if (ll_ChangeTTY)
                {
                    if (iRow > startRow)
                    {
                        iRow++;
                        //xlWorkSheet.Cells[iRow, 1] = "No. of Cases: " + ln_s2case.ToString("N0");
                        k = 2;

                        xlWorkSheet.Cells[iRow, k++] = "Treaty Total: ";
                        xlWorkSheet.Cells[iRow, k++] = ln_s3gpm;
                        xlWorkSheet.Cells[iRow, k++] = ln_s3ttypm;
                        xlWorkSheet.Cells[iRow, k++] = ln_s3comamt;
                        xlWorkSheet.Cells[iRow, k++] = ln_s3payable;

                        xlWorkSheet.get_Range(String.Format("B{0}", iRow)).Columns.HorizontalAlignment = 4;
                    }

                    lc_fttycode = dr["fttycode"].ToString().Trim();

                    ln_s3case = 0; ln_s3gpm = 0; ln_s3ttypm = 0; ln_s3comamt = 0; ln_s3payable = 0;

                    iRow += 2;

                    xlWorkSheet.Cells[iRow, 1] = lc_fttycode;
                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;
                }

                if (ll_ChangeDebtor)
                {
                    iRow++;
                    xlWorkSheet.Cells[iRow, 1] = lc_fdbtr + " - " + lc_fdbdesc;
                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;
                    iRow++;
                }


                decimal fgpm = Convert.ToDecimal(dr["fgpm"]);
                decimal fttypm = Convert.ToDecimal(dr["fttypm"]);
                decimal fcomamt = Convert.ToDecimal(dr["fcomamt"]);
                decimal fpayable = Convert.ToDecimal(dr["fpayable"]);

                ln_s1case++;
                ln_s1gpm = ln_s1gpm + fgpm;
                ln_s1ttypm = ln_s1ttypm + fttypm;
                ln_s1comamt = ln_s1comamt + fcomamt;
                ln_s1payable = ln_s1payable + fpayable;

                ln_s2case++;
                ln_s2gpm = ln_s2gpm + fgpm;
                ln_s2ttypm = ln_s2ttypm + fttypm;
                ln_s2comamt = ln_s2comamt + fcomamt;
                ln_s2payable = ln_s2payable + fpayable;

                ln_s3case++;
                ln_s3gpm = ln_s3gpm + fgpm;
                ln_s3ttypm = ln_s3ttypm + fttypm;
                ln_s3comamt = ln_s3comamt + fcomamt;
                ln_s3payable = ln_s3payable + fpayable;

                ln_ttcase++;
                ln_ttgpm = ln_ttgpm + fgpm;
                ln_ttttypm = ln_ttttypm + fttypm;
                ln_ttcomamt = ln_ttcomamt + fcomamt;
                ln_ttpayable = ln_ttpayable + fpayable;
            }

            xlWorkSheet.Cells[iRow, 1] = lc_fclsname;
            k = 3;

            xlWorkSheet.Cells[iRow, k++] = ln_s1gpm;
            xlWorkSheet.Cells[iRow, k++] = ln_s1ttypm;
            xlWorkSheet.Cells[iRow, k++] = ln_s1comamt;
            xlWorkSheet.Cells[iRow, k++] = ln_s1payable;
            iRow++;

            k = 2;

            xlWorkSheet.Cells[iRow, k++] = "Reinsurer Total: ";
            xlWorkSheet.Cells[iRow, k++] = ln_s2gpm;
            xlWorkSheet.Cells[iRow, k++] = ln_s2ttypm;
            xlWorkSheet.Cells[iRow, k++] = ln_s2comamt;
            xlWorkSheet.Cells[iRow, k++] = ln_s2payable;

            xlWorkSheet.get_Range(String.Format("B{0}", iRow)).Columns.HorizontalAlignment = 4;

            iRow += 2;
            k = 2;

            xlWorkSheet.Cells[iRow, k++] = "Treaty Total: ";
            xlWorkSheet.Cells[iRow, k++] = ln_s3gpm;
            xlWorkSheet.Cells[iRow, k++] = ln_s3ttypm;
            xlWorkSheet.Cells[iRow, k++] = ln_s3comamt;
            xlWorkSheet.Cells[iRow, k++] = ln_s3payable;

            xlWorkSheet.get_Range(String.Format("B{0}", iRow)).Columns.HorizontalAlignment = 4;

            iRow += 2;
            k = 2;

            xlWorkSheet.Cells[iRow, k++] = "Grand Total: ";
            xlWorkSheet.Cells[iRow, k++] = ln_ttgpm;
            xlWorkSheet.Cells[iRow, k++] = ln_ttttypm;
            xlWorkSheet.Cells[iRow, k++] = ln_ttcomamt;
            xlWorkSheet.Cells[iRow, k++] = ln_ttpayable;

            xlWorkSheet.get_Range(String.Format("B{0}", iRow)).Columns.HorizontalAlignment = 4;

            return;
        }

        public static void WsPrmBordDetailSUR(Excel.Worksheet xlWorkSheet, DataView dv, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            Decimal ln_fshare = 0;

            string lc_fttycode = "", lc_fttysec = "";

            Boolean ll_subR = false, ll_subS = false, ll_subT = false, ll_ChgTTYCode = false, ll_ChgTTYSec = false;
            
			decimal ln_Rttygm = 0, ln_Rttyprem = 0, ln_Rttycom = 0, ln_Rpaybrk = 0, 
                    ln_Rttygm_b = 0, ln_Rttyprem_b = 0, ln_Rttycom_b = 0, ln_Rpaybrk_b = 0;
			decimal ln_Sttygm = 0, ln_Sttyprem = 0, ln_Sttycom = 0, ln_Spaybrk = 0, 
                    ln_Sttygm_b = 0, ln_Sttyprem_b = 0, ln_Sttycom_b = 0, ln_Spaybrk_b = 0;
     		decimal ln_Tttygm = 0, ln_Tttyprem = 0, ln_Tttycom = 0, ln_Tpaybrk = 0, 
                    ln_Tttygm_b = 0, ln_Tttyprem_b = 0, ln_Tttycom_b = 0, ln_Tpaybrk_b = 0;

            for (int p = 0; p < dv.Count; p++)
            {
                ll_ChgTTYCode = lc_fttycode != dv[p]["fttycode"].ToString().Trim();
                ll_ChgTTYSec = ll_ChgTTYCode || lc_fttysec != dv[p]["fttysec"].ToString().Trim();

                if (ll_ChgTTYSec && ll_subR)
                {
                    iRow++;
                    k = 5;
                    
                    xlWorkSheet.Cells[iRow, k++] = "Section Total:";
                    xlWorkSheet.Cells[iRow, k++] = ln_Rttygm;
                    xlWorkSheet.Cells[iRow, k++] = ln_Rttyprem;
                    xlWorkSheet.Cells[iRow, k++] = ln_Rttycom;
                    xlWorkSheet.Cells[iRow, k++] = ln_Rpaybrk;

                    xlWorkSheet.get_Range(String.Format("E{0}", iRow)).HorizontalAlignment = 4;
               }

   		        if  (ll_ChgTTYCode && ll_subS)
                {
                    iRow++;
                    k = 5;

                    xlWorkSheet.Cells[iRow, k++] = "Treaty Total:";
                    xlWorkSheet.Cells[iRow, k++] = ln_Sttygm;
                    xlWorkSheet.Cells[iRow, k++] = ln_Sttyprem;
                    xlWorkSheet.Cells[iRow, k++] = ln_Sttycom;
                    xlWorkSheet.Cells[iRow, k++] = ln_Spaybrk;

                    xlWorkSheet.get_Range(String.Format("E{0}", iRow)).HorizontalAlignment = 4;

                    iRow++;
                    xlWorkSheet.Cells[iRow, 1] = String.Format("Your Share: {0} %",ln_fshare);
                    k = 6;

                    xlWorkSheet.Cells[iRow, k++] = ln_Sttygm_b;
                    xlWorkSheet.Cells[iRow, k++] = ln_Sttyprem_b;
                    xlWorkSheet.Cells[iRow, k++] =ln_Sttycom_b;
                    xlWorkSheet.Cells[iRow, k++] =ln_Spaybrk_b;
                }
                
                if (ll_ChgTTYSec)
                {
                    ll_subR = false;
                    ln_Rttygm = 0; ln_Rttyprem = 0; ln_Rttycom = 0; ln_Rpaybrk = 0; 
                    ln_Rttygm_b = 0; ln_Rttyprem_b = 0; ln_Rttycom_b = 0; ln_Rpaybrk_b = 0;
                }

                if (ll_ChgTTYCode)
                {
                    ll_subS = false;
                    ln_Sttygm = 0; ln_Sttyprem = 0; ln_Sttycom = 0; ln_Spaybrk = 0; 
                    ln_Sttygm_b = 0; ln_Sttyprem_b = 0; ln_Sttycom_b = 0; ln_Spaybrk_b = 0;
                }

                if (ll_ChgTTYSec)
                {
                    lc_fttycode = dv[p]["fttycode"].ToString().Trim();
                    lc_fttysec  = dv[p]["fttysec"].ToString().Trim();
                
                    iRow+=2;
                    xlWorkSheet.Cells[iRow, 1] = String.Format("Section {0}    ({1})",lc_fttysec,lc_fttycode);
                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;
                }
                
                iRow++;

                if (dv[p]["fpolno"].ToString().Trim() != "")
                {
                    decimal fsi = Convert.ToDecimal(dv[p]["fsi"]), fttysi = Convert.ToDecimal(dv[p]["fttysi"]),
                        fttygm = Convert.ToDecimal(dv[p]["fttygm"]), fttyprem = Convert.ToDecimal(dv[p]["fttyprem"]),
                        fttycom = Convert.ToDecimal(dv[p]["fttycom"]), fpaybrk = Convert.ToDecimal(dv[p]["fpaybrk"]),
                        fttygm_b = Convert.ToDecimal(dv[p]["fttygm_b"]), fttyprem_b = Convert.ToDecimal(dv[p]["fttyprem_b"]),
                        fttycom_b = Convert.ToDecimal(dv[p]["fttycom_b"]), fpaybrk_b = Convert.ToDecimal(dv[p]["fpaybrk_b"]);
                    
                    ll_subR = true;
                    ll_subS = true;
                    ll_subT = true;
                    
                    k = 1;

                    xlWorkSheet.Cells[iRow, k++] = dv[p]["fpolno"];
                    xlWorkSheet.Cells[iRow, k++] = dv[p]["fendtno"];
                    xlWorkSheet.Cells[iRow, k++] = dv[p]["fpolno"].ToString().Trim()+" - S00";
                    
                    if (fsi != 0)
                        xlWorkSheet.Cells[iRow, k] = fsi;
                    k++;
                    if(fttysi != 0)
                        xlWorkSheet.Cells[iRow, k] = fttysi;
                    k++;

                    xlWorkSheet.Cells[iRow, k++] = fttygm;
                    xlWorkSheet.Cells[iRow, k++] = fttyprem;
                    xlWorkSheet.Cells[iRow, k++] = fttycom;
                    xlWorkSheet.Cells[iRow, k++] = fpaybrk;

                    ln_fshare = Convert.ToDecimal(dv[p]["fshare"]);

                    ln_Rttygm = ln_Rttygm + fttygm;
                    ln_Rttyprem = ln_Rttyprem + fttyprem;
                    ln_Rttycom = ln_Rttycom + fttycom;
                    ln_Rpaybrk = ln_Rpaybrk + fpaybrk;
                    ln_Rttygm_b = ln_Rttygm_b + fttygm_b;
                    ln_Rttyprem_b = ln_Rttyprem_b + fttyprem_b;
                    ln_Rttycom_b = ln_Rttycom_b + fttycom_b;
                    ln_Rpaybrk_b = ln_Rpaybrk_b + fpaybrk_b;

                    ln_Sttygm = ln_Sttygm + fttygm;
                    ln_Sttyprem = ln_Sttyprem + fttyprem;
                    ln_Sttycom = ln_Sttycom + fttycom;
                    ln_Spaybrk = ln_Spaybrk + fpaybrk;
                    ln_Sttygm_b = ln_Sttygm_b + fttygm_b;
                    ln_Sttyprem_b = ln_Sttyprem_b + fttyprem_b;
                    ln_Sttycom_b = ln_Sttycom_b + fttycom_b;
                    ln_Spaybrk_b = ln_Spaybrk_b + fpaybrk_b;

                    ln_Tttygm = ln_Tttygm + fttygm;
                    ln_Tttyprem = ln_Tttyprem + fttyprem;
                    ln_Tttycom = ln_Tttycom + fttycom;
                    ln_Tpaybrk = ln_Tpaybrk + fpaybrk;
                    ln_Tttygm_b = ln_Tttygm_b + fttygm_b;
                    ln_Tttyprem_b = ln_Tttyprem_b + fttyprem_b;
                    ln_Tttycom_b = ln_Tttycom_b + fttycom_b;
                    ln_Tpaybrk_b = ln_Tpaybrk_b + fpaybrk_b;
                }
                else
                {
                    xlWorkSheet.Cells[iRow, 1] = "Nil";
                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;
                }
        
            }

            if (ll_subR)
            {
                iRow++;
                k = 5;

                xlWorkSheet.Cells[iRow, k++] = "Section Total:";
                xlWorkSheet.Cells[iRow, k++] = ln_Rttygm;
                xlWorkSheet.Cells[iRow, k++] = ln_Rttyprem;
                xlWorkSheet.Cells[iRow, k++] = ln_Rttycom;
                xlWorkSheet.Cells[iRow, k++] = ln_Rpaybrk;

                xlWorkSheet.get_Range(String.Format("E{0}", iRow)).HorizontalAlignment = 4;
            }

            if (ll_subS)
            {
                iRow++;
                k = 5;

                xlWorkSheet.Cells[iRow, k++] = "Treaty Total:";
                xlWorkSheet.Cells[iRow, k++] = ln_Sttygm;
                xlWorkSheet.Cells[iRow, k++] = ln_Sttyprem;
                xlWorkSheet.Cells[iRow, k++] = ln_Sttycom;
                xlWorkSheet.Cells[iRow, k++] = ln_Spaybrk;

                xlWorkSheet.get_Range(String.Format("E{0}", iRow)).HorizontalAlignment = 4;

                iRow++;
                xlWorkSheet.Cells[iRow, 1] = String.Format("Your Share: {0} %", ln_fshare);
                k = 6;

                xlWorkSheet.Cells[iRow, k++] = ln_Sttygm_b;
                xlWorkSheet.Cells[iRow, k++] = ln_Sttyprem_b;
                xlWorkSheet.Cells[iRow, k++] = ln_Sttycom_b;
                xlWorkSheet.Cells[iRow, k++] = ln_Spaybrk_b;

            }

            if (ll_subT)
            {
                iRow+=2;

                xlWorkSheet.Cells[iRow, 1] = "Your Share (All Treaty Years):";
                k = 6;

                xlWorkSheet.Cells[iRow, k++] = ln_Tttygm_b;
                xlWorkSheet.Cells[iRow, k++] = ln_Tttyprem_b;
                xlWorkSheet.Cells[iRow, k++] = ln_Tttycom_b;
                xlWorkSheet.Cells[iRow, k++] = ln_Tpaybrk_b;
            }

            return;
        }

        public static void WsPrmBordDetailFOB(Excel.Worksheet xlWorkSheet, DataView dv, string optid, Int16 startRow)
        {
            Int16 iRow = startRow, k;

            Decimal ln_fshare = 0;

            string lc_fttycode = "", lc_fttysec = "";

            Boolean ll_subR = false, ll_subS = false, ll_subT = false, ll_ChgTTYCode = false, ll_ChgTTYSec = false;

            decimal ln_Rttygm = 0, ln_Rttyprem = 0, ln_Rttycom = 0, ln_Rpaybrk = 0,
                    ln_Rttygm_b = 0, ln_Rttyprem_b = 0, ln_Rttycom_b = 0, ln_Rpaybrk_b = 0;
            decimal ln_Sttygm = 0, ln_Sttyprem = 0, ln_Sttycom = 0, ln_Spaybrk = 0,
                    ln_Sttygm_b = 0, ln_Sttyprem_b = 0, ln_Sttycom_b = 0, ln_Spaybrk_b = 0;
            decimal ln_Tttygm = 0, ln_Tttyprem = 0, ln_Tttycom = 0, ln_Tpaybrk = 0,
                    ln_Tttygm_b = 0, ln_Tttyprem_b = 0, ln_Tttycom_b = 0, ln_Tpaybrk_b = 0;

            for (int p = 0; p < dv.Count; p++)
            {
                ll_ChgTTYCode = lc_fttycode != dv[p]["fttycode"].ToString().Trim();
                ll_ChgTTYSec = ll_ChgTTYCode || lc_fttysec != dv[p]["fttysec"].ToString().Trim();

                if (ll_ChgTTYSec && ll_subR)
                {
                    iRow++;
                    k = 12;

                    xlWorkSheet.Cells[iRow, k++] = "Section Total:";
                    xlWorkSheet.Cells[iRow, k++] = ln_Rttyprem;
                    xlWorkSheet.Cells[iRow, k++] = ln_Rttycom;
                    xlWorkSheet.Cells[iRow, k++] = ln_Rpaybrk;

                    xlWorkSheet.get_Range(String.Format("E{0}", iRow)).HorizontalAlignment = 4;
                }

                if (ll_ChgTTYCode && ll_subS)
                {
                    iRow++;
                    k = 12;

                    xlWorkSheet.Cells[iRow, k++] = "Treaty Total:";
                    xlWorkSheet.Cells[iRow, k++] = ln_Sttyprem;
                    xlWorkSheet.Cells[iRow, k++] = ln_Sttycom;
                    xlWorkSheet.Cells[iRow, k++] = ln_Spaybrk;

                    xlWorkSheet.get_Range(String.Format("E{0}", iRow)).HorizontalAlignment = 4;

                    iRow++;
                    xlWorkSheet.Cells[iRow, 1] = String.Format("Your Share: {0} %", ln_fshare);
                    k = 13;

                    xlWorkSheet.Cells[iRow, k++] = ln_Sttyprem_b;
                    xlWorkSheet.Cells[iRow, k++] = ln_Sttycom_b;
                    xlWorkSheet.Cells[iRow, k++] = ln_Spaybrk_b;
                }

                if (ll_ChgTTYSec)
                {
                    ll_subR = false;
                    ln_Rttygm = 0; ln_Rttyprem = 0; ln_Rttycom = 0; ln_Rpaybrk = 0;
                    ln_Rttygm_b = 0; ln_Rttyprem_b = 0; ln_Rttycom_b = 0; ln_Rpaybrk_b = 0;
                }

                if (ll_ChgTTYCode)
                {
                    ll_subS = false;
                    ln_Sttygm = 0; ln_Sttyprem = 0; ln_Sttycom = 0; ln_Spaybrk = 0;
                    ln_Sttygm_b = 0; ln_Sttyprem_b = 0; ln_Sttycom_b = 0; ln_Spaybrk_b = 0;
                }

                if (ll_ChgTTYSec)
                {
                    lc_fttycode = dv[p]["fttycode"].ToString().Trim();
                    lc_fttysec = dv[p]["fttysec"].ToString().Trim();

                    iRow += 2;
                    //xlWorkSheet.Cells[iRow, 1] = String.Format("Section {0}    ({1})", lc_fttysec, lc_fttycode);
                    xlWorkSheet.Cells[iRow, 1] = String.Format("Treaty: {0}", lc_fttycode);
                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;
                }

                iRow++;

                if (dv[p]["fpolno"].ToString().Trim() != "")
                {
                    decimal fsi = Convert.ToDecimal(dv[p]["fsi"]), fttysi = Convert.ToDecimal(dv[p]["fttysi"]),
                        ftl = Convert.ToDecimal(dv[p]["ftl"]), fttytl = Convert.ToDecimal(dv[p]["fttytl"]),
                        fttygm = Convert.ToDecimal(dv[p]["fttygm"]), fttyprem = Convert.ToDecimal(dv[p]["fttyprem"]),
                        fttycom = Convert.ToDecimal(dv[p]["fttycom"]), fpaybrk = Convert.ToDecimal(dv[p]["fpaybrk"]),
                        fttygm_b = Convert.ToDecimal(dv[p]["fttygm_b"]), fttyprem_b = Convert.ToDecimal(dv[p]["fttyprem_b"]),
                        fttycom_b = Convert.ToDecimal(dv[p]["fttycom_b"]), fpaybrk_b = Convert.ToDecimal(dv[p]["fpaybrk_b"]);

                    ll_subR = false;
                    ll_subS = true;
                    ll_subT = true;

                    k = 1;

                    xlWorkSheet.Cells[iRow, k++] = dv[p]["fpolno"];
                    xlWorkSheet.Cells[iRow, k++] = dv[p]["fendtno"];
                    xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dv[p]["fefffr"]);
                    xlWorkSheet.Cells[iRow, k++] = Convert.ToDateTime(dv[p]["feffto"]);
                    xlWorkSheet.Cells[iRow, k++] = dv[p]["fmntfr"].Equals(System.DBNull.Value) ? "" : Convert.ToDateTime(dv[p]["fmntfr"]).ToString();
                    xlWorkSheet.Cells[iRow, k++] = dv[p]["fmntto"].Equals(System.DBNull.Value) ? "" : Convert.ToDateTime(dv[p]["fmntto"]).ToString();

                    xlWorkSheet.Cells[iRow, k++] = dv[p]["finsd"].ToString().Trim() + " " + dv[p]["finsd_t1"].ToString().Trim() + 
                        ((char)13).ToString() + ((char)10).ToString();
                    xlWorkSheet.Cells[iRow, k++] = dv[p]["fcontrt"].ToString().Trim()+((char)13).ToString() + ((char)10).ToString()+
                        dv[p]["fcontrt_t1"].ToString().Trim() + ((char)13).ToString() + ((char)10).ToString();

                    xlWorkSheet.get_Range(String.Format("G{0}", iRow), String.Format("G{0}", iRow + 1)).Merge();
                    xlWorkSheet.get_Range(String.Format("G{0}", iRow), String.Format("G{0}", iRow + 1)).HorizontalAlignment = 1;
                    xlWorkSheet.get_Range(String.Format("G{0}", iRow), String.Format("G{0}", iRow + 1)).VerticalAlignment = 1;
                    xlWorkSheet.get_Range(String.Format("G{0}", iRow), String.Format("G{0}", iRow)).RowHeight = 15;

                    xlWorkSheet.get_Range(String.Format("H{0}", iRow), String.Format("H{0}", iRow + 1)).Merge();
                    xlWorkSheet.get_Range(String.Format("H{0}", iRow), String.Format("H{0}", iRow + 1)).HorizontalAlignment = 1;
                    xlWorkSheet.get_Range(String.Format("H{0}", iRow), String.Format("H{0}", iRow + 1)).VerticalAlignment = 1;
                    xlWorkSheet.get_Range(String.Format("H{0}", iRow), String.Format("H{0}", iRow)).RowHeight = 15;
                                        
                    xlWorkSheet.Cells[iRow, k++] = fsi;
                    xlWorkSheet.Cells[iRow, k++] = ftl;
                    xlWorkSheet.Cells[iRow, k++] = fttysi;
                    xlWorkSheet.Cells[iRow, k++] = fttytl;
                    xlWorkSheet.Cells[iRow, k++] = fttyprem;
                    xlWorkSheet.Cells[iRow, k++] = fttycom;
                    xlWorkSheet.Cells[iRow, k++] = fpaybrk;

                    ln_fshare = Convert.ToDecimal(dv[p]["fshare"]);

                    ln_Rttygm = ln_Rttygm + fttygm;
                    ln_Rttyprem = ln_Rttyprem + fttyprem;
                    ln_Rttycom = ln_Rttycom + fttycom;
                    ln_Rpaybrk = ln_Rpaybrk + fpaybrk;
                    ln_Rttygm_b = ln_Rttygm_b + fttygm_b;
                    ln_Rttyprem_b = ln_Rttyprem_b + fttyprem_b;
                    ln_Rttycom_b = ln_Rttycom_b + fttycom_b;
                    ln_Rpaybrk_b = ln_Rpaybrk_b + fpaybrk_b;

                    ln_Sttygm = ln_Sttygm + fttygm;
                    ln_Sttyprem = ln_Sttyprem + fttyprem;
                    ln_Sttycom = ln_Sttycom + fttycom;
                    ln_Spaybrk = ln_Spaybrk + fpaybrk;
                    ln_Sttygm_b = ln_Sttygm_b + fttygm_b;
                    ln_Sttyprem_b = ln_Sttyprem_b + fttyprem_b;
                    ln_Sttycom_b = ln_Sttycom_b + fttycom_b;
                    ln_Spaybrk_b = ln_Spaybrk_b + fpaybrk_b;

                    ln_Tttygm = ln_Tttygm + fttygm;
                    ln_Tttyprem = ln_Tttyprem + fttyprem;
                    ln_Tttycom = ln_Tttycom + fttycom;
                    ln_Tpaybrk = ln_Tpaybrk + fpaybrk;
                    ln_Tttygm_b = ln_Tttygm_b + fttygm_b;
                    ln_Tttyprem_b = ln_Tttyprem_b + fttyprem_b;
                    ln_Tttycom_b = ln_Tttycom_b + fttycom_b;
                    ln_Tpaybrk_b = ln_Tpaybrk_b + fpaybrk_b;

                    iRow++;
                }
                else
                {
                    xlWorkSheet.Cells[iRow, 1] = "Nil";
                    xlWorkSheet.get_Range(String.Format("A{0}", iRow)).Font.Bold = true;
                }

            }

            if (ll_subR)
            {
                iRow++;
                k = 12;

                xlWorkSheet.Cells[iRow, k++] = "Section Total:";
                xlWorkSheet.Cells[iRow, k++] = ln_Rttyprem;
                xlWorkSheet.Cells[iRow, k++] = ln_Rttycom;
                xlWorkSheet.Cells[iRow, k++] = ln_Rpaybrk;

                xlWorkSheet.get_Range(String.Format("E{0}", iRow)).HorizontalAlignment = 4;
            }

            if (ll_subS)
            {
                iRow++;
                k = 12;

                xlWorkSheet.Cells[iRow, k++] = "Treaty Total:";
                xlWorkSheet.Cells[iRow, k++] = ln_Sttyprem;
                xlWorkSheet.Cells[iRow, k++] = ln_Sttycom;
                xlWorkSheet.Cells[iRow, k++] = ln_Spaybrk;

                xlWorkSheet.get_Range(String.Format("E{0}", iRow)).HorizontalAlignment = 4;

                iRow++;
                xlWorkSheet.Cells[iRow, 1] = String.Format("Your Share: {0} %", ln_fshare);
                k = 13;

                xlWorkSheet.Cells[iRow, k++] = ln_Sttyprem_b;
                xlWorkSheet.Cells[iRow, k++] = ln_Sttycom_b;
                xlWorkSheet.Cells[iRow, k++] = ln_Spaybrk_b;

            }

            if (ll_subT)
            {
                iRow += 2;

                xlWorkSheet.Cells[iRow, 1] = "Your Share (All Treaty Years):";
                k = 13;

                xlWorkSheet.Cells[iRow, k++] = ln_Tttyprem_b;
                xlWorkSheet.Cells[iRow, k++] = ln_Tttycom_b;
                xlWorkSheet.Cells[iRow, k++] = ln_Tpaybrk_b;
            }

            return;
        }

        public static void WbPrmBord(Excel.Workbook xlWorkBook, DataTable dtSource, DataTable dtDebtor,
                                string optid, string vttytype, string z_company,
                                string z_title, string z_currency, string z_period)
        {
            Int32 wsCount = dtDebtor.DefaultView.Count;
            DataView dvSource = dtSource.DefaultView;

            for (Int16 k = 0; k < dtDebtor.DefaultView.Count; k++)
            {
                Int16 i, sheetIndex = k;
                Excel.Worksheet xlWorkSheet;

                string fdbtrtype = dtDebtor.DefaultView[k]["fdbtrtype"].ToString().Trim(),
                       fdbtr = dtDebtor.DefaultView[k]["fdbtr"].ToString().Trim(),
                       fdbdesc = dtDebtor.DefaultView[k]["fdbdesc"].ToString().Trim();

                fdbdesc = fdbdesc + " (" + fdbtrtype + " - " + fdbtr + ")";
                dvSource.RowFilter = String.Format("fdbtrtype = '{0}' and trim(fdbtr) = '{1}'",fdbtrtype,fdbtr);
                dvSource.Sort = "fttycode,fttysec,fpolno,fendtno";

                Boolean noDetail = false;

                if (dvSource.Count == 0)
                    noDetail = true;

                sheetIndex++;
                xlWorkSheet = xlWorkBook.Worksheets.get_Item(sheetIndex);
                xlWorkSheet.Name = fdbtr;

                i = RptInExcel.WsHeader(xlWorkSheet, optid, z_company, z_title, z_currency, z_period);

                if (vttytype == "02")
                {
                    i = RptInExcel.WsColTitlePrmBordSUR(xlWorkSheet, noDetail, fdbdesc, optid, i);
                    RptInExcel.WsPrmBordDetailSUR(xlWorkSheet, dvSource, optid, i);
                }

                if (vttytype == "05")
                {
                    i = RptInExcel.WsColTitlePrmBordFOB(xlWorkSheet, noDetail, fdbdesc, optid, i);
                    RptInExcel.WsPrmBordDetailFOB(xlWorkSheet, dvSource, optid, i);
                }
            }
        }
    }
}
