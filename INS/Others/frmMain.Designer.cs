namespace INS
{
    partial class frmMain
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.ToolStripMenuItem = new System.Windows.Forms.MenuStrip();
            this.businessToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.directPolicyToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.contractorsAllRisksToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.employeesCompensationToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.claimsToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.accountsToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.managementRptToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.masterToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.attachmentToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.excClausesToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.siteToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.projectToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.lossAdjustToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.solicitorMasterToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.healthCareToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.natureOfLossToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.subContractorToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.accidentTypeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.jobNatureToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.selectPolicyToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.producerToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.cilentToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.rIBrokerToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.reinsurerToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.iNSClassToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.changePasswordToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.systemMaintainToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addUserToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.logoutToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.editToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripStatusLabel2 = new System.Windows.Forms.Label();
            this.ToolStripMenuItem.SuspendLayout();
            this.SuspendLayout();
            // 
            // ToolStripMenuItem
            // 
            this.ToolStripMenuItem.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.businessToolStripMenuItem,
            this.claimsToolStripMenuItem,
            this.accountsToolStripMenuItem,
            this.managementRptToolStripMenuItem,
            this.masterToolStripMenuItem,
            this.changePasswordToolStripMenuItem,
            this.systemMaintainToolStripMenuItem,
            this.logoutToolStripMenuItem,
            this.editToolStripMenuItem});
            this.ToolStripMenuItem.Location = new System.Drawing.Point(0, 0);
            this.ToolStripMenuItem.Name = "ToolStripMenuItem";
            this.ToolStripMenuItem.Size = new System.Drawing.Size(784, 24);
            this.ToolStripMenuItem.TabIndex = 0;
            this.ToolStripMenuItem.Text = "menuStrip1";
            // 
            // businessToolStripMenuItem
            // 
            this.businessToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.directPolicyToolStripMenuItem});
            this.businessToolStripMenuItem.Name = "businessToolStripMenuItem";
            this.businessToolStripMenuItem.Size = new System.Drawing.Size(64, 20);
            this.businessToolStripMenuItem.Text = "Business";
            // 
            // directPolicyToolStripMenuItem
            // 
            this.directPolicyToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.contractorsAllRisksToolStripMenuItem,
            this.employeesCompensationToolStripMenuItem});
            this.directPolicyToolStripMenuItem.Name = "directPolicyToolStripMenuItem";
            this.directPolicyToolStripMenuItem.Size = new System.Drawing.Size(140, 22);
            this.directPolicyToolStripMenuItem.Text = "Direct Policy";
            // 
            // contractorsAllRisksToolStripMenuItem
            // 
            this.contractorsAllRisksToolStripMenuItem.Name = "contractorsAllRisksToolStripMenuItem";
            this.contractorsAllRisksToolStripMenuItem.Size = new System.Drawing.Size(215, 22);
            this.contractorsAllRisksToolStripMenuItem.Text = "Contractor\'s All Risks";
            // 
            // employeesCompensationToolStripMenuItem
            // 
            this.employeesCompensationToolStripMenuItem.Name = "employeesCompensationToolStripMenuItem";
            this.employeesCompensationToolStripMenuItem.Size = new System.Drawing.Size(215, 22);
            this.employeesCompensationToolStripMenuItem.Text = "Employee\'s Compensation";
            // 
            // claimsToolStripMenuItem
            // 
            this.claimsToolStripMenuItem.Name = "claimsToolStripMenuItem";
            this.claimsToolStripMenuItem.Size = new System.Drawing.Size(55, 20);
            this.claimsToolStripMenuItem.Text = "Claims";
            // 
            // accountsToolStripMenuItem
            // 
            this.accountsToolStripMenuItem.Name = "accountsToolStripMenuItem";
            this.accountsToolStripMenuItem.Size = new System.Drawing.Size(69, 20);
            this.accountsToolStripMenuItem.Text = "Accounts";
            // 
            // managementRptToolStripMenuItem
            // 
            this.managementRptToolStripMenuItem.Name = "managementRptToolStripMenuItem";
            this.managementRptToolStripMenuItem.Size = new System.Drawing.Size(111, 20);
            this.managementRptToolStripMenuItem.Text = "Management Rpt";
            // 
            // masterToolStripMenuItem
            // 
            this.masterToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.attachmentToolStripMenuItem,
            this.excClausesToolStripMenuItem,
            this.siteToolStripMenuItem,
            this.projectToolStripMenuItem,
            this.lossAdjustToolStripMenuItem,
            this.solicitorMasterToolStripMenuItem,
            this.healthCareToolStripMenuItem,
            this.natureOfLossToolStripMenuItem,
            this.subContractorToolStripMenuItem,
            this.accidentTypeToolStripMenuItem,
            this.jobNatureToolStripMenuItem,
            this.selectPolicyToolStripMenuItem,
            this.producerToolStripMenuItem,
            this.cilentToolStripMenuItem,
            this.rIBrokerToolStripMenuItem,
            this.reinsurerToolStripMenuItem,
            this.iNSClassToolStripMenuItem});
            this.masterToolStripMenuItem.Name = "masterToolStripMenuItem";
            this.masterToolStripMenuItem.Size = new System.Drawing.Size(55, 20);
            this.masterToolStripMenuItem.Text = "Master";
            // 
            // attachmentToolStripMenuItem
            // 
            this.attachmentToolStripMenuItem.Name = "attachmentToolStripMenuItem";
            this.attachmentToolStripMenuItem.Size = new System.Drawing.Size(156, 22);
            this.attachmentToolStripMenuItem.Text = "Attachment";
            this.attachmentToolStripMenuItem.Click += new System.EventHandler(this.attachmentToolStripMenuItem_Click);
            // 
            // excClausesToolStripMenuItem
            // 
            this.excClausesToolStripMenuItem.Name = "excClausesToolStripMenuItem";
            this.excClausesToolStripMenuItem.Size = new System.Drawing.Size(156, 22);
            this.excClausesToolStripMenuItem.Text = "Exc Clauses";
            this.excClausesToolStripMenuItem.Click += new System.EventHandler(this.excClausesToolStripMenuItem_Click);
            // 
            // siteToolStripMenuItem
            // 
            this.siteToolStripMenuItem.Name = "siteToolStripMenuItem";
            this.siteToolStripMenuItem.Size = new System.Drawing.Size(156, 22);
            this.siteToolStripMenuItem.Text = "Site";
            this.siteToolStripMenuItem.Click += new System.EventHandler(this.siteToolStripMenuItem_Click);
            // 
            // projectToolStripMenuItem
            // 
            this.projectToolStripMenuItem.Name = "projectToolStripMenuItem";
            this.projectToolStripMenuItem.Size = new System.Drawing.Size(156, 22);
            this.projectToolStripMenuItem.Text = "Project";
            this.projectToolStripMenuItem.Click += new System.EventHandler(this.projectToolStripMenuItem_Click);
            // 
            // lossAdjustToolStripMenuItem
            // 
            this.lossAdjustToolStripMenuItem.Name = "lossAdjustToolStripMenuItem";
            this.lossAdjustToolStripMenuItem.Size = new System.Drawing.Size(156, 22);
            this.lossAdjustToolStripMenuItem.Text = "Loss Adjust";
            this.lossAdjustToolStripMenuItem.Click += new System.EventHandler(this.lossAdjustToolStripMenuItem_Click);
            // 
            // solicitorMasterToolStripMenuItem
            // 
            this.solicitorMasterToolStripMenuItem.Name = "solicitorMasterToolStripMenuItem";
            this.solicitorMasterToolStripMenuItem.Size = new System.Drawing.Size(156, 22);
            this.solicitorMasterToolStripMenuItem.Text = "Solicitor Master";
            this.solicitorMasterToolStripMenuItem.Click += new System.EventHandler(this.solicitorMasterToolStripMenuItem_Click);
            // 
            // healthCareToolStripMenuItem
            // 
            this.healthCareToolStripMenuItem.Name = "healthCareToolStripMenuItem";
            this.healthCareToolStripMenuItem.Size = new System.Drawing.Size(156, 22);
            this.healthCareToolStripMenuItem.Text = "Health Care";
            this.healthCareToolStripMenuItem.Click += new System.EventHandler(this.healthCareToolStripMenuItem_Click);
            // 
            // natureOfLossToolStripMenuItem
            // 
            this.natureOfLossToolStripMenuItem.Name = "natureOfLossToolStripMenuItem";
            this.natureOfLossToolStripMenuItem.Size = new System.Drawing.Size(156, 22);
            this.natureOfLossToolStripMenuItem.Text = "Nature of Loss";
            this.natureOfLossToolStripMenuItem.Click += new System.EventHandler(this.natureOfLossToolStripMenuItem_Click);
            // 
            // subContractorToolStripMenuItem
            // 
            this.subContractorToolStripMenuItem.Name = "subContractorToolStripMenuItem";
            this.subContractorToolStripMenuItem.Size = new System.Drawing.Size(156, 22);
            this.subContractorToolStripMenuItem.Text = "Sub Contractor";
            this.subContractorToolStripMenuItem.Click += new System.EventHandler(this.subContractorToolStripMenuItem_Click);
            // 
            // accidentTypeToolStripMenuItem
            // 
            this.accidentTypeToolStripMenuItem.Name = "accidentTypeToolStripMenuItem";
            this.accidentTypeToolStripMenuItem.Size = new System.Drawing.Size(156, 22);
            this.accidentTypeToolStripMenuItem.Text = "Accident Type";
            this.accidentTypeToolStripMenuItem.Click += new System.EventHandler(this.accidentTypeToolStripMenuItem_Click);
            // 
            // jobNatureToolStripMenuItem
            // 
            this.jobNatureToolStripMenuItem.Name = "jobNatureToolStripMenuItem";
            this.jobNatureToolStripMenuItem.Size = new System.Drawing.Size(156, 22);
            this.jobNatureToolStripMenuItem.Text = "Job Nature";
            this.jobNatureToolStripMenuItem.Click += new System.EventHandler(this.jobNatureToolStripMenuItem_Click);
            // 
            // selectPolicyToolStripMenuItem
            // 
            this.selectPolicyToolStripMenuItem.Name = "selectPolicyToolStripMenuItem";
            this.selectPolicyToolStripMenuItem.Size = new System.Drawing.Size(156, 22);
            this.selectPolicyToolStripMenuItem.Text = "Select Policy";
            this.selectPolicyToolStripMenuItem.Click += new System.EventHandler(this.selectPolicyToolStripMenuItem_Click);
            // 
            // producerToolStripMenuItem
            // 
            this.producerToolStripMenuItem.Name = "producerToolStripMenuItem";
            this.producerToolStripMenuItem.Size = new System.Drawing.Size(156, 22);
            this.producerToolStripMenuItem.Text = "Producer";
            this.producerToolStripMenuItem.Click += new System.EventHandler(this.producerToolStripMenuItem_Click);
            // 
            // cilentToolStripMenuItem
            // 
            this.cilentToolStripMenuItem.Name = "cilentToolStripMenuItem";
            this.cilentToolStripMenuItem.Size = new System.Drawing.Size(156, 22);
            this.cilentToolStripMenuItem.Text = "Client";
            this.cilentToolStripMenuItem.Click += new System.EventHandler(this.cilentToolStripMenuItem_Click);
            // 
            // rIBrokerToolStripMenuItem
            // 
            this.rIBrokerToolStripMenuItem.Name = "rIBrokerToolStripMenuItem";
            this.rIBrokerToolStripMenuItem.Size = new System.Drawing.Size(156, 22);
            this.rIBrokerToolStripMenuItem.Text = "RI Broker";
            this.rIBrokerToolStripMenuItem.Click += new System.EventHandler(this.rIBrokerToolStripMenuItem_Click);
            // 
            // reinsurerToolStripMenuItem
            // 
            this.reinsurerToolStripMenuItem.Name = "reinsurerToolStripMenuItem";
            this.reinsurerToolStripMenuItem.Size = new System.Drawing.Size(156, 22);
            this.reinsurerToolStripMenuItem.Text = "Reinsurer";
            this.reinsurerToolStripMenuItem.Click += new System.EventHandler(this.reinsurerToolStripMenuItem_Click);
            // 
            // iNSClassToolStripMenuItem
            // 
            this.iNSClassToolStripMenuItem.Name = "iNSClassToolStripMenuItem";
            this.iNSClassToolStripMenuItem.Size = new System.Drawing.Size(156, 22);
            this.iNSClassToolStripMenuItem.Text = "INS Class";
            this.iNSClassToolStripMenuItem.Click += new System.EventHandler(this.iNSClassToolStripMenuItem_Click);
            // 
            // changePasswordToolStripMenuItem
            // 
            this.changePasswordToolStripMenuItem.Name = "changePasswordToolStripMenuItem";
            this.changePasswordToolStripMenuItem.Size = new System.Drawing.Size(113, 20);
            this.changePasswordToolStripMenuItem.Text = "Change Password";
            this.changePasswordToolStripMenuItem.Click += new System.EventHandler(this.changePasswordToolStripMenuItem_Click_1);
            // 
            // systemMaintainToolStripMenuItem
            // 
            this.systemMaintainToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.addUserToolStripMenuItem});
            this.systemMaintainToolStripMenuItem.Name = "systemMaintainToolStripMenuItem";
            this.systemMaintainToolStripMenuItem.Size = new System.Drawing.Size(107, 20);
            this.systemMaintainToolStripMenuItem.Text = "System Maintain";
            // 
            // addUserToolStripMenuItem
            // 
            this.addUserToolStripMenuItem.Name = "addUserToolStripMenuItem";
            this.addUserToolStripMenuItem.Size = new System.Drawing.Size(119, 22);
            this.addUserToolStripMenuItem.Text = "AddUser";
            this.addUserToolStripMenuItem.Click += new System.EventHandler(this.addUserToolStripMenuItem_Click);
            // 
            // logoutToolStripMenuItem
            // 
            this.logoutToolStripMenuItem.Name = "logoutToolStripMenuItem";
            this.logoutToolStripMenuItem.Size = new System.Drawing.Size(59, 20);
            this.logoutToolStripMenuItem.Text = "LogOut";
            this.logoutToolStripMenuItem.Click += new System.EventHandler(this.logoutToolStripMenuItem_Click);
            // 
            // editToolStripMenuItem
            // 
            this.editToolStripMenuItem.Name = "editToolStripMenuItem";
            this.editToolStripMenuItem.Size = new System.Drawing.Size(12, 20);
            // 
            // toolStripStatusLabel2
            // 
            this.toolStripStatusLabel2.AutoSize = true;
            this.toolStripStatusLabel2.Location = new System.Drawing.Point(672, 6);
            this.toolStripStatusLabel2.Name = "toolStripStatusLabel2";
            this.toolStripStatusLabel2.Size = new System.Drawing.Size(35, 13);
            this.toolStripStatusLabel2.TabIndex = 1;
            this.toolStripStatusLabel2.Text = "label1";
            // 
            // frmMain
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(784, 562);
            this.ControlBox = false;
            this.Controls.Add(this.toolStripStatusLabel2);
            this.Controls.Add(this.ToolStripMenuItem);
            this.MainMenuStrip = this.ToolStripMenuItem;
            this.Name = "frmMain";
            this.Text = "Form1";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.Load += new System.EventHandler(this.frmMain_Load);
            this.ToolStripMenuItem.ResumeLayout(false);
            this.ToolStripMenuItem.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.MenuStrip ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem businessToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem claimsToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem accountsToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem managementRptToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem masterToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem changePasswordToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem systemMaintainToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem logoutToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem directPolicyToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem contractorsAllRisksToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem employeesCompensationToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem editToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem attachmentToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem addUserToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem excClausesToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem siteToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem projectToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem lossAdjustToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem solicitorMasterToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem healthCareToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem natureOfLossToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem subContractorToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem accidentTypeToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem jobNatureToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem selectPolicyToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem producerToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem cilentToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem rIBrokerToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem reinsurerToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem iNSClassToolStripMenuItem;
        private System.Windows.Forms.Label toolStripStatusLabel2;
    }
}

