using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using System.Globalization;
using System.Windows.Documents;
using System.Text.RegularExpressions;
using System.Configuration;
using System.Collections;

namespace INS
{
    public partial class frmXOL1 : Form
    {
        List<string> list = new List<string>();
        List<string> list1 = new List<string>();
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public frmXOL1()
        {
            InitializeComponent();
            InitCombobox();
            FillData("", "");
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            foreach (Control control in contact.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            foreach (Control control in panel2.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
        }
        void control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
            {
                SendKeys.Send("{TAB}");
            }
        }
        private DataTable dataSource = new DataTable();
        private DataTable dataSource1 = new DataTable();
        private DataTable layr = new DataTable();
        DataSet set = new DataSet("addrecord");

        public int i = 0, counter;
        DES des = new DES();
        DBConnect operate = new DBConnect();
        ExportDBF DBF = new ExportDBF();
        XLS txt = new XLS();
        public string fctlid_a = "";
        public String user = InsEnvironment.LoginUser.GetUserCode();
        public int row = 0;
        public int row1 = 0;
        public Boolean flag = false;
        public Boolean ADD = false;
        public Boolean Update = false;
        public Boolean flag1 = false;
        private bool datagridviewSelectionChanged = true;
        private bool datagridview2SelectionChanged = true;
        private bool datagridview3SelectionChanged = true;
        public string db = InsEnvironment.DataBase.GetDbm();

        void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            try
            {
                if (order != "" && query != "")
                {
                    if (order == "XTY#")
                    {
                        a = new SqlDataAdapter("select fctlid,fid as XTY#,fdesc as Desc#,fefffr as [Eff From] ,feffto as [Eff To],fcur  " +
    " from mxl where fid like '%" + query.ToUpper() + "%' order by " + order, c);
                    }
                    else
                    {
                        a = new SqlDataAdapter("select fctlid,fid as XTY#,fdesc as Desc#,fefffr as [Eff From] ,feffto as [Eff To],fcur  " +
" from mxl where fdesc like '%" + query.ToUpper() + "%' order by " + order, c);
                    }
                }
                else if (order != "" && query == "")
                {
                    a = new SqlDataAdapter("select fctlid,fid as XTY#,fdesc as Desc#,fefffr as [Eff From] ,feffto as [Eff To],fcur  " +
    " from mxl order by " + order, c);
                }
                else
                {
                    a = new SqlDataAdapter("select fctlid,fid as XTY#,fdesc as Desc#,fefffr as [Eff From] ,feffto as [Eff To],fcur  " +
    " from mxl order by fid ", c);

                }
                DataTable t = new DataTable();
                a.Fill(t);
                dataGridView1.DataSource = t;
                this.dataGridView1.Columns[0].Visible = false;
                this.dataGridView1.Columns[4].Visible = false;
                this.dataGridView1.Columns[5].Visible = false;
            }
            catch { }

            c.Close();
        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", "");
        }

        private void button1_Click(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", textBox1.Text.ToString().Trim());
        }

        public enum Mode
        {
            XTY = 1,
            Desc = 2
        }

        private void InitCombobox()
        {
            Array arr = System.Enum.GetValues(typeof(Mode));    // 获取枚举的所有值
            DataTable dt = new DataTable();
            dt.Columns.Add("String", Type.GetType("System.String"));
            dt.Columns.Add("Value", typeof(int));
            foreach (var a in arr)
            {
                string strText = EnumTextByDescription.GetEnumDesc((Mode)a);
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = (int)a;

                dt.Rows.Add(aRow);
            }

            comboBox1.DataSource = dt;
            comboBox1.DisplayMember = "String";
            comboBox1.ValueMember = "Value";
        }

        void loadMXLLAYR()
        {
            string str = "select fid,fctlid from MXLLAYR where fctlid_1 ='" + fctlid_a + "'";
            DataTable dt = new DataTable();
            dt = operate.GetTable(str);
            comboBox5.DisplayMember = "fid";
            comboBox5.ValueMember = "fctlid";
            comboBox5.DataSource = dt;
            comboBox6.DisplayMember = "fid";
            comboBox6.ValueMember = "fctlid";
            comboBox6.DataSource = dt;
        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            if (ADD == true || Update == true)
            {
                if (e.TabPage == Classes)
                    e.Cancel = true;
            }
        }

        private void dataGridView1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            if (!this.datagridviewSelectionChanged) return;
            try
            {
                if (flag == false && dataGridView1.CurrentCell != null)
                {
                    counter = dataGridView1.CurrentCell.RowIndex;
                }

                if (dataGridView1.Rows[counter].Cells["XTY#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["XTY#"].Value.ToString().Length != 0)
                    {
                        textBox8.Text = dataGridView1.Rows[counter].Cells["XTY#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        textBox9.Text = dataGridView1.Rows[counter].Cells["Desc#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Eff From"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Eff From"].Value.ToString().Length != 0)
                    {
                        if (dataGridView1.Rows[counter].Cells["Eff From"].Value.ToString() != "")
                        {
                            try
                            {
                                string a = dataGridView1.Rows[counter].Cells["Eff From"].Value.ToString();
                                DateTime b = Convert.ToDateTime(a);
                                textBox10.Text = b.ToString("yyyy.MM.dd");
                            }
                            catch { }
                        }
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Eff To"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Eff To"].Value.ToString().Length != 0)
                    {
                        if (dataGridView1.Rows[counter].Cells["Eff To"].Value.ToString() != "")
                        {
                            try
                            {
                                string a = dataGridView1.Rows[counter].Cells["Eff To"].Value.ToString();
                                DateTime b = Convert.ToDateTime(a);
                                textBox11.Text = b.ToString("yyyy.MM.dd");
                            }
                            catch { }
                        }
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fcur"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fcur"].Value.ToString().Length != 0)
                    {
                        comboBox3.SelectedItem = dataGridView1.Rows[counter].Cells["fcur"].Value.ToString();
                    }
                }

                if (ADD == false)
                {
                    fctlid_a = dataGridView1.Rows[counter].Cells["fctlid"].Value.ToString();
                    loadMXLLAYR();
                }
            }
            catch { }
        }

        private void dataGridView2_SelectionChanged(object sender, EventArgs e)
        {
            if (!this.datagridview2SelectionChanged) return;
            try
            {
                if (flag == false && dataGridView2.CurrentCell != null)
                {
                    row = dataGridView2.CurrentCell.RowIndex;
                }

                if (dataGridView2.Rows[row].Cells["Seq#"].Value != null)
                {
                    textBox12.Text = dataGridView2.Rows[row].Cells["Seq#"].Value.ToString().Trim();
                }
                if (dataGridView2.Rows[row].Cells["fbrkr1"].Value != null)
                {
                    textBox13.Text = dataGridView2.Rows[row].Cells["fbrkr1"].Value.ToString().Trim();
                }
                if (dataGridView2.Rows[row].Cells["Bdesc"].Value != null)
                {
                    textBox14.Text = dataGridView2.Rows[row].Cells["Bdesc"].Value.ToString().Trim();
                }
                if (dataGridView2.Rows[row].Cells["Reinsurer"].Value != null)
                {
                    textBox15.Text = dataGridView2.Rows[row].Cells["Reinsurer"].Value.ToString().Trim();
                }
                if (dataGridView2.Rows[row].Cells["Rdesc"].Value != null)
                {
                    textBox16.Text = dataGridView2.Rows[row].Cells["Rdesc"].Value.ToString().Trim();
                }
                if (dataGridView2.Rows[row].Cells["Share (%)"].Value != null)
                {
                    textBox17.Text = dataGridView2.Rows[row].Cells["Share (%)"].Value.ToString().Trim();
                }
                if (dataGridView2.Rows[row].Cells["fctlid"].Value != null)
                {
                    fctlidlabelinfo.Text = dataGridView2.Rows[row].Cells["fctlid"].Value.ToString();
                }
                if (dataGridView2.Rows[row].Cells["fctlid"].Value != null)
                {
                    fminpm.Text = dataGridView2.Rows[row].Cells["fminpm"].Value.ToString();
                    fdeppm.Text = dataGridView2.Rows[row].Cells["fdeppm"].Value.ToString();
                    fprate.Text = dataGridView2.Rows[row].Cells["fprate"].Value.ToString();
                    fadjrate.Text = dataGridView2.Rows[row].Cells["fadjrate"].Value.ToString();
                    fdrate.Text = dataGridView2.Rows[row].Cells["fdrate"].Value.ToString();
                    flimit.Text = dataGridView2.Rows[row].Cells["flimit"].Value.ToString();
                }
                rowlabelinfo.Text = row.ToString();
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void dataGridView3_SelectionChanged(object sender, EventArgs e)
        {
            if (!this.datagridview3SelectionChanged) return;
            try
            {
                if (flag1 == false && dataGridView3.CurrentCell != null)
                {
                    row1 = dataGridView3.CurrentCell.RowIndex;
                }

                if (dataGridView3.Rows[row1].Cells["Item#"].Value != null)
                {
                    textBox2.Text = dataGridView3.Rows[row1].Cells["Item#"].Value.ToString().Trim();
                }
                if (dataGridView3.Rows[row1].Cells["Loss Limit"].Value != null)
                {
                    textBox3.Text = dataGridView3.Rows[row1].Cells["Loss Limit"].Value.ToString().Trim();
                }
                if (dataGridView3.Rows[row1].Cells["Excess Point"].Value != null)
                {
                    textBox4.Text = dataGridView3.Rows[row1].Cells["Excess Point"].Value.ToString().Trim();
                }
                if (dataGridView3.Rows[row1].Cells["fctlid"].Value != null)
                {
                    fctlidlabelcontact.Text = dataGridView3.Rows[row1].Cells["fctlid"].Value.ToString();
                }
                if (dataGridView3.Rows[row1].Cells["list"].Value != null)
                {

                    string[] parts = dataGridView3.Rows[row1].Cells["list"].Value.ToString().Split(',');
                    List<string> listl1 = new List<string>(parts);
                    list.Clear();
                    foreach (string item in listl1)
                    {
                        list.Add(item);
                    }
                    listBox1.DataSource = null;
                    listBox1.DataSource = list;
                }
                if (dataGridView3.Rows[row1].Cells["list1"].Value != null)
                {

                    string[] parts = dataGridView3.Rows[row1].Cells["list1"].Value.ToString().Split(',');
                    List<string> listl2 = new List<string>(parts);
                    list1.Clear();
                    foreach (string item in listl2)
                    {
                        list1.Add(item);
                    }
                    listBox2.DataSource = null;
                    listBox2.DataSource = list1;
                }
                rowlabelcontact.Text = row1.ToString();
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }


        void ButtonControlback()
        {
            ADD = false;
            Update = false;
            textBox8.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox8.ReadOnly = true;
            textBox9.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox9.ReadOnly = true;
            textBox10.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox10.ReadOnly = true;
            textBox11.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox11.ReadOnly = true;
            comboBox3.BackColor = System.Drawing.SystemColors.InactiveCaption;
            comboBox3.Enabled = false;

            textBox12.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox12.ReadOnly = true;
            textBox13.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox13.ReadOnly = true;
            textBox15.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox15.ReadOnly = true;
            textBox17.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox17.ReadOnly = true;
            textBox2.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox2.ReadOnly = true;
            textBox3.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox3.ReadOnly = true;
            textBox4.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox4.ReadOnly = true;

            dcancel.Visible = false;
            dsave.Visible = false;
            dexit.Visible = true;
            dadd.Visible = true;
            dupdate.Visible = true;
            ddel.Visible = true;
            csave.Visible = false;
            cCancel.Visible = false;
            cadd.Visible = true;
            cdel.Visible = true;
            cupdate.Visible = true;
            cexit.Visible = true;
            add1.Visible = false;
            add2.Visible = false;
            add3.Visible = false;
            del1.Visible = false;
            del2.Visible = false;
            del3.Visible = false;
            button4.Enabled = false;
            button5.Enabled = false;
            button2.Enabled = false;
            button3.Enabled = false;
            button6.Enabled = false;
            button7.Enabled = false;
        }

        void ButtonControl()
        {
            textBox8.BackColor = System.Drawing.SystemColors.Window;
            textBox8.ReadOnly = false;
            textBox9.BackColor = System.Drawing.SystemColors.Window;
            textBox9.ReadOnly = false;
            textBox10.BackColor = System.Drawing.SystemColors.Window;
            textBox10.ReadOnly = false;
            textBox11.BackColor = System.Drawing.SystemColors.Window;
            textBox11.ReadOnly = false;
            comboBox3.BackColor = System.Drawing.SystemColors.Window;
            comboBox3.Enabled = true;
            dcancel.Visible = true;
            dsave.Visible = true;
            dexit.Visible = false;
            dadd.Visible = false;
            dupdate.Visible = false;
            ddel.Visible = false;
            csave.Visible = true;
            cCancel.Visible = true;
            cadd.Visible = false;
            cdel.Visible = false;
            cupdate.Visible = false;
            cexit.Visible = false;
            add1.Visible = true;
            add2.Visible = true;
            add3.Visible = true;
            del1.Visible = true;
            del2.Visible = true;
            del3.Visible = true;
            button4.Enabled = true;
            button5.Enabled = true;
            button2.Enabled = true;
            button3.Enabled = true;
            button6.Enabled = true;
            button7.Enabled = true;
        }

        void ButtonControladd()
        {
            ADD = true;
            DataTable listdataSource = dataGridView1.DataSource as DataTable;
            DataRow newCustomersRow = listdataSource.NewRow();
            listdataSource.Rows.InsertAt(newCustomersRow, counter);
            dataGridView1.DataSource = listdataSource;
            dataGridView1.CurrentCell = dataGridView1.Rows[counter - 1].Cells["XTY#"];

            foreach (Control ctrl in panel8.Controls)                //循环窗体的控件
            {
                if (ctrl is NumericTextBox)
                {
                    ((NumericTextBox)ctrl).Text = "";
                }
                //添加事件
            }
            foreach (Control ctrl in panel2.Controls)                //循环窗体的控件
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                }
                //添加事件
            }
            foreach (Control ctrl in Clauses.Controls)                //循环窗体的控件
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                }
                if (ctrl is MaskedTextBox)
                {
                    ((MaskedTextBox)ctrl).Text = "";
                }
                //添加事件
            }
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);
        }

        void ButtonCountrolupdate()
        {
            Update = true;
            if (dataGridView2.RowCount > 0)
            {
                textBox12.BackColor = System.Drawing.SystemColors.Window;
                textBox12.ReadOnly = false;
                textBox13.BackColor = System.Drawing.SystemColors.Window;
                textBox13.ReadOnly = false;
                textBox15.BackColor = System.Drawing.SystemColors.Window;
                textBox15.ReadOnly = false;
                textBox17.BackColor = System.Drawing.SystemColors.Window;
                textBox17.ReadOnly = false;
            }

            if (dataGridView3.RowCount > 0)
            {
                textBox2.BackColor = System.Drawing.SystemColors.Window;
                textBox2.ReadOnly = false;
                textBox3.BackColor = System.Drawing.SystemColors.Window;
                textBox3.ReadOnly = false;
                textBox4.BackColor = System.Drawing.SystemColors.Window;
                textBox4.ReadOnly = false;
            }
        }

        private void button9_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void dexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void cexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void dcancel_Click(object sender, EventArgs e)
        {
            ButtonControlback();
            FillData("", "");
        }

        private void cCancel_Click(object sender, EventArgs e)
        {
            ButtonControlback();
            FillData("", "");
        }

        private void lupdate_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonCountrolupdate();
        }

        private void dupdate_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonCountrolupdate();
        }

        private void cupdate_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonCountrolupdate();
        }

        private void ladd_Click(object sender, EventArgs e)
        {
            add();
        }

        private void dadd_Click(object sender, EventArgs e)
        {
            add();
        }

        private void cadd_Click(object sender, EventArgs e)
        {
            add();
        }

        void add()
        {
            tabControl1.SelectedTab = Clauses;
            list.Clear();
            list1.Clear();
            listBox1.DataSource = null;
            listBox2.DataSource = null;
            ButtonControl();
            ButtonControladd();

            comboBox5.DataSource = null;
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select a.fid, '' as fminpm,'' as fdeppm,'' as fprate,'' as fadjrate,'' as fdrate,'' as flimit, " +
                "d.fctlid_1,d.fctlid_2,d.fctlid,flogseq as Seq#,  " +
                "freinsr as Reinsurer,'' as [Share (%)], fbrkr1, " +
                "b.fdesc as Bdesc,c.fdesc as Rdesc from mxllayr a " +
                "left join mxlinsr d on a.fctlid_1 = d.fctlid_1 and a.fctlid = d.fctlid_2 " +
                "left join (select fid,fdesc  from mprdr where ftype='B') b on d.fbrkr1=b.fid " +
                "left join (select fid,fdesc from mprdr where ftype='R') c on d.freinsr=c.fid " +
                "where a.fctlid_1 = '1' and a.fid ='01' ORDER BY a.fid", c))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                dataSource = t2;
                dataGridView2.DataSource = t2;
                foreach (DataGridViewColumn Column in dataGridView2.Columns)
                {
                    Column.Visible = false;
                }
                this.dataGridView2.Columns["Share (%)"].Visible = true;
                this.dataGridView2.Columns["Reinsurer"].Visible = true;
                this.dataGridView2.Columns["Seq#"].Visible = true;
            }
            c.Close();
            SqlConnection c1 = DBConnect.dbConn;
            if (c1.State == ConnectionState.Closed)
            { c1.Open(); }

            using (SqlDataAdapter a1 = new SqlDataAdapter("select top 1 '' as Item#,'' as [Loss Limit], " +
                "'' as [Excess Point],'' as fctlid_1, '' as fctlid_2, '' as fctlid,'' as list, '' as list1 from mxllayrb where fctlid_1 = '1' ", c1))
            {
                DataTable t3 = new DataTable();
                a1.Fill(t3);
                dataGridView3.DataSource = t3;
                foreach (DataGridViewColumn Column in dataGridView3.Columns)
                {
                    Column.Visible = false;
                }
                this.dataGridView3.Columns["Loss Limit"].Visible = true;
                this.dataGridView3.Columns["Excess Point"].Visible = true;
                this.dataGridView3.Columns["Item#"].Visible = true;
            }
            c1.Close();

            string sql = "select '' as fid, '' as fminpm,'' as fdeppm,'' as fprate,'' as fadjrate,'' as fdrate,'' as flimit, '' as fctlid_1,'' as  fctlid_2, '' as fctlid from mxllayrb where fctlid ='1'";
            layr = operate.GetTable(sql);
            
        }

        private void ldel_Click(object sender, EventArgs e)
        {
            del();
        }

        private void ddel_Click(object sender, EventArgs e)
        {
            del();
        }

        private void cdel_Click(object sender, EventArgs e)
        {
            del();
        }

        void del()
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the ALL items?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string deldatestr1 =
                    "delete from mxl where fctlid='" + fctlid_a + "'";
                operate.OperateData(deldatestr1);
                string deldatestr =
                    "delete from mxlinsr where fctlid_1='" + fctlid_a + "'";
                operate.OperateData(deldatestr);
                string deldatestr3 =
                    "delete from MXLLAYR where fctlid_1='" + fctlid_a + "'";
                operate.OperateData(deldatestr3);
                string deldatestr2 =
                    "delete from mxllayrb where fctlid_1='" + fctlid_a + "'";
                operate.OperateData(deldatestr2);
                MessageBox.Show("Have Been Deleted", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                FillData("", "");
                tabControl1.SelectedTab = Classes;
            }

        }

        private void textBox8_TextChanged(object sender, EventArgs e)
        {
            dataGridView1.Rows[counter].Cells["XTY#"].Value = textBox8.Text;
            string result2 = CheckDuplicate(textBox8.Text);
            if (result2 != "")
            {
                MessageBox.Show(result2);
                textBox8.Focus();
                textBox8.SelectAll();
            }
        }

        public string CheckDuplicate(string fid)
        {
            if (fid != "")
            {
                DataTable listdataSource = dataGridView1.DataSource as DataTable;
                if (listdataSource.Rows.Count > 0)
                {
                    DataTable objectTable = listdataSource.DefaultView.ToTable();
                    DataRow[] rows = objectTable.Select("[XTY#]='" + fid.Trim() + "'");
                    if (rows.Length > 1)
                    {
                        return "Duplicate Value";
                    }
                    else { return ""; }
                }
                else { return ""; }
            }
            else { return ""; }
        }

        private void textBox17_TextChanged(object sender, EventArgs e)
        {
            if (dataGridView2.CurrentCell != null && (ADD == true || Update == true))
            {
                dataGridView2["Share (%)", dataGridView2.CurrentCell.RowIndex].Value = textBox17.Text;
                total();
            }
        }

        void total()
        {
            Decimal sum = 0;
            for (int i = 0; i < dataGridView2.Rows.Count; i++)
            {
                if (dataGridView2["Share (%)", i].Value.ToString() != "")
                {
                    sum = sum + Convert.ToDecimal(dataGridView2["Share (%)", i].Value);
                }
            }
            textBox18.Text = string.Format("{0:N0}", Convert.ToDecimal(sum.ToString().Replace(",", "")));
        }

        private void textBox3_TextChanged(object sender, EventArgs e)
        {
            if (dataGridView3.CurrentCell != null && (ADD == true || Update == true))
            {
                dataGridView3["Loss Limit", dataGridView3.CurrentCell.RowIndex].Value = textBox3.Text;
            }
        }
        
        private void textBox4_TextChanged(object sender, EventArgs e)
        {
            if (dataGridView3.CurrentCell != null && (ADD == true || Update == true))
            {
                dataGridView3["Excess Point", dataGridView3.CurrentCell.RowIndex].Value = textBox4.Text;
            }
        }

        private void textBox12_TextChanged(object sender, EventArgs e)
        {
            if (textBox12.Text != "")
            {
                Regex r = new Regex("^[0-9]{1,}$");
                if (!r.IsMatch(textBox12.Text))
                {
                    MessageBox.Show("Please input number!");
                    textBox12.Text.Remove(textBox12.Text.Length - 1);
                }
                dataGridView2["Seq#", row].Value = int.Parse(textBox12.Text.ToString());
            }
        }

        private void textBox13_TextChanged(object sender, EventArgs e)
        {
            if (textBox13.Text != "")
            {
                dataGridView2["fbrkr1", row].Value = textBox13.Text;
            }
        }

        private void textBox15_TextChanged(object sender, EventArgs e)
        {
            if (textBox15.Text != "")
            {
                dataGridView2["Reinsurer", row].Value = textBox15.Text;
            }
        }

        private void textBox2_TextChanged(object sender, EventArgs e)
        {
            if (textBox2.Text != "")
            {
                dataGridView3["Item#", row1].Value = textBox2.Text.ToString();
            }
        }

        private void textBox13_Validated(object sender, EventArgs e)
        {
            string desc = CheckValue(textBox13.Text.ToString(), "Broker");
            if (desc == null)
            {
                textBox13.Text = "";
                MessageBox.Show("Invalid Value");
            }
            else
            {
                textBox14.Text = desc;
            }
        }

        private void textBox15_Validated(object sender, EventArgs e)
        {
            string desc = CheckValue(textBox15.Text.ToString(), "Reinsurer");
            if (desc == null)
            {
                textBox15.Text = "";
                MessageBox.Show("Invalid Value");
            }
            else
            {
                textBox16.Text = desc;
            }
        }

        public string CheckValue(string fid, string flag)
        {
            string str = "";

            if (flag == "Broker")
            {
                str = "select fid,fdesc from " + db + "mprdr where ftype='B' and fid='" + fid + "'";
            }
            if (flag == "Reinsurer")
            {
                str = "select fid,fdesc from " + db + "mprdr where ftype='R' and fid='" + fid + "'";
            }
            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["odata"].ConnectionString);
            SqlCommand cmd = new SqlCommand(str, con);
            con.Open();
            using (SqlDataReader sdr = cmd.ExecuteReader())
            {
                if (sdr.HasRows && sdr.Read())
                {
                    string fdesc = sdr["fdesc"].ToString().Trim();
                    sdr.Close();
                    return fdesc;
                }
                else { return null; }
            }
        }

        private void del1_Click(object sender, EventArgs e)
        {
            if (dataGridView2.RowCount != 0)
            {
                try
                {
                    DataTable dataGridView2Source = dataGridView2.DataSource as DataTable;
                    dataGridView2Source.Rows.RemoveAt(dataGridView2.CurrentCell.RowIndex);
                    dataGridView2.DataSource = dataGridView2Source;
                    dataGridView2.CurrentCell = dataGridView2.Rows[dataGridView2.Rows.Count - 1].Cells["Seq#"];
                }
                catch { }
            }
            if (dataGridView2.RowCount == 0)
            {
                foreach (Control ctrl in panel2.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.InactiveCaption;
                    }
                    if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                    if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
                }
            }
            total();
        }

        private void del2_Click(object sender, EventArgs e)
        {
            if (dataGridView3.RowCount != 0)
            {
                try
                {
                    DataTable dataGridView3Source = dataGridView3.DataSource as DataTable;
                    dataGridView3Source.Rows.RemoveAt(dataGridView3.CurrentCell.RowIndex);
                    dataGridView3.DataSource = dataGridView3Source;
                    dataGridView3.CurrentCell = dataGridView3.Rows[dataGridView2.Rows.Count - 1].Cells["Item#"];
                }
                catch { }
            }
            if (dataGridView3.RowCount == 0)
            {
                foreach (Control ctrl in panel2.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.InactiveCaption;
                    }
                    if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                    if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
                }
            }
        }

        private void add1_Click(object sender, EventArgs e)
        {
            foreach (DataRow MyDataRow in layr.Select("fctlid = '" + comboBox5.SelectedValue.ToString() + "'"))
            {
                dataSource.ImportRow(MyDataRow);
            }
            if (comboBox5.SelectedValue != null && dataSource.Rows.Count != 0)
            {
                DataRow[] dr = dataSource.Select("fctlid_2 ='" + comboBox5.SelectedValue.ToString() + "'");
                if (dr != null && dr.Length != 0)
                {
                    dataGridView2.DataSource = dataSource.Select("fctlid_2 ='" + comboBox5.SelectedValue.ToString() + "'").CopyToDataTable();
                    dataGridView2.CurrentCell = dataGridView2.Rows[dataGridView2.Rows.Count - 1].Cells["Seq#"];
                }
            }

            textBox12.BackColor = System.Drawing.SystemColors.Window;
            textBox12.ReadOnly = false;
            textBox12.Text = "";
            textBox13.BackColor = System.Drawing.SystemColors.Window;
            textBox13.ReadOnly = false;
            textBox13.Text = "";
            textBox14.Text = "";
            textBox16.Text = "";
            textBox15.BackColor = System.Drawing.SystemColors.Window;
            textBox15.ReadOnly = false;
            textBox15.Text = "";
            textBox17.BackColor = System.Drawing.SystemColors.Window;
            textBox17.ReadOnly = false;
            textBox17.Text = "0.0";
        }

        private void add2_Click(object sender, EventArgs e)
        {
            dataSource1 = dataGridView3.DataSource as DataTable;
            DataRow newCustomersRow = dataSource1.NewRow();
            dataSource1.Rows.InsertAt(newCustomersRow, dataSource1.Rows.Count);
            dataGridView3.DataSource = dataSource1;
            dataGridView3.CurrentCell = dataGridView3.Rows[dataSource1.Rows.Count - 1].Cells["Item#"];
            dataGridView3["list", dataSource1.Rows.Count - 1].Value = "CAR (SEC 1),CPM,FGP,MYP,PAR";
            string[] parts = dataGridView3.Rows[dataSource1.Rows.Count - 1].Cells["list"].Value.ToString().Split(',');
            List<string> listl1 = new List<string>(parts);
            list.Clear();
            list1.Clear();
            foreach (string item in listl1)
            {
                list.Add(item);
            }
            listBox1.DataSource = null;
            listBox1.DataSource = list;
            textBox2.BackColor = System.Drawing.SystemColors.Window;
            textBox2.ReadOnly = false;
            textBox2.Text = "";
            textBox3.BackColor = System.Drawing.SystemColors.Window;
            textBox3.ReadOnly = false;
            textBox3.Text = "";
            textBox4.BackColor = System.Drawing.SystemColors.Window;
            textBox4.ReadOnly = false;
            textBox4.Text = "";
        }

        private void dsave_Click(object sender, EventArgs e)
        {
            saveadd();
        }

        private void csave_Click(object sender, EventArgs e)
        {
            saveadd();
        }

        void saveadd()
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really Insert the item?", "Inserted Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                String fctlid_contact = fctlidlabelcontact.Text;
                String fctlid_info = fctlidlabelinfo.Text;
                if (ADD == true)
                {
                    string str = "select * from xsysparm where fidtype ='MTY'";
                    DataTable dt = new DataTable();
                    dt = operate.GetTable(str);
                    fctlid_a = dt.Rows[0]["fnxtid"].ToString();
                }

                if (textBox8.Text.Trim() != "")
                {
                    if (ADD == true)
                    {
                        string adddatestr = "insert into mxl(fctlid,fid,fdesc,fbrkr,fefffr,feffto,fcur,factive,ftshare,fctldel,finpuser,finpdate,fupduser,fupddate) values" +
                            "('" + fctlid_a + "','" + textBox8.Text.Trim().Replace("'", "''") + "','" + textBox9.Text.Trim().Replace("'", "''") + "','','" + textBox10.Text.Trim().Replace("'", "''") + "','" + textBox11.Text.Trim().Replace("'", "''") + "','" + comboBox3.SelectedItem.ToString().Trim().Replace("'", "''") + "','','100.0000','','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "')";
                        operate.OperateData(adddatestr);

                        string updatestr = "update xsysparm set fnxtid=RIGHT('00000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='MTY'";
                        operate.OperateData(updatestr);
                    }
                    else
                    {
                        string updatedatestr =
                            "update mxl set fid ='" + textBox8.Text.Trim() + "',fdesc ='" + textBox9.Text.Trim() + "',fefffr ='" + textBox10.Text.Trim() + "' ,feffto ='" + textBox11.Text.Trim() + "' ,fcur ='" + comboBox3.SelectedItem.ToString().Trim() + "', fupduser ='" + user + "',fupddate ='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid='" + fctlid_a + "'";
                        operate.OperateData(updatedatestr);
                    }

                    saveinfo();
                    saveContact();

                    MessageBox.Show("Have Been Inserted!", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    ButtonControlback();
                }
                else
                {
                    MessageBox.Show("Some field is empty!", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        void saveinfo()
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            DataTable dt = dataGridView2.DataSource as DataTable;
            if (dt != null)
            {
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    if (!checknewinfo(dt.Rows[i]["fctlid"].ToString().Trim()))
                    {
                        string str = "select * from xsysparm where fidtype ='mxlinsr'";
                        DataTable dt1 = new DataTable();
                        dt1 = operate.GetTable(str);
                        string fctlid_info = dt1.Rows[0]["fnxtid"].ToString();
                        string adddatestrc =
                                "insert into mxlinsr(fctlid_1,fctlid,flogseq,freinsr,fbrkr1,fbrkge1,fbrkr2,fbrkge2,fbrkr3,fbrkge3,fshare) " +
                        "values('" + fctlid_a + "','" + fctlid_info + "','" + dt.Rows[i]["Seq#"].ToString() + "','" + dt.Rows[i]["Reinsurer"].ToString().Trim().Replace("'", "''") + "','" + dt.Rows[i]["fbrkr1"].ToString().Trim().Replace("'", "''") + "','0.0000','','0.0000','','0.0000','" + dt.Rows[i]["Share (%)"].ToString() + "')";
                        operate.OperateData(adddatestrc);

                        string updatestrc = "update xsysparm set fnxtid=RIGHT('000000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='mxlinsr'";
                        operate.OperateData(updatestrc);
                    }
                    else
                    {
                        string updatedatestro =
                               "update mxlinsr set flogseq ='" + dt.Rows[i]["Seq#"].ToString() + "',freinsr ='" + dt.Rows[i]["Reinsurer"].ToString().Trim().Replace("'", "''") + "' ,fbrkr1 ='" + dt.Rows[i]["fbrkr1"].ToString().Trim().Replace("'", "''") + "',fshare ='" + dt.Rows[i]["Share (%)"].ToString() + "' where fctlid='" + dt.Rows[i]["fctlid"].ToString() + "'";
                        operate.OperateData(updatedatestro);

                        string sql = "select a.fctlid, a.flogseq as Seq#, freinsr as Reinsurer,fshare as [Share (%)], fbrkr1, " +
                                "b.fdesc as Bdesc,c.fdesc as Rdesc, a.fctlid_1 from mxlinsr a " +
                                "left join (select fid,fdesc  from mprdr where ftype='B') b on a.fbrkr1=b.fid " +
                                "left join (select fid,fdesc from mprdr where ftype='R') c on a.freinsr=c.fid " +
                                "where fctlid_1 = '" + fctlid_a + "' ORDER BY flogseq";
                        DataTable dt0 = operate.GetTable(sql);
                        if (dt != null)
                        {
                            string[] delsql = CompareDt(dt0, dt, "fctlid", "mxlinsr");
                            for (int j = 0; j <= delsql.Length - 1; j++)
                            {
                                operate.OperateData(delsql[j]);
                            }
                        }
                    }
                }
            }

        }

        void saveContact()
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            DataTable dt = dataGridView3.DataSource as DataTable;
            if (dt != null)
            {
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    if (!checknewsec(dt.Rows[i]["fctlid"].ToString().Trim()))
                    {
                        string str = "select * from xsysparm where fidtype ='mxlsec'";
                        DataTable dt1 = new DataTable();
                        dt1 = operate.GetTable(str);
                        string fctlid_contact = dt1.Rows[0]["fnxtid"].ToString();

                        string adddatestrc =
                                "insert into mxlsec(fctlid_1,fctlid,fid,ftylmt,ftimes,fscale1,fscale2,fcom,fgrnet,fbrkge,fgrnet2) " +
                        "values('" + fctlid_a + "','" + fctlid_contact + "','" + dt.Rows[i]["Item#"].ToString() + "',CAST('" + dt.Rows[i]["Treaty Limit"].ToString().Replace(",", "").Replace(".0000", "") + "' as decimal(19,0)),'" + dt.Rows[i]["ftimes"].ToString().Trim() + "',CAST('" + dt.Rows[i]["fscale1"].ToString().Trim() + "' as decimal(10,4)),Cast('" + dt.Rows[i]["fscale2"].ToString().Trim() + "' as decimal(10,4)),'" + dt.Rows[i]["fcom"].ToString().Trim() + "','','0.0000','1')";
                        operate.OperateData(adddatestrc);

                        string updatestro = "update xsysparm set fnxtid=RIGHT('00000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='mxlsec'";
                        operate.OperateData(updatestro);


                        List<string> results1 = dt.Rows[i]["list"].ToString().Split(',').ToList();
                        for (int j = 0; j < results1.Count; j++)
                        {
                            string str2 = "select * from xsysparm where fidtype ='MTYCLASS'";
                            DataTable dt2 = new DataTable();
                            dt2 = operate.GetTable(str2);
                            string fctlid_mxlclass = dt2.Rows[0]["fnxtid"].ToString();

                            string addmxlclass = "insert into MTYCLASS (fctlid_1,fctlid_2,fctlid,ftype,fclass,fsec,fdesc)" +
                                "values('" + fctlid_a + "','" + fctlid_contact + "','" + fctlid_mxlclass + "','1','" + Regex.Replace(listBox1.Items[j].ToString(), @"/\(([^\(]*)\)/", "").Replace(" (SEC 1)", "").Replace(" (SEC 2)", "") + "','','" + results1[j].ToString() + "')";
                            operate.OperateData(addmxlclass);

                            string updatestrc = "update xsysparm set fnxtid=RIGHT('00000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='MTYCLASS'";
                            operate.OperateData(updatestrc);
                        }
                        List<string> results2 = dt.Rows[i]["list"].ToString().Split(',').ToList();
                        for (int j = 0; j < results2.Count; j++)
                        {
                            string str2 = "select * from xsysparm where fidtype ='MTYCLASS'";
                            DataTable dt2 = new DataTable();
                            dt2 = operate.GetTable(str2);
                            string fctlid_mxlclass = dt2.Rows[0]["fnxtid"].ToString();

                            string addmxlclass1 = "insert into MTYCLASS (fctlid_1,fctlid_2,fctlid,ftype,fclass,fsec,fdesc)" +
                                "values('" + fctlid_a + "','" + fctlid_contact + "','" + fctlid_mxlclass + "','2','" + Regex.Replace(listBox2.Items[j].ToString(), @"/\(([^\(]*)\)/", "").Replace(" (SEC 1)", "").Replace(" (SEC 2)", "") + "','','" + results2[j].ToString() + "')";
                            operate.OperateData(addmxlclass1);

                            string updatestrc1 = "update xsysparm set fnxtid=RIGHT('00000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='MTYCLASS'";
                            operate.OperateData(updatestrc1);
                        }
                    }
                    else
                    {
                        string updatedatestro =
                               "update mxlsec set fid ='" + dt.Rows[i]["Item#"].ToString().Trim() + "',ftylmt ='" + dt.Rows[i]["Treaty Limit"].ToString().Trim().Replace(",", "").Replace(".0000", "") + "',fscale1 ='" + dt.Rows[i]["fscale1"].ToString().Trim() + "',fscale2 ='" + dt.Rows[i]["fscale2"].ToString().Trim() + "' where fctlid='" + dt.Rows[i]["fctlid"].ToString().Trim() + "'";
                        operate.OperateData(updatedatestro);

                        SqlParameter param = new SqlParameter("@fctlid_1", SqlDbType.NVarChar, 20);
                        param.Value = fctlid_a;
                        DataTable dt0 = DBHelper.GetDataSetProd("SurplusList", Dbm, param);
                        if (dt != null)
                        {
                            string[] delsql = CompareDt(dt0, dt, "fctlid", "mxlsec");
                            for (int k = 0; k <= delsql.Length - 1; k++)
                            {
                                operate.OperateData(delsql[k]);
                            }
                        }
                        List<string> result1 = dt.Rows[i]["list"].ToString().Split(',').ToList();
                        for (int j = 0; j < result1.Count; j++)
                        {
                            string updatemxlclass = "update MTYCLASS set ftype ='1' where fctlid_1='" + fctlid_a + "' and fctlid_2='" + dt.Rows[i]["fctlid"].ToString().Trim() + "' and fdesc ='" + result1[j].ToString() + "'";
                            operate.OperateData(updatemxlclass);
                        }
                        List<string> result2 = dt.Rows[i]["list1"].ToString().Split(',').ToList();
                        for (int j = 0; j < result2.Count; j++)
                        {
                            string updatemxlclass1 = "update MTYCLASS set ftype ='2' where fctlid_1='" + fctlid_a + "' and fctlid_2='" + dt.Rows[i]["fctlid"].ToString().Trim() + "' and fdesc ='" + result2[j].ToString() + "'";
                            operate.OperateData(updatemxlclass1);
                        }
                    }
                }
            }

        }

        public string[] CompareDt(DataTable dt1, DataTable dt2, string keyField, string flag)
        {
            //为三个表拷贝表结构
            DataTable dtRetDel = dt1.Clone();
            DataTable dtRetAdd = dt1.Clone();
            DataTable dtRetDif = dt1.Clone();

            ArrayList updsql = new ArrayList();
            int colCount = dt1.Columns.Count;

            DataView dv1 = dt1.DefaultView;
            DataView dv2 = dt2.DefaultView;

            //先以第一个表为参照，看第二个表是修改了还是删除了
            foreach (DataRowView dr1 in dv1)
            {
                dv2.RowFilter = keyField + " = '" + dr1[keyField].ToString() + "'";
                if (dv2.Count > 0)
                {
                    if (!CompareUpdate(dr1, dv2[0]))//比较是否有不同的
                    {
                        dtRetDif.Rows.Add(dv2[0].Row.ItemArray);//修改后
                        continue;
                    }
                }
                else
                {
                    //已经被删除的
                    dtRetDel.Rows.Add(dr1.Row.ItemArray);
                }
            }

            //以第一个表为参照，看记录是否是新增的
            dv2.RowFilter = "";//清空条件
            foreach (DataRowView dr2 in dv2)
            {
                dv1.RowFilter = keyField + " = '" + dr2[keyField].ToString() + "'";
                if (dv1.Count == 0)
                {
                    //新增的
                    dtRetAdd.Rows.Add(dr2.Row.ItemArray);
                }
            }

            if (dtRetDel != null && dtRetDel.Rows.Count > 0)
            {
                for (int i = 0; i < dtRetDel.Rows.Count; i++)
                {
                    string del;
                    if (flag == "mxlsec") { del = "delete from mxlsec where fctlid='" + dtRetDel.Rows[i]["fctlid"].ToString().Trim() + "'"; }
                    else { del = "delete from mxlinsr where fctlid='" + dtRetDel.Rows[i]["fctlid"].ToString().Trim() + "'"; }
                    updsql.Add(del);
                }
            }

            return (string[])updsql.ToArray(typeof(string));
        }

        //比较是否有不同的
        private static bool CompareUpdate(DataRowView dr1, DataRowView dr2)
        {
            //行里只要有一项不一样，整个行就不一样,无需比较其它
            object val1;
            object val2;
            for (int i = 0; i < dr1.Row.ItemArray.Length; i++)
            {
                val1 = dr1[i];
                val2 = dr2[i];
                if (!val1.Equals(val2))
                {
                    return false;
                }
            }
            return true;
        }

        public Boolean checknew(string strSql)
        {
            string str =
                    "select count(*) from mxl where fid ='" + strSql.Trim() + "'";
            int i = operate.SelectData(str);
            if (i > 0)
            { return true; }
            else { return false; }
        }

        public Boolean checknewinfo(string strSql)
        {
            string str =
                    "select count(*) from mxlinsr where fctlid ='" + strSql.Trim() + "' and fctlid_1 ='" + fctlid_a.Trim() + "'";
            int i = operate.SelectData(str);
            if (i > 0)
            { return true; }
            else { return false; }
        }

        public Boolean checknewsec(string strSql)
        {
            string str =
                    "select count(*) from mxlsec where fctlid ='" + strSql.Trim() + "' and fctlid_1 ='" + fctlid_a.Trim() + "'";
            int i = operate.SelectData(str);
            if (i > 0)
            { return true; }
            else { return false; }
        }

        void reload()
        {
            string str = "select * from mxl where fctlid ='" + fctlid_a + "'";
            DataTable dt = new DataTable();
            dt = operate.GetTable(str);
            textBox8.Text = dt.Rows[0]["fid"].ToString();
            textBox9.Text = dt.Rows[0]["fdesc"].ToString();
            if (dt.Rows[0]["fefffr"].ToString() != "")
            {
                try
                {
                    string a = dt.Rows[0]["fefffr"].ToString();
                    DateTime b = Convert.ToDateTime(a);
                    textBox10.Text = b.ToString("yyyy.MM.dd");
                }
                catch { }
            }
            if (dt.Rows[0]["feffto"].ToString() != "")
            {
                try
                {
                    string a = dt.Rows[0]["feffto"].ToString();
                    DateTime b = Convert.ToDateTime(a);
                    textBox11.Text = b.ToString("yyyy.MM.dd");
                }
                catch { }
            }
            comboBox3.SelectedItem = dt.Rows[0]["fcur"].ToString();
        }


        private String _strValue;

        public String StrValue
        {
            set
            {
                _strValue = value;
            }
        }

        private String _strValuebn;

        public String StrValuebn
        {
            set
            {
                _strValuebn = value;
            }
        }
        private String _strValuer;

        public String StrValuer
        {
            set
            {
                _strValuer = value;
            }
        }

        private String _strValuern;

        public String StrValuern
        {
            set
            {
                _strValuern = value;
            }
        }

        public Boolean CloseFlag = false;

        private void button4_Click(object sender, EventArgs e)
        {
            frmBrkSearch temp_form = new frmBrkSearch();
            temp_form.Owner = this;
            temp_form.flag = "frmXOL";
            temp_form.ShowDialog();
            if (CloseFlag == false)
            {
                textBox13.Text = _strValue;
                textBox14.Text = _strValuebn;
            }
        }

        private void button5_Click(object sender, EventArgs e)
        {
            frmReinsSearch temp_form = new frmReinsSearch();
            temp_form.Owner = this;
            temp_form.flag = "frmXOL";
            temp_form.ShowDialog();
            if (CloseFlag == false)
            {
                textBox15.Text = _strValuer;
                textBox16.Text = _strValuern;
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (listBox1.SelectedIndex != -1)
            {
                int total = listBox1.SelectedItems.Count;
                for (int x = 0; x < total; x++)
                {
                    list.Remove(listBox1.SelectedItem.ToString());
                    list1.Add(listBox1.SelectedItem.ToString());
                }
            }
            listBox1.DataSource = null;
            listBox2.DataSource = null;
            listBox1.DataSource = list;
            listBox2.DataSource = list1;
            dataGridView3["list", row1].Value = string.Join(",", list.ToArray());
            dataGridView3["list1", row1].Value = string.Join(",", list1.ToArray());
        }

        private void button3_Click(object sender, EventArgs e)
        {
            listBox2.DataSource = null;
            list1.AddRange(list);
            listBox2.DataSource = list1;
            listBox1.DataSource = null;
            list.Clear();
            dataGridView3["list", row1].Value = string.Join(",", list.ToArray());
            dataGridView3["list1", row1].Value = string.Join(",", list1.ToArray());
        }

        private void button6_Click(object sender, EventArgs e)
        {
            if (listBox2.SelectedIndex != -1)
            {
                int total = listBox2.SelectedItems.Count;
                for (int x = 0; x < total; x++)
                {
                    list1.Remove(listBox2.SelectedItem.ToString());
                    list.Add(listBox2.SelectedItem.ToString());
                }
            }
            listBox1.DataSource = null;
            listBox2.DataSource = null;
            listBox1.DataSource = list;
            listBox2.DataSource = list1;
            dataGridView3["list", row1].Value = string.Join(",", list.ToArray());
            dataGridView3["list1", row1].Value = string.Join(",", list1.ToArray());
        }

        private void button7_Click(object sender, EventArgs e)
        {
            list.AddRange(list1);
            listBox1.DataSource = list;
            listBox2.DataSource = null;
            list1.Clear();
            dataGridView3["list", row1].Value = string.Join(",", list.ToArray());
            dataGridView3["list1", row1].Value = string.Join(",", list1.ToArray());
        }

        private void button8_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonCountrolupdate();
            textBox8.Text = "";
        }

        private void comboBox5_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ADD == false && Update == false) { layrChag(); } else { layrChag2(); }
        }

        private void comboBox6_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ADD == false && Update == false) { layrChag(); } else { layrChag2(); }
        }

        public void layrChag()
        {
            if (comboBox5.SelectedValue != null)
            {
                string sql = "select a.fid, fminpm,fdeppm,fprate,fadjrate,fdrate,flimit, " +
                        "d.fctlid_1,d.fctlid_2,d.fctlid,flogseq as Seq#,  " +
                        "freinsr as Reinsurer,fshare as [Share (%)], fbrkr1, " +
                        "b.fdesc as Bdesc ,c.fdesc as Rdesc from mxllayr a " +
                        "left join mxlinsr d on a.fctlid_1 = d.fctlid_1 and a.fctlid = d.fctlid_2 " +
                        "left join (select fid,fdesc from mprdr where ftype='B') b on d.fbrkr1=b.fid " +
                        "left join (select fid,fdesc from mprdr where ftype='R') c on d.freinsr=c.fid " +
                        "where a.fctlid_1 = '" + fctlid_a + "' ORDER BY a.fid";
                DataTable dtGrid2 = operate.GetTable(sql).Select("fctlid_2 ='" + comboBox5.SelectedValue.ToString() + "'").CopyToDataTable();
                dataGridView2.DataSource = dtGrid2;
                foreach (DataGridViewColumn Column in dataGridView2.Columns)
                {
                    Column.Visible = false;
                }
                this.dataGridView2.Columns["Share (%)"].Visible = true;
                this.dataGridView2.Columns["Reinsurer"].Visible = true;
                this.dataGridView2.Columns["Seq#"].Visible = true;

                SqlParameter[] param = new SqlParameter[] {
                    new SqlParameter("@fctlid_1", fctlid_a),
                    new SqlParameter("@fctlid_2", comboBox5.SelectedValue.ToString()) };
                dataGridView3.DataSource = DBHelper.GetDataSetProd("XOLList", Dbm, param);
                foreach (DataGridViewColumn Column in dataGridView3.Columns)
                {
                    Column.Visible = false;
                }
                this.dataGridView3.Columns["Loss Limit"].Visible = true;
                this.dataGridView3.Columns["Excess Point"].Visible = true;
                this.dataGridView3.Columns["Item#"].Visible = true;
                for (int i = 0; i < dataGridView3.Rows.Count; i++)
                {
                    dataGridView3["Loss Limit", i].Value = (Convert.ToDecimal(dataGridView3["Loss Limit", i].Value)).ToString("n2");
                    dataGridView3["Excess Point", i].Value = (Convert.ToDecimal(dataGridView3["Excess Point", i].Value)).ToString("n2");
                }
            }
        }

        public void layrChag2()
        {
            if (comboBox5.SelectedValue != null && dataSource.Rows.Count != 0)
            {
                DataRow[] dr = dataSource.Select("fctlid_2 ='" + comboBox5.SelectedValue.ToString() + "'");
                if (dr != null && dr.Length != 0)
                {
                    DataTable dtGrid2 = dataSource.Select("fctlid_2 ='" + comboBox5.SelectedValue.ToString() + "'").CopyToDataTable();
                    dataGridView2.DataSource = dtGrid2;
                    foreach (DataGridViewColumn Column in dataGridView2.Columns)
                    {
                        Column.Visible = false;
                    }
                    this.dataGridView2.Columns["Share (%)"].Visible = true;
                    this.dataGridView2.Columns["Reinsurer"].Visible = true;
                    this.dataGridView2.Columns["Seq#"].Visible = true;
                }
                else
                {
                    string sql = "select a.fid, '' as fminpm,'' as fdeppm,'' as fprate,'' as fadjrate,'' as fdrate,'' as flimit, " +
                                "d.fctlid_1,d.fctlid_2,d.fctlid,flogseq as Seq#,  " +
                                "freinsr as Reinsurer,'' as [Share (%)], fbrkr1, " +
                                "b.fdesc as Bdesc,c.fdesc as Rdesc from mxllayr a " +
                                "left join mxlinsr d on a.fctlid_1 = d.fctlid_1 and a.fctlid = d.fctlid_2 " +
                                "left join (select fid,fdesc  from mprdr where ftype='B') b on d.fbrkr1=b.fid " +
                                "left join (select fid,fdesc from mprdr where ftype='R') c on d.freinsr=c.fid " +
                                "where a.fctlid_1 = '1' and a.fid ='01' ORDER BY a.fid";

                    dataGridView2.DataSource = operate.GetTable(sql);
                    foreach (DataGridViewColumn Column in dataGridView2.Columns)
                    {
                        Column.Visible = false;
                    }
                    this.dataGridView2.Columns["Share (%)"].Visible = true;
                    this.dataGridView2.Columns["Reinsurer"].Visible = true;
                    this.dataGridView2.Columns["Seq#"].Visible = true;
                }


                SqlParameter[] param = new SqlParameter[] {
                    new SqlParameter("@fctlid_1", fctlid_a),
                    new SqlParameter("@fctlid_2", comboBox5.SelectedValue.ToString()) };
                dataGridView3.DataSource = DBHelper.GetDataSetProd("XOLList", Dbm, param);
                foreach (DataGridViewColumn Column in dataGridView3.Columns)
                {
                    Column.Visible = false;
                }
                this.dataGridView3.Columns["Loss Limit"].Visible = true;
                this.dataGridView3.Columns["Excess Point"].Visible = true;
                this.dataGridView3.Columns["Item#"].Visible = true;
                for (int i = 0; i < dataGridView3.Rows.Count; i++)
                {
                    dataGridView3["Loss Limit", i].Value = (Convert.ToDecimal(dataGridView3["Loss Limit", i].Value)).ToString("n2");
                    dataGridView3["Excess Point", i].Value = (Convert.ToDecimal(dataGridView3["Excess Point", i].Value)).ToString("n2");
                }
            }
            //else { 
            //    DataRow[] layrRow = layr.Select("fctlid_2 ='" + comboBox5.SelectedValue.ToString() + "'");
            //    if (layrRow != null && layrRow.Length != 0)
            //    {
            //        fminpm.Text = layrRow[0]["fminpm"].ToString();
            //    }
            //}
        }

        private void add3_Click(object sender, EventArgs e)
        {
            DataRow newlayrRow = layr.NewRow();
            if (layr.Rows.Count != 0)
            {
                newlayrRow["fid"] = int.Parse(layr.Rows[layr.Rows.Count - 1]["fid"].ToString()) + 1;
                newlayrRow["fctlid"] = int.Parse(layr.Rows[layr.Rows.Count - 1]["fid"].ToString()) + 1;
                newlayrRow["fctlid_2"] = int.Parse(layr.Rows[layr.Rows.Count - 1]["fid"].ToString()) + 1;
            }
            else
            {
                newlayrRow["fid"] = 1;
                newlayrRow["fctlid"] = 1;
                newlayrRow["fctlid_2"] = 1;
            }
            layr.Rows.Add(newlayrRow);
            newlayrRow["fminpm"] = "";
            newlayrRow["fminpm"] = "";
            newlayrRow["fdeppm"] = "";
            newlayrRow["fprate"] = "";
            newlayrRow["fadjrate"] = "";
            newlayrRow["fdrate"] = "";
            newlayrRow["flimit"] = "";

            comboBox5.DataSource = layr;
            comboBox5.DisplayMember = "fid";
            comboBox5.ValueMember = "fctlid_2";
            comboBox5.SelectedValue = newlayrRow["fctlid_2"];
            comboBox6.DataSource = layr;
            comboBox6.DisplayMember = "fid";
            comboBox6.ValueMember = "fctlid_2";
            comboBox6.SelectedValue = newlayrRow["fctlid_2"];
            foreach (Control ctrl in panel8.Controls)                //循环窗体的控件
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                    ((TextBox)ctrl).ReadOnly = false;
                    ((TextBox)ctrl).Text = "";
                }
                //添加事件
            }
        }

        private void fminpm_Validated(object sender, EventArgs e)
        {
            DataRow[] layrRow = layr.Select("fctlid = '" + comboBox5.SelectedValue.ToString() + "'");
            layrRow[0]["fminpm"] = fminpm.Text;
        }

        private void fprate_Validated(object sender, EventArgs e)
        {
            DataRow[] layrRow = layr.Select("fctlid = '" + comboBox5.SelectedValue.ToString() + "'");
            layrRow[0]["fprate"] = fprate.Text;
        }

        private void fdrate_Validated(object sender, EventArgs e)
        {
            DataRow[] layrRow = layr.Select("fctlid = '" + comboBox5.SelectedValue.ToString() + "'");
            layrRow[0]["fdrate"] = fdrate.Text;
        }

        private void fdeppm_Validated(object sender, EventArgs e)
        {
            DataRow[] layrRow = layr.Select("fctlid = '" + comboBox5.SelectedValue.ToString() + "'");
            layrRow[0]["fdeppm"] = fdeppm.Text;
        }

        private void fadjrate_Validated(object sender, EventArgs e)
        {
            DataRow[] layrRow = layr.Select("fctlid = '" + comboBox5.SelectedValue.ToString() + "'");
            layrRow[0]["fadjrate"] = fadjrate.Text;
        }

        private void flimit_Validated(object sender, EventArgs e)
        {
            DataRow[] layrRow = layr.Select("fctlid = '" + comboBox5.SelectedValue.ToString() + "'");
            layrRow[0]["flimit"] = flimit.Text;
        }

        private void textBox12_Validated(object sender, EventArgs e)
        {
            DataRow[] dtRow = dataSource.Select("fctlid = '" + comboBox5.SelectedValue.ToString() + "'");
            dtRow[dtRow.Length - 1]["Seq#"] = textBox12.Text;
            if (textBox12.Text != "")
            {
                dataGridView2["Seq#", row].Value = textBox12.Text;
            }
        }




    }
}

