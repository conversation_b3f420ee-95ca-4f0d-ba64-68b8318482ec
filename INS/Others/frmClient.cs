using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using System.Text.RegularExpressions;

namespace INS
{
    public partial class frmClient : Form
    {
        public frmClient()
        {
            InitializeComponent();
            InitCombobox();
            FillData("","");
        }
        DES des = new DES();
        DBConnect operate = new DBConnect();
        ExportDBF DBF = new ExportDBF();
        XLS txt = new XLS();
        private DataTable dataSource = new DataTable();
        private DataTable dataSource1 = new DataTable();
        public int i = 0;
        public string fctlid_a = "";
        public String user = "";
        public int row = 0;
        public int row1 = 0;
        public Boolean flag = false;
        public Boolean ADD = false;
        public Boolean flag1 = false;
        private bool datagridview2SelectionChanged = true;
        private bool datagridview3SelectionChanged = true;
        void FillData(string order,string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
             try
            {
                if (order != "" && query != "")
                {
                    if (order == "Adjuster#")
                    {
                        a = new SqlDataAdapter("select fid as Adjuster#, fdesc as Desc#, fcname as Chinese,falias ,fadd1 ,fadd2 ,fadd3, fadd4 , " +
                            "fpostcode ,fcountry, fregion ,ftel ,ffax ,femail ,frep ,finpuser ,finpdate ,fupduser , fupddate,fctlid,fmap,case when fstatus='1' then 'Active' else case when fstatus='2' then 'Suspended' else 'Terminated' end end as status,  " +
                            "case when fsubside='1' then 'COLI' else case when fsubside='2' then 'COXX' else '------' end end as fsubside, CASE when finter ='1' then 'Yes' else 'No' end finter from mprdr where ftype = 'C' " +
                            "AND fid like '%" + query.ToUpper() + "%'", c);
                    }
                    else
                    {
                        a = new SqlDataAdapter("select fid as Adjuster#, fdesc as Desc#, fcname as Chinese,falias ,fadd1 ,fadd2 ,fadd3, fadd4 , " +
                        "fpostcode ,fcountry, fregion ,ftel ,ffax ,femail ,frep ,finpuser ,finpdate ,fupduser , fupddate,fctlid,fmap,case when fstatus='1' then 'Active' else case when fstatus='2' then 'Suspended' else 'Terminated' end end as status,  " +
                        "case when fsubside='1' then 'COLI' else case when fsubside='2' then 'COXX' else '------' end end as fsubside, CASE when finter ='1' then 'Yes' else 'No' end finter from mprdr where ftype = 'C' " +
                        "AND fdesc like '%" + query.ToUpper() + "%'", c);
                    }
            }
             else if (order != "" && query == ""){
                a = new SqlDataAdapter("select fid as Adjuster#, fdesc as Desc#, fcname as Chinese,falias ,fadd1 ,fadd2 ,fadd3, fadd4 , "+
                    "fpostcode ,fcountry, fregion ,ftel ,ffax ,femail ,frep ,finpuser ,finpdate ,fupduser , fupddate,fctlid,fmap,case when fstatus='1' then 'Active' else case when fstatus='2' then 'Suspended' else 'Terminated' end end as status,  " +
                    "case when fsubside='1' then 'COLI' else case when fsubside='2' then 'COXX' else '------' end end as fsubside, CASE when finter ='1' then 'Yes' else 'No' end finter from mprdr where ftype = 'C' " +
                    "order by " + order, c);
            }
            else {
                a = new SqlDataAdapter("select fid as Adjuster#, fdesc as Desc#, fcname as Chinese,falias ,fadd1 ,fadd2 ,fadd3, fadd4 , " +
                    "fpostcode ,fcountry, fregion ,ftel ,ffax ,femail ,frep ,finpuser ,finpdate ,fupduser , fupddate,fctlid,fmap,case when fstatus='1' then 'Active' else case when fstatus='2' then 'Suspended' else 'Terminated' end end as status,  " +
                    "case when fsubside='1' then 'COLI' else case when fsubside='2' then 'COXX' else '------' end end as fsubside, CASE when finter ='1' then 'Yes' else 'No' end finter from mprdr where ftype = 'C' order by fid ", c);
            }
                DataTable t = new DataTable();
                a.Fill(t);
                dataGridView1.DataSource = t;
                this.dataGridView1.Columns[3].Visible = false;
                this.dataGridView1.Columns[4].Visible = false;
                this.dataGridView1.Columns[5].Visible = false;
                this.dataGridView1.Columns[6].Visible = false;
                this.dataGridView1.Columns[7].Visible = false;
                this.dataGridView1.Columns[8].Visible = false;
                this.dataGridView1.Columns[9].Visible = false;
                this.dataGridView1.Columns[10].Visible = false;
                this.dataGridView1.Columns[11].Visible = false;
                this.dataGridView1.Columns[12].Visible = false;
                this.dataGridView1.Columns[13].Visible = false;
                this.dataGridView1.Columns[14].Visible = false;
                this.dataGridView1.Columns[15].Visible = false;
                this.dataGridView1.Columns[16].Visible = false;
                this.dataGridView1.Columns[17].Visible = false;
                this.dataGridView1.Columns[18].Visible = false;
                this.dataGridView1.Columns[19].Visible = false;
                this.dataGridView1.Columns[20].Visible = false;
                this.dataGridView1.Columns[21].Visible = false;
                this.dataGridView1.Columns[22].Visible = false;
                this.dataGridView1.Columns[23].Visible = false;
            }
             catch { }
                c.Close();
        }


        private void tabControl1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            int counter;
            counter = dataGridView1.CurrentCell.RowIndex;
            if (tabControl1.SelectedIndex == 1)
            {
                if (dataGridView1.Rows[counter].Cells["Adjuster#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Adjuster#"].Value.ToString().Length != 0)
                    {
                        adjust1.Text = dataGridView1.Rows[counter].Cells["Adjuster#"].Value.ToString();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        adjust2.Text = dataGridView1.Rows[counter].Cells["Desc#"].Value.ToString();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Chinese"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Chinese"].Value.ToString().Length != 0)
                    {
                        chine.Text = dataGridView1.Rows[counter].Cells["Chinese"].Value.ToString();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["falias"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["falias"].Value.ToString().Length != 0)
                    {
                        alias.Text = dataGridView1.Rows[counter].Cells["falias"].Value.ToString();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fadd1"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fadd1"].Value.ToString().Length != 0)
                    {
                        addr1.Text = dataGridView1.Rows[counter].Cells["fadd1"].Value.ToString();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fadd2"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fadd2"].Value.ToString().Length != 0)
                    {
                        addr2.Text = dataGridView1.Rows[counter].Cells["fadd2"].Value.ToString();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fadd3"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fadd3"].Value.ToString().Length != 0)
                    {
                        addr3.Text = dataGridView1.Rows[counter].Cells["fadd3"].Value.ToString();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fadd4"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fadd4"].Value.ToString().Length != 0)
                    {
                        addr4.Text = dataGridView1.Rows[counter].Cells["fadd4"].Value.ToString();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fpostcode"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fpostcode"].Value.ToString().Length != 0)
                    {
                        post.Text = dataGridView1.Rows[counter].Cells["fpostcode"].Value.ToString();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fcountry"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fcountry"].Value.ToString().Length != 0)
                    {
                        country.Text = dataGridView1.Rows[counter].Cells["fcountry"].Value.ToString();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fregion"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fregion"].Value.ToString().Length != 0)
                    {
                        region.Text = dataGridView1.Rows[counter].Cells["fregion"].Value.ToString();
                    }
                }


                if (dataGridView1.Rows[counter].Cells["ftel"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["ftel"].Value.ToString().Length != 0)
                    {
                        tel.Text = dataGridView1.Rows[counter].Cells["ftel"].Value.ToString();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["ffax"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["ffax"].Value.ToString().Length != 0)
                    {
                        fax.Text = dataGridView1.Rows[counter].Cells["ffax"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[counter].Cells["femail"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["femail"].Value.ToString().Length != 0)
                    {
                        email.Text = dataGridView1.Rows[counter].Cells["femail"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[counter].Cells["frep"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["frep"].Value.ToString().Length != 0)
                    {
                        rep.Text = dataGridView1.Rows[counter].Cells["frep"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[counter].Cells["fmap"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fmap"].Value.ToString().Length != 0)
                    {
                        mapping.Text = dataGridView1.Rows[counter].Cells["fmap"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[counter].Cells["status"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["status"].Value.ToString().Length != 0)
                    {
                        status.SelectedItem  = dataGridView1.Rows[counter].Cells["status"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[counter].Cells["finter"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["finter"].Value.ToString().Length != 0)
                    {
                        inter.SelectedItem = dataGridView1.Rows[counter].Cells["finter"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[counter].Cells["fsubside"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fsubside"].Value.ToString().Length != 0)
                    {
                        subside.SelectedItem = dataGridView1.Rows[counter].Cells["fsubside"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[counter].Cells["finpuser"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["finpuser"].Value.ToString().Length != 0)
                    {
                        finpuser.Text = dataGridView1.Rows[counter].Cells["finpuser"].Value.ToString();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["finpdate"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["finpdate"].Value.ToString().Length != 0)
                    {
                        finpdate.Text = dataGridView1.Rows[counter].Cells["finpdate"].Value.ToString();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fupduser"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fupduser"].Value.ToString().Length != 0)
                    {
                        fupduser.Text = dataGridView1.Rows[counter].Cells["fupduser"].Value.ToString();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fupddate"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fupddate"].Value.ToString().Length != 0)
                    {
                        fupddate.Text = dataGridView1.Rows[counter].Cells["fupddate"].Value.ToString();
                    }
                }

                fctlid_a = dataGridView1.Rows[counter].Cells["fctlid"].Value.ToString();
            }
            if (tabControl1.SelectedIndex == 2)
            {
                fctlid_a = dataGridView1.Rows[counter].Cells["fctlid"].Value.ToString();
                textBox2.Text = "";
                textBox3.Text = "";
                textBox4.Text = ""; 
                textBox5.Text = ""; 
                textBox6.Text = ""; 
                textBox7.Text = "";
                textBox8.Text = "";
                textBox9.Text = ""; 
                reloadgridview2("");
                reloadgridview3("");
            }
            if (tabControl1.SelectedIndex == 0)
            {
                FillData("", "");
            }
        }

        void reloadgridview2(string fid)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select fctldel,fctlid_1, fctlid, fname as Name, fpost as Position,ftel as [Tel No.],ffax as Fax,femail as Email from mcontact where fctlid_1 = '" + fctlid_a + "' ORDER BY name", c))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                if (fid == "" && row == 0)//fisrt row
                {
                    if (t2.Rows.Count != 0)
                    {
                        dataGridView2.DataSource = t2;
                        dataGridView2.CurrentCell = dataGridView2.Rows[row].Cells["Name"];
                    }
                    else
                    {
                        datagridview2SelectionChanged = false;
                        dataGridView2.DataSource = t2;
                    }
                }
                else
                {
                    datagridview2SelectionChanged = false;
                    dataGridView2.DataSource = t2;
                    if (fid == "" && row != 0) // add cancel
                    {
                        datagridview2SelectionChanged = true;
                        flag = true;
                        row = int.Parse(rowlabelcontact.Text);
                        dataGridView2.CurrentCell = dataGridView2.Rows[int.Parse(rowlabelcontact.Text)].Cells["Name"];
                    }
                    if (fid != "") //add update
                    {
                        for (int i = 0; i < t2.Rows.Count; i++)
                        {
                            if (t2.Rows[i]["Name"].ToString().Trim() == fid.Trim())
                            {
                                flag = true;
                                row = i;
                                rowlabelcontact.Text = i.ToString();
                                dataGridView2.CurrentCell = dataGridView2.Rows[i].Cells["Name"];
                                break;
                            }
                        }
                    }
                }
                this.dataGridView2.Columns[0].Visible = false;
                this.dataGridView2.Columns[1].Visible = false;
                this.dataGridView2.Columns[2].Visible = false;
            }
            datagridview2SelectionChanged = true;
            flag = false;
            c.Close();

        }

        void reloadgridview3(string fid)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select fctlid_1,fctlid, flogseq as Seq#,fdesc as [Old Name], frepdate from mnamhist where fctlid_1 = '" + fctlid_a + "' ORDER BY Seq#", c))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                if (fid == "" && row1 == 0)//fisrt row
                {
                    if (t2.Rows.Count != 0)
                    {
                        dataGridView3.DataSource = t2;
                        dataGridView3.CurrentCell = dataGridView3.Rows[row1].Cells["Old Name"];
                    }
                    else
                    {
                        datagridview3SelectionChanged = false;
                        dataGridView3.DataSource = t2;
                    }
                }
                else
                {
                    datagridview3SelectionChanged = false;
                    dataGridView3.DataSource = t2;
                    if (fid == "" && row1 != 0) // add cancel
                    {
                        datagridview3SelectionChanged = true;
                        flag1 = true;
                        row1 = int.Parse(rowlabelold.Text);
                        dataGridView3.CurrentCell = dataGridView3.Rows[int.Parse(rowlabelold.Text)].Cells["Old Name"];
                    }
                    if (fid != "") //add update
                    {
                        for (int i = 0; i < t2.Rows.Count; i++)
                        {
                            if (t2.Rows[i]["Old Name"].ToString().Trim() == fid.Trim())
                            {
                                flag1 = true;
                                row1 = i;
                                rowlabelold.Text = i.ToString();
                                dataGridView3.CurrentCell = dataGridView3.Rows[i].Cells["Old Name"];
                                break;
                            }
                        }
                    }
                }
                this.dataGridView3.Columns[0].Visible = false;
                this.dataGridView3.Columns[1].Visible = false;
                this.dataGridView3.Columns[4].Visible = false;
            }
            datagridview3SelectionChanged = true;
            flag1 = false;
            c.Close();

        }

        private void dataGridView2_SelectionChanged(object sender, EventArgs e)
        {
            if (!this.datagridview2SelectionChanged) return;
            try
            {
                if (flag == false)
                {
                    row = dataGridView2.CurrentCell.RowIndex;
                }

                if (dataGridView2.Rows[row].Cells["Name"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["Name"].Value.ToString().Length != 0)
                    {
                        textBox2.Text = dataGridView2.Rows[row].Cells["Name"].Value.ToString().Trim();
                    }
                }

                if (dataGridView2.Rows[row].Cells["Position"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["Position"].Value.ToString().Length != 0)
                    {
                        textBox3.Text = dataGridView2.Rows[row].Cells["Position"].Value.ToString().Trim();
                    }
                }

                if (dataGridView2.Rows[row].Cells["Tel No."].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["Tel No."].Value.ToString().Length != 0)
                    {
                        textBox4.Text = dataGridView2.Rows[row].Cells["Tel No."].Value.ToString().Trim();
                    }
                }

                if (dataGridView2.Rows[row].Cells["Fax"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["Fax"].Value.ToString().Length != 0)
                    {
                        textBox5.Text = dataGridView2.Rows[row].Cells["Fax"].Value.ToString().Trim();
                    }
                }

                if (dataGridView2.Rows[row].Cells["Email"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["Email"].Value.ToString().Length != 0)
                    {
                        textBox6.Text = dataGridView2.Rows[row].Cells["Email"].Value.ToString().Trim();
                    }
                }
                if (dataGridView2.Rows[row].Cells["fctlid"].Value != null)
                {
                    fctlidlabelcontact.Text = dataGridView2.Rows[row].Cells["fctlid"].Value.ToString();
                }
                rowlabelcontact.Text = row.ToString();
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void dataGridView3_SelectionChanged(object sender, EventArgs e)
        {
            if (!this.datagridview3SelectionChanged) return;
            try
            {
                if (flag1 == false)
                {
                    row1 = dataGridView3.CurrentCell.RowIndex;
                }

                if (dataGridView3.Rows[row1].Cells["Seq#"].Value != null)
                {
                    if (dataGridView3.Rows[row1].Cells["Seq#"].Value.ToString().Length != 0)
                    {
                        textBox7.Text = dataGridView3.Rows[row1].Cells["Seq#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView3.Rows[row1].Cells["Old Name"].Value != null)
                {
                    if (dataGridView3.Rows[row1].Cells["Old Name"].Value.ToString().Length != 0)
                    {
                        textBox8.Text = dataGridView3.Rows[row1].Cells["Old Name"].Value.ToString().Trim();
                    }
                }

                if (dataGridView3.Rows[row1].Cells["frepdate"].Value != null)
                {
                    if (dataGridView3.Rows[row1].Cells["frepdate"].Value.ToString().Length != 0)
                    {
                        textBox9.Text = dataGridView3.Rows[row1].Cells["frepdate"].Value.ToString().Trim();
                    }
                }

                if (dataGridView3.Rows[row1].Cells["fctlid"].Value != null)
                {
                    fctlidlabelold.Text = dataGridView3.Rows[row1].Cells["fctlid"].Value.ToString();
                }
                rowlabelold.Text = row1.ToString();
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            //e.Cancel = true;
        }

        private void tabControl1_Selecting2(object sender, TabControlCancelEventArgs e)
        {
            //e.Cancel = false;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", textBox1.Text.ToString().Trim());
        }

        public enum Mode
        {
            Adjuster = 1,
            Desc = 2
        }

        private void InitCombobox()
        {
            Array arr = System.Enum.GetValues(typeof(Mode));    // 获取枚举的所有值
            DataTable dt = new DataTable();
            dt.Columns.Add("String", Type.GetType("System.String"));
            dt.Columns.Add("Value", typeof(int));
            foreach (var a in arr)
            {
                string strText = EnumTextByDescription.GetEnumDesc((Mode)a);
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = (int)a;

                dt.Rows.Add(aRow);
            }

            comboBox1.DataSource = dt;
            comboBox1.DisplayMember = "String";
            comboBox1.ValueMember = "Value";
        }
        void ButtonControlback()
        {
            ADD = false;
            adjust1.BackColor = System.Drawing.SystemColors.InactiveCaption;
            adjust1.ReadOnly = true;
            adjust2.BackColor = System.Drawing.SystemColors.InactiveCaption;
            adjust2.ReadOnly = true;
            chine.BackColor = System.Drawing.SystemColors.InactiveCaption;
            chine.ReadOnly = true;
            alias.BackColor = System.Drawing.SystemColors.InactiveCaption;
            alias.ReadOnly = true;
            addr1.BackColor = System.Drawing.SystemColors.InactiveCaption ;
            addr1.ReadOnly = true;
            addr2.BackColor = System.Drawing.SystemColors.InactiveCaption;
            addr2.ReadOnly = true;
            addr3.BackColor = System.Drawing.SystemColors.InactiveCaption;
            addr3.ReadOnly = true;
            addr4.BackColor = System.Drawing.SystemColors.InactiveCaption;
            addr4.ReadOnly = true;
            post.BackColor = System.Drawing.SystemColors.InactiveCaption;
            post.ReadOnly = true;
            country.BackColor = System.Drawing.SystemColors.InactiveCaption;
            country.ReadOnly = true;
            region.BackColor = System.Drawing.SystemColors.InactiveCaption;
            region.ReadOnly = true;
            tel.BackColor = System.Drawing.SystemColors.InactiveCaption;
            tel.ReadOnly = true;
            fax.BackColor = System.Drawing.SystemColors.InactiveCaption;
            fax.ReadOnly = true;
            email.BackColor = System.Drawing.SystemColors.InactiveCaption;
            email.ReadOnly = true;
            rep.BackColor = System.Drawing.SystemColors.InactiveCaption;
            rep.ReadOnly = true;
            mapping.BackColor = System.Drawing.SystemColors.InactiveCaption;
            mapping.ReadOnly = true;
            status.BackColor = System.Drawing.SystemColors.InactiveCaption;
            status.Enabled = false;
            inter.BackColor = System.Drawing.SystemColors.InactiveCaption;
            inter.Enabled = false;
            subside.BackColor = System.Drawing.SystemColors.InactiveCaption;
            subside.Enabled = false;
            textBox2.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox2.ReadOnly = true;
            textBox3.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox3.ReadOnly = true;
            textBox4.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox4.ReadOnly = true;
            textBox5.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox5.ReadOnly = true;
            textBox6.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox6.ReadOnly = true;
            textBox7.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox7.ReadOnly = true;
            textBox8.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox8.ReadOnly = true;
            textBox9.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox9.ReadOnly = true;
            dcancel.Visible = false;
            dsave.Visible = false;
            dexit.Visible = true;
            dadd.Visible = true;
            dupdate.Visible = true;
            ddel.Visible = true;
            dprint.Visible = true;
            csave.Visible = false;
            cCancel.Visible = false;
            cadd.Visible = true;
            cdel.Visible = true;
            cupdate.Visible = true;
            cprint.Visible = true;
            cexit.Visible = true;
            add1.Visible = false;
            add2.Visible = false;
            del1.Visible = false;
            del2.Visible = false;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting2);
        }

        void ButtonControl()
        {
            add1.Visible = true;
            add2.Visible = true;
            del1.Visible = true;
            del2.Visible = true;
            dsave.Visible = true;
            csave.Visible = true;
            cCancel.Visible = true;
            cadd.Visible = false;
            cdel.Visible = false;
            cupdate.Visible = false;
            cprint.Visible = false;
            cexit.Visible = false;
            adjust1.BackColor = System.Drawing.SystemColors.Window;
            adjust1.ReadOnly = false;
            adjust2.BackColor = System.Drawing.SystemColors.Window;
            adjust2.ReadOnly = false;
            chine.BackColor = System.Drawing.SystemColors.Window;
            chine.ReadOnly = false;
            alias.BackColor = System.Drawing.SystemColors.Window;
            alias.ReadOnly = false;
            addr1.BackColor = System.Drawing.SystemColors.Window;
            addr1.ReadOnly = false;
            addr2.BackColor = System.Drawing.SystemColors.Window;
            addr2.ReadOnly = false;
            addr3.BackColor = System.Drawing.SystemColors.Window;
            addr3.ReadOnly = false;
            addr4.BackColor = System.Drawing.SystemColors.Window;
            addr4.ReadOnly = false;
            post.BackColor = System.Drawing.SystemColors.Window;
            post.ReadOnly = false;
            country.BackColor = System.Drawing.SystemColors.Window;
            country.ReadOnly = false;
            region.BackColor = System.Drawing.SystemColors.Window;
            region.ReadOnly = false;
            tel.BackColor = System.Drawing.SystemColors.Window;
            tel.ReadOnly = false;
            fax.BackColor = System.Drawing.SystemColors.Window;
            fax.ReadOnly = false;
            email.BackColor = System.Drawing.SystemColors.Window;
            email.ReadOnly = false;
            rep.BackColor = System.Drawing.SystemColors.Window;
            rep.ReadOnly = false;
            mapping.BackColor = System.Drawing.SystemColors.Window;
            mapping.ReadOnly = false;
            status.BackColor = System.Drawing.SystemColors.Window;
            status.Enabled  = true;
            inter.BackColor = System.Drawing.SystemColors.Window;
            inter.Enabled = true;
            subside.BackColor = System.Drawing.SystemColors.Window;
            subside.Enabled = true;
            dcancel.Visible = true;
            dexit.Visible = false;
            dadd.Visible = false;
            dupdate.Visible = false;
            ddel.Visible = false;
            dprint.Visible = false;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);
        }

        void ButtonControladd()
        {
            adjust1.Text = "";
            adjust2.Text = "";
            chine.Text = "";
            alias.Text = "";
            addr1.Text = "";
            addr2.Text = "";
            addr3.Text = "";
            addr4.Text = "";
            post.Text = "";
            country.Text = "";
            region.Text = "";
            tel.Text = "";
            fax.Text = "";
            email.Text = "";
            rep.Text = "";
            mapping.Text = "";
            finpuser.Text = "";
            finpdate.Text = "";
            fupddate.Text = "";
            fupduser.Text = "";
        }

        void reload()
        {
            string str = "select fid , fdesc, fcname, falias ,fadd1 ,fadd2 ,fadd3, fadd4 , "+
                        "fpostcode ,fcountry, fregion ,ftel ,ffax ,femail ,frep ,finpuser ,finpdate ,fupduser , fupddate,fmap,case when fstatus='1' then 'Active' else case when fstatus='2' then 'Suspended' else 'Terminated' end end as status, " +
                        "case when fsubside='1' then 'COLI' else case when fsubside='2' then 'COXX' else '------' end end as fsubside, CASE when finter ='1' then 'Yes' else 'No' end finter from mprdr where ftype = 'C' " +
                        "and fctlid ='" + fctlid_a + "'";
            DataTable dt = new DataTable();
            dt = operate.GetTable(str);
            adjust1.Text = dt.Rows[0]["fid"].ToString();
            adjust2.Text = dt.Rows[0]["fdesc"].ToString();
            chine.Text = dt.Rows[0]["fcname"].ToString();
            alias.Text = dt.Rows[0]["falias"].ToString();
            addr1.Text = dt.Rows[0]["fadd1"].ToString();
            addr2.Text = dt.Rows[0]["fadd2"].ToString();
            addr3.Text = dt.Rows[0]["fadd3"].ToString();
            addr4.Text = dt.Rows[0]["fadd4"].ToString();
            post.Text = dt.Rows[0]["fpostcode"].ToString();
            country.Text = dt.Rows[0]["fcountry"].ToString();
            region.Text = dt.Rows[0]["fregion"].ToString();
            tel.Text = dt.Rows[0]["ftel"].ToString();
            fax.Text = dt.Rows[0]["ffax"].ToString();
            email.Text = dt.Rows[0]["femail"].ToString();
            rep.Text = dt.Rows[0]["frep"].ToString();
            mapping.Text = dt.Rows[0]["fmap"].ToString();
            status.SelectedItem = dt.Rows[0]["status"].ToString();
            inter.SelectedItem = dt.Rows[0]["finter"].ToString();
            subside.SelectedItem = dt.Rows[0]["fsubside"].ToString();
            finpuser.Text = dt.Rows[0]["finpuser"].ToString();
            finpdate.Text = dt.Rows[0]["finpdate"].ToString();
            fupduser.Text = dt.Rows[0]["fupduser"].ToString();
            fupddate.Text = dt.Rows[0]["fupddate"].ToString();
        }

        private void button9_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void dexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void cexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void dcancel_Click(object sender, EventArgs e)
        {
            ButtonControlback();
            reload();
            reloadgridview2("");
            reloadgridview3("");
        }

        private void cCancel_Click(object sender, EventArgs e)
        {
            ButtonControlback();
            reload();
            reloadgridview2("");
            reloadgridview3("");
        }

        private void lupdate_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
        }

        private void dupdate_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
        }

        private void cupdate_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
        }

        private void ladd_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonControladd();
        }

        private void dadd_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonControladd();
        }

        private void cadd_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonControladd();
        }

        private void ldel_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            del();
        }

        private void ddel_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            del();
            reloadgridview2("");
            reloadgridview3("");
        }

        private void cdel_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            del();
        }

        void del()
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the ALL items?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string deldatestr1 =
                    "delete from mprdr where fctlid='" + fctlid_a + "'";
                operate.OperateData(deldatestr1);
                string deldatestr =
                    "delete from mnamhist where fctlid_1='" + fctlid_a + "'";
                operate.OperateData(deldatestr);
                string deldatestr2 =
                    "delete from mcontact where fctlid_1='" + fctlid_a + "'";
                operate.OperateData(deldatestr2);
                MessageBox.Show("Have Been Deleted", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                FillData("", "");
                tabControl1.SelectedTab = Classes;
            }
           
        }

        private void adjust1_TextChanged(object sender, EventArgs e)
        {
            string context = this.textBox4.Text;

            string str1 = "SELECT fid FROM mprdr where fctlid = '" + fctlid_a + "'";
            DataTable dt1 = new DataTable();
            dt1 = operate.GetTable(str1);
            for (int i = 0; i < dt1.Rows.Count; i++)
            {
                if ((dt1.Rows[i]["fid"].ToString().Trim() == context.Trim()) && (ADD == true))
                {
                    MessageBox.Show("Duplicate", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private void del1_Click(object sender, EventArgs e)
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the item?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string fctlid = fctlidlabelcontact.Text;
                string deldatestr =
                    "delete from mcontact where fctlid='" + fctlid + "'";
                operate.OperateData(deldatestr);
                MessageBox.Show("Have Been Deleted", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                textBox2.Text = "";
                textBox3.Text = "";
                textBox4.Text = "";
                textBox5.Text = "";
                textBox6.Text = "";
                reloadgridview2("");
            }
        }

        private void del2_Click(object sender, EventArgs e)
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the item?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string fctlid = fctlidlabelold.Text;
                string deldatestr =
                    "delete from mnamhist where fctlid='" + fctlid + "'";
                operate.OperateData(deldatestr);
                MessageBox.Show("Have Been Deleted", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                textBox7.Text = "";
                textBox8.Text = "";
                textBox9.Text = "";
                reloadgridview3("");
            }
        }

        private void add1_Click(object sender, EventArgs e)
        {
            dataSource = dataGridView2.DataSource as DataTable;
            DataRow newCustomersRow = dataSource.NewRow();
            newCustomersRow["Name"] = textBox2.Text;
            newCustomersRow["Position"] = textBox3.Text;
            newCustomersRow["Email"] = textBox6.Text;
            newCustomersRow["Fax"] = textBox5.Text;
            newCustomersRow["fctlid"] = fctlidlabelcontact;
            newCustomersRow["Tel No."] = textBox4.Text;
            newCustomersRow["fctlid_1"] = fctlid_a;
            newCustomersRow["fctldel"] = "";
            dataSource.Rows.Add(newCustomersRow);
            dataGridView2.DataSource = dataSource;
            textBox2.BackColor = System.Drawing.SystemColors.Window;
            textBox2.ReadOnly = false;
            textBox2.Text = "";
            textBox3.BackColor = System.Drawing.SystemColors.Window;
            textBox3.ReadOnly = false;
            textBox3.Text = "";
            textBox4.BackColor = System.Drawing.SystemColors.Window;
            textBox4.ReadOnly = false;
            textBox4.Text = "";
            textBox5.BackColor = System.Drawing.SystemColors.Window;
            textBox5.ReadOnly = false;
            textBox5.Text = "";
            textBox6.BackColor = System.Drawing.SystemColors.Window;
            textBox6.ReadOnly = false;
            textBox6.Text = "";
        }

        private void add2_Click(object sender, EventArgs e)
        {
            i++;
            dataSource1 = dataGridView3.DataSource as DataTable;
            DataRow newCustomersRow = dataSource1.NewRow();

            if (textBox7.Text == "")
            {
                newCustomersRow["Seq#"] = i;
            }
            else { newCustomersRow["Seq#"] = int.Parse(textBox7.Text.ToString()); }
            newCustomersRow["Old Name"] = textBox8.Text;
            DateTime time = DateTime.Now;
            if (textBox9.Text == "")
            {
                newCustomersRow["frepdate"] = time;
            }
            else { newCustomersRow["frepdate"] = Convert.ToDateTime(textBox9.Text); }
            newCustomersRow["fctlid"] = fctlidlabelold;
            newCustomersRow["fctlid_1"] = fctlid_a;
            dataSource1.Rows.Add(newCustomersRow);
            dataGridView3.DataSource = dataSource1;
            textBox7.BackColor = System.Drawing.SystemColors.Window;
            textBox7.ReadOnly = false;
            textBox7.Text = "";
            textBox8.BackColor = System.Drawing.SystemColors.Window;
            textBox8.ReadOnly = false;
            textBox8.Text = "";
            textBox9.BackColor = System.Drawing.SystemColors.Window;
            textBox9.ReadOnly = false;
            textBox9.Text = "";
        }

        private void dsave_Click(object sender, EventArgs e)
        {
            saveadd();
            ButtonControlback();
        }

        private void csave_Click(object sender, EventArgs e)
        {
            saveadd();
            ButtonControlback();
        }

        void saveadd() {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really Insert the item?", "Inserted Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                String fctlid_contact = fctlidlabelcontact.Text;
                String fctlid_old = fctlidlabelold.Text;
                String fstatus = "";
                if (status.SelectedItem.ToString().Trim() == "Active")
                { fstatus = "1"; }
                else if (status.SelectedItem.ToString().Trim() == "Suspended")
                { fstatus = "2"; }
                else if (status.SelectedItem.ToString().Trim() == "Terminated")
                { fstatus = "3"; }
                String fsubside = "";
                if (subside.SelectedItem.ToString().Trim() == "COLI")
                { fsubside = "1"; }
                else if (subside.SelectedItem.ToString().Trim() == "COXX")
                { fsubside = "2"; }
                else if (subside.SelectedItem.ToString().Trim() == "------")
                { fsubside = "3"; }
                String finter = "";
                if (inter.SelectedItem.ToString().Trim() == "Yes")
                { finter = "1"; }
                else
                { finter = "2"; }
                if (!checknew(adjust1.Text))
                {
                    string str = "select * from xsysparm where fidtype ='PRDID'";
                    DataTable dt = new DataTable();
                    dt = operate.GetTable(str);
                    fctlid_a = dt.Rows[0]["fnxtid"].ToString();
                }

               

                if (adjust1.Text.Trim() != "" && adjust2.Text.Trim() != "")
                {
                    if (!checknew(adjust1.Text))
                    {
                        string adddatestr = "insert into mprdr(ftype,fctlid,fid,fmap,fdescidx,fdesc,fcname,falias,fadd1,fadd2,fadd3,fadd4,fpostcode,fcountry,fregion,ftel,ffax,femail,frep,finter,fsubside,fstatus,fctldel,finpuser,finpdate,fupduser,fupddate) values" +
                            "('C','" + fctlid_a + "','" + adjust1.Text.Trim().Replace("'", "''") + "','" + mapping.Text.Trim().Replace("'", "''") + "','" + adjust2.Text.Trim().ToUpper().Replace("'", "''") + "','" + adjust2.Text.Trim().Replace("'", "''") + "','" + chine.Text.Trim().Replace("'", "''") + "','" + alias.Text.Trim().Replace("'", "''") + "','" + addr1.Text.Trim().Replace("'", "''") + "','" + addr2.Text.Trim().Replace("'", "''") + "','" + addr3.Text.Trim().Replace("'", "''") + "','" + addr4.Text.Trim().Replace("'", "''") + "','" + post.Text.Trim().Replace("'", "''") + "','" + country.Text.Trim().Replace("'", "''") + "','" + region.Text.Trim().Replace("'", "''") + "','" + tel.Text.Trim().Replace("'", "''") + "','" + fax.Text.Trim().Replace("'", "''") + "','" + email.Text.Trim().Replace("'", "''") + "','" + rep.Text.Trim().Replace("'", "''") + "','" + finter.Trim().Replace("'", "''") + "','" + fsubside.Trim().Replace("'", "''") + "','" + fstatus.Trim().Replace("'", "''") + "','','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "')";
                        operate.OperateData(adddatestr);

                        string updatestr = "update xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='PRDID'";
                        operate.OperateData(updatestr);
                    }
                    else {
                        string updatedatestr =
                            "update mprdr set finter ='" + finter.Trim().Replace("'", "''") + "', fsubside ='" + fsubside.Trim().Replace("'", "''") + "', fstatus ='" + fstatus.Trim().Replace("'", "''") + "', fid ='" + adjust1.Text.Trim().Replace("'", "''") + "',fmap ='" + mapping.Text.Trim().Replace("'", "''") + "',fdescidx ='" + adjust2.Text.Trim().ToUpper().Replace("'", "''") + "' ,fdesc ='" + adjust2.Text.Trim().Replace("'", "''") + "' ,fcname ='" + chine.Text.Trim().Replace("'", "''") + "',falias ='" + alias.Text.Trim().Replace("'", "''") + "',fadd1 ='" + addr1.Text.Trim().Replace("'", "''") + "',fadd2 ='" + addr2.Text.Trim().Replace("'", "''") + "',fadd3 ='" + addr3.Text.Trim().Replace("'", "''") + "',fadd4 ='" + addr4.Text.Trim().Replace("'", "''") + "',fpostcode ='" + post.Text.Trim().Replace("'", "''") + "',fcountry ='" + country.Text.Trim().Replace("'", "''") + "',fregion ='" + region.Text.Trim().Replace("'", "''") + "',ftel ='" + tel.Text.Trim().Replace("'", "''") + "',ffax ='" + fax.Text.Trim().Replace("'", "''") + "',femail ='" + email.Text.Trim().Replace("'", "''") + "',frep ='" + rep.Text.Trim().Replace("'", "''") + "',fupduser ='" + user + "',fupddate ='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid='" + fctlid_a + "'";
                        operate.OperateData(updatedatestr);
                    }
                    saveContact();
                    saveOld();



                    MessageBox.Show("Have Been Inserted!", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    ButtonControl();
                    reloadgridview2(textBox2.Text.Trim());
                    reloadgridview3(textBox8.Text.Trim());
                    reload();
                }
                else
                {
                    MessageBox.Show("Some field is empty!", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        public Boolean checknew(string strSql)
        {
            string str =
                    "select count(*) from mprdr where fid ='" + strSql.Trim() + "'";
            int i = operate.SelectData(str);
            if (i > 0)
            { return true; }
            else { return false; }
        }

        public Boolean checknewcontact(string strSql)
        {
            string str =
                    "select count(*) from mcontact where fctlid ='" + strSql.Trim() + "' and fctlid_1 ='" + fctlid_a.Trim() + "'";
            int i = operate.SelectData(str);
            if (i > 0)
            { return true; }
            else { return false; }
        }

        public Boolean checknewold(string strSql)
        {
            string str =
                    "select count(*) from mnamhist where fctlid ='" + strSql.Trim() + "' and fctlid_1 ='" + fctlid_a.Trim() + "'";
            int i = operate.SelectData(str);
            if (i > 0)
            { return true; }
            else { return false; }
        }
        private void textBox2_TextChanged(object sender, EventArgs e)
        {
            dataGridView2["Name", row].Value = textBox2.Text;
        }

        private void textBox3_TextChanged(object sender, EventArgs e)
        {
            dataGridView2["Position", row].Value = textBox3.Text;
        }

        private void textBox4_TextChanged(object sender, EventArgs e)
        {
            dataGridView2["Tel No.", row].Value = textBox4.Text;
        }

        private void textBox5_TextChanged(object sender, EventArgs e)
        {
            dataGridView2["Fax", row].Value = textBox5.Text;
        }

        private void textBox6_TextChanged(object sender, EventArgs e)
        {
            dataGridView2["Email", row].Value = textBox6.Text;
        }

        void saveContact()
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            DataTable dt = dataGridView2.DataSource as DataTable;
            if (dt != null)
            {

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    if (!checknewcontact(dt.Rows[i]["fctlid"].ToString().Trim()))
                    {
                        string str = "select * from xsysparm where fidtype ='CNTID'";
                        DataTable dt1 = new DataTable();
                        dt1 = operate.GetTable(str);
                        string fctlid_contact = dt1.Rows[0]["fnxtid"].ToString();

                        if (dt.Rows[i]["fctlid"].ToString().Trim() != "")
                        {
                            string adddatestrc =
                                    "insert into mcontact(fctlid_1,fctlid,fname,fpost,ftel,ffax,femail,fctldel) " +
                            "values('" + fctlid_a + "','" + fctlid_contact + "','" + dt.Rows[i]["Name"].ToString().Trim() + "','" + dt.Rows[i]["Position"].ToString().Trim() + "','" + dt.Rows[i]["Tel No."].ToString().Trim() + "','" + dt.Rows[i]["Fax"].ToString().Trim() + "','" + dt.Rows[i]["Email"].ToString().Trim() + "','')";
                            operate.OperateData(adddatestrc);

                            string updatestrc = "update xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='CNTID'";
                            operate.OperateData(updatestrc);
                        }
                    }
                    else
                    {
                        string updatedatestro =
                               "update mcontact set fname ='" + dt.Rows[i]["Name"].ToString().Trim() + "',fpost ='" + dt.Rows[i]["Position"].ToString().Trim() + "',ftel ='" + dt.Rows[i]["Tel No."].ToString().Trim() + "',ffax ='" + dt.Rows[i]["Fax"].ToString().Trim() + "',femail ='" + dt.Rows[i]["Email"].ToString().Trim() + "' where fctlid='" + dt.Rows[i]["fctlid"].ToString().Trim() + "'";
                        operate.OperateData(updatedatestro);
                    }
                }
            }
        }

        private void textBox7_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (textBox7.Text == "")
                {
                    dataGridView3["Seq#", dataGridView3.CurrentCell.RowIndex].Value = i;
                    textBox7.Text = i.ToString();
                }
                else
                {
                    Regex r = new Regex("^[0-9]{1,}$");
                    if (!r.IsMatch(textBox7.Text))
                    {
                        MessageBox.Show("请输入数字");
                    }
                    dataGridView3["Seq#", row1].Value = int.Parse(textBox7.Text.ToString());
                }
            }
            catch { }
        }

        private void textBox8_TextChanged(object sender, EventArgs e)
        {
            dataGridView3["Old Name", row1].Value = textBox8.Text;
        }

        private void textBox9_TextChanged(object sender, EventArgs e)
        {
            dataGridView3["frepdate", row1].Value = textBox9.Text;
        }

        void saveOld()
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            DataTable dt = dataGridView3.DataSource as DataTable;
            if (dt != null)
            {

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    DateTime time = DateTime.Now;
                    if (!checknewold(dt.Rows[i]["fctlid"].ToString().Trim()))
                    {
                        string str = "select * from xsysparm where fidtype ='MNAMHIST'";
                        DataTable dt1 = new DataTable();
                        dt1 = operate.GetTable(str);
                        string fctlid_old = dt1.Rows[0]["fnxtid"].ToString();

                        if (dt.Rows[i]["fctlid"].ToString().Trim() != "")
                        {
                            string adddatestrc =
                                    "insert into mnamhist(fctlid,fctlid_1,flogseq,fdesc,frepdate) " +
                            "values('" + fctlid_old + "','" + fctlid_a + "','" + dt.Rows[i]["Seq#"].ToString().Trim() + "','" + dt.Rows[i]["Old Name"].ToString().Trim() + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "')";
                            operate.OperateData(adddatestrc);

                            string updatestrc = "update xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='MNAMHIST'";
                            operate.OperateData(updatestrc);
                        }
                    }
                    else
                    {
                        string updatedatestro =
                              "update mnamhist set flogseq ='" + dt.Rows[i]["Seq#"].ToString().Trim() + "',fdesc ='" + dt.Rows[i]["Old Name"].ToString().Trim() + "' where fctlid='" + dt.Rows[i]["fctlid"].ToString().Trim() + "'";
                        operate.OperateData(updatedatestro);
                    }
                }
            }
        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", "");
        }

    }
}

