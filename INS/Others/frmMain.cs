using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using INS.INSClass;

namespace INS
{
    public partial class frmMain : Form
    {
        public frmMain()
        {
            InitializeComponent();
        }
        public string User = InsEnvironment.LoginUser.GetUserCode();
        public string Logintime;
        DBConnect operate = new DBConnect();
        private void frmMain_Load(object sender, EventArgs e)
        {

            if (User == "")
            {
                Frm_Demo Convert = new Frm_Demo();
                frmLogin Login = new frmLogin();
                Login.ShowDialog();
            }
            else {
                toolStripStatusLabel2.Text = User;
                string sql = "select * from tb_User where UserName='" + User + "'";
                DataTable dt = operate.GetTable(sql);
                string Power = dt.Rows[0]["UserName"].ToString();
                //MessageBox.Show(power);
                if (User != "anna")
                {
                    systemMaintainToolStripMenuItem.Enabled = false;
                }
                if (Power == "0")
                {
                    ToolStripMenuItem.Enabled = false;
                    ToolStripMenuItem.Enabled = false;
                }
            }
        }


        private void changePasswordToolStripMenuItem_Click_1(object sender, EventArgs e)
        {
            frmChangePwd ChangePwd = new frmChangePwd();
            ChangePwd.name = User;
            ChangePwd.ShowDialog();
        }


        private void frmMain_FormClosed(object sender, FormClosedEventArgs e)
        {
            Application.Exit();
        }

        private void logoutToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Application.Exit();
            User = "";
        }

        private void attachmentToolStripMenuItem_Click(object sender, EventArgs e)
        {
            frmAtt Attachment = new frmAtt();
            Attachment.ShowDialog();
        }

        private void addUserToolStripMenuItem_Click(object sender, EventArgs e)
        {
            frmAddUser AddUser = new frmAddUser();
            AddUser.ShowDialog();
        }

        private void excClausesToolStripMenuItem_Click(object sender, EventArgs e)
        {
            frmExcessClause ExcessClause = new frmExcessClause();
            ExcessClause.ShowDialog();
        }

        private void siteToolStripMenuItem_Click(object sender, EventArgs e)
        {
            frmSite site = new frmSite();
            site.ShowDialog();
        }

        private void projectToolStripMenuItem_Click(object sender, EventArgs e)
        {
            frmProject project = new frmProject();
            project.ShowDialog();
        }

        private void lossAdjustToolStripMenuItem_Click(object sender, EventArgs e)
        {
            //frmLossAdjuster Loss = new frmLossAdjuster();
            //Loss.ShowDialog();
        }

        private void solicitorMasterToolStripMenuItem_Click(object sender, EventArgs e)
        {
            frmSolicitor Solicitor = new frmSolicitor();
            Solicitor.ShowDialog();
        }

        private void healthCareToolStripMenuItem_Click(object sender, EventArgs e)
        {
            frmHealth Health = new frmHealth();
            Health.ShowDialog();
        }

        private void natureOfLossToolStripMenuItem_Click(object sender, EventArgs e)
        {
            frmNatureLoss NatureLoss = new frmNatureLoss();
            NatureLoss.ShowDialog();
        }

        private void subContractorToolStripMenuItem_Click(object sender, EventArgs e)
        {
            frmSubContractor SubContractor = new frmSubContractor();
            SubContractor.ShowDialog();
        }

        private void accidentTypeToolStripMenuItem_Click(object sender, EventArgs e)
        {
            frmAccident Accident = new frmAccident();
            Accident.ShowDialog();
        }

        private void jobNatureToolStripMenuItem_Click(object sender, EventArgs e)
        {
            frmJobNature JobNature = new frmJobNature();
            JobNature.ShowDialog();
        }

        private void selectPolicyToolStripMenuItem_Click(object sender, EventArgs e)
        {
            frmSelectPolicy SelectPolicy = new frmSelectPolicy();
            SelectPolicy.ShowDialog();
        }

        private void producerToolStripMenuItem_Click(object sender, EventArgs e)
        {
            frmProducer Producer = new frmProducer();
            Producer.ShowDialog();
        }

        private void cilentToolStripMenuItem_Click(object sender, EventArgs e)
        {

        }

        private void rIBrokerToolStripMenuItem_Click(object sender, EventArgs e)
        {
            frmBroker Broker = new frmBroker();
            Broker.ShowDialog();
        }

        private void reinsurerToolStripMenuItem_Click(object sender, EventArgs e)
        {
            frmReinsurer Reinsurer = new frmReinsurer();
            Reinsurer.ShowDialog();
        }

        private void iNSClassToolStripMenuItem_Click(object sender, EventArgs e)
        {
            frmInsClass InsClass = new frmInsClass();
            InsClass.ShowDialog();
        }


    }
}
