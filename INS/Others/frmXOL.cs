using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using System.Globalization;
using System.Windows.Documents;
using System.Text.RegularExpressions;

namespace INS
{
    public partial class frmXOL : Form
    {
        List<string> list = new List<string>();
        List<string> list1 = new List<string>();

        public frmXOL()
        {
            InitializeComponent();
            InitCombobox();
            FillData("", "");
        }


        private DataTable dataSource = new DataTable();
        private DataTable dataSource1 = new DataTable();
        public int i = 0, counter;
        DES des = new DES();
        DBConnect operate = new DBConnect();
        ExportDBF DBF = new ExportDBF();
        XLS txt = new XLS();
        public string fctlid_a = "";
        public string fid_layer = "";
        public String user = InsEnvironment.LoginUser.GetUserCode();
        public int row = 0;
        public int row1 = 0;
        public Boolean flag = false;
        public Boolean ADD = false;
        public Boolean layerAdd = false;
        public Boolean flag1 = false;
        private bool datagridviewSelectionChanged = true;
        private bool datagridview2SelectionChanged = true;
        private bool datagridview3SelectionChanged = true;

        void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            try
            {
                if (order != "" && query != "")
                {
                    if (order == "TTY#")
                    {
                        a = new SqlDataAdapter("select fctlid,fid as TTY#,fdesc as Desc#,fefffr as [Eff From] ,feffto as [Eff To],fcur  " +
    " from mxl where fid like '%" + query.ToUpper() + "%' order by " + order, c);
                    }
                    else
                    {
                        a = new SqlDataAdapter("select fctlid,fid as TTY#,fdesc as Desc#,fefffr as [Eff From] ,feffto as [Eff To],fcur  " +
" from mxl where fdesc like '%" + query.ToUpper() + "%' order by " + order, c);
                    }
                }
                else if (order != "" && query == "")
                {
                    a = new SqlDataAdapter("select fctlid,fid as TTY#,fdesc as Desc#,fefffr as [Eff From] ,feffto as [Eff To],fcur  " +
    " from mxl order by " + order, c);
                }
                else
                {
                    a = new SqlDataAdapter("select fctlid,fid as TTY#,fdesc as Desc#,fefffr as [Eff From] ,feffto as [Eff To],fcur  " +
    " from mxl order by fid ", c);

                }
                DataTable t = new DataTable();
                a.Fill(t);
                dataGridView1.DataSource = t;
                this.dataGridView1.Columns[0].Visible = false;
                this.dataGridView1.Columns[4].Visible = false;
                this.dataGridView1.Columns[5].Visible = false;
            }
            catch { }
            c.Close();
        }

        private void dataGridView1_SelectedIndexChanged(object sender, System.EventArgs e)
        {

            if (!this.datagridviewSelectionChanged) return;
            try
            {
                if (flag == false && dataGridView1.CurrentCell != null)
                {
                    counter = dataGridView1.CurrentCell.RowIndex;
                }
                if (dataGridView1.Rows[counter].Cells["TTY#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["TTY#"].Value.ToString().Length != 0)
                    {
                        textBox8.Text = dataGridView1.Rows[counter].Cells["TTY#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        textBox9.Text = dataGridView1.Rows[counter].Cells["Desc#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Eff From"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Eff From"].Value.ToString().Length != 0)
                    {
                        try
                        {
                            string a = dataGridView1.Rows[counter].Cells["Eff From"].Value.ToString();
                            DateTime b = Convert.ToDateTime(a);
                            textBox10.Text = b.ToString("yyyy.MM.dd");
                        }
                        catch { }
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Eff To"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Eff To"].Value.ToString().Length != 0)
                    {
                        try
                        {
                            string a = dataGridView1.Rows[counter].Cells["Eff To"].Value.ToString();
                            DateTime b = Convert.ToDateTime(a);
                            textBox11.Text = b.ToString("yyyy.MM.dd");
                        }
                        catch { }
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fcur"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fcur"].Value.ToString().Length != 0)
                    {
                        comboBox3.SelectedItem = dataGridView1.Rows[counter].Cells["fcur"].Value.ToString();
                    }
                }
                if (ADD == false)
                {
                    fctlid_a = dataGridView1.Rows[counter].Cells["fctlid"].Value.ToString();
                    loadMXLLAYR();
                    reloadgridview2("");
                    loadMXLLAYR1();
                    reloadgridview3("");
                    mxlclass();
                }
            }
            catch { }
        }

        void reloadgridview1(string fid, string lab)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select fctlid,fid as TTY#,fdesc as Desc#,fefffr as [Eff From] ,feffto as [Eff To],fcur from mxl order by fid ", c))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                if (lab == "insert" || lab == "update")
                {
                    dataGridView1.DataSource = t2;
                    if (fid != "")
                    {
                        for (int i = 0; i < t2.Rows.Count; i++)
                        {
                            if (t2.Rows[i]["TTY#"].ToString().Trim() == fid.Trim())
                            {
                                counter = i;
                                dataGridView1.CurrentCell = dataGridView1.Rows[i].Cells["TTY#"];
                                break;
                            }
                        }
                    }
                }
                if (lab == "del")
                {
                    datagridviewSelectionChanged = false;
                    dataGridView1.DataSource = t2;
                    datagridviewSelectionChanged = true;
                    if (counter == 0)
                    {
                        dataGridView1.CurrentCell = dataGridView1.Rows[counter].Cells["Desc#"];
                    }
                    else
                    {
                        dataGridView1.CurrentCell = dataGridView1.Rows[counter - 1].Cells["Desc#"];
                    }
                }
                if (lab == "cancel")
                {
                    datagridviewSelectionChanged = false;
                    dataGridView1.DataSource = t2;
                    datagridviewSelectionChanged = true;
                    if (counter >= t2.Rows.Count)
                    {
                        dataGridView1.CurrentCell = dataGridView1.Rows[counter - 1].Cells["Desc#"];
                    }
                    else { dataGridView1.CurrentCell = dataGridView1.Rows[counter].Cells["Desc#"]; }
                }
                if (lab == "")
                {
                    if (t2.Rows.Count != 0)
                    {
                        dataGridView1.DataSource = t2;
                        dataGridView1.CurrentCell = dataGridView1.Rows[counter].Cells["TTY#"];
                    }
                }
            }
            dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            datagridviewSelectionChanged = true;
            c.Close();
        }


        void reloadgridview2(string fid)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select a.fctlid, a.flogseq as Seq#, freinsr as Reinsurer,fshare as [Share (%)], fbrkr1, " +
                                "b.fdesc as Bdesc,c.fdesc as Rdesc, a.fctlid_1 from mxlinsr a " +
                                "left join (select fid,fdesc  from mprdr where ftype='B') b on a.fbrkr1=b.fid " +
                                "left join (select fid,fdesc from mprdr where ftype='R') c on a.freinsr=c.fid " +
                                "where fctlid_1 = '" + fctlid_a + "' and fctlid_2 = '" + fid_layer + "' ORDER BY flogseq", c))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                dataGridView2.DataSource = null;
                dataGridView2.DataSource = t2;
                if (t2.Rows.Count == 0)
                {
                    textBox12.Text = "";
                    textBox13.Text = "";
                    textBox14.Text = "";
                    textBox15.Text = "";
                    textBox16.Text = "";
                    textBox17.Text = "";
                }
                else
                {
                    dataGridView2.CurrentCell = dataGridView2.Rows[row].Cells["Seq#"];
                }
                this.dataGridView2.Columns[0].Visible = false;
                this.dataGridView2.Columns[4].Visible = false;
                this.dataGridView2.Columns[5].Visible = false;
                this.dataGridView2.Columns[6].Visible = false;
                this.dataGridView2.Columns[7].Visible = false;

                Decimal sum = 0;
                for (int i = 0; i < t2.Rows.Count; i++)
                {
                    if (t2.Rows[i]["Share (%)"].ToString().Trim() != "")
                    {
                        sum = sum + Convert.ToDecimal(t2.Rows[i]["Share (%)"].ToString().Trim());
                    }
                }
                textBox18.Text = sum.ToString();
                textBox18.Text = string.Format("{0:N4}", Convert.ToDecimal(textBox18.Text));
            }
            datagridview2SelectionChanged = true;
            flag = false;
            c.Close();

        }

        void reloadgridview3(string fid)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a1 = new SqlDataAdapter("select fid as Item#, flimit as [Loss Limit], fexcess as [Excess Point], fctlid, fctlid_1,fctlid_2 " +
  "from mxllayrb where fctlid_1 = '" + fctlid_a + "' and fctlid_2 = '" + fid_layer + "' ORDER BY fid", c))
            {
                DataTable t3 = new DataTable();
                a1.Fill(t3);
                dataGridView3.DataSource = null;
                dataGridView3.DataSource = t3;
                if (t3.Rows.Count == 0)
                {
                    textBox2.Text = "";
                    textBox3.Text = "";
                    textBox4.Text = "";
                    textBox5.Text = "";
                    textBox6.Text = "";
                    textBox7.Text = "";
                }
                else
                {
                    dataGridView3.CurrentCell = dataGridView3.Rows[row1].Cells["Item#"];
                }
                this.dataGridView3.Columns[3].Visible = false;
                this.dataGridView3.Columns[4].Visible = false;
                this.dataGridView3.Columns[5].Visible = false;

            }
            datagridview3SelectionChanged = true;
            flag1 = false;
            c.Close();

        }

        void loadMXLLAYR()
        {
            string str = "select fid,fctlid from MXLLAYR where fctlid_1 ='" + fctlid_a + "'";
            DataTable dt = new DataTable();
            dt = operate.GetTable(str);

            comboBox2.DisplayMember = "fid";
            comboBox2.ValueMember = "fctlid";
            comboBox2.DataSource = dt;

        }

        void loadMXLLAYR1()
        {
            string str = "select fid,fctlid from MXLLAYR where fctlid_1 ='" + fctlid_a + "'";
            DataTable dt = new DataTable();
            dt = operate.GetTable(str);
            comboBox4.DataSource = dt;
            comboBox4.DisplayMember = "fid";
            comboBox4.ValueMember = "fctlid";

        }

        private void comboBox2_SelectedValueChanged(object sender, EventArgs e)
        {
            if ((comboBox2.SelectedValue != null) && (comboBox2.SelectedValue.ToString() != "0"))
            {
                fid_layer = comboBox2.SelectedValue.ToString();
                string str = "select * from MXLLAYR where fctlid_1 ='" + fctlid_a + "' and fctlid ='" + fid_layer + "' ";
                DataTable dt = new DataTable();
                dt = operate.GetTable(str);
                if (dt != null)
                {
                    textBox5.Text = dt.Rows[0]["fminpm"].ToString();
                    textBox19.Text = dt.Rows[0]["fdeppm"].ToString();
                    textBox6.Text = dt.Rows[0]["fprate"].ToString();
                    textBox20.Text = dt.Rows[0]["fadjrate"].ToString();
                    textBox7.Text = dt.Rows[0]["fdrate"].ToString();
                    textBox21.Text = dt.Rows[0]["flimit"].ToString();
                    reloadgridview2("");
                }
            }
        }


        private void comboBox4_SelectedValueChanged(object sender, EventArgs e)
        {
            if ((comboBox4.SelectedValue != null) && (comboBox4.SelectedValue.ToString() != "0"))
            {
                fid_layer = comboBox4.SelectedValue.ToString();
                reloadgridview3("");
                mxlclass();
            }
        }

        private void dataGridView2_SelectionChanged(object sender, EventArgs e)
        {
            if (!this.datagridview2SelectionChanged) return;
            try
            {
                if (flag == false && dataGridView2.CurrentCell != null)
                {
                    row = dataGridView2.CurrentCell.RowIndex;
                }

                if (dataGridView2.Rows[row].Cells["Seq#"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["Seq#"].Value.ToString().Length != 0)
                    {
                        textBox12.Text = dataGridView2.Rows[row].Cells["Seq#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView2.Rows[row].Cells["fbrkr1"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["fbrkr1"].Value.ToString().Length != 0)
                    {
                        textBox13.Text = dataGridView2.Rows[row].Cells["fbrkr1"].Value.ToString().Trim();
                    }
                }

                if (dataGridView2.Rows[row].Cells["Bdesc"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["Bdesc"].Value.ToString().Length != 0)
                    {
                        textBox14.Text = dataGridView2.Rows[row].Cells["Bdesc"].Value.ToString().Trim();
                    }
                }

                if (dataGridView2.Rows[row].Cells["Reinsurer"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["Reinsurer"].Value.ToString().Length != 0)
                    {
                        textBox15.Text = dataGridView2.Rows[row].Cells["Reinsurer"].Value.ToString().Trim();
                    }
                }

                if (dataGridView2.Rows[row].Cells["Rdesc"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["Rdesc"].Value.ToString().Length != 0)
                    {
                        textBox16.Text = dataGridView2.Rows[row].Cells["Rdesc"].Value.ToString().Trim();
                    }
                }
                if (dataGridView2.Rows[row].Cells["Share (%)"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["Share (%)"].Value.ToString().Length != 0)
                    {
                        textBox17.Text = dataGridView2.Rows[row].Cells["Share (%)"].Value.ToString().Trim();
                        textBox17.Text = string.Format("{0:N4}", Convert.ToDecimal(textBox17.Text));
                    }
                }
                if (dataGridView2.Rows[row].Cells["fctlid"].Value != null)
                {
                    fctlidlabelinfo.Text = dataGridView2.Rows[row].Cells["fctlid"].Value.ToString();
                }
                rowlabelinfo.Text = row.ToString();
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void dataGridView3_SelectionChanged(object sender, EventArgs e)
        {
            if (!this.datagridview3SelectionChanged) return;
            try
            {
                if (flag1 == false && dataGridView3.CurrentCell != null)
                {
                    row1 = dataGridView3.CurrentCell.RowIndex;
                }

                if (dataGridView3.Rows[row1].Cells["Item#"].Value != null)
                {
                    if (dataGridView3.Rows[row1].Cells["Item#"].Value.ToString().Length != 0)
                    {
                        textBox2.Text = dataGridView3.Rows[row1].Cells["Item#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView3.Rows[row1].Cells["Loss Limit"].Value != null)
                {
                    if (dataGridView3.Rows[row1].Cells["Loss Limit"].Value.ToString().Length != 0)
                    {
                        textBox3.Text = dataGridView3.Rows[row1].Cells["Loss Limit"].Value.ToString().Trim();
                    }
                }

                if (dataGridView3.Rows[row1].Cells["Excess Point"].Value != null)
                {
                    if (dataGridView3.Rows[row1].Cells["Excess Point"].Value.ToString().Length != 0)
                    {
                        textBox4.Text = dataGridView3.Rows[row1].Cells["Excess Point"].Value.ToString().Trim();
                    }
                }
                if (dataGridView3.Rows[row1].Cells["fctlid"].Value != null)
                {
                    fctlidlabelcontact.Text = dataGridView3.Rows[row1].Cells["fctlid"].Value.ToString();
                }
                rowlabelcontact.Text = row1.ToString();
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        void mxlclass()
        {
            list.Clear();
            list1.Clear();
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select fdesc from mxlclass where fctlid_1 ='" + fctlid_a + "' and fctlid_2 ='" + fid_layer + "' and fctlid_3 ='" + fctlidlabelcontact.Text.ToString().Trim() + "' and ftype=1", c))
            {
                DataTable dt1 = new DataTable();
                a.Fill(dt1);
                for (int i = 0; i < dt1.Rows.Count; i++)
                {
                    list.Add(dt1.Rows[i]["fdesc"].ToString().Trim());
                }
            }

            using (SqlDataAdapter a = new SqlDataAdapter("select fdesc from mxlclass where fctlid_1 ='" + fctlid_a + "' and fctlid_2 ='" + fid_layer + "' and fctlid_3 ='" + fctlidlabelcontact.Text.ToString().Trim() + "' and ftype=2", c))
            {
                DataTable dt2 = new DataTable();
                a.Fill(dt2);
                for (int i = 0; i < dt2.Rows.Count; i++)
                {
                    list1.Add(dt2.Rows[i]["fdesc"].ToString().Trim());
                }
            }
            listBox1.DataSource = null;
            listBox2.DataSource = null;
            listBox1.DataSource = list;
            listBox2.DataSource = list1;

            c.Close();
        }


        private void button1_Click(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", textBox1.Text.ToString().Trim());
        }

        public enum Mode
        {
            TTY = 1,
            Desc = 2
        }

        private void InitCombobox()
        {
            Array arr = System.Enum.GetValues(typeof(Mode));    // 获取枚举的所有值
            DataTable dt = new DataTable();
            dt.Columns.Add("String", Type.GetType("System.String"));
            dt.Columns.Add("Value", typeof(int));
            foreach (var a in arr)
            {
                string strText = EnumTextByDescription.GetEnumDesc((Mode)a);
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = (int)a;

                dt.Rows.Add(aRow);
            }

            comboBox1.DataSource = dt;
            comboBox1.DisplayMember = "String";
            comboBox1.ValueMember = "Value";
        }


        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            if (ADD == true)
            {
                if (e.TabPage == Classes)
                    e.Cancel = true;
            }
        }

        void ButtonControlback()
        {
            ADD = false;
            layerAdd = false;
            textBox5.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox5.ReadOnly = true;
            textBox6.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox6.ReadOnly = true;
            textBox7.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox7.ReadOnly = true;
            textBox19.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox19.ReadOnly = true;
            textBox20.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox20.ReadOnly = true;
            textBox21.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox21.ReadOnly = true;
            textBox8.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox8.ReadOnly = true;
            textBox9.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox9.ReadOnly = true;
            textBox10.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox10.ReadOnly = true;
            textBox11.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox11.ReadOnly = true;
            comboBox3.BackColor = System.Drawing.SystemColors.InactiveCaption;
            comboBox3.Enabled = false;

            textBox12.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox12.ReadOnly = true;
            textBox13.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox13.ReadOnly = true;
            textBox15.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox15.ReadOnly = true;
            textBox17.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox17.ReadOnly = true;
            textBox2.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox2.ReadOnly = true;
            textBox3.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox3.ReadOnly = true;
            textBox4.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox4.ReadOnly = true;

            dcancel.Visible = false;
            dsave.Visible = false;
            dexit.Visible = true;
            dadd.Visible = true;
            dupdate.Visible = true;
            ddel.Visible = true;
            csave.Visible = false;
            cCancel.Visible = false;
            cadd.Visible = true;
            cdel.Visible = true;
            cupdate.Visible = true;
            cexit.Visible = true;
            add1.Visible = false;
            add2.Visible = false;
            add3.Visible = false;
            del1.Visible = false;
            del2.Visible = false;
            del3.Visible = false;
            button4.Enabled = false;
            button5.Enabled = false;
            button2.Enabled = false;
            button3.Enabled = false;
            button6.Enabled = false;
            button7.Enabled = false;
            comboBox2.Enabled = true;
        }

        void ButtonControl()
        {
            textBox8.BackColor = System.Drawing.SystemColors.Window;
            textBox8.ReadOnly = false;
            textBox9.BackColor = System.Drawing.SystemColors.Window;
            textBox9.ReadOnly = false;
            textBox10.BackColor = System.Drawing.SystemColors.Window;
            textBox10.ReadOnly = false;
            textBox11.BackColor = System.Drawing.SystemColors.Window;
            textBox11.ReadOnly = false;
            comboBox3.BackColor = System.Drawing.SystemColors.Window;
            comboBox3.Enabled = true;
            dcancel.Visible = true;
            dsave.Visible = true;
            dexit.Visible = false;
            dadd.Visible = false;
            dupdate.Visible = false;
            ddel.Visible = false;
            csave.Visible = true;
            cCancel.Visible = true;
            cadd.Visible = false;
            cdel.Visible = false;
            cupdate.Visible = false;
            cexit.Visible = false;
            add1.Visible = true;
            add2.Visible = true;
            add3.Visible = true;
            del3.Visible = true;
            del1.Visible = true;
            del2.Visible = true;
            button4.Enabled = true;
            button5.Enabled = true;
            button2.Enabled = true;
            button3.Enabled = true;
            button6.Enabled = true;
            button7.Enabled = true;

        }

        void ButtonControladd()
        {
            ADD = true;
            DataTable listdataSource = dataGridView1.DataSource as DataTable;
            DataRow newCustomersRow = listdataSource.NewRow();
            listdataSource.Rows.InsertAt(newCustomersRow, counter);
            dataGridView1.DataSource = listdataSource;
            dataGridView1.CurrentCell = dataGridView1.Rows[counter - 1].Cells["TTY#"];
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);
            layerAdd = true;
            textBox5.Text = "";
            textBox6.Text = "";
            textBox7.Text = "";
            textBox19.Text = "";
            textBox20.Text = "";
            textBox21.Text = "";
            textBox8.Text = "";
            textBox9.Text = "";
            textBox10.Text = "";
            textBox11.Text = "";
            textBox12.Text = "";
            textBox13.Text = "";
            textBox14.Text = "";
            textBox15.Text = "";
            textBox16.Text = "";
            textBox17.Text = "";
            textBox2.Text = "";
            textBox3.Text = "";
            textBox4.Text = "";
            if (listBox1.Items.Count.ToString() == "0" && listBox2.Items.Count.ToString() == "0")
            {
                list.Clear();
                SqlConnection c = DBConnect.dbConn;
                if (c.State == ConnectionState.Closed)
                { c.Open(); }

                using (SqlDataAdapter a = new SqlDataAdapter("select distinct fdesc from mxlclass where ftype=1", c))
                {
                    DataTable dt1 = new DataTable();
                    a.Fill(dt1);
                    for (int i = 0; i < dt1.Rows.Count; i++)
                    {
                        list.Add(dt1.Rows[i]["fdesc"].ToString().Trim());
                    }
                }

                listBox1.DataSource = null;
                listBox1.DataSource = list;

                c.Close();

            }
        }

        void ButtonCountrolupdate()
        {
            textBox5.BackColor = System.Drawing.SystemColors.Window;
            textBox5.ReadOnly = false;
            textBox6.BackColor = System.Drawing.SystemColors.Window;
            textBox6.ReadOnly = false;
            textBox7.BackColor = System.Drawing.SystemColors.Window;
            textBox7.ReadOnly = false;
            textBox19.BackColor = System.Drawing.SystemColors.Window;
            textBox19.ReadOnly = false;
            textBox20.BackColor = System.Drawing.SystemColors.Window;
            textBox20.ReadOnly = false;
            textBox21.BackColor = System.Drawing.SystemColors.Window;
            textBox21.ReadOnly = false;
            textBox12.BackColor = System.Drawing.SystemColors.Window;
            textBox12.ReadOnly = false;
            textBox13.BackColor = System.Drawing.SystemColors.Window;
            textBox13.ReadOnly = false;
            textBox15.BackColor = System.Drawing.SystemColors.Window;
            textBox15.ReadOnly = false;
            textBox17.BackColor = System.Drawing.SystemColors.Window;
            textBox17.ReadOnly = false;
            textBox2.BackColor = System.Drawing.SystemColors.Window;
            textBox2.ReadOnly = false;
            textBox3.BackColor = System.Drawing.SystemColors.Window;
            textBox3.ReadOnly = false;
            textBox4.BackColor = System.Drawing.SystemColors.Window;
            textBox4.ReadOnly = false;
            if (listBox1.Items.Count.ToString() == "0" && listBox2.Items.Count.ToString() == "0")
            {
                list.Clear();
                SqlConnection c = DBConnect.dbConn;
                if (c.State == ConnectionState.Closed)
                { c.Open(); }

                using (SqlDataAdapter a = new SqlDataAdapter("select distinct fdesc from mxlclass where ftype=1", c))
                {
                    DataTable dt1 = new DataTable();
                    a.Fill(dt1);
                    for (int i = 0; i < dt1.Rows.Count; i++)
                    {
                        list.Add(dt1.Rows[i]["fdesc"].ToString().Trim());
                    }
                }

                listBox1.DataSource = null;
                listBox1.DataSource = list;

                c.Close();

            }
        }

        private void button9_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void dexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void cexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void dcancel_Click(object sender, EventArgs e)
        {
            ButtonControlback();
            reload();
            mxlclass();
            loadMXLLAYR();
            loadMXLLAYR1();
            reloadgridview1("", "cancel");
        }

        private void cCancel_Click(object sender, EventArgs e)
        {
            ButtonControlback();
            reload();
            mxlclass();
            loadMXLLAYR();
            loadMXLLAYR1();
            reloadgridview1("", "cancel");
        }

        private void lupdate_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonCountrolupdate();
        }

        private void dupdate_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonCountrolupdate();
        }

        private void cupdate_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonCountrolupdate();
        }

        private void ladd_Click(object sender, EventArgs e)
        {
            add();
        }

        private void dadd_Click(object sender, EventArgs e)
        {
            add();
        }

        private void cadd_Click(object sender, EventArgs e)
        {
            add();
        }

        void add()
        {
            tabControl1.SelectedTab = Clauses;

            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select a.fctlid, a.flogseq as Seq#, freinsr as Reinsurer,fshare as [Share (%)], fbrkr1, " +
                                "b.fdesc as Bdesc,c.fdesc as Rdesc, a.fctlid_1 from mxlinsr a " +
                                "left join (select fid,fdesc  from mprdr where ftype='B') b on a.fbrkr1=b.fid " +
                                "left join (select fid,fdesc from mprdr where ftype='R') c on a.freinsr=c.fid " +
                                "where fctlid_1 = '1' and fctlid_2 = '" + fid_layer + "' ORDER BY flogseq", c))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                dataGridView2.DataSource = t2;
                this.dataGridView2.Columns[0].Visible = false;
                this.dataGridView2.Columns[4].Visible = false;
                this.dataGridView2.Columns[5].Visible = false;
                this.dataGridView2.Columns[6].Visible = false;
                this.dataGridView2.Columns[7].Visible = false;
            }
            c.Close();

            SqlConnection c1 = DBConnect.dbConn;
            if (c1.State == ConnectionState.Closed)
            { c1.Open(); }

            using (SqlDataAdapter a1 = new SqlDataAdapter("select fid as Item#, flimit as [Loss Limit], fexcess as [Excess Point],fctlid, fctlid_1,fctlid_2 " +
  "from mxllayrb where fctlid_1 = '1' and fctlid_2 = '" + fid_layer + "' ORDER BY fid", c1))
            {
                DataTable t3 = new DataTable();
                a1.Fill(t3);
                dataGridView3.DataSource = t3;

                this.dataGridView3.Columns[3].Visible = false;
                this.dataGridView3.Columns[4].Visible = false;
                this.dataGridView3.Columns[5].Visible = false;
            }
            c1.Close();
            comboBox2.DataSource = null;
            DataTable dt = new DataTable();
            dt.Columns.Add("fctlid", typeof(int));
            dt.Columns.Add("fid");
            comboBox2.DisplayMember = "fid";
            comboBox2.ValueMember = "fctlid";
            comboBox2.DataSource = dt;
            comboBox4.DataSource = dt;
            DataRow dr = dt.NewRow();
            dr["fid"] = "01";
            dr["fctlid"] = 0;
            dt.Rows.InsertAt(dr, 0);
            comboBox2.SelectedIndex = 0;
            listBox1.DataSource = null;
            list.AddRange(list1);
            listBox1.DataSource = list;
            listBox2.DataSource = null;
            list1.Clear();
            ButtonControl();
            ButtonControladd();
        }

        private void ldel_Click(object sender, EventArgs e)
        {
            del();
        }

        private void ddel_Click(object sender, EventArgs e)
        {
            del();
        }

        private void cdel_Click(object sender, EventArgs e)
        {
            del();
        }

        void del()
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the ALL items?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string deldatestr1 =
                    "delete from mxl where fctlid='" + fctlid_a + "'";
                operate.OperateData(deldatestr1);
                string deldatestr =
                    "delete from mxlinsr where fctlid_1='" + fctlid_a + "'";
                operate.OperateData(deldatestr);
                string deldatestr3 =
                    "delete from MXLLAYR where fctlid_1='" + fctlid_a + "'";
                operate.OperateData(deldatestr3);
                string deldatestr2 =
                    "delete from mxllayrb where fctlid_1='" + fctlid_a + "'";
                operate.OperateData(deldatestr2);
                MessageBox.Show("Have Been Deleted", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                FillData("", "");
                tabControl1.SelectedTab = Classes;
            }

        }

        private void textBox8_TextChanged(object sender, EventArgs e)
        {
            dataGridView1.Rows[counter].Cells["TTY#"].Value = textBox8.Text;
            string result2 = CheckDuplicate(textBox8.Text);
            if (result2 != "")
            {
                MessageBox.Show(result2);
                textBox8.Text = "";
            }
        }

        public string CheckDuplicate(string fid)
        {
            if (fid != "")
            {
                DataTable listdataSource = dataGridView1.DataSource as DataTable;
                if (listdataSource.Rows.Count > 0)
                {
                    DataTable objectTable = listdataSource.DefaultView.ToTable();
                    DataRow[] rows = objectTable.Select("[TTY#]='" + fid.Trim() + "'");
                    if (rows.Length > 1)
                    {
                        return "Duplicate Value";
                    }
                    else { return ""; }
                }
                else { return ""; }
            }
            else { return ""; }
        }


        private void textBox17_TextChanged(object sender, EventArgs e)
        {
            if (textBox17.Text != "")
            {
                dataGridView2["Share (%)", row].Value = textBox17.Text.ToString().Replace(",", "");
                total();
            }
        }

        void total()
        {
            Decimal sum = 0;
            for (int i = 0; i < dataGridView2.Rows.Count; i++)
            {
                if (dataGridView2["Share (%)", i].Value.ToString() != "")
                {
                    sum = sum + Convert.ToDecimal(dataGridView2["Share (%)", i].Value);
                }
            }
            textBox18.Text = string.Format("{0:N0}", Convert.ToDecimal(sum.ToString().Replace(",", "")));
        }

        private void textBox3_TextChanged(object sender, EventArgs e)
        {
            if (textBox3.Text != "")
            {
                dataGridView3["Loss Limit", row1].Value = textBox3.Text.ToString().Replace(",", "");
            }
        }
        private void textBox4_TextChanged(object sender, EventArgs e)
        {
            if (textBox4.Text != "")
            {
                dataGridView3["Excess Point", row1].Value = textBox4.Text.ToString().Replace(",", "");
            }
        }
        private void textBox5_TextChanged(object sender, EventArgs e)
        {
            if (textBox5.Text != "")
            {
                textBox5.Text = string.Format("{0:#,0.####}", Convert.ToDecimal(textBox5.Text));
            }
        }
        private void textBox6_TextChanged(object sender, EventArgs e)
        {
            if (textBox6.Text != "")
            {
                textBox6.Text = string.Format("{0:N4}", Convert.ToDecimal(textBox6.Text));
            }
        }
        private void textBox7_TextChanged(object sender, EventArgs e)
        {
            if (textBox7.Text != "")
            {
                textBox7.Text = string.Format("{0:N4}", Convert.ToDecimal(textBox7.Text));
            }
        }
        private void textBox19_TextChanged(object sender, EventArgs e)
        {
            if (textBox19.Text != "")
            {
                textBox19.Text = string.Format("{0:#,0.####}", Convert.ToDecimal(textBox19.Text));
            }
        }
        private void textBox20_TextChanged(object sender, EventArgs e)
        {
            if (textBox20.Text != "")
            {
                textBox20.Text = string.Format("{0:N4}", Convert.ToDecimal(textBox20.Text));
            }
        }
        private void textBox21_TextChanged(object sender, EventArgs e)
        {
            if (textBox21.Text != "")
            {
                textBox21.Text = string.Format("{0:#,0.####}", Convert.ToDecimal(textBox21.Text));
            }
        }
        private void del1_Click(object sender, EventArgs e)
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the item?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string fctlid = fctlidlabelinfo.Text;
                string deldatestr =
                    "delete from mxlinsr where fctlid='" + fctlid + "'";
                operate.OperateData(deldatestr);
                MessageBox.Show("Have Been Deleted", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                row = 0;
                reloadgridview2("");

            }
        }

        private void del2_Click(object sender, EventArgs e)
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the item?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string fctlid = fctlidlabelcontact.Text;
                string deldatestr =
                    "delete from mxllayrb where fctlid='" + fctlid + "'";
                operate.OperateData(deldatestr);
                MessageBox.Show("Have Been Deleted", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                row1 = 0;
                reloadgridview3("");
            }
        }

        private void add1_Click(object sender, EventArgs e)
        {
            dataSource = dataGridView2.DataSource as DataTable;
            DataRow newCustomersRow = dataSource.NewRow();
            dataSource.Rows.InsertAt(newCustomersRow, dataSource.Rows.Count);
            dataGridView2.DataSource = dataSource;
            dataGridView2.CurrentCell = dataGridView2.Rows[dataSource.Rows.Count - 1].Cells["Seq#"];

            textBox12.BackColor = System.Drawing.SystemColors.Window;
            textBox12.ReadOnly = false;
            textBox12.Text = "";
            textBox13.BackColor = System.Drawing.SystemColors.Window;
            textBox13.ReadOnly = false;
            textBox13.Text = "";
            textBox14.Text = "";
            textBox16.Text = "";
            textBox15.BackColor = System.Drawing.SystemColors.Window;
            textBox15.ReadOnly = false;
            textBox15.Text = "";
            textBox17.BackColor = System.Drawing.SystemColors.Window;
            textBox17.ReadOnly = false;
            textBox17.Text = "";
        }

        private void add2_Click(object sender, EventArgs e)
        {
            dataSource1 = dataGridView3.DataSource as DataTable;
            DataRow newCustomersRow = dataSource1.NewRow();
            dataSource1.Rows.InsertAt(newCustomersRow, dataSource1.Rows.Count);
            dataGridView3.DataSource = dataSource1;
            dataGridView3.CurrentCell = dataGridView3.Rows[dataSource1.Rows.Count - 1].Cells["Item#"];

            if (layerAdd == false)
            {
                comboBox4.DataSource = null;
                string str = "select fid,fctlid from MXLLAYR where fctlid_1 ='" + fctlid_a + "'";
                DataTable dt = new DataTable();
                dt = operate.GetTable(str);

                comboBox4.DisplayMember = "fid";
                comboBox4.ValueMember = "fctlid";
                comboBox4.DataSource = dt;

                DataRow dr = dt.NewRow();
                dr["fid"] = int.Parse(dt.Rows[dt.Rows.Count - 1]["fid"].ToString()) + 1;
                dr["fctlid"] = 0;
                dt.Rows.InsertAt(dr, 0);
                comboBox4.SelectedValue = 0;
                comboBox4.Enabled = false;
            }
            fid_layer = "";

            textBox2.BackColor = System.Drawing.SystemColors.Window;
            textBox2.ReadOnly = false;
            textBox2.Text = "";
            textBox3.BackColor = System.Drawing.SystemColors.Window;
            textBox3.ReadOnly = false;
            textBox3.Text = "";
            textBox4.BackColor = System.Drawing.SystemColors.Window;
            textBox4.ReadOnly = false;
            textBox4.Text = "";
            listBox1.DataSource = null;
            list.AddRange(list1);
            listBox1.DataSource = list;
            listBox2.DataSource = null;
            list1.Clear();
        }

        private void dsave_Click(object sender, EventArgs e)
        {
            saveadd();
            ButtonControlback();
        }

        private void csave_Click(object sender, EventArgs e)
        {
            saveadd();
            ButtonControlback();
        }

        void saveadd()
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really Insert the item?", "Inserted Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                String fctlid_contact = fctlidlabelcontact.Text;
                String fctlid_info = fctlidlabelinfo.Text;

                if (ADD == true)
                {
                    string str = "select * from xsysparm where fidtype ='MXL'";
                    DataTable dt = new DataTable();
                    dt = operate.GetTable(str);
                    fctlid_a = dt.Rows[0]["fnxtid"].ToString();
                }

                if (!checknewlayer(fid_layer))
                {
                    string str = "select * from xsysparm where fidtype ='MXLLAYR'";
                    DataTable dt = new DataTable();
                    dt = operate.GetTable(str);
                    fid_layer = dt.Rows[0]["fnxtid"].ToString();
                }

                if (!checknewinfo(textBox12.Text))
                {
                    string str = "select * from xsysparm where fidtype ='MXLINSR'";
                    DataTable dt = new DataTable();
                    dt = operate.GetTable(str);
                    fctlid_info = dt.Rows[0]["fnxtid"].ToString();
                }

                if (!checknewsec(textBox2.Text))
                {
                    string str1 = "select * from xsysparm where fidtype ='MXLLAYRB'";
                    DataTable dt1 = new DataTable();
                    dt1 = operate.GetTable(str1);
                    fctlid_contact = dt1.Rows[0]["fnxtid"].ToString();
                }

                if (textBox8.Text.Trim() != "" && textBox9.Text.Trim() != "")
                {
                    if (ADD == true)
                    {
                        string adddatestr = "insert into mxl(fctlid,fid,fdesc,fbrkr,fefffr,feffto,fcur,fpbase,fgrnet,fctldel,finpuser,finpdate,fupduser,fupddate) values" +
                            "('" + fctlid_a + "','" + textBox8.Text.Trim().Replace("'", "''") + "','" + textBox9.Text.Trim().Replace("'", "''") + "','','" + textBox10.Text.Trim().Replace("'", "''") + "','" + textBox11.Text.Trim().Replace("'", "''") + "','" + comboBox3.SelectedItem.ToString().Trim().Replace("'", "''") + "','','','','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "')";
                        operate.OperateData(adddatestr);

                        string updatestr = "update xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='MXL'";
                        operate.OperateData(updatestr);
                    }
                    else
                    {
                        string updatedatestr =
                            "update mxl set fid ='" + textBox8.Text.Trim().Replace("'", "''") + "',fdesc ='" + textBox9.Text.Trim().Replace("'", "''") + "',fefffr ='" + textBox10.Text.Trim().Replace("'", "''") + "' ,feffto ='" + textBox11.Text.Trim().Replace("'", "''") + "' ,fcur ='" + comboBox3.SelectedItem.ToString().Trim().Replace("'", "''") + "', fupduser ='" + user + "',fupddate ='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid='" + fctlid_a + "'";
                        operate.OperateData(updatedatestr);
                    }

                    if (!checknewlayer(fid_layer))
                    {
                        string adddatestr = "insert into MXLLAYR(fctlid_1,fctlid,fid,fminpm,fdeppm,fprate,fadjrate,fdrate,flimit,ftshare) values" +
                            "('" + fctlid_a + "','" + fid_layer + "','" + ((DataRowView)comboBox2.SelectedItem)["fid"].ToString() + "','" + textBox5.Text.Trim().Replace(",", "") + "','" + textBox19.Text.Trim().Replace(",", "") + "','" + textBox6.Text.Trim().Replace(",", "") + "','" + textBox20.Text.Trim().Replace(",", "") + "','" + textBox7.Text.Trim().Replace(",", "") + "','" + textBox21.Text.Trim().Replace(",", "") + "','100.0000')";
                        operate.OperateData(adddatestr);

                        string updatestr = "update xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='MXLLAYR'";
                        operate.OperateData(updatestr);
                    }
                    else
                    {
                        string updatedatestr =
                            "update MXLLAYR set fminpm = cast('" + textBox5.Text.Trim().Replace(",", "") + "' as decimal(19,0)),fdeppm =cast('" + textBox19.Text.Trim().Replace(",", "") + "' as decimal(19,0)) ,fprate = cast('" + textBox6.Text.Trim().Replace(",", "") + "' as decimal(10,4)) ,fadjrate =cast('" + textBox20.Text.Trim().Replace(",", "") + "' as decimal(10,4)), fdrate = cast('" + textBox7.Text.Trim().Replace(",", "") + "' as decimal(10,4)),flimit =cast('" + textBox21.Text.Trim().Replace(",", "") + "' as decimal(19,0)) where fctlid='" + fid_layer + "'";
                        operate.OperateData(updatedatestr);
                    }

                    saveinfo();
                    saveContact();

                    MessageBox.Show("Have Been Inserted!", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    ButtonControl();
                    reloadgridview1(textBox8.Text.Trim(), "insert");
                }
                else
                {
                    MessageBox.Show("Some field is empty!", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        public Boolean checknew(string strSql)
        {
            string str =
                    "select count(*) from mxl where fid ='" + strSql.Trim() + "'";
            int i = operate.SelectData(str);
            if (i > 0)
            { return true; }
            else { return false; }
        }

        public Boolean checknewlayer(string strSql)
        {
            string str =
                    "select count(*) from MXLLAYR where fctlid ='" + strSql.Trim() + "'";
            int i = operate.SelectData(str);
            if (i > 0)
            { return true; }
            else { return false; }
        }

        public Boolean checknewinfo(string strSql)
        {
            string str =
                    "select count(*) from mxlinsr where fctlid ='" + strSql.Trim() + "' and fctlid_1 ='" + fctlid_a.Trim() + "' and fctlid_2 ='" + fid_layer.Trim() + "'";
            int i = operate.SelectData(str);
            if (i > 0)
            { return true; }
            else { return false; }
        }

        public Boolean checknewsec(string strSql)
        {
            string str =
                    "select count(*) from mxllayrb where fctlid ='" + strSql.Trim() + "' and fctlid_1 ='" + fctlid_a.Trim() + "' and fctlid_2 ='" + fid_layer.Trim() + "'";
            int i = operate.SelectData(str);
            if (i > 0)
            { return true; }
            else { return false; }
        }

        void reload()
        {
            string str = "select * from mxl where fctlid ='" + fctlid_a + "'";
            DataTable dt = new DataTable();
            dt = operate.GetTable(str);
            textBox8.Text = dt.Rows[0]["fid"].ToString();
            textBox9.Text = dt.Rows[0]["fdesc"].ToString();
            if (dt.Rows[0]["fefffr"].ToString() != "")
            {
                try
                {
                    string a = dt.Rows[0]["fefffr"].ToString();
                    DateTime b = Convert.ToDateTime(a);
                    textBox10.Text = b.ToString("yyyy.MM.dd");
                }
                catch { }
            }
            if (dt.Rows[0]["feffto"].ToString() != "")
            {
                try
                {
                    string a = dt.Rows[0]["feffto"].ToString();
                    DateTime b = Convert.ToDateTime(a);
                    textBox11.Text = b.ToString("yyyy.MM.dd");
                }
                catch { }
            }
            comboBox3.SelectedItem = dt.Rows[0]["fcur"].ToString();
        }


        private String _strValue;

        public String StrValue
        {
            set
            {
                _strValue = value;
            }
        }

        private String _strValuebn;

        public String StrValuebn
        {
            set
            {
                _strValuebn = value;
            }
        }
        private String _strValuer;

        public String StrValuer
        {
            set
            {
                _strValuer = value;
            }
        }

        private String _strValuern;

        public String StrValuern
        {
            set
            {
                _strValuern = value;
            }
        }

        public Boolean CloseFlag = false;

        private void button4_Click(object sender, EventArgs e)
        {
            frmBrkSearch temp_form = new frmBrkSearch();
            temp_form.Owner = this;
            temp_form.flag = "frmXOL";
            temp_form.ShowDialog();
            if (CloseFlag == false)
            {
                textBox13.Text = _strValue;
                textBox14.Text = _strValuebn;
            }

        }

        private void button5_Click(object sender, EventArgs e)
        {
            frmReinsSearch temp_form = new frmReinsSearch();
            temp_form.Owner = this;
            temp_form.flag = "frmXOL";
            temp_form.ShowDialog();
            if (CloseFlag == false)
            {
                textBox15.Text = _strValuer;
                textBox16.Text = _strValuern;
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (listBox1.SelectedIndex != -1)
            {
                int total = listBox1.SelectedItems.Count;
                for (int x = 0; x < total; x++)
                {
                    list.Remove(listBox1.SelectedItem.ToString());
                    list1.Add(listBox1.SelectedItem.ToString());
                }
            }
            listBox1.DataSource = null;
            listBox2.DataSource = null;
            listBox1.DataSource = list;
            listBox2.DataSource = list1;
        }

        private void button3_Click(object sender, EventArgs e)
        {
            listBox2.DataSource = null;
            list1.AddRange(list);
            listBox2.DataSource = list1;
            listBox1.DataSource = null;
            list.Clear();
        }

        private void button6_Click(object sender, EventArgs e)
        {
            if (listBox2.SelectedIndex != -1)
            {
                int total = listBox2.SelectedItems.Count;
                for (int x = 0; x < total; x++)
                {
                    list1.Remove(listBox2.SelectedItem.ToString());
                    list.Add(listBox2.SelectedItem.ToString());
                }
            }
            listBox1.DataSource = null;
            listBox2.DataSource = null;
            listBox1.DataSource = list;
            listBox2.DataSource = list1;
        }

        private void button7_Click(object sender, EventArgs e)
        {
            list.AddRange(list1);
            listBox1.DataSource = list;
            listBox2.DataSource = null;
            list1.Clear();
        }

        private void button8_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonCountrolupdate();
            textBox8.Text = "";
        }

        private void del3_Click(object sender, EventArgs e)
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the item?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string deldatestr =
                    "delete from MXLLAYR where fctlid='" + fid_layer + "'";
                operate.OperateData(deldatestr);
                MessageBox.Show("Have Been Deleted", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                loadMXLLAYR();
                reloadgridview2("");
            }
        }

        private void add3_Click(object sender, EventArgs e)
        {
            if (layerAdd == false)
            {
                comboBox2.DataSource = null;
                string str = "select fid,fctlid from MXLLAYR where fctlid_1 ='" + fctlid_a + "'";
                DataTable dt = new DataTable();
                dt = operate.GetTable(str);

                comboBox2.DisplayMember = "fid";
                comboBox2.ValueMember = "fctlid";
                comboBox2.DataSource = dt;

                DataRow dr = dt.NewRow();
                dr["fid"] = int.Parse(dt.Rows[dt.Rows.Count - 1]["fid"].ToString()) + 1;
                dr["fctlid"] = 0;
                dt.Rows.InsertAt(dr, 0);
                comboBox2.SelectedValue = 0;
                comboBox2.Enabled = false;
            }
            fid_layer = "";
            textBox5.BackColor = System.Drawing.SystemColors.Window;
            textBox5.ReadOnly = false;
            textBox5.Text = "";
            textBox6.BackColor = System.Drawing.SystemColors.Window;
            textBox6.ReadOnly = false;
            textBox6.Text = "";
            textBox7.BackColor = System.Drawing.SystemColors.Window;
            textBox7.ReadOnly = false;
            textBox7.Text = "";
            textBox19.BackColor = System.Drawing.SystemColors.Window;
            textBox19.ReadOnly = false;
            textBox19.Text = "";
            textBox20.BackColor = System.Drawing.SystemColors.Window;
            textBox20.ReadOnly = false;
            textBox20.Text = "";
            textBox21.BackColor = System.Drawing.SystemColors.Window;
            textBox21.ReadOnly = false;
            textBox21.Text = "";

        }

        private void textBox13_TextChanged(object sender, EventArgs e)
        {
            if (textBox13.Text != "")
            {
                dataGridView2["fbrkr1", row].Value = textBox13.Text;
            }
        }

        private void textBox15_TextChanged(object sender, EventArgs e)
        {
            if (textBox15.Text != "")
            {
                dataGridView2["Reinsurer", row].Value = textBox15.Text;
            }
        }

        private void textBox12_TextChanged(object sender, EventArgs e)
        {
            if (textBox12.Text != "")
            {
                Regex r = new Regex("^[0-9]{1,}$");
                if (!r.IsMatch(textBox12.Text))
                {
                    MessageBox.Show("Please input number!");
                    textBox12.Text.Remove(textBox12.Text.Length - 1);
                }
                dataGridView2["Seq#", row].Value = int.Parse(textBox12.Text.ToString());
            }
        }

        void saveinfo()
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            DataTable dt = dataGridView2.DataSource as DataTable;
            if (dt != null)
            {

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    if (!checknewinfo(dt.Rows[i]["fctlid"].ToString().Trim()))
                    {
                        string str = "select * from xsysparm where fidtype ='MXLINSR'";
                        DataTable dt1 = new DataTable();
                        dt1 = operate.GetTable(str);
                        string fctlid_info = dt1.Rows[0]["fnxtid"].ToString();

                        string adddatestrc =
                                "insert into mxlinsr(fctlid_1,fctlid_2,fctlid,flogseq,freinsr,fbrkr1,fbrkge1,fbrkr2,fbrkge2,fbrkr3,fbrkge3,fshare) " +
                        "values('" + fctlid_a + "','" + fid_layer + "','" + fctlid_info + "','" + dt.Rows[i]["Seq#"].ToString() + "','" + dt.Rows[i]["Reinsurer"].ToString() + "','" + dt.Rows[i]["fbrkr1"].ToString() + "','0.0000','','0.0000','','0.0000','" + dt.Rows[i]["Share (%)"].ToString() + "')";
                        operate.OperateData(adddatestrc);

                        string updatestrc = "update xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='MXLINSR'";
                        operate.OperateData(updatestrc);
                    }
                    else
                    {
                        string updatedatestro =
                               "update mxlinsr set flogseq ='" + dt.Rows[i]["Seq#"].ToString() + "',freinsr ='" + dt.Rows[i]["Reinsurer"].ToString() + "' ,fbrkr1 ='" + dt.Rows[i]["fbrkr1"].ToString() + "',fshare ='" + dt.Rows[i]["Share (%)"].ToString() + "' where fctlid='" + dt.Rows[i]["fctlid"].ToString() + "'";
                        operate.OperateData(updatedatestro);
                    }
                }
            }
        }

        void saveContact()
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            DataTable dt = dataGridView3.DataSource as DataTable;
            if (dt != null)
            {

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    if (!checknewsec(dt.Rows[i]["fctlid"].ToString().Trim()))
                    {
                        string str = "select * from xsysparm where fidtype ='MXLLAYRB'";
                        DataTable dt1 = new DataTable();
                        dt1 = operate.GetTable(str);
                        string fctlid_contact = dt1.Rows[0]["fnxtid"].ToString();

                        string adddatestrc =
                               "insert into mxllayrb(fctlid_1,fctlid_2,fctlid,fid,flimit,fexcess) " +
                        "values('" + fctlid_a + "','" + fid_layer + "','" + fctlid_contact + "','" + dt.Rows[i]["Item#"].ToString() + "','" + dt.Rows[i]["Loss Limit"].ToString().Trim().Replace(",", "") + "','" + dt.Rows[i]["Excess Point"].ToString().Trim().Replace(",", "") + "')";
                        operate.OperateData(adddatestrc);

                        string updatestro = "update xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='MXLLAYRB'";
                        operate.OperateData(updatestro);

                        for (int j = 0; j < listBox1.Items.Count; j++)
                        {
                            string str2 = "select * from xsysparm where fidtype ='MXLCLASS'";
                            DataTable dt2 = new DataTable();
                            dt2 = operate.GetTable(str2);
                            string fctlid_mtyclass = dt2.Rows[0]["fnxtid"].ToString();

                            string addmtyclass = "insert into mxlclass (fctlid_1,fctlid_2,fctlid_3,fctlid,ftype,fclass,fsec,fdesc,floscode)" +
                                "values('" + fctlid_a + "','" + fid_layer + "','" + fctlid_contact + "','" + fctlid_mtyclass + "','1','" + Regex.Replace(listBox1.Items[i].ToString(), @"/\(([^\(]*)\)/", "").Replace(" (SEC 1)", "").Replace(" (SEC 2)", "").Replace(" (TPBI)", "") + "','','" + listBox1.Items[i].ToString() + "','')";
                            operate.OperateData(addmtyclass);

                            string updatestrc = "update xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='MXLCLASS'";
                            operate.OperateData(updatestrc);
                        }
                        for (int j = 0; j < listBox2.Items.Count; j++)
                        {
                            string str2 = "select * from xsysparm where fidtype ='MXLCLASS'";
                            DataTable dt2 = new DataTable();
                            dt2 = operate.GetTable(str2);
                            string fctlid_mtyclass = dt2.Rows[0]["fnxtid"].ToString();

                            string addmtyclass1 = "insert into mxlclass (fctlid_1,fctlid_2,fctlid_3,fctlid,ftype,fclass,fsec,fdesc,floscode)" +
                                "values('" + fctlid_a + "','" + fid_layer + "','" + fctlid_contact + "','" + fctlid_mtyclass + "','2','" + Regex.Replace(listBox2.Items[i].ToString(), @"/\(([^\(]*)\)/", "").Replace(" (SEC 1)", "").Replace(" (SEC 2)", "").Replace(" (TPBI)", "") + "','','" + listBox2.Items[i].ToString() + "','')";
                            operate.OperateData(addmtyclass1);

                            string updatestrc1 = "update xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='MXLCLASS'";
                            operate.OperateData(updatestrc1);
                        }
                    }
                    else
                    {
                        string updatedatestro =
                               "update mxllayrb set fid ='" + dt.Rows[i]["Item#"].ToString().Trim() + "',flimit ='" + dt.Rows[i]["Loss Limit"].ToString().Trim().Replace(",", "") + "',fexcess ='" + dt.Rows[i]["Excess Point"].ToString().Trim().Replace(",", "") + "' where fctlid='" + dt.Rows[i]["fctlid"].ToString().Trim() + "'";
                        operate.OperateData(updatedatestro);

                        for (int j = 0; j < listBox1.Items.Count; j++)
                        {
                            string updatemtyclass = "update mxlclass set ftype ='1' where fctlid_1='" + fctlid_a + "' and fctlid_2 ='" + fid_layer.Trim() + "' and fctlid_3='" + dt.Rows[i]["fctlid"].ToString().Trim() + "' and fdesc ='" + listBox1.Items[j].ToString() + "'";
                            operate.OperateData(updatemtyclass);
                        }

                        for (int j = 0; j < listBox2.Items.Count; j++)
                        {
                            string updatemtyclass1 = "update mxlclass set ftype ='2' where fctlid_1='" + fctlid_a + "' and fctlid_2 ='" + fid_layer.Trim() + "' and fctlid_3='" + dt.Rows[i]["fctlid"].ToString().Trim() + "' and fdesc ='" + listBox2.Items[j].ToString() + "'";
                            operate.OperateData(updatemtyclass1);
                        }
                    }
                }
            }
        }

        private void textBox2_TextChanged(object sender, EventArgs e)
        {
            if (textBox2.Text != "")
            {
                dataGridView3["Item#", row1].Value = textBox2.Text;
            }
        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", "");
        }
    }
}

