using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using INS.INSClass;

namespace INS
{
    public partial class Frm_Demo : Form
    {
        public Frm_Demo()
        {
            InitializeComponent();
        }
        DES des = new DES();
        XLS txt = new XLS();

        private void btn_Ok_Click(object sender, EventArgs e)
        {
            Double iNum = 0.0;
            if (Double.TryParse(txt_Num.Text, out iNum) == false)
            {
                txt_Num.Focus();
                txt_Num.SelectAll();
                MessageBox.Show("It's not number!","Warning");
                return;
            }

            txt_Ret.Text = NumGetString.NumGetStr(iNum);

           
        }

        private void button1_Click(object sender, EventArgs e)
        {
            //txt_Ret.Text = des.decode(txt_Num.Text);
            //txt_Ret.Text = des.Utf8ToGB2312("测试一下");
            txt_Ret.Text = txt.ReadExcelToTable3("D:\\book1.xls");
        }
    }
}
