using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using System.Globalization;
using System.Windows.Documents;
using System.Text.RegularExpressions;

namespace INS
{
    public partial class frmFac1 : Form
    {
        List<string> list = new List<string>();
        List<string> list1 = new List<string>();

        public frmFac1()
        {
            InitializeComponent();
            InitCombobox();
            FillData("", "");

        }
        private DataTable dataSource = new DataTable();
        private DataTable dataSource1 = new DataTable();
        public int i = 0;
        DES des = new DES();
        DBConnect operate = new DBConnect();
        ExportDBF DBF = new ExportDBF();
        XLS txt = new XLS();
        public string fctlid_a = "";
        public String user = InsEnvironment.LoginUser.GetUserCode();
        public int row = 0;
        public int row1 = 0;
        public Boolean flag = false;
        public Boolean ADD = false;
        public Boolean flag1 = false;
        private bool datagridview2SelectionChanged = true;
        private bool datagridview3SelectionChanged = true;
        void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            try
            {
                if (order != "" && query != "")
                {
                    if (order == "TTY#")
                    {
                        a = new SqlDataAdapter("select fctlid,fid as TTY#,fdesc as Desc#,fefffr as [Eff From] ,feffto as [Eff To],fcur  " +
                            " from mfb where fid like '%" + query.ToUpper() + "%'", c);
                    }
                    else
                    {
                        a = new SqlDataAdapter("select fctlid,fid as TTY#,fdesc as Desc#,fefffr as [Eff From] ,feffto as [Eff To],fcur  " +
                            " from mfb where fdesc like '%" + query.ToUpper() + "%'", c);
                    }
                }
                else if (order != "" && query == "")
                {
                    a = new SqlDataAdapter("select fctlid,fid as TTY#,fdesc as Desc#,fefffr as [Eff From] ,feffto as [Eff To],fcur  " +
                            " from mfb order by " + order, c);
                }
                else
                {
                    a = new SqlDataAdapter("select fctlid,fid as TTY#,fdesc as Desc#,fefffr as [Eff From] ,feffto as [Eff To],fcur  " +
                            " from mfb order by fid ", c);

                }
                DataTable t = new DataTable();
                a.Fill(t);
                dataGridView1.DataSource = t;
                this.dataGridView1.Columns[0].Visible = false;
                this.dataGridView1.Columns[4].Visible = false;
                this.dataGridView1.Columns[5].Visible = false;
            }
            catch { }
            c.Close();
        }

        private void tabControl1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            int counter;
            counter = dataGridView1.CurrentCell.RowIndex;
            fctlid_a = dataGridView1.Rows[counter].Cells["fctlid"].Value.ToString();

            if (tabControl1.SelectedIndex == 1 && ADD == false)
            {
                if (dataGridView1.Rows[counter].Cells["TTY#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["TTY#"].Value.ToString().Length != 0)
                    {
                        textBox8.Text = dataGridView1.Rows[counter].Cells["TTY#"].Value.ToString();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        textBox9.Text = dataGridView1.Rows[counter].Cells["Desc#"].Value.ToString();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Eff From"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Eff From"].Value.ToString().Length != 0)
                    {
                        textBox10.Text = dataGridView1.Rows[counter].Cells["Eff From"].Value.ToString();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["Eff To"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["Eff To"].Value.ToString().Length != 0)
                    {
                        textBox11.Text = dataGridView1.Rows[counter].Cells["Eff To"].Value.ToString();
                    }
                }

                if (dataGridView1.Rows[counter].Cells["fcur"].Value != null)
                {
                    if (dataGridView1.Rows[counter].Cells["fcur"].Value.ToString().Length != 0)
                    {
                        comboBox3.SelectedItem = dataGridView1.Rows[counter].Cells["fcur"].Value.ToString();
                    }
                }

                reloadgridview2("");
                reloadgridview3("");
                mfbclass();

            }
            if (tabControl1.SelectedIndex == 2 && ADD == false)
            {
                reloadgridview3("");
                mfbclass();
            }
        }

        void reloadgridview2(string fid)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select a.fctlid, a.flogseq as Seq#, freinsr as Reinsurer,fshare as [Share (%)], fbrkr1, " +
                                "b.fdesc as Bdesc,c.fdesc as Rdesc, a.fctlid_1 from mfbinsr a " +
                                "left join (select fid,fdesc from mprdr where ftype='B') b on a.fbrkr1=b.fid " +
                                "left join (select fid,fdesc from mprdr where ftype='R') c on a.freinsr=c.fid " +
                                "where fctlid_1 = '" + fctlid_a + "' ORDER BY flogseq", c))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                int total = 0;
                for (int i = 0; i < t2.Rows.Count; i++)
                {
                    total = total + int.Parse(t2.Rows[i]["Share (%)"].ToString().Trim().Replace(".0000", ""));
                }
                textBox18.Text = total.ToString();
                textBox18.Text = string.Format("{0:N4}", Convert.ToDecimal(textBox18.Text));
                if (rowlabelinfo.Text != "")
                {
                    if (dataGridView2.RowCount > int.Parse(rowlabelinfo.Text))
                    { row = 0; }
                }
                if (fid == "" && row == 0)//fisrt row
                {
                    if (t2.Rows.Count != 0)
                    {
                        dataGridView2.DataSource = t2;
                        dataGridView2.CurrentCell = dataGridView2.Rows[row].Cells["Seq#"];
                    }
                    else
                    {
                        datagridview2SelectionChanged = false;
                        dataGridView2.DataSource = t2;
                    }
                }
                else
                {
                    datagridview2SelectionChanged = false;
                    dataGridView2.DataSource = t2;
                    if (fid == "" && row != 0) // add cancel
                    {
                        datagridview2SelectionChanged = true;
                        flag = true;
                        row = int.Parse(rowlabelinfo.Text);

                        dataGridView2.CurrentCell = dataGridView2.Rows[int.Parse(rowlabelinfo.Text)].Cells["Seq#"];
                    }
                    if (fid != "") //add update
                    {
                        for (int i = 0; i < t2.Rows.Count; i++)
                        {
                            if (t2.Rows[i]["fctlid"].ToString().Trim() == fid.Trim())
                            {
                                flag = true;
                                row = i;
                                rowlabelinfo.Text = i.ToString();
                                dataGridView2.CurrentCell = dataGridView2.Rows[i].Cells["Seq#"];
                                break;
                            }
                        }
                    }
                }
                this.dataGridView2.Columns[0].Visible = false;
                this.dataGridView2.Columns[4].Visible = false;
                this.dataGridView2.Columns[5].Visible = false;
                this.dataGridView2.Columns[6].Visible = false;
                this.dataGridView2.Columns[7].Visible = false;

            }
            datagridview2SelectionChanged = true;
            flag = false;
            c.Close();

        }

        void reloadgridview3(string fid)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select fctlid, fid as Seq#, ftylmt as [Treaty Limit], ftimes, fscale1 ,fscale2 ,fcom ,case when fgrnet='1' then 'Gross' else 'Net' end as fgrnet, fctlid_1 " +
  "from mfbsec where fctlid_1 = '" + fctlid_a + "' ORDER BY fid", c))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                if (fid == "" && row1 == 0)//fisrt row
                {
                    if (t2.Rows.Count != 0)
                    {
                        dataGridView3.DataSource = t2;
                        dataGridView3.CurrentCell = dataGridView3.Rows[row1].Cells["Seq#"];
                    }
                    else
                    {
                        datagridview3SelectionChanged = false;
                        dataGridView3.DataSource = t2;
                    }
                }
                else
                {
                    datagridview3SelectionChanged = false;
                    dataGridView3.DataSource = t2;
                    if (fid == "" && row1 != 0) // add cancel
                    {
                        datagridview3SelectionChanged = true;
                        flag1 = true;
                        row1 = int.Parse(rowlabelcontact.Text);
                        dataGridView3.CurrentCell = dataGridView3.Rows[int.Parse(rowlabelcontact.Text)].Cells["Seq#"];
                    }
                    if (fid != "") //add update
                    {
                        for (int i = 0; i < t2.Rows.Count; i++)
                        {
                            if (t2.Rows[i]["Seq#"].ToString().Trim() == fid.Trim())
                            {
                                flag1 = true;
                                row1 = i;
                                rowlabelcontact.Text = i.ToString();
                                dataGridView3.CurrentCell = dataGridView3.Rows[i].Cells["Seq#"];
                                break;
                            }
                        }
                    }
                }
                this.dataGridView3.Columns[0].Visible = false;
                this.dataGridView3.Columns[3].Visible = false;
                this.dataGridView3.Columns[4].Visible = false;
                this.dataGridView3.Columns[5].Visible = false;
                this.dataGridView3.Columns[6].Visible = false;
                this.dataGridView3.Columns[7].Visible = false;
                this.dataGridView3.Columns[8].Visible = false;
            }
            datagridview3SelectionChanged = true;
            flag1 = false;
            c.Close();

        }

        private void dataGridView2_SelectionChanged(object sender, EventArgs e)
        {
            if (!this.datagridview2SelectionChanged) return;
            try
            {
                if (flag == false && dataGridView2.CurrentCell != null)
                {
                    row = dataGridView2.CurrentCell.RowIndex;
                }

                if (dataGridView2.Rows[row].Cells["Seq#"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["Seq#"].Value.ToString().Length != 0)
                    {
                        textBox12.Text = dataGridView2.Rows[row].Cells["Seq#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView2.Rows[row].Cells["fbrkr1"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["fbrkr1"].Value.ToString().Length != 0)
                    {
                        textBox13.Text = dataGridView2.Rows[row].Cells["fbrkr1"].Value.ToString().Trim();
                    }
                }

                if (dataGridView2.Rows[row].Cells["Bdesc"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["Bdesc"].Value.ToString().Length != 0)
                    {
                        textBox14.Text = dataGridView2.Rows[row].Cells["Bdesc"].Value.ToString().Trim();
                    }
                }

                if (dataGridView2.Rows[row].Cells["Reinsurer"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["Reinsurer"].Value.ToString().Length != 0)
                    {
                        textBox15.Text = dataGridView2.Rows[row].Cells["Reinsurer"].Value.ToString().Trim();
                    }
                }

                if (dataGridView2.Rows[row].Cells["Rdesc"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["Rdesc"].Value.ToString().Length != 0)
                    {
                        textBox16.Text = dataGridView2.Rows[row].Cells["Rdesc"].Value.ToString().Trim();
                    }
                }
                if (dataGridView2.Rows[row].Cells["Share (%)"].Value != null)
                {
                    if (dataGridView2.Rows[row].Cells["Share (%)"].Value.ToString().Length != 0)
                    {
                        textBox17.Text = dataGridView2.Rows[row].Cells["Share (%)"].Value.ToString().Trim();
                        textBox17.Text = string.Format("{0:N4}", Convert.ToDecimal(textBox17.Text));
                    }
                }
                if (dataGridView2.Rows[row].Cells["fctlid"].Value != null)
                {
                    fctlidlabelinfo.Text = dataGridView2.Rows[row].Cells["fctlid"].Value.ToString();
                }
                rowlabelinfo.Text = row.ToString();
            }
            catch
            {
                //MessageBox.Show("Have Error", "Warning",
                //MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void dataGridView3_SelectionChanged(object sender, EventArgs e)
        {
            if (!this.datagridview3SelectionChanged) return;
            try
            {
                if (flag1 == false && dataGridView3.CurrentCell != null)
                {
                    row1 = dataGridView3.CurrentCell.RowIndex;
                }

                if (dataGridView3.Rows[row1].Cells["Seq#"].Value != null)
                {
                    if (dataGridView3.Rows[row1].Cells["Seq#"].Value.ToString().Length != 0)
                    {
                        textBox2.Text = dataGridView3.Rows[row1].Cells["Seq#"].Value.ToString().Trim();
                    }
                }

                if (dataGridView3.Rows[row1].Cells["Treaty Limit"].Value != null)
                {
                    if (dataGridView3.Rows[row1].Cells["Treaty Limit"].Value.ToString().Length != 0)
                    {
                        textBox3.Text = dataGridView3.Rows[row1].Cells["Treaty Limit"].Value.ToString().Trim();
                    }
                }

                if (dataGridView3.Rows[row1].Cells["fscale1"].Value != null)
                {
                    if (dataGridView3.Rows[row1].Cells["fscale1"].Value.ToString().Length != 0)
                    {
                        textBox4.Text = dataGridView3.Rows[row1].Cells["fscale1"].Value.ToString().Trim();
                        textBox4.Text = string.Format("{0:N4}", Convert.ToDecimal(textBox4.Text));
                    }
                }

                if (dataGridView3.Rows[row1].Cells["fscale2"].Value != null)
                {
                    if (dataGridView3.Rows[row1].Cells["fscale2"].Value.ToString().Length != 0)
                    {
                        textBox5.Text = dataGridView3.Rows[row1].Cells["fscale2"].Value.ToString().Trim();
                        textBox5.Text = string.Format("{0:N4}", Convert.ToDecimal(textBox5.Text));
                    }
                }

                if (dataGridView3.Rows[row1].Cells["fctlid"].Value != null)
                {
                    fctlidlabelcontact.Text = dataGridView3.Rows[row1].Cells["fctlid"].Value.ToString();
                }
                rowlabelcontact.Text = row1.ToString();

                mfbclass();
            }
            catch
            {
                //MessageBox.Show("Have Error", "Warning",
                //   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        void mfbclass()
        {
            list.Clear();
            list1.Clear();
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select fdesc from mfbclass where fctlid_1 ='" + fctlid_a + "' and fctlid_2 ='" + fctlidlabelcontact.Text.ToString().Trim() + "' and ftype=1", c))
            {
                DataTable dt1 = new DataTable();
                a.Fill(dt1);
                for (int i = 0; i < dt1.Rows.Count; i++)
                {
                    list.Add(dt1.Rows[i]["fdesc"].ToString().Trim());
                }
            }


            using (SqlDataAdapter a = new SqlDataAdapter("select fdesc from mfbclass where fctlid_1 ='" + fctlid_a + "' and fctlid_2 ='" + fctlidlabelcontact.Text.ToString().Trim() + "' and ftype=2", c))
            {
                DataTable dt2 = new DataTable();
                a.Fill(dt2);
                for (int i = 0; i < dt2.Rows.Count; i++)
                {
                    list1.Add(dt2.Rows[i]["fdesc"].ToString().Trim());
                }
            }
            listBox1.DataSource = null;
            listBox2.DataSource = null;
            listBox1.DataSource = list;
            listBox2.DataSource = list1;

            c.Close();
        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            //e.Cancel = true;
        }

        private void tabControl1_Selecting2(object sender, TabControlCancelEventArgs e)
        {
            //e.Cancel = false;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", textBox1.Text.ToString().Trim());
        }

        public enum Mode
        {
            TTY = 1,
            Desc = 2
        }

        private void InitCombobox()
        {
            Array arr = System.Enum.GetValues(typeof(Mode));    // 获取枚举的所有值
            DataTable dt = new DataTable();
            dt.Columns.Add("String", Type.GetType("System.String"));
            dt.Columns.Add("Value", typeof(int));
            foreach (var a in arr)
            {
                string strText = EnumTextByDescription.GetEnumDesc((Mode)a);
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = (int)a;

                dt.Rows.Add(aRow);
            }

            comboBox1.DataSource = dt;
            comboBox1.DisplayMember = "String";
            comboBox1.ValueMember = "Value";
        }


        void ButtonControlback()
        {
            ADD = false;
            textBox8.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox8.ReadOnly = true;
            textBox9.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox9.ReadOnly = true;
            textBox10.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox10.ReadOnly = true;
            textBox11.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox11.ReadOnly = true;
            comboBox3.BackColor = System.Drawing.SystemColors.InactiveCaption;
            comboBox3.Enabled = false;

            textBox12.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox12.ReadOnly = true;
            textBox13.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox13.ReadOnly = true;
            textBox15.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox15.ReadOnly = true;
            textBox17.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox17.ReadOnly = true;
            textBox2.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox2.ReadOnly = true;
            textBox3.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox3.ReadOnly = true;
            textBox4.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox4.ReadOnly = true;
            textBox5.BackColor = System.Drawing.SystemColors.InactiveCaption;
            textBox5.ReadOnly = true;

            dcancel.Visible = false;
            dsave.Visible = false;
            dexit.Visible = true;
            dadd.Visible = true;
            dupdate.Visible = true;
            ddel.Visible = true;
            csave.Visible = false;
            cCancel.Visible = false;
            cadd.Visible = true;
            cdel.Visible = true;
            cupdate.Visible = true;
            cexit.Visible = true;
            add1.Visible = false;
            add2.Visible = false;
            del1.Visible = false;
            del2.Visible = false;
            button4.Enabled = false;
            button5.Enabled = false;
            button2.Enabled = false;
            button3.Enabled = false;
            button6.Enabled = false;
            button7.Enabled = false;
            reloadgridview2("");
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting2);
        }

        void ButtonControl()
        {
            ADD = true;
            textBox8.BackColor = System.Drawing.SystemColors.Window;
            textBox8.ReadOnly = false;
            textBox9.BackColor = System.Drawing.SystemColors.Window;
            textBox9.ReadOnly = false;
            textBox10.BackColor = System.Drawing.SystemColors.Window;
            textBox10.ReadOnly = false;
            textBox11.BackColor = System.Drawing.SystemColors.Window;
            textBox11.ReadOnly = false;
            comboBox3.BackColor = System.Drawing.SystemColors.Window;
            comboBox3.Enabled = true;
            dcancel.Visible = true;
            dsave.Visible = true;
            dexit.Visible = false;
            dadd.Visible = false;
            dupdate.Visible = false;
            ddel.Visible = false;
            csave.Visible = true;
            cCancel.Visible = true;
            cadd.Visible = false;
            cdel.Visible = false;
            cupdate.Visible = false;
            cexit.Visible = false;
            add1.Visible = true;
            add2.Visible = true;
            del1.Visible = true;
            del2.Visible = true;
            button4.Enabled = true;
            button5.Enabled = true;
            button2.Enabled = true;
            button3.Enabled = true;
            button6.Enabled = true;
            button7.Enabled = true;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);
        }

        void ButtonControladd()
        {
            textBox8.Text = "";
            textBox9.Text = "";
            textBox10.Text = "";
            textBox11.Text = "";
            textBox12.Text = "";
            textBox13.Text = "";
            textBox14.Text = "";
            textBox15.Text = "";
            textBox16.Text = "";
            textBox17.Text = "";
            textBox2.Text = "";
            textBox3.Text = "";
            textBox4.Text = "";
            textBox5.Text = "";
        }

        void ButtonCountrolupdate()
        {
            textBox12.BackColor = System.Drawing.SystemColors.Window;
            textBox12.ReadOnly = false;
            textBox13.BackColor = System.Drawing.SystemColors.Window;
            textBox13.ReadOnly = false;
            textBox15.BackColor = System.Drawing.SystemColors.Window;
            textBox15.ReadOnly = false;
            textBox17.BackColor = System.Drawing.SystemColors.Window;
            textBox17.ReadOnly = false;
            textBox2.BackColor = System.Drawing.SystemColors.Window;
            textBox2.ReadOnly = false;
            textBox3.BackColor = System.Drawing.SystemColors.Window;
            textBox3.ReadOnly = false;
            textBox4.BackColor = System.Drawing.SystemColors.Window;
            textBox4.ReadOnly = false;
            textBox5.BackColor = System.Drawing.SystemColors.Window;
            textBox5.ReadOnly = false;

        }

        private void button9_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void dexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void cexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void dcancel_Click(object sender, EventArgs e)
        {
            ButtonControlback();
            reload();
            mfbclass();
            reloadgridview2("");
            reloadgridview3("");
        }

        private void cCancel_Click(object sender, EventArgs e)
        {
            ButtonControlback();
            reload();
            reloadgridview2("");
            datagridview3SelectionChanged = true;
            reloadgridview3("");
            mfbclass();
        }

        private void lupdate_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonCountrolupdate();
        }

        private void dupdate_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonCountrolupdate();
        }

        private void cupdate_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonCountrolupdate();
        }

        private void ladd_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonControladd();

            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select a.fctlid, a.flogseq as Seq#, freinsr as Reinsurer,fshare as [Share (%)], fbrkr1, " +
                                "b.fdesc as Bdesc,c.fdesc as Rdesc, a.fctlid_1 from mfbinsr a " +
                                "left join (select fid,fdesc from mprdr where ftype='B') b on a.fbrkr1=b.fid " +
                                "left join (select fid,fdesc from mprdr where ftype='R') c on a.freinsr=c.fid " +
                                "where fctlid_1 = '" + fctlid_a + "' ORDER BY flogseq", c))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                t2.Clear();
                dataGridView2.DataSource = t2;
            }
            c.Close();
            SqlConnection c1 = DBConnect.dbConn;
            if (c1.State == ConnectionState.Closed)
            { c1.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select fctlid, fid as Seq#, ftylmt as [Treaty Limit], ftimes, fscale1 ,fscale2 ,fcom ,case when fgrnet='1' then 'Gross' else 'Net' end as fgrnet, fctlid_1 " +
  "from mfbsec where fctlid_1 = '" + fctlid_a + "' ORDER BY fid", c1))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                t2.Clear();
                dataGridView3.DataSource = t2;
            }
            c1.Close();
            listBox1.DataSource = null;
            list.AddRange(list1);
            listBox1.DataSource = list;
            listBox2.DataSource = null;
            list1.Clear();

        }

        private void dadd_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonControladd();

            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select a.fctlid, a.flogseq as Seq#, freinsr as Reinsurer,fshare as [Share (%)], fbrkr1, " +
                                "b.fdesc as Bdesc,c.fdesc as Rdesc, a.fctlid_1 from mfbinsr a " +
                                "left join (select fid,fdesc from mprdr where ftype='B') b on a.fbrkr1=b.fid " +
                                "left join (select fid,fdesc from mprdr where ftype='R') c on a.freinsr=c.fid " +
                                "where fctlid_1 = '" + fctlid_a + "' ORDER BY flogseq", c))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                t2.Clear();
                dataGridView2.DataSource = t2;
            }
            c.Close();
            SqlConnection c1 = DBConnect.dbConn;
            if (c1.State == ConnectionState.Closed)
            { c1.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select fctlid, fid as Seq#, ftylmt as [Treaty Limit], ftimes, fscale1 ,fscale2 ,fcom ,case when fgrnet='1' then 'Gross' else 'Net' end as fgrnet, fctlid_1 " +
  "from mfbsec where fctlid_1 = '" + fctlid_a + "' ORDER BY fid", c1))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                t2.Clear();
                dataGridView3.DataSource = t2;
            }
            c1.Close();
            listBox1.DataSource = null;
            list.AddRange(list1);
            listBox1.DataSource = list;
            listBox2.DataSource = null;
            list1.Clear();
        }

        private void cadd_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonControladd();

            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select a.fctlid, a.flogseq as Seq#, freinsr as Reinsurer,fshare as [Share (%)], fbrkr1, " +
                                "b.fdesc as Bdesc,c.fdesc as Rdesc, a.fctlid_1 from mfbinsr a " +
                                "left join (select fid,fdesc from mprdr where ftype='B') b on a.fbrkr1=b.fid " +
                                "left join (select fid,fdesc from mprdr where ftype='R') c on a.freinsr=c.fid " +
                                "where fctlid_1 = '" + fctlid_a + "' ORDER BY flogseq", c))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                t2.Clear();
                dataGridView2.DataSource = t2;
            }
            c.Close();

            SqlConnection c1 = DBConnect.dbConn;
            if (c1.State == ConnectionState.Closed)
            { c1.Open(); }

            using (SqlDataAdapter a = new SqlDataAdapter("select fctlid, fid as Seq#, ftylmt as [Treaty Limit], ftimes, fscale1 ,fscale2 ,fcom ,case when fgrnet='1' then 'Gross' else 'Net' end as fgrnet, fctlid_1 " +
  "from mfbsec where fctlid_1 = '" + fctlid_a + "' ORDER BY fid", c1))
            {
                DataTable t2 = new DataTable();
                a.Fill(t2);
                t2.Clear();
                dataGridView3.DataSource = t2;
            }
            c1.Close();
            listBox1.DataSource = null;
            list.AddRange(list1);
            listBox1.DataSource = list;
            listBox2.DataSource = null;
            list1.Clear();
        }

        private void ldel_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            del();
        }

        private void ddel_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            del();
        }

        private void cdel_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            del();
        }

        void del()
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the ALL items?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string deldatestr1 =
                    "delete from mfb where fctlid='" + fctlid_a + "'";
                operate.OperateData(deldatestr1);
                string deldatestr =
                    "delete from mfbinsr where fctlid_1='" + fctlid_a + "'";
                operate.OperateData(deldatestr);
                string deldatestr2 =
                    "delete from mfbsec where fctlid_1='" + fctlid_a + "'";
                operate.OperateData(deldatestr2);
                MessageBox.Show("Have Been Deleted", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                FillData("", "");
                tabControl1.SelectedTab = Classes;
            }

        }

        private void textBox8_TextChanged(object sender, EventArgs e)
        {
            string context = this.textBox8.Text;

            string str1 = "SELECT fid FROM mfb where fctlid = '" + fctlid_a + "'";
            DataTable dt1 = new DataTable();
            dt1 = operate.GetTable(str1);
            for (int i = 0; i < dt1.Rows.Count; i++)
            {
                if ((dt1.Rows[i]["fid"].ToString().Trim() == context.Trim()) && (ADD == true))
                {
                    MessageBox.Show("Duplicate", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private void textBox17_TextChanged(object sender, EventArgs e)
        {
            if (textBox17.Text != "")
            {
                dataGridView2["Share (%)", row].Value = textBox17.Text.ToString().Replace(",", "");
                total();
            }
        }

        void total()
        {
            Decimal sum = 0;
            for (int i = 0; i < dataGridView2.Rows.Count; i++)
            {
                if (dataGridView2["Share (%)", i].Value.ToString() != "")
                {
                    sum = sum + Convert.ToDecimal(dataGridView2["Share (%)", i].Value);
                }
            }
            textBox18.Text = string.Format("{0:N0}", Convert.ToDecimal(sum.ToString().Replace(",", "")));
        }

        private void textBox2_TextChanged(object sender, EventArgs e)
        {
            if (textBox2.Text == "")
            {
                dataGridView3["Seq#", dataGridView3.CurrentCell.RowIndex].Value = i;
                textBox2.Text = i.ToString();
            }
            else
            {
                Regex r = new Regex("^[0-9]{1,}$");
                if (!r.IsMatch(textBox2.Text))
                {
                    MessageBox.Show("Please input number!");
                }
                dataGridView3["Seq#", row1].Value = int.Parse(textBox2.Text.ToString());
            }
        }

        private void textBox3_TextChanged(object sender, EventArgs e)
        {
            if (textBox3.Text != "")
            {
                try
                {
                    dataGridView3["Treaty Limit", row1].Value = textBox3.Text.ToString().Replace(",", "");
                }
                catch
                { }
            }
            else
            {
                dataGridView3["Treaty Limit", row1].Value = "0.0";
            }
        }
        private void textBox4_TextChanged(object sender, EventArgs e)
        {
            if (textBox4.Text != "")
            {
                textBox4.Text = string.Format("{0:N4}", Convert.ToDecimal(textBox4.Text));
                dataGridView3["fscale1", row1].Value = textBox4.Text;
            }
        }
        private void textBox10_TextChanged(object sender, EventArgs e)
        {
            if (textBox10.Text != "")
            {
                try
                {
                    string a = textBox10.Text;
                    DateTime b = Convert.ToDateTime(a);
                    textBox10.Text = b.ToShortDateString();
                }
                catch { }
            }
        }
        private void textBox11_TextChanged(object sender, EventArgs e)
        {
            if (textBox11.Text != "")
            {
                try
                {
                    string a = textBox11.Text;
                    DateTime b = Convert.ToDateTime(a);
                    textBox11.Text = b.ToShortDateString();
                }
                catch { }
            }
        }
        private void textBox5_TextChanged(object sender, EventArgs e)
        {
            if (textBox5.Text != "")
            {
                textBox5.Text = string.Format("{0:N4}", Convert.ToDecimal(textBox5.Text));
                dataGridView3["fscale2", row1].Value = textBox5.Text;
            }
        }

        private void del1_Click(object sender, EventArgs e)
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the item?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string fctlid = fctlidlabelinfo.Text;
                string deldatestr =
                    "delete from mfbinsr where fctlid='" + fctlid + "'";
                operate.OperateData(deldatestr);
                MessageBox.Show("Have Been Deleted", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                row = 0;
                reloadgridview2("");

            }
        }

        private void del2_Click(object sender, EventArgs e)
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the item?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string fctlid = fctlidlabelcontact.Text;
                string deldatestr =
                    "delete from mfbsec where fctlid='" + fctlid + "'";
                operate.OperateData(deldatestr);
                MessageBox.Show("Have Been Deleted", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                row1 = 0;
                reloadgridview3("");
            }
        }

        private void add1_Click(object sender, EventArgs e)
        {
            dataSource = dataGridView2.DataSource as DataTable;
            DataRow newCustomersRow = dataSource.NewRow();
            dataSource.Rows.InsertAt(newCustomersRow, dataSource.Rows.Count);
            dataGridView2.DataSource = dataSource;
            dataGridView2.CurrentCell = dataGridView2.Rows[dataSource.Rows.Count - 1].Cells["Seq#"];

            textBox12.BackColor = System.Drawing.SystemColors.Window;
            textBox12.ReadOnly = false;
            textBox12.Text = "";
            textBox13.BackColor = System.Drawing.SystemColors.Window;
            textBox13.ReadOnly = false;
            textBox13.Text = "";
            textBox14.Text = "";
            textBox16.Text = "";
            textBox15.BackColor = System.Drawing.SystemColors.Window;
            textBox15.ReadOnly = false;
            textBox15.Text = "";
            textBox17.BackColor = System.Drawing.SystemColors.Window;
            textBox17.ReadOnly = false;
            textBox17.Text = "0.0";
        }

        private void add2_Click(object sender, EventArgs e)
        {
            dataSource1 = dataGridView3.DataSource as DataTable;
            DataRow newCustomersRow = dataSource1.NewRow();
            dataSource1.Rows.InsertAt(newCustomersRow, dataSource1.Rows.Count);
            dataGridView3.DataSource = dataSource1;
            dataGridView3.CurrentCell = dataGridView3.Rows[dataSource1.Rows.Count - 1].Cells["Seq#"];

            textBox2.BackColor = System.Drawing.SystemColors.Window;
            textBox2.ReadOnly = false;
            textBox2.Text = "";
            textBox3.BackColor = System.Drawing.SystemColors.Window;
            textBox3.ReadOnly = false;
            textBox3.Text = "";
            textBox4.BackColor = System.Drawing.SystemColors.Window;
            textBox4.ReadOnly = false;
            textBox4.Text = "";
            textBox5.BackColor = System.Drawing.SystemColors.Window;
            textBox5.ReadOnly = false;
            textBox5.Text = "";
            listBox1.DataSource = null;
            list.AddRange(list1);
            listBox1.DataSource = list;
            listBox2.DataSource = null;
            list1.Clear();
        }

        private void dsave_Click(object sender, EventArgs e)
        {
            saveadd();
            ButtonControlback();
        }

        private void csave_Click(object sender, EventArgs e)
        {
            saveadd();
            ButtonControlback();
        }

        void saveadd()
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really Insert the item?", "Inserted Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                String fctlid_contact = fctlidlabelcontact.Text;
                String fctlid_info = fctlidlabelinfo.Text;

                if (ADD == true)
                {
                    string str = "select * from xsysparm where fidtype ='MFB'";
                    DataTable dt = new DataTable();
                    dt = operate.GetTable(str);
                    fctlid_a = dt.Rows[0]["fnxtid"].ToString();
                }

                if (textBox8.Text.Trim() != "" && textBox9.Text.Trim() != "")
                {
                    if (ADD == true)
                    {
                        string adddatestr = "insert into mfb(fctlid,fid,fdesc,fbrkr,fefffr,feffto,fcur,ftshare,fctldel,finpuser,finpdate,fupduser,fupddate) values" +
                            "('" + fctlid_a + "','" + textBox8.Text.Trim().Replace("'", "''") + "','" + textBox9.Text.Trim().Replace("'", "''") + "','',convert(datetime,'" + textBox10.Text.Trim() + "',102),convert(datetime,'" + textBox11.Text.Trim() + "',102),'" + comboBox3.SelectedItem.ToString().Trim() + "','100.0000','','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "')";
                        operate.OperateData(adddatestr);

                        string updatestr = "update xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='MFB'";
                        operate.OperateData(updatestr);
                    }
                    else
                    {
                        string updatedatestr =
                            "update mfb set fid ='" + textBox8.Text.Trim() + "',fdesc ='" + textBox9.Text.Trim() + "',fefffr =convert(datetime,'" + textBox10.Text.Trim() + "',102) ,feffto =convert(datetime,'" + textBox11.Text.Trim() + "',102) ,fcur ='" + comboBox3.SelectedItem.ToString().Trim() + "', fupduser ='" + user + "',fupddate ='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid='" + fctlid_a + "'";
                        operate.OperateData(updatedatestr);
                    }

                    saveinfo();

                    saveContact();


                    MessageBox.Show("Have Been Inserted!", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    ButtonControl();
                    reloadgridview2(textBox12.Text.Trim());
                    reloadgridview3(textBox2.Text.Trim());
                }
                else
                {
                    MessageBox.Show("Some field is empty!", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        public Boolean checknew(string strSql)
        {
            string str =
                    "select count(*) from mfb where fid ='" + strSql.Trim() + "'";
            int i = operate.SelectData(str);
            if (i > 0)
            { return true; }
            else { return false; }
        }

        public Boolean checknewinfo(string strSql)
        {
            string str =
                    "select count(*) from mfbinsr where fctlid ='" + strSql.Trim() + "' and fctlid_1 ='" + fctlid_a.Trim() + "'";
            int i = operate.SelectData(str);
            if (i > 0)
            { return true; }
            else { return false; }
        }

        public Boolean checknewsec(string strSql)
        {
            string str =
                    "select count(*) from mfbsec where fctlid ='" + strSql.Trim() + "' and fctlid_1 ='" + fctlid_a.Trim() + "'";
            int i = operate.SelectData(str);
            if (i > 0)
            { return true; }
            else { return false; }
        }

        void reload()
        {
            string str = "select * from mfb where fctlid ='" + fctlid_a + "'";
            DataTable dt = new DataTable();
            dt = operate.GetTable(str);
            textBox8.Text = dt.Rows[0]["fid"].ToString();
            textBox9.Text = dt.Rows[0]["fdesc"].ToString();
            textBox10.Text = dt.Rows[0]["fefffr"].ToString();
            textBox11.Text = dt.Rows[0]["feffto"].ToString();
            comboBox3.SelectedItem = dt.Rows[0]["fcur"].ToString();
        }


        private String _strValue;

        public String StrValue
        {
            set
            {
                _strValue = value;
            }
        }

        private String _strValuebn;

        public String StrValuebn
        {
            set
            {
                _strValuebn = value;
            }
        }
        private String _strValuer;

        public String StrValuer
        {
            set
            {
                _strValuer = value;
            }
        }

        private String _strValuern;

        public String StrValuern
        {
            set
            {
                _strValuern = value;
            }
        }
        public Boolean CloseFlag = false;

        private void button4_Click(object sender, EventArgs e)
        {
            frmBrkSearch temp_form = new frmBrkSearch();
            temp_form.Owner = this;
            temp_form.flag = "frmFac";
            temp_form.ShowDialog();
            if (CloseFlag == false)
            {
                textBox13.Text = _strValue;
                textBox14.Text = _strValuebn;
            }

        }

        private void button5_Click(object sender, EventArgs e)
        {
            frmReinsSearch temp_form = new frmReinsSearch();
            temp_form.Owner = this;
            temp_form.flag = "frmFac";
            temp_form.ShowDialog();
            if (CloseFlag == false)
            {
                textBox15.Text = _strValuer;
                textBox16.Text = _strValuern;
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (listBox1.SelectedIndex != -1)
            {
                int total = listBox1.SelectedItems.Count;
                for (int x = 0; x < total; x++)
                {
                    list.Remove(listBox1.SelectedItem.ToString());
                    list1.Add(listBox1.SelectedItem.ToString());
                }
            }
            listBox1.DataSource = null;
            listBox2.DataSource = null;
            listBox1.DataSource = list;
            listBox2.DataSource = list1;
        }

        private void button3_Click(object sender, EventArgs e)
        {
            listBox2.DataSource = null;
            list1.AddRange(list);
            listBox2.DataSource = list1;
            listBox1.DataSource = null;
            list.Clear();
        }

        private void button6_Click(object sender, EventArgs e)
        {
            if (listBox2.SelectedIndex != -1)
            {
                int total = listBox2.SelectedItems.Count;
                for (int x = 0; x < total; x++)
                {
                    list1.Remove(listBox2.SelectedItem.ToString());
                    list.Add(listBox2.SelectedItem.ToString());
                }
            }
            listBox1.DataSource = null;
            listBox2.DataSource = null;
            listBox1.DataSource = list;
            listBox2.DataSource = list1;
        }

        private void button7_Click(object sender, EventArgs e)
        {
            list.AddRange(list1);
            listBox1.DataSource = list;
            listBox2.DataSource = null;
            list1.Clear();
        }

        private void button8_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            ButtonControl();
            ButtonCountrolupdate();
            textBox8.Text = "";
        }

        private void textBox12_TextChanged(object sender, EventArgs e)
        {
            if (textBox12.Text == "")
            {
                dataGridView2["Seq#", dataGridView2.CurrentCell.RowIndex].Value = i;
                textBox12.Text = i.ToString();
            }
            else
            {
                Regex r = new Regex("^[0-9]{1,}$");
                if (!r.IsMatch(textBox12.Text))
                {
                    MessageBox.Show("Please input number!");
                }
                dataGridView2["Seq#", row].Value = int.Parse(textBox12.Text.ToString());
            }
        }

        private void textBox13_TextChanged(object sender, EventArgs e)
        {
            dataGridView2["fbrkr1", row].Value = textBox13.Text;
        }

        private void textBox15_TextChanged(object sender, EventArgs e)
        {
            dataGridView2["Reinsurer", row].Value = textBox15.Text;
        }

        void saveinfo()
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            DataTable dt = dataGridView2.DataSource as DataTable;
            if (dt != null)
            {

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    if (!checknewinfo(dt.Rows[i]["fctlid"].ToString().Trim()))
                    {
                        string str = "select * from xsysparm where fidtype ='mfbinsr'";
                        DataTable dt1 = new DataTable();
                        dt1 = operate.GetTable(str);
                        string fctlid_info = dt1.Rows[0]["fnxtid"].ToString();

                        if (dt.Rows[i]["fctlid"].ToString().Trim() != "")
                        {
                            string adddatestrc =
                                    "insert into mfbinsr(fctlid_1,fctlid,flogseq,freinsr,fbrkr1,fbrkge1,fbrkr2,fbrkge2,fbrkr3,fbrkge3,fshare) " +
                            "values('" + fctlid_a + "','" + fctlid_info + "','" + dt.Rows[i]["Seq#"].ToString() + "','" + dt.Rows[i]["Reinsurer"].ToString() + "','" + dt.Rows[i]["fbrkr1"].ToString() + "','0.0000','','0.0000','','0.0000','" + dt.Rows[i]["Share (%)"].ToString() + "')";
                            operate.OperateData(adddatestrc);

                            string updatestrc = "update xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='mfbinsr'";
                            operate.OperateData(updatestrc);
                        }
                    }
                    else
                    {
                        string updatedatestro =
                               "update mfbinsr set flogseq ='" + dt.Rows[i]["Seq#"].ToString() + "',freinsr ='" + dt.Rows[i]["Reinsurer"].ToString().Trim().Replace("'", "''") + "' ,fbrkr1 ='" + dt.Rows[i]["fbrkr1"].ToString().Trim().Replace("'", "''") + "',fshare ='" + dt.Rows[i]["Share (%)"].ToString().Trim().Replace("'", "''") + "' where fctlid='" + dt.Rows[i]["fctlid"].ToString() + "'";
                        operate.OperateData(updatedatestro);
                    }
                }
            }
        }

        void saveContact()
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }

            DataTable dt = dataGridView3.DataSource as DataTable;
            if (dt != null)
            {

                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    if (!checknewsec(dt.Rows[i]["fctlid"].ToString().Trim()))
                    {
                        string str = "select * from xsysparm where fidtype ='mfbsec'";
                        DataTable dt1 = new DataTable();
                        dt1 = operate.GetTable(str);
                        string fctlid_contact = dt1.Rows[0]["fnxtid"].ToString();

                        if (dt.Rows[i]["fctlid"].ToString().Trim() != "")
                        {
                            string adddatestrc =
                                    "insert into mfbsec(fctlid_1,fctlid,fid,ftylmt,ftimes,fscale1,fscale2,fcom,fgrnet,fbrkge,fgrnet2) " +
                            "values('" + fctlid_a + "','" + fctlid_contact + "','" + dt.Rows[i]["Seq#"].ToString() + "',CAST('" + dt.Rows[i]["Treaty Limit"].ToString().Replace(",", "").Replace(".0000", "") + "' as decimal(19,0)),0,CAST('" + dt.Rows[i]["fscale1"].ToString().Trim() + "' as decimal(10,4)),Cast('" + dt.Rows[i]["fscale2"].ToString().Trim() + "' as decimal(10,4)),0.0,'','0.0000','1')";
                            operate.OperateData(adddatestrc);

                            string updatestro = "update xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='mfbsec'";
                            operate.OperateData(updatestro);

                            for (int j = 0; j < listBox1.Items.Count; j++)
                            {
                                string str2 = "select * from xsysparm where fidtype ='mfbclass'";
                                DataTable dt2 = new DataTable();
                                dt2 = operate.GetTable(str2);
                                string fctlid_mtyclass = dt2.Rows[0]["fnxtid"].ToString();

                                string addmtyclass = "insert into mfbclass (fctlid_1,fctlid_2,fctlid,ftype,fclass,fsec,fdesc)" +
                                    "values('" + fctlid_a + "','" + fctlid_contact + "','" + fctlid_mtyclass + "','1','" + Regex.Replace(listBox1.Items[j].ToString(), @"/\(([^\(]*)\)/", "").Replace(" (SEC 1)", "").Replace(" (SEC 2)", "") + "','','" + listBox1.Items[j].ToString() + "')";
                                operate.OperateData(addmtyclass);

                                string updatestrc = "update xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='mfbclass'";
                                operate.OperateData(updatestrc);
                            }
                            for (int j = 0; j < listBox2.Items.Count; j++)
                            {
                                string str2 = "select * from xsysparm where fidtype ='mfbclass'";
                                DataTable dt2 = new DataTable();
                                dt2 = operate.GetTable(str2);
                                string fctlid_mtyclass = dt2.Rows[0]["fnxtid"].ToString();

                                string addmtyclass1 = "insert into mfbclass (fctlid_1,fctlid_2,fctlid,ftype,fclass,fsec,fdesc)" +
                                    "values('" + fctlid_a + "','" + fctlid_contact + "','" + fctlid_mtyclass + "','2','" + Regex.Replace(listBox2.Items[j].ToString(), @"/\(([^\(]*)\)/", "").Replace(" (SEC 1)", "").Replace(" (SEC 2)", "") + "','','" + listBox2.Items[j].ToString() + "')";
                                operate.OperateData(addmtyclass1);

                                string updatestrc1 = "update xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='mfbclass'";
                                operate.OperateData(updatestrc1);
                            }
                        }
                    }
                    else
                    {
                        string updatedatestro =
                               "update mfbsec set fid ='" + dt.Rows[i]["Seq#"].ToString().Trim() + "',ftylmt ='" + dt.Rows[i]["Treaty Limit"].ToString().Trim().Replace(",", "").Replace(".0000", "") + "',fscale1 ='" + dt.Rows[i]["fscale1"].ToString().Trim() + "',fscale2 ='" + dt.Rows[i]["fscale2"].ToString().Trim() + "' where fctlid='" + dt.Rows[i]["fctlid"].ToString().Trim() + "'";
                        operate.OperateData(updatedatestro);

                        for (int j = 0; j < listBox1.Items.Count; j++)
                        {
                            string updatemtyclass = "update mfbclass set ftype ='1' where fctlid_1='" + fctlid_a + "' and fctlid_2='" + dt.Rows[i]["fctlid"].ToString().Trim() + "' and fdesc ='" + listBox1.Items[j].ToString() + "'";
                            operate.OperateData(updatemtyclass);
                        }

                        for (int j = 0; j < listBox2.Items.Count; j++)
                        {
                            string updatemtyclass1 = "update mfbclass set ftype ='2' where fctlid_1='" + fctlid_a + "' and fctlid_2='" + dt.Rows[i]["fctlid"].ToString().Trim() + "' and fdesc ='" + listBox2.Items[j].ToString() + "'";
                            operate.OperateData(updatemtyclass1);
                        }
                    }
                }
            }
        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", "");
        }


    }
}

