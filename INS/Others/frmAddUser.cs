using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using INS.INSClass;
namespace INS
{
    public partial class frmAddUser : Form
    {
        public frmAddUser()
        {
            InitializeComponent();
        }
        DBConnect operate = new DBConnect();
        private void button2_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            if (txtUserName.Text.Trim() == "")
            {
                MessageBox.Show("Can't be Empty", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else
            {
                DES des = new DES();
                string str=
                    "select count(*) from tb_User where UserName ='" + txtUserName.Text.Trim() + "'";
                int i=operate.SelectData(str);
                if (i > 0)
                {
                    MessageBox.Show("Duplicated User", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                else
                {
                    string Addsql =
                        "insert into tb_User(UserName,UserPwd) values('" + txtUserName.Text.Trim() + "','" + des.encode(txtPsw.Text.Trim()) + "')";
                    if (operate.OperateData(Addsql) > 0)
                    {
                        MessageBox.Show("Have Added", "Show",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                txtUserName.Text = "";
                txtPsw.Text = "";
            }
        }
    }
}