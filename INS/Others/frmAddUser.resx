<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAMAEBAQAAEABAAoAQAANgAAABAQAAABAAgAaAUAAF4BAAAQEAAAAQAgAGgEAADGBgAAKAAAABAA
        AAAgAAAAAQAEAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///8A9PT0AAB7/wD4+PgAz8/PALS0
        tADZ2dkAdLTJAMXFxQDw8PAA6enpAODg4AC9vb0A8/PzAADE/wAAAGV7x5AAAABnoiI6vJAABrIiMzM8
        eQAHISIiOjx3YGpBIjMzO3WQbhEiMjLrx5AMQUIzMzvH0AkkFCI+q8UAAJsiIy48kAAAANWiM2AAAAAA
        D//zMAAAAAANIirQAAAAAAwiIqkAAAAA3iZiIlAAAABSZmYu0AAAAAaV3dYAAPAfAADABwAAgAMAAIAB
        AAAAAQAAAAEAAIABAACAAwAAwAcAAPAfAAD4HwAA+B8AAPgPAADwBwAA8AcAAPgPAAAoAAAAEAAAACAA
        AAABAAgAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP///wD09PQAAHv/AK6urgD29vYA2traAMbG
        xgDz8/MAvLy8AOjo6AD19fUAv7+/APv7+wDx8fEA09PTAPLy8gDPz88A8PDwANbW1gC2trYA/v7+AN/f
        3wC0tLQA6enpANHR0QDe3t4A+vr6ALe3twDb29sAAKr/AMfHxwDJyckAxMTEALKysgDg4OAA5OTkAOvr
        6wAAxv8A5+fnAOPj4wC7u7sA6urqAL6+vgDDw8MAwMDAAP39/QAAkf8A7+/vANnZ2QC1tbUA0NDQAADw
        /wDt7e0A5eXlAOHh4QCzs7MAxcXFAMzMzADIyMgAAN//AN3d3QAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcEQYnIxMHAAAAAAAAADITDgICAgMS
        GBYHAAAAABQKBQsCAwMDAwMoBiAAAAAGCxUFAgICAxADJAYPHAAiDhsBAgIDAwMDAwodDx8AFwguFQIC
        AwIDAhAlPQ87AAA2DQENCwMDAwMDKhYxKQAAIQIbAQ0CAgMIEhg3GQAAAAAHCgUFAgMCCAMaBwAAAAAA
        AAAJETUCAwMUAAAAAAAAAAAAADQ8Jh4vAwAAAAAAAAAAAAArAgICMAwAAAAAAAAAAAAAGgICAgIOOQAA
        AAAAAAAACQgCBAQCAgIzAAAAAAAAABkCBAQEBAIQCQAAAAAAAAAAFyw6LQwMOAAAAADwHwAAwAcAAIAD
        AACAAQAAAAEAAAABAACAAQAAgAMAAMAHAADwHwAA+B8AAPgfAAD4DwAA8AcAAPAHAAD4DwAAKAAAABAA
        AAAgAAAAAQAgAAAAAABABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALKyshi3t7f/z8/P/9ra
        2v/n5+f/4ODg/9bW1v/Gxsb/srKyfLKysggAAAAAAAAAAAAAAAAAAAAAsrKyB7W1tf/W1tb/8fHx//T0
        9P/09PT/9PT0/wB7///w8PD/6enp/9/f3//Gxsb/srKyHgAAAAAAAAAAAAAAALa2tv/o6Oj/9vb2//X1
        9f/09PT/AHv//wB7//8Ae///AHv//wB7///j4+P/2tra/8nJyf+ysrIWAAAAALKysiHa2tr/9fX1//7+
        /v/29vb/9PT0//T09P/09PT/AHv///Ly8v8Ae///5OTk/9ra2v/T09P/t7e3/wAAAACysrLT8fHx//r6
        +v//////9PT0//T09P8Ae///AHv//wB7//8Ae///AHv//+jo6P/b29v/09PT/8fHx/+ysrIEtLS0//Pz
        8//9/f3//v7+//T09P/09PT/AHv///T09P8Ae///9PT0//Ly8v/r6+v/3d3d/9PT0//IyMj/srKyBbKy
        sl3l5eX/+/v7///////7+/v/9fX1/wB7//8Ae///AHv//wB7//8Ae///6urq/9/f3//Z2dn/u7u7/wAA
        AACysrIFxMTE//T09P/6+vr///////v7+//09PT/9PT0/wB7///z8/P/8PDw/+np6f/h4eH/0dHR/7Ky
        shwAAAAAAAAAALKyshzGxsb/6Ojo//b29v/29vb/9PT0/wB7///09PT/8/Pz/wB7///e3t7/xsbG/7Ky
        sh4AAAAAAAAAAAAAAAAAAAAAsrKyB7KysnW8vLz/z8/P/+3t7f/09PT/AHv//wB7//+2trb/srKyQ7Ky
        sgMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADw//8A3///AMb//wCq//8Akf//AHv//wAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALKysgG+vr7/9PT0//T09P/09PT/7+/v/7+/
        v/+ysrIWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACysrI03t7e//T09P/09PT/9PT0//T0
        9P/x8fH/xcXF/7Kysh4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAvLy8//Pz8//09PT/rq6u/66u
        rv/09PT/9PT0//T09P/Q0ND/srKyCgAAAAAAAAAAAAAAAAAAAAAAAAAAsrKyDNHR0f/09PT/rq6u/66u
        rv+urq7/rq6u//T09P/y8vL/vLy8/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACysrIetLS0/8PD
        w//MzMz/wMDA/7+/v/+/v7//s7Oz/7KyshoAAAAAAAAAAAAAAADwHwAAwAcAAIADAACAAQAAAAEAAAAB
        AACAAQAAgAMAAMAHAADwHwAA+B8AAPgfAAD4DwAA8AcAAPAHAAD4DwAA
</value>
  </data>
</root>