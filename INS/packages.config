<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="BouncyCastle" version="1.8.6.1" targetFramework="net461" />
  <package id="Common.Logging" version="3.4.1" targetFramework="net461" />
  <package id="Common.Logging.Core" version="3.4.1" targetFramework="net461" />
  <package id="CrystalReports.Engine" version="13.0.4000" targetFramework="net461" />
  <package id="CrystalReports.ReportAppServer.ClientDoc" version="13.0.4000" targetFramework="net461" />
  <package id="CrystalReports.ReportAppServer.CommLayer" version="13.0.4000" targetFramework="net461" />
  <package id="CrystalReports.ReportAppServer.CommonControls" version="13.0.4000" targetFramework="net461" />
  <package id="CrystalReports.ReportAppServer.CommonObjectModel" version="13.0.4000" targetFramework="net461" />
  <package id="CrystalReports.ReportAppServer.Controllers" version="13.0.4000" targetFramework="net461" />
  <package id="CrystalReports.ReportAppServer.CubeDefModel" version="13.0.4000" targetFramework="net461" />
  <package id="CrystalReports.ReportAppServer.DataDefModel" version="13.0.4000" targetFramework="net461" />
  <package id="CrystalReports.ReportAppServer.DataSetConversion" version="13.0.4000" targetFramework="net461" />
  <package id="CrystalReports.ReportAppServer.ObjectFactory" version="13.0.4000" targetFramework="net461" />
  <package id="CrystalReports.ReportAppServer.Prompting" version="13.0.4000" targetFramework="net461" />
  <package id="CrystalReports.ReportAppServer.ReportDefModel" version="13.0.4000" targetFramework="net461" />
  <package id="CrystalReports.ReportAppServer.XmlSerialize" version="13.0.4000" targetFramework="net461" />
  <package id="CrystalReports.Shared" version="13.0.4000" targetFramework="net461" />
  <package id="itext7" version="7.1.15" targetFramework="net461" />
  <package id="iTextSharp" version="********" targetFramework="net461" />
  <package id="log4net" version="1.2.10" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.CSharp" version="4.0.1" targetFramework="net461" />
  <package id="Microsoft.Office.Interop.Word" version="15.0.4797.1003" targetFramework="net461" />
  <package id="Newtonsoft.Json" version="9.0.1" targetFramework="net40" requireReinstallation="True" />
  <package id="Portable.BouncyCastle" version="1.8.9" targetFramework="net461" />
  <package id="RestSharp" version="106.6.10" targetFramework="net461" />
</packages>