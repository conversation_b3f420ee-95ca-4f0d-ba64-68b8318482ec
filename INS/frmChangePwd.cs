using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Data.SqlClient;

using InsEnvironment;
using INS.INSClass;

namespace INS
{
    public partial class frmChangePwd : Form
    {
        public frmChangePwd()
        {
            InitializeComponent();
            pswControl();
        }

        DBConnect operate = new DBConnect();
        SqlConnection conn = DBConnect.dbConn;

        public string name;
        public string len;
        public int life;
        public string cyle;
        public string flag="";

        private int minPasslen;
        private int maxPasslen;
        private int passLife;
        private int passCycle;

        void pswControl()
        {
            minPasslen = CompInfo.GetPassLen();
            maxPasslen = 10;
            passLife = CompInfo.GetPasslife();
            passCycle = CompInfo.GetPassCyle();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            string oldpwd  = txtOldPwd.Text.Trim();
            string newpwd = txtNewPwd.Text.Trim();
            string newpwd2 = txtNewPwd2.Text.Trim();

            String sqlString = "select * from muser where Fcode = '{0}'";
            sqlString = string.Format(sqlString,name);
            SqlCommand cmd = new SqlCommand(sqlString, conn);
            SqlDataReader sdr = cmd.ExecuteReader();

            sdr.Read();
            cmd.Dispose();

            if (!sdr.HasRows)
            {
                MessageBox.Show("User does not exist!", "Warning",
                                 MessageBoxButtons.OK, MessageBoxIcon.Information);

                sdr.Close();
                conn.Close();
                this.Close();
  
                return;
            }

            if (oldpwd.Length < maxPasslen)
                oldpwd = oldpwd.PadRight(maxPasslen, ' ');
            else
                oldpwd = oldpwd.Substring(0, maxPasslen);

            if (UtilityFunc.EnCode_Str(oldpwd, true) != sdr["Fpassword"].ToString())
            {
                MessageBox.Show("Invalid current password!", "Warning",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);

                sdr.Close();

                txtOldPwd.Text = "";
                txtNewPwd.Text = "";
                txtNewPwd2.Text = "";

                txtOldPwd.Focus();

                return;
            }

            if (newpwd != newpwd2)
            {
                MessageBox.Show("Mismatched new password entries!", "Warning",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);

                sdr.Close();

                txtOldPwd.Text = "";
                txtNewPwd.Text = "";
                txtNewPwd2.Text = "";

                txtOldPwd.Focus();

                return;
            }

            if (newpwd.Contains(" "))
            {
                MessageBox.Show("Invalid new password!", "Warning",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);

                sdr.Close();

                txtOldPwd.Text = "";
                txtNewPwd.Text = "";
                txtNewPwd2.Text = "";

                txtOldPwd.Focus();

                return;
            }

            if (newpwd.Length < minPasslen -1 || newpwd.Length > maxPasslen)
            {
                MessageBox.Show("Invalid new password length! Must be between "+minPasslen.ToString("G")+" to "
                                +maxPasslen.ToString("G")+ " characters", "Warning",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);

                sdr.Close();

                txtOldPwd.Text = "";
                txtNewPwd.Text = "";
                txtNewPwd2.Text = "";

                txtOldPwd.Focus();

                return;
            }

            if (newpwd.Length < maxPasslen)
                newpwd = newpwd.PadRight(maxPasslen, ' ');
            else
                newpwd = newpwd.Substring(0, maxPasslen);

            newpwd = UtilityFunc.EnCode_Str(newpwd, true);
            string passhis = sdr["Fpasshis"].ToString().Substring(0,maxPasslen*passCycle-1);

            if (passhis.Contains(newpwd))
            {
                MessageBox.Show("Recurrent password is not allowed!", "Warning",
                                 MessageBoxButtons.OK, MessageBoxIcon.Information);

                sdr.Close();

                txtOldPwd.Text = "";
                txtNewPwd.Text = "";
                txtNewPwd2.Text = "";

                txtOldPwd.Focus();

                return;
            }

            int strLength = newpwd2.Length;
            Boolean contains_alpha = false, contains_num = false, legal_str = false;

            Int32 ascii_no;

            for (int i = 0; i != strLength; i++)
            {
                ascii_no = (int) newpwd2[i];

                if (!contains_alpha &&
                    ((ascii_no >= (int)'a' && ascii_no <= (int)'z') || (ascii_no >= (int)'A' && ascii_no <= (int)'Z')))
                    contains_alpha = true;

                if (!contains_num && (ascii_no >= (int)'0' && ascii_no <= (int)'9'))
                    contains_num = true;

                if ((ascii_no >= (int)'0' && ascii_no <= (int)'9') ||
                    (ascii_no >= (int)'a' && ascii_no <= (int)'z') ||
                    (ascii_no >= (int)'A' && ascii_no <= (int)'Z'))
                    legal_str = true;
                else
                    legal_str = false;

                if (!legal_str)
                    break;
            }

            if (!contains_alpha || !contains_num || !legal_str)
            {
                MessageBox.Show("Password must consists of alpha and numeric characters!", "Warning",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);

                sdr.Close();

                txtOldPwd.Text = "";
                txtNewPwd.Text = "";
                txtNewPwd2.Text = "";

                txtOldPwd.Focus();

                return;
            }

            sdr.Close();
            passhis = newpwd + passhis.Substring(0, maxPasslen * (passCycle - 1) - 1);
            
            DateTime newExpiry = Convert.ToDateTime(DateTime.Now.ToShortDateString());

            newExpiry = newExpiry.AddDays(passLife-1);

            SqlCommand sqlComm = new SqlCommand();
            sqlComm = conn.CreateCommand();
            sqlComm.CommandText = @"UPDATE muser SET Fpassword = @paramNewpass, Fpasshis = @paramPasshis, Fvdate = @paramNewExpiry
                                    WHERE Fcode=@paramName";

            sqlComm.Parameters.Add("@paramNewpass", SqlDbType.VarChar);
            sqlComm.Parameters["@paramNewpass"].Value = newpwd;

            sqlComm.Parameters.Add("@paramPasshis", SqlDbType.VarChar);
            sqlComm.Parameters["@paramPasshis"].Value = passhis;

            sqlComm.Parameters.Add("@paramNewExpiry", SqlDbType.DateTime);
            sqlComm.Parameters["@paramNewExpiry"].Value = newExpiry;
            
            sqlComm.Parameters.Add("@paramName", SqlDbType.VarChar);
            sqlComm.Parameters["@paramName"].Value = name;

            sqlComm.ExecuteNonQuery();
            sqlComm.Dispose();

            MessageBox.Show("Successful password change!", "Warning",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
            this.Close();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (flag == "changepsw")
            {
                FrmMain1 Main = new FrmMain1();
                Main.User = name;
                Main.ShowDialog();
            }

            this.Close();

        }
    }
}