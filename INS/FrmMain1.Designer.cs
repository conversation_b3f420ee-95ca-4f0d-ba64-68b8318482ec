namespace INS
{
    partial class FrmMain1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.TreeNode treeNode1 = new System.Windows.Forms.TreeNode("Direct Policy");
            System.Windows.Forms.TreeNode treeNode2 = new System.Windows.Forms.TreeNode("Co Insurance");
            System.Windows.Forms.TreeNode treeNode3 = new System.Windows.Forms.TreeNode("RI Inward");
            System.Windows.Forms.TreeNode treeNode4 = new System.Windows.Forms.TreeNode("Direct Policy");
            System.Windows.Forms.TreeNode treeNode5 = new System.Windows.Forms.TreeNode("Co Insurance");
            System.Windows.Forms.TreeNode treeNode6 = new System.Windows.Forms.TreeNode("RI Inward");
            System.Windows.Forms.TreeNode treeNode7 = new System.Windows.Forms.TreeNode("Inquiry", new System.Windows.Forms.TreeNode[] {
            treeNode4,
            treeNode5,
            treeNode6});
            System.Windows.Forms.TreeNode treeNode8 = new System.Windows.Forms.TreeNode("Report");
            System.Windows.Forms.TreeNode treeNode9 = new System.Windows.Forms.TreeNode("Expiry Notice");
            System.Windows.Forms.TreeNode treeNode10 = new System.Windows.Forms.TreeNode("Auto Generate", new System.Windows.Forms.TreeNode[] {
            treeNode9});
            System.Windows.Forms.TreeNode treeNode11 = new System.Windows.Forms.TreeNode("Dr/Cr Notes");
            System.Windows.Forms.TreeNode treeNode12 = new System.Windows.Forms.TreeNode("Business", new System.Windows.Forms.TreeNode[] {
            treeNode1,
            treeNode2,
            treeNode3,
            treeNode7,
            treeNode8,
            treeNode10,
            treeNode11});
            System.Windows.Forms.TreeNode treeNode13 = new System.Windows.Forms.TreeNode("Direct Claims");
            System.Windows.Forms.TreeNode treeNode14 = new System.Windows.Forms.TreeNode("Co Insurance");
            System.Windows.Forms.TreeNode treeNode15 = new System.Windows.Forms.TreeNode("RI Claims");
            System.Windows.Forms.TreeNode treeNode16 = new System.Windows.Forms.TreeNode("Direct & Co In");
            System.Windows.Forms.TreeNode treeNode17 = new System.Windows.Forms.TreeNode("RI Inward");
            System.Windows.Forms.TreeNode treeNode18 = new System.Windows.Forms.TreeNode("Inquiry", new System.Windows.Forms.TreeNode[] {
            treeNode16,
            treeNode17});
            System.Windows.Forms.TreeNode treeNode19 = new System.Windows.Forms.TreeNode("Report");
            System.Windows.Forms.TreeNode treeNode20 = new System.Windows.Forms.TreeNode("Dr/Cr Notes");
            System.Windows.Forms.TreeNode treeNode21 = new System.Windows.Forms.TreeNode("Cover Letter");
            System.Windows.Forms.TreeNode treeNode22 = new System.Windows.Forms.TreeNode("Claims", new System.Windows.Forms.TreeNode[] {
            treeNode13,
            treeNode14,
            treeNode15,
            treeNode18,
            treeNode19,
            treeNode20,
            treeNode21});
            System.Windows.Forms.TreeNode treeNode23 = new System.Windows.Forms.TreeNode("Misc Dr/Cr Notes");
            System.Windows.Forms.TreeNode treeNode24 = new System.Windows.Forms.TreeNode("Settlement Voucher");
            System.Windows.Forms.TreeNode treeNode25 = new System.Windows.Forms.TreeNode("Report");
            System.Windows.Forms.TreeNode treeNode26 = new System.Windows.Forms.TreeNode("Accounts", new System.Windows.Forms.TreeNode[] {
            treeNode23,
            treeNode24,
            treeNode25});
            System.Windows.Forms.TreeNode treeNode27 = new System.Windows.Forms.TreeNode("Management Rpt");
            System.Windows.Forms.TreeNode treeNode28 = new System.Windows.Forms.TreeNode("Attachment");
            System.Windows.Forms.TreeNode treeNode29 = new System.Windows.Forms.TreeNode("Exc Clauses");
            System.Windows.Forms.TreeNode treeNode30 = new System.Windows.Forms.TreeNode("Site");
            System.Windows.Forms.TreeNode treeNode31 = new System.Windows.Forms.TreeNode("Project");
            System.Windows.Forms.TreeNode treeNode32 = new System.Windows.Forms.TreeNode("LossAdjuster");
            System.Windows.Forms.TreeNode treeNode33 = new System.Windows.Forms.TreeNode("Solicitor");
            System.Windows.Forms.TreeNode treeNode34 = new System.Windows.Forms.TreeNode("Health Care");
            System.Windows.Forms.TreeNode treeNode35 = new System.Windows.Forms.TreeNode("Nature of Loss");
            System.Windows.Forms.TreeNode treeNode36 = new System.Windows.Forms.TreeNode("Sub Contractor");
            System.Windows.Forms.TreeNode treeNode37 = new System.Windows.Forms.TreeNode("Accident Type");
            System.Windows.Forms.TreeNode treeNode38 = new System.Windows.Forms.TreeNode("Job Nature");
            System.Windows.Forms.TreeNode treeNode39 = new System.Windows.Forms.TreeNode("Select Policy");
            System.Windows.Forms.TreeNode treeNode40 = new System.Windows.Forms.TreeNode("Producer");
            System.Windows.Forms.TreeNode treeNode41 = new System.Windows.Forms.TreeNode("Client");
            System.Windows.Forms.TreeNode treeNode42 = new System.Windows.Forms.TreeNode("RI Broker");
            System.Windows.Forms.TreeNode treeNode43 = new System.Windows.Forms.TreeNode("Reinsurer");
            System.Windows.Forms.TreeNode treeNode44 = new System.Windows.Forms.TreeNode("Ins Class");
            System.Windows.Forms.TreeNode treeNode45 = new System.Windows.Forms.TreeNode("Ins Interest");
            System.Windows.Forms.TreeNode treeNode46 = new System.Windows.Forms.TreeNode("Misc Code");
            System.Windows.Forms.TreeNode treeNode47 = new System.Windows.Forms.TreeNode("Charge Code");
            System.Windows.Forms.TreeNode treeNode48 = new System.Windows.Forms.TreeNode("GL A/C");
            System.Windows.Forms.TreeNode treeNode49 = new System.Windows.Forms.TreeNode("Surplus Treaty");
            System.Windows.Forms.TreeNode treeNode50 = new System.Windows.Forms.TreeNode("Fac Obligatory");
            System.Windows.Forms.TreeNode treeNode51 = new System.Windows.Forms.TreeNode("XOL Treaty");
            System.Windows.Forms.TreeNode treeNode52 = new System.Windows.Forms.TreeNode("Treaty SetUp", new System.Windows.Forms.TreeNode[] {
            treeNode49,
            treeNode50,
            treeNode51});
            System.Windows.Forms.TreeNode treeNode53 = new System.Windows.Forms.TreeNode("IA Levy");
            System.Windows.Forms.TreeNode treeNode54 = new System.Windows.Forms.TreeNode("GTFC");
            System.Windows.Forms.TreeNode treeNode55 = new System.Windows.Forms.TreeNode("ECILMB");
            System.Windows.Forms.TreeNode treeNode56 = new System.Windows.Forms.TreeNode("ECIIB");
            System.Windows.Forms.TreeNode treeNode57 = new System.Windows.Forms.TreeNode("IFS");
            System.Windows.Forms.TreeNode treeNode58 = new System.Windows.Forms.TreeNode("FFS");
            System.Windows.Forms.TreeNode treeNode59 = new System.Windows.Forms.TreeNode("Levy Rate Setup", new System.Windows.Forms.TreeNode[] {
            treeNode53,
            treeNode54,
            treeNode55,
            treeNode56,
            treeNode57,
            treeNode58});
            System.Windows.Forms.TreeNode treeNode60 = new System.Windows.Forms.TreeNode("Department");
            System.Windows.Forms.TreeNode treeNode61 = new System.Windows.Forms.TreeNode("Master", new System.Windows.Forms.TreeNode[] {
            treeNode28,
            treeNode29,
            treeNode30,
            treeNode31,
            treeNode32,
            treeNode33,
            treeNode34,
            treeNode35,
            treeNode36,
            treeNode37,
            treeNode38,
            treeNode39,
            treeNode40,
            treeNode41,
            treeNode42,
            treeNode43,
            treeNode44,
            treeNode45,
            treeNode46,
            treeNode47,
            treeNode48,
            treeNode52,
            treeNode59,
            treeNode60});
            this.menuStrip1 = new System.Windows.Forms.MenuStrip();
            this.fileToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.helpToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.addUserToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.systemModeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.checkDataToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.importProjectInfToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.logOutToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.treeView1 = new System.Windows.Forms.TreeView();
            this.panel1 = new System.Windows.Forms.Panel();
            this.menuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // menuStrip1
            // 
            this.menuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.fileToolStripMenuItem,
            this.helpToolStripMenuItem,
            this.logOutToolStripMenuItem});
            this.menuStrip1.Location = new System.Drawing.Point(0, 0);
            this.menuStrip1.Name = "menuStrip1";
            this.menuStrip1.Size = new System.Drawing.Size(880, 24);
            this.menuStrip1.TabIndex = 2;
            this.menuStrip1.Text = "menuStrip1";
            // 
            // fileToolStripMenuItem
            // 
            this.fileToolStripMenuItem.Name = "fileToolStripMenuItem";
            this.fileToolStripMenuItem.Size = new System.Drawing.Size(73, 20);
            this.fileToolStripMenuItem.Text = "Password";
            this.fileToolStripMenuItem.Click += new System.EventHandler(this.fileToolStripMenuItem_Click);
            // 
            // helpToolStripMenuItem
            // 
            this.helpToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.addUserToolStripMenuItem,
            this.systemModeToolStripMenuItem,
            this.checkDataToolStripMenuItem,
            this.importProjectInfToolStripMenuItem});
            this.helpToolStripMenuItem.Enabled = false;
            this.helpToolStripMenuItem.Name = "helpToolStripMenuItem";
            this.helpToolStripMenuItem.Size = new System.Drawing.Size(113, 20);
            this.helpToolStripMenuItem.Text = "System Maintain";
            // 
            // addUserToolStripMenuItem
            // 
            this.addUserToolStripMenuItem.Name = "addUserToolStripMenuItem";
            this.addUserToolStripMenuItem.Size = new System.Drawing.Size(173, 22);
            this.addUserToolStripMenuItem.Text = "Edit User";
            this.addUserToolStripMenuItem.Click += new System.EventHandler(this.addUserToolStripMenuItem_Click);
            // 
            // systemModeToolStripMenuItem
            // 
            this.systemModeToolStripMenuItem.Name = "systemModeToolStripMenuItem";
            this.systemModeToolStripMenuItem.Size = new System.Drawing.Size(173, 22);
            this.systemModeToolStripMenuItem.Text = "System Mode";
            this.systemModeToolStripMenuItem.Click += new System.EventHandler(this.systemModeToolStripMenuItem_Click);
            // 
            // checkDataToolStripMenuItem
            // 
            this.checkDataToolStripMenuItem.Name = "checkDataToolStripMenuItem";
            this.checkDataToolStripMenuItem.Size = new System.Drawing.Size(173, 22);
            this.checkDataToolStripMenuItem.Text = "Check Data";
            // 
            // importProjectInfToolStripMenuItem
            // 
            this.importProjectInfToolStripMenuItem.Name = "importProjectInfToolStripMenuItem";
            this.importProjectInfToolStripMenuItem.Size = new System.Drawing.Size(173, 22);
            this.importProjectInfToolStripMenuItem.Text = "Import Project Inf";
            // 
            // logOutToolStripMenuItem
            // 
            this.logOutToolStripMenuItem.Name = "logOutToolStripMenuItem";
            this.logOutToolStripMenuItem.Size = new System.Drawing.Size(63, 20);
            this.logOutToolStripMenuItem.Text = "LogOut";
            this.logOutToolStripMenuItem.Click += new System.EventHandler(this.logOutToolStripMenuItem_Click);
            // 
            // treeView1
            // 
            this.treeView1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.treeView1.FullRowSelect = true;
            this.treeView1.HideSelection = false;
            this.treeView1.Location = new System.Drawing.Point(0, 23);
            this.treeView1.Name = "treeView1";
            treeNode1.Name = "FrmBDirect";
            treeNode1.Text = "Direct Policy";
            treeNode2.Name = "FrmBCOInsur";
            treeNode2.Text = "Co Insurance";
            treeNode3.Name = "FrmBRII";
            treeNode3.Text = "RI Inward";
            treeNode4.Name = "FrmDirectInquiry";
            treeNode4.Text = "Direct Policy";
            treeNode5.Name = "FrmCOInquiry";
            treeNode5.Text = "Co Insurance";
            treeNode6.Name = "FrmRIInquiry";
            treeNode6.Text = "RI Inward";
            treeNode7.ForeColor = System.Drawing.Color.Black;
            treeNode7.Name = "BNode3";
            treeNode7.Text = "Inquiry";
            treeNode8.Name = "FrmBReport";
            treeNode8.Text = "Report";
            treeNode9.Name = "undauto";
            treeNode9.Text = "Expiry Notice";
            treeNode10.Name = "BNode5";
            treeNode10.Text = "Auto Generate";
            treeNode11.Name = "FrmBNotes";
            treeNode11.Text = "Dr/Cr Notes";
            treeNode12.Name = "Node0";
            treeNode12.Text = "Business";
            treeNode13.Name = "FrmCDirect";
            treeNode13.Text = "Direct Claims";
            treeNode14.Name = "FrmCCOInsur";
            treeNode14.Text = "Co Insurance";
            treeNode15.Name = "FrmCRII";
            treeNode15.Text = "RI Claims";
            treeNode16.Name = "DirectClaimInquiry";
            treeNode16.Text = "Direct & Co In";
            treeNode17.Name = "RIClaimInquiry";
            treeNode17.Text = "RI Inward";
            treeNode18.Name = "Node14";
            treeNode18.Text = "Inquiry";
            treeNode19.Name = "FrmCReport";
            treeNode19.Text = "Report";
            treeNode20.Name = "FrmCNotes";
            treeNode20.Text = "Dr/Cr Notes";
            treeNode21.Name = "tcvrlet";
            treeNode21.Text = "Cover Letter";
            treeNode22.Name = "Node1";
            treeNode22.Text = "Claims";
            treeNode23.Name = "tginvh";
            treeNode23.Text = "Misc Dr/Cr Notes";
            treeNode24.Name = "tpyset";
            treeNode24.Text = "Settlement Voucher";
            treeNode25.Name = "FrmAReport";
            treeNode25.Text = "Report";
            treeNode26.Name = "Node2";
            treeNode26.Text = "Accounts";
            treeNode27.Name = "Node0";
            treeNode27.Text = "Management Rpt";
            treeNode28.Name = "frmAtt";
            treeNode28.Text = "Attachment";
            treeNode29.Name = "frmExcessClause";
            treeNode29.Text = "Exc Clauses";
            treeNode30.Name = "frmSite";
            treeNode30.Text = "Site";
            treeNode31.Name = "frmProject";
            treeNode31.Text = "Project";
            treeNode32.Name = "frmLossAdjuster";
            treeNode32.Text = "LossAdjuster";
            treeNode33.Name = "frmSolicitor";
            treeNode33.Text = "Solicitor";
            treeNode34.Name = "frmHealth";
            treeNode34.Text = "Health Care";
            treeNode35.Name = "frmNatureLoss";
            treeNode35.Text = "Nature of Loss";
            treeNode36.Name = "frmSubContractor";
            treeNode36.Text = "Sub Contractor";
            treeNode37.Name = "frmAccident";
            treeNode37.Text = "Accident Type";
            treeNode38.Name = "frmJobNature";
            treeNode38.Text = "Job Nature";
            treeNode39.Name = "frmSelectPolicy";
            treeNode39.Text = "Select Policy";
            treeNode40.Name = "frmProducer";
            treeNode40.Text = "Producer";
            treeNode41.Name = "frmClient";
            treeNode41.Text = "Client";
            treeNode42.Name = "frmBroker";
            treeNode42.Text = "RI Broker";
            treeNode43.Name = "frmReinsurer";
            treeNode43.Text = "Reinsurer";
            treeNode44.Name = "frmInsClass";
            treeNode44.Text = "Ins Class";
            treeNode45.Name = "frmInsInterest";
            treeNode45.Text = "Ins Interest";
            treeNode46.Name = "frmMiscCode";
            treeNode46.Text = "Misc Code";
            treeNode47.Name = "frmChargeCode";
            treeNode47.Text = "Charge Code";
            treeNode48.Name = "frmGL";
            treeNode48.Text = "GL A/C";
            treeNode49.Name = "frmSurplus";
            treeNode49.Text = "Surplus Treaty";
            treeNode50.Name = "frmFac";
            treeNode50.Text = "Fac Obligatory";
            treeNode51.Name = "frmXOL";
            treeNode51.Text = "XOL Treaty";
            treeNode52.Name = "frmTreaty";
            treeNode52.Text = "Treaty SetUp";
            treeNode53.Name = "ialevy";
            treeNode53.Text = "IA Levy";
            treeNode54.Name = "Node4";
            treeNode54.Text = "GTFC";
            treeNode55.Name = "Node5";
            treeNode55.Text = "ECILMB";
            treeNode56.Name = "Node6";
            treeNode56.Text = "ECIIB";
            treeNode57.Name = "Node7";
            treeNode57.Text = "IFS";
            treeNode58.Name = "Node8";
            treeNode58.Text = "FFS";
            treeNode59.Name = "levy";
            treeNode59.Text = "Levy Rate Setup";
            treeNode60.Name = "frmDept";
            treeNode60.Text = "Department";
            treeNode61.Name = "Master";
            treeNode61.Text = "Master";
            this.treeView1.Nodes.AddRange(new System.Windows.Forms.TreeNode[] {
            treeNode12,
            treeNode22,
            treeNode26,
            treeNode27,
            treeNode61});
            this.treeView1.Size = new System.Drawing.Size(168, 651);
            this.treeView1.TabIndex = 3;
            this.treeView1.AfterSelect += new System.Windows.Forms.TreeViewEventHandler(this.treeView1_AfterSelect);
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.Location = new System.Drawing.Point(167, 24);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(713, 642);
            this.panel1.TabIndex = 4;
            // 
            // FrmMain1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(880, 666);
            this.Controls.Add(this.panel1);
            this.Controls.Add(this.treeView1);
            this.Controls.Add(this.menuStrip1);
            this.IsMdiContainer = true;
            this.MainMenuStrip = this.menuStrip1;
            this.Name = "FrmMain1";
            this.Tag = "";
            this.Text = "China Overseas Insurance Limited - Version 2014";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.Load += new System.EventHandler(this.FrmMain1_Load);
            this.menuStrip1.ResumeLayout(false);
            this.menuStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.MenuStrip menuStrip1;
        private System.Windows.Forms.TreeView treeView1;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.ToolStripMenuItem fileToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem helpToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem logOutToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem addUserToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem systemModeToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem checkDataToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem importProjectInfToolStripMenuItem;



    }
}