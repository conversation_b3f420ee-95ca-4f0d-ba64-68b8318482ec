using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;

namespace INS
{
    public partial class frmfinvno : Form
    {
        public frmfinvno()
        {
            InitializeComponent();
            FillData("", "");
        }

        public frmfinvno(Forms.tcpyappr tcpyappr)
        {
            // TODO: Complete member initialization
            this.tcpyappr = tcpyappr;
            InitializeComponent();
            FillData("", "");
        }

        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "", fclmno = "", delfinvno = "";
        private Forms.tcpyappr tcpyappr;
        public DataTable dtappr = new DataTable();

        public void FillData(string order, string query)
        {
            string sql = "select distinct a.finvno as [Dr/Cr No.],a.fbilldate as [Doc Date],a.fpayee as [Payee],   " +
                            "case when a.famount < 0 then isnull(abs(a.famount),0) - isnull(abs(c.famount),0) else (-(isnull(abs(a.famount),0)) + isnull(abs(c.famount),0)) end as [Balance], " +
                            "isnull((a.famount),0) as [apprfamount],b.fctlid,b.flogseq, " +
                            "a.fpaytype,a.fpaynat,b.fnatdesc from opyinvh  a "+
                            "left join ocpay b on a.fctlid_py = b.fctlid  "+
                            "left join (select sum(famount) as famount, finvno from ocpydet group by finvno) c on a.finvno = c.finvno " +
                            "where a.fclmno ='" + fclmno + "' order by a.finvno";
            DataTable dt =  DBHelper.GetDataSet(sql);
            foreach (DataRow dr in dt.Rows)
            {
                foreach (DataRow drappr in dtappr.Rows)
                {
                    //if (drappr["finvno"].ToString().Trim() == dr["Dr/Cr No."].ToString().Trim() && Fct.sdFat(dr["Balance"]) != 0)
                    //{
                    //    if (Fct.sdFat(dr["apprfamount"]) < 0) { dr["Balance"] = Math.Abs(Fct.sdFat(dr["apprfamount"])) - Math.Abs(Fct.sdFat(drappr["famount"])); }
                    //    else { dr["Balance"] = -(Math.Abs(Fct.sdFat(dr["apprfamount"])) - Math.Abs(Fct.sdFat(drappr["famount"]))); }
                    //}
                    if (delfinvno.Trim() == dr["Dr/Cr No."].ToString().Trim())
                    {
                        if (Fct.sdFat(dr["apprfamount"]) < 0)
                        {
                            dr["Balance"] = Math.Abs(Fct.sdFat(dr["apprfamount"]));
                        }
                        else { dr["Balance"] = -Math.Abs(Fct.sdFat(dr["apprfamount"])); }
                    }
                }
            }
           
            dataGridView1.DataSource = dt;
            dataGridView1.Columns["Balance"].DefaultCellStyle.Format = "N4";
            dataGridView1.Columns["fpaytype"].Visible = false;
            dataGridView1.Columns["fpaynat"].Visible = false;
            dataGridView1.Columns["fnatdesc"].Visible = false;
            dataGridView1.Columns["apprfamount"].Visible = false;
            dataGridView1.Columns["fctlid"].Visible = false;
            dataGridView1.Columns["flogseq"].Visible = false;
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }
                if (dataGridView1.Rows[row].Cells["Dr/Cr No."].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Dr/Cr No."].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["Dr/Cr No."].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Doc Date"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Doc Date"].Value.ToString().Length != 0)
                    {
                        try
                        {
                            DateTime b = Convert.ToDateTime(dataGridView1.Rows[row].Cells["Doc Date"].Value.ToString().Trim());
                            label3.Text = b.ToString("yyyy.MM.dd");
                        }
                        catch { }
                    }
                }
                if (dataGridView1.Rows[row].Cells["Payee"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Payee"].Value.ToString().Length != 0)
                    {
                        label4.Text = dataGridView1.Rows[row].Cells["Payee"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["fpaytype"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["fpaytype"].Value.ToString().Length != 0)
                    {
                        label5.Text = dataGridView1.Rows[row].Cells["fpaytype"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["fpaynat"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["fpaynat"].Value.ToString().Length != 0)
                    {
                        label6.Text = dataGridView1.Rows[row].Cells["fpaynat"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["fnatdesc"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["fnatdesc"].Value.ToString().Length != 0)
                    {
                        label7.Text = dataGridView1.Rows[row].Cells["fnatdesc"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Balance"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Balance"].Value.ToString().Length != 0)
                    {
                        label8.Text = dataGridView1.Rows[row].Cells["Balance"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["fctlid"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["fctlid"].Value.ToString().Length != 0)
                    {
                        label9.Text = dataGridView1.Rows[row].Cells["fctlid"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["flogseq"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["flogseq"].Value.ToString().Length != 0)
                    {
                        label10.Text = dataGridView1.Rows[row].Cells["flogseq"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            tcpyappr.finvnoValue = textBox1.Text.ToString().Trim();
            tcpyappr.docdateValue = label3.Text.ToString().Trim();
            tcpyappr.payeeValue = label4.Text.ToString().Trim();
            tcpyappr.fpaytypeValue = label5.Text.ToString().Trim();
            tcpyappr.fpaynatValue = label6.Text.ToString().Trim();
            tcpyappr.fnatdescValue = label7.Text.ToString().Trim();
            tcpyappr.afamountValue = label8.Text.ToString().Trim();
            tcpyappr.fctlid_py = label9.Text.ToString().Trim();
            tcpyappr.fpyseqValue = label10.Text;
            tcpyappr.delfinvno = "";
            this.Close();
        }


        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }


    }
}
