using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;
using INS.Ctrl;

namespace INS
{
    public partial class frmExcessSearch : Form
    {
        public frmExcessSearch()
        {
            InitializeComponent();
            FillData("", "");
        }

        public frmExcessSearch(DirectExcess directExcess)
        {
            this.directExcess = directExcess;
            InitializeComponent();
        }

        public frmExcessSearch(EndorExcess endorExcess)
        {
            // TODO: Complete member initialization
            this.endorExcess = endorExcess;
            InitializeComponent();
        }

        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "";
        public string section = "";
        private String _fclass;
        private DirectExcess directExcess;
        private EndorExcess endorExcess;

        public String Fclass
        {
            set
            {
                _fclass = value;
            }
        }

        public void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            if (section == "1")
            {

                if (order == "" && query == "")
                {
                    a = new SqlDataAdapter("select b.fid as Clause#, b.fdesc as Desc#, b.flogseq as Seq#, fsid as Item#, fcontent  " +
                                            "from minsc a left join misec b on a.fctlid=b.fctlid_a  " +
                                            "left join (select b.fid,b.fctlid from minsc a " +
                                            "left join misec b on a.fctlid=b.fctlid_a  " +
                                            "where ftype =1 and fdoctype =2 ) c on c.fctlid = b.fctlid_1 " +
                                            "where fdoctype =2 and ftype =2 and a.fid ='" + _fclass + "' and c.fid ='SEC1' " +
                                            "order by b.flogseq", c);
                }
                else if (order != "")
                {
                    a = new SqlDataAdapter("select b.fid as Clause#, b.fdesc as Desc#, b.flogseq as Seq#, fsid as Item#, fcontent  " +
                                            "from minsc a left join misec b on a.fctlid=b.fctlid_a  " +
                                            "left join (select b.fid,b.fctlid from minsc a " +
                                            "left join misec b on a.fctlid=b.fctlid_a  " +
                                            "where ftype =1 and fdoctype =2 ) c on c.fctlid = b.fctlid_1 " +
                                            "where fdoctype =2 and ftype =2 and a.fid ='" + _fclass + "' and c.fid ='SEC1' " +
                                            "order by '" + order + "'", c);
                }
                else if (query != "")
                {
                    a = new SqlDataAdapter("select b.fid as Clause#, b.fdesc as Desc#, b.flogseq as Seq#, fsid as Item#, fcontent " +
                                            "from minsc a left join misec b on a.fctlid=b.fctlid_a  " +
                                            "left join (select b.fid,b.fctlid from minsc a " +
                                            "left join misec b on a.fctlid=b.fctlid_a  " +
                                            "where ftype =1 and fdoctype =2 ) c on c.fctlid = b.fctlid_1 " +
                                            "where fdoctype =2 and ftype =2 and a.fid ='" + _fclass + "' and c.fid ='SEC1' " +
                                            "AND b.fdesc = '" + query + "'", c);
                }
                else
                {
                    a = new SqlDataAdapter("select b.fid as Clause#, b.fdesc as Desc#, b.flogseq as Seq#, fsid as Item#, fcontent " +
                                            "from minsc a left join misec b on a.fctlid=b.fctlid_a  " +
                                            "left join (select b.fid,b.fctlid from minsc a " +
                                            "left join misec b on a.fctlid=b.fctlid_a  " +
                                            "where ftype =1 and fdoctype =2 ) c on c.fctlid = b.fctlid_1 " +
                                            "where fdoctype =2 and ftype =2 and a.fid ='" + _fclass + "' and c.fid ='SEC1' " +
                                            "AND b.fdesc = '" + query + "' order by flogseq", c);
                }
            }
            else
            {

                if (order == "" && query == "")
                {
                    a = new SqlDataAdapter("select b.fid as Clause#, b.fdesc as Desc#, b.flogseq as Seq#, fsid as Item#, fcontent " +
                                            "from minsc a left join misec b on a.fctlid=b.fctlid_a  " +
                                            "left join (select b.fid,b.fctlid from minsc a " +
                                            "left join misec b on a.fctlid=b.fctlid_a  " +
                                            "where ftype =1 and fdoctype =2 ) c on c.fctlid = b.fctlid_1 " +
                                            "where fdoctype =2 and ftype =2 and a.fid ='" + _fclass + "' and c.fid ='SEC2' " +
                                            "order by b.flogseq", c);
                }
                else if (order != "")
                {
                    a = new SqlDataAdapter("select b.fid as Clause#, b.fdesc as Desc#, b.flogseq as Seq#, fsid as Item#, fcontent " +
                                            "from minsc a left join misec b on a.fctlid=b.fctlid_a  " +
                                            "left join (select b.fid,b.fctlid from minsc a " +
                                            "left join misec b on a.fctlid=b.fctlid_a  " +
                                            "where ftype =1 and fdoctype =2 ) c on c.fctlid = b.fctlid_1 " +
                                            "where fdoctype =2 and ftype =2 and a.fid ='" + _fclass + "' and c.fid ='SEC2' " +
                                            "order by '" + order + "'", c);
                }
                else if (query != "")
                {
                    a = new SqlDataAdapter("select b.fid as Clause#, b.fdesc as Desc#, b.flogseq as Seq#, fsid as Item#, fcontent " +
                                            "from minsc a left join misec b on a.fctlid=b.fctlid_a  " +
                                            "left join (select b.fid,b.fctlid from minsc a " +
                                            "left join misec b on a.fctlid=b.fctlid_a  " +
                                            "where ftype =1 and fdoctype =2 ) c on c.fctlid = b.fctlid_1 " +
                                            "where fdoctype =2 and ftype =2 and a.fid ='" + _fclass + "' and c.fid ='SEC2' " +
                                            "AND b.fdesc = '" + query + "'", c);
                }
                else
                {
                    a = new SqlDataAdapter("select b.fid as Clause#, b.fdesc as Desc#, b.flogseq as Seq#, fsid as Item#, fcontent " +
                                            "from minsc a left join misec b on a.fctlid=b.fctlid_a  " +
                                            "left join (select b.fid,b.fctlid from minsc a " +
                                            "left join misec b on a.fctlid=b.fctlid_a  " +
                                            "where ftype =1 and fdoctype =2 ) c on c.fctlid = b.fctlid_1 " +
                                            "where fdoctype =2 and ftype =2 and a.fid ='" + _fclass + "' and c.fid ='SEC2' " +
                                            "AND b.fdesc = '" + query + "' order by flogseq", c);
                }
            }
            DataTable t = new DataTable();
            a.Fill(t);
            dataGridView1.DataSource = t;
            this.dataGridView1.Columns[2].Visible = false;
            this.dataGridView1.Columns[3].Visible = false;
            this.dataGridView1.Columns[4].Visible = false;
            c.Close();
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }
                if (dataGridView1.Rows[row].Cells["Clause#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Clause#"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["Clause#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["Desc#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Seq#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Seq#"].Value.ToString().Length != 0)
                    {
                        label4.Text = dataGridView1.Rows[row].Cells["Seq#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Item#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Item#"].Value.ToString().Length != 0)
                    {
                        label5.Text = dataGridView1.Rows[row].Cells["Item#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["fcontent"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["fcontent"].Value.ToString().Length != 0)
                    {
                        label6.Text = dataGridView1.Rows[row].Cells["fcontent"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            if (flag == "Direct")
            {
                if (section == "1")
                {
                    directExcess.ExcessValue = Fct.stFat(textBox1.Text.ToString());
                    directExcess.ExcessDesc = Fct.stFat(label3.Text.ToString());
                    directExcess.ExcessSeq = Fct.stFat(label4.Text.ToString());
                    directExcess.ExcessItem = Fct.stFat(label5.Text.ToString());
                    directExcess.ExcessContent = Fct.stFat(label6.Text.ToString());
                    this.Close();
                }
                else
                {
                    directExcess.ExcessValue2 = Fct.stFat(textBox1.Text.ToString());
                    directExcess.ExcessDesc2 = Fct.stFat(label3.Text.ToString());
                    directExcess.ExcessSeq2 = Fct.stFat(label4.Text.ToString());
                    directExcess.ExcessItem2 = Fct.stFat(label5.Text.ToString());
                    directExcess.ExcessContent2 = Fct.stFat(label6.Text.ToString());
                    this.Close();
                }

            }
            if (flag == "Endor")
            {
                if (section == "1")
                {
                    endorExcess.ExcessValue = Fct.stFat(textBox1.Text.ToString());
                    endorExcess.ExcessDesc = Fct.stFat(label3.Text.ToString());
                    endorExcess.ExcessSeq = Fct.stFat(label4.Text.ToString());
                    endorExcess.ExcessItem = Fct.stFat(label5.Text.ToString());
                    endorExcess.ExcessContent = Fct.stFat(label6.Text.ToString());
                    this.Close();
                }
                else
                {
                    endorExcess.ExcessValue2 = Fct.stFat(textBox1.Text.ToString());
                    endorExcess.ExcessDesc2 = Fct.stFat(label3.Text.ToString());
                    endorExcess.ExcessSeq2 = Fct.stFat(label4.Text.ToString());
                    endorExcess.ExcessItem2 = Fct.stFat(label5.Text.ToString());
                    endorExcess.ExcessContent2 = Fct.stFat(label6.Text.ToString());
                    this.Close();
                }

            }
        }


        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }


    }
}
