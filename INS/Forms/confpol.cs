using CrystalDecisions.Shared;
using INS.Business.objRpt;
using INS.Business.Report;
using INS.INSClass;
using RestSharp;
using Newtonsoft.Json;
using JsonSerializer = RestSharp.Serialization.Json.JsonSerializer;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading;
using System.Web;
using System.Windows.Forms;
using Newtonsoft.Json.Linq;
//FZ2YHY48
namespace INS.Business.SearchForm
{

    public class SSORoot
    {
        /// <summary>
        /// 
        /// </summary>
        public string encrypt_token { get; set; }

    }


    public class AccessRoot
    {
        /// <summary>
        /// 
        /// </summary>
        public string access_token { get; set; }

    }


    public class TokenRoot
    {
        /// <summary>
        /// 
        /// </summary>
        public string token { get; set; }

    }

    public class Root
    {
        /// <summary>
        /// 
        /// </summary>
        public string workflow_id { get; set; }
    }

    public class ExtraMap
    {
    }

    //public class Att
    //{
    //    public long id { get; set; }
    //    public int reference { get; set; }
    //    public int subReference { get; set; }
    //    public int category { get; set; }
    //    public int type { get; set; }
    //    public string filename { get; set; }
    //    public string mimeType { get; set; }
    //    public long createdate { get; set; }
    //    public int size { get; set; }
    //    public object description { get; set; }
    //    public long fileUrl { get; set; }
    //    public string extension { get; set; }
    //    public string icon { get; set; }
    //    public object genesisId { get; set; }
    //    public int sort { get; set; }
    //    public string officeTransformEnable { get; set; }
    //    public string v { get; set; }
    //    public ExtraMap extraMap { get; set; }
    //    public bool @new { get; set; }
    //}

    public class AttsItem
    {
        public int category { get; set; }
        public string createdate { get; set; }
        public string description { get; set; }
        public string extension { get; set; }
        public ExtraMap extraMap { get; set; }
        public long fileUrl { get; set; }
        public string filename { get; set; }
        public string genesisId { get; set; }
        public string icon { get; set; }
        public string id { get; set; }
        public string mimeType { get; set; }
        public bool @new { get; set; }
        public string officeTransformEnable { get; set; }
        public string reference { get; set; }
        public string size { get; set; }
        public int sort { get; set; }
        public string subReference { get; set; }
        public int type { get; set; }
        public string v { get; set; }
    }

    public class UploadRoot
    {
        public int n_a_s { get; set; }
        public List<AttsItem> atts { get; set; }
    }

    public partial class confpol : Form
    {
        private string authKey_ssoToken = "15630cdb650f2933f490a5c84045fc3605f7300d";
        private string user_id = "anna_miao";
        private string authKey_accessToken = "a7cdc68ab8ac2cf506850735470db36be959ef10";
        //private string TestauthKey_accessToken = "93b55df12e5772cbfd522621c08b4be45f204187";

        private string BASE_URL = "https://api.cohl.com";
        //public string REQUEST_URL = "https://api.cohl.com/auth/sso_encrypt_token";
        //public string ACCESS_URL = "https://api.cohl.com/auth/access_token";
        //public string REST_URL = "https://api.cohl.com/systems/a8/gettoken";
        //public string TestUPLOAD_URL = "http://a8test.csci.cohl.com/seeyon/rest/attachment?applicationCategory=0&extensions=&firstSave=true";
       // public string UPLOAD_URL = "https://i.cohl.com/seeyon/rest/attachment?applicationCategory=0&extensions=&firstSave=true";

        public string a8_token = "";
        public string sso_token = "";
        public string access_token = "";
        public long[] file_handle = new long[0];
        public string workflow_id = "";

        public string fctlid, autori = "";
        private DirectPolicy directPolicy;
        private Endorsement endorsement;
        Reinsurance reins = new Reinsurance();
        EndtReinsurance Endtreins = new EndtReinsurance();
        public string db = InsEnvironment.DataBase.GetDbm();
        private RIWard rIWard;
        private RIEndt rIEndt;
        private string rpDirectory = "M:\\Software II\\New INS Runtime\\Setup\\objRpt";
        //public Boolean ll_getno, ll_con;
        public String tranStatus;
        public string wl_fpolno;
        public Int32 optionNo = 0;
        DateTime time = DateTime.Now;

        public confpol()
        {
            InitializeComponent();
            Control();
        }

        public confpol(DirectPolicy directPolicy)
        {
            // TODO: Complete member initialization
            this.directPolicy = directPolicy;
            InitializeComponent();
            Control();
        }

        public confpol(RIWard rIWard)
        {
            // TODO: Complete member initialization
            this.rIWard = rIWard;
            InitializeComponent(); Control();
        }

        public confpol(Endorsement endorsement)
        {
            // TODO: Complete member initialization
            this.endorsement = endorsement;
            InitializeComponent(); Control();
        }


        public confpol(RIEndt rIEndt)
        {
            // TODO: Complete member initialization
            this.rIEndt = rIEndt;
            InitializeComponent(); Control();
        }

        public void Control()
        {
            if (tranStatus == "Pending")
            {
                radioButton1.Text = "Get Policy No. / Endorsement No.";
                radioButton2.Text = "Confirm Policy / Endorsement";
            }

            if (tranStatus == "Hold")
            {
                radioButton1.Text = "Confirm Policy / Endorsement";
                radioButton2.Text = "Cancel Policy / Endorsement";
            }

            radioButton1.Checked = true;

            String Sql = "select fpolno from polh where fctlid ='" + fctlid + "'";
            DataTable dt = DBHelper.GetDataSet(Sql);
            string ofpolno = "";
            if (dt != null && dt.Rows.Count > 0)
            {
                ofpolno = dt.Rows[0]["fpolno"].ToString().Trim();
            }
            if (ofpolno != "")
            {
                //checkBox3.Visible = true;
                //checkBox1.Visible = false;
            }
            else
            {
                //checkBox3.Visible = false;
                //checkBox1.Visible = true;
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            optionNo = radioButton1.Checked ? 1 : 2;
            optionNo = tranStatus == "Pending" ? optionNo : optionNo + 1;

            this.Close();
            return;
        }

        public void confirmPolicy(Int32 optionPath)
        {
            string fissdate = "", Class = "", fuwyr = "", fbus = "", ftype = "", fpolno = "", ofpolno = "", ofendt = "", ofconfirm = "", fctlid_new = "", Sql4 = "";
            String Sql = "select  convert(varchar, fissdate, 102) as fissdate,fclass, fuwyr, fbus, ftype, fpolno,fendtno,fconfirm from polh where fctlid ='" + fctlid + "'";
            
            ArrayList addsql = new ArrayList();
            DataTable dt5 = new DataTable();
            DataTable dt = DBHelper.GetDataSet(Sql);

            if (dt == null || dt.Rows.Count == 0)
                return;

            Class = dt.Rows[0]["fclass"].ToString().Trim();
            fuwyr = dt.Rows[0]["fuwyr"].ToString().Trim();
            DateTime b = DateTime.ParseExact(dt.Rows[0]["fissdate"].ToString().Trim(), "yyyy.MM.dd", CultureInfo.InvariantCulture);
            fissdate = b.ToString("yyyy.MM.dd");
            fissdate = DateTime.Now.ToString("yyyy.MM.dd");
            fbus = dt.Rows[0]["fbus"].ToString().Trim();
            ftype = dt.Rows[0]["ftype"].ToString().Trim();
            ofpolno = dt.Rows[0]["fpolno"].ToString().Trim();
            ofendt = dt.Rows[0]["fendtno"].ToString().Trim();
            ofconfirm = dt.Rows[0]["fconfirm"].ToString().Trim();

            String Sql1 = "", Sql2 = "";

            if (optionPath == 1 && ftype == "P" && ofpolno != "")
                return;

            if (optionPath == 1 && ftype == "E" && ofendt != "")
                return;

            if (optionPath == 2 && "3|4".Contains(ofconfirm))
                return;

            if (optionPath == 3 && "3|4".Contains(ofconfirm))
                return;

            if ("D|K".Contains(fbus) && ftype == "P" && ofpolno == "")
            {
                Sql1 = "select rtrim(fprefix) + rtrim(fnxtid) as polno from " + db + "xsysparm where fidtype='" + Class + "' AND fuy ='" + fuwyr + "'";
                Sql2 = "update " + db + "xsysparm  set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 4) where fidtype='" + Class + "' AND fuy ='" + fuwyr + "'";
            }

            if ("D|K".Contains(fbus) && ftype == "E" && ofendt == "")
            {
                Sql1 = "select rtrim(fprefix) + rtrim(fnxtid) as polno from " + db + "xsysparm where fidtype='ENDTNO' AND fuy ='" + fissdate.Substring(0, 4) + "'";
                Sql2 = "update " + db + "xsysparm  set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 4) where fidtype='ENDTNO' AND fuy ='" + fissdate.Substring(0, 4) + "'";
            }

            if (fbus == "R" && ftype == "P" && ofpolno == "")
            {
                Sql1 = "select rtrim(fprefix) + rtrim(fnxtid) as polno from " + db + "xsysparm where fidtype='" + fbus + Class + "' AND fuy ='" + fuwyr + "'";
                Sql2 = "update " + db + "xsysparm  set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 4) where fidtype='" + fbus + Class + "' AND fuy ='" + fuwyr + "'";
            }

            if (fbus == "R" && ftype == "E" && ofendt == "")
            {
                Sql1 = "select rtrim(fprefix) + rtrim(fnxtid) as polno from " + db + "xsysparm where fidtype='RENDTNO' AND fuy ='" + fissdate.Substring(0, 4) + "'";
                Sql2 = "update " + db + "xsysparm  set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 4) where fidtype='RENDTNO' AND fuy ='" + fissdate.Substring(0, 4) + "'";
            }

            DataTable dt1 = null;

            if (Sql1 != "") { dt1 = DBHelper.GetDataSet(Sql1); }
            else if (ftype == "P") { fpolno = ofpolno; }
            else if (ftype == "E") { fpolno = ofendt; }

            if (dt1 != null && dt1.Rows.Count > 0)
            {
                fpolno = dt1.Rows[0]["polno"].ToString().Trim();
            }

            if (fpolno == "")
                return;

            string upddatestr = "", upddatestr1 = "", upddatestr2 = "", upddatestr3 = "", upddatestr5 = "", upddatestr6 = "", upddatestr7 = "", upddatestr8 = "", upddatestr9 = "";

            if (optionPath == 1)
            {
                if (ftype == "E")
                {
                    wl_fpolno = fpolno;
                    upddatestr = "update polh set fendtno ='" + fpolno + "',fconfirm ='2'," + "fupduser= '" + InsEnvironment.LoginUser.GetUserCode() + "' ,fupddate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid='" + fctlid + "'";
                    if (Class == "CAR" || Class == "CSB" || Class == "MYP" || Class == "PAR")
                    {
                        upddatestr1 = "update oinsint set fendtno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                    }
                    if (Class == "EEC")
                    {
                        upddatestr1 = "update oinsint_b set fendtno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                    }
                    if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII" || Class == "GLF")
                    {
                        upddatestr1 = "update oinsint_c set fendtno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                    }
                    if (Class == "CPM")
                    {
                        upddatestr1 = "update oinsint_d set fendtno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                    }
                    if (Class == "PMP" || Class == "CMP")
                    {
                        upddatestr1 = "update oinsint_e set fendtno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                    }
                    upddatestr2 =
                       "update oinsex set fendtno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                    upddatestr3 =
                       "update oinsatt set fendtno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                    if (Class == "CGL" || Class == "FGP" || Class == "GLF" || Class == "MYP" || Class == "PAR")
                    {
                        upddatestr5 = "update oinsloc set fendtno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                    }
                    if (Class == "FGP")
                    {
                        upddatestr6 = "update oinsper set fendtno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                    }
                    upddatestr7 = "update orih set fendtno ='" + fpolno + "',fupduser= '" + InsEnvironment.LoginUser.GetUserCode() + "' ,fupddate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid_1='" + fctlid + "'";
                    if (Class == "CAR")
                    {
                        upddatestr8 = "update orihc set fendtno ='" + fpolno + "',fupduser= '" + InsEnvironment.LoginUser.GetUserCode() + "' ,fupddate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid_1='" + fctlid + "'";
                    }
                    if (Class == "PAR" || Class == "CPM")
                    {
                        upddatestr9 = "update oril set fendtno ='" + fpolno + "' where fctlid_e='" + fctlid + "'";
                    }
                }
                else
                {
                    wl_fpolno = fpolno;
                    upddatestr = "update polh set fpolno ='" + fpolno + "',fconfirm ='2'," + "fupduser= '" + InsEnvironment.LoginUser.GetUserCode() + "' ,fupddate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid='" + fctlid + "'";
                    if (Class == "CAR" || Class == "CSB" || Class == "MYP" || Class == "PAR")
                    {
                        upddatestr1 = "update oinsint set fpolno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                    }
                    if (Class == "EEC")
                    {
                        upddatestr1 = "update oinsint_b set fpolno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                    }
                    if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII" || Class == "GLF")
                    {
                        upddatestr1 = "update oinsint_c set fpolno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                    }
                    if (Class == "CPM")
                    {
                        upddatestr1 = "update oinsint_d set fpolno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                    }
                    if (Class == "PMP" || Class == "CMP")
                    {
                        upddatestr1 = "update oinsint_e set fpolno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                    }
                    upddatestr2 =
                       "update oinsex set fpolno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                    upddatestr3 =
                       "update oinsatt set fpolno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                    if (Class == "CGL" || Class == "FGP" || Class == "GLF" || Class == "MYP" || Class == "PAR")
                    {
                        upddatestr5 = "update oinsloc set fpolno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                    }
                    if (Class == "FGP")
                    {
                        upddatestr6 = "update oinsper set fpolno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                    }
                    upddatestr7 = "update orih set fpolno ='" + fpolno + "',fupduser= '" + InsEnvironment.LoginUser.GetUserCode() + "' ,fupddate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid_1='" + fctlid + "'";
                    if (Class == "CAR")
                    {
                        upddatestr8 = "update orihc set fpolno ='" + fpolno + "',fupduser= '" + InsEnvironment.LoginUser.GetUserCode() + "' ,fupddate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid_1='" + fctlid + "'";
                    }
                    if (Class == "PAR" || Class == "CPM")
                    {
                        upddatestr9 = "update oril set fpolno ='" + fpolno + "' where fctlid_e='" + fctlid + "'";
                    }
                }
            }


            if (optionPath == 2)
            {
                DialogResult myResult;
                myResult = MessageBox.Show("Are you really Confirm the Policy All information?", "Policy Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
                if (myResult == DialogResult.OK)
                {
                    if (ofpolno == "")
                    {
                        wl_fpolno = fpolno;
                        upddatestr = "update polh set fctldel ='', fissdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "',fpolno ='" + fpolno + "',fconfirm =3,fcnfuser= '" + InsEnvironment.LoginUser.GetUserCode() + "' ,fcnfdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid='" + fctlid + "'";
                        if (Class == "CAR" || Class == "CSB" || Class == "MYP" || Class == "PAR")
                        {
                            upddatestr1 = "update oinsint set fpolno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                        }
                        if (Class == "EEC")
                        {
                            upddatestr1 = "update oinsint_b set fpolno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                        }
                        if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII" || Class == "GLF")
                        {
                            upddatestr1 = "update oinsint_c set fpolno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                        }
                        if (Class == "CPM")
                        {
                            upddatestr1 = "update oinsint_d set fpolno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                        }
                        if (Class == "PMP" || Class == "CMP")
                        {
                            upddatestr1 = "update oinsint_e set fpolno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                        }
                        upddatestr2 =
                           "update oinsex set fpolno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                        upddatestr3 =
                           "update oinsatt set fpolno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                        if (Class == "CGL" || Class == "FGP" || Class == "GLF" || Class == "MYP" || Class == "PAR")
                        {
                            upddatestr5 = "update oinsloc set fpolno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                        }
                        if (Class == "FGP")
                        {
                            upddatestr6 = "update oinsper set fpolno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                        }
                        upddatestr7 = "update orih set fpolno ='" + fpolno + "',fstatus =3,fcnfuser= '" + InsEnvironment.LoginUser.GetUserCode() + "' ,fcnfdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid_1='" + fctlid + "'";
                        if (Class == "CAR")
                        {
                            upddatestr8 = "update orihc set fpolno ='" + fpolno + "',fstatus =3,fcnfuser= '" + InsEnvironment.LoginUser.GetUserCode() + "' ,fcnfdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid_1='" + fctlid + "'";
                        }
                        if (Class == "PAR" || Class == "CPM")
                        {
                            upddatestr9 = "update oril set fpolno ='" + fpolno + "' where fctlid_e='" + fctlid + "'";
                        }
                    }
                    else
                    {
                        if (ftype == "E")
                        {
                            wl_fpolno = fpolno;
                            upddatestr = "update polh set fctldel ='', fissdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "',fendtno ='" + fpolno + "',fconfirm =3,fcnfuser= '" + InsEnvironment.LoginUser.GetUserCode() + "' ,fcnfdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid='" + fctlid + "'";
                            if (Class == "CAR" || Class == "CSB" || Class == "MYP" || Class == "PAR")
                            {
                                upddatestr1 = "update oinsint set fendtno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                            }
                            if (Class == "EEC")
                            {
                                upddatestr1 = "update oinsint_b set fendtno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                            }
                            if (Class == "CGL" || Class == "CLL" || Class == "FGP" || Class == "PII" || Class == "GLF")
                            {
                                upddatestr1 = "update oinsint_c set fendtno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                            }
                            if (Class == "CPM")
                            {
                                upddatestr1 = "update oinsint_d set fendtno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                            }
                            if (Class == "PMP" || Class == "CMP")
                            {
                                upddatestr1 = "update oinsint_e set fendtno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                            }
                            upddatestr2 =
                               "update oinsex set fendtno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                            upddatestr3 =
                               "update oinsatt set fendtno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                            if (Class == "CGL" || Class == "FGP" || Class == "GLF" || Class == "MYP" || Class == "PAR")
                            {
                                upddatestr5 = "update oinsloc set fendtno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                            }
                            if (Class == "FGP")
                            {
                                upddatestr6 = "update oinsper set fendtno ='" + fpolno + "' where fctlid_1='" + fctlid + "'";
                            }
                            upddatestr7 = "update orih set fendtno ='" + fpolno + "',fstatus =3,fcnfuser= '" + InsEnvironment.LoginUser.GetUserCode() + "' ,fcnfdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid_1='" + fctlid + "'";
                            if (Class == "CAR")
                            {
                                upddatestr8 = "update orihc set fendtno ='" + fpolno + "',fstatus =3,fcnfuser= '" + InsEnvironment.LoginUser.GetUserCode() + "' ,fcnfdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid_1='" + fctlid + "'";
                            }
                            if (Class == "PAR" || Class == "CPM")
                            {
                                upddatestr9 = "update oril set fendtno ='" + fpolno + "' where fctlid_e='" + fctlid + "'";
                            }
                        }
                        else
                        {
                            wl_fpolno = ofpolno;
                            upddatestr = "update polh set fctldel ='',fissdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "',fconfirm =3 ,fcnfuser= '" + InsEnvironment.LoginUser.GetUserCode() + "' ,fcnfdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid='" + fctlid + "'";
                            upddatestr7 = "update orih set fstatus =3,fcnfuser= '" + InsEnvironment.LoginUser.GetUserCode() + "' ,fcnfdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid_1='" + fctlid + "'";
                            if (Class == "CAR")
                            {
                                upddatestr8 = "update orihc set fstatus =3,fcnfuser= '" + InsEnvironment.LoginUser.GetUserCode() + "' ,fcnfdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid_1='" + fctlid + "'";
                            }
                            if (Class == "PAR" || Class == "CPM")
                            {
                                upddatestr9 = "";
                            }
                        }
                    }
                }
            }


            if (optionPath == 3)
            {
                upddatestr = "update polh set fconfirm =4 where fctlid='" + fctlid + "'";
            }
            string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                SqlCommand command = connection.CreateCommand();
                SqlTransaction transaction;

                // Start a local transaction.
                transaction = connection.BeginTransaction("SampleTransaction");

                // Must assign both transaction object and connection 
                // to Command object for a pending local transaction
                command.Connection = connection;
                command.Transaction = transaction;

                try
                {
                    command.CommandText = upddatestr;
                    command.ExecuteNonQuery();
                    if (upddatestr1 != "")
                    {
                        command.CommandText = upddatestr1;
                        command.ExecuteNonQuery();
                    }
                    if (upddatestr2 != "")
                    {
                        command.CommandText = upddatestr2;
                        command.ExecuteNonQuery();
                    }
                    if (upddatestr3 != "")
                    {
                        command.CommandText = upddatestr3;
                        command.ExecuteNonQuery();
                    }
                    if (upddatestr5 != "")
                    {
                        command.CommandText = upddatestr5;
                        command.ExecuteNonQuery();
                    }
                    if (upddatestr6 != "")
                    {
                        command.CommandText = upddatestr6;
                        command.ExecuteNonQuery();
                    }
                    if (upddatestr7 != "")
                    {
                        command.CommandText = upddatestr7;
                        command.ExecuteNonQuery();
                    }
                    if (upddatestr8 != "")
                    {
                        command.CommandText = upddatestr8;
                        command.ExecuteNonQuery();
                    }
                    if (upddatestr9 != "")
                    {
                        command.CommandText = upddatestr9;
                        command.ExecuteNonQuery();
                    }
                    if (Sql2 != "")
                    {
                        command.CommandText = Sql2;
                        command.ExecuteNonQuery();
                    }
                    transaction.Commit();
                    if (optionPath == 2)
                    {
                        //insert into DR/CR
                        fctlid_new = Fct.NewId("OINVHID");
                        string Sql5 = "";
                        if (fbus == "R")
                        {
                            Sql5 = "select distinct a.fctlid as fctlid_1, rtrim(fbus) as fbus,rtrim(fbus)+'0100' as fmodule, rtrim(fcoins) as fcoins, case when rtrim(a.fpolno)='' then '" + fpolno.Trim() + "' else rtrim(a.fpolno) end fpolno,rtrim(a.fendtno) as fendtno,rtrim(fsrcref) as fsrcref,  " +
                                        "rtrim(fcedepol),rtrim(fcedeendt),rtrim(fuwyr),rtrim(a.fclass) as fclass,rtrim(a.fsclass) as fsclass, " +
                                        "case when rtrim(a.fclass)='EEC' then case when rtrim(a.fsclass) ='CONTRACTOR' then 'CR' else 'OT' end else '' end as facclass,rtrim(fsiteid),rtrim(finstall),rtrim(ftotinstal),  " +
                                        "'" + fctlid_new.Trim() + "' as fctlid,convert(VARCHAR(25),fissdate,120) as fissdate,convert(VARCHAR(25),finvdate,120) as finvdate,null as fbilldate,null as fbkdate,  " +
                                        "'' as fbordmon,convert(VARCHAR(25),fefffr,120) as fefffr, convert(VARCHAR(25),feffto,120) as feffto,rtrim(finvno) as finvno,case when rtrim(fprdr)<>'PRD001' then 'B' else 'R' end as fdbtrtype,  " +
                                        "case when fprdr<>'PRD001' then rtrim(fprdr) else rtrim(fclnt) end as fdbtr,rtrim(fprdr) as fprdr,rtrim(c.fdesc) as fpdesc,rtrim(fclnt) as fclnt,  " +
                                        "rtrim(fsclnt),rtrim(fpmcur),b.fgpm,b.ftdamt,fcomtype,b.fcomamt,b.fnpm,b.flvyamt1,b.flvyamt2,  " +
                                        "b.flvyamt3,b.flvyamta,'0.0000' as fadmfee,b.fnpm+b.flvyamt1+b.flvyamt2+b.flvyamt3+b.flvyamta as famount,ftd,fcom,fgrnet,0 as fcrterm,  " +
                                        "Reinsurance Facultative Inward'+char(13)+'Your Ref '+ case when fprdr<>'PRD001' then rtrim(fsrcref) else rtrim(fcedepol) end as fremark, " +
                                        "'' as fpartr,'1' as fsrc,'01' as ftype,'1' as fdoctype,  " +
                                        "'' as falias, '' as fvdesc,'2' as fintadj,'3' as fstatus ,'2' as fposted ,  " +
                                        "rtrim(a.finpuser),convert(VARCHAR(25),a.finpdate,120) as finpdate,rtrim(a.fupduser),convert(VARCHAR(25),a.fupddate,120) as fupddate,'" + InsEnvironment.LoginUser.GetUserCode() + "' " +
                                        "as fcnfuser,'" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' as fcnfdate,'' as fvoichid,'' as fcinpname, " +
                                        "rtrim(f.facclass) as item_id, rtrim(g.fmap) as interco_id,rtrim(c.fmap) as cont_id, " +
                                        "case when rtrim(a.fclass)<>'EEC' then '000' else case when rtrim(a.fsclass) ='CONTRACTOR' then 'CR' else 'OT' end end as dept_id, " +
                                        "'IN' as person_id,'' as fcremark  " +
                                        "from polh a  " +
                                        "left join oinvdet b on b.fctlid_1 = a.fctlid  " +
                                        "left join " + db + "mprdr c on rtrim(c.fid)= rtrim(a.fprdr)  " +
                                        "left join ndept d on d.fid= a.fsclnt  " +
                                        "left join oinsint_e e on e.fctlid_1= a.fctlid  " +
                                        "left join " + db + "minsc f on rtrim(f.fid)= rtrim(a.fclass)  " +
                                        "left join " + db + "mprdr g on rtrim(g.fid)= rtrim(a.fclnt) " +
                                        "where b.fctlid_1='" + fctlid + "'";
                        }
                        else
                        {
                            Sql5 = "select distinct a.fctlid as fctlid_1, rtrim(fbus) as fbus,'D0100' as fmodule, rtrim(fcoins) as fcoins, case when rtrim(a.fpolno)='' then '" + fpolno.Trim() + "' else rtrim(a.fpolno) end fpolno,rtrim(a.fendtno) as fendtno,rtrim(fsrcref) as fsrcref,  " +
                                     "rtrim(fcedepol),rtrim(fcedeendt),rtrim(fuwyr),rtrim(a.fclass) as fclass,rtrim(a.fsclass) as fsclass, " +
                                     "case when rtrim(a.fclass)='EEC' then case when rtrim(a.fsclass) ='CONTRACTOR' then 'CR' else 'OT' end else '' end as facclass,rtrim(fsiteid),rtrim(finstall),rtrim(ftotinstal),  " +
                                     "'" + fctlid_new.Trim() + "' as fctlid,convert(VARCHAR(25),fissdate,120) as fissdate,convert(VARCHAR(25),finvdate,120) as finvdate,null as fbilldate,null as fbkdate,  " +
                                     "case when a.fcoins='1' or a.fcoins='2' then right(convert(varchar(10),fefffr,112),6) else '' end as fbordmon,convert(VARCHAR(25),fefffr,120) as fefffr, convert(VARCHAR(25),feffto,120) as feffto,rtrim(finvno) as finvno,case when rtrim(fprdr)<>'PDM001' then 'P' else 'C' end as fdbtrtype,  " +
                                     "case when fprdr<>'PDM001' then rtrim(fprdr) else rtrim(fclnt) end as fdbtr,rtrim(fprdr) as fprdr,rtrim(c.fdesc) as fpdesc,rtrim(fclnt) as fclnt,  " +
                                     "rtrim(fsclnt),rtrim(fpmcur),b.fgpm,b.ftdamt,fcomtype,b.fcomamt,b.fnpm,b.flvyamt1,b.flvyamt2,  " +
                                     "b.flvyamt3,b.flvyamta,'0.0000' as fadmfee,b.fnpm+b.flvyamt1+b.flvyamt2+b.flvyamt3+b.flvyamta as famount,ftd,fcom,fgrnet,0 as fcrterm,  ";
                            if (Class == "CAR" || Class == "EEC")
                            {
                                Sql5 = Sql5 + "case when rtrim(a.fbus)<>'K' then '' else convert(varchar(10), a.fefffr, 103) +'-'+convert(varchar(10), a.feffto, 103)+ char(13) " +
                                " +'(Your Policy:'+rtrim(a.fcedepol)+ case when rtrim(a.fcedeendt)='' then ')' else 'Your Endorsement:' +rtrim(fcedeendt) +')'end end as fremark,'' as fcremark, ";
                            }
                            else if (Class == "PMP" || Class == "CMP")
                            {
                                Sql5 = Sql5 + "rtrim(e.frno)+' ('+convert(varchar(10), fefffr, 103) +'-'+convert(varchar(10), a.feffto, 103)+')' as fremark,'' as fcremark, ";
                            }
                            else
                            {
                                Sql5 = Sql5 + "'' as fremark,'' as fcremark, ";
                            }
                            Sql5 = Sql5 + "'' as fpartr,'1' as fsrc,'01' as ftype,  " +
                                     "case when b.fnpm+b.flvyamt1+b.flvyamt2+b.flvyamt3 >0 then '1' else '2' end as fdoctype,  " +
                                     "case when rtrim(fprdr)<>'PDM001' then rtrim(c.falias) else rtrim(c.falias) + '-' + rtrim(d.falias) end as falias,  " +
                                     "case when rtrim(a.fclass)='CAR' OR rtrim(a.fclass)='EEC' then SUBSTRING(rtrim(fsite), 1, 80) else case when rtrim(a.fclass)='PMP' OR rtrim(a.fclass)='CMP'   " +
                                     "then rtrim(e.frno) else '' end end as fvdesc,'2' as fintadj,'3' as fstatus ,'2' as fposted ,  " +
                                     "rtrim(a.finpuser),convert(VARCHAR(25),a.finpdate,120) as finpdate,rtrim(a.fupduser),convert(VARCHAR(25),a.fupddate,120) as fupddate,'" + InsEnvironment.LoginUser.GetUserCode() + "' " +
                                     "as fcnfuser,'" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' as fcnfdate,'' as fvoichid,'' as fcinpname, " +
                                     "rtrim(f.facclass) as item_id, rtrim(g.fmap) as interco_id,rtrim(c.fmap) as cont_id, " +
                                     "case when rtrim(a.fclass)<>'EEC' then '000' else case when rtrim(a.fsclass) ='CONTRACTOR' then 'CR' else 'OT' end end as dept_id, " +
                                     "case when rtrim(a.fsclnt)<>'' then rtrim(a.fsclnt) else '00' end as person_id,a.flvyadj1,a.ftdadj,a.fcomadj  " +
                                     "from polh a  " +
                                     "left join oinvdet b on b.fctlid_1 = a.fctlid  " +
                                     "left join " + db + "mprdr c on rtrim(c.fid)= rtrim(a.fprdr)  " +
                                     "left join ndept d on d.fid= a.fsclnt  " +
                                     "left join oinsint_e e on e.fctlid_1= a.fctlid  " +
                                     "left join " + db + "minsc f on rtrim(f.fid)= rtrim(a.fclass)  " +
                                     "left join " + db + "mprdr g on rtrim(g.fid)= rtrim(a.fclnt) " +
                                     "where b.fctlid_1='" + fctlid + "'";
                        }

                        dt5 = DBHelper.GetDataSet(Sql5);
                        if (dt5 != null && dt5.Rows.Count > 0)
                        {
                            for (int i = 0; i < dt5.Rows.Count; i++)
                            {
                                if (i != 0) { fctlid_new = (int.Parse(fctlid_new) + 1).ToString().PadLeft(10, '0'); }
                                dt5.Rows[i]["fctlid"] = fctlid_new;
                            }
                            dt5.Rows[0]["ftdamt"] = Fct.sdFat(dt5.Rows[0]["ftdamt"]);
                            dt5.Rows[0]["fcomamt"] = Fct.sdFat(dt5.Rows[0]["fcomamt"]);
                            dt5.Rows[0]["flvyamt1"] = Fct.sdFat(dt5.Rows[0]["flvyamt1"]);
                            dt5.Rows[0]["flvyamta"] = Fct.sdFat(dt5.Rows[0]["flvyamta"]);
                            dt5.Rows[0]["famount"] = Fct.sdFat(dt5.Rows[0]["fnpm"]) + Fct.sdFat(dt5.Rows[0]["flvyamt1"]) + Fct.sdFat(dt5.Rows[0]["flvyamt2"]) + Fct.sdFat(dt5.Rows[0]["flvyamt3"]) + Fct.sdFat(dt5.Rows[0]["flvyamta"]);
                            dt5.Columns.Remove("ftdadj");
                            dt5.Columns.Remove("fcomadj");
                            dt5.Columns.Remove("flvyadj1");
                        }

                        Boolean isSuccuss = DBHelper.BulkInsertDataTable("oinvh", dt5);
                        if (isSuccuss == true)
                        {
                            Sql4 = "update " + db + "xsysparm  set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST('" + fctlid_new + "' as int)+1)), 10) where fidtype='OINVHID'";
                            DBHelper.ExecuteCommand(Sql4);
                            //INSERT INTO Reinsurance
                            if (Class == "CMP" || Class == "PMP" || Class == "CSB" || (Class == "EEC" && autori == "Yes"))
                            {
                                isSuccuss = saveOrih(fctlid, Class);
                                if (isSuccuss == true)
                                {
                                    string foshare = "100.0000";
                                    if (ftype == "P") { foshare = "0.0000"; }
                                    string sql = "select fctlid_1, fctlid as fctlid_2, '' as fctlid, fbus,fkind,'01' as fritype,'' as fsec, 'Retention' as fid, " +
                                                "'' as fttycode, '' as fttylayr, '' as fttysec, '' as fttyclass,0.0000 as fcom, '' as fgrnet,0.0000 as fbrkge, " +
                                                "'' as fgrnet2, 0.0000 as fcomamt,0.0000 as fbrkgeamt,0.0000 as fabsnet,100.0000 as fshare,fsi,fsi2, " +
                                                "100.0000 as fupdshare,fupdsi as fupdsi,fupdsi2 as fupdsi2,'" + foshare + "' as foshare,fosi as fosi,fosi2 as fosi2, " +
                                                "fgpm,fnpm, 0.0000 as fshare_c,0.0000 as fsi_c,0.0000 as fsi2_c,0.0000 as fgpm_c,0.0000 as fnpm_c, " +
                                                "0.0000 as fmiscamt_c,fclass, '' as floscode, '1' as fppl, '' as fcalc,0.0000 as fexcess,0.0000 as flimit from orih  " +
                                                "where fctlid_1='" + fctlid + "' " +
                                                "union select top 1 * from orid1 where fclass='" + Class + "' and fritype in ('04') " +
                                                "union select top 1 * from orid1 where fclass='" + Class + "' and fritype in ('05') " +
                                                "order by fritype";
                                    DataTable orid1dt = DBHelper.GetDataSet(sql);
                                    string fctlid_orid1 = Fct.NewId("orid1");
                                    string orih_fctlid = "";
                                    for (int i = 0; i < orid1dt.Rows.Count; i++)
                                    {
                                        if (i != 0) { fctlid_orid1 = (int.Parse(fctlid_orid1) + 1).ToString().PadLeft(10, '0'); }
                                        orid1dt.Rows[i]["fctlid"] = fctlid_orid1;
                                        orid1dt.Rows[i]["fctlid_1"] = orid1dt.Rows[0]["fctlid_1"];
                                        orid1dt.Rows[i]["fctlid_2"] = orid1dt.Rows[0]["fctlid_2"];
                                        orih_fctlid = orid1dt.Rows[0]["fctlid_2"].ToString();
                                    }
                                    isSuccuss = DBHelper.BulkInsertDataTable("orid1", orid1dt);
                                    if (isSuccuss == true)
                                    {
                                        string updfctlid = "update " + db + "xsysparm set fnxtid='" + (int.Parse(fctlid_orid1) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='orid1'";
                                        isSuccuss = DBHelper.ExecuteCommand(updfctlid);
                                        if (isSuccuss == true)
                                        {
                                            bool is_ok = false;
                                            if (ftype != "E")
                                            {
                                                reins.fclass = Class;
                                                reins.fctlid = orih_fctlid;
                                                is_ok = reins.u_genxolmthd2(true);
                                                if (is_ok) { is_ok = reins.u_genrtninst(); }
                                            }
                                            else
                                            {
                                                Endtreins.fclass = Class;
                                                Endtreins.fctlid = orih_fctlid;
                                                is_ok = Endtreins.u_genxolmthd2(true);
                                                if (is_ok) { is_ok = Endtreins.u_genrtninst(); }
                                            }

                                            if (is_ok)
                                            {
                                                string updst = "update orih set fposted ='1',fissdate = case when fissdate=null then getdate() else fissdate end,fefffr = case when fefffr=null then getdate() else fefffr end,feffto = case when feffto=null then getdate() else feffto end,fincpdate = case when fincpdate=null then getdate() else fincpdate end, fcnfuser='" + InsEnvironment.LoginUser.GetUserCode() + "', fcnfdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid = '" + orih_fctlid + "'";
                                                is_ok = DBHelper.ExecuteCommand(updst);
                                            }
                                        }
                                    }
                                }
                            }
                            else if (Class == "EEC" && autori == "No") { }//isSuccuss = saveOrih(fctlid, Class); }
                        }

                        //if (endorsement != null)
                        //{
                        //    if (optionPath == 2)
                        //    {
                        //        pendtExport(CrystalDecisions.Shared.ExportFormatType.WordForWindows, "test.doc", Class, fctlid);
                        //    }
                        //    //Workflow_Click();
                        //}
                        //if (directPolicy != null)
                        //{
                        //    if (optionPath == 2)
                        //    {
                        //        pscheExport(CrystalDecisions.Shared.ExportFormatType.WordForWindows, "test.doc", Class, fctlid);
                        //    }
                        //    //Workflow_Click();
                        //}
                    }
                    if (endorsement != null) { 
                        endorsement.ConfirmRlt = fctlid;
                    }
                    if (directPolicy != null) { 
                        directPolicy.ConfirmRlt = fctlid;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show("Haven't Been Confirmed", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    if (endorsement != null) { endorsement.ConfirmRlt = "No"; }
                    if (directPolicy != null) { directPolicy.ConfirmRlt = "No"; }
                    Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                    Console.WriteLine("  Message: {0}", ex.Message);

                    // Attempt to roll back the transaction. 
                    try
                    {
                        transaction.Rollback();
                    }
                    catch (Exception ex2)
                    {
                        // This catch block will handle any errors that may have occurred 
                        // on the server that would cause the rollback to fail, such as 
                        // a closed connection.
                        Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                        Console.WriteLine("  Message: {0}", ex2.Message);
                    }
                }
                connection.Close();
            }
            this.Close();
        }

        public bool saveOrih(string fctlid, string Class)
        {
            bool isSuccuss = true;
            string fkind = UtilityFunc.GetKindFrmClass(Class);
            string fxol = "", fcalc = "";
            if (Class == "EEC" || Class == "CMP" || Class == "CGL" || Class == "PMP")
            { fxol = "1"; }
            else { fxol = "2"; }
            if (Class == "CAR" || Class == "CPM" || Class == "FGP" || Class == "MYP" || Class == "PAR")
            { fcalc = "2"; }
            else { fcalc = "1"; }

            string orih = Fct.NewId("orih");
            string orihc = Fct.NewId("orihc");
            string orih_fctlid = "";
            DataTable dt = new DataTable();
            string sql = "update " + db + "[xsysparm] set fnxtid =RIGHT('000000000'+ LTRIM(STR(CAST((select max(fctlid) from orih) as int)+1)), 10) where fidtype ='orih'";
            DBHelper.GetScalar(sql);
            if (directPolicy != null)
            {
                string selsql = "select fctlid as fctlid_1,fctlid_p,fctlid_v,fbus,ftype,fendttype,fpolno,fendtno,fvseq,fvarno,fextend,  " +
                            "fsrcref,fcedepol,fcedeendt,fuwyr,fclass,fsclass,'01' as frino,'" + fkind + "' as fkind,  " +
                            "'" + orih + "' as fctlid,fissdate,fefffr,fefffr,feffto,'" + fcalc + "' as fcalc,fsicur, " +
                            "case when fclass='EEC' OR fclass='CMP' OR fclass='PMP' then flmt1 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then fliab else fsi end end as fsi, " +
                            "case when fclass='CMP' OR fclass='PMP' then flmt2 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then 0 else fliab end end as fsi2, " +
                            "case when fclass='EEC' OR fclass='CMP' OR fclass='PMP' then fupdlmt1 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then fupdliab else fupdsi end end as fupdsi, " +
                            "case when fclass='CMP' OR fclass='PMP' then fupdlmt2 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then 0 else fupdliab end end as fupdsi2, " +
                            "'0.0000' as forgsi,'0.0000' as forgliab,fpmcur, fgpm,fnpm,'" + fxol + "' as fxol, " +
                            "case when fclass='EEC' OR fclass='CMP' OR fclass='PMP' then flmt1 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then fliab else fsi end end as fsi_c, " +
                            "case when fclass='CMP' OR fclass='PMP' then flmt2 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then 0 else fliab end end as fsi2_c, " +
                            "fgpm as fgpm_c, fnpm as fnpm_c,'100.0000' as fshare_c, " +
                            "finpuser,finpdate,fupduser,fupddate,fcnfuser,fcnfdate,fconfirm,'2' as fposted from polh " +
                            "where fctlid='" + fctlid + "'";
                dt = DBHelper.GetDataSet(selsql);
            }
            else
            {
                string selsql = "select polh.fctlid as fctlid_1,fctlid_p,fctlid_v,fbus,ftype,fendttype,fpolno,fendtno,fvseq,fvarno,fextend,  " +
                                   "fsrcref,fcedepol,fcedeendt,fuwyr,fclass,fsclass,'01' as frino,'" + fkind + "' as fkind,  " +
                                   "'" + orih + "' as fctlid,fissdate,case when fclass ='EEC' and fendttype <> 1 then fincfr_p else fincfr end,fefffr,feffto,'" + fcalc + "' as fcalc,fsicur, " +
                                   "case when fclass='EEC' OR fclass='CMP' OR fclass='PMP' then flmt1 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then fliab else fsi end end as fsi, " +
                                   "case when fclass='CMP' OR fclass='PMP' then flmt2 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then 0 else fliab end end as fsi2, " +
                                   "case when fclass='EEC' OR fclass='CMP' OR fclass='PMP' then fupdlmt1 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then fupdliab else fupdsi end end as fupdsi, " +
                                   "case when fclass='CMP' OR fclass='PMP' then fupdlmt2 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then 0 else fupdliab end end as fupdsi2, " +
                                   "case when (fclass='EEC' and fendttype=3) then 0 else case when (fclass='EEC' and fendttype<>3) OR fclass='CMP' OR fclass='PMP' then b.fupdlmt1o else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then b.fosi2 else b.fosi end end end as forgsi, " +
                                   "case when fclass='CMP' OR fclass='PMP' then b.fupdlmt2o else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then 0 else b.fosi2 end end as forgliab,fpmcur, fgpm,fnpm,'" + fxol + "' as fxol, " +
                                   "case when fclass='EEC' OR fclass='CMP' OR fclass='PMP' then flmt1 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then fliab else fsi end end as fsi_c, " +
                                   "case when fclass='CMP' OR fclass='PMP' then flmt2 else case when fclass='CLL' OR fclass='CGL' OR fclass='PII' then 0 else fliab end end as fsi2_c, " +
                                   "fgpm as fgpm_c, fnpm as fnpm_c,'100.0000' as fshare_c, " +
                                   "finpuser,finpdate,fupduser,fupddate,fcnfuser,fcnfdate,fconfirm,'2' as fposted from polh " +
                                   "left join (select fupdsi as fosi , fupdliab as fosi2 , fupdlmt1 as fupdlmt1o, fupdlmt2 as fupdlmt2o, fctlid from polh) b on b.fctlid = polh.fctlid_pk " +
                                   "where polh.fctlid='" + fctlid + "'";
                dt = DBHelper.GetDataSet(selsql);
            }

            if (dt.Rows.Count > 0)
            {
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    if (i != 0) { orih = (int.Parse(orih) + 1).ToString().PadLeft(10, '0'); }
                    dt.Rows[i]["fctlid"] = orih;
                    orih_fctlid = dt.Rows[i]["fctlid"].ToString();
                }
                isSuccuss = DBHelper.BulkInsertDataTable("orih", dt);

                string updorih = "update " + db + "xsysparm set fnxtid='" + (int.Parse(orih_fctlid) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='orih'";
                DBHelper.ExecuteCommand(updorih);
            }
            return isSuccuss;
        }

        public void GetToken_Click()
        {
            var client = new RestClient(BASE_URL);
            var request = new RestRequest("auth/sso_encrypt_token", Method.GET);
            request.AddParameter("auth_key", authKey_ssoToken);
            request.AddParameter("user_id", user_id);
            request.AddHeader("Content-Type", "application/json");
            //var response = client.Get<SSORoot>(request);
            //txtOutput.Text += "\n" + "sso token was received: " + response.Data.encrypt_token;
            IRestResponse response = client.Execute(request);
            var response2 = new RestResponse { Content = response.Content.Trim() };
            var serializer = new JsonSerializer();
            var output = serializer.Deserialize<SSORoot>(response2);
            this.sso_token = output.encrypt_token;

            request = new RestRequest("auth/access_token", Method.GET);
            request.AddParameter("auth_key", authKey_accessToken);
            request.AddHeader("Content-Type", "application/json");
            response = client.Execute(request);
            response2 = new RestResponse { Content = response.Content.Trim() };
            serializer = new JsonSerializer();
            var output1 = serializer.Deserialize<AccessRoot>(response2);
            this.access_token = output1.access_token;

            //request = new RestRequest("systems/a8/gettoken", Method.POST);
            request = new RestRequest("systems/a8/indexcsci.php/gettoken", Method.POST);
            var jsonParam = new { sso_encrypt_token = HttpUtility.UrlEncode(output.encrypt_token) };
            request.AddParameter("application/json", JsonConvert.SerializeObject(jsonParam), RestSharp.ParameterType.RequestBody);
            request.AddHeader("Content-Type", "application/json");
            request.AddHeader("Authorization", "Bearer " + HttpUtility.UrlEncode(output1.access_token));
            response = client.Execute(request);
            response2 = new RestResponse { Content = response.Content.Trim() };
            serializer = new JsonSerializer();
            var output2 = serializer.Deserialize<TokenRoot>(response2);
            this.a8_token = output2.token;

        }

        public void Attachment_Click()
        {
            // testurl var url = "http://a8test.csci.cohl.com/seeyon/rest/attachment?token=" + this.a8_token + "&applicationCategory=0&extensions=&firstSave=true";
            var url = "http://i.cohl.csci.cohl.com/seeyon/rest/attachment?token=" + this.a8_token + "&applicationCategory=0&extensions=&firstSave=true";
            var client = new RestClient(url);
            var request = new RestRequest(Method.POST);
            request.AlwaysMultipartFormData = true;
            request.AddParameter("token", this.a8_token);
            request.AddHeader("Content-Type", "multipart/form-data");
            request.AddFile("file", "M:\\PUSHTOA8\\" + wl_fpolno + ".pdf");
            request.AddParameter("multipart/form-data", "" + wl_fpolno + ".pdf", RestSharp.ParameterType.RequestBody);
            var response = client.Execute(request);
            var response2 = new RestResponse { Content = response.Content.Trim() };
            var serializer = new JsonSerializer();
            var output2 = serializer.Deserialize<UploadRoot>(response2);
            this.file_handle = new long[] { output2.atts[0].fileUrl };
        }

        public void Workflow_Click()
        {
            GetToken_Click();
            Attachment_Click();
            var client = new RestClient(BASE_URL);
            //var request = new RestRequest("systems/a8/workflows", Method.POST);
            var request = new RestRequest("systems/a8/indexcsci.php/workflows", Method.POST);
            //var jsonData = JsonConvert.SerializeObject(new A8DataWrapper<T>(HttpUtility.UrlEncode(this.sso_token), "policy_approve", "", this.file_handle));
            var jsonParam = new
            {
                sso_encrypt_token = HttpUtility.UrlEncode(this.sso_token),
                workflow_tid = "policy_approval",
                form_item = new { 流程编号 = fctlid, 保費金額 = "10000", 核保档案编号 = wl_fpolno, 日期 = DateTime.Now.ToString("yyyy-MM-dd") },
                attachments = this.file_handle
            };
            request.AddParameter("application/json", JsonConvert.SerializeObject(jsonParam), RestSharp.ParameterType.RequestBody);
            request.AddHeader("Content-Type", "application/json");
            request.AddHeader("Authorization", "Bearer " + HttpUtility.UrlEncode(this.access_token));
            var response = client.Execute(request);
            var response2 = new RestResponse { Content = response.Content.Trim() };
            var serializer = new JsonSerializer();
            var output2 = serializer.Deserialize<Root>(response2);
            this.workflow_id = output2.workflow_id;

            string updst = "update polh set fctldel ='" + this.workflow_id + "' where fctlid = '" + fctlid + "'";
            DBHelper.ExecuteCommand(updst);
        }

    }
}



      