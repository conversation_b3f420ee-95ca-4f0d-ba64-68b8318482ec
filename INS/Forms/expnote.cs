using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using System.Collections;
using System.Configuration;
using INS.Business.Report;
using CrystalDecisions.Shared;
using CrystalDecisions.CrystalReports.Engine;
using INS.Business.objRpt;

namespace INS
{
    public partial class expnote : Form
    {
        BindData[] u_yesnoarr = new BindData[]  {
                                 new BindData("1","Yes"),
                                 new BindData("2","No")};
        private string rpDirectory = "M:\\Software II\\New INS Runtime\\Setup\\objRpt";
        //public expnote(Business.DirectPolicy directPolicy)
        //{
        //    InitializeComponent();
        //    foreach (Control control in Clauses.Controls)                //循环窗体的控件
        //    {
        //        control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
        //    }
        //    userRight();
        //    FillData();
        //    init();
        //}

        public expnote(Business.SearchForm.renewopt renewopt)
        {
            // TODO: Complete member initialization
            this.renewopt = renewopt;
            InitializeComponent();
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            userRight();
            FillData();
            init();
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            TextBox focusTextBox = null;

            if (e.KeyCode == Keys.Enter)
            {                    //判断用户是否按下回车键
                if (sender is TextBox)
                {
                    focusTextBox = (TextBox)sender;
                    SendKeys.Send("{TAB}");
                }
            }
        }

        public string db = InsEnvironment.DataBase.GetDbm();
        public String fctlid_p = "", fritype = "", oldrow = "", fctlid = "";
        Boolean Updateflag = false;
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        private Business.SearchForm.renewopt renewopt;

        void init()
        {
            fposted.DataSource = u_yesnoarr;
            fposted.ValueMember = "ID";
            fposted.DisplayMember = "Item";
        }

        public void FillData()
        {
            try
            {
                btnback();
                string sql = "select * from oexdet where fctlid_p='" + fctlid_p + "'";
                vlist.DataSource = DBHelper.GetDataSet(sql);
                foreach (DataGridViewColumn Column in vlist.Columns)
                {
                    Column.Visible = false;
                }
                this.vlist.Columns["frefno"].Visible = true;
                this.vlist.Columns["fpolno"].Visible = true;
                this.vlist.Columns["fendtno"].Visible = true;
                this.vlist.Columns["fautono"].Visible = true;
                this.vlist.Columns["fconno"].Visible = true;
                this.vlist.Columns["fexpdate"].Visible = true;
                this.vlist.Columns["fprdr"].Visible = true;
                this.vlist.Columns["fclnt"].Visible = true;
                this.vlist.Columns["fissdate"].Visible = true;
                this.vlist.Columns["fposted"].Visible = true;
                vlist.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.vlist_CellFormatting);
            }
            catch { }
        }

        private void vlist_CellFormatting(object sender, System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            try
            {
                vlist.Columns["fissdate"].HeaderText = "Issue Date";
            vlist.Columns["fissdate"].DefaultCellStyle.Format = "dd'/'MM'/'yyyy";
            vlist.Columns["frefno"].HeaderText = "Ref No.";
            vlist.Columns["fpolno"].HeaderText = "Policy No.";
            vlist.Columns["fendtno"].HeaderText = "Endt No.";
            vlist.Columns["fautono"].HeaderText = "Auto No.";
            vlist.Columns["fconno"].HeaderText = "Expiry No.";
            vlist.Columns["fposted"].HeaderText = "Confirmed";
            vlist.Columns["fprdr"].HeaderText = "Producer#";
            vlist.Columns["fclnt"].HeaderText = "Client#";
            vlist.Columns["fexpdate"].HeaderText = "Expiry Date";
            vlist.Columns["fexpdate"].DefaultCellStyle.Format = "dd'/'MM'/'yyyy";
            }
            catch { }
        }

        void GridViewchg()
        {
            if (vlist.CurrentCell != null)
            {
                int counter;
                counter = vlist.CurrentCell.RowIndex;
                if (vlist.Rows[counter].Cells["fclass"].Value != null)
                {
                    fclass.Text = vlist.Rows[counter].Cells["fclass"].Value.ToString().Trim();
                }
                if (vlist.Rows[counter].Cells["fattn"].Value != null)
                {
                    fattn.Text = vlist.Rows[counter].Cells["fattn"].Value.ToString().Trim();
                }

                if (vlist.Rows[counter].Cells["fexpdate"].Value != null)
                {
                    try
                    {
                        string a = vlist.Rows[counter].Cells["fexpdate"].Value.ToString().Trim();
                        DateTime b = Convert.ToDateTime(a);
                        fexp.Text = b.ToString("yyyy.MM.dd");
                    }
                    catch { }
                }
                if (vlist.Rows[counter].Cells["fprdr"].Value != null)
                {
                    fprdr.Text = vlist.Rows[counter].Cells["fprdr"].Value.ToString().Trim();
                }
                if (vlist.Rows[counter].Cells["fautono"].Value != null)
                {
                    fautono.Text = vlist.Rows[counter].Cells["fautono"].Value.ToString().Trim();
                }
                if (vlist.Rows[counter].Cells["frefno"].Value != null)
                {
                    frefno.Text = vlist.Rows[counter].Cells["frefno"].Value.ToString().Trim();
                }
                if (vlist.Rows[counter].Cells["fpolno"].Value != null)
                {
                    fpolno.Text = vlist.Rows[counter].Cells["fpolno"].Value.ToString().Trim();
                }
                if (vlist.Rows[counter].Cells["fendtno"].Value != null)
                {
                    fendtno.Text = vlist.Rows[counter].Cells["fendtno"].Value.ToString().Trim();
                }
                if (vlist.Rows[counter].Cells["fissdate"].Value != null)
                {
                    try
                    {
                        string a = vlist.Rows[counter].Cells["fissdate"].Value.ToString().Trim();
                        DateTime b = Convert.ToDateTime(a);
                        fissdate.Text = b.ToString("yyyy.MM.dd");
                    }
                    catch { }
                }
                if (vlist.Rows[counter].Cells["fclnt"].Value != null)
                {
                    fclnt.Text = vlist.Rows[counter].Cells["fclnt"].Value.ToString().Trim();
                }
                if (vlist.Rows[counter].Cells["fconno"].Value != null)
                {
                    fconno.Text = vlist.Rows[counter].Cells["fconno"].Value.ToString().Trim();
                }
                if (vlist.Rows[counter].Cells["fprdr"].Value != null)
                {
                    fprdr.Text = vlist.Rows[counter].Cells["fprdr"].Value.ToString().Trim();
                }
                if (vlist.Rows[counter].Cells["faddr"].Value != null)
                {
                    faddr_t.Text = vlist.Rows[counter].Cells["faddr"].Value.ToString().Trim();
                }
                if (vlist.Rows[counter].Cells["fheader1"].Value != null)
                {
                    fheader_t.Text = vlist.Rows[counter].Cells["fheader1"].Value.ToString().Trim();
                }
                if (vlist.Rows[counter].Cells["fcontent"].Value != null)
                {
                    fcontent.Text = vlist.Rows[counter].Cells["fcontent"].Value.ToString().Trim();
                }

                if (vlist.Rows[counter].Cells["finpuser"].Value != null)
                {
                    finpuser.Text = vlist.Rows[counter].Cells["finpuser"].Value.ToString().Trim();
                }
                if (vlist.Rows[counter].Cells["finpdate"].Value != null)
                {
                    finpdate.Text = vlist.Rows[counter].Cells["finpdate"].Value.ToString().Trim();
                }
                if (vlist.Rows[counter].Cells["fupduser"].Value != null)
                {
                    fupduser.Text = vlist.Rows[counter].Cells["fupduser"].Value.ToString().Trim();
                }
                if (vlist.Rows[counter].Cells["fupddate"].Value != null)
                {
                    fupddate.Text = vlist.Rows[counter].Cells["fupddate"].Value.ToString().Trim();
                }
                if (vlist.Rows[counter].Cells["fposted"].Value != null)
                {
                    fposted.SelectedValue = vlist.Rows[counter].Cells["fposted"].Value.ToString().Trim();
                }
                rowlabel.Text = counter.ToString();
                fctlid = vlist.Rows[counter].Cells["fctlid"].Value.ToString().Trim();
                PostbtnControl();
            }
        }

        private void vlist_CurrentCellChanged(object sender, EventArgs e)
        {
            GridViewchg();
        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            e.Cancel = true;
        }

        private void tabControl1_Selecting2(object sender, TabControlCancelEventArgs e)
        {
            e.Cancel = false;
        }

        void btnback()
        {
            foreach (Control ctrl in Clauses.Controls)
            {
                if (ctrl is MaskedTextBox)
                {
                    ((MaskedTextBox)ctrl).ReadOnly = true;
                    ((MaskedTextBox)ctrl).BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = true;
                    ((TextBox)ctrl).BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (ctrl is RichTextBox)
                {
                    ((RichTextBox)ctrl).ReadOnly = true;
                    ((RichTextBox)ctrl).BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
            }
        }

        void btnmodify()
        {
            faddr_t.ReadOnly = false;
            faddr_t.BackColor = System.Drawing.Color.White;
            fheader_t.ReadOnly = false;
            fheader_t.BackColor = System.Drawing.Color.White;
            fcontent.ReadOnly = false;
            fcontent.BackColor = System.Drawing.Color.White;
            fattn.ReadOnly = false;
            fattn.BackColor = System.Drawing.Color.White;
            frefno.ReadOnly = false;
            frefno.BackColor = System.Drawing.Color.White;
        }

        public void userRight()
        {
            string p_fright = InsEnvironment.LoginUser.GetUserRight3c();
            string g_user = InsEnvironment.LoginUser.GetUserCode();
            string finpuser = "";
            Boolean ll_con = false;
            if ((finpuser == g_user && p_fright.Substring(7, 1) == "1") || (finpuser != g_user && p_fright.Substring(8, 1) == "1"))
            {
                if (ddel.Visible == true) { ddel.Visible = true; delbtn.Visible = true; }
            }
            else
            {
                ddel.Visible = false; delbtn.Visible = false;
            }
            if ((finpuser == g_user && p_fright.Substring(5, 1) == "1") || (finpuser != g_user && p_fright.Substring(6, 1) == "1"))
            {
                ll_con = true;
            }
            if (ll_con == true && (fconfirm.Visible == true || fposted.SelectedValue =="2"))
            {
                dPost.Visible = true; fconfirm.Visible = true;
            }
            else
            {
                dPost.Visible = false; fconfirm.Visible = false;
            }
        }

        void PostbtnControl()
        {
            if (!Updateflag)
            {
                string sql = "select * from " + Dbm + "xsysperiod where fidtype='BILLMON'";
                DataTable dt = DBHelper.GetDataSet(sql);
                DateTime now = DateTime.Now;
                if (dt.Rows.Count > 0)
                {
                    DateTime fdatefr = Convert.ToDateTime(dt.Rows[0]["fdatefr"]);
                    DateTime fdateto = Convert.ToDateTime(dt.Rows[0]["fdateto"]);
                    if (dt.Rows[0]["fextdto"].ToString().Trim() == "")
                    {
                        fdateto = Convert.ToDateTime(dt.Rows[0]["fdateto"]);
                    }
                    else { fdateto = Convert.ToDateTime(dt.Rows[0]["fextdto"]); }
                    //DateTime finDate = Convert.ToDateTime(finvdate.Text);

                    // if (now.Ticks > fdatefr.Ticks && now.Ticks < fdateto.Ticks && fposted.SelectedItem.ToString() == "No" )
                    if (fposted.SelectedValue == "2")
                    {
                        dPost.Visible = true;
                        fconfirm.Visible = true;
                        dupdate.Visible = true;
                        updbtn.Visible = true;
                        delbtn.Visible = true;
                        ddel.Visible = true;
                    }
                    else
                    {
                        dPost.Visible = false;
                        fconfirm.Visible = false;
                        dupdate.Visible = false;
                        updbtn.Visible = false;
                        delbtn.Visible = false;
                        ddel.Visible = false;
                    }
                    userRight();
                }
            }
        }

        void btncontrol()
        {
            if (Updateflag == true)
            {
                updbtn.Visible = false;
                delbtn.Visible = false;
                printbtn.Visible = false;
                exitbtn.Visible = false;
                savebtn.Visible = true;
                cancelbtn.Visible = true;

                dupdate.Visible = false;
                ddel.Visible = false;
                dprint.Visible = false;
                dexit.Visible = false;
                dPost.Visible = false;
                savebtn.Visible = true;
                cancelbtn.Visible = true;
            }
            else
            {
                updbtn.Visible = true;
                delbtn.Visible = true;
                printbtn.Visible = true;
                exitbtn.Visible = true;
                savebtn.Visible = false;
                cancelbtn.Visible = false;

                dupdate.Visible = true;
                ddel.Visible = true;
                dprint.Visible = true;
                dexit.Visible = true;
                dPost.Visible = true;
                savebtn.Visible = false;
                cancelbtn.Visible = false;
            }
            PostbtnControl();
        }

        void delete()
        {
            ArrayList delarr = new ArrayList();
            string del = "delete from oexdet where fautono ='" + Fct.stFat(fautono.Text) + "'";
            delarr.Add(del);

            Boolean isSuccuss = DBHelper.ExecuteSqlTransaction((string[])delarr.ToArray(typeof(string)));
            if (isSuccuss == true)
            {
                MessageBox.Show("Have Been Deleted", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("Haven't Been Deleted", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            FillData();
            btnback();
            btncontrol();
        }

        void update()
        {
            tabControl1.SelectedTab = Clauses;
            Updateflag = true;
            btnmodify();
            btncontrol();
        }

        void save()
        {
            string user = InsEnvironment.LoginUser.GetUserCode();
            DateTime time = DateTime.Now;
            string oldrow = frefno.Text;
            ArrayList updarr = new ArrayList();
            DateTime ld_fissdate = DateTime.Today;
            if (fissdate.Text != "")
            {
                ld_fissdate = Convert.ToDateTime(fissdate.Text);
            }
            DateTime ld_fexpdate = DateTime.Today;
            if (fexp.Text != "")
            {
                ld_fexpdate = Convert.ToDateTime(fexp.Text);
            }
            string upd = "update oexdet set fattn = '" + Fct.stFat(fattn.Text) + "', fcontent = N'" + Fct.stFat(fcontent.Text) + "',frefno='" + Fct.stFat(frefno.Text) + "',  " +
            "faddr = N'" + Fct.stFat(faddr_t.Text) + "',fheader1 = N'" + Fct.stFat(fheader_t.Text) + "',fissdate = '" + time.ToString("yyyy-MM-dd HH:mm:ss") + "', " +
            "fexpdate = convert(datetime,'" + fexp.Text + "',102),[fupduser] = '" + user + "',[fupddate] = '" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fautono ='" + Fct.stFat(fautono.Text) + "'";
            updarr.Add(upd);

            Boolean isSuccuss = DBHelper.ExecuteSqlTransaction((string[])updarr.ToArray(typeof(string)));
            if (isSuccuss == true)
            {
                MessageBox.Show("Have Been Updated!", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("Haven't Been Updated", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            FillData();
            btnback();
            DataTable dt3 = vlist.DataSource as DataTable;
            foreach (DataRow row in dt3.Rows)
            {
                int SelectedIndex = dt3.Rows.IndexOf(row);
                if (oldrow == row["frefno"].ToString().Trim())
                {
                    vlist.CurrentCell = vlist.Rows[SelectedIndex].Cells["frefno"];
                    GridViewchg();
                    break;
                }
            }

        }

        void cancel()
        {
            Updateflag = false;
            btnback();
            btncontrol();
        }

        void confirm()
        {
            string oldrow = fautono.Text;
            ArrayList conarr = new ArrayList();
            DateTime ld_billdate = DateTime.Today;
            string lc_seqno = NEWID("EXPIRYNO", ld_billdate.Year.ToString());
            DateTime ld_fissdate = DateTime.Today;
            //if (fissdate.Text != "") {
            //    ld_fissdate = Convert.ToDateTime(fissdate.Text);
            //}

            string consql = "update oexdet set fconno='" + lc_seqno + "', fissdate='" + ld_fissdate.ToString("yyyy-MM-dd HH:mm:ss") + "',fposted ='1',fupduser = '" + fupduser.Text + "', fupddate = convert(datetime,'" + fupddate.Text.Trim() + "',102) where fautono ='" + Fct.stFat(fautono.Text) + "' ";
            conarr.Add(consql);

            string consql1 = "update " + Dbm + "xsysparm  set fnxtid=RIGHT('000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 4) where fidtype='EXPIRYNO' and fuy ='" + ld_fissdate.Year.ToString() + "'";
            conarr.Add(consql1);

            Boolean isSuccuss = DBHelper.ExecuteSqlTransaction((string[])conarr.ToArray(typeof(string)));
            if (isSuccuss == true)
            {
                MessageBox.Show("Have Been Confirmed!", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("Haven't Been Confirmed", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            FillData();
            btnback();
            DataTable dt3 = vlist.DataSource as DataTable;
            foreach (DataRow row in dt3.Rows)
            {
                int SelectedIndex = dt3.Rows.IndexOf(row);
                if (oldrow == row["fautono"].ToString().Trim())
                {
                    vlist.CurrentCell = vlist.Rows[SelectedIndex].Cells["fautono"];
                    GridViewchg();
                    break;
                }
            }
        }

        public string NEWID(string type, string year)
        {
            string lc_seqno = "";
            string sql = "select rtrim(fprefix)+fnxtid as lc_seqno from " + Dbm + "xsysparm where fidtype='" + type + "' and fuy='" + year + "' ";
            DataTable dt = DBHelper.GetDataSet(sql);
            if (dt.Rows.Count > 0)
            {
                lc_seqno = dt.Rows[0]["lc_seqno"].ToString();
            }
            return lc_seqno.Trim();
        }

        void print()
        {

            //pexpnote tempform = new pexpnote(this);
            //tempform.fctlid = fctlid;
            //tempform.dtlist = vlist.DataSource as DataTable;
            //tempform.ShowDialog();
            //this.Close();

            string sql = "select * from oexdet where fctlid in ('" + fctlid + "')";
            DataTable dt = DBHelper.GetDataSet(sql);

            string sql1 = "select a.*,b.fdesc as fclsdesc from oexdet a left join " + Dbm + "minsc b on rtrim(a.fclass) =rtrim(b.fid) where a.fctlid in ('" + fctlid + "')";
            DataTable dt1 = DBHelper.GetDataSet(sql1);

            
            DataSet myDataSet = new DataSet();
            myDataSet.Tables.Add(dt);
            myDataSet.Tables.Add(dt1);
            string wl_fautono = "";

            if (dt1.Rows.Count > 0 && dt.Rows.Count > 0)
            {
                wl_fautono = dt.Rows[0]["fautono"].ToString().Trim();
                try
                {
                    progBar.Value = 0;
                    waitlbl.Text = "Processing .....";
                    progBar.Visible = true;
                    waitlblfr.Visible = true;
                    waitlblto.Visible = true;
                    waitlbl.Visible = true;
                    progBar.Update();
                    ReportDocument cryRpt1 = new ReportDocument(); 
                    if (progBar != null) { progBar.Value = 5; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }
                    cryRpt1.Load(rpDirectory + "\\expnote.rpt");
                    if (progBar != null) { progBar.Value = 50; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }
                    cryRpt1.SetDataSource(myDataSet.Tables[0]);
                    cryRpt1.Subreports["polh"].SetDataSource(myDataSet.Tables[1]);
                    cryRpt1.Subreports["endrmk"].SetDataSource(myDataSet.Tables[0]);
                    if (progBar != null) { progBar.Value = 95; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }
                    cryDocViewer temp_form = new cryDocViewer(cryRpt1);

                    try
                    {
                        ExportOptions CrExportOptions;
                        DiskFileDestinationOptions CrDiskFileDestinationOptions = new DiskFileDestinationOptions();
                        PdfRtfWordFormatOptions CrFormatTypeOptions = new PdfRtfWordFormatOptions();
                        CrDiskFileDestinationOptions.DiskFileName = "M:\\COIL_eExpireNote\\" + wl_fautono.Trim() + ".doc";

                        CrExportOptions = cryRpt1.ExportOptions;
                        {
                            CrExportOptions.ExportDestinationType = ExportDestinationType.DiskFile;
                            CrExportOptions.ExportFormatType = ExportFormatType.WordForWindows;
                            CrExportOptions.DestinationOptions = CrDiskFileDestinationOptions;
                            CrExportOptions.FormatOptions = CrFormatTypeOptions;
                        }
                        cryRpt1.Export();
                        Word2PDF WP = new Word2PDF();
                        WP.word2PDF("M:\\COIL_eExpireNote\\" + wl_fautono.Trim() + ".doc", "M:\\COIL_eExpireNote\\" + wl_fautono.Trim() + ".pdf");
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show(ex.ToString());
                    }

                    if (progBar != null) { progBar.Value = 100; waitlbl.Text = progBar.Value.ToString().Trim() + "%"; progBar.Update(); waitlbl.Update(); }
                    temp_form.ShowDialog();
                }
                catch (Exception ex)
                {
                    // LogHelper.Error(ex);
                }
            }
            else
            {
                try
                {
                    MessageBox.Show(this, "Invalid Print!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button2);
                }
                catch (Exception ex)
                {
                    // LogHelper.Error(ex);
                }
            }
            progBar.Value = 0;
            waitlbl.Text = "Processing .....";
            progBar.Visible = false;
            waitlblfr.Visible = false;
            waitlblto.Visible = false;
            waitlbl.Visible = false;
        }

        void exit()
        {
            this.Close();
        }

        private void dupdate_Click(object sender, EventArgs e)
        {
            update();
        }

        private void ddel_Click(object sender, EventArgs e)
        {
            delete();
        }

        private void savebtn_Click(object sender, EventArgs e)
        {
            Updateflag = false;
            save();
            btncontrol();
        }

        private void dPost_Click(object sender, EventArgs e)
        {
            Updateflag = false;
            confirm();
            btncontrol();
        }

        private void cancelbtn_Click(object sender, EventArgs e)
        {
            Updateflag = false;
            cancel();
            btncontrol();
        }

        private void dprint_Click(object sender, EventArgs e)
        {
            print();
            //pexpnote tempform = new pexpnote(this);
            //tempform.fctlid = fctlid;
            //tempform.ShowDialog();
        }

        private void dexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void exitbtn_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void printbtn_Click(object sender, EventArgs e)
        {
            print();
        }

        private void fconfirm_Click(object sender, EventArgs e)
        {
            confirm();
        }

        private void delbtn_Click(object sender, EventArgs e)
        {
            delete();
        }

        private void updbtn_Click(object sender, EventArgs e)
        {
            update();
        }
    }
}

