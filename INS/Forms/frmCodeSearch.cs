using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;

namespace INS
{
    public partial class frmCodeSearch : Form
    {
        public frmCodeSearch()
        {
            InitializeComponent();
            FillData("", "");
        }

        public frmCodeSearch(tginvh tginvh)
        {
            // TODO: Complete member initialization
            this.tginvh = tginvh;
            InitializeComponent();
        }

        DBConnect operate = new DBConnect();
        public string flag = "";
        private tginvh tginvh;

        public void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            if (order == "" && query == "")
            {
                a = new SqlDataAdapter("select fid as Code,fdesc as Desc# from mchgcode where ftype='C' order by fid", c);
            }
            else if (order != "")
            {
                a = new SqlDataAdapter("select fid as Code,fdesc as Desc# from mchgcode where ftype='C' order by '" + order + "'", c);
            }
            else if (query != "")
            {
                a = new SqlDataAdapter("select fid as Code,fdesc as Desc# from mchgcode where ftype='C' AND fdesc = '" + query + "'", c);
            }
            else
            {
                a = new SqlDataAdapter("select fid as Code,fdesc as Desc# from mchgcode where ftype='C' AND fdesc = '" + query + "' order by fid", c);
            }
            DataTable t = new DataTable();
            a.Fill(t);
            dataGridView1.DataSource = t;
            c.Close();
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }
                if (dataGridView1.Rows[row].Cells["Code"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Code"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["Code"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["Desc#"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            tginvh.CodeValue = Fct.stFat(textBox1.Text);
            tginvh.CodeDesc = Fct.stFat(label3.Text);
            this.Close();
        }


        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        

    }
}
