using INS.INSClass;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
//FZ2YHY48
namespace INS.Business.SearchForm
{
    public partial class confclm : Form
    {

        public string fctlid = "", page = "", flag = "";
        public string db = InsEnvironment.DataBase.GetDbm();
        private Claim.tClaim tClaim;
        private Claim.rClaim rClaim;
        private Claim.tclmeci tclmeci;
        private Claim.rclmeci rclmeci;
        public confclm()
        {
            InitializeComponent();
            Control();
        }

        public confclm(Claim.tClaim tClaim)
        {
            // TODO: Complete member initialization
            this.tClaim = tClaim;
            InitializeComponent();
            Control();
        }

        public confclm(Claim.rClaim rClaim)
        {
            // TODO: Complete member initialization
            this.rClaim = rClaim;
            InitializeComponent();
            Control();
        }

        public confclm(Claim.tclmeci tclmeci)
        {
            // TODO: Complete member initialization
            this.tclmeci = tclmeci;
            InitializeComponent();
            Control();
        }

        public confclm(Claim.rclmeci rclmeci)
        {
            // TODO: Complete member initialization
            this.rclmeci = rclmeci;
            InitializeComponent();
            Control();
        }

        public void Control()
        {
            String Sql = "select LEFT(CONVERT(VARCHAR,b.ld_faccdate, 120), 10) as ld_faccdate,fclmno from oclaim,  " +
                        "(select case when getdate() between fdatefr and  " +
                        "case when fextdto is null then fdateto else fextdto end " +
                        "then getdate() else null end ld_faccdate " +
                        "from " + db + "xsysperiod where fidtype ='CLMMON') b where fctlid ='" + fctlid + "'";
            DataTable dt = DBHelper.GetDataSet(Sql);
            string ofclmno = "", ld_faccdate = "";
            if (dt != null && dt.Rows.Count > 0)
            {
                ofclmno = dt.Rows[0]["fclmno"].ToString().Trim();
                ld_faccdate = dt.Rows[0]["ld_faccdate"].ToString().Trim();
            }
            if (ofclmno != "")
            {
                radioButton3.Visible = true;
                radioButton2.Visible = true;
                radioButton1.Visible = false;
            }
            else
            {
                radioButton3.Visible = false;
                radioButton2.Visible = true;
                radioButton1.Visible = true;

                //if (ld_faccdate == null || ld_faccdate == "") { radioButton2.Visible = false; } else { radioButton2.Visible = true; }
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            if (!radioButton2.Checked && !radioButton1.Checked && !radioButton3.Checked)
            { return; }
            if (radioButton2.Checked)
            {
                if (flag == "tClaim")
                {
                    if (!tClaim.Validation())
                    {
                        this.Close();
                        return;
                    }
                }
                if (flag == "rClaim")
                {
                    if (!rClaim.Validation())
                    {
                        this.Close();
                        return;
                    }
                }
                if (flag == "tclmeci")
                {
                    if (!tclmeci.Validation())
                    {
                        this.Close();
                        return;
                    }
                }
                if (flag == "rclmeci")
                {
                    if (!rclmeci.Validation())
                    {
                        this.Close();
                        return;
                    }
                }
            }
            ArrayList addsql = new ArrayList();
            DateTime time = DateTime.Now; bool isSucess = false;
            string Sql2 = "", fnature = "", fclass = "", lc_class = "", lc_losyr = "", ld_faccdate = "", fbus = "", fclmno = "", 
                ofclmno = "", lc_fretnsh="", flosdate = "", ffrm2date = "",bringupdate="";
            string Sql = "select fclass, case when fbus ='R' then 'RC'+rtrim(fclass) else 'C'+rtrim(fclass) end as lc_class,  " +
                        "fuwyr, fbus, flosdate,year(flosdate) as lc_losyr, LEFT(CONVERT(VARCHAR,b.ld_faccdate, 120), 10) as ld_faccdate," +
                        "fclmno,LEFT(CONVERT(VARCHAR,flosdate, 120), 10) as flosdate,LEFT(CONVERT(VARCHAR,ffrm2date, 120), 10) as ffrm2date from oclaim,  " +
                        "(select case when getdate() between fdatefr and fdateto then getdate() else fnxtdate end ld_faccdate  " +
                        "from " + db + "xsysperiod where fidtype ='CLMMON') b where fctlid ='" + fctlid + "'";
            DataTable dt = DBHelper.GetDataSet(Sql);
            if (dt != null && dt.Rows.Count > 0)
            {
                lc_class = dt.Rows[0]["lc_class"].ToString().Trim();
                lc_losyr = dt.Rows[0]["lc_losyr"].ToString().Trim();
                fbus = dt.Rows[0]["fbus"].ToString().Trim();
                ld_faccdate = dt.Rows[0]["ld_faccdate"].ToString().Trim();
                ofclmno = dt.Rows[0]["fclmno"].ToString().Trim();
                fclass = dt.Rows[0]["fclass"].ToString().Trim();
                flosdate = dt.Rows[0]["flosdate"].ToString().Trim();
                DateTime b = Convert.ToDateTime(flosdate);
                bringupdate = b.AddMonths(7).ToString("yyyy-MM-dd");
                ffrm2date = dt.Rows[0]["ffrm2date"].ToString().Trim();
            }
            string facstr = "select b.fshare as fretnsh,e.fshare as fttysh,e.fttycode,e.fttysec,c.fshare as ffacbsh,d.fshare as ffacsh " +
                                    "from (select fshare,fctlid_1,fkind,fctlid_2 from orid1 where fid ='Retention' ) b  " +
                                    "left join (select fshare,fctlid_1 from orid1 where fid ='Fac Oblig') c on b.fctlid_1= c.fctlid_1 " +
                                    "left join (select fshare,fctlid_1 from orid1 where fid ='Facultative') d on b.fctlid_1= d.fctlid_1 " +
                                    "left join (select fshare,fttycode,fttysec,fctlid_1 from orid1 where fid ='Surplus Treaty') e on b.fctlid_1= e.fctlid_1 " +
                                    "where b.fctlid_1 = (select fctlid_p from oclaim where fctlid ='" + fctlid + "')";
            DataTable facdt = DBHelper.GetDataSet(facstr);
            if (facdt != null && facdt.Rows.Count > 0)
            { lc_fretnsh = facdt.Rows[0]["fretnsh"].ToString().Trim(); }

            if (ofclmno == "")
            {
                String Sql1 = "select rtrim(fprefix) + rtrim(fnxtid) as fclmno from " + db + "xsysparm where fidtype='" + lc_class + "' AND fuy ='" + lc_losyr + "'";
                DataTable dt1 = DBHelper.GetDataSet(Sql1);
                if (dt1 != null && dt1.Rows.Count > 0)
                {
                    fclmno = dt1.Rows[0]["fclmno"].ToString().Trim();
                }
                Sql2 = "update " + db + "xsysparm  set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 4) where fidtype='" + lc_class + "' AND fuy ='" + lc_losyr + "'";
            }
            string updstr = "", updstr1 = "", updstr2 = "", updstr3 = "", updstr4 = "", updstr5 = "", updstr6 = "", updstr7 = "", updstr8 = "", updstr9 = "";
            if (page == "tabPage2")
            {
                if (ld_faccdate == "") { ld_faccdate = time.ToString("yyyy-MM-dd HH:mm:ss"); }
                if (radioButton1.Checked)
                {
                    updstr = "update oclaim set fclmno ='" + fclmno + "',faccdate='" + ld_faccdate + "',fstatus =2, fcnfuser = '" + InsEnvironment.LoginUser.GetUserCode() + "' ,fcnfdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "'  where fctlid='" + fctlid + "'";
                    updstr1 = "update oceci set fclmno ='" + fclmno + "' where fctlid_1='" + fctlid + "'";
                }
                else if ((radioButton2.Checked))
                {
                    if (ofclmno != "") { fclmno = ofclmno; }
                    updstr = "update oclaim set fclmno ='" + fclmno + "',faccdate='" + ld_faccdate + "',fstatus =3, fcnfuser = '" + InsEnvironment.LoginUser.GetUserCode() + "' ,fcnfdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "'  where fctlid='" + fctlid + "'";
                    if (fclass == "PMP" || fclass == "CMP")
                    {
                        updstr6 = "insert into oclaim_s " +
                            "select fctlid as fctlid_1, '" + Fct.NewId("OCLAIM_S") + "' as fctlid,'2' as fnature,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,  " +
                            "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0 from oclaim where fctlid='" + fctlid + "'";
                        updstr1 = "insert into oclaim_s " +
                           "select fctlid as fctlid_1, '" + (int.Parse(Fct.NewId("OCLAIM_S")) + 1).ToString().PadLeft(10, '0') + "' as fctlid,'1' as fnature,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,  " +
                           "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0 from oclaim where fctlid='" + fctlid + "'";
                    }
                    else
                    {
                        updstr1 = "insert into oclaim_s " +
                            "select fctlid as fctlid_1, '" + Fct.NewId("OCLAIM_S") + "' as fctlid,'1' as fnature,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,  " +
                            "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0 from oclaim where fctlid='" + fctlid + "'";
                    }
                    updstr2 = "insert into oclaim_a " +
                              "select fctlid as fctlid_1,'" + Fct.NewId("OCLAIM_A") + "' as fctlid,'NEW' as famend,1 as flogseq,'" + ld_faccdate + "' as faccdate,frepdate,flosdate,ffrm2date, " +
                              "fuwyr,fsec,fcover,frno,fprdr,fclnt,fsrcclm,fcedeclm,fitem,flosloc,fpartr_t1,fpartr_t2,fnatlos,fadjr1,fadjr2,fsolr1,fsolr2,frcrdate,frcrto, " +
                              "fhlhc,3 as fstatus,fmode,fdestroy,fdesdate,'',finpuser,finpdate,fupduser,fupddate,'" + InsEnvironment.LoginUser.GetUserCode() + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' from oclaim where fctlid='" + fctlid + "'";
                    updstr3 = "insert into ocabk_oe " +
                              "select '" + Fct.NewId("OCABK_OE") + "' as fctlid,fctlid as fctlid_c, 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0, " +
                              "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0 from oclaim where fctlid='" + fctlid + "'";
                    updstr4 = "insert into ocabk_oc " +
                               "select '" + Fct.NewId("OCABK_OC") + "' as fctlid,fctlid as fctlid_c,'1' as fzeropay,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0, " +
                               "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0 from oclaim where fctlid='" + fctlid + "'";

                    if (fclass == "EEC"  && lc_fretnsh == "100.0000" && (ffrm2date ==""))
                    {

                        updstr7 = "INSERT INTO [dbo].[oclaim_log]([fctlid_1],[fclass],[fclmno],[flogseq],[finpdate],[fbringdate],[ftoken],[fcycle],[fnote],[fprogress],[fstatus],[finpuser],[fupduser],[fupddate],[fcnfuser],[fcnfdate],casehandler) VALUES(" +
                                "'" + fctlid + "','EEC','" + fclmno + "','1','" + time.ToString("yyyy-MM-dd") + "','" + bringupdate + "' ,'Close claims','Notification only','','','3','Admin','Admin','" + time.ToString("yyyy-MM-dd") + "','Admin','" + time.ToString("yyyy-MM-dd") + "','Admin')";

                        updstr = "update oclaim set fclmno ='" + fclmno + "',faccdate='" + ld_faccdate + "',fstatus =3, " +
                                "fosamt= 100000,fosretn=100000, fincamt=100000, fincretn=100000, " +
                                "fcnfuser = '" + InsEnvironment.LoginUser.GetUserCode() + "' ,fcnfdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "'  " +
                                "where fctlid='" + fctlid + "'";

                        updstr1 = "insert into oclaim_s " +
                                "select fctlid as fctlid_1, '" + Fct.NewId("OCLAIM_S") + "' as fctlid,'1' as fnature,100000,100000,0,0,0,0,0,0,0,0,0,0,0,0,100000,100000,0,0,0,0,  " +
                                "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0 from oclaim where fctlid='" + fctlid + "'";

                        updstr3 = "insert into ocabk_oe " +
                              "select '" + Fct.NewId("OCABK_OE") + "' as fctlid,fctlid as fctlid_c, 100000,0,0,0,0,0,100000,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0, " +
                              "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,100000,0,0,0,0,0,100000,0,0,0,0,0,0,0,0,0,0,0,0,0,0,100000,0,0,0,0,0,100000 from oclaim where fctlid='" + fctlid + "'";

                        updstr8 = "insert into ocabk_e " +
                               "select '" + Fct.NewId("OCABK_E") + "' as fctlid,fctlid as fctlid_c,'" + Fct.NewId("ocadj") + "', 1,1,1,0,0,0,NULL,NULL,0,NULL,NULL,0,NULL,NULL,0,NULL,NULL,0,NULL,NULL,0,NULL,NULL,0,NULL,NULL,0,NULL,NULL,0,NULL,NULL," +
                               "0,NULL,NULL,0,NULL,NULL,0,NULL,NULL,0,NULL,NULL,0,NULL,NULL,0,NULL,NULL,0,1,1,0,0,0,0,0," +
                               "100000,100000,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,100000,100000,0,0,0,0,0,0,0,100000,100000,'' from oclaim where fctlid='" + fctlid + "'";

                        updstr9 = "INSERT INTO [dbo].[ocadj] " +
                                  "select 'EEC',fsclass,fclmno,fpolno,fendtno,fctlid_p,fctlid_e,fctlid,'" + Fct.NewId("ocadj") + "',1,1,1, faccdate, 100000.0000,  " +
                                  "0,100000.0000,100000.0000,0,0,0,0,0,'','','','',0,null,'','',3,'Admin','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','Admin','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','Admin','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' from oclaim " +
                                  " where fctlid = '" + fctlid + "'";

                    }
                    if (fclass == "PMP" || fclass == "CMP")
                    {
                        addsql.Add("update " + db + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+2)), 10) where fidtype ='OCLAIM_S'");
                    }
                    else
                    {
                        addsql.Add("update " + db + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='OCLAIM_S'");
                    }
                    addsql.Add("update " + db + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='OCLAIM_A'");
                    addsql.Add("update " + db + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='OCABK_OE'");
                    addsql.Add("update " + db + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='OCABK_OC'");
                    addsql.Add("update " + db + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='ocabk_e'");
                    addsql.Add("update " + db + "xsysparm set fnxtid=RIGHT('0000000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype ='ocadj'");
                    updstr5 = "update oceci set fclmno ='" + fclmno + "' where fctlid_1='" + fctlid + "'";
                }
                else
                {
                    updstr = "update oclaim set fstatus = 4, fcnfuser = '" + InsEnvironment.LoginUser.GetUserCode() + "' ,fcnfdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "'  where fctlid='" + fctlid + "'";
                }
            }

            string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                SqlCommand command = connection.CreateCommand();
                SqlTransaction transaction;

                // Start a local transaction.
                transaction = connection.BeginTransaction("SampleTransaction");

                // Must assign both transaction object and connection 
                // to Command object for a pending local transaction
                command.Connection = connection;
                command.Transaction = transaction;

                try
                {
                    command.CommandText = updstr;
                    command.ExecuteNonQuery();
                    if (updstr1 != null && updstr1 != "")
                    {
                        command.CommandText = updstr1;
                        command.ExecuteNonQuery();
                    }
                    if (updstr6 != null && updstr6 != "")
                    {
                        command.CommandText = updstr6;
                        command.ExecuteNonQuery();
                    }
                    if (updstr2 != null && updstr2 != "")
                    {
                        command.CommandText = updstr2;
                        command.ExecuteNonQuery();
                    }
                    if (updstr3 != null && updstr3 != "")
                    {
                        command.CommandText = updstr3;
                        command.ExecuteNonQuery();
                    }
                    if (updstr4 != null && updstr4 != "")
                    {
                        command.CommandText = updstr4;
                        command.ExecuteNonQuery();
                    }
                    if (updstr5 != null && updstr5 != "")
                    {
                        command.CommandText = updstr5;
                        command.ExecuteNonQuery();
                    }
                    if (updstr7 != null && updstr7 != "")
                    {
                        command.CommandText = updstr7;
                        command.ExecuteNonQuery();
                    }
                    if (updstr8 != null && updstr8 != "")
                    {
                        command.CommandText = updstr8;
                        command.ExecuteNonQuery();
                    }
                    if (updstr9 != null && updstr9 != "")
                    {
                        command.CommandText = updstr9;
                        command.ExecuteNonQuery();
                    }
                    if (ofclmno == "" && !radioButton3.Checked)
                    {
                        command.CommandText = Sql2;
                        command.ExecuteNonQuery();
                    }
                    string[] arr = (string[])addsql.ToArray(typeof(string));
                    if (arr != null)
                    {
                        for (int i = 0; i < arr.Length; i++)
                        {
                            if (addsql[i] != "" && addsql[i] != null)
                            {
                                command.CommandText = arr[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    transaction.Commit();
                    if (radioButton2.Checked)
                    {
                        isSucess = u_geninv();
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show("Haven't Been Confirmed", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                    Console.WriteLine("  Message: {0}", ex.Message);

                    // Attempt to roll back the transaction. 
                    try
                    {
                        transaction.Rollback();
                    }
                    catch (Exception ex2)
                    {
                        // This catch block will handle any errors that may have occurred 
                        // on the server that would cause the rollback to fail, such as 
                        // a closed connection.
                        Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                        Console.WriteLine("  Message: {0}", ex2.Message);
                    }
                }
                connection.Close();
            }
            if (tClaim != null) { tClaim.ConfirmRlt = fctlid; }
            if (rClaim != null) { rClaim.ConfirmRlt = fctlid; }
            if (tclmeci != null) { tclmeci.ConfirmRlt = fctlid; }
            if (rclmeci != null) { rclmeci.ConfirmRlt = fctlid; }
            this.Close();
        }

        bool u_geninv()
        {
            return true;
        }
    }
}
