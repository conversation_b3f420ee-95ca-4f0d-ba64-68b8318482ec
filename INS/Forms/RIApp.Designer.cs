namespace INS
{
    partial class RIApp
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            this.Clauses = new System.Windows.Forms.TabPage();
            this.fprngr = new System.Windows.Forms.ComboBox();
            this.label23 = new System.Windows.Forms.Label();
            this.label22 = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.label18 = new System.Windows.Forms.Label();
            this.label17 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.frinsrref = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.btprint = new System.Windows.Forms.Button();
            this.btPost = new System.Windows.Forms.Button();
            this.fissdate = new System.Windows.Forms.TextBox();
            this.label21 = new System.Windows.Forms.Label();
            this.foutgo = new System.Windows.Forms.TextBox();
            this.label19 = new System.Windows.Forms.Label();
            this.friapp = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.fcnfdate = new System.Windows.Forms.TextBox();
            this.fcnfuser = new System.Windows.Forms.TextBox();
            this.fremark = new System.Windows.Forms.TextBox();
            this.lfgpm = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.fdbtrdesc = new System.Windows.Forms.TextBox();
            this.fdbtr = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.fupddate = new System.Windows.Forms.TextBox();
            this.fupduser = new System.Windows.Forms.TextBox();
            this.label15 = new System.Windows.Forms.Label();
            this.finpdate = new System.Windows.Forms.TextBox();
            this.finpuser = new System.Windows.Forms.TextBox();
            this.label14 = new System.Windows.Forms.Label();
            this.fdbtrtype = new System.Windows.Forms.ComboBox();
            this.label13 = new System.Windows.Forms.Label();
            this.fposted = new System.Windows.Forms.ComboBox();
            this.label12 = new System.Windows.Forms.Label();
            this.falter1 = new System.Windows.Forms.TextBox();
            this.label9 = new System.Windows.Forms.Label();
            this.finsd = new System.Windows.Forms.TextBox();
            this.label8 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.fendtno = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.fpolno = new System.Windows.Forms.TextBox();
            this.fcession = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.dsave = new System.Windows.Forms.Button();
            this.dcancel = new System.Windows.Forms.Button();
            this.dexit = new System.Windows.Forms.Button();
            this.dupdate = new System.Windows.Forms.Button();
            this.fshareb = new Shorty.Windows.Forms.NumericTextBox();
            this.fsharea = new Shorty.Windows.Forms.NumericTextBox();
            this.fpayable = new Shorty.Windows.Forms.NumericTextBox();
            this.fcomamt = new Shorty.Windows.Forms.NumericTextBox();
            this.fgpm = new Shorty.Windows.Forms.NumericTextBox();
            this.flimit = new Shorty.Windows.Forms.NumericTextBox();
            this.fsi = new Shorty.Windows.Forms.NumericTextBox();
            this.Classes = new System.Windows.Forms.TabPage();
            this.Postirng = new System.Windows.Forms.Button();
            this.button1 = new System.Windows.Forms.Button();
            this.rowlabel = new System.Windows.Forms.Label();
            this.lupdate = new System.Windows.Forms.Button();
            this.lexit = new System.Windows.Forms.Button();
            this.comboBox1 = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.dataGridView1 = new System.Windows.Forms.DataGridView();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.Clauses.SuspendLayout();
            this.Classes.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
            this.tabControl1.SuspendLayout();
            this.SuspendLayout();
            // 
            // Clauses
            // 
            this.Clauses.Controls.Add(this.fprngr);
            this.Clauses.Controls.Add(this.label23);
            this.Clauses.Controls.Add(this.label22);
            this.Clauses.Controls.Add(this.label20);
            this.Clauses.Controls.Add(this.label18);
            this.Clauses.Controls.Add(this.label17);
            this.Clauses.Controls.Add(this.label16);
            this.Clauses.Controls.Add(this.label5);
            this.Clauses.Controls.Add(this.frinsrref);
            this.Clauses.Controls.Add(this.label3);
            this.Clauses.Controls.Add(this.btprint);
            this.Clauses.Controls.Add(this.btPost);
            this.Clauses.Controls.Add(this.fissdate);
            this.Clauses.Controls.Add(this.label21);
            this.Clauses.Controls.Add(this.foutgo);
            this.Clauses.Controls.Add(this.label19);
            this.Clauses.Controls.Add(this.friapp);
            this.Clauses.Controls.Add(this.label11);
            this.Clauses.Controls.Add(this.fcnfdate);
            this.Clauses.Controls.Add(this.fcnfuser);
            this.Clauses.Controls.Add(this.fremark);
            this.Clauses.Controls.Add(this.lfgpm);
            this.Clauses.Controls.Add(this.label10);
            this.Clauses.Controls.Add(this.fdbtrdesc);
            this.Clauses.Controls.Add(this.fdbtr);
            this.Clauses.Controls.Add(this.label2);
            this.Clauses.Controls.Add(this.fupddate);
            this.Clauses.Controls.Add(this.fupduser);
            this.Clauses.Controls.Add(this.label15);
            this.Clauses.Controls.Add(this.finpdate);
            this.Clauses.Controls.Add(this.finpuser);
            this.Clauses.Controls.Add(this.label14);
            this.Clauses.Controls.Add(this.fdbtrtype);
            this.Clauses.Controls.Add(this.label13);
            this.Clauses.Controls.Add(this.fposted);
            this.Clauses.Controls.Add(this.label12);
            this.Clauses.Controls.Add(this.falter1);
            this.Clauses.Controls.Add(this.label9);
            this.Clauses.Controls.Add(this.finsd);
            this.Clauses.Controls.Add(this.label8);
            this.Clauses.Controls.Add(this.label7);
            this.Clauses.Controls.Add(this.fendtno);
            this.Clauses.Controls.Add(this.label6);
            this.Clauses.Controls.Add(this.fpolno);
            this.Clauses.Controls.Add(this.fcession);
            this.Clauses.Controls.Add(this.label4);
            this.Clauses.Controls.Add(this.dsave);
            this.Clauses.Controls.Add(this.dcancel);
            this.Clauses.Controls.Add(this.dexit);
            this.Clauses.Controls.Add(this.dupdate);
            this.Clauses.Controls.Add(this.fshareb);
            this.Clauses.Controls.Add(this.fsharea);
            this.Clauses.Controls.Add(this.fpayable);
            this.Clauses.Controls.Add(this.fcomamt);
            this.Clauses.Controls.Add(this.fgpm);
            this.Clauses.Controls.Add(this.flimit);
            this.Clauses.Controls.Add(this.fsi);
            this.Clauses.Location = new System.Drawing.Point(4, 22);
            this.Clauses.Name = "Clauses";
            this.Clauses.Padding = new System.Windows.Forms.Padding(3);
            this.Clauses.Size = new System.Drawing.Size(825, 601);
            this.Clauses.TabIndex = 1;
            this.Clauses.Text = "Detail";
            this.Clauses.UseVisualStyleBackColor = true;
            // 
            // fprngr
            // 
            this.fprngr.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.fprngr.Enabled = false;
            this.fprngr.FormattingEnabled = true;
            this.fprngr.Items.AddRange(new object[] {
            "Yes",
            "No"});
            this.fprngr.Location = new System.Drawing.Point(546, 377);
            this.fprngr.Name = "fprngr";
            this.fprngr.Size = new System.Drawing.Size(47, 20);
            this.fprngr.TabIndex = 104;
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(465, 375);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(63, 24);
            this.label23.TabIndex = 103;
            this.label23.Text = "Show Gross \r\nOn R/I App.";
            this.label23.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(465, 327);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(70, 12);
            this.label22.TabIndex = 101;
            this.label22.Text = "Fac Share (%)";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(465, 297);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(70, 12);
            this.label20.TabIndex = 99;
            this.label20.Text = "Fac Share (%)";
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(145, 445);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(99, 12);
            this.label18.TabIndex = 97;
            this.label18.Text = "Net Premium Ceded";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(145, 414);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(80, 12);
            this.label17.TabIndex = 95;
            this.label17.Text = "Total Deduction";
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(145, 382);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(76, 12);
            this.label16.TabIndex = 93;
            this.label16.Text = "Gross Premium";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(143, 327);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(62, 12);
            this.label5.TabIndex = 91;
            this.label5.Text = "T.P.L. Limit";
            // 
            // frinsrref
            // 
            this.frinsrref.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.frinsrref.Location = new System.Drawing.Point(396, 138);
            this.frinsrref.Name = "frinsrref";
            this.frinsrref.ReadOnly = true;
            this.frinsrref.Size = new System.Drawing.Size(254, 22);
            this.frinsrref.TabIndex = 90;
            this.frinsrref.TextChanged += new System.EventHandler(this.frinsrref_TextChanged);
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(341, 143);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(56, 12);
            this.label3.TabIndex = 89;
            this.label3.Text = "Rinsr Ref#";
            // 
            // btprint
            // 
            this.btprint.Location = new System.Drawing.Point(559, 563);
            this.btprint.Name = "btprint";
            this.btprint.Size = new System.Drawing.Size(75, 23);
            this.btprint.TabIndex = 87;
            this.btprint.Text = "Print";
            this.btprint.UseVisualStyleBackColor = true;
            this.btprint.Click += new System.EventHandler(this.btprint_Click);
            // 
            // btPost
            // 
            this.btPost.Location = new System.Drawing.Point(483, 563);
            this.btPost.Name = "btPost";
            this.btPost.Size = new System.Drawing.Size(75, 23);
            this.btPost.TabIndex = 83;
            this.btPost.Text = "Posting";
            this.btPost.UseVisualStyleBackColor = true;
            this.btPost.Click += new System.EventHandler(this.btPost_Click);
            // 
            // fissdate
            // 
            this.fissdate.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.fissdate.Location = new System.Drawing.Point(636, 44);
            this.fissdate.Name = "fissdate";
            this.fissdate.ReadOnly = true;
            this.fissdate.Size = new System.Drawing.Size(88, 22);
            this.fissdate.TabIndex = 78;
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(584, 48);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(44, 12);
            this.label21.TabIndex = 77;
            this.label21.Text = "Iss. Date";
            // 
            // foutgo
            // 
            this.foutgo.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.foutgo.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper;
            this.foutgo.Location = new System.Drawing.Point(146, 138);
            this.foutgo.Name = "foutgo";
            this.foutgo.ReadOnly = true;
            this.foutgo.Size = new System.Drawing.Size(179, 22);
            this.foutgo.TabIndex = 74;
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(93, 143);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(55, 12);
            this.label19.TabIndex = 73;
            this.label19.Text = "Outgoing#";
            // 
            // friapp
            // 
            this.friapp.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.friapp.Location = new System.Drawing.Point(396, 108);
            this.friapp.Name = "friapp";
            this.friapp.ReadOnly = true;
            this.friapp.Size = new System.Drawing.Size(102, 22);
            this.friapp.TabIndex = 72;
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(342, 114);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(43, 12);
            this.label11.TabIndex = 71;
            this.label11.Text = "R/I App";
            // 
            // fcnfdate
            // 
            this.fcnfdate.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.fcnfdate.Location = new System.Drawing.Point(270, 517);
            this.fcnfdate.Name = "fcnfdate";
            this.fcnfdate.ReadOnly = true;
            this.fcnfdate.Size = new System.Drawing.Size(167, 22);
            this.fcnfdate.TabIndex = 70;
            // 
            // fcnfuser
            // 
            this.fcnfuser.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.fcnfuser.Location = new System.Drawing.Point(199, 517);
            this.fcnfuser.Name = "fcnfuser";
            this.fcnfuser.ReadOnly = true;
            this.fcnfuser.Size = new System.Drawing.Size(71, 22);
            this.fcnfuser.TabIndex = 69;
            // 
            // fremark
            // 
            this.fremark.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.fremark.Location = new System.Drawing.Point(147, 238);
            this.fremark.MaxLength = 5500;
            this.fremark.Multiline = true;
            this.fremark.Name = "fremark";
            this.fremark.ReadOnly = true;
            this.fremark.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            this.fremark.Size = new System.Drawing.Size(503, 34);
            this.fremark.TabIndex = 66;
            // 
            // lfgpm
            // 
            this.lfgpm.AutoSize = true;
            this.lfgpm.Location = new System.Drawing.Point(143, 297);
            this.lfgpm.Name = "lfgpm";
            this.lfgpm.Size = new System.Drawing.Size(91, 12);
            this.lfgpm.TabIndex = 18;
            this.lfgpm.Text = "Total Sum Insured";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(93, 241);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(42, 12);
            this.label10.TabIndex = 65;
            this.label10.Text = "Remark";
            // 
            // fdbtrdesc
            // 
            this.fdbtrdesc.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.fdbtrdesc.Location = new System.Drawing.Point(218, 77);
            this.fdbtrdesc.Name = "fdbtrdesc";
            this.fdbtrdesc.ReadOnly = true;
            this.fdbtrdesc.Size = new System.Drawing.Size(340, 22);
            this.fdbtrdesc.TabIndex = 58;
            // 
            // fdbtr
            // 
            this.fdbtr.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.fdbtr.Location = new System.Drawing.Point(146, 77);
            this.fdbtr.Name = "fdbtr";
            this.fdbtr.ReadOnly = true;
            this.fdbtr.Size = new System.Drawing.Size(71, 22);
            this.fdbtr.TabIndex = 57;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(94, 82);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(30, 12);
            this.label2.TabIndex = 56;
            this.label2.Text = "A/C#";
            // 
            // fupddate
            // 
            this.fupddate.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.fupddate.Location = new System.Drawing.Point(545, 493);
            this.fupddate.Name = "fupddate";
            this.fupddate.ReadOnly = true;
            this.fupddate.Size = new System.Drawing.Size(167, 22);
            this.fupddate.TabIndex = 55;
            // 
            // fupduser
            // 
            this.fupduser.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.fupduser.Location = new System.Drawing.Point(467, 493);
            this.fupduser.Name = "fupduser";
            this.fupduser.ReadOnly = true;
            this.fupduser.Size = new System.Drawing.Size(78, 22);
            this.fupduser.TabIndex = 54;
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(398, 496);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(61, 12);
            this.label15.TabIndex = 53;
            this.label15.Text = "Updated By";
            // 
            // finpdate
            // 
            this.finpdate.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.finpdate.Location = new System.Drawing.Point(216, 493);
            this.finpdate.Name = "finpdate";
            this.finpdate.ReadOnly = true;
            this.finpdate.Size = new System.Drawing.Size(175, 22);
            this.finpdate.TabIndex = 52;
            // 
            // finpuser
            // 
            this.finpuser.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.finpuser.Location = new System.Drawing.Point(146, 493);
            this.finpuser.Name = "finpuser";
            this.finpuser.ReadOnly = true;
            this.finpuser.Size = new System.Drawing.Size(71, 22);
            this.finpuser.TabIndex = 51;
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(81, 496);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(58, 12);
            this.label14.TabIndex = 50;
            this.label14.Text = "Created By";
            // 
            // fdbtrtype
            // 
            this.fdbtrtype.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.fdbtrtype.Enabled = false;
            this.fdbtrtype.FormattingEnabled = true;
            this.fdbtrtype.Items.AddRange(new object[] {
            "Broker",
            "Client",
            "Producer",
            "Insurer"});
            this.fdbtrtype.Location = new System.Drawing.Point(636, 78);
            this.fdbtrtype.Name = "fdbtrtype";
            this.fdbtrtype.Size = new System.Drawing.Size(99, 20);
            this.fdbtrtype.TabIndex = 49;
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(584, 79);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(51, 12);
            this.label13.TabIndex = 48;
            this.label13.Text = "A/C Type";
            // 
            // fposted
            // 
            this.fposted.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.fposted.Enabled = false;
            this.fposted.FormattingEnabled = true;
            this.fposted.Items.AddRange(new object[] {
            "Yes",
            "No"});
            this.fposted.Location = new System.Drawing.Point(146, 519);
            this.fposted.Name = "fposted";
            this.fposted.Size = new System.Drawing.Size(47, 20);
            this.fposted.TabIndex = 47;
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(81, 522);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(35, 12);
            this.label12.TabIndex = 46;
            this.label12.Text = "Posted";
            // 
            // falter1
            // 
            this.falter1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.falter1.Location = new System.Drawing.Point(147, 197);
            this.falter1.MaxLength = 5500;
            this.falter1.Multiline = true;
            this.falter1.Name = "falter1";
            this.falter1.ReadOnly = true;
            this.falter1.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            this.falter1.Size = new System.Drawing.Size(503, 34);
            this.falter1.TabIndex = 40;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(91, 202);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(57, 12);
            this.label9.TabIndex = 39;
            this.label9.Text = "Alternation";
            // 
            // finsd
            // 
            this.finsd.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.finsd.Location = new System.Drawing.Point(146, 168);
            this.finsd.Name = "finsd";
            this.finsd.ReadOnly = true;
            this.finsd.Size = new System.Drawing.Size(504, 22);
            this.finsd.TabIndex = 38;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(93, 171);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(40, 12);
            this.label8.TabIndex = 37;
            this.label8.Text = "Insured";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(93, 48);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(40, 12);
            this.label7.TabIndex = 35;
            this.label7.Text = "Policy#";
            // 
            // fendtno
            // 
            this.fendtno.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.fendtno.Location = new System.Drawing.Point(396, 46);
            this.fendtno.Name = "fendtno";
            this.fendtno.ReadOnly = true;
            this.fendtno.Size = new System.Drawing.Size(100, 22);
            this.fendtno.TabIndex = 34;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(345, 50);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(47, 12);
            this.label6.TabIndex = 33;
            this.label6.Text = "Endt No.";
            // 
            // fpolno
            // 
            this.fpolno.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.fpolno.Location = new System.Drawing.Point(146, 45);
            this.fpolno.Name = "fpolno";
            this.fpolno.ReadOnly = true;
            this.fpolno.Size = new System.Drawing.Size(100, 22);
            this.fpolno.TabIndex = 32;
            // 
            // fcession
            // 
            this.fcession.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.fcession.Location = new System.Drawing.Point(146, 109);
            this.fcession.Name = "fcession";
            this.fcession.ReadOnly = true;
            this.fcession.Size = new System.Drawing.Size(124, 22);
            this.fcession.TabIndex = 28;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(93, 114);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(41, 12);
            this.label4.TabIndex = 27;
            this.label4.Text = "Cession";
            // 
            // dsave
            // 
            this.dsave.Location = new System.Drawing.Point(511, 565);
            this.dsave.Name = "dsave";
            this.dsave.Size = new System.Drawing.Size(75, 21);
            this.dsave.TabIndex = 22;
            this.dsave.Text = "Save";
            this.dsave.UseVisualStyleBackColor = true;
            this.dsave.Visible = false;
            this.dsave.Click += new System.EventHandler(this.dsave_Click);
            // 
            // dcancel
            // 
            this.dcancel.Location = new System.Drawing.Point(608, 565);
            this.dcancel.Name = "dcancel";
            this.dcancel.Size = new System.Drawing.Size(75, 21);
            this.dcancel.TabIndex = 21;
            this.dcancel.Text = "Cancel";
            this.dcancel.UseVisualStyleBackColor = true;
            this.dcancel.Visible = false;
            this.dcancel.Click += new System.EventHandler(this.dcancel_Click);
            // 
            // dexit
            // 
            this.dexit.Location = new System.Drawing.Point(635, 564);
            this.dexit.Name = "dexit";
            this.dexit.Size = new System.Drawing.Size(75, 23);
            this.dexit.TabIndex = 18;
            this.dexit.Text = "Exit";
            this.dexit.UseVisualStyleBackColor = true;
            this.dexit.Click += new System.EventHandler(this.dexit_Click);
            // 
            // dupdate
            // 
            this.dupdate.Location = new System.Drawing.Point(142, 565);
            this.dupdate.Name = "dupdate";
            this.dupdate.Size = new System.Drawing.Size(75, 21);
            this.dupdate.TabIndex = 17;
            this.dupdate.Text = "Modify";
            this.dupdate.UseVisualStyleBackColor = true;
            this.dupdate.Click += new System.EventHandler(this.dupdate_Click);
            // 
            // fshareb
            // 
            this.fshareb.AllowNegative = true;
            this.fshareb.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.fshareb.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.fshareb.Location = new System.Drawing.Point(546, 321);
            this.fshareb.Name = "fshareb";
            this.fshareb.NumericPrecision = 17;
            this.fshareb.NumericScaleOnFocus = 4;
            this.fshareb.NumericScaleOnLostFocus = 4;
            this.fshareb.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fshareb.Size = new System.Drawing.Size(74, 22);
            this.fshareb.TabIndex = 102;
            this.fshareb.Text = "0";
            this.fshareb.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fshareb.ZeroIsValid = true;
            // 
            // fsharea
            // 
            this.fsharea.AllowNegative = true;
            this.fsharea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.fsharea.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.fsharea.Location = new System.Drawing.Point(546, 291);
            this.fsharea.Name = "fsharea";
            this.fsharea.NumericPrecision = 17;
            this.fsharea.NumericScaleOnFocus = 4;
            this.fsharea.NumericScaleOnLostFocus = 4;
            this.fsharea.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fsharea.Size = new System.Drawing.Size(74, 22);
            this.fsharea.TabIndex = 100;
            this.fsharea.Text = "0";
            this.fsharea.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fsharea.ZeroIsValid = true;
            // 
            // fpayable
            // 
            this.fpayable.AllowNegative = true;
            this.fpayable.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.fpayable.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.fpayable.Location = new System.Drawing.Point(240, 439);
            this.fpayable.Name = "fpayable";
            this.fpayable.NumericPrecision = 17;
            this.fpayable.NumericScaleOnFocus = 2;
            this.fpayable.NumericScaleOnLostFocus = 2;
            this.fpayable.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fpayable.Size = new System.Drawing.Size(138, 22);
            this.fpayable.TabIndex = 98;
            this.fpayable.Text = "0";
            this.fpayable.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fpayable.ZeroIsValid = true;
            // 
            // fcomamt
            // 
            this.fcomamt.AllowNegative = true;
            this.fcomamt.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.fcomamt.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.fcomamt.Location = new System.Drawing.Point(240, 408);
            this.fcomamt.Name = "fcomamt";
            this.fcomamt.NumericPrecision = 17;
            this.fcomamt.NumericScaleOnFocus = 2;
            this.fcomamt.NumericScaleOnLostFocus = 2;
            this.fcomamt.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fcomamt.Size = new System.Drawing.Size(138, 22);
            this.fcomamt.TabIndex = 96;
            this.fcomamt.Text = "0";
            this.fcomamt.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fcomamt.ZeroIsValid = true;
            // 
            // fgpm
            // 
            this.fgpm.AllowNegative = true;
            this.fgpm.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.fgpm.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.fgpm.Location = new System.Drawing.Point(240, 376);
            this.fgpm.Name = "fgpm";
            this.fgpm.NumericPrecision = 17;
            this.fgpm.NumericScaleOnFocus = 2;
            this.fgpm.NumericScaleOnLostFocus = 2;
            this.fgpm.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fgpm.Size = new System.Drawing.Size(138, 22);
            this.fgpm.TabIndex = 94;
            this.fgpm.Text = "0";
            this.fgpm.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fgpm.ZeroIsValid = true;
            // 
            // flimit
            // 
            this.flimit.AllowNegative = true;
            this.flimit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.flimit.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.flimit.Location = new System.Drawing.Point(240, 321);
            this.flimit.Name = "flimit";
            this.flimit.NumericPrecision = 17;
            this.flimit.NumericScaleOnFocus = 2;
            this.flimit.NumericScaleOnLostFocus = 2;
            this.flimit.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.flimit.Size = new System.Drawing.Size(138, 22);
            this.flimit.TabIndex = 92;
            this.flimit.Text = "0.00";
            this.flimit.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.flimit.ZeroIsValid = true;
            // 
            // fsi
            // 
            this.fsi.AllowNegative = true;
            this.fsi.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.fsi.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.fsi.Location = new System.Drawing.Point(240, 291);
            this.fsi.Name = "fsi";
            this.fsi.NumericPrecision = 17;
            this.fsi.NumericScaleOnFocus = 2;
            this.fsi.NumericScaleOnLostFocus = 2;
            this.fsi.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fsi.Size = new System.Drawing.Size(138, 22);
            this.fsi.TabIndex = 19;
            this.fsi.Text = "0.00";
            this.fsi.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fsi.ZeroIsValid = true;
            // 
            // Classes
            // 
            this.Classes.Controls.Add(this.Postirng);
            this.Classes.Controls.Add(this.button1);
            this.Classes.Controls.Add(this.rowlabel);
            this.Classes.Controls.Add(this.lupdate);
            this.Classes.Controls.Add(this.lexit);
            this.Classes.Controls.Add(this.comboBox1);
            this.Classes.Controls.Add(this.label1);
            this.Classes.Controls.Add(this.dataGridView1);
            this.Classes.Location = new System.Drawing.Point(4, 22);
            this.Classes.Name = "Classes";
            this.Classes.Padding = new System.Windows.Forms.Padding(3);
            this.Classes.Size = new System.Drawing.Size(825, 601);
            this.Classes.TabIndex = 0;
            this.Classes.Text = "List";
            this.Classes.UseVisualStyleBackColor = true;
            // 
            // Postirng
            // 
            this.Postirng.Location = new System.Drawing.Point(473, 527);
            this.Postirng.Name = "Postirng";
            this.Postirng.Size = new System.Drawing.Size(75, 23);
            this.Postirng.TabIndex = 12;
            this.Postirng.Text = "Postirng";
            this.Postirng.UseVisualStyleBackColor = true;
            this.Postirng.Visible = false;
            this.Postirng.Click += new System.EventHandler(this.Postirng_Click);
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(548, 527);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(75, 23);
            this.button1.TabIndex = 11;
            this.button1.Text = "Print";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // rowlabel
            // 
            this.rowlabel.AutoSize = true;
            this.rowlabel.Location = new System.Drawing.Point(351, 507);
            this.rowlabel.Name = "rowlabel";
            this.rowlabel.Size = new System.Drawing.Size(45, 12);
            this.rowlabel.TabIndex = 10;
            this.rowlabel.Text = "rowlabel";
            this.rowlabel.Visible = false;
            // 
            // lupdate
            // 
            this.lupdate.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.lupdate.Location = new System.Drawing.Point(52, 528);
            this.lupdate.Name = "lupdate";
            this.lupdate.Size = new System.Drawing.Size(75, 21);
            this.lupdate.TabIndex = 9;
            this.lupdate.Text = "Modify";
            this.lupdate.UseVisualStyleBackColor = true;
            this.lupdate.Click += new System.EventHandler(this.lupdate_Click);
            // 
            // lexit
            // 
            this.lexit.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.lexit.Location = new System.Drawing.Point(623, 527);
            this.lexit.Name = "lexit";
            this.lexit.Size = new System.Drawing.Size(75, 23);
            this.lexit.TabIndex = 6;
            this.lexit.Text = "Exit";
            this.lexit.UseVisualStyleBackColor = true;
            this.lexit.Click += new System.EventHandler(this.button9_Click);
            // 
            // comboBox1
            // 
            this.comboBox1.FormattingEnabled = true;
            this.comboBox1.Location = new System.Drawing.Point(75, 37);
            this.comboBox1.Name = "comboBox1";
            this.comboBox1.Size = new System.Drawing.Size(146, 20);
            this.comboBox1.TabIndex = 2;
            this.comboBox1.SelectedIndexChanged += new System.EventHandler(this.comboBox1_SelectedIndexChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(21, 40);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(49, 12);
            this.label1.TabIndex = 1;
            this.label1.Text = "Order By";
            // 
            // dataGridView1
            // 
            this.dataGridView1.AllowUserToAddRows = false;
            this.dataGridView1.AllowUserToDeleteRows = false;
            this.dataGridView1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridView1.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dataGridView1.BackgroundColor = System.Drawing.SystemColors.ButtonFace;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.SystemColors.Control;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("Courier New", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle1.ForeColor = System.Drawing.SystemColors.WindowText;
            dataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridView1.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.dataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView1.Location = new System.Drawing.Point(23, 65);
            this.dataGridView1.Name = "dataGridView1";
            this.dataGridView1.ReadOnly = true;
            this.dataGridView1.Size = new System.Drawing.Size(775, 435);
            this.dataGridView1.TabIndex = 0;
            this.dataGridView1.CurrentCellChanged += new System.EventHandler(this.dataGridView1_CurrentCellChanged);
            // 
            // tabControl1
            // 
            this.tabControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tabControl1.Appearance = System.Windows.Forms.TabAppearance.FlatButtons;
            this.tabControl1.Controls.Add(this.Classes);
            this.tabControl1.Controls.Add(this.Clauses);
            this.tabControl1.ItemSize = new System.Drawing.Size(48, 18);
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(833, 627);
            this.tabControl1.TabIndex = 0;
            this.tabControl1.SelectedIndexChanged += new System.EventHandler(this.tabControl1_SelectedIndexChanged);
            // 
            // RIApp
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.AutoSize = true;
            this.ClientSize = new System.Drawing.Size(834, 627);
            this.Controls.Add(this.tabControl1);
            this.Name = "RIApp";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "R/I App";
            this.Clauses.ResumeLayout(false);
            this.Clauses.PerformLayout();
            this.Classes.ResumeLayout(false);
            this.Classes.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
            this.tabControl1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabPage Clauses;
        private System.Windows.Forms.TabPage Classes;
        private System.Windows.Forms.ComboBox comboBox1;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.DataGridView dataGridView1;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.Button dexit;
        private System.Windows.Forms.Button dupdate;
        private System.Windows.Forms.Button dcancel;
        private System.Windows.Forms.Button dsave;
        private System.Windows.Forms.Button lexit;
        private System.Windows.Forms.Button lupdate;
        private System.Windows.Forms.ComboBox fdbtrtype;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.ComboBox fposted;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.TextBox falter1;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.TextBox finsd;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.TextBox fendtno;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.TextBox fpolno;
        private System.Windows.Forms.TextBox fcession;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox fupddate;
        private System.Windows.Forms.TextBox fupduser;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.TextBox finpdate;
        private System.Windows.Forms.TextBox finpuser;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label rowlabel;
        private System.Windows.Forms.TextBox fdbtrdesc;
        private System.Windows.Forms.TextBox fdbtr;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox fremark;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.TextBox fcnfdate;
        private System.Windows.Forms.TextBox fcnfuser;
        private System.Windows.Forms.TextBox friapp;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.TextBox foutgo;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.TextBox fissdate;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.Button btPost;
        private System.Windows.Forms.Button btprint;
        private System.Windows.Forms.Button Postirng;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.TextBox frinsrref;
        private System.Windows.Forms.Label label3;
        private Shorty.Windows.Forms.NumericTextBox fpayable;
        private System.Windows.Forms.Label label18;
        private Shorty.Windows.Forms.NumericTextBox fcomamt;
        private System.Windows.Forms.Label label17;
        private Shorty.Windows.Forms.NumericTextBox fgpm;
        private System.Windows.Forms.Label label16;
        private Shorty.Windows.Forms.NumericTextBox flimit;
        private System.Windows.Forms.Label label5;
        private Shorty.Windows.Forms.NumericTextBox fsi;
        private System.Windows.Forms.Label lfgpm;
        private Shorty.Windows.Forms.NumericTextBox fsharea;
        private System.Windows.Forms.Label label20;
        private Shorty.Windows.Forms.NumericTextBox fshareb;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.ComboBox fprngr;
        private System.Windows.Forms.Label label23;


    }
}