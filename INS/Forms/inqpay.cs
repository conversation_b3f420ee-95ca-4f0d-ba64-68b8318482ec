using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business
{
    public partial class inqpay : Form
    {
        private Ctrl.DirectGen directGen;


        public inqpay()
        {
            InitializeComponent();
        }

        public inqpay(Ctrl.DirectGen directGen)
        {
            // TODO: Complete member initialization
            this.directGen = directGen;
            InitializeComponent();
        }

        public void Filldata(string fctlid){
            string sql = "SELECT a.fctlid,a.fpolno,a.fendtno,a.fsrcref,a.fcedepol,a.fcedeendt,a.finvno, "+
                        "CONVERT(varchar, a.finstall)+'/'+CONVERT(varchar, a.ftotinstal) as Install#, "+
                        "a.finstall,a.ftotinstal,a.finvdate,a.fbilldate,a.fpmcur as fcur,a.famount, " +
                        "b.fctlid as fctlid_s,b.fsetamt,b.ftsetdr,b.ftsetcr,b.fbalance " +
		                "FROM oinvh a  "+
		                "left join ostat b  "+
		                "on a.fctlid = b.fctlid_i and a.fmodule = b.fmodule  "+
		                "where a.fstatus = '3' and a.fctlid_1 in  "+
                        "(select fctlid from polh where fctlid_p = '" + fctlid + "')  " +
		                "order by a.fpolno,a.fendtno,a.finstall,a.finvno";
            dataGridView1.DataSource = DBHelper.GetDataSet(sql);
            foreach (DataGridViewColumn Column in dataGridView1.Columns)
            {
                Column.Visible = false;
            }
            dataGridView1.Columns["fpolno"].Visible = true;
            dataGridView1.Columns["fendtno"].Visible = true;
            dataGridView1.Columns["finvno"].Visible = true;
            dataGridView1.Columns["Install#"].Visible = true;
            dataGridView1.Columns["finvdate"].Visible = true;
            dataGridView1.Columns["fbilldate"].Visible = true;
            dataGridView1.Columns["famount"].Visible = true;
            dataGridView1.Columns["fsetamt"].Visible = true;
            dataGridView1.Columns["fbalance"].Visible = true;
            dataGridView1.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.dataGridView1_CellFormatting);
        }

        private void dataGridView1_CellFormatting(object sender, System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            dataGridView1.Columns["fpolno"].HeaderText = "Policy#";
            dataGridView1.Columns["fendtno"].HeaderText = "Endt No.";
            dataGridView1.Columns["finvdate"].HeaderText = "Doc Date";
            dataGridView1.Columns["finvdate"].DefaultCellStyle.Format = "dd'/'MM'/'yyyy";
            dataGridView1.Columns["finvno"].HeaderText = "Dr/Cr No.";
            dataGridView1.Columns["fbilldate"].HeaderText = "Bill Date";
            dataGridView1.Columns["fbilldate"].DefaultCellStyle.Format = "dd'/'MM'/'yyyy";
            dataGridView1.Columns["famount"].HeaderText = "Amount";
            dataGridView1.Columns["fsetamt"].HeaderText = "Settlement";
            dataGridView1.Columns["fbalance"].HeaderText = "Balance";
        }

        private void dataGridView1_CurrentCellChanged(object sender, EventArgs e)
        {   int counter=0;
            if (dataGridView1.CurrentCell != null) {
                counter = dataGridView1.CurrentCell.RowIndex;
            }
            if (dataGridView1.Rows[counter].Cells["fpolno"].Value != null)
            {
                if (dataGridView1.Rows[counter].Cells["fpolno"].Value.ToString().Length != 0)
                {
                    fpolno.Text = dataGridView1.Rows[counter].Cells["fpolno"].Value.ToString().Trim();
                }
            }
            if (dataGridView1.Rows[counter].Cells["fendtno"].Value != null)
            {
                if (dataGridView1.Rows[counter].Cells["fendtno"].Value.ToString().Length != 0)
                {
                    fendtno.Text = dataGridView1.Rows[counter].Cells["fendtno"].Value.ToString().Trim();
                }
            }
            if (dataGridView1.Rows[counter].Cells["finvno"].Value != null)
            {
                if (dataGridView1.Rows[counter].Cells["finvno"].Value.ToString().Length != 0)
                {
                    finvno.Text = dataGridView1.Rows[counter].Cells["finvno"].Value.ToString().Trim();
                }
            }
            if (dataGridView1.Rows[counter].Cells["finstall"].Value != null)
            {
                if (dataGridView1.Rows[counter].Cells["finstall"].Value.ToString().Length != 0)
                {
                    finstall.Text = dataGridView1.Rows[counter].Cells["finstall"].Value.ToString().Trim();
                }
            }
            if (dataGridView1.Rows[counter].Cells["ftotinstal"].Value != null)
            {
                if (dataGridView1.Rows[counter].Cells["ftotinstal"].Value.ToString().Length != 0)
                {
                    ftotinstal.Text = dataGridView1.Rows[counter].Cells["ftotinstal"].Value.ToString().Trim();
                }
            }
            if (dataGridView1.Rows[counter].Cells["fcur"].Value != null)
            {
                if (dataGridView1.Rows[counter].Cells["fcur"].Value.ToString().Length != 0)
                {
                    fcur.Text = dataGridView1.Rows[counter].Cells["fcur"].Value.ToString().Trim();
                }
            }
            if (dataGridView1.Rows[counter].Cells["famount"].Value != null)
            {
                if (dataGridView1.Rows[counter].Cells["famount"].Value.ToString().Length != 0)
                {
                    famount.Text = Convert.ToDecimal(dataGridView1.Rows[counter].Cells["famount"].Value.ToString().Trim()).ToString("n2");
                }
            }
            if (dataGridView1.Rows[counter].Cells["fsetamt"].Value != null)
            {
                if (dataGridView1.Rows[counter].Cells["fsetamt"].Value.ToString().Length != 0)
                {
                    setamt.Text = Convert.ToDecimal(dataGridView1.Rows[counter].Cells["fsetamt"].Value.ToString().Trim()).ToString("n2");
                }
            }
            if (dataGridView1.Rows[counter].Cells["fbalance"].Value != null)
            {
                if (dataGridView1.Rows[counter].Cells["fbalance"].Value.ToString().Length != 0)
                {
                    fbalance.Text = Convert.ToDecimal(dataGridView1.Rows[counter].Cells["fbalance"].Value.ToString().Trim()).ToString("n2");
                }
            }
            string fctlid_s = dataGridView1.Rows[counter].Cells["fctlid_s"].Value.ToString().Trim();
            Gridview2(fctlid_s);
        }

        void Gridview2(string vfctlid_s)
        { 
             string sql ="SELECT a.fdocno,b.fothref,a.fdocdate, "+
   				"a.finvset,a.fset_g,a.fset_c,a.fset_s,a.fadj,b.fposted "+
		        "FROM oacset a, oacjnl b "+
                "where	a.finvset <> 0 and a.fctlid_j = b.fctlid and a.fctlid_s = '" + vfctlid_s + "' " +
		        "order by a.fdocdate";
             s1a.DataSource = DBHelper.GetDataSet(sql);
             foreach (DataGridViewColumn Column in s1a.Columns)
             {
                 Column.Visible = false;
             }
             s1a.Columns["fdocno"].Visible = true;
             s1a.Columns["fothref"].Visible = true;
             s1a.Columns["fdocdate"].Visible = true;
             s1a.Columns["finvset"].Visible = true;
             s1a.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.s1a_CellFormatting);
        }

        private void s1a_CellFormatting(object sender, System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            s1a.Columns["fdocno"].HeaderText = "Journal#";
            s1a.Columns["fothref"].HeaderText = "Other Ref.";
            s1a.Columns["fdocdate"].HeaderText = "Doc Date";
            s1a.Columns["fdocdate"].DefaultCellStyle.Format = "dd'/'MM'/'yyyy";
            s1a.Columns["finvset"].HeaderText = "Settled Amount";
        }

        private void s1a_CurrentCellChanged(object sender, EventArgs e)
        {
            int counter = 0;
            if (s1a.CurrentCell != null)
            {
                counter = s1a.CurrentCell.RowIndex;
            }
            if (s1a.Rows[counter].Cells["fdocno"].Value != null)
            {
                if (s1a.Rows[counter].Cells["fdocno"].Value.ToString().Length != 0)
                {
                    fdocno.Text = s1a.Rows[counter].Cells["fdocno"].Value.ToString().Trim();
                }
            }
            if (s1a.Rows[counter].Cells["fothref"].Value != null)
            {
                if (s1a.Rows[counter].Cells["fothref"].Value.ToString().Length != 0)
                {
                    fothref.Text = s1a.Rows[counter].Cells["fothref"].Value.ToString().Trim();
                }
            }
            if (s1a.Rows[counter].Cells["fset_g"].Value != null)
            {
                if (s1a.Rows[counter].Cells["fset_g"].Value.ToString().Length != 0)
                {
                    fset_g.Text = Convert.ToDecimal(s1a.Rows[counter].Cells["fset_g"].Value.ToString().Trim()).ToString("n2");
                }
            }
            if (s1a.Rows[counter].Cells["fset_s"].Value != null)
            {
                if (s1a.Rows[counter].Cells["fset_s"].Value.ToString().Length != 0)
                {
                    fset_s.Text = Convert.ToDecimal(s1a.Rows[counter].Cells["fset_s"].Value.ToString().Trim()).ToString("n2");
                }
            }
            if (s1a.Rows[counter].Cells["finvset"].Value != null)
            {
                if (s1a.Rows[counter].Cells["finvset"].Value.ToString().Length != 0)
                {
                    finvset.Text = Convert.ToDecimal(s1a.Rows[counter].Cells["finvset"].Value.ToString().Trim()).ToString("n2");
                }
            }
            if (s1a.Rows[counter].Cells["fset_c"].Value != null)
            {
                if (s1a.Rows[counter].Cells["fset_c"].Value.ToString().Length != 0)
                {
                    fset_c.Text = Convert.ToDecimal(s1a.Rows[counter].Cells["fset_c"].Value.ToString().Trim()).ToString("n2");
                }
            }
            if (s1a.Rows[counter].Cells["fdocdate"].Value != null)
            {
                if (s1a.Rows[counter].Cells["fdocdate"].Value.ToString().Length != 0)
                {
                    try
                    {
                        DateTime b = Convert.ToDateTime(s1a.Rows[counter].Cells["fdocdate"].Value.ToString().Trim());
                        fdocdate.Text = b.ToString("yyyy.MM.dd");
                    }
                    catch { }
                }
            }
        }

        private void button6_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }



    }
}
