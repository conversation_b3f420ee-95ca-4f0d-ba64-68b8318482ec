using INS.Business.Report;
using INS.INSClass;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace INS.Forms
{
    public partial class tcpyappr : Form
    {
        public string fctlid_c = "", fctlid_cy = "", fclass = "", flag = "", fctlid_py = "", selectfctlid = "", delfinvno="";
        public int fpyseq=0;
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public string Dbo = InsEnvironment.DataBase.GetDbo();
        public Boolean addflag = false, updateflag = false, Skip_Validate =false;
        public DataTable dtcpydet = new DataTable();
        public string user = InsEnvironment.LoginUser.GetUserCode();

        public String finvnoValue
        {
            set { finvno.Text = value; }
        }
        public String fpyseqValue
        {
            set { label4.Text = value; }
        }
        public String docdateValue
        {
            set { finvdate.Text = value; }
        }
        public String payeeValue
        {
            set { fpayee_t.Text = value; }
        }
        public String fpaytypeValue
        {
            set { fpaytype.SelectedValue = value; }
        }
        public String fpaynatValue
        {
            set { fpaynat.SelectedValue = value; }
        }
        public String fnatdescValue
        {
            set { fnatdesc.Text = value; }
        }
        public String afamountValue
        {
            set { afamount.Text = value; }
        }
        BindData[] u_pytypearr = new BindData[]  {
                                 new BindData("0",""),
                                 new BindData("1","Payment"),
                                 new BindData("2","Refund"),
                                 new BindData("3","Contribution"),
                                 new BindData("4","Recovery")};
        BindData[] u_pynatarr = new BindData[]  {
                                 new BindData("0",""),
                                 new BindData("01","Claim Amount"),
                                 new BindData("02","Own Solicitor"),
                                 new BindData("07","Clmnt's Solicitor"),
                                 new BindData("03","Adjuster Fee"),
                                 new BindData("08","Healthcare Fee"),
                                 new BindData("04","Other")};
        BindData[] u_paycatarr1 = new BindData[]  {
                                 new BindData("0",""),
                                 new BindData("1","Claim Amt (EC)"),
                                 new BindData("2","Claim Amt (Com Law)"),
                                 new BindData("3","Clmnt Sol (EC)"),
                                 new BindData("4","Clmnt Sol (Com Law)")};
        string[,] u_paycatarr = {
                                    {"0",""},
                                    {"1","Claim Amt (EC)"},
                                    {"2","Claim Amt (Com Law)"},
                                    {"3","Clmnt Sol (EC)"},
                                    {"4","Clmnt Sol (Com Law)"}};

        private Ctrl.Claim.PAY pAY;
        private Ctrl.Claim.EciPAY eciPAY;
        public tcpyappr()
        {
            InitializeComponent();
            init();
        }

        public tcpyappr(Ctrl.Claim.PAY pAY)
        {
            // TODO: Complete member initialization
            this.pAY = pAY;
            InitializeComponent();
            init();
        }

        public tcpyappr(Ctrl.Claim.EciPAY eciPAY)
        {
            // TODO: Complete member initialization
            this.eciPAY = eciPAY;
            InitializeComponent();
            init();
        }

        public void setControlFocus(string ctrlName)
        {
            //Controls[ctrlName].Focus();
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件             //添加事件
            }
            foreach (Control control in panel21.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件             //添加事件
            }
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            TextBox focusTextBox = null;
            if (e.KeyCode == Keys.Enter)
            {                    //判断用户是否按下回车键
                if (sender is TextBox)
                {
                    focusTextBox = (TextBox)sender;
                    if (!focusTextBox.AcceptsReturn)
                        SendKeys.Send("{TAB}");
                }
                else SendKeys.Send("{TAB}");
            }
        }

        void init()
        {
            fpaytype.DataSource = u_pytypearr;
            fpaytype.ValueMember = "ID";
            fpaytype.DisplayMember = "Item";
            fpaynat.DataSource = u_pynatarr;
            fpaynat.ValueMember = "ID";
            fpaynat.DisplayMember = "Item";
            fpaycat.DataSource = u_paycatarr1;
            fpaycat.ValueMember = "ID";
            fpaycat.DisplayMember = "Item";
            
        }

        public void FillData(string order, string query)
        {
            init();
            string sql = "";
            if (fclass != "EEC") {
                sql = "select a.*, '' as fno,'' as fempyee from ocpyappr a where fctlid_c =(select fctlid from oclaim where fclmno ='" + fctlid_c + "')"; 
            }
            else
            {
                sql = "select a.*, b.fno,b.fempyee from ocpyappr a left join oceci b on a.fctlid_c = b.fctlid_1 where fctlid_c =(select fctlid from oclaim where fclmno ='" + fctlid_c + "')";
            }
            appr.DataSource = DBHelper.GetDataSet(sql);
            if (appr.RowCount > 0) { 
                appr.CurrentCell = appr.Rows[appr.RowCount - 1].Cells["frefno"];
                foreach (DataGridViewRow dr in appr.Rows)
                {
                    if (dr.Cells["fpaycat"].Value.ToString().Length == 1)
                    {
                        dr.Cells["fpaycat"].Value = u_paycatarr[Fct.snFat(dr.Cells["fpaycat"].Value), 1];
                    }
                }
            }
            else {
                string ocpydet = "select * from ocpydet where fctlid_c =(select fctlid from oclaim where fclmno ='" + fctlid_c + "') and fctlid_ap='" + fctlid_cy + "'";
                cpydet.DataSource = DBHelper.GetDataSet(ocpydet);
                foreach (DataGridViewColumn Column in cpydet.Columns)
                {
                    Column.Visible = false;
                }
                cpydet.Columns["flogseq"].Visible = true;
                cpydet.Columns["fdocdate"].Visible = true;
                cpydet.Columns["finvno"].Visible = true;
                cpydet.Columns["famount"].Visible = true;
                cpydet.Columns["flogseq"].HeaderText = "Seq#";
                cpydet.Columns["fdocdate"].HeaderText = "Doc Date";
                cpydet.Columns["finvno"].HeaderText = "Dr/Cr No.";
                cpydet.Columns["famount"].HeaderText = "Amount";
                cpydet.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.cpydet_CellFormatting);
        
            }
            foreach (DataGridViewColumn Column in appr.Columns)
            {
                Column.Visible = false;
            }
            appr.Columns["frefno"].Visible = true;
            appr.Columns["fdocdate"].Visible = true;
            appr.Columns["fpaycat"].Visible = true;
            appr.Columns["fpayamt"].Visible = true;
            appr.Columns["famount"].Visible = true;
            appr.Columns["fbalance"].Visible = true;
            appr.Columns["frefno"].HeaderText = "Approval No.";
            appr.Columns["fdocdate"].HeaderText = "Doc Date";
            appr.Columns["fpaycat"].HeaderText = "Category";
            appr.Columns["famount"].HeaderText = "Appr Amt";
            appr.Columns["famount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            appr.Columns["famount"].DefaultCellStyle.Format = "N4";
            appr.Columns["fpayamt"].HeaderText = "Pay Amt";
            appr.Columns["fpayamt"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            appr.Columns["fpayamt"].DefaultCellStyle.Format = "N4";
            appr.Columns["fbalance"].HeaderText = "Balance";
            appr.Columns["fbalance"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            appr.Columns["fbalance"].DefaultCellStyle.Format = "N4";

            back();
        }

        private void appr_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0; 
                if (appr.CurrentCell != null)
                {
                    row = appr.CurrentCell.RowIndex;
                }
                else { return; }
                if (appr.Rows[row].Cells["frefno"].Value != null)
                {
                    if (appr.Rows[row].Cells["frefno"].Value.ToString().Length != 0)
                    {
                        frefno.Text = appr.Rows[row].Cells["frefno"].Value.ToString().Trim();
                    }
                }
                if (appr.Rows[row].Cells["fremark"].Value != null)
                {
                    if (appr.Rows[row].Cells["fremark"].Value.ToString().Length != 0)
                    {
                        fremark.Text = appr.Rows[row].Cells["fremark"].Value.ToString().Trim();
                    }
                }
                foreach (DataGridViewRow dr in appr.Rows)
                {
                    if (dr.Cells["fpaycat"].Value.ToString().Length == 1)
                    {
                        dr.Cells["fpaycat"].Value = u_paycatarr[Fct.snFat(dr.Cells["fpaycat"].Value), 1];
                    }
                }
                if (appr.Rows[row].Cells["fpaycat"].Value != null)
                {
                    if (appr.Rows[row].Cells["fpaycat"].Value.ToString().Length != 0)
                    {
                        fpaycat.Text = appr.Rows[row].Cells["fpaycat"].Value.ToString().Trim();
                    }
                }
                if (appr.Rows[row].Cells["fno"].Value != null)
                {
                    if (appr.Rows[row].Cells["fno"].Value.ToString().Length != 0)
                    {
                        fno.Text = appr.Rows[row].Cells["fno"].Value.ToString().Trim();
                    }
                }
                if (appr.Rows[row].Cells["fempyee"].Value != null)
                {
                    if (appr.Rows[row].Cells["fempyee"].Value.ToString().Length != 0)
                    {
                        fempyee.Text = appr.Rows[row].Cells["fempyee"].Value.ToString().Trim();
                    }
                }
                if (appr.Rows[row].Cells["fdocdate"].Value != null)
                {
                    if (appr.Rows[row].Cells["fdocdate"].Value.ToString().Length != 0)
                    {
                        try
                        {
                            DateTime b = Convert.ToDateTime(appr.Rows[row].Cells["fdocdate"].Value.ToString().Trim());
                            fdocdate.Text = b.ToString("yyyy.MM.dd");
                        }
                        catch { }
                    }
                }
                if (appr.Rows[row].Cells["famount"].Value != null)
                {
                    famount.Text = appr.Rows[row].Cells["famount"].Value.ToString().Trim();
                }
                finpuser.Text = appr.Rows[row].Cells["finpuser"].Value.ToString();
                finpdate.Text = appr.Rows[row].Cells["finpdate"].Value.ToString();
                fupduser.Text = appr.Rows[row].Cells["fupduser"].Value.ToString();
                fupddate.Text = appr.Rows[row].Cells["fupddate"].Value.ToString();
                if (appr.Rows[row].Cells["fpayamt"].Value != null)
                {
                    fpayamt.Text = appr.Rows[row].Cells["fpayamt"].Value.ToString().Trim();
                }
                if (appr.Rows[row].Cells["fbalance"].Value != null)
                {
                    fbalance.Text = appr.Rows[row].Cells["fbalance"].Value.ToString().Trim();
                }
                if (appr.Rows[row].Cells["fctlid"].Value != null)
                {
                    fctlid_cy = appr.Rows[row].Cells["fctlid"].Value.ToString().Trim();
                }
                tpReload(fctlid_cy);
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        public void tpReload(string fctlid_cy)
        {
            string ocpydet = "select * from ocpydet where fctlid_c =(select fctlid from oclaim where fclmno ='" + fctlid_c + "') and fctlid_ap='" + fctlid_cy + "'";
            cpydet.DataSource = DBHelper.GetDataSet(ocpydet);
            foreach (DataGridViewColumn Column in cpydet.Columns)
            {
                Column.Visible = false;
            }
            cpydet.Columns["flogseq"].Visible = true;
            cpydet.Columns["fdocdate"].Visible = true;
            cpydet.Columns["finvno"].Visible = true;
            cpydet.Columns["famount"].Visible = true;
            cpydet.Columns["flogseq"].HeaderText = "Seq#";
            cpydet.Columns["fdocdate"].HeaderText = "Doc Date";
            cpydet.Columns["finvno"].HeaderText = "Dr/Cr No.";
            cpydet.Columns["famount"].HeaderText = "Amount";
            if (cpydet.RowCount > 0) { cpydet.CurrentCell = cpydet.Rows[0].Cells["flogseq"]; }
            else
            {
                foreach (Control ctrl in panel21.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                    }
                    if (ctrl is MaskedTextBox)
                    {
                        ((MaskedTextBox)ctrl).Text = "";
                    }
                }
                //foreach (Control ctrl in Clauses.Controls)
                //{
                //    if (ctrl is MaskedTextBox)
                //    {
                //        ((MaskedTextBox)ctrl).Text = "";
                //    }
                //    if (ctrl is TextBox)
                //    {
                //        ((TextBox)ctrl).Text = "";
                //    }
                //}
            }
            cpydet.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.cpydet_CellFormatting);
        }

        private void cpydet_CellFormatting(object sender, System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            cpydet.Columns["flogseq"].HeaderText = "Settle#";
            cpydet.Columns["flogseq"].DefaultCellStyle.Format = "N0";
            cpydet.Columns["flogseq"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            cpydet.Columns["fdocdate"].HeaderText = "Doc Date";
            cpydet.Columns["fdocdate"].DefaultCellStyle.Format = "dd'/'MM'/'yyyy";
            cpydet.Columns["finvno"].HeaderText = "Dr/Cr No.";
            cpydet.Columns["famount"].HeaderText = "Amount";
            cpydet.Columns["famount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
        }

        private void cpydet_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int cpydetrow = 0;
                if (cpydet.CurrentCell != null)
                {
                    cpydetrow = cpydet.CurrentCell.RowIndex;
                }
                else { return; }
                flogseq.Text = cpydet.Rows[cpydetrow].Cells["flogseq"].Value.ToString().Trim();
                finvno.Text = cpydet.Rows[cpydetrow].Cells["finvno"].Value.ToString().Trim();
                fnatdesc.Text = cpydet.Rows[cpydetrow].Cells["fnatdesc"].Value.ToString().Trim();
                afamount.Text = cpydet.Rows[cpydetrow].Cells["famount"].Value.ToString();
                fpayee_t.Text = cpydet.Rows[cpydetrow].Cells["fpayee"].Value.ToString().Trim();
                if (cpydet.Rows[cpydetrow].Cells["fdocdate"].Value != null)
                {
                    if (cpydet.Rows[cpydetrow].Cells["fdocdate"].Value.ToString().Length != 0)
                    {
                        try
                        {
                            DateTime b = Convert.ToDateTime(cpydet.Rows[cpydetrow].Cells["fdocdate"].Value.ToString().Trim());
                            finvdate.Text = b.ToString("yyyy.MM.dd");
                        }
                        catch { }
                    }
                }
                if (cpydet.Rows[cpydetrow].Cells["fpaytype"].Value != null)
                {
                    if (cpydet.Rows[cpydetrow].Cells["fpaytype"].Value.ToString().Length != 0)
                    {
                        fpaytype.SelectedValue = cpydet.Rows[cpydetrow].Cells["fpaytype"].Value.ToString().Trim();
                    }
                }
                if (cpydet.Rows[cpydetrow].Cells["fpaynat"].Value != null)
                {
                    if (cpydet.Rows[cpydetrow].Cells["fpaynat"].Value.ToString().Length != 0)
                    {
                        fpaynat.SelectedValue = cpydet.Rows[cpydetrow].Cells["fpaynat"].Value.ToString().Trim();
                    }
                }
            }
            catch { }
        }

        void add()
        {
            tabControl1.SelectedTab = Clauses;
            setControlFocus("frefno");
            addflag = true;
            add1.Visible = true;
            del1.Visible = true;
            foreach (Control ctrl in Clauses.Controls)
            {
                if (ctrl is TextBox)
                {
                    if (((TextBox)ctrl).TabStop == true) {
                        ((TextBox)ctrl).ReadOnly = false;
                        ((TextBox)ctrl).BackColor = System.Drawing.Color.White; 
                    }
                    ((TextBox)ctrl).Text = "";
                }
                if (ctrl is MaskedTextBox)
                {
                     ((MaskedTextBox)ctrl).ReadOnly = false;
                     ((MaskedTextBox)ctrl).BackColor = System.Drawing.Color.White;
                }
                fpaycat.Enabled = true;
                fpaycat.BackColor = System.Drawing.Color.White;
                //fempyee.Enabled = true;
                //fempyee.BackColor = System.Drawing.Color.White;
            }
            DataTable dt = cpydet.DataSource as DataTable;
            if (dt != null)
            {
                dt.Clear(); cpydet.DataSource = dt;
                foreach (Control ctrl in panel21.Controls)
                {
                    if (ctrl is MaskedTextBox)
                    {
                        ((MaskedTextBox)ctrl).Text = "";
                    }
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                    }
                }
                foreach (Control ctrl in panel2.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                    }
                }
                fdocdate.Text = DateTime.Now.ToString("yyyy.MM.dd HH:mm:ss");
                fempyee.SelectedValue = "1";
            }
            btncontrol();
        }

        void back() {
            add1.Visible = false;
            del1.Visible = false;
            addflag = false;
            updateflag = false;
            foreach (Control ctrl in panel1.Controls)
            {
                if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
            }
            foreach (Control ctrl in panel21.Controls)
            {
                if (ctrl is MaskedTextBox)
                {
                    ((MaskedTextBox)ctrl).ReadOnly = true;
                    ((MaskedTextBox)ctrl).BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
                }
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = true;
                    ((TextBox)ctrl).BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
                }
                if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
            }
            foreach (Control ctrl in Clauses.Controls)
            {
                if (ctrl is MaskedTextBox)
                {
                    ((MaskedTextBox)ctrl).ReadOnly = true;
                    ((MaskedTextBox)ctrl).BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
                }
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = true;
                    ((TextBox)ctrl).BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
                }
                if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
            }
            DataTable dt3 = appr.DataSource as DataTable;
            foreach (DataRow row in dt3.Rows)
            {
                int SelectedIndex = dt3.Rows.IndexOf(row);
                if (fctlid_cy == row["fctlid"].ToString().Trim())
                {
                    appr.CurrentCell = appr.Rows[SelectedIndex].Cells["frefno"];
                    break;
                }
            }
            btncontrol();
            
        }

        private void button1_Click(object sender, EventArgs e)
        {
            add();
        }

        private void button7_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            FillData("", "");
            back();
        }

        void update() {
            setControlFocus("frefno");
            updateflag = true;
            add1.Visible = true;
            del1.Visible = true;
            foreach (Control ctrl in Clauses.Controls)
            {
                if (ctrl is TextBox)
                {
                    if (((TextBox)ctrl).TabStop == true) {
                        ((TextBox)ctrl).ReadOnly = false;
                        ((TextBox)ctrl).BackColor = System.Drawing.Color.White; 
                    }
                }
                if (ctrl is MaskedTextBox)
                {
                     ((MaskedTextBox)ctrl).ReadOnly = false;
                     ((MaskedTextBox)ctrl).BackColor = System.Drawing.Color.White;
                }
                fpaycat.Enabled = true;
                fpaycat.BackColor = System.Drawing.Color.White;
                //fempyee.Enabled = true;
              //  fempyee.BackColor = System.Drawing.Color.White;
            }
            XSRHPAYEE.Enabled = true;
            flogseq.ReadOnly = false;
            flogseq.BackColor = System.Drawing.Color.White;
            fnatdesc.ReadOnly = false;
            fnatdesc.BackColor = System.Drawing.Color.White;
            afamount.ReadOnly = false;
            afamount.BackColor = System.Drawing.Color.White;
            btncontrol();
        }

        private void button3_Click(object sender, EventArgs e)
        {
            update();
        }

        void del() {
            ArrayList delarr = new ArrayList();
            string del3 = " UPDATE ocpay SET ocpay.fappramt = ocpay.fappramt - b.fappramt " +
             "FROM  ocpay INNER JOIN (select sum(famount) as fappramt,finvno from ocpydet    " +
             "where fctlid_ap ='" + fctlid_cy + "' group by finvno) as b ON ocpay.finvno = b.finvno  " +
             "WHERE fctlid_1 =(select fctlid from oclaim where fclmno =''" + fctlid_c + "'')";
            delarr.Add(del3);
            string del = "delete from ocpyappr where fctlid ='" + fctlid_cy + "'";
            delarr.Add(del);
            string del2 = "delete from ocpydet where fctlid_ap ='" + fctlid_cy + "'";
            delarr.Add(del2);


            Boolean isSuccuss = DBHelper.ExecuteSqlTransaction((string[])delarr.ToArray(typeof(string)));
            if (isSuccuss == true)
            {
                MessageBox.Show("Have Been Deleted", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("Haven't Been Deleted", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            FillData("", "");
            back();

            
        }

        private void button5_Click(object sender, EventArgs e)
        {
            del();
        }

        private void add1_Click(object sender, EventArgs e)
        {
            dtcpydet = cpydet.DataSource as DataTable;
            DataRow newCustomersRow = dtcpydet.NewRow();
            dtcpydet.Rows.InsertAt(newCustomersRow, dtcpydet.Rows.Count);
            cpydet.DataSource = dtcpydet;
            cpydet.CurrentCell = cpydet.Rows[dtcpydet.Rows.Count - 1].Cells["flogseq"];
            finvno.Text = "";
            fpayee_t.Text = "";
            finvdate.Text = DateTime.Now.ToString("yyyy.MM.dd HH:mm:ss");
            fpaytype.SelectedValue = "0";
            fpaynat.SelectedValue = "0";
            flogseq.Text = dtcpydet.Rows.Count.ToString();
            flogseq.BackColor = System.Drawing.Color.White;
            flogseq.ReadOnly = false;
            fnatdesc.Text = "";
            fnatdesc.ReadOnly = false;
            fnatdesc.BackColor = System.Drawing.Color.White;
            afamount.Text = "";
            afamount.ReadOnly = false;
            afamount.BackColor = System.Drawing.Color.White;
            XSRHPAYEE.Enabled = true;
        }

        private void del1_Click(object sender, EventArgs e)
        {
            if (cpydet.RowCount != 0)
            {
                try
                {
                    dtcpydet = cpydet.DataSource as DataTable;
                    delfinvno = cpydet["finvno", cpydet.CurrentCell.RowIndex].Value.ToString();
                    dtcpydet.Rows.RemoveAt(cpydet.CurrentCell.RowIndex);
                    cpydet.DataSource = dtcpydet;
                    cpydet.CurrentCell = cpydet.Rows[dtcpydet.Rows.Count - 1].Cells["flogseq"];
                }
                catch { }
            }
            if (cpydet.RowCount == 0)
            {
                foreach (Control ctrl in panel21.Controls)
                {
                    if (ctrl is MaskedTextBox)
                    {
                        ((MaskedTextBox)ctrl).Text = "";
                        ((MaskedTextBox)ctrl).ReadOnly = true;
                        ((MaskedTextBox)ctrl).BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
                    }
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
                    }
                    if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                    if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
                }
            }
        }

        private void flogseq_TextChanged(object sender, EventArgs e)
        {
            if ((addflag == true || updateflag == true) && cpydet.CurrentCell != null && !Skip_Validate)
            {
                cpydet["flogseq", cpydet.CurrentCell.RowIndex].Value = flogseq.Text.ToString();
            }
        }

        private void fnatdesc_Validated(object sender, EventArgs e)
        {
            if ((addflag == true || updateflag == true) && cpydet.CurrentCell != null && !Skip_Validate)
            {
                cpydet["fnatdesc", cpydet.CurrentCell.RowIndex].Value = fnatdesc.Text.ToString();
            }
        }

        private void afamount_Validated(object sender, EventArgs e)
        {
            if ((addflag == true || updateflag == true) && cpydet.CurrentCell != null && !Skip_Validate)
            {
                cpydet["famount", cpydet.CurrentCell.RowIndex].Value = Fct.sdFat(afamount.Text);
            }
            total();
        }

        private void famount_Validated(object sender, EventArgs e)
        {
            total();
        }

        void total() {
            Decimal afamounts = 0;
            DataTable dt = cpydet.DataSource as DataTable;
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                if (Fct.sdFat(dt.Rows[i]["famount"].ToString()) != 0)
                {
                    afamounts = afamounts + Convert.ToDecimal(dt.Rows[i]["famount"].ToString());
                }
            }
            fpayamt.Text = afamounts.ToString();
            fbalance.Text = (Fct.sdFat(famount.Text) - Fct.sdFat(fpayamt.Text)).ToString();
        }

        private void XSRHPAYEE_Click(object sender, EventArgs e)
        {
            string fctlids = "";
            foreach (DataGridViewRow dr in cpydet.Rows)
            {
                fctlids = fctlids + "','" + dr.Cells["finvno"].Value.ToString();
            }
            frmfinvno tempform = new frmfinvno(this);
            tempform.fclmno = fctlid_c;
            tempform.delfinvno = delfinvno;
            tempform.dtappr = cpydet.DataSource as DataTable;
            tempform.FillData("", "");
            tempform.ShowDialog();
        }

        private void finvno_TextChanged(object sender, EventArgs e)
        {
            if ((addflag == true || updateflag == true) && cpydet.CurrentCell != null && !Skip_Validate)
            {
                cpydet["finvno", cpydet.CurrentCell.RowIndex].Value = finvno.Text.ToString();
            }
        }

        private void finvdate_TextChanged(object sender, EventArgs e)
        {
            if ((addflag == true || updateflag == true) && cpydet.CurrentCell != null && !Skip_Validate)
            {
                cpydet["fdocdate", cpydet.CurrentCell.RowIndex].Value = finvdate.Text.ToString();
            }
        }

        private void fpayee_t_TextChanged(object sender, EventArgs e)
        {
            if ((addflag == true || updateflag == true) && cpydet.CurrentCell != null && !Skip_Validate)
            {
                cpydet["fpayee", cpydet.CurrentCell.RowIndex].Value = fpayee_t.Text.ToString();
            }
        }

        private void fpaytype_TextChanged(object sender, EventArgs e)
        {
            if ((addflag == true || updateflag == true) && cpydet.CurrentCell != null && !Skip_Validate)
            {
                cpydet["fpaytype", cpydet.CurrentCell.RowIndex].Value = fpaytype.SelectedValue;
            }
        }

        private void fpaynat_TextChanged(object sender, EventArgs e)
        {
            if ((addflag == true || updateflag == true) && cpydet.CurrentCell != null && !Skip_Validate)
            {
                cpydet["fpaynat", cpydet.CurrentCell.RowIndex].Value = fpaynat.SelectedValue;
            }
        }

        private void label4_TextChanged(object sender, EventArgs e)
        {
            if ((addflag == true || updateflag == true) && cpydet.CurrentCell != null && !Skip_Validate)
            {
                cpydet["fpyseq", cpydet.CurrentCell.RowIndex].Value = label4.Text;
            }
        }

        void save()
        {
            ArrayList addsql = new ArrayList();
            string sql1 = "select fctlid from oclaim where fclmno ='" + fctlid_c + "'";
            DataTable dtoclaim = DBHelper.GetDataSet(sql1);
            string fctlid_c1 = dtoclaim.Rows[0]["fctlid"].ToString();
            if (addflag == true)
            {
                DateTime time = DateTime.Now;
                
                string fctlid_cpydet = Fct.NewId("OCPYDET");
                string fctlid_ocpyappr = Fct.NewId("ocpyappr");

                string insertsql_op =   "INSERT INTO [dbo].[ocpyappr]([fctlid_c],[fctlid],[fdocdate],[frefno],[fclmno],[fno],[fpaycat],[fremark],[famount],[fpayamt], "+
                                        "[fbalance],[fstatus],[finpuser],[finpdate],[fupduser],[fupddate])  "+
                                        "VALUES('" + fctlid_c1 + "','" + fctlid_ocpyappr + "',convert(datetime,'" + fdocdate.Text.Trim() + "',102),'" + frefno.Text + "','" + fctlid_c + "', " +
                                        "'" + fno.Text + "','" + fpaycat.SelectedValue.ToString() + "','" + fremark.Text + "','" + Fct.sdFat(famount.Text) + "','" + Fct.sdFat(fpayamt.Text) + "', " +
                                        "'" + Fct.sdFat(fbalance.Text) + "','','" + user + "','" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','" + user + "', '" + time.ToString("yyyy-MM-dd HH:mm:ss") + "')";
                addsql.Add(insertsql_op);

                string sqlfctlid_op = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_ocpyappr) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='ocpyappr'";
                addsql.Add(sqlfctlid_op);
                DataTable dt = cpydet.DataSource as DataTable;
                if (dt != null && dt.Rows.Count > 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        if (i != 0) { fctlid_cpydet = (int.Parse(fctlid_cpydet) + 1).ToString().PadLeft(10, '0'); }
                        string insertsql = "INSERT INTO [dbo].[ocpydet]([fctlid_c],[fctlid_ap],[fctlid_py],[fpyseq],[fctlid],[flogseq],[frefno],[fdocdate], "+
                                            "[finvno],[fpayee],[fpaytype],[fpaynat],[fnatdesc],[famount])  "+
                                            "VALUES('" + fctlid_c1 + "','" + fctlid_ocpyappr + "','" + fctlid_py + "'," + Fct.snFat(dt.Rows[i]["fpyseq"]) + ",'" + fctlid_cpydet + "', " +
                                            "'" + dt.Rows[i]["flogseq"] + "','" + dt.Rows[i]["frefno"] + "',convert(datetime, '" + Fct.dateFat(dt.Rows[i]["fdocdate"]) + "',120),'" + dt.Rows[i]["finvno"] + "','" + dt.Rows[i]["fpayee"] + "', " +
                                            "'" + dt.Rows[i]["fpaytype"] + "','" + dt.Rows[i]["fpaynat"] + "','" + Fct.stFat(dt.Rows[i]["fnatdesc"]) + "','" + Fct.sdFat(dt.Rows[i]["famount"]) + "')";
                        addsql.Add(insertsql);
                    }
                    string sqlfctlid = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_cpydet) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='OCPYDET'";
                    addsql.Add(sqlfctlid);
                }
            }
            else if (updateflag == true)
            {
                string updatesql = "UPDATE [dbo].[ocpyappr] SET [fdocdate] = convert(datetime,'" + fdocdate.Text.Trim() + "',102),[frefno] = '" + frefno.Text + "',[fclmno] = '" + fctlid_c + "' " +
                                    ",[fno] = '" + fno.Text + "',[fpaycat] = '" + fpaycat.SelectedValue.ToString() + "',[fremark] = '" + fremark.Text + "',[famount] = '" + Fct.sdFat(famount.Text) + "' " +
                                    ",[fpayamt] = '" + Fct.sdFat(fpayamt.Text) + "',[fbalance] = '" + Fct.sdFat(fbalance.Text) + "' WHERE fctlid ='"+ fctlid_cy +"'";
                addsql.Add(updatesql);
                String Sql = "select * from ocpydet where fctlid_c =(select fctlid from oclaim where fclmno ='" + fctlid_c + "') and fctlid_ap='" + fctlid_cy + "'";
                DataTable dt0 = DBHelper.GetDataSet(Sql);
                DataTable dt = cpydet.DataSource as DataTable;
                if (dt != null)
                {
                    addsql.AddRange(CompareDt(dt0, dt, "fctlid"));
                }
            }

            
            Boolean isSuccuss = DBHelper.ExecuteSqlTransaction((string[])addsql.ToArray(typeof(string)));
            string str = "UPDATE ocpay SET ocpay.fappramt = b.fappramt FROM "+
                        "ocpay INNER JOIN (select sum(famount) as fappramt,finvno from ocpydet  "+
                        "where fctlid_c = (select fctlid from oclaim where fclmno ='" + fctlid_c + "') group by finvno) AS b " +
                        "ON ocpay.finvno = b.finvno WHERE fctlid_1 =(select fctlid from oclaim where fclmno ='" + fctlid_c + "')";
            isSuccuss = DBHelper.ExecuteCommand(str);
            if (isSuccuss == true)
            {
               // MessageBox.Show("Have Been Saved", "Warning",
                //    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("Haven't Been Saved", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        public string[] CompareDt(DataTable dt1, DataTable dt2, string keyField)
        {
            //为三个表拷贝表结构
            DataTable dtRetDel = dt1.Clone();
            DataTable dtRetAdd = dt1.Clone();
            DataTable dtRetDif = dt1.Clone();

            ArrayList updsql = new ArrayList();
            int colCount = dt1.Columns.Count;

            DataView dv1 = dt1.DefaultView;
            DataView dv2 = dt2.DefaultView;

            //先以第一个表为参照，看第二个表是修改了还是删除了
            foreach (DataRowView dr1 in dv1)
            {
                dv2.RowFilter = keyField + " = '" + dr1[keyField].ToString() + "'";
                if (dv2.Count > 0)
                {
                    if (!CompareUpdate(dr1, dv2[0]))//比较是否有不同的
                    {
                        dtRetDif.Rows.Add(dv2[0].Row.ItemArray);//修改后
                        continue;
                    }
                }
                else
                {
                    //已经被删除的
                    dtRetDel.Rows.Add(dr1.Row.ItemArray);
                }
            }

            //以第一个表为参照，看记录是否是新增的
            dv2.RowFilter = "";//清空条件
            foreach (DataRowView dr2 in dv2)
            {
                dv1.RowFilter = keyField + " = '" + dr2[keyField].ToString() + "'";
                if (dv1.Count == 0)
                {
                    //新增的
                    dtRetAdd.Rows.Add(dr2.Row.ItemArray);
                }
            }

            if (dtRetAdd != null && dtRetAdd.Rows.Count > 0)
            {
                string sql1 = "select fctlid from oclaim where fclmno ='" + fctlid_c + "'";
                DataTable dtoclaim = DBHelper.GetDataSet(sql1);
                string fctlid_c1 = dtoclaim.Rows[0]["fctlid"].ToString();
               string fctlid_cpydet = Fct.NewId("OCPYDET");
               for (int i = 0; i < dtRetAdd.Rows.Count; i++)
                    {
                        if (i != 0) { fctlid_cpydet = (int.Parse(fctlid_cpydet) + 1).ToString().PadLeft(10, '0'); }
                        string insertsql = "INSERT INTO [dbo].[ocpydet]([fctlid_c],[fctlid_ap],[fctlid_py],[fpyseq],[fctlid],[flogseq],[frefno],[fdocdate], "+
                                            "[finvno],[fpayee],[fpaytype],[fpaynat],[fnatdesc],[famount])  "+
                                            "VALUES('" + fctlid_c1 + "','" + fctlid_cy + "','" + fctlid_py + "'," + Fct.snFat(dtRetAdd.Rows[i]["fpyseq"]) + ",'" + fctlid_cpydet + "', " +
                                            "'" + dtRetAdd.Rows[i]["flogseq"] + "','" + dtRetAdd.Rows[i]["frefno"] + "', convert(datetime,'" +  Fct.dateFat(dtRetAdd.Rows[i]["fdocdate"]) + "',120),'" + dtRetAdd.Rows[i]["finvno"] + "','" + dtRetAdd.Rows[i]["fpayee"] + "', " +
                                            "'" + dtRetAdd.Rows[i]["fpaytype"] + "','" + dtRetAdd.Rows[i]["fpaynat"] + "','" + Fct.stFat(dtRetAdd.Rows[i]["fnatdesc"]) + "','" + Fct.sdFat(dtRetAdd.Rows[i]["famount"]) + "')";
                        updsql.Add(insertsql);
                    }
                    string sqlfctlid = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_cpydet) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='OCPYDET'";
                    updsql.Add(sqlfctlid);
            }
            if (dtRetDif != null && dtRetDif.Rows.Count > 0)
            {
                for (int i = 0; i < dtRetDif.Rows.Count; i++)
                {
                    string update = "UPDATE [dbo].[ocpydet] SET [flogseq] = '" + Fct.snFat(dtRetDif.Rows[i]["flogseq"]) + "',[fpyseq] = '" + Fct.snFat(dtRetDif.Rows[i]["fpyseq"]) + "',[frefno] = '" + Fct.stFat(dtRetDif.Rows[i]["frefno"]) + "',[fdocdate] = convert(datetime,'" + Fct.dateFat(dtRetDif.Rows[i]["fdocdate"]) + "',120) " +
                                    ",[finvno] = '" + Fct.stFat(dtRetDif.Rows[i]["finvno"]) + "',[fpayee] = '" + Fct.stFat(dtRetDif.Rows[i]["fpayee"]) + "',[fpaytype] = '" + Fct.stFat(dtRetDif.Rows[i]["fpaytype"]) + "',[fpaynat] = '" + Fct.stFat(dtRetDif.Rows[i]["fpaynat"]) + "' " +
                                    ",[fnatdesc] = '" + Fct.stFat(dtRetDif.Rows[i]["fnatdesc"]) + "',[famount] = '" + Fct.sdFat(dtRetDif.Rows[i]["famount"]) + "' WHERE fctlid='" + dtRetDif.Rows[i]["fctlid"].ToString().Trim() + "'";
                    updsql.Add(update);
                }
            }

            if (dtRetDel != null && dtRetDel.Rows.Count > 0)
            {
                for (int i = 0; i < dtRetDel.Rows.Count; i++)
                {
                    string del = "delete from ocpydet where fctlid='" + dtRetDel.Rows[i]["fctlid"].ToString().Trim() + "'";
                    updsql.Add(del);
                }
            }

            return (string[])updsql.ToArray(typeof(string));
        }

        //比较是否有不同的
        private static bool CompareUpdate(DataRowView dr1, DataRowView dr2)
        {
            //行里只要有一项不一样，整个行就不一样,无需比较其它
            object val1;
            object val2;
            for (int i = 0; i < dr1.Row.ItemArray.Length; i++)
            {
                val1 = dr1[i];
                val2 = dr2[i];
                if (!val1.Equals(val2))
                {
                    return false;
                }
            }
            return true;
        }

        private void button4_Click(object sender, EventArgs e)
        {
            string oldrow = frefno.Text;
            save();
            FillData("", "");
            DataTable dt3 = appr.DataSource as DataTable;
            foreach (DataRow row in dt3.Rows)
            {
                int SelectedIndex = dt3.Rows.IndexOf(row);
                if (oldrow == row["frefno"].ToString().Trim())
                {
                    appr.CurrentCell = appr.Rows[SelectedIndex].Cells["frefno"];
                    break;
                }
            }
            back();
        }

        void btncontrol() {
            if (addflag == true || updateflag == true)
            {
                addbtn.Visible = false;
                updbtn.Visible = false;
                delbtn.Visible = false;
                printbtn.Visible = false;
                savebtn.Visible = true;
                cancelbtn.Visible = true;
                exitbtn.Visible = false;
            }
            else {
                addbtn.Visible = true;
                if (appr.RowCount > 0)
                { updbtn.Visible = true; delbtn.Visible = true; ModifyPolicy.Visible = true; DelPolicy.Visible = true; }
                else { updbtn.Visible = false; delbtn.Visible = false; ModifyPolicy.Visible = false; DelPolicy.Visible = false; }
               
                
               // printbtn.Visible = true;
                savebtn.Visible = false;
                cancelbtn.Visible = false;
                exitbtn.Visible = true;
            }
        }

        private void AddPolicy_Click(object sender, EventArgs e)
        {
            add();
        }

        private void ModifyPolicy_Click(object sender, EventArgs e)
        {
            update();
        }

        private void DelPolicy_Click(object sender, EventArgs e)
        {
            del();
        }

        private void ExitPolicy_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        void print() {
            pclaim tempform = new pclaim(this);
            tempform.fclass = fclass;
            tempform.fctlid_1 = fctlid_cy;
            tempform.ln_flogseq = Fct.snFat(cpydet["flogseq", cpydet.CurrentCell.RowIndex].Value);
            tempform.ld_faccdate = Convert.ToDateTime(cpydet["fdocdate", cpydet.CurrentCell.RowIndex].Value);
            tempform.p_rpttype = "clmappr";
            tempform.fctlid = fctlid_py;
            tempform.conctrl();
            tempform.ShowDialog();
        }

        private void PrintPolicy_Click(object sender, EventArgs e)
        {
            print();
        }

        private void printbtn_Click(object sender, EventArgs e)
        {
            print();
        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            if (addflag == true)
            {
                if (e.TabPage == Classes)
                    e.Cancel = true;
            }
        }



    }
}
