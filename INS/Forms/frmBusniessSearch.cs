using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;

namespace INS
{
    public partial class frmBusniessSearch : Form
    {
        public frmBusniessSearch()
        {
            InitializeComponent();
            FillData("", "");
        }

        public frmBusniessSearch(Ctrl.DirectGen directGen)
        {
            // TODO: Complete member initialization
            this.directGen = directGen;
            InitializeComponent();
        }

        public frmBusniessSearch(Ctrl.RIGen rIGen)
        {
            // TODO: Complete member initialization
            this.rIGen = rIGen;
            InitializeComponent();
        }

        public frmBusniessSearch(Ctrl.EndorDirectGen endorDirectGen)
        {
            // TODO: Complete member initialization
            this.endorDirectGen = endorDirectGen;
            InitializeComponent();
        }

        public frmBusniessSearch(Ctrl.EndorRIGen endorRIGen)
        {
            // TODO: Complete member initialization
            this.endorRIGen = endorRIGen;
            InitializeComponent();
        }

        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "";
        private Ctrl.DirectGen directGen;
        private Ctrl.RIGen rIGen;
        private Ctrl.EndorDirectGen endorDirectGen;
        private Ctrl.EndorRIGen endorRIGen;

        public void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            if (order == "" && query == "")
            {
                a = new SqlDataAdapter("select fid as Code#, fdesc as Desc# from msparam where fctlid_1 = '0000000001' order by fid", c);
            }
            else if (order != "")
            {
                a = new SqlDataAdapter("select fid as Code#, fdesc as Desc# from msparam where fctlid_1 = '0000000001' ORDER BY '" + order + "'", c);
            }
            else if (query != "")
            {
                a = new SqlDataAdapter("select fid as Code#, fdesc as Desc# from msparam where fctlid_1 = '0000000001' and fdesc = '" + query + "'", c);
            }
            else
            {
                a = new SqlDataAdapter("select fid as Code#, fdesc as Desc# from msparam where fctlid_1 = '0000000001' and fdesc = '" + query + "' order by fid", c);
            }
            DataTable t = new DataTable();
            a.Fill(t);
            dataGridView1.DataSource = t;
            c.Close();
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }
                if (dataGridView1.Rows[row].Cells["Code#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Code#"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["Code#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["Desc#"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            if (flag == "Direct")
            {
                directGen.BusinessValue = Fct.stFat(textBox1.Text.ToString());
                directGen.BusinessDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "RI")
            {
                rIGen.BusinessValue = Fct.stFat(textBox1.Text.ToString());
                rIGen.BusinessDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "Endor")
            {
                endorDirectGen.BusinessValue = Fct.stFat(textBox1.Text.ToString());
                endorDirectGen.BusinessDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EndorRI")
            {
                endorRIGen.BusinessValue = Fct.stFat(textBox1.Text.ToString());
                endorRIGen.BusinessDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }

    }
}
