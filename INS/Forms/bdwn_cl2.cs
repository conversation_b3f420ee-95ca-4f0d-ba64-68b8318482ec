using INS.INSClass;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
//FZ2YHY48
namespace INS.Business.SearchForm
{
    public partial class bdwn_cl2 : Form
    {

        public string fctlid, page = "", flag="";
        public bool cEditMode = false;
        public DataTable ocabk_c = new DataTable();
        public string db = InsEnvironment.DataBase.GetDbm();
        public Boolean Auto = false;
        private Ctrl.Claim.EciADJ eciADJ;
        public bdwn_cl2()
        {
            InitializeComponent();
        }

        public bdwn_cl2(Ctrl.Claim.EciADJ eciADJ)
        {
            // TODO: Complete member initialization
            this.eciADJ = eciADJ;
            InitializeComponent();
            foreach (Control control in this.Controls)              //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件             //添加事件
            }
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            TextBox focusTextBox = null;

            if (e.KeyCode == Keys.Enter)
            {                    //判断用户是否按下回车键
                if (sender is TextBox)
                {
                    focusTextBox = (TextBox)sender;
                    if (!focusTextBox.AcceptsReturn)
                        SendKeys.Send("{TAB}");
                }
                else SendKeys.Send("{TAB}");
            }
        }

        void init()
        { XcmdConfirm.Visible = cEditMode; XAutoLbl.Visible = Auto; }

        public void tpReload(string fctlid_a)
        {
            init();
            XcmdConfirm.Visible = false;
            string sql = "select * from ocabk_c a left join (select fclmno, flogseq, faccdate,fctlid from ocadj) b on a.fctlid_a = b.fctlid where fctlid_a= '" + fctlid_a + "'";
            DataTable dt = DBHelper.GetDataSet(sql);
            if (dt.Rows.Count > 0)
            {
                fclmno.Text = dt.Rows[0]["fclmno"].ToString();
                flogseq.Text = dt.Rows[0]["flogseq"].ToString();
                DateTime b = Convert.ToDateTime(dt.Rows[0]["faccdate"].ToString().Trim());
                faccdate.Text = b.ToString("yyyy.MM.dd");
                fos08.Text = dt.Rows[0]["fos08"].ToString();
                frvos08.Text = dt.Rows[0]["frvos08"].ToString();
                fadj08.Text = dt.Rows[0]["fadj08"].ToString();
                fos09.Text = dt.Rows[0]["fos09"].ToString();
                frvos09.Text = dt.Rows[0]["frvos09"].ToString();
                fadj09.Text = dt.Rows[0]["fadj09"].ToString();
                fos11.Text = dt.Rows[0]["fos11"].ToString();
                frvos11.Text = dt.Rows[0]["frvos11"].ToString();
                fadj11.Text = dt.Rows[0]["fadj11"].ToString();
                fos12.Text = dt.Rows[0]["fos12"].ToString();
                frvos12.Text = dt.Rows[0]["frvos12"].ToString();
                fadj12.Text = dt.Rows[0]["fadj12"].ToString();
                fos13.Text = dt.Rows[0]["fos13"].ToString();
                frvos13.Text = dt.Rows[0]["frvos13"].ToString();
                fadj13.Text = dt.Rows[0]["fadj13"].ToString();
                fosA.Text = dt.Rows[0]["fosA"].ToString();
                frvosA.Text = dt.Rows[0]["frvosA"].ToString();
                fadjA.Text = dt.Rows[0]["fadjA"].ToString();
                fosC.Text = dt.Rows[0]["fosC"].ToString();
                frvosC.Text = dt.Rows[0]["frvosC"].ToString();
                fadjC.Text = dt.Rows[0]["fadjC"].ToString();
                fos.Text = dt.Rows[0]["fos"].ToString();
                frvos.Text = dt.Rows[0]["frvos"].ToString();
                fadj.Text = dt.Rows[0]["fadj"].ToString();
                fremark_t.Text = dt.Rows[0]["fremark_t1"].ToString();
            }
        }

        void ButtonAdd()
        {
            XcmdExit.Visible = false;
            frvos08.Enabled = cEditMode;
            frvosA.Enabled = cEditMode;
            frvos09.Enabled = cEditMode;
            frvos11.Enabled = cEditMode;
            frvos12.Enabled = cEditMode;
            frvos13.Enabled = cEditMode;
            fremark_t.Enabled = cEditMode;
            frvosA.BackColor = System.Drawing.Color.White;
            frvosA.ReadOnly = !cEditMode;
            frvos08.BackColor = System.Drawing.Color.White;
            frvos08.ReadOnly = !cEditMode;
            frvos09.BackColor = System.Drawing.Color.White;
            frvos09.ReadOnly = !cEditMode;
            frvos11.BackColor = System.Drawing.Color.White;
            frvos11.ReadOnly = !cEditMode;
            frvos12.BackColor = System.Drawing.Color.White;
            frvos12.ReadOnly = !cEditMode;
            frvos13.BackColor = System.Drawing.Color.White;
            frvos13.ReadOnly = !cEditMode;
            fremark_t.ReadOnly = !cEditMode;
        }

        public void tpAdd(string str, string flg, string date, string fctlid_a)
        {
            XcmdConfirm.Visible = true;
            if (ocabk_c == null)
            {
                if (fctlid_a.Length == 10)
                {
                    string sql1 = "select * from ocabk_c where fctlid_a ='" + fctlid_a + "'";
                    ocabk_c = DBHelper.GetDataSet(sql1);
                }
                else
                {
                    string sql = "select '' as flogseq,fos01, fos01 as frvos01, 0.0000 as fadj01, fos02,fos02 as frvos02,0.0000 as fadj02, fos03, fos03 as frvos03,0.0000 as fadj03, " +
                        "fos04, fos04 as frvos04,0.0000 as fadj04,fos05, fos05 as frvos05,0.0000 as fadj05,fos06,fos06 as frvos06,0.0000 as fadj06, " +
                        "fos07, fos07 as frvos07,0.0000 as fadj07,fos08, fos08 as frvos08,0.0000 as fadj08,fos09,fos09 as frvos09,0.0000 as fadj09, " +
                        "fos10, fos10 as frvos10,0.0000 as fadj10,fos11, fos11 as frvos11,0.0000 as fadj11,fos12, fos12 as frvos12,0.0000 as fadj12,fos13, fos13 as frvos13,0.0000 as fadj13,fosA, fosA as frvosA,0.0000 as fadjA,fosA2, fosA2 as frvosA2,0.0000 as fadjA2,fosB, fosB as frvosB,0.0000 as fadjB, " +
                        "fosC, fosC as frvosC,0.0000 as fadjC,fos, fos as frvos,0.0000 as fadj,'' as fremark_t1 from ocabk_oc a where fctlid_c = (select fctlid from oclaim where fclmno = '" + str + "')";
                    ocabk_c = DBHelper.GetDataSet(sql);
                }
            }
            fclmno.Text = str;
            flogseq.Text = flg;
            DataTable dt = ocabk_c;
            if (dt.Rows.Count > 0)
            {
                DateTime b = Convert.ToDateTime(date);
                faccdate.Text = b.ToString("yyyy.MM.dd");
                fos08.Text = dt.Rows[0]["fos08"].ToString();
                frvos08.Text = dt.Rows[0]["frvos08"].ToString();
                fadj08.Text = dt.Rows[0]["fadj08"].ToString();
                fos09.Text = dt.Rows[0]["fos09"].ToString();
                frvos09.Text = dt.Rows[0]["frvos09"].ToString();
                fadj09.Text = dt.Rows[0]["fadj09"].ToString();
                fos11.Text = dt.Rows[0]["fos11"].ToString();
                frvos11.Text = dt.Rows[0]["frvos11"].ToString();
                fadj11.Text = dt.Rows[0]["fadj11"].ToString();
                fos12.Text = dt.Rows[0]["fos12"].ToString();
                frvos12.Text = dt.Rows[0]["frvos12"].ToString();
                fadj12.Text = dt.Rows[0]["fadj12"].ToString();
                fos13.Text = dt.Rows[0]["fos13"].ToString();
                frvos13.Text = dt.Rows[0]["frvos13"].ToString();
                fadj13.Text = dt.Rows[0]["fadj13"].ToString();
                fosA.Text = dt.Rows[0]["fosA"].ToString();
                frvosA.Text = dt.Rows[0]["frvosA"].ToString();
                fadjA.Text = dt.Rows[0]["fadjA"].ToString();
                fosA2.Text = dt.Rows[0]["fosA2"].ToString();
                frvosA2.Text = dt.Rows[0]["frvosA2"].ToString();
                fadjA2.Text = dt.Rows[0]["fadjA2"].ToString();
                fosB.Text = dt.Rows[0]["fosB"].ToString();
                frvosB.Text = dt.Rows[0]["frvosB"].ToString();
                fadjB.Text = dt.Rows[0]["fadjB"].ToString();
                fosC.Text = dt.Rows[0]["fosC"].ToString();
                frvosC.Text = dt.Rows[0]["frvosC"].ToString();
                fadjC.Text = dt.Rows[0]["fadjC"].ToString();
                fos.Text = dt.Rows[0]["fos"].ToString();
                frvos.Text = dt.Rows[0]["frvos"].ToString();
                fadj.Text = dt.Rows[0]["fadj"].ToString();
                fremark_t.Text = dt.Rows[0]["fremark_t1"].ToString();
            }
            ButtonAdd();
        }

        void u_setpage(string p_name)
        {
            if (p_name == "THE REST")
            {
                frvosA.Enabled = cEditMode;
                frvos08.Enabled = cEditMode;
                frvos09.Enabled = cEditMode;
                frvos11.Enabled = cEditMode;
                frvos12.Enabled = cEditMode;
                frvos13.Enabled = cEditMode;
                fremark_t.Enabled = !cEditMode;
            }
        }

        void u_calvalue(string p_varname)
        {
            string lc_index = "";
            if (p_varname !="FRVOSA" && (p_varname.Substring(5, 2) == "08" || p_varname.Substring(5, 2) == "09" || p_varname.Substring(5, 2) == "10" || p_varname.Substring(5, 2) == "11" || p_varname.Substring(5, 2) == "12" || p_varname.Substring(5, 2) == "13"))
            {
                lc_index = p_varname.Substring(5, 2);
            }
            if (p_varname.Substring(0, 5) == "FRVOS" && p_varname.Length == 7 && lc_index != "")
            {
                TextBox tb = (TextBox)Controls.Find(string.Format("{0}", "fadj" + lc_index), true).FirstOrDefault();
                TextBox fr = (TextBox)Controls.Find(string.Format("{0}", "frvos" + lc_index), true).FirstOrDefault();
                TextBox fo = (TextBox)Controls.Find(string.Format("{0}", "fos" + lc_index), true).FirstOrDefault();
                tb.Text = Fct.stFat(Fct.sdFat(fr.Text) - Fct.sdFat(fo.Text));
            }
            if (p_varname.Substring(0, 6) == "FRVOSA")
            {
                lc_index = p_varname.Substring(5, 1);
                TextBox tb = (TextBox)Controls.Find(string.Format("{0}", "fadj" + lc_index), true).FirstOrDefault();
                TextBox fr = (TextBox)Controls.Find(string.Format("{0}", "frvos" + lc_index), true).FirstOrDefault();
                TextBox fo = (TextBox)Controls.Find(string.Format("{0}", "fos" + lc_index), true).FirstOrDefault();
                tb.Text = Fct.stFat(Fct.sdFat(fr.Text) - Fct.sdFat(fo.Text));
            }

            fadj.Text = Fct.stFat(Fct.sdFat(fadjA.Text) + Fct.sdFat(fadj08.Text) + Fct.sdFat(fadj09.Text) + Fct.sdFat(fadj11.Text) + Fct.sdFat(fadj12.Text) + Fct.sdFat(fadj13.Text));
            frvos.Text = Fct.stFat(Fct.sdFat(frvosA.Text) + Fct.sdFat(frvos08.Text) + Fct.sdFat(frvos09.Text) + Fct.sdFat(frvos11.Text) + Fct.sdFat(frvos12.Text) + Fct.sdFat(frvos13.Text));
            fadjA2.Text = Fct.stFat(Fct.sdFat(fadj08.Text));
            fadjB.Text = Fct.stFat(Fct.sdFat(fadj09.Text));
            fadjC.Text = Fct.stFat(Fct.sdFat(fadj11.Text) + Fct.sdFat(fadj12.Text) + Fct.sdFat(fadj13.Text));
            frvosA2.Text = Fct.stFat(Fct.sdFat(frvos08.Text));
            frvosB.Text = Fct.stFat(Fct.sdFat(frvos09.Text));
            frvosC.Text = Fct.stFat(Fct.sdFat(frvos11.Text) + Fct.sdFat(frvos12.Text) + Fct.sdFat(frvos13.Text));
        }

        private void frvosA_Validated(object sender, EventArgs e)
        {
            u_calvalue("FRVOSA"); 
        }

       
        private void frvos08_Validated(object sender, EventArgs e)
        {
            u_calvalue("FRVOS08");
        }

        private void frvos09_Validated(object sender, EventArgs e)
        {
            u_calvalue("FRVOS09"); 
        }

        private void frvos11_Validated(object sender, EventArgs e)
        {
            u_calvalue("FRVOS11");
        }

        private void frvos12_Validated(object sender, EventArgs e)
        {
            u_calvalue("FRVOS12");
        }

        private void frvos13_Validated(object sender, EventArgs e)
        {
            u_calvalue("FRVOS13");
        }

        private void XcmdConfirm_Click(object sender, EventArgs e)
        {
            if (ocabk_c.Rows.Count == 0)
            {
                DataRow newRow = ocabk_c.NewRow();
                ocabk_c.Rows.Add(newRow);
            }
            foreach (DataRow dr in ocabk_c.Rows)
            {
                dr["flogseq"] = Fct.sdFat(flogseq.Text);
                dr["fadj08"] = Fct.sdFat(fadj08.Text);
                dr["frvos08"] = Fct.sdFat(frvos08.Text);
                dr["fos08"] = Fct.sdFat(fos08.Text);
                dr["fadj09"] = Fct.sdFat(fadj09.Text);
                dr["frvos09"] = Fct.sdFat(frvos09.Text);
                dr["fos09"] = Fct.sdFat(fos09.Text);
                dr["fadj11"] = Fct.sdFat(fadj11.Text);
                dr["frvos11"] = Fct.sdFat(frvos11.Text);
                dr["fos11"] = Fct.sdFat(fos11.Text);
                dr["fadj12"] = Fct.sdFat(fadj12.Text);
                dr["frvos12"] = Fct.sdFat(frvos12.Text);
                dr["fos12"] = Fct.sdFat(fos12.Text);
                dr["fadj13"] = Fct.sdFat(fadj13.Text);
                dr["frvos13"] = Fct.sdFat(frvos13.Text);
                dr["fos13"] = Fct.sdFat(fos13.Text);
                dr["fadjA"] = Fct.sdFat(fadjA.Text);
                dr["frvosA"] = Fct.sdFat(frvosA.Text);
                dr["fosA"] = Fct.sdFat(fosA.Text);
                dr["fadjA2"] = Fct.sdFat(fadjA2.Text);
                dr["frvosA2"] = Fct.sdFat(frvosA2.Text);
                dr["fosA2"] = Fct.sdFat(fosA2.Text);
                dr["fadjB"] = Fct.sdFat(fadjB.Text);
                dr["frvosB"] = Fct.sdFat(frvosB.Text);
                dr["fosB"] = Fct.sdFat(fosB.Text);
                dr["fadjC"] = Fct.sdFat(fadjC.Text);
                dr["frvosC"] = Fct.sdFat(frvosC.Text);
                dr["fosC"] = Fct.sdFat(fosC.Text);
                dr["fadj"] = Fct.sdFat(fadj.Text);
                dr["frvos"] = Fct.sdFat(frvos.Text);
                dr["fos"] = Fct.sdFat(fos.Text);
            }
            if (flag == "EciADJ")
            {
                eciADJ.fclawamtValue = fadj.Text.ToString();
                eciADJ.DTocabk_c = ocabk_c;
                eciADJ.u_cadj();
            }
            this.Close();
        }

        private void XcmdExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

       
    }
}
