using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;

namespace INS
{
    public partial class frmClmdet : Form
    {
        public string ftype = "", flag = "", ftypebtn;
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public frmClmdet()
        {
            InitializeComponent();
            FillData("", "");
        }

        public frmClmdet(Ctrl.EecClaim.EciClmdet eciClmdet)
        {
            // TODO: Complete member initialization
            this.eciClmdet = eciClmdet;
            InitializeComponent();
            foreach (Control control in Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件             //添加事件
            }
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            TextBox focusTextBox = null;
            if (e.KeyCode == Keys.Enter)
            {                    //判断用户是否按下回车键
                if (sender is TextBox)
                {
                    focusTextBox = (TextBox)sender;

                    if (!focusTextBox.AcceptsReturn)
                    {
                        SendKeys.Send("{TAB}");
                    }
                    else
                    {
                        if (focusTextBox.SelectionStart == 0)
                        {
                            focusTextBox.SelectionStart = focusTextBox.TextLength;
                            SendKeys.Send("{TAB}");
                        }
                    }
                }
                else SendKeys.Send("{TAB}");
            }
        }
        DES des = new DES();
        DBConnect operate = new DBConnect();
        private Ctrl.EecClaim.EciClmdet eciClmdet;

        public void FillData(string order, string query)
        {
            string Sql = "";
            if (order == "") { comboBox1.SelectedIndex = 1; }
            if (order == "" && query == "")
            {
                Sql = "select fid as [I.D.#], fdesc as Desc#,fdesc_c  from " + Dbm + "[msite] where ftype ='" + ftype + "' ORDER BY FID";
            }
            else if (order != "" && query.Trim() == "")
            {
                if (order == "I.D.") { Sql = "select fid as [I.D.#], fdesc as Desc#,fdesc_c from " + Dbm + "[msite] where ftype ='" + ftype + "' order by fid"; }
                if (order == "Desc") { Sql = "select  fdesc as Desc#,fid as [I.D.#],fdesc_c from " + Dbm + "[msite] where ftype ='" + ftype + "' order by fdesc"; }
                if (order == "Chinese") { Sql = "select fdesc_c,fid as [I.D.#], fdesc as Desc# from " + Dbm + "[msite] where ftype ='" + ftype + "' order by fdesc_c"; }
            }
            else if (order != "" && query.Trim() != "")
            {
                if (order == "I.D.") { Sql = "select fid as [I.D.#], fdesc as Desc#,fdesc_c from " + Dbm + "[msite] where ftype ='" + ftype + "' and fid = '" + query.Trim() + "' order by fid"; }
                if (order == "Desc") { Sql = "select  fdesc as Desc#,fid as [I.D.#],fdesc_c from " + Dbm + "[msite] where ftype ='" + ftype + "' and fdesc like '%" + query.Trim() + "%' order by fdesc"; }
                if (order == "Chinese") { Sql = "select fdesc_c,fid as [I.D.#], fdesc as Desc# from " + Dbm + "[msite] where ftype ='" + ftype + "' and fdesc_c like '%" + query.Trim() + "%' order by fdesc_c"; }
            }
            else
            {
                Sql = "select fid as [I.D.#], fdesc as Desc#,fdesc_c from " + Dbm + "[msite] where ftype ='" + ftype + "' and fdesc = '" + query + "' order by fid";
            }
            string orgtext = Fct.stFat(label5.Text);
            dataGridView1.DataSource = DBHelper.GetDataSet(Sql);
            foreach (DataGridViewRow row in dataGridView1.Rows)
            {
                if (row.Cells["I.D.#"].Value.ToString().Trim() == orgtext)
                {
                    dataGridView1.CurrentCell = row.Cells["I.D.#"];
                }
            }
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }

                if (dataGridView1.Rows[row].Cells["I.D.#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["I.D.#"].Value.ToString().Length != 0)
                    {
                        label5.Text = dataGridView1.Rows[row].Cells["I.D.#"].Value.ToString();
                    }
                }
                if (comboBox1.SelectedItem.ToString().Trim() == "I.D.")
                {
                    if (dataGridView1.Rows[row].Cells["I.D.#"].Value != null)
                    {
                        if (dataGridView1.Rows[row].Cells["I.D.#"].Value.ToString().Length != 0)
                        {
                            textBox1.Text = dataGridView1.Rows[row].Cells["I.D.#"].Value.ToString();
                        }
                    }
                }
                if (comboBox1.SelectedItem.ToString().Trim() == "Desc")
                {
                    if (dataGridView1.Rows[row].Cells["Desc#"].Value != null)
                    {
                        if (dataGridView1.Rows[row].Cells["Desc#"].Value.ToString().Length != 0)
                        {
                            textBox1.Text = dataGridView1.Rows[row].Cells["Desc#"].Value.ToString();
                        }
                    }
                }
                if (comboBox1.SelectedItem.ToString().Trim() == "Chinese")
                {
                    if (dataGridView1.Rows[row].Cells["fdesc_c"].Value != null)
                    {
                        if (dataGridView1.Rows[row].Cells["fdesc_c"].Value.ToString().Length != 0)
                        {
                            textBox1.Text = dataGridView1.Rows[row].Cells["fdesc_c"].Value.ToString();
                        }
                    }
                }
                if (dataGridView1.Rows[row].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["Desc#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["fdesc_c"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["fdesc_c"].Value.ToString().Length != 0)
                    {
                        label4.Text = dataGridView1.Rows[row].Cells["fdesc_c"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            if (flag == "EciClmdet")
            {
                if (ftype == "A")
                {
                    eciClmdet.facctype_Value = Fct.stFat(label5.Text);
                    eciClmdet.facctype_tDesc = Fct.stFat(label3.Text);
                }
                if (ftype == "J")
                {
                    eciClmdet.fjobn_Value = Fct.stFat(label5.Text);
                    eciClmdet.fjobn_tValue = Fct.stFat(label3.Text);
                    eciClmdet.occup_cDesc = Fct.stFat(label4.Text);
                }
                if (ftypebtn == "empyr")
                {
                    eciClmdet.fimempyr_Value = Fct.stFat(label5.Text);
                    eciClmdet.fimempyr_tValue = Fct.stFat(label3.Text);
                    eciClmdet.imempyr_cDesc = Fct.stFat(label4.Text);
                }
                if (ftypebtn == "scontr")
                {
                    eciClmdet.fscontr_Value = Fct.stFat(label5.Text);
                    eciClmdet.fscontr_tValue = Fct.stFat(label3.Text);
                    eciClmdet.scontr_cDesc = Fct.stFat(label4.Text);
                }   
            }
            this.Close();
        }


        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void textBox1_TextChanged(object sender, EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), textBox1.Text);
        }



    }
}
