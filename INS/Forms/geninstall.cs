using INS.INSClass;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business.SearchForm
{
    public partial class geninstall : Form
    {
        private Ctrl.DirectInstall directInstall;

        public string flag = "", Class = "", InstallTotComm = "", InstallTotDisc = "", InstallGP = "", InstallNP = "", InstallEC1amt = "", InstallEC2amt = "", InstallEC3amt = "";

        private Ctrl.EndorInstall endorInstall;
        private Ctrl.RIInstall rIInstall;
        public string InstallEffrDate
        {
            get { return textBox15.Text; }
            set { textBox15.Text = value; }
        }
        public string InstallEftoDate
        {
            get { return textBox16.Text; }
            set { textBox16.Text = value; }
        }
        public geninstall()
        {
            InitializeComponent();
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
            {
                SendKeys.Send("{TAB}");
            }
        }

        public geninstall(Ctrl.DirectInstall directInstall)
        {
            // TODO: Complete member initialization
            this.directInstall = directInstall;
            InitializeComponent();
        }


        public geninstall(Ctrl.EndorInstall endorInstall)
        {
            // TODO: Complete member initialization
            this.endorInstall = endorInstall;
            InitializeComponent();
        }

        public geninstall(Ctrl.RIInstall rIInstall)
        {
            // TODO: Complete member initialization
            this.rIInstall = rIInstall;
            InitializeComponent();
        }

        private void textBox14_TextChanged(object sender, EventArgs e)
        {
            if (textBox14.Text != "")
            {
                if (int.Parse(textBox14.Text.ToString()) > 12)
                {
                    textBox14.Text = "";
                    foreach (Control ctrl in panel1.Controls)
                    {
                        if (ctrl is TextBox)
                        {
                            ((TextBox)ctrl).Text = "";
                        }
                    }
                }
                else
                {
                    foreach (Control ctrl in panel1.Controls)
                    {
                        if (ctrl is TextBox)
                        {
                            ((TextBox)ctrl).Text = "";
                        }
                    }
                    CheckChange();
                    for (int i = 1; i < 13; i++)
                    {
                        Decimal a = 100;
                        a = Decimal.Round(a / i, 4);
                        Decimal b = 0;
                        if (textBox14.Text == i.ToString())
                        {
                            for (int k = 1; k < i; k++)
                            { b = b + a; }
                            for (int j = 1; j < i + 1; j++)
                            {
                                foreach (Control ctrl in panel1.Controls)
                                {
                                    if (ctrl is TextBox)
                                    {
                                        if (((TextBox)ctrl).TabIndex == j)
                                        {
                                            if (((TextBox)ctrl).TabIndex == i)
                                            {
                                                ((TextBox)ctrl).Text = (100 - b).ToString();
                                                label17.Text = (100 - b).ToString("N4");
                                            }
                                            else
                                            {
                                                ((TextBox)ctrl).Text = a.ToString("N4");
                                            }
                                        }
                                    }
                                }
                            }

                        }
                    }
                }
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            if (textBox14.Text != "")
            {
                ArrayList result = new ArrayList();
                    foreach (Control ctrl in panel1.Controls)
                    {
                        if (ctrl is TextBox)
                        {
                            if (!string.IsNullOrEmpty(((TextBox)ctrl).Text))
                            {
                                result.Add(((TextBox)ctrl).Text);
                            }
                        }
                    }

                if (flag == "RIInstall")
                {
                    rIInstall.installTotal = textBox14.Text.ToString();
                    rIInstall.installstring = (string[])result.ToArray(typeof(string));
                    rIInstall.begindate = textBox15.Text.ToString();
                    rIInstall.enddate = textBox16.Text.ToString();
                    rIInstall.Boolinstall = checkBox1.Checked;
                    rIInstall.installmet();
                }
                else if (flag == "Endor")
                {
                    endorInstall.installTotal = textBox14.Text.ToString();
                    endorInstall.installstring = (string[])result.ToArray(typeof(string));
                    endorInstall.begindate = textBox15.Text.ToString();
                    endorInstall.enddate = textBox16.Text.ToString();
                    endorInstall.Boolinstall = checkBox1.Checked;
                    endorInstall.installmet();
                }
                else
                {
                    directInstall.installTotal = textBox14.Text.ToString();
                    directInstall.installstring = (string[])result.ToArray(typeof(string));
                    directInstall.begindate = textBox15.Text.ToString();
                    directInstall.enddate = textBox16.Text.ToString();
                    directInstall.Boolinstall = checkBox1.Checked;
                    directInstall.installmet();
                }
                this.Close();
            }
        }

        public void CheckChange()
        {
            if (checkBox1.Checked == true)
            {
                foreach (Control ctrl in panel1.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Enabled = false;
                        ((TextBox)ctrl).BackColor = System.Drawing.Color.Khaki;
                    }
                }
            }
            else
            {
                foreach (Control ctrl in panel1.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        for (int j = 1; j < int.Parse(textBox14.Text); j++)
                        {
                            if (((TextBox)ctrl).TabIndex == j)
                            {
                                ((TextBox)ctrl).Enabled = true;
                                ((TextBox)ctrl).BackColor = System.Drawing.Color.White;
                            }
                        }
                    }
                }
            }
        }

        private void checkBox1_CheckedChanged(object sender, EventArgs e)
        {
            CheckChange();
        }

        public void CheckData()
        {
            Decimal a = 0;
            foreach (Control ctrl in panel1.Controls)
            {
                if (ctrl is TextBox)
                {
                        if (((TextBox)ctrl).TabIndex < int.Parse(textBox14.Text))
                        {
                            a = a + Convert.ToDecimal(((TextBox)ctrl).Text);
                        }
                }
            }
            foreach (Control ctrl in panel1.Controls)
            {
                if (ctrl is TextBox)
                {
                    if (((TextBox)ctrl).TabIndex == int.Parse(textBox14.Text))
                    {
                        ((TextBox)ctrl).Text = (100 - a).ToString("N4");
                    }
                }
            }                        
        }

        private void textBox1_TextChanged(object sender, EventArgs e)
        {
            textBox1.Text = string.Format("{0:#,##0.0000}", double.Parse(textBox1.Text));
            CheckData();
        }

        private void textBox2_Validated(object sender, EventArgs e)
        {
            textBox2.Text = string.Format("{0:#,##0.0000}", double.Parse(textBox2.Text));
            CheckData();
        }

        private void textBox3_Validated(object sender, EventArgs e)
        {
            textBox3.Text = string.Format("{0:#,##0.0000}", double.Parse(textBox3.Text));
            CheckData();
        }

        private void textBox4_Validated(object sender, EventArgs e)
        {
            textBox4.Text = string.Format("{0:#,##0.0000}", double.Parse(textBox4.Text));
            CheckData();
        }

        private void textBox5_Validated(object sender, EventArgs e)
        {
            textBox5.Text = string.Format("{0:#,##0.0000}", double.Parse(textBox5.Text));
            CheckData();
        }

        private void textBox6_Validated(object sender, EventArgs e)
        {
            textBox6.Text = string.Format("{0:#,##0.0000}", double.Parse(textBox6.Text));
            CheckData();
        }

        private void textBox12_Validated(object sender, EventArgs e)
        {
            textBox12.Text = string.Format("{0:#,##0.0000}", double.Parse(textBox12.Text));
            CheckData();
        }

        private void textBox11_Validated(object sender, EventArgs e)
        {
            textBox11.Text = string.Format("{0:#,##0.0000}", double.Parse(textBox11.Text));
            CheckData();
        }

        private void textBox10_Validated(object sender, EventArgs e)
        {
            textBox10.Text = string.Format("{0:#,##0.0000}", double.Parse(textBox10.Text));
            CheckData();
        }

        private void textBox9_Validated(object sender, EventArgs e)
        {
            textBox9.Text = string.Format("{0:#,##0.0000}", double.Parse(textBox9.Text));
            CheckData();
        }

        private void textBox8_Validated(object sender, EventArgs e)
        {
            textBox8.Text = string.Format("{0:#,##0.0000}", double.Parse(textBox8.Text));
            CheckData();
        }

        private void textBox7_Validated(object sender, EventArgs e)
        {
            textBox7.Text = string.Format("{0:#,##0.0000}", double.Parse(textBox7.Text));
            CheckData();
        }

        private void InstallmentGenerate_Load(object sender, EventArgs e)
        {
            foreach (Control control in panel1.Controls)                //循环窗体的控件
            {
               control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
        }
    }
}
