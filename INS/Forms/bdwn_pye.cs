using INS.INSClass;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
//FZ2YHY48
namespace INS.Business.SearchForm
{
    public partial class bdwn_pye : Form
    {

        public string fctlid_p="", page = "", flag = "";
        public bool cEditMode = false;
        public DataTable ocpbk_e = new DataTable();
        public string db = InsEnvironment.DataBase.GetDbm();
        private Ctrl.Claim.EciPAY eciPAY;
        public bdwn_pye()
        {
            InitializeComponent();
        }

        public bdwn_pye(Ctrl.Claim.EciPAY eciPAY)
        {
            // TODO: Complete member initialization
            this.eciPAY = eciPAY;
            InitializeComponent();
            foreach (Control control in this.Controls)                  //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件             //添加事件
            }
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            TextBox focusTextBox = null;

            if (e.KeyCode == Keys.Enter)
            {                    //判断用户是否按下回车键
                if (sender is TextBox)
                {
                    focusTextBox = (TextBox)sender;
                    if (!focusTextBox.AcceptsReturn)
                        SendKeys.Send("{TAB}");
                }
                else SendKeys.Send("{TAB}");
            }
        }

        void init()
        {
            XcmdConfirm.Visible = cEditMode;
        }

        public void tpReload(string fctlid_a)
        {
            XcmdConfirm.Visible = false;
            string sql = "select * from ocpbk_e a left join (select fclmno, flogseq, finvno,fctlid from ocpay) b on a.fctlid_p = b.fctlid where fctlid_p = '" + fctlid_a + "'";
            DataTable dt = DBHelper.GetDataSet(sql);
            if (dt.Rows.Count > 0)
            {
                fclmno.Text = dt.Rows[0]["fclmno"].ToString();
                flogseq.Text = dt.Rows[0]["flogseq"].ToString();
                finvno.Text = dt.Rows[0]["finvno"].ToString();
                fos01.Text = dt.Rows[0]["fos01"].ToString();
                fpay01.Text = dt.Rows[0]["fpay01"].ToString();
                frvos01.Text = dt.Rows[0]["frvos01"].ToString();
                fos02.Text = dt.Rows[0]["fos02"].ToString();
                fpay02.Text = dt.Rows[0]["fpay02"].ToString();
                frvos02.Text = dt.Rows[0]["frvos02"].ToString();
                fos03.Text = dt.Rows[0]["fos03"].ToString();
                fpay03.Text = dt.Rows[0]["fpay03"].ToString();
                frvos03.Text = dt.Rows[0]["frvos03"].ToString();
                fos04.Text = dt.Rows[0]["fos04"].ToString();
                fpay04.Text = dt.Rows[0]["fpay04"].ToString();
                frvos04.Text = dt.Rows[0]["frvos04"].ToString();
                fos05.Text = dt.Rows[0]["fos05"].ToString();
                fpay05.Text = dt.Rows[0]["fpay05"].ToString();
                frvos05.Text = dt.Rows[0]["frvos05"].ToString();
                fos06.Text = dt.Rows[0]["fos06"].ToString();
                fpay06.Text = dt.Rows[0]["fpay06"].ToString();
                frvos06.Text = dt.Rows[0]["frvos06"].ToString();
                fos07.Text = dt.Rows[0]["fos07"].ToString();
                fpay07.Text = dt.Rows[0]["fpay07"].ToString();
                frvos07.Text = dt.Rows[0]["frvos07"].ToString();
                fos08.Text = dt.Rows[0]["fos08"].ToString();
                fpay08.Text = dt.Rows[0]["fpay08"].ToString();
                frvos08.Text = dt.Rows[0]["frvos08"].ToString();
                fos09.Text = dt.Rows[0]["fos09"].ToString();
                fpay09.Text = dt.Rows[0]["fpay09"].ToString();
                frvos09.Text = dt.Rows[0]["frvos09"].ToString();
                fos10.Text = dt.Rows[0]["fos10"].ToString();
                fpay10.Text = dt.Rows[0]["fpay10"].ToString();
                frvos10.Text = dt.Rows[0]["frvos10"].ToString();
                fosA.Text = dt.Rows[0]["fosA"].ToString();
                fpayA.Text = dt.Rows[0]["fpayA"].ToString();
                frvosA.Text = dt.Rows[0]["frvosA"].ToString();
                fosB.Text = dt.Rows[0]["fosB"].ToString();
                fpayB.Text = dt.Rows[0]["fpayB"].ToString();
                frvosB.Text = dt.Rows[0]["frvosB"].ToString();
                fosC.Text = dt.Rows[0]["fosC"].ToString();
                fpayC.Text = dt.Rows[0]["fpayC"].ToString();
                frvosC.Text = dt.Rows[0]["frvosC"].ToString();
                fos.Text = dt.Rows[0]["fos"].ToString();
                fpay.Text = dt.Rows[0]["fpay"].ToString();
                frvos.Text = dt.Rows[0]["frvos"].ToString();
            }
            XcmdExit.Visible = true;
            fpay01.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fpay01.ReadOnly = cEditMode;
            fpay02.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fpay02.ReadOnly = cEditMode;
            fpay03.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fpay03.ReadOnly = cEditMode;
            fpay04.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fpay04.ReadOnly = cEditMode;
            fpay05.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fpay05.ReadOnly = cEditMode;
            fpay06.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fpay06.ReadOnly = cEditMode;
            fpay07.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fpay07.ReadOnly = cEditMode;
            fpay08.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fpay08.ReadOnly = cEditMode;
            fpay09.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fpay09.ReadOnly = cEditMode;
            fpay10.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fpay10.ReadOnly = cEditMode;
        }

        void ButtonAdd()
        {
            XcmdExit.Visible = false;
            fpay01.BackColor = System.Drawing.Color.White;
            fpay01.ReadOnly = !cEditMode;
            fpay02.BackColor = System.Drawing.Color.White;
            fpay02.ReadOnly = !cEditMode;
            fpay03.BackColor = System.Drawing.Color.White;
            fpay03.ReadOnly = !cEditMode;
            fpay04.BackColor = System.Drawing.Color.White;
            fpay04.ReadOnly = !cEditMode;
            fpay05.BackColor = System.Drawing.Color.White;
            fpay05.ReadOnly = !cEditMode;
            fpay06.BackColor = System.Drawing.Color.White;
            fpay06.ReadOnly = !cEditMode;
            fpay07.BackColor = System.Drawing.Color.White;
            fpay07.ReadOnly = !cEditMode;
            fpay08.BackColor = System.Drawing.Color.White;
            fpay08.ReadOnly = !cEditMode;
            fpay09.BackColor = System.Drawing.Color.White;
            fpay09.ReadOnly = !cEditMode;
            fpay10.BackColor = System.Drawing.Color.White;
            fpay10.ReadOnly = !cEditMode;
        }

        public void tpAdd(string str, string flg)
        {
            XcmdConfirm.Visible = true;
            if (ocpbk_e.Rows.Count ==0)
            {
                if (fctlid_p.Length == 10)
                {
                    string sql1 = "select * from ocpbk_e where fctlid_p ='" + fctlid_p + "'";
                    ocpbk_e = DBHelper.GetDataSet(sql1);
                }
                else
                {
                    //string lfctlid_p = (int.Parse(fctlid_p) - 1).ToString().PadLeft(10, '0');
                    //string sql1 = "select * from ocpbk_e where fctlid_p ='" + lfctlid_p + "'";
                    //ocpbk_e = DBHelper.GetDataSet(sql1);
                    //if (ocpbk_e.Rows.Count == 0)
                    //{
                        string sql = "select '" + fctlid_p + "' as fctlid_p, '' as flogseq, fos01-ftpay01 as fos01, fos01-ftpay01 as frvos01,  0.0000  as fpay01, " +
                              "fos02-ftpay02 as fos02, fos02-ftpay02 as frvos02,  0.0000  as fpay02, " +
                              "fos03-ftpay03 as fos03, fos03-ftpay03 as frvos03,  0.0000  as fpay03, " +
                              "fos04-ftpay04 as fos04, fos04-ftpay04 as frvos04,  0.0000  as fpay04, " +
                              "fos05-ftpay05 as fos05, fos05-ftpay05 as frvos05,  0.0000  as fpay05, " +
                              "fos06-ftpay06 as fos06, fos06-ftpay06 as frvos06,  0.0000  as fpay06, " +
                              "fos07-ftpay07 as fos07, fos07-ftpay07 as frvos07,  0.0000  as fpay07, " +
                              "fos08-ftpay08 as fos08, fos08-ftpay08 as frvos08,  0.0000  as fpay08, " +
                              "fos09-ftpay09 as fos09, fos09-ftpay09 as frvos09,  0.0000  as fpay09, " +
                              "fos10-ftpay10 as fos10, fos10-ftpay10 as frvos10,  0.0000  as fpay10, " +
                              "fosA-ftpayA as fosA, fosA-ftpayA as frvosA,  0.0000  as fpayA, " +
                              "fosB-ftpayB as fosB, fosB-ftpayB as frvosB,  0.0000  as fpayB, " +
                              "fosC-ftpayC as fosC, fosC-ftpayC as frvosC,  0.0000  as fpayC, " +
                              "fos-ftpay as fos, fos-ftpay as frvos, 0.0000 as fpay from ocabk_oe " +
                              "where fctlid_c = (select fctlid from oclaim where fclmno = '" + str + "') ";
                        ocpbk_e = DBHelper.GetDataSet(sql);
                }
            }
            DataTable dt = new DataTable();
            DataTable dt1 = new DataTable();
            if (ocpbk_e.Select("fctlid_p ='" + fctlid_p + "'").Length > 0)
            {
                dt = ocpbk_e.Select("fctlid_p ='" + fctlid_p + "'").CopyToDataTable();
            }
            else
            {
                string lfctlid_p = (int.Parse(fctlid_p) - 1).ToString().PadLeft(10, '0');
                if (ocpbk_e.Select("fctlid_p ='" + lfctlid_p + "'").Length > 0)
                {
                    dt1 = ocpbk_e.Select("fctlid_p ='" + lfctlid_p + "'").CopyToDataTable();
                }
                else {
                    lfctlid_p = (int.Parse(fctlid_p) - 1).ToString();
                    if (ocpbk_e.Select("fctlid_p ='" + lfctlid_p + "'").Length > 0) {
                        dt1 = ocpbk_e.Select("fctlid_p ='" + lfctlid_p + "'").CopyToDataTable();
                    }
                }
            }
            fclmno.Text = str;
            flogseq.Text = flg;
            if (dt1.Rows.Count > 0)
            {
                fos01.Text = dt1.Rows[0]["frvos01"].ToString();
                frvos01.Text = dt1.Rows[0]["frvos01"].ToString();
                fpay01.Text = "";
                fos02.Text = dt1.Rows[0]["frvos02"].ToString();
                frvos02.Text = dt1.Rows[0]["frvos02"].ToString();
                fpay02.Text = "";
                fos03.Text = dt1.Rows[0]["frvos03"].ToString();
                frvos03.Text = dt1.Rows[0]["frvos03"].ToString();
                fpay03.Text = "";
                fos04.Text = dt1.Rows[0]["frvos04"].ToString();
                frvos04.Text = dt1.Rows[0]["frvos04"].ToString();
                fpay04.Text = "";
                fos05.Text = dt1.Rows[0]["frvos05"].ToString();
                frvos05.Text = dt1.Rows[0]["frvos05"].ToString();
                fpay05.Text = "";
                fos06.Text = dt1.Rows[0]["frvos06"].ToString();
                frvos06.Text = dt1.Rows[0]["frvos06"].ToString();
                fpay06.Text = "";
                fos07.Text = dt1.Rows[0]["frvos07"].ToString();
                frvos07.Text = dt1.Rows[0]["frvos07"].ToString();
                fpay07.Text = "";
                fos08.Text = dt1.Rows[0]["frvos08"].ToString();
                frvos08.Text = dt1.Rows[0]["frvos08"].ToString();
                fpay08.Text = "";
                fos09.Text = dt1.Rows[0]["frvos09"].ToString();
                frvos09.Text = dt1.Rows[0]["frvos09"].ToString();
                fpay09.Text = "";
                fos10.Text = dt1.Rows[0]["frvos10"].ToString();
                frvos10.Text = dt1.Rows[0]["frvos10"].ToString();
                fpay10.Text = "";
                fosA.Text = dt1.Rows[0]["frvosA"].ToString();
                frvosA.Text = dt1.Rows[0]["frvosA"].ToString();
                fpayA.Text = "";
                fosB.Text = dt1.Rows[0]["frvosB"].ToString();
                frvosB.Text = dt1.Rows[0]["frvosB"].ToString();
                fpayB.Text = "";
                fosC.Text = dt1.Rows[0]["frvosC"].ToString();
                frvosC.Text = dt1.Rows[0]["frvosC"].ToString();
                fpayC.Text = "";
                fos.Text = dt1.Rows[0]["frvos"].ToString();
                frvos.Text = dt1.Rows[0]["frvos"].ToString();
                fpay.Text = "";
            }
            if (dt.Rows.Count > 0)
            {
                fos01.Text = dt.Rows[0]["fos01"].ToString();
                frvos01.Text = dt.Rows[0]["frvos01"].ToString();
                fpay01.Text = dt.Rows[0]["fpay01"].ToString();
                fos02.Text = dt.Rows[0]["fos02"].ToString();
                frvos02.Text = dt.Rows[0]["frvos02"].ToString();
                fpay02.Text = dt.Rows[0]["fpay02"].ToString();
                fos03.Text = dt.Rows[0]["fos03"].ToString();
                frvos03.Text = dt.Rows[0]["frvos03"].ToString();
                fpay03.Text = dt.Rows[0]["fpay03"].ToString();
                fos04.Text = dt.Rows[0]["fos04"].ToString();
                frvos04.Text = dt.Rows[0]["frvos04"].ToString();
                fpay04.Text = dt.Rows[0]["fpay04"].ToString();
                fos05.Text = dt.Rows[0]["fos05"].ToString();
                frvos05.Text = dt.Rows[0]["frvos05"].ToString();
                fpay05.Text = dt.Rows[0]["fpay05"].ToString();
                fos06.Text = dt.Rows[0]["fos06"].ToString();
                frvos06.Text = dt.Rows[0]["frvos06"].ToString();
                fpay06.Text = dt.Rows[0]["fpay06"].ToString();
                fos07.Text = dt.Rows[0]["fos07"].ToString();
                frvos07.Text = dt.Rows[0]["frvos07"].ToString();
                fpay07.Text = dt.Rows[0]["fpay07"].ToString();
                fos08.Text = dt.Rows[0]["fos08"].ToString();
                frvos08.Text = dt.Rows[0]["frvos08"].ToString();
                fpay08.Text = dt.Rows[0]["fpay08"].ToString();
                fos09.Text = dt.Rows[0]["fos09"].ToString();
                frvos09.Text = dt.Rows[0]["frvos09"].ToString();
                fpay09.Text = dt.Rows[0]["fpay09"].ToString();
                fos10.Text = dt.Rows[0]["fos10"].ToString();
                frvos10.Text = dt.Rows[0]["frvos10"].ToString();
                fpay10.Text = dt.Rows[0]["fpay10"].ToString();
                fosA.Text = dt.Rows[0]["fosA"].ToString();
                frvosA.Text = dt.Rows[0]["frvosA"].ToString();
                fpayA.Text = dt.Rows[0]["fpayA"].ToString();
                fosB.Text = dt.Rows[0]["fosB"].ToString();
                frvosB.Text = dt.Rows[0]["frvosB"].ToString();
                fpayB.Text = dt.Rows[0]["fpayB"].ToString();
                fosC.Text = dt.Rows[0]["fosC"].ToString();
                frvosC.Text = dt.Rows[0]["frvosC"].ToString();
                fpayC.Text = dt.Rows[0]["fpayC"].ToString();
                fos.Text = dt.Rows[0]["fos"].ToString();
                frvos.Text = dt.Rows[0]["frvos"].ToString();
                fpay.Text = dt.Rows[0]["fpay"].ToString();
            }
            ButtonAdd();
        }

        void u_setpage(string p_name)
        {
            if (p_name == "THE REST")
            {
                fpay01.Enabled = cEditMode;
                fpay02.Enabled = cEditMode;
                fpay03.Enabled = cEditMode;
                fpay04.Enabled = cEditMode;
                fpay05.Enabled = cEditMode;
                fpay06.Enabled = cEditMode;
                fpay07.Enabled = cEditMode;
                fpay08.Enabled = cEditMode;
                fpay09.Enabled = cEditMode;
                fpay10.Enabled = cEditMode;
            }
        }

        void u_calvalue(string p_varname)
        {
            string lc_index = ""; decimal ln_opay = 0;
            if (p_varname.Substring(4, 2) == "01" || p_varname.Substring(4, 2) == "02" || p_varname.Substring(4, 2) == "03" || p_varname.Substring(4, 2) == "04" || p_varname.Substring(4, 2) == "05" || p_varname.Substring(4, 2) == "06" || p_varname.Substring(4, 2) == "07" || p_varname.Substring(4, 2) == "08" || p_varname.Substring(4, 2) == "09" || p_varname.Substring(4, 2) == "10")
            {
                lc_index = p_varname.Substring(4, 2);
            }
            if (p_varname.Substring(0, 4) == "FPAY" && p_varname.Length == 6 && lc_index != "")
            {
                TextBox fp = (TextBox)Controls.Find(string.Format("{0}", "fpay" + lc_index), true).FirstOrDefault();
                TextBox fr = (TextBox)Controls.Find(string.Format("{0}", "frvos" + lc_index), true).FirstOrDefault();
                TextBox fo = (TextBox)Controls.Find(string.Format("{0}", "fos" + lc_index), true).FirstOrDefault();
                ln_opay = Fct.sdFat(Fct.sdFat(fo.Text) - Fct.sdFat(fr.Text));
                fr.Text = Fct.stFat(Fct.sdFat(fo.Text) - Fct.sdFat(fp.Text));

                fpayA.Text = Fct.stFat(Fct.sdFat(fpay01.Text) + Fct.sdFat(fpay02.Text) + Fct.sdFat(fpay03.Text) + Fct.sdFat(fpay04.Text) + Fct.sdFat(fpay05.Text) + Fct.sdFat(fpay06.Text));
                fpayB.Text = Fct.stFat(Fct.sdFat(fpay07.Text));
                fpayC.Text = Fct.stFat(Fct.sdFat(fpay08.Text) + Fct.sdFat(fpay09.Text) + Fct.sdFat(fpay10.Text));
                frvosA.Text = Fct.stFat(Fct.sdFat(fosA.Text) - Fct.sdFat(fpayA.Text));
                frvosB.Text = Fct.stFat(Fct.sdFat(fosB.Text) - Fct.sdFat(fpayB.Text));
                frvosC.Text = Fct.stFat(Fct.sdFat(fosC.Text) - Fct.sdFat(fpayC.Text));
                fpay.Text = Fct.stFat(Fct.sdFat(fpayA.Text) + Fct.sdFat(fpayB.Text) + Fct.sdFat(fpayC.Text));
                frvos.Text = Fct.stFat(Fct.sdFat(frvosA.Text) + Fct.sdFat(frvosB.Text) + Fct.sdFat(frvosC.Text));
            }
        }

        private void XcmdConfirm_Click(object sender, EventArgs e)
        {
            if (ocpbk_e.Rows.Count == 0)
            {
                DataRow newRow = ocpbk_e.NewRow();
                newRow["fctlid_p"] = fctlid_p;
                ocpbk_e.Rows.Add(newRow);
            }
            else
            {
                DataRow[] Rows = ocpbk_e.Select("fctlid_p ='" + fctlid_p + "'");
                if (Rows == null || Rows.Length == 0)
                {
                    DataRow newRow = ocpbk_e.NewRow();
                    newRow["fctlid_p"] = fctlid_p;
                    ocpbk_e.Rows.Add(newRow);
                }
            }
            DataRow[] layrRow;
            layrRow = ocpbk_e.Select("fctlid_p ='" + fctlid_p + "'");
            foreach (DataRow dr in layrRow)
            {
                dr["flogseq"] = Fct.sdFat(flogseq.Text);
                dr["fpay01"] = Fct.sdFat(fpay01.Text);
                dr["frvos01"] = Fct.sdFat(frvos01.Text);
                dr["fos01"] = Fct.sdFat(fos01.Text);
                dr["fpay02"] = Fct.sdFat(fpay02.Text);
                dr["frvos02"] = Fct.sdFat(frvos02.Text);
                dr["fos02"] = Fct.sdFat(fos02.Text);
                dr["fpay03"] = Fct.sdFat(fpay03.Text);
                dr["frvos03"] = Fct.sdFat(frvos03.Text);
                dr["fos03"] = Fct.sdFat(fos03.Text);
                dr["fpay04"] = Fct.sdFat(fpay04.Text);
                dr["frvos04"] = Fct.sdFat(frvos04.Text);
                dr["fos04"] = Fct.sdFat(fos04.Text);
                dr["fpay05"] = Fct.sdFat(fpay05.Text);
                dr["frvos05"] = Fct.sdFat(frvos05.Text);
                dr["fos05"] = Fct.sdFat(fos05.Text);
                dr["fpay06"] = Fct.sdFat(fpay06.Text);
                dr["frvos06"] = Fct.sdFat(frvos06.Text);
                dr["fos06"] = Fct.sdFat(fos06.Text);
                dr["fpay07"] = Fct.sdFat(fpay07.Text);
                dr["frvos07"] = Fct.sdFat(frvos07.Text);
                dr["fos07"] = Fct.sdFat(fos07.Text);
                dr["fpay08"] = Fct.sdFat(fpay08.Text);
                dr["frvos08"] = Fct.sdFat(frvos08.Text);
                dr["fos08"] = Fct.sdFat(fos08.Text);
                dr["fpay09"] = Fct.sdFat(fpay09.Text);
                dr["frvos09"] = Fct.sdFat(frvos09.Text);
                dr["fos09"] = Fct.sdFat(fos09.Text);
                dr["fpay10"] = Fct.sdFat(fpay10.Text);
                dr["frvos10"] = Fct.sdFat(frvos10.Text);
                dr["fos10"] = Fct.sdFat(fos10.Text);
                dr["fpayA"] = Fct.sdFat(fpayA.Text);
                dr["frvosA"] = Fct.sdFat(frvosA.Text);
                dr["fosA"] = Fct.sdFat(fosA.Text);
                dr["fpayB"] = Fct.sdFat(fpayB.Text);
                dr["frvosB"] = Fct.sdFat(frvosB.Text);
                dr["fosB"] = Fct.sdFat(fosB.Text);
                dr["fpayC"] = Fct.sdFat(fpayC.Text);
                dr["frvosC"] = Fct.sdFat(frvosC.Text);
                dr["fosC"] = Fct.sdFat(fosC.Text);
                dr["fpay"] = Fct.sdFat(fpay.Text);
                dr["frvos"] = Fct.sdFat(frvos.Text);
                dr["fos"] = Fct.sdFat(fos.Text);
            }
            if (flag == "EciPAY")
            {
                eciPAY.fecamtValue = Fct.sdFat(fpay.Text.ToString()).ToString("n2");
                eciPAY.DTocpbk_e = ocpbk_e;
                eciPAY.u_cpay();

                this.Close();
            }
        }

        private void XcmdExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void fpay01_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(fpay01.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //fpay01.Focus();
            }
            else {  }
            u_calvalue("FPAY01");
        }

        private void fpay02_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(fpay02.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //fpay02.Focus();
            }
            else { }
            u_calvalue("FPAY02");
        }

        private void fpay03_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(fpay03.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //fpay03.Focus();
            }
            else {  }
            u_calvalue("FPAY03");
        }

        private void fpay04_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(fpay04.Text) < 0)
            {
               // MessageBox.Show("Invalid Value");
               //// fpay04.Focus();
            }
            else { }
            u_calvalue("FPAY04"); 
        }

        private void fpay05_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(fpay05.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //fpay05.Focus();
            }
            else {  }
            u_calvalue("FPAY05");
        }

        private void fpay06_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(fpay06.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //fpay06.Focus();
            }
            else { }
            u_calvalue("FPAY06"); 
        }

        private void fpay07_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(fpay07.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //fpay07.Focus();
            }
            else {  }
            u_calvalue("FPAY07");
        }

        private void fpay08_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(fpay08.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //fpay08.Focus();
            }
            else { }
            u_calvalue("FPAY08"); 
        }

        private void fpay09_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(fpay09.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //fpay09.Focus();
            }
            else { }
            u_calvalue("FPAY09"); 
        }


        private void fpay10_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(fpay10.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //fpay10.Focus();
            }
            else { }
            u_calvalue("FPAY10"); 
        }



    }
}
