using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;
using INS.Ctrl;

namespace INS
{
    public partial class EndtDirectInsured : Form
    {
        public EndtDirectInsured()
        {
            InitializeComponent();
            FillData("", "");
        }

        public EndtDirectInsured(EndorDirectInsured_CPM endorDirectInsured_CPM)
        {
            // TODO: Complete member initialization
            this.endorDirectInsured_CPM = endorDirectInsured_CPM;
            InitializeComponent();
        }

        public EndtDirectInsured(EndorDirectInsured_EEC endorDirectInsured_EEC)
        {
            // TODO: Complete member initialization
            this.endorDirectInsured_EEC = endorDirectInsured_EEC;
            InitializeComponent();
        }

        public EndtDirectInsured(EndorDirectInsured_PAR endorDirectInsured_PAR)
        {
            // TODO: Complete member initialization
            this.endorDirectInsured_PAR = endorDirectInsured_PAR;
            InitializeComponent();
        }

        public EndtDirectInsured(EndorDirectInsured_CGL endorDirectInsured_CGL)
        {
            // TODO: Complete member initialization
            this.endorDirectInsured_CGL = endorDirectInsured_CGL;
            InitializeComponent();
        }

        public EndtDirectInsured(EndorRIInsured_EEC endorRIInsured_EEC)
        {
            // TODO: Complete member initialization
            this.endorRIInsured_EEC = endorRIInsured_EEC;
            InitializeComponent();
        }

        public EndtDirectInsured(EndorRIInsured_CGL endorRIInsured_CGL)
        {
            // TODO: Complete member initialization
            this.endorRIInsured_CGL = endorRIInsured_CGL;
            InitializeComponent();
        }

        public EndtDirectInsured(EndorRIInsured_PAR endorRIInsured_PAR)
        {
            // TODO: Complete member initialization
            this.endorRIInsured_PAR = endorRIInsured_PAR;
            InitializeComponent();
        }

        public EndtDirectInsured(EndorDirectInsured_CAR endorDirectInsured_CAR)
        {
            // TODO: Complete member initialization
            this.endorDirectInsured_CAR = endorDirectInsured_CAR;
            InitializeComponent();
        }

        public EndtDirectInsured(EndorRIInsured_CAR endorRIInsured_CAR)
        {
            // TODO: Complete member initialization
            this.endorRIInsured_CAR = endorRIInsured_CAR;
            InitializeComponent();
        }


        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "", sqladd="";
        public string section = "";
        public string fctlid_p = "", fitem_1 = "";
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        private String _fclass;
        private EndorDirectInsured_CPM endorDirectInsured_CPM;
        private EndorDirectInsured_EEC endorDirectInsured_EEC;
        private EndorDirectInsured_PAR endorDirectInsured_PAR;
        private EndorDirectInsured_CGL endorDirectInsured_CGL;
        private EndorRIInsured_EEC endorRIInsured_EEC;
        private EndorRIInsured_CGL endorRIInsured_CGL;
        private EndorRIInsured_PAR endorRIInsured_PAR;
        private EndorDirectInsured_CAR endorDirectInsured_CAR;
        private EndorRIInsured_CAR endorRIInsured_CAR;

        public String Fclass
        {
            set
            {
                _fclass = value;
            }
        }

        public void FillData(string order, string query)
        {
            string sql = "";
            if (flag == "EndorCPM")
            {
                sql = "select fdesc1, a.fendtno, a.fitem, fyr,flogseq,fupdqty,a.fupdsi,fctlid,fctlid_1 from oinsint_d a  " +
                      "where fctlid in (select max(a.fctlid) as fctlid from oinsint_d a left join polh b on b.fctlid = a.fctlid_1 where a.fpolno =(select distinct fpolno from polh where fctlid ='" + fctlid_p + "') and b.fconfirm =3 group by a.fitem)";
            }
            if (flag == "EndorEEC" || flag == "EndorRIEEC")
            {
                sql = "select a.fid, c.fdesc as fdesc, a.fendtno, a.fitem, a.fdesc1, a.flogseq, " +
                        "a.fnum1, a.fupdnum1, a.fsi,a.fupdsi,a.fctlid, a.fctlid_1 " +
                        "from oinsint_b a  " +
                        "left join (select fid, fdesc from " + Dbm + "msparam where fctlid_1 = '0000000002') c on a.fid= c.fid " +
                        "where a.fctlid in  " +
                        "(select max(a.fctlid) as fctlid from oinsint_b a left join polh b on b.fctlid = a.fctlid_1 where a.fpolno =(select distinct fpolno from polh where fctlid ='" + fctlid_p + "')  " +
                        "and a.fvseq =(select distinct fvseq from polh where fctlid ='" + fctlid_p + "') and b.fconfirm =3 group by a.fitem) ";
            }
            if (flag == "EndorCAR" || flag == "EndorRICAR")
            {
                sql = "select a.fid, fidN, fendtno, fitem, a.fctlid from oinsint a   " +
                        "left join (select fid,fdesc as fidN from "+Dbm+"misec where fdoctype ='1' and ftype ='2' and fctlid_a ='0000000077') c on a.fid = c.fid " +
                        "where a.fctlid in  " +
                        "(select max(a.fctlid) as fctlid from oinsint a left join polh b on b.fctlid = a.fctlid_1 where a.fpolno =(select distinct fpolno from polh where fctlid ='" + fctlid_p + "')  " +
                        "and a.fvseq =(select distinct fvseq from polh where fctlid ='" + fctlid_p + "') and a.ftype = 'SEC1A' and b.fconfirm =3 group by a.fitem) ";
            }
            if (flag == "EndorPARLoc" || flag == "EndorCGLLoc" || flag == "RIEndorPARLoc" || flag == "RIEndorCGLLoc")
            {
                sql = "select rtrim(fdesc2) as fdesc, a.fendtno, a.fitem, a.fctlid, a.fdesc2 from oinsloc a " +
                            "left join (select fid,fdesc as fidN from " + Dbm + "msparam where fctlid_1 = '0000000006') b on a.fid = b.fid " +
                            "where a.fctlid in (select max(a.fctlid) as fctlid from oinsloc a left join polh b on b.fctlid = a.fctlid_1 where a.fpolno =(select distinct fpolno from polh where fctlid ='" + fctlid_p + "') and b.fconfirm =3 group by a.fitem)";
            }
            if (flag == "EndorPAR" || flag == "RIEndorPAR")
            {
                sql = "select fid, fendtno, fitem, fctlid from oinsint " +
                      "where fctlid in (select max(a.fctlid) as fctlid from oinsint a left join polh b on b.fctlid = a.fctlid_1 where a.fpolno =(select distinct fpolno from polh where fctlid ='" + fctlid_p + "') and b.fconfirm =3 group by a.fitem) and fitem_1 in ('" + fitem_1 + "')";
            }
            if (flag == "EndorCGL" || flag == "RIEndorCGL")
            {
                sql = "select fid, fendtno, fitem, fctlid from oinsper " +
                      "where fctlid in (select max(a.fctlid) as fctlid from oinsper a left join polh b on b.fctlid = a.fctlid_1 where a.fpolno =(select distinct fpolno from polh where fctlid ='" + fctlid_p + "') and b.fconfirm =3 group by a.fitem) and fitem_1 in ('" + fitem_1 + "')";
            }
            dataGridView1.DataSource = DBHelper.GetDataSet(sql + sqladd);
            for (int i = 3; i < dataGridView1.ColumnCount; i++)
            {
                this.dataGridView1.Columns[i].Visible = false;
            }
            this.dataGridView1.Columns[0].Visible = true;
            this.dataGridView1.Columns[1].Visible = true;
            this.dataGridView1.Columns[2].Visible = true;
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }
                if (flag == "EndorCAR" || flag == "EndorRICAR")
                {
                    if (dataGridView1.Rows[row].Cells["fctlid"].Value != null)
                    {
                        if (dataGridView1.Rows[row].Cells["fctlid"].Value.ToString().Length != 0)
                        {
                            label3.Text = dataGridView1.Rows[row].Cells["fctlid"].Value.ToString();
                        }
                    }
                    if (dataGridView1.Rows[row].Cells["fid"].Value != null)
                    {
                        if (dataGridView1.Rows[row].Cells["fid"].Value.ToString().Length != 0)
                        {
                            label5.Text = dataGridView1.Rows[row].Cells["fid"].Value.ToString();
                        }
                    }
                }
                if (flag == "EndorCPM")
                {
                    if (dataGridView1.Rows[row].Cells["fctlid"].Value != null)
                    {
                        if (dataGridView1.Rows[row].Cells["fctlid"].Value.ToString().Length != 0)
                        {
                            label3.Text = dataGridView1.Rows[row].Cells["fctlid"].Value.ToString();
                        }
                    }
                    if (dataGridView1.Rows[row].Cells["fdesc1"].Value != null)
                    {
                        if (dataGridView1.Rows[row].Cells["fdesc1"].Value.ToString().Length != 0)
                        {
                            label5.Text = dataGridView1.Rows[row].Cells["fdesc1"].Value.ToString();
                        }
                    }
                }
                if (flag == "EndorEEC" || flag == "EndorRIEEC")
                {
                    if (dataGridView1.Rows[row].Cells["fctlid"].Value != null)
                    {
                        if (dataGridView1.Rows[row].Cells["fctlid"].Value.ToString().Length != 0)
                        {
                            label3.Text = dataGridView1.Rows[row].Cells["fctlid"].Value.ToString();
                        }
                    }
                    if (dataGridView1.Rows[row].Cells["fdesc1"].Value != null)
                    {
                        if (dataGridView1.Rows[row].Cells["fdesc1"].Value.ToString().Length != 0)
                        {
                            label5.Text = dataGridView1.Rows[row].Cells["fdesc1"].Value.ToString();
                        }
                    }
                }
                if (flag == "EndorPARLoc" || flag == "EndorCGLLoc" || flag == "RIEndorPARLoc" || flag == "RIEndorCGLLoc")
                {
                    if (dataGridView1.Rows[row].Cells["fctlid"].Value != null)
                    {
                        if (dataGridView1.Rows[row].Cells["fctlid"].Value.ToString().Length != 0)
                        {
                            label3.Text = dataGridView1.Rows[row].Cells["fctlid"].Value.ToString();
                        }
                    }
                    if (dataGridView1.Rows[row].Cells["fdesc2"].Value != null)
                    {
                        if (dataGridView1.Rows[row].Cells["fdesc2"].Value.ToString().Length != 0)
                        {
                            label5.Text = dataGridView1.Rows[row].Cells["fdesc2"].Value.ToString();
                        }
                    }
                }
                if (flag == "EndorPAR" || flag == "RIEndorPAR")
                {
                    if (dataGridView1.Rows[row].Cells["fctlid"].Value != null)
                    {
                        if (dataGridView1.Rows[row].Cells["fctlid"].Value.ToString().Length != 0)
                        {
                            label3.Text = dataGridView1.Rows[row].Cells["fctlid"].Value.ToString();
                        }
                    }
                    if (dataGridView1.Rows[row].Cells["fid"].Value != null)
                    {
                        if (dataGridView1.Rows[row].Cells["fid"].Value.ToString().Length != 0)
                        {
                            label4.Text = dataGridView1.Rows[row].Cells["fid"].Value.ToString();
                        }
                    }
                }

                if (flag == "EndorCGL" || flag == "RIEndorCGL")
                {
                    if (dataGridView1.Rows[row].Cells["fctlid"].Value != null)
                    {
                        if (dataGridView1.Rows[row].Cells["fctlid"].Value.ToString().Length != 0)
                        {
                            label3.Text = dataGridView1.Rows[row].Cells["fctlid"].Value.ToString();
                        }
                    }
                    if (dataGridView1.Rows[row].Cells["fid"].Value != null)
                    {
                        if (dataGridView1.Rows[row].Cells["fid"].Value.ToString().Length != 0)
                        {
                            label4.Text = dataGridView1.Rows[row].Cells["fid"].Value.ToString();
                        }
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            if (flag == "EndorCPM")
            {
                string result2 = endorDirectInsured_CPM.CheckDuplicate(label5.Text.ToString(), "Click");
                if (result2 != "")
                {
                    MessageBox.Show(result2);
                }
                else
                {
                    endorDirectInsured_CPM.PickList_click(label3.Text.ToString());
                    this.Close();
                }
            }
            if (flag == "EndorEEC")
            {
                string result2 = endorDirectInsured_EEC.CheckDuplicate(label5.Text.ToString(), "Click");
                if (result2 != "")
                {
                    MessageBox.Show(result2);
                }
                else
                {
                    endorDirectInsured_EEC.PickList_click(label3.Text.ToString());
                    this.Close();
                }
            }
            if (flag == "EndorRIEEC")
            {
                string result2 = endorRIInsured_EEC.CheckDuplicate(label5.Text.ToString(), "Click");
                if (result2 != "")
                {
                    MessageBox.Show(result2);
                }
                else
                {
                    endorDirectInsured_EEC.PickList_click(label3.Text.ToString());
                    this.Close();
                }
            }
            if (flag == "EndorCAR")
            {
                    endorDirectInsured_CAR.PickList_click(label3.Text.ToString());
                    this.Close();
            }
            if (flag == "EndorRICAR")
            {
                endorRIInsured_CAR.PickList_click(label3.Text.ToString());
                    this.Close();
            }
            if (flag == "EndorPARLoc")
            {
                    endorDirectInsured_PAR.PickListLoc_click(label3.Text.ToString());
                    this.Close();
            }
            if (flag == "EndorPAR")
            {
                    endorDirectInsured_PAR.PickList_click(label3.Text.ToString());
                    this.Close();
            }
            if (flag == "RIEndorPARLoc")
            {
                endorRIInsured_PAR.PickListLoc_click(label3.Text.ToString());
                this.Close();
            }
            if (flag == "RIEndorPAR")
            {
                endorRIInsured_PAR.PickList_click(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EndorCGLLoc")
            {
                endorDirectInsured_CGL.PickListLoc_click(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EndorCGL")
            {
                endorDirectInsured_CGL.PickList_click(label3.Text.ToString());
                this.Close();
            }
            if (flag == "RIEndorCGLLoc")
            {
                endorRIInsured_CGL.PickListLoc_click(label3.Text.ToString());
                this.Close();
            }
            if (flag == "RIEndorCGL")
            {
                endorRIInsured_CGL.PickList_click(label3.Text.ToString());
                this.Close();
            }
        }


        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }


    }
}
