namespace INS.Business.SearchForm
{
    partial class bdwn_cl
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.fclmno = new System.Windows.Forms.TextBox();
            this.label66 = new System.Windows.Forms.Label();
            this.flogseq = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.faccdate = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.fos01 = new Shorty.Windows.Forms.NumericTextBox();
            this.frvos01 = new Shorty.Windows.Forms.NumericTextBox();
            this.fadj01 = new Shorty.Windows.Forms.NumericTextBox();
            this.fadj02 = new Shorty.Windows.Forms.NumericTextBox();
            this.frvos02 = new Shorty.Windows.Forms.NumericTextBox();
            this.fos02 = new Shorty.Windows.Forms.NumericTextBox();
            this.fadj03 = new Shorty.Windows.Forms.NumericTextBox();
            this.frvos03 = new Shorty.Windows.Forms.NumericTextBox();
            this.fos03 = new Shorty.Windows.Forms.NumericTextBox();
            this.fadj04 = new Shorty.Windows.Forms.NumericTextBox();
            this.frvos04 = new Shorty.Windows.Forms.NumericTextBox();
            this.fos04 = new Shorty.Windows.Forms.NumericTextBox();
            this.fadj05 = new Shorty.Windows.Forms.NumericTextBox();
            this.frvos05 = new Shorty.Windows.Forms.NumericTextBox();
            this.fos05 = new Shorty.Windows.Forms.NumericTextBox();
            this.fadj06 = new Shorty.Windows.Forms.NumericTextBox();
            this.frvos06 = new Shorty.Windows.Forms.NumericTextBox();
            this.fos06 = new Shorty.Windows.Forms.NumericTextBox();
            this.fadjA = new Shorty.Windows.Forms.NumericTextBox();
            this.frvosA = new Shorty.Windows.Forms.NumericTextBox();
            this.fosA = new Shorty.Windows.Forms.NumericTextBox();
            this.label22 = new System.Windows.Forms.Label();
            this.label23 = new System.Windows.Forms.Label();
            this.label24 = new System.Windows.Forms.Label();
            this.label25 = new System.Windows.Forms.Label();
            this.label26 = new System.Windows.Forms.Label();
            this.fadj07 = new Shorty.Windows.Forms.NumericTextBox();
            this.frvos07 = new Shorty.Windows.Forms.NumericTextBox();
            this.fos07 = new Shorty.Windows.Forms.NumericTextBox();
            this.label28 = new System.Windows.Forms.Label();
            this.label30 = new System.Windows.Forms.Label();
            this.fadj08 = new Shorty.Windows.Forms.NumericTextBox();
            this.frvos08 = new Shorty.Windows.Forms.NumericTextBox();
            this.fos08 = new Shorty.Windows.Forms.NumericTextBox();
            this.fadj09 = new Shorty.Windows.Forms.NumericTextBox();
            this.frvos09 = new Shorty.Windows.Forms.NumericTextBox();
            this.fos09 = new Shorty.Windows.Forms.NumericTextBox();
            this.label31 = new System.Windows.Forms.Label();
            this.label32 = new System.Windows.Forms.Label();
            this.label33 = new System.Windows.Forms.Label();
            this.fadjC = new Shorty.Windows.Forms.NumericTextBox();
            this.frvosC = new Shorty.Windows.Forms.NumericTextBox();
            this.fosC = new Shorty.Windows.Forms.NumericTextBox();
            this.fadj = new Shorty.Windows.Forms.NumericTextBox();
            this.frvos = new Shorty.Windows.Forms.NumericTextBox();
            this.fos = new Shorty.Windows.Forms.NumericTextBox();
            this.fremark_t = new System.Windows.Forms.TextBox();
            this.label36 = new System.Windows.Forms.Label();
            this.XcmdExit = new System.Windows.Forms.Button();
            this.XcmdConfirm = new System.Windows.Forms.Button();
            this.fadj11 = new Shorty.Windows.Forms.NumericTextBox();
            this.frvos11 = new Shorty.Windows.Forms.NumericTextBox();
            this.fos11 = new Shorty.Windows.Forms.NumericTextBox();
            this.fadj12 = new Shorty.Windows.Forms.NumericTextBox();
            this.frvos12 = new Shorty.Windows.Forms.NumericTextBox();
            this.fos12 = new Shorty.Windows.Forms.NumericTextBox();
            this.label27 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.fadj10 = new Shorty.Windows.Forms.NumericTextBox();
            this.frvos10 = new Shorty.Windows.Forms.NumericTextBox();
            this.fos10 = new Shorty.Windows.Forms.NumericTextBox();
            this.fadj13 = new Shorty.Windows.Forms.NumericTextBox();
            this.frvos13 = new Shorty.Windows.Forms.NumericTextBox();
            this.fos13 = new Shorty.Windows.Forms.NumericTextBox();
            this.fadjA2 = new System.Windows.Forms.Label();
            this.fadjB = new System.Windows.Forms.Label();
            this.frvosA2 = new System.Windows.Forms.Label();
            this.frvosB = new System.Windows.Forms.Label();
            this.ftadj13 = new System.Windows.Forms.TextBox();
            this.ftadj10 = new System.Windows.Forms.TextBox();
            this.ftadj12 = new System.Windows.Forms.TextBox();
            this.ftadj11 = new System.Windows.Forms.TextBox();
            this.ftadj = new System.Windows.Forms.TextBox();
            this.ftadjC = new System.Windows.Forms.TextBox();
            this.ftadj09 = new System.Windows.Forms.TextBox();
            this.ftadj08 = new System.Windows.Forms.TextBox();
            this.ftadj07 = new System.Windows.Forms.TextBox();
            this.ftadjA = new System.Windows.Forms.TextBox();
            this.ftadj06 = new System.Windows.Forms.TextBox();
            this.ftadj05 = new System.Windows.Forms.TextBox();
            this.ftadj04 = new System.Windows.Forms.TextBox();
            this.ftadj03 = new System.Windows.Forms.TextBox();
            this.ftadj02 = new System.Windows.Forms.TextBox();
            this.ftadj01 = new System.Windows.Forms.TextBox();
            this.ftadjB = new System.Windows.Forms.Label();
            this.ftadjA2 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.fosB = new System.Windows.Forms.Label();
            this.fosA2 = new System.Windows.Forms.Label();
            this.XAutoLbl = new System.Windows.Forms.Label();
            this.SuspendLayout();
            // 
            // fclmno
            // 
            this.fclmno.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fclmno.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fclmno.Location = new System.Drawing.Point(95, 16);
            this.fclmno.Name = "fclmno";
            this.fclmno.ReadOnly = true;
            this.fclmno.Size = new System.Drawing.Size(112, 22);
            this.fclmno.TabIndex = 1;
            // 
            // label66
            // 
            this.label66.AutoSize = true;
            this.label66.ForeColor = System.Drawing.Color.White;
            this.label66.Location = new System.Drawing.Point(36, 22);
            this.label66.Name = "label66";
            this.label66.Size = new System.Drawing.Size(53, 12);
            this.label66.TabIndex = 293;
            this.label66.Text = "Claim No.";
            // 
            // flogseq
            // 
            this.flogseq.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.flogseq.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.flogseq.Location = new System.Drawing.Point(269, 16);
            this.flogseq.Name = "flogseq";
            this.flogseq.ReadOnly = true;
            this.flogseq.Size = new System.Drawing.Size(54, 22);
            this.flogseq.TabIndex = 2;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.ForeColor = System.Drawing.Color.White;
            this.label1.Location = new System.Drawing.Point(225, 22);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 295;
            this.label1.Text = "Adjust#";
            // 
            // faccdate
            // 
            this.faccdate.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.faccdate.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.faccdate.Location = new System.Drawing.Point(397, 16);
            this.faccdate.Name = "faccdate";
            this.faccdate.ReadOnly = true;
            this.faccdate.Size = new System.Drawing.Size(97, 22);
            this.faccdate.TabIndex = 3;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.ForeColor = System.Drawing.Color.White;
            this.label2.Location = new System.Drawing.Point(346, 22);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(48, 12);
            this.label2.TabIndex = 297;
            this.label2.Text = "A/C Date";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Underline);
            this.label3.ForeColor = System.Drawing.Color.White;
            this.label3.Location = new System.Drawing.Point(36, 47);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(144, 16);
            this.label3.TabIndex = 299;
            this.label3.Text = "PART A1 - Damages";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label11.ForeColor = System.Drawing.Color.White;
            this.label11.Location = new System.Drawing.Point(408, 47);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(104, 16);
            this.label11.TabIndex = 341;
            this.label11.Text = "Last O/S (1)";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label12.ForeColor = System.Drawing.Color.White;
            this.label12.Location = new System.Drawing.Point(507, 47);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(128, 16);
            this.label12.TabIndex = 342;
            this.label12.Text = "Revised O/S (2)";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label13.ForeColor = System.Drawing.Color.White;
            this.label13.Location = new System.Drawing.Point(611, 47);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(168, 16);
            this.label13.TabIndex = 343;
            this.label13.Text = "Adjustment (2) - (1)";
            // 
            // fos01
            // 
            this.fos01.AllowNegative = true;
            this.fos01.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fos01.Enabled = false;
            this.fos01.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fos01.Location = new System.Drawing.Point(397, 66);
            this.fos01.Name = "fos01";
            this.fos01.NumericPrecision = 155;
            this.fos01.NumericScaleOnFocus = 2;
            this.fos01.NumericScaleOnLostFocus = 2;
            this.fos01.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fos01.ReadOnly = true;
            this.fos01.Size = new System.Drawing.Size(97, 22);
            this.fos01.TabIndex = 4;
            this.fos01.Text = "0";
            this.fos01.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fos01.ZeroIsValid = true;
            // 
            // frvos01
            // 
            this.frvos01.AllowNegative = true;
            this.frvos01.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvos01.Enabled = false;
            this.frvos01.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvos01.Location = new System.Drawing.Point(495, 66);
            this.frvos01.Name = "frvos01";
            this.frvos01.NumericPrecision = 15;
            this.frvos01.NumericScaleOnFocus = 2;
            this.frvos01.NumericScaleOnLostFocus = 2;
            this.frvos01.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvos01.ReadOnly = true;
            this.frvos01.Size = new System.Drawing.Size(112, 22);
            this.frvos01.TabIndex = 5;
            this.frvos01.Text = "0";
            this.frvos01.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvos01.ZeroIsValid = true;
            this.frvos01.Validated += new System.EventHandler(this.frvos01_Validated);
            // 
            // fadj01
            // 
            this.fadj01.AllowNegative = true;
            this.fadj01.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadj01.Enabled = false;
            this.fadj01.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadj01.Location = new System.Drawing.Point(608, 66);
            this.fadj01.Name = "fadj01";
            this.fadj01.NumericPrecision = 15;
            this.fadj01.NumericScaleOnFocus = 2;
            this.fadj01.NumericScaleOnLostFocus = 2;
            this.fadj01.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadj01.ReadOnly = true;
            this.fadj01.Size = new System.Drawing.Size(114, 22);
            this.fadj01.TabIndex = 6;
            this.fadj01.Text = "0";
            this.fadj01.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadj01.ZeroIsValid = true;
            // 
            // fadj02
            // 
            this.fadj02.AllowNegative = true;
            this.fadj02.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadj02.Enabled = false;
            this.fadj02.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadj02.Location = new System.Drawing.Point(608, 88);
            this.fadj02.Name = "fadj02";
            this.fadj02.NumericPrecision = 15;
            this.fadj02.NumericScaleOnFocus = 2;
            this.fadj02.NumericScaleOnLostFocus = 2;
            this.fadj02.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadj02.ReadOnly = true;
            this.fadj02.Size = new System.Drawing.Size(114, 22);
            this.fadj02.TabIndex = 9;
            this.fadj02.Text = "0";
            this.fadj02.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadj02.ZeroIsValid = true;
            // 
            // frvos02
            // 
            this.frvos02.AllowNegative = true;
            this.frvos02.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvos02.Enabled = false;
            this.frvos02.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvos02.Location = new System.Drawing.Point(495, 88);
            this.frvos02.Name = "frvos02";
            this.frvos02.NumericPrecision = 15;
            this.frvos02.NumericScaleOnFocus = 2;
            this.frvos02.NumericScaleOnLostFocus = 2;
            this.frvos02.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvos02.ReadOnly = true;
            this.frvos02.Size = new System.Drawing.Size(112, 22);
            this.frvos02.TabIndex = 8;
            this.frvos02.Text = "0";
            this.frvos02.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvos02.ZeroIsValid = true;
            this.frvos02.Validated += new System.EventHandler(this.frvos02_Validated);
            // 
            // fos02
            // 
            this.fos02.AllowNegative = true;
            this.fos02.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fos02.Enabled = false;
            this.fos02.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fos02.Location = new System.Drawing.Point(397, 88);
            this.fos02.Name = "fos02";
            this.fos02.NumericPrecision = 15;
            this.fos02.NumericScaleOnFocus = 2;
            this.fos02.NumericScaleOnLostFocus = 2;
            this.fos02.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fos02.ReadOnly = true;
            this.fos02.Size = new System.Drawing.Size(97, 22);
            this.fos02.TabIndex = 7;
            this.fos02.Text = "0";
            this.fos02.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fos02.ZeroIsValid = true;
            // 
            // fadj03
            // 
            this.fadj03.AllowNegative = true;
            this.fadj03.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadj03.Enabled = false;
            this.fadj03.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadj03.Location = new System.Drawing.Point(608, 110);
            this.fadj03.Name = "fadj03";
            this.fadj03.NumericPrecision = 15;
            this.fadj03.NumericScaleOnFocus = 2;
            this.fadj03.NumericScaleOnLostFocus = 2;
            this.fadj03.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadj03.ReadOnly = true;
            this.fadj03.Size = new System.Drawing.Size(114, 22);
            this.fadj03.TabIndex = 12;
            this.fadj03.Text = "0";
            this.fadj03.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadj03.ZeroIsValid = true;
            // 
            // frvos03
            // 
            this.frvos03.AllowNegative = true;
            this.frvos03.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvos03.Enabled = false;
            this.frvos03.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvos03.Location = new System.Drawing.Point(495, 110);
            this.frvos03.Name = "frvos03";
            this.frvos03.NumericPrecision = 15;
            this.frvos03.NumericScaleOnFocus = 2;
            this.frvos03.NumericScaleOnLostFocus = 2;
            this.frvos03.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvos03.ReadOnly = true;
            this.frvos03.Size = new System.Drawing.Size(112, 22);
            this.frvos03.TabIndex = 11;
            this.frvos03.Text = "0";
            this.frvos03.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvos03.ZeroIsValid = true;
            this.frvos03.Validated += new System.EventHandler(this.frvos03_Validated);
            // 
            // fos03
            // 
            this.fos03.AllowNegative = true;
            this.fos03.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fos03.Enabled = false;
            this.fos03.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fos03.Location = new System.Drawing.Point(397, 110);
            this.fos03.Name = "fos03";
            this.fos03.NumericPrecision = 15;
            this.fos03.NumericScaleOnFocus = 2;
            this.fos03.NumericScaleOnLostFocus = 2;
            this.fos03.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fos03.ReadOnly = true;
            this.fos03.Size = new System.Drawing.Size(97, 22);
            this.fos03.TabIndex = 10;
            this.fos03.Text = "0";
            this.fos03.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fos03.ZeroIsValid = true;
            // 
            // fadj04
            // 
            this.fadj04.AllowNegative = true;
            this.fadj04.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadj04.Enabled = false;
            this.fadj04.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadj04.Location = new System.Drawing.Point(608, 132);
            this.fadj04.Name = "fadj04";
            this.fadj04.NumericPrecision = 15;
            this.fadj04.NumericScaleOnFocus = 2;
            this.fadj04.NumericScaleOnLostFocus = 2;
            this.fadj04.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadj04.ReadOnly = true;
            this.fadj04.Size = new System.Drawing.Size(114, 22);
            this.fadj04.TabIndex = 15;
            this.fadj04.Text = "0";
            this.fadj04.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadj04.ZeroIsValid = true;
            // 
            // frvos04
            // 
            this.frvos04.AllowNegative = true;
            this.frvos04.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvos04.Enabled = false;
            this.frvos04.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvos04.Location = new System.Drawing.Point(495, 132);
            this.frvos04.Name = "frvos04";
            this.frvos04.NumericPrecision = 15;
            this.frvos04.NumericScaleOnFocus = 2;
            this.frvos04.NumericScaleOnLostFocus = 2;
            this.frvos04.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvos04.ReadOnly = true;
            this.frvos04.Size = new System.Drawing.Size(112, 22);
            this.frvos04.TabIndex = 14;
            this.frvos04.Text = "0";
            this.frvos04.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvos04.ZeroIsValid = true;
            this.frvos04.Validated += new System.EventHandler(this.frvos04_Validated);
            // 
            // fos04
            // 
            this.fos04.AllowNegative = true;
            this.fos04.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fos04.Enabled = false;
            this.fos04.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fos04.Location = new System.Drawing.Point(397, 132);
            this.fos04.Name = "fos04";
            this.fos04.NumericPrecision = 15;
            this.fos04.NumericScaleOnFocus = 2;
            this.fos04.NumericScaleOnLostFocus = 2;
            this.fos04.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fos04.ReadOnly = true;
            this.fos04.Size = new System.Drawing.Size(97, 22);
            this.fos04.TabIndex = 13;
            this.fos04.Text = "0";
            this.fos04.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fos04.ZeroIsValid = true;
            // 
            // fadj05
            // 
            this.fadj05.AllowNegative = true;
            this.fadj05.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadj05.Enabled = false;
            this.fadj05.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadj05.Location = new System.Drawing.Point(608, 154);
            this.fadj05.Name = "fadj05";
            this.fadj05.NumericPrecision = 15;
            this.fadj05.NumericScaleOnFocus = 2;
            this.fadj05.NumericScaleOnLostFocus = 2;
            this.fadj05.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadj05.ReadOnly = true;
            this.fadj05.Size = new System.Drawing.Size(114, 22);
            this.fadj05.TabIndex = 18;
            this.fadj05.Text = "0";
            this.fadj05.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadj05.ZeroIsValid = true;
            // 
            // frvos05
            // 
            this.frvos05.AllowNegative = true;
            this.frvos05.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvos05.Enabled = false;
            this.frvos05.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvos05.Location = new System.Drawing.Point(495, 154);
            this.frvos05.Name = "frvos05";
            this.frvos05.NumericPrecision = 15;
            this.frvos05.NumericScaleOnFocus = 2;
            this.frvos05.NumericScaleOnLostFocus = 2;
            this.frvos05.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvos05.ReadOnly = true;
            this.frvos05.Size = new System.Drawing.Size(112, 22);
            this.frvos05.TabIndex = 17;
            this.frvos05.Text = "0";
            this.frvos05.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvos05.ZeroIsValid = true;
            this.frvos05.Validated += new System.EventHandler(this.frvos05_Validated);
            // 
            // fos05
            // 
            this.fos05.AllowNegative = true;
            this.fos05.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fos05.Enabled = false;
            this.fos05.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fos05.Location = new System.Drawing.Point(397, 154);
            this.fos05.Name = "fos05";
            this.fos05.NumericPrecision = 15;
            this.fos05.NumericScaleOnFocus = 2;
            this.fos05.NumericScaleOnLostFocus = 2;
            this.fos05.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fos05.ReadOnly = true;
            this.fos05.Size = new System.Drawing.Size(97, 22);
            this.fos05.TabIndex = 16;
            this.fos05.Text = "0";
            this.fos05.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fos05.ZeroIsValid = true;
            // 
            // fadj06
            // 
            this.fadj06.AllowNegative = true;
            this.fadj06.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadj06.Enabled = false;
            this.fadj06.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadj06.Location = new System.Drawing.Point(608, 198);
            this.fadj06.Name = "fadj06";
            this.fadj06.NumericPrecision = 15;
            this.fadj06.NumericScaleOnFocus = 2;
            this.fadj06.NumericScaleOnLostFocus = 2;
            this.fadj06.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadj06.ReadOnly = true;
            this.fadj06.Size = new System.Drawing.Size(114, 22);
            this.fadj06.TabIndex = 24;
            this.fadj06.Text = "0";
            this.fadj06.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadj06.ZeroIsValid = true;
            // 
            // frvos06
            // 
            this.frvos06.AllowNegative = true;
            this.frvos06.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvos06.Enabled = false;
            this.frvos06.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvos06.Location = new System.Drawing.Point(495, 198);
            this.frvos06.Name = "frvos06";
            this.frvos06.NumericPrecision = 15;
            this.frvos06.NumericScaleOnFocus = 2;
            this.frvos06.NumericScaleOnLostFocus = 2;
            this.frvos06.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvos06.ReadOnly = true;
            this.frvos06.Size = new System.Drawing.Size(112, 22);
            this.frvos06.TabIndex = 23;
            this.frvos06.Text = "0";
            this.frvos06.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvos06.ZeroIsValid = true;
            this.frvos06.Validated += new System.EventHandler(this.frvos06_Validated);
            // 
            // fos06
            // 
            this.fos06.AllowNegative = true;
            this.fos06.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fos06.Enabled = false;
            this.fos06.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fos06.Location = new System.Drawing.Point(397, 198);
            this.fos06.Name = "fos06";
            this.fos06.NumericPrecision = 15;
            this.fos06.NumericScaleOnFocus = 2;
            this.fos06.NumericScaleOnLostFocus = 2;
            this.fos06.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fos06.ReadOnly = true;
            this.fos06.Size = new System.Drawing.Size(97, 22);
            this.fos06.TabIndex = 22;
            this.fos06.Text = "0";
            this.fos06.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fos06.ZeroIsValid = true;
            // 
            // fadjA
            // 
            this.fadjA.AllowNegative = true;
            this.fadjA.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadjA.Enabled = false;
            this.fadjA.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadjA.Location = new System.Drawing.Point(608, 242);
            this.fadjA.Name = "fadjA";
            this.fadjA.NumericPrecision = 15;
            this.fadjA.NumericScaleOnFocus = 2;
            this.fadjA.NumericScaleOnLostFocus = 2;
            this.fadjA.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadjA.ReadOnly = true;
            this.fadjA.Size = new System.Drawing.Size(114, 22);
            this.fadjA.TabIndex = 30;
            this.fadjA.Text = "0";
            this.fadjA.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadjA.ZeroIsValid = true;
            // 
            // frvosA
            // 
            this.frvosA.AllowNegative = true;
            this.frvosA.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvosA.Enabled = false;
            this.frvosA.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvosA.Location = new System.Drawing.Point(495, 242);
            this.frvosA.Name = "frvosA";
            this.frvosA.NumericPrecision = 15;
            this.frvosA.NumericScaleOnFocus = 2;
            this.frvosA.NumericScaleOnLostFocus = 2;
            this.frvosA.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvosA.ReadOnly = true;
            this.frvosA.Size = new System.Drawing.Size(112, 22);
            this.frvosA.TabIndex = 29;
            this.frvosA.Text = "0";
            this.frvosA.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvosA.ZeroIsValid = true;
            // 
            // fosA
            // 
            this.fosA.AllowNegative = true;
            this.fosA.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fosA.Enabled = false;
            this.fosA.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fosA.Location = new System.Drawing.Point(397, 242);
            this.fosA.Name = "fosA";
            this.fosA.NumericPrecision = 15;
            this.fosA.NumericScaleOnFocus = 2;
            this.fosA.NumericScaleOnLostFocus = 2;
            this.fosA.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fosA.ReadOnly = true;
            this.fosA.Size = new System.Drawing.Size(97, 22);
            this.fosA.TabIndex = 28;
            this.fosA.Text = "0";
            this.fosA.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fosA.ZeroIsValid = true;
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label22.ForeColor = System.Drawing.Color.White;
            this.label22.Location = new System.Drawing.Point(317, 248);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(80, 16);
            this.label22.TabIndex = 384;
            this.label22.Text = "Sub Total";
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label23.ForeColor = System.Drawing.Color.Navy;
            this.label23.Location = new System.Drawing.Point(35, 204);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(304, 16);
            this.label23.TabIndex = 385;
            this.label23.Text = "Less: Contributory negligence, if any";
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label24.ForeColor = System.Drawing.Color.Navy;
            this.label24.Location = new System.Drawing.Point(36, 226);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(80, 16);
            this.label24.TabIndex = 386;
            this.label24.Text = "Less: ECC";
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label25.ForeColor = System.Drawing.Color.White;
            this.label25.Location = new System.Drawing.Point(36, 182);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(56, 16);
            this.label25.TabIndex = 387;
            this.label25.Text = "Others";
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label26.ForeColor = System.Drawing.Color.White;
            this.label26.Location = new System.Drawing.Point(36, 299);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(400, 16);
            this.label26.TabIndex = 388;
            this.label26.Text = "Other Expenses (Medical Report, Adjuster Fee ...)";
            // 
            // fadj07
            // 
            this.fadj07.AllowNegative = true;
            this.fadj07.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadj07.Enabled = false;
            this.fadj07.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadj07.Location = new System.Drawing.Point(608, 220);
            this.fadj07.Name = "fadj07";
            this.fadj07.NumericPrecision = 15;
            this.fadj07.NumericScaleOnFocus = 2;
            this.fadj07.NumericScaleOnLostFocus = 2;
            this.fadj07.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadj07.ReadOnly = true;
            this.fadj07.Size = new System.Drawing.Size(114, 22);
            this.fadj07.TabIndex = 27;
            this.fadj07.Text = "0";
            this.fadj07.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadj07.ZeroIsValid = true;
            // 
            // frvos07
            // 
            this.frvos07.AllowNegative = true;
            this.frvos07.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvos07.Enabled = false;
            this.frvos07.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvos07.Location = new System.Drawing.Point(495, 220);
            this.frvos07.Name = "frvos07";
            this.frvos07.NumericPrecision = 15;
            this.frvos07.NumericScaleOnFocus = 2;
            this.frvos07.NumericScaleOnLostFocus = 2;
            this.frvos07.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvos07.ReadOnly = true;
            this.frvos07.Size = new System.Drawing.Size(112, 22);
            this.frvos07.TabIndex = 26;
            this.frvos07.Text = "0";
            this.frvos07.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvos07.ZeroIsValid = true;
            this.frvos07.Validated += new System.EventHandler(this.frvos07_Validated);
            // 
            // fos07
            // 
            this.fos07.AllowNegative = true;
            this.fos07.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fos07.Enabled = false;
            this.fos07.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fos07.Location = new System.Drawing.Point(397, 220);
            this.fos07.Name = "fos07";
            this.fos07.NumericPrecision = 15;
            this.fos07.NumericScaleOnFocus = 2;
            this.fos07.NumericScaleOnLostFocus = 2;
            this.fos07.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fos07.ReadOnly = true;
            this.fos07.Size = new System.Drawing.Size(97, 22);
            this.fos07.TabIndex = 25;
            this.fos07.Text = "0";
            this.fos07.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fos07.ZeroIsValid = true;
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label28.ForeColor = System.Drawing.Color.White;
            this.label28.Location = new System.Drawing.Point(36, 344);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(328, 16);
            this.label28.TabIndex = 393;
            this.label28.Text = "PART B - Claimant\'s Costs && Disbursement";
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label30.ForeColor = System.Drawing.Color.White;
            this.label30.Location = new System.Drawing.Point(36, 373);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(272, 16);
            this.label30.TabIndex = 398;
            this.label30.Text = "PART C - Own Costs && Disbursement";
            // 
            // fadj08
            // 
            this.fadj08.AllowNegative = true;
            this.fadj08.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadj08.Enabled = false;
            this.fadj08.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadj08.Location = new System.Drawing.Point(608, 293);
            this.fadj08.Name = "fadj08";
            this.fadj08.NumericPrecision = 15;
            this.fadj08.NumericScaleOnFocus = 2;
            this.fadj08.NumericScaleOnLostFocus = 2;
            this.fadj08.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadj08.ReadOnly = true;
            this.fadj08.Size = new System.Drawing.Size(114, 22);
            this.fadj08.TabIndex = 33;
            this.fadj08.Text = "0";
            this.fadj08.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadj08.ZeroIsValid = true;
            // 
            // frvos08
            // 
            this.frvos08.AllowNegative = true;
            this.frvos08.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvos08.Enabled = false;
            this.frvos08.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvos08.Location = new System.Drawing.Point(495, 293);
            this.frvos08.Name = "frvos08";
            this.frvos08.NumericPrecision = 15;
            this.frvos08.NumericScaleOnFocus = 2;
            this.frvos08.NumericScaleOnLostFocus = 2;
            this.frvos08.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvos08.ReadOnly = true;
            this.frvos08.Size = new System.Drawing.Size(112, 22);
            this.frvos08.TabIndex = 32;
            this.frvos08.Text = "0";
            this.frvos08.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvos08.ZeroIsValid = true;
            this.frvos08.Validated += new System.EventHandler(this.frvos08_Validated);
            // 
            // fos08
            // 
            this.fos08.AllowNegative = true;
            this.fos08.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fos08.Enabled = false;
            this.fos08.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fos08.Location = new System.Drawing.Point(397, 293);
            this.fos08.Name = "fos08";
            this.fos08.NumericPrecision = 15;
            this.fos08.NumericScaleOnFocus = 2;
            this.fos08.NumericScaleOnLostFocus = 2;
            this.fos08.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fos08.ReadOnly = true;
            this.fos08.Size = new System.Drawing.Size(97, 22);
            this.fos08.TabIndex = 31;
            this.fos08.Text = "0";
            this.fos08.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fos08.ZeroIsValid = true;
            // 
            // fadj09
            // 
            this.fadj09.AllowNegative = true;
            this.fadj09.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadj09.Enabled = false;
            this.fadj09.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadj09.Location = new System.Drawing.Point(608, 339);
            this.fadj09.Name = "fadj09";
            this.fadj09.NumericPrecision = 15;
            this.fadj09.NumericScaleOnFocus = 2;
            this.fadj09.NumericScaleOnLostFocus = 2;
            this.fadj09.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadj09.ReadOnly = true;
            this.fadj09.Size = new System.Drawing.Size(114, 22);
            this.fadj09.TabIndex = 36;
            this.fadj09.Text = "0";
            this.fadj09.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadj09.ZeroIsValid = true;
            // 
            // frvos09
            // 
            this.frvos09.AllowNegative = true;
            this.frvos09.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvos09.Enabled = false;
            this.frvos09.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvos09.Location = new System.Drawing.Point(495, 339);
            this.frvos09.Name = "frvos09";
            this.frvos09.NumericPrecision = 15;
            this.frvos09.NumericScaleOnFocus = 2;
            this.frvos09.NumericScaleOnLostFocus = 2;
            this.frvos09.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvos09.ReadOnly = true;
            this.frvos09.Size = new System.Drawing.Size(112, 22);
            this.frvos09.TabIndex = 35;
            this.frvos09.Text = "0";
            this.frvos09.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvos09.ZeroIsValid = true;
            this.frvos09.Validated += new System.EventHandler(this.frvos09_Validated);
            // 
            // fos09
            // 
            this.fos09.AllowNegative = true;
            this.fos09.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fos09.Enabled = false;
            this.fos09.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fos09.Location = new System.Drawing.Point(397, 339);
            this.fos09.Name = "fos09";
            this.fos09.NumericPrecision = 15;
            this.fos09.NumericScaleOnFocus = 2;
            this.fos09.NumericScaleOnLostFocus = 2;
            this.fos09.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fos09.ReadOnly = true;
            this.fos09.Size = new System.Drawing.Size(97, 22);
            this.fos09.TabIndex = 34;
            this.fos09.Text = "0";
            this.fos09.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fos09.ZeroIsValid = true;
            // 
            // label31
            // 
            this.label31.AutoSize = true;
            this.label31.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label31.ForeColor = System.Drawing.Color.White;
            this.label31.Location = new System.Drawing.Point(36, 391);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(112, 16);
            this.label31.TabIndex = 408;
            this.label31.Text = "Mediation Fee";
            // 
            // label32
            // 
            this.label32.AutoSize = true;
            this.label32.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label32.ForeColor = System.Drawing.Color.White;
            this.label32.Location = new System.Drawing.Point(36, 414);
            this.label32.Name = "label32";
            this.label32.Size = new System.Drawing.Size(96, 16);
            this.label32.TabIndex = 409;
            this.label32.Text = "Legal Costs";
            // 
            // label33
            // 
            this.label33.AutoSize = true;
            this.label33.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label33.ForeColor = System.Drawing.Color.White;
            this.label33.Location = new System.Drawing.Point(36, 439);
            this.label33.Name = "label33";
            this.label33.Size = new System.Drawing.Size(120, 16);
            this.label33.TabIndex = 410;
            this.label33.Text = "Other Expenses";
            // 
            // fadjC
            // 
            this.fadjC.AllowNegative = true;
            this.fadjC.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadjC.Enabled = false;
            this.fadjC.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadjC.Location = new System.Drawing.Point(608, 453);
            this.fadjC.Name = "fadjC";
            this.fadjC.NumericPrecision = 15;
            this.fadjC.NumericScaleOnFocus = 2;
            this.fadjC.NumericScaleOnLostFocus = 2;
            this.fadjC.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadjC.ReadOnly = true;
            this.fadjC.Size = new System.Drawing.Size(114, 22);
            this.fadjC.TabIndex = 48;
            this.fadjC.Text = "0";
            this.fadjC.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadjC.ZeroIsValid = true;
            // 
            // frvosC
            // 
            this.frvosC.AllowNegative = true;
            this.frvosC.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvosC.Enabled = false;
            this.frvosC.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvosC.Location = new System.Drawing.Point(495, 453);
            this.frvosC.Name = "frvosC";
            this.frvosC.NumericPrecision = 15;
            this.frvosC.NumericScaleOnFocus = 2;
            this.frvosC.NumericScaleOnLostFocus = 2;
            this.frvosC.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvosC.ReadOnly = true;
            this.frvosC.Size = new System.Drawing.Size(112, 22);
            this.frvosC.TabIndex = 47;
            this.frvosC.Text = "0";
            this.frvosC.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvosC.ZeroIsValid = true;
            // 
            // fosC
            // 
            this.fosC.AllowNegative = true;
            this.fosC.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fosC.Enabled = false;
            this.fosC.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fosC.Location = new System.Drawing.Point(397, 453);
            this.fosC.Name = "fosC";
            this.fosC.NumericPrecision = 15;
            this.fosC.NumericScaleOnFocus = 2;
            this.fosC.NumericScaleOnLostFocus = 2;
            this.fosC.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fosC.ReadOnly = true;
            this.fosC.Size = new System.Drawing.Size(97, 22);
            this.fosC.TabIndex = 46;
            this.fosC.Text = "0";
            this.fosC.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fosC.ZeroIsValid = true;
            // 
            // fadj
            // 
            this.fadj.AllowNegative = true;
            this.fadj.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadj.Enabled = false;
            this.fadj.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadj.Location = new System.Drawing.Point(608, 499);
            this.fadj.Name = "fadj";
            this.fadj.NumericPrecision = 15;
            this.fadj.NumericScaleOnFocus = 2;
            this.fadj.NumericScaleOnLostFocus = 2;
            this.fadj.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadj.ReadOnly = true;
            this.fadj.Size = new System.Drawing.Size(114, 22);
            this.fadj.TabIndex = 51;
            this.fadj.Text = "0";
            this.fadj.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadj.ZeroIsValid = true;
            // 
            // frvos
            // 
            this.frvos.AllowNegative = true;
            this.frvos.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvos.Enabled = false;
            this.frvos.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvos.Location = new System.Drawing.Point(495, 499);
            this.frvos.Name = "frvos";
            this.frvos.NumericPrecision = 15;
            this.frvos.NumericScaleOnFocus = 2;
            this.frvos.NumericScaleOnLostFocus = 2;
            this.frvos.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvos.ReadOnly = true;
            this.frvos.Size = new System.Drawing.Size(112, 22);
            this.frvos.TabIndex = 50;
            this.frvos.Text = "0";
            this.frvos.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvos.ZeroIsValid = true;
            // 
            // fos
            // 
            this.fos.AllowNegative = true;
            this.fos.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fos.Enabled = false;
            this.fos.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fos.Location = new System.Drawing.Point(397, 499);
            this.fos.Name = "fos";
            this.fos.NumericPrecision = 15;
            this.fos.NumericScaleOnFocus = 2;
            this.fos.NumericScaleOnLostFocus = 2;
            this.fos.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fos.ReadOnly = true;
            this.fos.Size = new System.Drawing.Size(97, 22);
            this.fos.TabIndex = 49;
            this.fos.Text = "0";
            this.fos.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fos.ZeroIsValid = true;
            // 
            // fremark_t
            // 
            this.fremark_t.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fremark_t.Enabled = false;
            this.fremark_t.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fremark_t.Location = new System.Drawing.Point(125, 524);
            this.fremark_t.MaxLength = 254;
            this.fremark_t.Multiline = true;
            this.fremark_t.Name = "fremark_t";
            this.fremark_t.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            this.fremark_t.Size = new System.Drawing.Size(597, 49);
            this.fremark_t.TabIndex = 52;
            // 
            // label36
            // 
            this.label36.AutoSize = true;
            this.label36.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label36.ForeColor = System.Drawing.Color.White;
            this.label36.Location = new System.Drawing.Point(36, 539);
            this.label36.Name = "label36";
            this.label36.Size = new System.Drawing.Size(64, 16);
            this.label36.TabIndex = 420;
            this.label36.Text = "Remark:";
            // 
            // XcmdExit
            // 
            this.XcmdExit.Location = new System.Drawing.Point(647, 578);
            this.XcmdExit.Name = "XcmdExit";
            this.XcmdExit.Size = new System.Drawing.Size(75, 23);
            this.XcmdExit.TabIndex = 421;
            this.XcmdExit.Text = "Exit(&E)";
            this.XcmdExit.UseVisualStyleBackColor = true;
            this.XcmdExit.Click += new System.EventHandler(this.XcmdExit_Click);
            // 
            // XcmdConfirm
            // 
            this.XcmdConfirm.Location = new System.Drawing.Point(611, 578);
            this.XcmdConfirm.Name = "XcmdConfirm";
            this.XcmdConfirm.Size = new System.Drawing.Size(75, 23);
            this.XcmdConfirm.TabIndex = 422;
            this.XcmdConfirm.Text = "Accept(&O)";
            this.XcmdConfirm.UseVisualStyleBackColor = true;
            this.XcmdConfirm.Visible = false;
            this.XcmdConfirm.Click += new System.EventHandler(this.XcmdConfirm_Click);
            // 
            // fadj11
            // 
            this.fadj11.AllowNegative = true;
            this.fadj11.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadj11.Enabled = false;
            this.fadj11.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadj11.Location = new System.Drawing.Point(608, 387);
            this.fadj11.Name = "fadj11";
            this.fadj11.NumericPrecision = 15;
            this.fadj11.NumericScaleOnFocus = 2;
            this.fadj11.NumericScaleOnLostFocus = 2;
            this.fadj11.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadj11.ReadOnly = true;
            this.fadj11.Size = new System.Drawing.Size(114, 22);
            this.fadj11.TabIndex = 39;
            this.fadj11.Text = "0";
            this.fadj11.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadj11.ZeroIsValid = true;
            // 
            // frvos11
            // 
            this.frvos11.AllowNegative = true;
            this.frvos11.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvos11.Enabled = false;
            this.frvos11.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvos11.Location = new System.Drawing.Point(495, 387);
            this.frvos11.Name = "frvos11";
            this.frvos11.NumericPrecision = 15;
            this.frvos11.NumericScaleOnFocus = 2;
            this.frvos11.NumericScaleOnLostFocus = 2;
            this.frvos11.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvos11.ReadOnly = true;
            this.frvos11.Size = new System.Drawing.Size(112, 22);
            this.frvos11.TabIndex = 38;
            this.frvos11.Text = "0";
            this.frvos11.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvos11.ZeroIsValid = true;
            this.frvos11.Validated += new System.EventHandler(this.frvos11_Validated);
            // 
            // fos11
            // 
            this.fos11.AllowNegative = true;
            this.fos11.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fos11.Enabled = false;
            this.fos11.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fos11.Location = new System.Drawing.Point(397, 387);
            this.fos11.Name = "fos11";
            this.fos11.NumericPrecision = 15;
            this.fos11.NumericScaleOnFocus = 2;
            this.fos11.NumericScaleOnLostFocus = 2;
            this.fos11.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fos11.ReadOnly = true;
            this.fos11.Size = new System.Drawing.Size(97, 22);
            this.fos11.TabIndex = 37;
            this.fos11.Text = "0";
            this.fos11.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fos11.ZeroIsValid = true;
            // 
            // fadj12
            // 
            this.fadj12.AllowNegative = true;
            this.fadj12.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadj12.Enabled = false;
            this.fadj12.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadj12.Location = new System.Drawing.Point(608, 409);
            this.fadj12.Name = "fadj12";
            this.fadj12.NumericPrecision = 15;
            this.fadj12.NumericScaleOnFocus = 2;
            this.fadj12.NumericScaleOnLostFocus = 2;
            this.fadj12.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadj12.ReadOnly = true;
            this.fadj12.Size = new System.Drawing.Size(114, 22);
            this.fadj12.TabIndex = 42;
            this.fadj12.Text = "0";
            this.fadj12.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadj12.ZeroIsValid = true;
            // 
            // frvos12
            // 
            this.frvos12.AllowNegative = true;
            this.frvos12.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvos12.Enabled = false;
            this.frvos12.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvos12.Location = new System.Drawing.Point(495, 409);
            this.frvos12.Name = "frvos12";
            this.frvos12.NumericPrecision = 15;
            this.frvos12.NumericScaleOnFocus = 2;
            this.frvos12.NumericScaleOnLostFocus = 2;
            this.frvos12.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvos12.ReadOnly = true;
            this.frvos12.Size = new System.Drawing.Size(112, 22);
            this.frvos12.TabIndex = 41;
            this.frvos12.Text = "0";
            this.frvos12.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvos12.ZeroIsValid = true;
            this.frvos12.Validated += new System.EventHandler(this.frvos12_Validated);
            // 
            // fos12
            // 
            this.fos12.AllowNegative = true;
            this.fos12.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fos12.Enabled = false;
            this.fos12.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fos12.Location = new System.Drawing.Point(397, 409);
            this.fos12.Name = "fos12";
            this.fos12.NumericPrecision = 15;
            this.fos12.NumericScaleOnFocus = 2;
            this.fos12.NumericScaleOnLostFocus = 2;
            this.fos12.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fos12.ReadOnly = true;
            this.fos12.Size = new System.Drawing.Size(97, 22);
            this.fos12.TabIndex = 40;
            this.fos12.Text = "0";
            this.fos12.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fos12.ZeroIsValid = true;
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Enabled = false;
            this.label27.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label27.ForeColor = System.Drawing.Color.White;
            this.label27.Location = new System.Drawing.Point(301, 343);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(96, 16);
            this.label27.TabIndex = 392;
            this.label27.Text = "Legal Costs";
            this.label27.Visible = false;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Underline);
            this.label4.ForeColor = System.Drawing.Color.White;
            this.label4.Location = new System.Drawing.Point(37, 278);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(144, 16);
            this.label4.TabIndex = 432;
            this.label4.Text = "PART A2 - Damages";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label5.ForeColor = System.Drawing.Color.White;
            this.label5.Location = new System.Drawing.Point(36, 72);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(40, 16);
            this.label5.TabIndex = 433;
            this.label5.Text = "PSLA";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label6.ForeColor = System.Drawing.Color.White;
            this.label6.Location = new System.Drawing.Point(37, 94);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(200, 16);
            this.label6.TabIndex = 434;
            this.label6.Text = "Loss of Earning + 5% MPF";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label7.ForeColor = System.Drawing.Color.White;
            this.label7.Location = new System.Drawing.Point(37, 116);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(256, 16);
            this.label7.TabIndex = 435;
            this.label7.Text = "Loss of Future Earning + 5% MPF";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label8.ForeColor = System.Drawing.Color.White;
            this.label8.Location = new System.Drawing.Point(36, 138);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(200, 16);
            this.label8.TabIndex = 436;
            this.label8.Text = "Loss of Earning Capacity";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label9.ForeColor = System.Drawing.Color.White;
            this.label9.Location = new System.Drawing.Point(36, 160);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(128, 16);
            this.label9.TabIndex = 437;
            this.label9.Text = "Special Damages";
            // 
            // fadj10
            // 
            this.fadj10.AllowNegative = true;
            this.fadj10.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadj10.Enabled = false;
            this.fadj10.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadj10.Location = new System.Drawing.Point(608, 176);
            this.fadj10.Name = "fadj10";
            this.fadj10.NumericPrecision = 15;
            this.fadj10.NumericScaleOnFocus = 2;
            this.fadj10.NumericScaleOnLostFocus = 2;
            this.fadj10.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadj10.ReadOnly = true;
            this.fadj10.Size = new System.Drawing.Size(114, 22);
            this.fadj10.TabIndex = 21;
            this.fadj10.Text = "0";
            this.fadj10.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadj10.ZeroIsValid = true;
            // 
            // frvos10
            // 
            this.frvos10.AllowNegative = true;
            this.frvos10.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvos10.Enabled = false;
            this.frvos10.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvos10.Location = new System.Drawing.Point(495, 176);
            this.frvos10.Name = "frvos10";
            this.frvos10.NumericPrecision = 15;
            this.frvos10.NumericScaleOnFocus = 2;
            this.frvos10.NumericScaleOnLostFocus = 2;
            this.frvos10.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvos10.ReadOnly = true;
            this.frvos10.Size = new System.Drawing.Size(112, 22);
            this.frvos10.TabIndex = 20;
            this.frvos10.Text = "0";
            this.frvos10.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvos10.ZeroIsValid = true;
            this.frvos10.Validated += new System.EventHandler(this.frvos10_Validated);
            // 
            // fos10
            // 
            this.fos10.AllowNegative = true;
            this.fos10.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fos10.Enabled = false;
            this.fos10.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fos10.Location = new System.Drawing.Point(397, 176);
            this.fos10.Name = "fos10";
            this.fos10.NumericPrecision = 15;
            this.fos10.NumericScaleOnFocus = 2;
            this.fos10.NumericScaleOnLostFocus = 2;
            this.fos10.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fos10.ReadOnly = true;
            this.fos10.Size = new System.Drawing.Size(97, 22);
            this.fos10.TabIndex = 19;
            this.fos10.Text = "0";
            this.fos10.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fos10.ZeroIsValid = true;
            // 
            // fadj13
            // 
            this.fadj13.AllowNegative = true;
            this.fadj13.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadj13.Enabled = false;
            this.fadj13.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadj13.Location = new System.Drawing.Point(608, 431);
            this.fadj13.Name = "fadj13";
            this.fadj13.NumericPrecision = 15;
            this.fadj13.NumericScaleOnFocus = 2;
            this.fadj13.NumericScaleOnLostFocus = 2;
            this.fadj13.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadj13.ReadOnly = true;
            this.fadj13.Size = new System.Drawing.Size(114, 22);
            this.fadj13.TabIndex = 45;
            this.fadj13.Text = "0";
            this.fadj13.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadj13.ZeroIsValid = true;
            // 
            // frvos13
            // 
            this.frvos13.AllowNegative = true;
            this.frvos13.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvos13.Enabled = false;
            this.frvos13.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvos13.Location = new System.Drawing.Point(495, 431);
            this.frvos13.Name = "frvos13";
            this.frvos13.NumericPrecision = 15;
            this.frvos13.NumericScaleOnFocus = 2;
            this.frvos13.NumericScaleOnLostFocus = 2;
            this.frvos13.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvos13.ReadOnly = true;
            this.frvos13.Size = new System.Drawing.Size(112, 22);
            this.frvos13.TabIndex = 44;
            this.frvos13.Text = "0";
            this.frvos13.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvos13.ZeroIsValid = true;
            this.frvos13.Validated += new System.EventHandler(this.frvos13_Validated);
            // 
            // fos13
            // 
            this.fos13.AllowNegative = true;
            this.fos13.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fos13.Enabled = false;
            this.fos13.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fos13.Location = new System.Drawing.Point(397, 431);
            this.fos13.Name = "fos13";
            this.fos13.NumericPrecision = 15;
            this.fos13.NumericScaleOnFocus = 2;
            this.fos13.NumericScaleOnLostFocus = 2;
            this.fos13.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fos13.ReadOnly = true;
            this.fos13.Size = new System.Drawing.Size(97, 22);
            this.fos13.TabIndex = 43;
            this.fos13.Text = "0";
            this.fos13.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fos13.ZeroIsValid = true;
            // 
            // fadjA2
            // 
            this.fadjA2.AutoSize = true;
            this.fadjA2.Location = new System.Drawing.Point(49, 480);
            this.fadjA2.Name = "fadjA2";
            this.fadjA2.Size = new System.Drawing.Size(37, 12);
            this.fadjA2.TabIndex = 444;
            this.fadjA2.Text = "fadjA2";
            this.fadjA2.Visible = false;
            // 
            // fadjB
            // 
            this.fadjB.AutoSize = true;
            this.fadjB.Location = new System.Drawing.Point(123, 480);
            this.fadjB.Name = "fadjB";
            this.fadjB.Size = new System.Drawing.Size(31, 12);
            this.fadjB.TabIndex = 445;
            this.fadjB.Text = "fadjB";
            this.fadjB.Visible = false;
            // 
            // frvosA2
            // 
            this.frvosA2.AutoSize = true;
            this.frvosA2.Location = new System.Drawing.Point(187, 480);
            this.frvosA2.Name = "frvosA2";
            this.frvosA2.Size = new System.Drawing.Size(43, 12);
            this.frvosA2.TabIndex = 446;
            this.frvosA2.Text = "frvosA2";
            this.frvosA2.Visible = false;
            // 
            // frvosB
            // 
            this.frvosB.AutoSize = true;
            this.frvosB.Location = new System.Drawing.Point(246, 480);
            this.frvosB.Name = "frvosB";
            this.frvosB.Size = new System.Drawing.Size(37, 12);
            this.frvosB.TabIndex = 447;
            this.frvosB.Text = "frvosB";
            this.frvosB.Visible = false;
            // 
            // ftadj13
            // 
            this.ftadj13.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadj13.Enabled = false;
            this.ftadj13.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadj13.Location = new System.Drawing.Point(630, 431);
            this.ftadj13.Name = "ftadj13";
            this.ftadj13.ReadOnly = true;
            this.ftadj13.Size = new System.Drawing.Size(114, 22);
            this.ftadj13.TabIndex = 463;
            this.ftadj13.Visible = false;
            // 
            // ftadj10
            // 
            this.ftadj10.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadj10.Enabled = false;
            this.ftadj10.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadj10.Location = new System.Drawing.Point(630, 176);
            this.ftadj10.Name = "ftadj10";
            this.ftadj10.ReadOnly = true;
            this.ftadj10.Size = new System.Drawing.Size(114, 22);
            this.ftadj10.TabIndex = 462;
            this.ftadj10.Visible = false;
            // 
            // ftadj12
            // 
            this.ftadj12.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadj12.Enabled = false;
            this.ftadj12.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadj12.Location = new System.Drawing.Point(630, 409);
            this.ftadj12.Name = "ftadj12";
            this.ftadj12.ReadOnly = true;
            this.ftadj12.Size = new System.Drawing.Size(114, 22);
            this.ftadj12.TabIndex = 461;
            this.ftadj12.Visible = false;
            // 
            // ftadj11
            // 
            this.ftadj11.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadj11.Enabled = false;
            this.ftadj11.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadj11.Location = new System.Drawing.Point(630, 387);
            this.ftadj11.Name = "ftadj11";
            this.ftadj11.ReadOnly = true;
            this.ftadj11.Size = new System.Drawing.Size(114, 22);
            this.ftadj11.TabIndex = 460;
            this.ftadj11.Visible = false;
            // 
            // ftadj
            // 
            this.ftadj.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadj.Enabled = false;
            this.ftadj.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadj.Location = new System.Drawing.Point(630, 499);
            this.ftadj.Name = "ftadj";
            this.ftadj.ReadOnly = true;
            this.ftadj.Size = new System.Drawing.Size(114, 22);
            this.ftadj.TabIndex = 459;
            this.ftadj.Visible = false;
            // 
            // ftadjC
            // 
            this.ftadjC.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadjC.Enabled = false;
            this.ftadjC.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadjC.Location = new System.Drawing.Point(630, 453);
            this.ftadjC.Name = "ftadjC";
            this.ftadjC.ReadOnly = true;
            this.ftadjC.Size = new System.Drawing.Size(114, 22);
            this.ftadjC.TabIndex = 458;
            this.ftadjC.Visible = false;
            // 
            // ftadj09
            // 
            this.ftadj09.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadj09.Enabled = false;
            this.ftadj09.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadj09.Location = new System.Drawing.Point(630, 339);
            this.ftadj09.Name = "ftadj09";
            this.ftadj09.ReadOnly = true;
            this.ftadj09.Size = new System.Drawing.Size(114, 22);
            this.ftadj09.TabIndex = 457;
            this.ftadj09.Visible = false;
            // 
            // ftadj08
            // 
            this.ftadj08.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadj08.Enabled = false;
            this.ftadj08.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadj08.Location = new System.Drawing.Point(630, 293);
            this.ftadj08.Name = "ftadj08";
            this.ftadj08.ReadOnly = true;
            this.ftadj08.Size = new System.Drawing.Size(114, 22);
            this.ftadj08.TabIndex = 456;
            this.ftadj08.Visible = false;
            // 
            // ftadj07
            // 
            this.ftadj07.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadj07.Enabled = false;
            this.ftadj07.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadj07.Location = new System.Drawing.Point(630, 220);
            this.ftadj07.Name = "ftadj07";
            this.ftadj07.ReadOnly = true;
            this.ftadj07.Size = new System.Drawing.Size(114, 22);
            this.ftadj07.TabIndex = 455;
            this.ftadj07.Visible = false;
            // 
            // ftadjA
            // 
            this.ftadjA.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadjA.Enabled = false;
            this.ftadjA.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadjA.Location = new System.Drawing.Point(630, 242);
            this.ftadjA.Name = "ftadjA";
            this.ftadjA.ReadOnly = true;
            this.ftadjA.Size = new System.Drawing.Size(114, 22);
            this.ftadjA.TabIndex = 454;
            this.ftadjA.Visible = false;
            // 
            // ftadj06
            // 
            this.ftadj06.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadj06.Enabled = false;
            this.ftadj06.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadj06.Location = new System.Drawing.Point(630, 198);
            this.ftadj06.Name = "ftadj06";
            this.ftadj06.ReadOnly = true;
            this.ftadj06.Size = new System.Drawing.Size(114, 22);
            this.ftadj06.TabIndex = 453;
            this.ftadj06.Visible = false;
            // 
            // ftadj05
            // 
            this.ftadj05.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadj05.Enabled = false;
            this.ftadj05.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadj05.Location = new System.Drawing.Point(630, 154);
            this.ftadj05.Name = "ftadj05";
            this.ftadj05.ReadOnly = true;
            this.ftadj05.Size = new System.Drawing.Size(114, 22);
            this.ftadj05.TabIndex = 452;
            this.ftadj05.Visible = false;
            // 
            // ftadj04
            // 
            this.ftadj04.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadj04.Enabled = false;
            this.ftadj04.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadj04.Location = new System.Drawing.Point(630, 132);
            this.ftadj04.Name = "ftadj04";
            this.ftadj04.ReadOnly = true;
            this.ftadj04.Size = new System.Drawing.Size(114, 22);
            this.ftadj04.TabIndex = 451;
            this.ftadj04.Visible = false;
            // 
            // ftadj03
            // 
            this.ftadj03.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadj03.Enabled = false;
            this.ftadj03.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadj03.Location = new System.Drawing.Point(630, 110);
            this.ftadj03.Name = "ftadj03";
            this.ftadj03.ReadOnly = true;
            this.ftadj03.Size = new System.Drawing.Size(114, 22);
            this.ftadj03.TabIndex = 450;
            this.ftadj03.Visible = false;
            // 
            // ftadj02
            // 
            this.ftadj02.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadj02.Enabled = false;
            this.ftadj02.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadj02.Location = new System.Drawing.Point(630, 88);
            this.ftadj02.Name = "ftadj02";
            this.ftadj02.ReadOnly = true;
            this.ftadj02.Size = new System.Drawing.Size(114, 22);
            this.ftadj02.TabIndex = 449;
            this.ftadj02.Visible = false;
            // 
            // ftadj01
            // 
            this.ftadj01.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadj01.Enabled = false;
            this.ftadj01.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadj01.Location = new System.Drawing.Point(630, 66);
            this.ftadj01.Name = "ftadj01";
            this.ftadj01.ReadOnly = true;
            this.ftadj01.Size = new System.Drawing.Size(114, 22);
            this.ftadj01.TabIndex = 448;
            this.ftadj01.Visible = false;
            // 
            // ftadjB
            // 
            this.ftadjB.AutoSize = true;
            this.ftadjB.Location = new System.Drawing.Point(124, 505);
            this.ftadjB.Name = "ftadjB";
            this.ftadjB.Size = new System.Drawing.Size(34, 12);
            this.ftadjB.TabIndex = 465;
            this.ftadjB.Text = "ftadjB";
            this.ftadjB.Visible = false;
            // 
            // ftadjA2
            // 
            this.ftadjA2.AutoSize = true;
            this.ftadjA2.Location = new System.Drawing.Point(50, 505);
            this.ftadjA2.Name = "ftadjA2";
            this.ftadjA2.Size = new System.Drawing.Size(40, 12);
            this.ftadjA2.TabIndex = 464;
            this.ftadjA2.Text = "ftadjA2";
            this.ftadjA2.Visible = false;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label10.ForeColor = System.Drawing.Color.White;
            this.label10.Location = new System.Drawing.Point(317, 459);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(80, 16);
            this.label10.TabIndex = 466;
            this.label10.Text = "Sub Total";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label14.ForeColor = System.Drawing.Color.White;
            this.label14.Location = new System.Drawing.Point(317, 505);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(48, 16);
            this.label14.TabIndex = 467;
            this.label14.Text = "Total";
            // 
            // fosB
            // 
            this.fosB.AutoSize = true;
            this.fosB.Location = new System.Drawing.Point(246, 505);
            this.fosB.Name = "fosB";
            this.fosB.Size = new System.Drawing.Size(27, 12);
            this.fosB.TabIndex = 469;
            this.fosB.Text = "fosB";
            this.fosB.Visible = false;
            // 
            // fosA2
            // 
            this.fosA2.AutoSize = true;
            this.fosA2.Location = new System.Drawing.Point(187, 505);
            this.fosA2.Name = "fosA2";
            this.fosA2.Size = new System.Drawing.Size(33, 12);
            this.fosA2.TabIndex = 468;
            this.fosA2.Text = "fosA2";
            this.fosA2.Visible = false;
            // 
            // XAutoLbl
            // 
            this.XAutoLbl.AutoSize = true;
            this.XAutoLbl.BackColor = System.Drawing.Color.Red;
            this.XAutoLbl.ForeColor = System.Drawing.Color.White;
            this.XAutoLbl.Location = new System.Drawing.Point(500, 22);
            this.XAutoLbl.Name = "XAutoLbl";
            this.XAutoLbl.Size = new System.Drawing.Size(61, 12);
            this.XAutoLbl.TabIndex = 470;
            this.XAutoLbl.Text = "Auto Adjust";
            this.XAutoLbl.Visible = false;
            // 
            // bdwn_cl
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.SteelBlue;
            this.ClientSize = new System.Drawing.Size(768, 611);
            this.Controls.Add(this.XAutoLbl);
            this.Controls.Add(this.fosB);
            this.Controls.Add(this.fosA2);
            this.Controls.Add(this.label14);
            this.Controls.Add(this.label10);
            this.Controls.Add(this.ftadjB);
            this.Controls.Add(this.ftadjA2);
            this.Controls.Add(this.ftadj13);
            this.Controls.Add(this.ftadj10);
            this.Controls.Add(this.ftadj12);
            this.Controls.Add(this.ftadj11);
            this.Controls.Add(this.ftadj);
            this.Controls.Add(this.ftadjC);
            this.Controls.Add(this.ftadj09);
            this.Controls.Add(this.ftadj08);
            this.Controls.Add(this.ftadj07);
            this.Controls.Add(this.ftadjA);
            this.Controls.Add(this.ftadj06);
            this.Controls.Add(this.ftadj05);
            this.Controls.Add(this.ftadj04);
            this.Controls.Add(this.ftadj03);
            this.Controls.Add(this.ftadj02);
            this.Controls.Add(this.ftadj01);
            this.Controls.Add(this.frvosB);
            this.Controls.Add(this.frvosA2);
            this.Controls.Add(this.fadjB);
            this.Controls.Add(this.fadjA2);
            this.Controls.Add(this.fadj13);
            this.Controls.Add(this.frvos13);
            this.Controls.Add(this.fos13);
            this.Controls.Add(this.fadj10);
            this.Controls.Add(this.frvos10);
            this.Controls.Add(this.fos10);
            this.Controls.Add(this.label9);
            this.Controls.Add(this.label8);
            this.Controls.Add(this.label7);
            this.Controls.Add(this.label6);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.fadj12);
            this.Controls.Add(this.frvos12);
            this.Controls.Add(this.fos12);
            this.Controls.Add(this.fadj11);
            this.Controls.Add(this.frvos11);
            this.Controls.Add(this.fos11);
            this.Controls.Add(this.XcmdConfirm);
            this.Controls.Add(this.XcmdExit);
            this.Controls.Add(this.label36);
            this.Controls.Add(this.fremark_t);
            this.Controls.Add(this.fadj);
            this.Controls.Add(this.frvos);
            this.Controls.Add(this.fos);
            this.Controls.Add(this.fadjC);
            this.Controls.Add(this.frvosC);
            this.Controls.Add(this.fosC);
            this.Controls.Add(this.label33);
            this.Controls.Add(this.label32);
            this.Controls.Add(this.label31);
            this.Controls.Add(this.fadj09);
            this.Controls.Add(this.frvos09);
            this.Controls.Add(this.fos09);
            this.Controls.Add(this.fadj08);
            this.Controls.Add(this.frvos08);
            this.Controls.Add(this.fos08);
            this.Controls.Add(this.label30);
            this.Controls.Add(this.label28);
            this.Controls.Add(this.label27);
            this.Controls.Add(this.fadj07);
            this.Controls.Add(this.frvos07);
            this.Controls.Add(this.fos07);
            this.Controls.Add(this.label26);
            this.Controls.Add(this.label25);
            this.Controls.Add(this.label24);
            this.Controls.Add(this.label23);
            this.Controls.Add(this.label22);
            this.Controls.Add(this.fadjA);
            this.Controls.Add(this.frvosA);
            this.Controls.Add(this.fosA);
            this.Controls.Add(this.fadj06);
            this.Controls.Add(this.frvos06);
            this.Controls.Add(this.fos06);
            this.Controls.Add(this.fadj05);
            this.Controls.Add(this.frvos05);
            this.Controls.Add(this.fos05);
            this.Controls.Add(this.fadj04);
            this.Controls.Add(this.frvos04);
            this.Controls.Add(this.fos04);
            this.Controls.Add(this.fadj03);
            this.Controls.Add(this.frvos03);
            this.Controls.Add(this.fos03);
            this.Controls.Add(this.fadj02);
            this.Controls.Add(this.frvos02);
            this.Controls.Add(this.fos02);
            this.Controls.Add(this.fadj01);
            this.Controls.Add(this.frvos01);
            this.Controls.Add(this.fos01);
            this.Controls.Add(this.label13);
            this.Controls.Add(this.label12);
            this.Controls.Add(this.label11);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.faccdate);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.flogseq);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.fclmno);
            this.Controls.Add(this.label66);
            this.Name = "bdwn_cl";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Reserve Breakdown - Employee\'s Compensation";
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.TextBox fclmno;
        private System.Windows.Forms.Label label66;
        private System.Windows.Forms.TextBox flogseq;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox faccdate;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label13;
        private Shorty.Windows.Forms.NumericTextBox fos01;
        private Shorty.Windows.Forms.NumericTextBox frvos01;
        private Shorty.Windows.Forms.NumericTextBox fadj01;
        private Shorty.Windows.Forms.NumericTextBox fadj02;
        private Shorty.Windows.Forms.NumericTextBox frvos02;
        private Shorty.Windows.Forms.NumericTextBox fos02;
        private Shorty.Windows.Forms.NumericTextBox fadj03;
        private Shorty.Windows.Forms.NumericTextBox frvos03;
        private Shorty.Windows.Forms.NumericTextBox fos03;
        private Shorty.Windows.Forms.NumericTextBox fadj04;
        private Shorty.Windows.Forms.NumericTextBox frvos04;
        private Shorty.Windows.Forms.NumericTextBox fos04;
        private Shorty.Windows.Forms.NumericTextBox fadj05;
        private Shorty.Windows.Forms.NumericTextBox frvos05;
        private Shorty.Windows.Forms.NumericTextBox fos05;
        private Shorty.Windows.Forms.NumericTextBox fadj06;
        private Shorty.Windows.Forms.NumericTextBox frvos06;
        private Shorty.Windows.Forms.NumericTextBox fos06;
        private Shorty.Windows.Forms.NumericTextBox fadjA;
        private Shorty.Windows.Forms.NumericTextBox frvosA;
        private Shorty.Windows.Forms.NumericTextBox fosA;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.Label label26;
        private Shorty.Windows.Forms.NumericTextBox fadj07;
        private Shorty.Windows.Forms.NumericTextBox frvos07;
        private Shorty.Windows.Forms.NumericTextBox fos07;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.Label label30;
        private Shorty.Windows.Forms.NumericTextBox fadj08;
        private Shorty.Windows.Forms.NumericTextBox frvos08;
        private Shorty.Windows.Forms.NumericTextBox fos08;
        private Shorty.Windows.Forms.NumericTextBox fadj09;
        private Shorty.Windows.Forms.NumericTextBox frvos09;
        private Shorty.Windows.Forms.NumericTextBox fos09;
        private System.Windows.Forms.Label label31;
        private System.Windows.Forms.Label label32;
        private System.Windows.Forms.Label label33;
        private Shorty.Windows.Forms.NumericTextBox fadjC;
        private Shorty.Windows.Forms.NumericTextBox frvosC;
        private Shorty.Windows.Forms.NumericTextBox fosC;
        private Shorty.Windows.Forms.NumericTextBox fadj;
        private Shorty.Windows.Forms.NumericTextBox frvos;
        private Shorty.Windows.Forms.NumericTextBox fos;
        private System.Windows.Forms.TextBox fremark_t;
        private System.Windows.Forms.Label label36;
        private System.Windows.Forms.Button XcmdExit;
        private System.Windows.Forms.Button XcmdConfirm;
        private Shorty.Windows.Forms.NumericTextBox fadj11;
        private Shorty.Windows.Forms.NumericTextBox frvos11;
        private Shorty.Windows.Forms.NumericTextBox fos11;
        private Shorty.Windows.Forms.NumericTextBox fadj12;
        private Shorty.Windows.Forms.NumericTextBox frvos12;
        private Shorty.Windows.Forms.NumericTextBox fos12;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label9;
        private Shorty.Windows.Forms.NumericTextBox fadj10;
        private Shorty.Windows.Forms.NumericTextBox frvos10;
        private Shorty.Windows.Forms.NumericTextBox fos10;
        private Shorty.Windows.Forms.NumericTextBox fadj13;
        private Shorty.Windows.Forms.NumericTextBox frvos13;
        private Shorty.Windows.Forms.NumericTextBox fos13;
        private System.Windows.Forms.Label fadjA2;
        private System.Windows.Forms.Label fadjB;
        private System.Windows.Forms.Label frvosA2;
        private System.Windows.Forms.Label frvosB;
        private System.Windows.Forms.TextBox ftadj13;
        private System.Windows.Forms.TextBox ftadj10;
        private System.Windows.Forms.TextBox ftadj12;
        private System.Windows.Forms.TextBox ftadj11;
        private System.Windows.Forms.TextBox ftadj;
        private System.Windows.Forms.TextBox ftadjC;
        private System.Windows.Forms.TextBox ftadj09;
        private System.Windows.Forms.TextBox ftadj08;
        private System.Windows.Forms.TextBox ftadj07;
        private System.Windows.Forms.TextBox ftadjA;
        private System.Windows.Forms.TextBox ftadj06;
        private System.Windows.Forms.TextBox ftadj05;
        private System.Windows.Forms.TextBox ftadj04;
        private System.Windows.Forms.TextBox ftadj03;
        private System.Windows.Forms.TextBox ftadj02;
        private System.Windows.Forms.TextBox ftadj01;
        private System.Windows.Forms.Label ftadjB;
        private System.Windows.Forms.Label ftadjA2;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label fosB;
        private System.Windows.Forms.Label fosA2;
        private System.Windows.Forms.Label XAutoLbl;

    }
}