using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;

namespace INS
{
    public partial class frmset : Form
    {
        public frmset()
        {
            InitializeComponent();
            FillData("", "");
        }

        public frmset(Forms.selstat selstat)
        {
            // TODO: Complete member initialization
            this.selstat = selstat;
            InitializeComponent();
            FillData("", "");
        }

        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string lc_fdbtrtype = "%", lc_fdbtr = "", lc_fdbrdesc = "", lc_fstatid = "%", fstatno="";
        private Forms.selstat selstat;

        public void FillData(string order, string query)
        {
            string sql ="select frefno as [Statement#],frefdate as [Date],fdbtr as [A/C#], "+
                        "fstatid as [Type],CONVERT(varchar(10),finstall)+'/'+CONVERT(varchar(10),ftotinstal) as [Install#], "+
                        "fbal_g as [Gross Bal],fbal_c as [Comm Bal],fbal_s as [Sus Bal],fbalance as [Balance],fctlid "+
                        "from ostat where fstatus <> 4 ";
            if (lc_fdbtrtype == "M"  && lc_fdbrdesc == "" && lc_fstatid == "%")
            {sql = sql + "and fdbtrtype ='' ";}
            else if (lc_fdbtrtype == "M"  && lc_fdbrdesc != "" && lc_fstatid == "%")
            {sql = sql + "and fdbtrtype ='' and rtrim(fdbrdesc) = '"+ lc_fdbrdesc +"'";}
            else if (lc_fdbtrtype == "M"  && lc_fdbrdesc == "")
            {sql = sql + "and fdbtrtype ='' and fstatid = '"+ lc_fstatid +"'" ;}
            else if (lc_fdbtrtype == "M")
            {sql = sql + "and fdbtrtype ='' and rtrim(fdbrdesc) = '"+ lc_fdbrdesc +"' and fstatid = '"+ lc_fstatid +"'" ;}
            else if (lc_fdbtrtype == "%" && lc_fstatid == "%")
            {sql = sql + "and fstatus <> 4 ";}
            else if (lc_fstatid == "%" && lc_fdbtr == "")
            {sql = sql + "and fdbtrtype ='"+lc_fdbtrtype+"'";}
            else if (lc_fstatid == "%" && lc_fdbtr != "")
            {sql = sql + "and fdbtrtype ='"+lc_fdbtrtype+"' and fdbtr = '"+ lc_fdbtr +"'";}
            else if (lc_fdbtrtype == "%")
            {sql = sql + "and fstatid = '"+ lc_fstatid +"'" ;}
            else {sql = sql + "and fdbtrtype ='"+lc_fdbtrtype+"' and fdbtr = '"+ lc_fdbtr +"' and fstatid ='"+ lc_fstatid +"'";}

            dataGridView1.DataSource = DBHelper.GetDataSet(sql);
            this.dataGridView1.Columns["fctlid"].Visible = false;
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }

                if (dataGridView1.Rows[row].Cells["fctlid"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["fctlid"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["fctlid"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            selstat.fctlid = label3.Text;
            selstat.filldata(label3.Text);
            this.Close();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }

    }
}
