using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;

namespace INS
{
    public partial class frmOccSearch : Form
    {
        public frmOccSearch()
        {
            InitializeComponent();
            FillData("", "");
        }

        public frmOccSearch(Ctrl.DirectInsured_CGL directInsured_CGL)
        {
            // TODO: Complete member initialization
            this.directInsured_CGL = directInsured_CGL;
            InitializeComponent();
        }

        public frmOccSearch(Ctrl.DirectInsured_EEC directInsured_EEC)
        {
            // TODO: Complete member initialization
            this.directInsured_EEC = directInsured_EEC;
            InitializeComponent();
        }

        public frmOccSearch(Ctrl.DirectInsured_PAR directInsured_PAR)
        {
            // TODO: Complete member initialization
            this.directInsured_PAR = directInsured_PAR;
            InitializeComponent();
        }

        public frmOccSearch(Ctrl.RIInsured_PAR rIInsured_PAR)
        {
            // TODO: Complete member initialization
            this.rIInsured_PAR = rIInsured_PAR;
            InitializeComponent();
        }

        public frmOccSearch(Ctrl.RIInsured_CGL rIInsured_CGL)
        {
            // TODO: Complete member initialization
            this.rIInsured_CGL = rIInsured_CGL;
            InitializeComponent();
        }

        public frmOccSearch(Ctrl.RIInsured_EEC rIInsured_EEC)
        {
            // TODO: Complete member initialization
            this.rIInsured_EEC = rIInsured_EEC;
            InitializeComponent();
        }

        public frmOccSearch(Ctrl.EndorDirectInsured_EEC endorDirectInsured_EEC)
        {
            // TODO: Complete member initialization
            this.endorDirectInsured_EEC = endorDirectInsured_EEC;
            InitializeComponent();
        }

        public frmOccSearch(Ctrl.EndorDirectInsured_PAR endorDirectInsured_PAR)
        {
            // TODO: Complete member initialization
            this.endorDirectInsured_PAR = endorDirectInsured_PAR;
            InitializeComponent();
        }

        public frmOccSearch(Ctrl.EndorDirectInsured_CGL endorDirectInsured_CGL)
        {
            // TODO: Complete member initialization
            this.endorDirectInsured_CGL = endorDirectInsured_CGL;
            InitializeComponent();
        }

        public frmOccSearch(Ctrl.EndorRIInsured_EEC endorRIInsured_EEC)
        {
            // TODO: Complete member initialization
            this.endorRIInsured_EEC = endorRIInsured_EEC;
            InitializeComponent();
        }

        public frmOccSearch(Ctrl.EndorRIInsured_CGL endorRIInsured_CGL)
        {
            // TODO: Complete member initialization
            this.endorRIInsured_CGL = endorRIInsured_CGL;
            InitializeComponent();
        }

        public frmOccSearch(Ctrl.EndorRIInsured_PAR endorRIInsured_PAR)
        {
            // TODO: Complete member initialization
            this.endorRIInsured_PAR = endorRIInsured_PAR;
            InitializeComponent();
        }


        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "";
        private Ctrl.DirectInsured_CGL directInsured_CGL;
        private Ctrl.DirectInsured_EEC directInsured_EEC;
        private Ctrl.DirectInsured_PAR directInsured_PAR;
        private Ctrl.RIInsured_PAR rIInsured_PAR;
        private Ctrl.RIInsured_CGL rIInsured_CGL;
        private Ctrl.RIInsured_EEC rIInsured_EEC;
        private Ctrl.EndorDirectInsured_EEC endorDirectInsured_EEC;
        private Ctrl.EndorDirectInsured_PAR endorDirectInsured_PAR;
        private Ctrl.EndorDirectInsured_CGL endorDirectInsured_CGL;
        private Ctrl.EndorRIInsured_EEC endorRIInsured_EEC;
        private Ctrl.EndorRIInsured_CGL endorRIInsured_CGL;
        private Ctrl.EndorRIInsured_PAR endorRIInsured_PAR;

        public void setControlFocus(string ctrlName)
        {
            Controls["panel1"].Controls[ctrlName].Focus();
        }

        public void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            if (flag == "EEC" || flag == "EndorDirectInsured_EEC" || flag == "riEEC")
            {
                if (order == "" && query == "")
                {
                    a = new SqlDataAdapter("select fid as Code#, fdesc as Desc# from msparam where fctlid_1 = '0000000002' order by fid", c);
                }
                else if (order != "")
                {
                    a = new SqlDataAdapter("select fid as Code#, fdesc as Desc# from msparam where fctlid_1 = '0000000002' ORDER BY '" + order + "'", c);
                }
                else if (query != "")
                {
                    a = new SqlDataAdapter("select fid as Code#, fdesc as Desc# from msparam where fctlid_1 = '0000000002' and fdesc = '" + query + "'", c);
                }
                else
                {
                    a = new SqlDataAdapter("select fid as Code#, fdesc as Desc# from msparam where fctlid_1 = '0000000002' and fdesc = '" + query + "' order by fid", c);
                }
            }
            else
            {
                if (order == "" && query == "")
                {
                    a = new SqlDataAdapter("select fid as Code#, fdesc as Desc# from msparam where fctlid_1 = '0000000006' order by fid", c);
                }
                else if (order != "")
                {
                    a = new SqlDataAdapter("select fid as Code#, fdesc as Desc# from msparam where fctlid_1 = '0000000006' ORDER BY '" + order + "'", c);
                }
                else if (query != "")
                {
                    a = new SqlDataAdapter("select fid as Code#, fdesc as Desc# from msparam where fctlid_1 = '0000000006' and fdesc = '" + query + "'", c);
                }
                else
                {
                    a = new SqlDataAdapter("select fid as Code#, fdesc as Desc# from msparam where fctlid_1 = '0000000006' and fdesc = '" + query + "' order by fid", c);
                }
            }
            DataTable t = new DataTable();
            a.Fill(t);
            dataGridView1.DataSource = t;
            c.Close();
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }
                if (dataGridView1.Rows[row].Cells["Code#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Code#"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["Code#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["Desc#"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            if (flag == "CGL")
            {
                directInsured_CGL.OccValue = Fct.stFat(textBox1.Text.ToString());
                directInsured_CGL.OccDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riCGL")
            {
                rIInsured_CGL.OccValue = Fct.stFat(textBox1.Text.ToString());
                rIInsured_CGL.OccDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }

            if (flag == "EEC")
            {
                directInsured_EEC.OccValue = Fct.stFat(textBox1.Text.ToString());
                directInsured_EEC.OccDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riEEC")
            {
                string result2 = rIInsured_EEC.CheckDuplicate(textBox1.Text.ToString(), "Click");
                if (result2 != "")
                {
                    MessageBox.Show(result2);
                }
                else
                {
                    rIInsured_EEC.OccValue = Fct.stFat(textBox1.Text.ToString());
                    rIInsured_EEC.OccDesc = Fct.stFat(label3.Text.ToString());
                    this.Close();
                }
            }
            if (flag == "PAR")
            {
                directInsured_PAR.OccValue = Fct.stFat(textBox1.Text.ToString());
                directInsured_PAR.OccDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riPAR")
            {
                rIInsured_PAR.OccValue = Fct.stFat(textBox1.Text.ToString());
                rIInsured_PAR.OccDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EndorDirectInsured_EEC")
            {
                string result2 = endorDirectInsured_EEC.CheckDuplicate(textBox1.Text.ToString(), "Click");
                if (result2 != "")
                {
                    MessageBox.Show(result2);
                }
                else
                {
                    endorDirectInsured_EEC.OccValue = Fct.stFat(textBox1.Text.ToString());
                    endorDirectInsured_EEC.OccDesc = Fct.stFat(label3.Text.ToString());
                    this.Close();
                }
            }
            if (flag == "EndorRIInsured_EEC")
            {
                string result2 = endorRIInsured_EEC.CheckDuplicate(textBox1.Text.ToString(), "Click");
                if (result2 != "")
                {
                    MessageBox.Show(result2);
                }
                else
                {
                    endorRIInsured_EEC.OccValue = Fct.stFat(textBox1.Text.ToString());
                    endorRIInsured_EEC.OccDesc = Fct.stFat(label3.Text.ToString());
                    this.Close();
                }
            }
            if (flag == "EndorDirectInsured_PAR")
            {
                endorDirectInsured_PAR.OccValue = Fct.stFat(textBox1.Text.ToString());
                endorDirectInsured_PAR.OccDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EndorRIInsured_PAR")
            {
                endorRIInsured_PAR.OccValue =Fct.stFat( textBox1.Text.ToString());
                endorRIInsured_PAR.OccDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EndorDirectInsured_CGL")
            {
                string result2 = endorDirectInsured_CGL.CheckDuplicate(textBox1.Text.ToString(), "Click");
                if (result2 != "")
                {
                    MessageBox.Show(result2);
                }
                else
                {
                    endorDirectInsured_CGL.OccValue = Fct.stFat(textBox1.Text.ToString());
                    endorDirectInsured_CGL.OccDesc =Fct.stFat( label3.Text.ToString());
                    this.Close();
                }
            }
            if (flag == "RIEndorInsured_CGL")
            {
                endorRIInsured_CGL.OccValue = Fct.stFat(textBox1.Text.ToString());
                endorRIInsured_CGL.OccDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }

    }
}
