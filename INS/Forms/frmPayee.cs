using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;

namespace INS
{
    public partial class frmPayee : Form
    {
        public frmPayee()
        {
            InitializeComponent();
            FillData("", "");
        }

        public frmPayee(Ctrl.Claim.EciPAY eciPAY)
        {
            // TODO: Complete member initialization
            this.eciPAY = eciPAY;
            InitializeComponent();
            FillData("", "");
        }

        public frmPayee(Ctrl.Claim.PAY pAY)
        {
            // TODO: Complete member initialization
            this.pAY = pAY;
            InitializeComponent();
            FillData("", "");
        }

        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "", fclmno ="";
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        private Ctrl.Claim.PAY pAY;
        private Ctrl.Claim.EciPAY eciPAY;

        public void FillData(string order, string query)
        {
            string sql = "SELECT distinct fempyee as fpayee FROM oceci where fclmno ='" + fclmno + "' and rtrim(fempyee)<>'' " +
                        "union select finsd as fpayee from oclaim where fclmno ='" + fclmno + "' and rtrim(finsd)<>'' " +
                        "union select g.fdesc as fpayee from oclaim a  " +
                        "left join (select fid,CASE WHEN fdesc ='Charles Taylor Adjusting' THEN 'Charles Taylor Holdings B.V.' ELSE fdesc END AS fdesc from " + Dbm + "mprdr where ftype='L') g on a.fadjr1=g.fid where fclmno ='" + fclmno + "' and rtrim(g.fdesc)<>''" +
                        "union select h.fdesc as fpayee from oclaim a  " +
                        "left join (select fid,CASE WHEN fdesc ='Charles Taylor Adjusting' THEN 'Charles Taylor Holdings B.V.' ELSE fdesc END AS fdesc from " + Dbm + "mprdr where ftype='L') h on a.fadjr2=h.fid where fclmno ='" + fclmno + "' and rtrim(h.fdesc)<>''" +
                        "union select i.fdesc as fpayee from oclaim a  " +
                        "left join (select fid,fdesc from " + Dbm + "mprdr where ftype='S') i on a.fsolr1=i.fid where fclmno ='" + fclmno + "' and rtrim(i.fdesc)<>'' " +
                        "union select j.fdesc as fpayee from oclaim a  " +
                        "left join (select fid,fdesc from " + Dbm + "mprdr where ftype='S') j on a.fsolr2=j.fid where fclmno ='" + fclmno + "' and rtrim(j.fdesc)<>'' " +
                        "union select k.fdesc as fpayee from oclaim a  " +
                        "left join (select fid,fdesc from " + Dbm + "mprdr where ftype='H') k on a.fhlhc=k.fid where fclmno ='" + fclmno + "' and rtrim(k.fdesc)<>'' " +
                        "union select 'Hospital Authority' as fpayee ";
            DataTable dt = DBHelper.GetDataSet(sql);
            dataGridView1.DataSource = dt;
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }
                if (dataGridView1.Rows[row].Cells["fpayee"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["fpayee"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["fpayee"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            
            if (flag == "PAY")
            {
                pAY.PayeeValue = Fct.stFat(textBox1.Text.ToString());
                this.Close();
            }
            if (flag == "EciPAY")
            {
                eciPAY.PayeeValue = Fct.stFat(textBox1.Text.ToString());
                this.Close();
            }
           
        }


        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }


    }
}
