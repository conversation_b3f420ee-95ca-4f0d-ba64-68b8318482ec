using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;
using System.Configuration;
using System.Collections;

namespace INS
{
    public partial class frmClientSearch : Form
    {
        public frmClientSearch()
        {
            InitializeComponent();
            FillData("", "");
        }

        public frmClientSearch(Ctrl.DirectGen directGen)
        {
            // TODO: Complete member initialization
            this.directGen = directGen;
            InitializeComponent();
        }

        public frmClientSearch(frmCoverNote frmCoverNote)
        {
            // TODO: Complete member initialization
            this.frmCoverNote = frmCoverNote;
            InitializeComponent();
        }

        public frmClientSearch(Ctrl.RIGen rIGen)
        {
            // TODO: Complete member initialization
            this.rIGen = rIGen;
            InitializeComponent();
        }

        public frmClientSearch(Ctrl.EndorDirectGen endorDirectGen)
        {
            // TODO: Complete member initialization
            this.endorDirectGen = endorDirectGen;
            InitializeComponent();
        }

        public frmClientSearch(Ctrl.Claim.REG rEG)
        {
            // TODO: Complete member initialization
            this.rEG = rEG;
            InitializeComponent();
        }

        public frmClientSearch(Ctrl.Claim.riREG riREG)
        {
            // TODO: Complete member initialization
            this.riREG = riREG;
            InitializeComponent();
        }

        public frmClientSearch(Ctrl.Claim.EciREG eciREG)
        {
            // TODO: Complete member initialization
            this.eciREG = eciREG;
            InitializeComponent();
        }

        public frmClientSearch(Ctrl.Claim.riEciREG riEciREG)
        {
            // TODO: Complete member initialization
            this.riEciREG = riEciREG;
            InitializeComponent();
        }

        public frmClientSearch(Ctrl.Claim.riEciAMD riEciAMD)
        {
            // TODO: Complete member initialization
            this.riEciAMD = riEciAMD;
            InitializeComponent();
        }

        public frmClientSearch(tginvh tginvh)
        {
            // TODO: Complete member initialization
            this.tginvh = tginvh;
            InitializeComponent();
        }

        public frmClientSearch(Account.ctrl.acchk acchk)
        {
            // TODO: Complete member initialization
            this.acchk = acchk;
            InitializeComponent();
        }

        public frmClientSearch(Account.ctrl.acset acset)
        {
            // TODO: Complete member initialization
            this.acset = acset;
            InitializeComponent();
        }

        public frmClientSearch(Forms.selstat selstat)
        {
            // TODO: Complete member initialization
            this.selstat = selstat;
            InitializeComponent();
        }


        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "", ftype ="";
        public int row;
        private Ctrl.DirectGen directGen;
        private Ctrl.RIGen rIGen;
        private Ctrl.EndorDirectGen endorDirectGen;
        public string db = InsEnvironment.DataBase.GetDbm();
        private Ctrl.Claim.REG rEG;
        private Ctrl.Claim.riREG riREG;
        private Ctrl.Claim.EciREG eciREG;
        private Ctrl.Claim.riEciREG riEciREG;
        private Ctrl.Claim.riEciAMD riEciAMD;
        private tginvh tginvh;
        private Account.ctrl.acchk acchk;
        private Account.ctrl.acset acset;
        private Forms.selstat selstat;
        private frmCoverNote frmCoverNote;

        public void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            if (ftype == "") { ftype = "P"; }
            if (order == "" && query == "")
            {
                a = new SqlDataAdapter("select fid as Client#,fdesc as Desc#, fadd1, fadd2, fadd3,rtrim(fadd1)+rtrim(fadd2)+rtrim(fadd3)+rtrim(fadd4) as faddr from mprdr where ftype='" + ftype + "' and fstatus =1 order by fid", c);
            }
            else if (order != "")
            {
                a = new SqlDataAdapter("select fid as Client#,fdesc as Desc#, fadd1, fadd2, fadd3,rtrim(fadd1)+rtrim(fadd2)+rtrim(fadd3)+rtrim(fadd4) as faddr from mprdr where ftype='" + ftype + "' and fstatus =1 order by '" + order + "'", c);
            }
            else if (query != "")
            {
                a = new SqlDataAdapter("select fid as Client#,fdesc as Desc#, fadd1, fadd2, fadd3,rtrim(fadd1)+rtrim(fadd2)+rtrim(fadd3)+rtrim(fadd4) as faddr from mprdr where ftype='" + ftype + "' and fstatus =1 AND fid = '" + query + "'", c);
            }
            else
            {
                a = new SqlDataAdapter("select fid as Client#,fdesc as Desc#, fadd1, fadd2, fadd3,rtrim(fadd1)+rtrim(fadd2)+rtrim(fadd3)+rtrim(fadd4) as faddr from mprdr where ftype='" + ftype + "' and fstatus =1 AND fid = '" + query + "' order by fid", c);
            }
            DataTable t = new DataTable();
            a.Fill(t);
            dataGridView1.DataSource = t;
            this.dataGridView1.Columns[2].Visible = false;
            this.dataGridView1.Columns[3].Visible = false;
            this.dataGridView1.Columns[4].Visible = false;
            c.Close();
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }
                if (dataGridView1.Rows[row].Cells["Client#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Client#"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["Client#"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        public string[] CheckValue(string fid)
        {
            //string str  = "select fid,fdesc,RTRIM(fadd1)+RTRIM(fadd2)+RTRIM(fadd3) as faddr from " + db + "mprdr where ftype='C' and fid='" + fid + "'";
            string str = "select fid,fdesc,fadd1,fadd2,fadd3,fadd4 from " + db + "mprdr where ftype='"+ftype+"' and fid='" + fid + "'";
            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["odata"].ConnectionString);
            SqlCommand cmd = new SqlCommand(str, con);
            con.Open();
            using (SqlDataReader sdr = cmd.ExecuteReader())
            {
                if (sdr.HasRows && sdr.Read())
                {
                    String faddress,fadd1,fadd2,fadd3,fadd4;

                    fadd1 = sdr["fadd1"].ToString().Trim();
                    fadd2 = sdr["fadd2"].ToString().Trim();
                    fadd3 = sdr["fadd3"].ToString().Trim();
                    fadd4 = sdr["fadd4"].ToString().Trim();

                    faddress = fadd1.Trim();
                    faddress = (faddress + (fadd2.Trim() == "" ? "" : " ") + fadd2.Trim()).Trim();
                    faddress = (faddress + (fadd3.Trim() == "" ? "" : " ") + fadd3.Trim()).Trim();
                    faddress = (faddress + (fadd4.Trim() == "" ? "" : " ") + fadd4.Trim()).Trim(); 

                    ArrayList result = new ArrayList();
                    result.Add(sdr["fdesc"].ToString().Trim());
                    result.Add(faddress.ToString().Trim());
                    sdr.Close();
                    return (string[])result.ToArray(typeof(string));
                }
                else { return null; }
            }
        }


        private void Query_Click(object sender, EventArgs e)
        {
            if (flag == "Direct")
            {
                directGen.CliValue = textBox1.Text.ToString().Trim();
                string[] result = CheckValue(textBox1.Text.ToString());
                directGen.CliDesc = result[0].Trim();
                directGen.CliAddr = result[1];
                this.Close();
            }
            if (flag == "CoverNote")
            {
                frmCoverNote.CliValue = textBox1.Text.ToString().Trim();
                string[] result = CheckValue(textBox1.Text.ToString());
                frmCoverNote.CliDesc = result[0].Trim();
                frmCoverNote.CliAddr = result[1];
                this.Close();
            }
            if (flag == "RI")
            {
                rIGen.CliValue = textBox1.Text.ToString().Trim();
                string[] result = CheckValue(textBox1.Text.ToString());
                rIGen.CliDesc = result[0].Trim();
                rIGen.CliAddr = result[1].Trim();
                this.Close();
            }
            if (flag == "Endor")
            {
                endorDirectGen.CliValue = textBox1.Text.ToString().Trim();
                string[] result = CheckValue(textBox1.Text.ToString());
                endorDirectGen.CliDesc = result[0].Trim();
                endorDirectGen.CliAddr = result[1].Trim();
                this.Close();
            }
            if (flag == "REG")
            {
                rEG.CliValue = textBox1.Text.ToString().Trim();
                string[] result = CheckValue(textBox1.Text.ToString());
                rEG.CliDesc = result[0].Trim();
                this.Close();
            }
            if (flag == "riREG")
            {
                riREG.CliValue = textBox1.Text.ToString().Trim();
                string[] result = CheckValue(textBox1.Text.ToString());
                riREG.CliDesc = result[0].Trim();
                this.Close();
            }
            if (flag == "EciREG")
            {
                eciREG.CliValue = textBox1.Text.ToString().Trim();
                string[] result = CheckValue(textBox1.Text.ToString());
                eciREG.CliDesc = result[0].Trim();
                this.Close();
            }
            if (flag == "riEciREG")
            {
                riEciREG.CliValue = textBox1.Text.ToString().Trim();
                string[] result = CheckValue(textBox1.Text.ToString());
                riEciREG.CliDesc = result[0].Trim();
                this.Close();
            }
            if (flag == "riEciAMD")
            {
                riEciAMD.CliValue = textBox1.Text.ToString().Trim();
                string[] result = CheckValue(textBox1.Text.ToString());
                riEciAMD.CliDesc = result[0].Trim();
                this.Close();
            }
            if (flag == "tginvh")
            {
                tginvh.FdbtrValue  = textBox1.Text.ToString().Trim();
                string[] result = CheckValue(textBox1.Text.ToString());
                tginvh.FdbtrDesc  = result[0].Trim();
                tginvh.Faddr = result[1].Trim();
                this.Close();
            }
            if (flag == "acchk")
            {
                acchk.FdbtrValue = textBox1.Text.ToString().Trim();
                string[] result = CheckValue(textBox1.Text.ToString());
                acchk.FdbtrDesc = result[0].Trim();
                this.Close();
            }
            if (flag == "acset")
            {
                acset.FdbtrValue = textBox1.Text.ToString().Trim();
                string[] result = CheckValue(textBox1.Text.ToString());
                acset.FdbtrDesc = result[0].Trim();
                this.Close();
            }
            if (flag == "selstat")
            {
                selstat.FdbtrValue = textBox1.Text.ToString().Trim();
                string[] result = CheckValue(textBox1.Text.ToString());
                selstat.FdbtrDesc = result[0].Trim();
                this.Close();
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        

    }
}
