using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;

namespace INS
{
    public partial class frmLocSearch : Form
    {
        public frmLocSearch()
        {
            InitializeComponent();
            FillData("", "");
        }

        public frmLocSearch(Ctrl.Claim.REG rEG)
        {
            // TODO: Complete member initialization
            this.rEG = rEG;
            InitializeComponent();
        }

        public frmLocSearch(Ctrl.Claim.riREG riREG)
        {
            // TODO: Complete member initialization
            this.riREG = riREG;
            InitializeComponent();
        }

        public frmLocSearch(Ctrl.Claim.AMD aMD)
        {
            // TODO: Complete member initialization
            this.aMD = aMD;
            InitializeComponent();
        }

        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string fpolno = "", flag = "";
        private Ctrl.Claim.REG rEG;
        private Ctrl.Claim.riREG riREG;
        private Ctrl.Claim.AMD aMD;

        public void FillData(string order, string query)
        {
            string sql = "select rtrim(a.fdesc2) as Loc, max(a.fendtno) as EndtNo, a.fitem as RCD, b.fincfr, b.fincto " +
                        "from oinsloc a  "+
                        "left join polh b on a.fctlid_1= b.fctlid and b.fconfirm =3 and a.ftype ='SEC1A'  "+
                        "where b.fpolno='" + fpolno + "' " +
                        "GROUP BY a.fitem, b.fincfr, b.fincto,a.fdesc2";
            dataGridView1.DataSource = DBHelper.GetDataSet(sql);
            dataGridView1.Columns["fincfr"].Visible = false;
            dataGridView1.Columns["fincto"].Visible = false;
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }
                if (dataGridView1.Rows[row].Cells["EndtNo"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["EndtNo"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["EndtNo"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["RCD"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["RCD"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["RCD"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Loc"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Loc"].Value.ToString().Length != 0)
                    {
                        label4.Text = dataGridView1.Rows[row].Cells["Loc"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["fincfr"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["fincfr"].Value.ToString().Length != 0)
                    {
                        label5.Text = dataGridView1.Rows[row].Cells["fincfr"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["fincto"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["fincto"].Value.ToString().Length != 0)
                    {
                        label6.Text = dataGridView1.Rows[row].Cells["fincto"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            if (flag == "REG")
            {
                rEG.FitemValue = Fct.stFat(label3.Text.ToString());
                rEG.LocDesc = Fct.stFat(label4.Text.ToString());
                rEG.FincfrValue = Fct.stFat(label5.Text.ToString());
                rEG.FinctoValue = Fct.stFat(label6.Text.ToString());
                this.Close();
            }
            if (flag == "AMD")
            {
                aMD.FitemValue = Fct.stFat(label3.Text.ToString());
                aMD.LocDesc = Fct.stFat(label4.Text.ToString());
                aMD.FincfrValue = Fct.stFat(label5.Text.ToString());
                aMD.FinctoValue = Fct.stFat(label6.Text.ToString());
                this.Close();
            }
            if (flag == "riREG")
            {
                riREG.FitemValue = Fct.stFat(label3.Text.ToString());
                riREG.LocDesc = Fct.stFat(label4.Text.ToString());
                riREG.FincfrValue = Fct.stFat(label5.Text.ToString());
                riREG.FinctoValue = Fct.stFat(label6.Text.ToString());
                this.Close();
            }     
        }


        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        

    }
}
