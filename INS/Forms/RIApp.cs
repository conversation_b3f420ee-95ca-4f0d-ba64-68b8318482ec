using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using System.Collections;
using System.Configuration;
using INS.Business.Report;

namespace INS
{
    public partial class RIApp : Form
    {
        public RIApp()
        {
            InitializeComponent();
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            InitCombobox();
            InitCombobox1();
            FillData("", "", "");
        }

        public RIApp(Ctrl.Reins.FacProp facProp)
        {
            // TODO: Complete member initialization
            this.facProp = facProp;
            InitializeComponent();
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            InitCombobox();
            InitCombobox1();
            FillData("", "", "");
        }

        public RIApp(Ctrl.Reins.NonProp nonProp)
        {
            // TODO: Complete member initialization
            this.nonProp = nonProp;
            InitializeComponent();
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            InitCombobox();
            InitCombobox1();
            FillData("", "", "");
        }

        public RIApp(Ctrl.Reins.EndtFacProp endtFacProp)
        {
            // TODO: Complete member initialization
            this.endtFacProp = endtFacProp;
            InitializeComponent();
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            InitCombobox();
            InitCombobox1();
            FillData("", "", "");
        }

        public RIApp(Ctrl.Reins.FacPropPar facPropPar)
        {
            // TODO: Complete member initialization
            this.facPropPar = facPropPar;
            InitializeComponent();
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            InitCombobox();
            InitCombobox1();
            FillData("", "", "");
        }

        public RIApp(Ctrl.Reins.EndtFacPropPar endtFacPropPar)
        {
            // TODO: Complete member initialization
            this.endtFacPropPar = endtFacPropPar;
            InitializeComponent();
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            InitCombobox();
            InitCombobox1();
            FillData("", "", "");
        }


        void control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
            {
                SendKeys.Send("{TAB}");
            }
        }
        DES des = new DES();
        DBConnect operate = new DBConnect();
        ExportDBF DBF = new ExportDBF();
        XLS txt = new XLS();
        public string db = InsEnvironment.DataBase.GetDbm();
        public string fctlid_1 = "", fritype = "", fclass = "";
        public String oldrow = "", fctlid = "";
        private Ctrl.DirectInstall directInstall;
        private Ctrl.RIInstall rIInstall;
        private Ctrl.EndorInstall endorInstall;
        private Business.FrmBNotes frmBNotes;
        private Ctrl.Reins.FacProp facProp;
        private Ctrl.Reins.NonProp nonProp;
        private Ctrl.Reins.EndtFacProp endtFacProp;
        private Ctrl.Reins.FacPropPar facPropPar;
        private Ctrl.Reins.EndtFacPropPar endtFacPropPar;

        public void FillData(string order, string query, string list)
        {
            try
            {
                string sql = "select a.fpolno as [Policy No.],a.fendtno as [Endt No.],a.frino as [RI No.], a.fcession as [Cession],a.friapp as [R/I App],  " +
                            "case when a.fdbtrtype='B' then 'Broker' when a.fdbtrtype='C' then 'Client' when a.fdbtrtype='P' then 'Producer' else 'Insurer' end as [A/C Type], " +
                            "a.fdbtr as [Dbtr],case when a.fposted=1 then 'Yes' else 'No' end as [Posted], d.fdesc as facdesc, a.fissdate as [Issue Date],  " +
                            "a.fgpm,a.fcomamt,a.fpayable,a.foutgo,a.frinsrref,a.falter1,a.fremark,a.fsi,case when isnull(a.flimit,0) = 0 then a.fliab else a.flimit end as flimit,a.fsharea,a.fshareb,b.finsd,a.finpuser,a.finpdate, " +
                            "a.fupduser,a.fupddate,a.fcnfuser,a.fcnfdate, case when fprngr=1 then 'Yes' else 'No' end as fprngr, a.fctlid, a.fritype,a.fdbtrtype  " +
                            "from oriapp a left join polh b on a.fctlid_e = b.fctlid  " +
                            "left join " + db + "[mprdr] d on d.fid=a.fdbtr ";
                if (list == "")
                {
                    sql = sql + "where a.fctlid_e = (select fctlid_1 from orih where fctlid = '" + fctlid_1 + "') and fritype='" + fritype + "' order by a.frino";
                }
                dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
                dataGridView1.DataSource = DBHelper.GetDataSet(sql);
                for (int i = 8; i < dataGridView1.ColumnCount; i++)
                {
                    this.dataGridView1.Columns[i].Visible = false;
                }
                dataGridView1.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.dataGridView1_CellFormatting);
            }
            catch { }
        }

        private void dataGridView1_CellFormatting(object sender,
      System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            if (dataGridView1.Columns[e.ColumnIndex].Name.Equals("Policy No."))
            {
                dataGridView1.Columns[e.ColumnIndex].Width = 90;
            }
            if (dataGridView1.Columns[e.ColumnIndex].Name.Equals("Issue Date"))
            {
                dataGridView1.Columns[e.ColumnIndex].Width = 62;
            }
            if (dataGridView1.Columns[e.ColumnIndex].Name.Equals("Posted"))
            {
                dataGridView1.Columns[e.ColumnIndex].Width = 45;
            }
            if (dataGridView1.Columns[e.ColumnIndex].Name.Equals("RI No."))
            {
                dataGridView1.Columns[e.ColumnIndex].Width = 35;
            }
        }

        void GridViewchg()
        {
            if (dataGridView1.CurrentCell != null)
            {
                int counter;
                counter = dataGridView1.CurrentCell.RowIndex;
                if (dataGridView1.Rows[counter].Cells["Cession"].Value != null)
                {
                    fcession.Text = dataGridView1.Rows[counter].Cells["Cession"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["R/I App"].Value != null)
                {
                    friapp.Text = dataGridView1.Rows[counter].Cells["R/I App"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["foutgo"].Value != null)
                {
                    foutgo.Text = dataGridView1.Rows[counter].Cells["foutgo"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["frinsrref"].Value != null)
                {
                    frinsrref.Text = dataGridView1.Rows[counter].Cells["frinsrref"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["Policy No."].Value != null)
                {
                    fpolno.Text = dataGridView1.Rows[counter].Cells["Policy No."].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["Endt No."].Value != null)
                {
                    fendtno.Text = dataGridView1.Rows[counter].Cells["Endt No."].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["Issue Date"].Value != null)
                {
                    try
                    {
                        string a = dataGridView1.Rows[counter].Cells["Issue Date"].Value.ToString().Trim();
                        DateTime b = Convert.ToDateTime(a);
                        fissdate.Text = b.ToString("yyyy.MM.dd");
                    }
                    catch { }
                }
                if (dataGridView1.Rows[counter].Cells["Dbtr"].Value != null)
                {
                    fdbtr.Text = dataGridView1.Rows[counter].Cells["Dbtr"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["facdesc"].Value != null)
                {
                    fdbtrdesc.Text = dataGridView1.Rows[counter].Cells["facdesc"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["finsd"].Value != null)
                {
                    finsd.Text = dataGridView1.Rows[counter].Cells["finsd"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["falter1"].Value != null)
                {
                    falter1.Text = dataGridView1.Rows[counter].Cells["falter1"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fremark"].Value != null)
                {
                    fremark.Text = dataGridView1.Rows[counter].Cells["fremark"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fsi"].Value != null)
                {
                    fsi.Text = dataGridView1.Rows[counter].Cells["fsi"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fsharea"].Value != null)
                {
                    fsharea.Text = dataGridView1.Rows[counter].Cells["fsharea"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fshareb"].Value != null)
                {
                    fshareb.Text = dataGridView1.Rows[counter].Cells["fshareb"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["flimit"].Value != null)
                {
                    flimit.Text = dataGridView1.Rows[counter].Cells["flimit"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fgpm"].Value != null)
                {
                    fgpm.Text = dataGridView1.Rows[counter].Cells["fgpm"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fcomamt"].Value != null)
                {
                    fcomamt.Text = dataGridView1.Rows[counter].Cells["fcomamt"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fpayable"].Value != null)
                {
                    fpayable.Text = dataGridView1.Rows[counter].Cells["fpayable"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["finpuser"].Value != null)
                {
                    finpuser.Text = dataGridView1.Rows[counter].Cells["finpuser"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["finpdate"].Value != null)
                {
                    finpdate.Text = dataGridView1.Rows[counter].Cells["finpdate"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fupduser"].Value != null)
                {
                    fupduser.Text = dataGridView1.Rows[counter].Cells["fupduser"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fupddate"].Value != null)
                {
                    fupddate.Text = dataGridView1.Rows[counter].Cells["fupddate"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fcnfuser"].Value != null)
                {
                    fcnfuser.Text = dataGridView1.Rows[counter].Cells["fcnfuser"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fcnfdate"].Value != null)
                {
                    fcnfdate.Text = dataGridView1.Rows[counter].Cells["fcnfdate"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["A/C Type"].Value != null)
                {
                    fdbtrtype.Text = dataGridView1.Rows[counter].Cells["A/C Type"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fprngr"].Value != null)
                {
                    fprngr.SelectedItem = dataGridView1.Rows[counter].Cells["fprngr"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["Posted"].Value != null)
                {
                    fposted.SelectedItem = dataGridView1.Rows[counter].Cells["Posted"].Value.ToString().Trim();
                }
                if (fposted.SelectedItem == "Yes")
                {
                    Postirng.Visible = false;
                    btPost.Visible = false;
                }
                else
                {
                    Postirng.Visible = true;
                    btPost.Visible = true;
                }
                fprngr.SelectedItem = "Yes";
                rowlabel.Text = counter.ToString();
                fctlid = dataGridView1.Rows[counter].Cells["fctlid"].Value.ToString().Trim();
            }
        }

        private void dataGridView1_CurrentCellChanged(object sender, EventArgs e)
        {
            GridViewchg();
        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            e.Cancel = true;
        }

        private void tabControl1_Selecting2(object sender, TabControlCancelEventArgs e)
        {
            e.Cancel = false;
        }

        public enum Mode
        {
            [Description("Dr/Cr No.")]
            finvno,

            [Description("Policy#")]
            fpolno,

            [Description("A/C#")]
            fdbtr,

            [Description("Client#")]
            fclnt
        }

        private void InitCombobox()
        {
            Array arr = System.Enum.GetValues(typeof(Mode));    // 获取枚举的所有值
            DataTable dt = new DataTable();
            dt.Columns.Add("String", Type.GetType("System.String"));
            dt.Columns.Add("Value", typeof(int));
            foreach (var a in arr)
            {
                string strText = EnumTextByDescription.GetEnumDesc((Mode)a);
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = (int)a;

                dt.Rows.Add(aRow);
            }

            comboBox1.DataSource = dt;
            comboBox1.DisplayMember = "String";
            comboBox1.ValueMember = "Value";
        }

        public enum Mode1
        {
            [Description("Broker")]
            B,

            [Description("Client")]
            C,

            [Description("Producer")]
            P,

            [Description("Insurer")]
            R
        }

        private void InitCombobox1()
        {
            Array arr = System.Enum.GetValues(typeof(Mode1));    // 获取枚举的所有值
            DataTable dt = new DataTable();
            dt.Columns.Add("String", Type.GetType("System.String"));
            dt.Columns.Add("Value", typeof(int));
            foreach (var a in arr)
            {
                string strText = EnumTextByDescription.GetEnumDesc((Mode1)a);
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = (int)a;

                dt.Rows.Add(aRow);
            }

            fdbtrtype.DataSource = dt;
            fdbtrtype.DisplayMember = "String";
            fdbtrtype.ValueMember = "Value";
        }

        void ButtonControl()
        {
            foutgo.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            foutgo.ReadOnly = true;
            frinsrref.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            frinsrref.ReadOnly = true;
            fremark.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            fremark.ReadOnly = true;
            falter1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            falter1.ReadOnly = true;
            fsi.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            fsi.ReadOnly = true;
            fsharea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            fsharea.ReadOnly = true;
            fshareb.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            fshareb.ReadOnly = true;
            button1.Visible = true;
            btprint.Visible = true;
            dcancel.Visible = false;
            dsave.Visible = false;
            dexit.Visible = true;
            dupdate.Visible = true;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting2);
        }

        private void button9_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void dexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void lupdate_Click(object sender, EventArgs e)
        {
            oldrow = rowlabel.Text;
            tabControl1.SelectedTab = Clauses;
            fremark.BackColor = System.Drawing.SystemColors.Window;
            fremark.ReadOnly = false;
            foutgo.BackColor = System.Drawing.SystemColors.Window;
            foutgo.ReadOnly = false;
            frinsrref.BackColor = System.Drawing.SystemColors.Window;
            frinsrref.ReadOnly = false;
            fremark.BackColor = System.Drawing.SystemColors.Window;
            fremark.ReadOnly = false;
            falter1.BackColor = System.Drawing.SystemColors.Window;
            falter1.ReadOnly = false;
            fsi.BackColor = System.Drawing.SystemColors.Window;
            fsi.ReadOnly = false;
            fsharea.BackColor = System.Drawing.SystemColors.Window;
            fsharea.ReadOnly = false;
            fshareb.BackColor = System.Drawing.SystemColors.Window;
            fshareb.ReadOnly = false;
            dcancel.Visible = true;
            dsave.Visible = true;
            Postirng.Visible = false;
            btPost.Visible = false;
            button1.Visible = false;
            btprint.Visible = false;
            dexit.Visible = false;
            dupdate.Visible = false;
            fprngr.Enabled = true;
            fprngr.BackColor = System.Drawing.SystemColors.Window;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);
        }

        private void dupdate_Click(object sender, EventArgs e)
        {
            oldrow = rowlabel.Text;
            tabControl1.SelectedTab = Clauses;
            fremark.BackColor = System.Drawing.SystemColors.Window;
            fremark.ReadOnly = false;
            foutgo.BackColor = System.Drawing.SystemColors.Window;
            foutgo.ReadOnly = false;
            frinsrref.BackColor = System.Drawing.SystemColors.Window;
            frinsrref.ReadOnly = false;
            falter1.BackColor = System.Drawing.SystemColors.Window;
            falter1.ReadOnly = false;
            fsi.BackColor = System.Drawing.SystemColors.Window;
            fsi.ReadOnly = false;
            fsharea.BackColor = System.Drawing.SystemColors.Window;
            fsharea.ReadOnly = false;
            fshareb.BackColor = System.Drawing.SystemColors.Window;
            fshareb.ReadOnly = false;
            Postirng.Visible = false;
            btPost.Visible = false;
            button1.Visible = false;
            btprint.Visible = false;
            dcancel.Visible = true;
            dsave.Visible = true;
            dexit.Visible = false;
            dupdate.Visible = false;
            fprngr.Enabled = true;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);

        }

        private void dsave_Click(object sender, EventArgs e)
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really Update the item?", "Updated Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                string falters = falter1.Text.ToString().Trim();
                string falter1s = "", falter2s = "";
                if (falters.Length > 254)
                {
                    falter1s = falter1.Text.ToString().Trim().Substring(0, 253);
                    falter2s = falter1.Text.ToString().Trim().Substring(254, falter1.Text.Length);
                }
                else
                {
                    falter1s = falter1.Text.ToString().Trim();
                }
                string updatedatestr = "update oriapp set fremark='" + Fct.stFat(fremark.Text) + "' ,foutgo='" + Fct.stFat(foutgo.Text) + "',frinsrref='" + Fct.stFat(frinsrref.Text) + "',falter1='" + Fct.stFat(falter1.Text) + "',fsi='" + Convert.ToDecimal(fsi.Text) + "',fsharea='" + Convert.ToDecimal(fsharea.Text) + "',fshareb='" + Convert.ToDecimal(fshareb.Text) + "',fissdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "',fupduser='" + InsEnvironment.LoginUser.GetUserCode() + "',fupddate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid='" + fctlid + "'";
                DBHelper.ExecuteCommand(updatedatestr);
                MessageBox.Show("Have Been Updated", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                ButtonControl();
                string oldfctlid = fctlid;
                FillData("", "", "");
                DataTable dt3 = dataGridView1.DataSource as DataTable;
                foreach (DataRow row in dt3.Rows)
                {
                    int SelectedIndex = dt3.Rows.IndexOf(row);
                    if (oldfctlid == row["fctlid"].ToString().Trim())
                    {
                        dataGridView1.CurrentCell = dataGridView1.Rows[SelectedIndex].Cells["Cession"];
                        GridViewchg();
                        break;
                    }
                }
                //reload(oldrow, "update");
            }
            else
            {
                MessageBox.Show("Some field is empty!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void dcancel_Click(object sender, EventArgs e)
        {
            ButtonControl();
            FillData("", "", "");
            //reload(oldrow, "cancel");
        }

        void reload(string fid, string lab)
        {
            string str = "select a.fpolno as [Policy No.],a.fendtno as [Endt No.],a.frino as [RI No.], a.fcession as [Cession],a.friapp as [R/I App],  " +
            "case when a.fdbtrtype='B' then 'Broker' when a.fdbtrtype='C' then 'Client' when a.fdbtrtype='P' then 'Producer' else 'Insurer' end as [A/C Type], " +
            "a.fdbtr as [Dbtr], d.fdesc as facdesc, case when a.fposted=1 then 'Yes' else 'No' end as [Posted], a.fissdate as [Issue Date],  " +
            "a.fgpm,a.fcomamt,a.fpayable,a.foutgo,a.frinsrref,a.falter1,a.fremark,a.fsi,a.flimit,a.fsharea,a.fshareb,b.finsd,a.finpuser,a.finpdate, " +
            "a.fupduser,a.fupddate,a.fcnfuser,a.fcnfdate, case when fprngr=1 then 'Yes' else 'No' end as fprngr, a.fctlid, a.fritype,a.fdbtrtype   " +
            "from oriapp a left join polh b on a.fctlid_e = b.fctlid  " +
            "left join " + db + "[mprdr] d on d.fid=a.fdbtr ";
            // str = str + "where a.fctlid_ri='" + fctlid_1 + "' and fritype='" + fritype + "' order by a.fctlid";
            str = str + "where a.fctlid_e = (select fctlid_1 from orih where fctlid = '" + fctlid_1 + "') and fritype='" + fritype + "' order by a.fctlid";

            DataTable dt = DBHelper.GetDataSet(str);
            if (dt.Rows.Count > 0)
            {
                dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
                dataGridView1.DataSource = dt;
                for (int i = 11; i < dataGridView1.ColumnCount; i++)
                {
                    this.dataGridView1.Columns[i].Visible = false;
                }
                dataGridView1.CellFormatting +=
                new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.dataGridView1_CellFormatting);
                if (lab == "cancel")
                {
                    if (dataGridView1.CurrentCell != null)
                    {
                        dataGridView1.CurrentCell = dataGridView1.Rows[int.Parse(fid)].Cells["RI No."];
                    }
                }
                else
                {
                    DataTable dt3 = dataGridView1.DataSource as DataTable;
                    foreach (DataRow row in dt3.Rows)
                    {
                        int SelectedIndex = dt3.Rows.IndexOf(row);
                        if (fid == row["Cession"].ToString().Trim())
                        {
                            dataGridView1.CurrentCell = dataGridView1.Rows[SelectedIndex].Cells["Cession"];
                            GridViewchg();
                            break;
                        }
                    }
                }
            }
        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", "", "");
        }

        private void button1_Click(object sender, EventArgs e)
        {
            Print();
        }

        private void btprint_Click(object sender, EventArgs e)
        {
            Print();
        }

        private void Postirng_Click(object sender, EventArgs e)
        {
            Post();
        }

        private void btPost_Click(object sender, EventArgs e)
        {
            Post();
        }

        void Post()
        {
            string oldrow = fcession.Text;
            string[] update1 = postPart1();
            string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                SqlCommand command = connection.CreateCommand();
                SqlTransaction transaction;

                // Start a local transaction.
                transaction = connection.BeginTransaction("SampleTransaction");

                // Must assign both transaction object and connection 
                // to Command object for a pending local transaction
                command.Connection = connection;
                command.Transaction = transaction;

                try
                {
                    if (update1 != null)
                    {
                        for (int i = 0; i < update1.Length; i++)
                        {
                            if (update1[i] != "" && update1[i] != null)
                            {
                                command.CommandText = update1[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    transaction.Commit();
                }
                catch (Exception ex)
                {
                    MessageBox.Show("Haven't Been Posted", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                    Console.WriteLine("  Message: {0}", ex.Message);

                    // Attempt to roll back the transaction. 
                    try
                    {
                        transaction.Rollback();
                    }
                    catch (Exception ex2)
                    {
                        // This catch block will handle any errors that may have occurred 
                        // on the server that would cause the rollback to fail, such as 
                        // a closed connection.
                        Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                        Console.WriteLine("  Message: {0}", ex2.Message);
                    }
                }
                MessageBox.Show("Have Been Posted", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                connection.Close();
                FillData("", "", "");
                DataTable dt3 = dataGridView1.DataSource as DataTable;
                foreach (DataRow row in dt3.Rows)
                {
                    int SelectedIndex = dt3.Rows.IndexOf(row);
                    if (oldrow == row["Cession"].ToString().Trim())
                    {
                        dataGridView1.CurrentCell = dataGridView1.Rows[SelectedIndex].Cells["Cession"];
                        GridViewchg();
                        break;
                    }
                }
            }
        }

        public string[] postPart1()
        {
            DateTime time = DateTime.Now;
            ArrayList PostSql = new ArrayList();
            string updsql = "update oriapp set fremark='" + Fct.stFat(fremark.Text) + "' ,foutgo='" + Fct.stFat(foutgo.Text) + "',frinsrref='" + Fct.stFat(frinsrref.Text) + "',falter1='" + Fct.stFat(falter1.Text) + "',fsi='" + Convert.ToDecimal(fsi.Text) + "',fsharea='" + Convert.ToDecimal(fsharea.Text) + "',fshareb='" + Convert.ToDecimal(fshareb.Text) + "',fissdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "',fposted=1,fcnfuser='" + InsEnvironment.LoginUser.GetUserCode() + "',fcnfdate='" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' where fctlid='" + fctlid + "'";
            PostSql.Add(updsql);
            DataTable dt = dataGridView1.DataSource as DataTable;
            foreach (DataRow dr in dt.Rows)
            {
                updsql = "update orid2 set friapp ='" + dr["R/I App"] + "' , fcession ='" + dr["Cession"] + "',frinsrref='" + dr["frinsrref"] + "'   " +
                "where fctlid_2 = '" + fctlid_1 + "' and fritype  ='" + dr["fritype"] + "'  " +
                "and case when fbrkr='PRD001' then 'R' else 'B' end ='" + dr["fdbtrtype"] + "'   " +
                "and case when fbrkr='PRD001' then freinsr else fbrkr end ='" + dr["Dbtr"] + "' ";
                PostSql.Add(updsql);
            }
            return (string[])PostSql.ToArray(typeof(string));
        }


        public string NEWID(string type, string year)
        {
            string lc_seqno = "";
            string sql = "select rtrim(fprefix)+fnxtid as lc_seqno from " + db + "xsysparm where fidtype='" + type + "' and fuy='" + year + "' ";
            DataTable dt = DBHelper.GetDataSet(sql);
            if (dt.Rows.Count > 0)
            {
                lc_seqno = dt.Rows[0]["lc_seqno"].ToString();
            }
            return lc_seqno;
        }

        public string newid(string type)
        {
            string lc_seqno = "";
            string sql = "select fnxtid as lc_seqno from " + db + "xsysparm where fidtype='" + type + "' ";
            DataTable dt = DBHelper.GetDataSet(sql);
            if (dt.Rows.Count > 0)
            {
                lc_seqno = dt.Rows[0]["lc_seqno"].ToString();
            }
            return lc_seqno;
        }

        private void frinsrref_TextChanged(object sender, EventArgs e)
        {
            dataGridView1["frinsrref", dataGridView1.CurrentCell.RowIndex].Value = frinsrref.Text;
        }

        void Print()
        {
            prih tempform = new prih(this);
            tempform.fclass = fpolno.Text.Trim();
            tempform.fctlid = fctlid;
            tempform.p_calling = "RIAPP";
            tempform.conctrl();
            tempform.ShowDialog();
        }


        private void tabControl1_SelectedIndexChanged(object sender, EventArgs e)
        {
            //if (tabControl1.SelectedIndex == 0)
            //{
            //    FillData("", "", "");
            //}
        }
    }
}

