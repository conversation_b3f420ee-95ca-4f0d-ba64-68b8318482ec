using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;

namespace INS
{
    public partial class frmXolSearch : Form
    {
        public frmXolSearch()
        {
            InitializeComponent();
            FillData("", "");
        }

        public frmXolSearch(Ctrl.Claim.REINSR rEINSR)
        {
            // TODO: Complete member initialization
            this.rEINSR = rEINSR;
            InitializeComponent();
        }

        public frmXolSearch(Ctrl.Claim.PAY pAY)
        {
            // TODO: Complete member initialization
            this.pAY = pAY;
            InitializeComponent();
        }

        public frmXolSearch(Ctrl.Claim.ADJ aDJ)
        {
            // TODO: Complete member initialization
            this.aDJ = aDJ;
            InitializeComponent();
        }

        public frmXolSearch(Ctrl.Claim.EciREINSR eciREINSR)
        {
            // TODO: Complete member initialization
            this.eciREINSR = eciREINSR;
            InitializeComponent();
        }

        public frmXolSearch(Ctrl.Claim.EciADJ eciADJ)
        {
            // TODO: Complete member initialization
            this.eciADJ = eciADJ;
            InitializeComponent();
        }

        public frmXolSearch(Ctrl.Claim.EciPAY eciPAY)
        {
            // TODO: Complete member initialization
            this.eciPAY = eciPAY;
            InitializeComponent();
        }

        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "";
        private Ctrl.Claim.REINSR rEINSR;
        public string db = InsEnvironment.DataBase.GetDbm();
        private Ctrl.Claim.PAY pAY;
        private Ctrl.Claim.ADJ aDJ;
        private Ctrl.Claim.EciREINSR eciREINSR;
        private Ctrl.Claim.EciADJ eciADJ;
        private Ctrl.Claim.EciPAY eciPAY;

        public void FillData(string order, string query)
        {
            string xol = "select distinct a.fid as [Treaty#], c.fid as [Layer], fefffr as [Eff Fr], feffto as [Eff To], b.fdesc as [Desc] " +
                        "from " + db + "mxl a  " +
                        "left join " + db + "mxllayr c on c.fctlid_1 =a.fctlid " +
                        "left join " + db + "mxlclass b on b.fctlid_1=a.fctlid and b.fctlid_2= c.fctlid " +
                        "where b.ftype ='2'";
            dataGridView1.DataSource = DBHelper.GetDataSet(xol);
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }
                if (dataGridView1.Rows[row].Cells["Treaty#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Treaty#"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["Treaty#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Layer"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Layer"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["Layer"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            if (flag == "REINSR")
            {
                rEINSR.XolCode = Fct.stFat(textBox1.Text.ToString());
                rEINSR.XolLayer = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "PAY")
            {
                pAY.XolCode = Fct.stFat(textBox1.Text.ToString());
                pAY.XolLayer = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "ADJ")
            {
                aDJ.XolCode = Fct.stFat(textBox1.Text.ToString());
                aDJ.XolLayer = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EciREINSR")
            {
                eciREINSR.XolCode = Fct.stFat(textBox1.Text.ToString());
                eciREINSR.XolLayer = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EciADJ")
            {
                eciADJ.XolCode = Fct.stFat(textBox1.Text.ToString());
                eciADJ.XolLayer = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EciPAY")
            {
                eciPAY.XolCode =Fct.stFat( textBox1.Text.ToString());
                eciPAY.XolLayer = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
        }


        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }


    }
}
