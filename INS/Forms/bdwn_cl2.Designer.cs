namespace INS.Business.SearchForm
{
    partial class bdwn_cl2
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.fclmno = new System.Windows.Forms.TextBox();
            this.label66 = new System.Windows.Forms.Label();
            this.flogseq = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.faccdate = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.label26 = new System.Windows.Forms.Label();
            this.label28 = new System.Windows.Forms.Label();
            this.label30 = new System.Windows.Forms.Label();
            this.label31 = new System.Windows.Forms.Label();
            this.label32 = new System.Windows.Forms.Label();
            this.label33 = new System.Windows.Forms.Label();
            this.fremark_t = new System.Windows.Forms.TextBox();
            this.label36 = new System.Windows.Forms.Label();
            this.XcmdExit = new System.Windows.Forms.Button();
            this.XcmdConfirm = new System.Windows.Forms.Button();
            this.label27 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.fadjA2 = new System.Windows.Forms.Label();
            this.fadjB = new System.Windows.Forms.Label();
            this.frvosA2 = new System.Windows.Forms.Label();
            this.frvosB = new System.Windows.Forms.Label();
            this.ftadj13 = new System.Windows.Forms.TextBox();
            this.ftadj12 = new System.Windows.Forms.TextBox();
            this.ftadj11 = new System.Windows.Forms.TextBox();
            this.ftadj = new System.Windows.Forms.TextBox();
            this.ftadjC = new System.Windows.Forms.TextBox();
            this.ftadj09 = new System.Windows.Forms.TextBox();
            this.ftadj08 = new System.Windows.Forms.TextBox();
            this.ftadjA = new System.Windows.Forms.TextBox();
            this.ftadjB = new System.Windows.Forms.Label();
            this.ftadjA2 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.fosB = new System.Windows.Forms.Label();
            this.fosA2 = new System.Windows.Forms.Label();
            this.XAutoLbl = new System.Windows.Forms.Label();
            this.fadj13 = new Shorty.Windows.Forms.NumericTextBox();
            this.frvos13 = new Shorty.Windows.Forms.NumericTextBox();
            this.fos13 = new Shorty.Windows.Forms.NumericTextBox();
            this.fadj12 = new Shorty.Windows.Forms.NumericTextBox();
            this.frvos12 = new Shorty.Windows.Forms.NumericTextBox();
            this.fos12 = new Shorty.Windows.Forms.NumericTextBox();
            this.fadj11 = new Shorty.Windows.Forms.NumericTextBox();
            this.frvos11 = new Shorty.Windows.Forms.NumericTextBox();
            this.fos11 = new Shorty.Windows.Forms.NumericTextBox();
            this.fadj = new Shorty.Windows.Forms.NumericTextBox();
            this.frvos = new Shorty.Windows.Forms.NumericTextBox();
            this.fos = new Shorty.Windows.Forms.NumericTextBox();
            this.fadjC = new Shorty.Windows.Forms.NumericTextBox();
            this.frvosC = new Shorty.Windows.Forms.NumericTextBox();
            this.fosC = new Shorty.Windows.Forms.NumericTextBox();
            this.fadj09 = new Shorty.Windows.Forms.NumericTextBox();
            this.frvos09 = new Shorty.Windows.Forms.NumericTextBox();
            this.fos09 = new Shorty.Windows.Forms.NumericTextBox();
            this.fadj08 = new Shorty.Windows.Forms.NumericTextBox();
            this.frvos08 = new Shorty.Windows.Forms.NumericTextBox();
            this.fos08 = new Shorty.Windows.Forms.NumericTextBox();
            this.fadjA = new Shorty.Windows.Forms.NumericTextBox();
            this.frvosA = new Shorty.Windows.Forms.NumericTextBox();
            this.fosA = new Shorty.Windows.Forms.NumericTextBox();
            this.SuspendLayout();
            // 
            // fclmno
            // 
            this.fclmno.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fclmno.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fclmno.Location = new System.Drawing.Point(95, 16);
            this.fclmno.Name = "fclmno";
            this.fclmno.ReadOnly = true;
            this.fclmno.Size = new System.Drawing.Size(112, 22);
            this.fclmno.TabIndex = 1;
            // 
            // label66
            // 
            this.label66.AutoSize = true;
            this.label66.ForeColor = System.Drawing.Color.White;
            this.label66.Location = new System.Drawing.Point(36, 22);
            this.label66.Name = "label66";
            this.label66.Size = new System.Drawing.Size(53, 12);
            this.label66.TabIndex = 293;
            this.label66.Text = "Claim No.";
            // 
            // flogseq
            // 
            this.flogseq.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.flogseq.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.flogseq.Location = new System.Drawing.Point(269, 16);
            this.flogseq.Name = "flogseq";
            this.flogseq.ReadOnly = true;
            this.flogseq.Size = new System.Drawing.Size(54, 22);
            this.flogseq.TabIndex = 2;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.ForeColor = System.Drawing.Color.White;
            this.label1.Location = new System.Drawing.Point(225, 22);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 295;
            this.label1.Text = "Adjust#";
            // 
            // faccdate
            // 
            this.faccdate.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.faccdate.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.faccdate.Location = new System.Drawing.Point(397, 16);
            this.faccdate.Name = "faccdate";
            this.faccdate.ReadOnly = true;
            this.faccdate.Size = new System.Drawing.Size(97, 22);
            this.faccdate.TabIndex = 3;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.ForeColor = System.Drawing.Color.White;
            this.label2.Location = new System.Drawing.Point(346, 22);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(48, 12);
            this.label2.TabIndex = 297;
            this.label2.Text = "A/C Date";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Underline);
            this.label3.ForeColor = System.Drawing.Color.White;
            this.label3.Location = new System.Drawing.Point(36, 84);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(144, 16);
            this.label3.TabIndex = 299;
            this.label3.Text = "PART A1 - Damages";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label11.ForeColor = System.Drawing.Color.White;
            this.label11.Location = new System.Drawing.Point(408, 47);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(104, 16);
            this.label11.TabIndex = 341;
            this.label11.Text = "Last O/S (1)";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label12.ForeColor = System.Drawing.Color.White;
            this.label12.Location = new System.Drawing.Point(507, 47);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(128, 16);
            this.label12.TabIndex = 342;
            this.label12.Text = "Revised O/S (2)";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label13.ForeColor = System.Drawing.Color.White;
            this.label13.Location = new System.Drawing.Point(611, 47);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(168, 16);
            this.label13.TabIndex = 343;
            this.label13.Text = "Adjustment (2) - (1)";
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label26.ForeColor = System.Drawing.Color.White;
            this.label26.Location = new System.Drawing.Point(36, 141);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(400, 16);
            this.label26.TabIndex = 388;
            this.label26.Text = "Other Expenses (Medical Report, Adjuster Fee ...)";
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label28.ForeColor = System.Drawing.Color.White;
            this.label28.Location = new System.Drawing.Point(36, 186);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(328, 16);
            this.label28.TabIndex = 393;
            this.label28.Text = "PART B - Claimant\'s Costs && Disbursement";
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label30.ForeColor = System.Drawing.Color.White;
            this.label30.Location = new System.Drawing.Point(36, 215);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(272, 16);
            this.label30.TabIndex = 398;
            this.label30.Text = "PART C - Own Costs && Disbursement";
            // 
            // label31
            // 
            this.label31.AutoSize = true;
            this.label31.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label31.ForeColor = System.Drawing.Color.White;
            this.label31.Location = new System.Drawing.Point(36, 233);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(112, 16);
            this.label31.TabIndex = 408;
            this.label31.Text = "Mediation Fee";
            // 
            // label32
            // 
            this.label32.AutoSize = true;
            this.label32.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label32.ForeColor = System.Drawing.Color.White;
            this.label32.Location = new System.Drawing.Point(36, 256);
            this.label32.Name = "label32";
            this.label32.Size = new System.Drawing.Size(96, 16);
            this.label32.TabIndex = 409;
            this.label32.Text = "Legal Costs";
            // 
            // label33
            // 
            this.label33.AutoSize = true;
            this.label33.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label33.ForeColor = System.Drawing.Color.White;
            this.label33.Location = new System.Drawing.Point(36, 281);
            this.label33.Name = "label33";
            this.label33.Size = new System.Drawing.Size(120, 16);
            this.label33.TabIndex = 410;
            this.label33.Text = "Other Expenses";
            // 
            // fremark_t
            // 
            this.fremark_t.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fremark_t.Enabled = false;
            this.fremark_t.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fremark_t.Location = new System.Drawing.Point(149, 413);
            this.fremark_t.MaxLength = 254;
            this.fremark_t.Multiline = true;
            this.fremark_t.Name = "fremark_t";
            this.fremark_t.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            this.fremark_t.Size = new System.Drawing.Size(597, 49);
            this.fremark_t.TabIndex = 52;
            // 
            // label36
            // 
            this.label36.AutoSize = true;
            this.label36.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.label36.ForeColor = System.Drawing.Color.White;
            this.label36.Location = new System.Drawing.Point(76, 416);
            this.label36.Name = "label36";
            this.label36.Size = new System.Drawing.Size(64, 16);
            this.label36.TabIndex = 420;
            this.label36.Text = "Remark:";
            // 
            // XcmdExit
            // 
            this.XcmdExit.Location = new System.Drawing.Point(629, 475);
            this.XcmdExit.Name = "XcmdExit";
            this.XcmdExit.Size = new System.Drawing.Size(75, 23);
            this.XcmdExit.TabIndex = 421;
            this.XcmdExit.Text = "Exit(&E)";
            this.XcmdExit.UseVisualStyleBackColor = true;
            this.XcmdExit.Click += new System.EventHandler(this.XcmdExit_Click);
            // 
            // XcmdConfirm
            // 
            this.XcmdConfirm.Location = new System.Drawing.Point(593, 475);
            this.XcmdConfirm.Name = "XcmdConfirm";
            this.XcmdConfirm.Size = new System.Drawing.Size(75, 23);
            this.XcmdConfirm.TabIndex = 422;
            this.XcmdConfirm.Text = "Accept(&O)";
            this.XcmdConfirm.UseVisualStyleBackColor = true;
            this.XcmdConfirm.Visible = false;
            this.XcmdConfirm.Click += new System.EventHandler(this.XcmdConfirm_Click);
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Enabled = false;
            this.label27.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label27.ForeColor = System.Drawing.Color.White;
            this.label27.Location = new System.Drawing.Point(301, 185);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(96, 16);
            this.label27.TabIndex = 392;
            this.label27.Text = "Legal Costs";
            this.label27.Visible = false;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Underline);
            this.label4.ForeColor = System.Drawing.Color.White;
            this.label4.Location = new System.Drawing.Point(37, 120);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(144, 16);
            this.label4.TabIndex = 432;
            this.label4.Text = "PART A2 - Damages";
            // 
            // fadjA2
            // 
            this.fadjA2.AutoSize = true;
            this.fadjA2.Location = new System.Drawing.Point(49, 322);
            this.fadjA2.Name = "fadjA2";
            this.fadjA2.Size = new System.Drawing.Size(37, 12);
            this.fadjA2.TabIndex = 444;
            this.fadjA2.Text = "fadjA2";
            this.fadjA2.Visible = false;
            // 
            // fadjB
            // 
            this.fadjB.AutoSize = true;
            this.fadjB.Location = new System.Drawing.Point(123, 322);
            this.fadjB.Name = "fadjB";
            this.fadjB.Size = new System.Drawing.Size(31, 12);
            this.fadjB.TabIndex = 445;
            this.fadjB.Text = "fadjB";
            this.fadjB.Visible = false;
            // 
            // frvosA2
            // 
            this.frvosA2.AutoSize = true;
            this.frvosA2.Location = new System.Drawing.Point(187, 322);
            this.frvosA2.Name = "frvosA2";
            this.frvosA2.Size = new System.Drawing.Size(43, 12);
            this.frvosA2.TabIndex = 446;
            this.frvosA2.Text = "frvosA2";
            this.frvosA2.Visible = false;
            // 
            // frvosB
            // 
            this.frvosB.AutoSize = true;
            this.frvosB.Location = new System.Drawing.Point(246, 322);
            this.frvosB.Name = "frvosB";
            this.frvosB.Size = new System.Drawing.Size(37, 12);
            this.frvosB.TabIndex = 447;
            this.frvosB.Text = "frvosB";
            this.frvosB.Visible = false;
            // 
            // ftadj13
            // 
            this.ftadj13.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadj13.Enabled = false;
            this.ftadj13.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadj13.Location = new System.Drawing.Point(630, 273);
            this.ftadj13.Name = "ftadj13";
            this.ftadj13.ReadOnly = true;
            this.ftadj13.Size = new System.Drawing.Size(114, 22);
            this.ftadj13.TabIndex = 463;
            this.ftadj13.Visible = false;
            // 
            // ftadj12
            // 
            this.ftadj12.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadj12.Enabled = false;
            this.ftadj12.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadj12.Location = new System.Drawing.Point(630, 251);
            this.ftadj12.Name = "ftadj12";
            this.ftadj12.ReadOnly = true;
            this.ftadj12.Size = new System.Drawing.Size(114, 22);
            this.ftadj12.TabIndex = 461;
            this.ftadj12.Visible = false;
            // 
            // ftadj11
            // 
            this.ftadj11.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadj11.Enabled = false;
            this.ftadj11.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadj11.Location = new System.Drawing.Point(630, 229);
            this.ftadj11.Name = "ftadj11";
            this.ftadj11.ReadOnly = true;
            this.ftadj11.Size = new System.Drawing.Size(114, 22);
            this.ftadj11.TabIndex = 460;
            this.ftadj11.Visible = false;
            // 
            // ftadj
            // 
            this.ftadj.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadj.Enabled = false;
            this.ftadj.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadj.Location = new System.Drawing.Point(630, 341);
            this.ftadj.Name = "ftadj";
            this.ftadj.ReadOnly = true;
            this.ftadj.Size = new System.Drawing.Size(114, 22);
            this.ftadj.TabIndex = 459;
            this.ftadj.Visible = false;
            // 
            // ftadjC
            // 
            this.ftadjC.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadjC.Enabled = false;
            this.ftadjC.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadjC.Location = new System.Drawing.Point(630, 295);
            this.ftadjC.Name = "ftadjC";
            this.ftadjC.ReadOnly = true;
            this.ftadjC.Size = new System.Drawing.Size(114, 22);
            this.ftadjC.TabIndex = 458;
            this.ftadjC.Visible = false;
            // 
            // ftadj09
            // 
            this.ftadj09.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadj09.Enabled = false;
            this.ftadj09.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadj09.Location = new System.Drawing.Point(630, 181);
            this.ftadj09.Name = "ftadj09";
            this.ftadj09.ReadOnly = true;
            this.ftadj09.Size = new System.Drawing.Size(114, 22);
            this.ftadj09.TabIndex = 457;
            this.ftadj09.Visible = false;
            // 
            // ftadj08
            // 
            this.ftadj08.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadj08.Enabled = false;
            this.ftadj08.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadj08.Location = new System.Drawing.Point(630, 135);
            this.ftadj08.Name = "ftadj08";
            this.ftadj08.ReadOnly = true;
            this.ftadj08.Size = new System.Drawing.Size(114, 22);
            this.ftadj08.TabIndex = 456;
            this.ftadj08.Visible = false;
            // 
            // ftadjA
            // 
            this.ftadjA.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.ftadjA.Enabled = false;
            this.ftadjA.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.ftadjA.Location = new System.Drawing.Point(630, 84);
            this.ftadjA.Name = "ftadjA";
            this.ftadjA.ReadOnly = true;
            this.ftadjA.Size = new System.Drawing.Size(114, 22);
            this.ftadjA.TabIndex = 454;
            this.ftadjA.Visible = false;
            // 
            // ftadjB
            // 
            this.ftadjB.AutoSize = true;
            this.ftadjB.Location = new System.Drawing.Point(124, 347);
            this.ftadjB.Name = "ftadjB";
            this.ftadjB.Size = new System.Drawing.Size(34, 12);
            this.ftadjB.TabIndex = 465;
            this.ftadjB.Text = "ftadjB";
            this.ftadjB.Visible = false;
            // 
            // ftadjA2
            // 
            this.ftadjA2.AutoSize = true;
            this.ftadjA2.Location = new System.Drawing.Point(50, 347);
            this.ftadjA2.Name = "ftadjA2";
            this.ftadjA2.Size = new System.Drawing.Size(40, 12);
            this.ftadjA2.TabIndex = 464;
            this.ftadjA2.Text = "ftadjA2";
            this.ftadjA2.Visible = false;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label10.ForeColor = System.Drawing.Color.White;
            this.label10.Location = new System.Drawing.Point(317, 301);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(80, 16);
            this.label10.TabIndex = 466;
            this.label10.Text = "Sub Total";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Font = new System.Drawing.Font("Courier New", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label14.ForeColor = System.Drawing.Color.White;
            this.label14.Location = new System.Drawing.Point(317, 347);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(48, 16);
            this.label14.TabIndex = 467;
            this.label14.Text = "Total";
            // 
            // fosB
            // 
            this.fosB.AutoSize = true;
            this.fosB.Location = new System.Drawing.Point(246, 347);
            this.fosB.Name = "fosB";
            this.fosB.Size = new System.Drawing.Size(27, 12);
            this.fosB.TabIndex = 469;
            this.fosB.Text = "fosB";
            this.fosB.Visible = false;
            // 
            // fosA2
            // 
            this.fosA2.AutoSize = true;
            this.fosA2.Location = new System.Drawing.Point(187, 347);
            this.fosA2.Name = "fosA2";
            this.fosA2.Size = new System.Drawing.Size(33, 12);
            this.fosA2.TabIndex = 468;
            this.fosA2.Text = "fosA2";
            this.fosA2.Visible = false;
            // 
            // XAutoLbl
            // 
            this.XAutoLbl.AutoSize = true;
            this.XAutoLbl.BackColor = System.Drawing.Color.Red;
            this.XAutoLbl.ForeColor = System.Drawing.Color.White;
            this.XAutoLbl.Location = new System.Drawing.Point(500, 22);
            this.XAutoLbl.Name = "XAutoLbl";
            this.XAutoLbl.Size = new System.Drawing.Size(61, 12);
            this.XAutoLbl.TabIndex = 470;
            this.XAutoLbl.Text = "Auto Adjust";
            this.XAutoLbl.Visible = false;
            // 
            // fadj13
            // 
            this.fadj13.AllowNegative = true;
            this.fadj13.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadj13.Enabled = false;
            this.fadj13.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadj13.Location = new System.Drawing.Point(608, 273);
            this.fadj13.Name = "fadj13";
            this.fadj13.NumericPrecision = 15;
            this.fadj13.NumericScaleOnFocus = 2;
            this.fadj13.NumericScaleOnLostFocus = 2;
            this.fadj13.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadj13.ReadOnly = true;
            this.fadj13.Size = new System.Drawing.Size(114, 22);
            this.fadj13.TabIndex = 45;
            this.fadj13.Text = "0";
            this.fadj13.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadj13.ZeroIsValid = true;
            // 
            // frvos13
            // 
            this.frvos13.AllowNegative = true;
            this.frvos13.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvos13.Enabled = false;
            this.frvos13.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvos13.Location = new System.Drawing.Point(495, 273);
            this.frvos13.Name = "frvos13";
            this.frvos13.NumericPrecision = 15;
            this.frvos13.NumericScaleOnFocus = 2;
            this.frvos13.NumericScaleOnLostFocus = 2;
            this.frvos13.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvos13.ReadOnly = true;
            this.frvos13.Size = new System.Drawing.Size(112, 22);
            this.frvos13.TabIndex = 44;
            this.frvos13.Text = "0";
            this.frvos13.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvos13.ZeroIsValid = true;
            this.frvos13.Validated += new System.EventHandler(this.frvos13_Validated);
            // 
            // fos13
            // 
            this.fos13.AllowNegative = true;
            this.fos13.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fos13.Enabled = false;
            this.fos13.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fos13.Location = new System.Drawing.Point(397, 273);
            this.fos13.Name = "fos13";
            this.fos13.NumericPrecision = 15;
            this.fos13.NumericScaleOnFocus = 2;
            this.fos13.NumericScaleOnLostFocus = 2;
            this.fos13.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fos13.ReadOnly = true;
            this.fos13.Size = new System.Drawing.Size(97, 22);
            this.fos13.TabIndex = 43;
            this.fos13.Text = "0";
            this.fos13.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fos13.ZeroIsValid = true;
            // 
            // fadj12
            // 
            this.fadj12.AllowNegative = true;
            this.fadj12.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadj12.Enabled = false;
            this.fadj12.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadj12.Location = new System.Drawing.Point(608, 251);
            this.fadj12.Name = "fadj12";
            this.fadj12.NumericPrecision = 15;
            this.fadj12.NumericScaleOnFocus = 2;
            this.fadj12.NumericScaleOnLostFocus = 2;
            this.fadj12.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadj12.ReadOnly = true;
            this.fadj12.Size = new System.Drawing.Size(114, 22);
            this.fadj12.TabIndex = 42;
            this.fadj12.Text = "0";
            this.fadj12.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadj12.ZeroIsValid = true;
            // 
            // frvos12
            // 
            this.frvos12.AllowNegative = true;
            this.frvos12.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvos12.Enabled = false;
            this.frvos12.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvos12.Location = new System.Drawing.Point(495, 251);
            this.frvos12.Name = "frvos12";
            this.frvos12.NumericPrecision = 15;
            this.frvos12.NumericScaleOnFocus = 2;
            this.frvos12.NumericScaleOnLostFocus = 2;
            this.frvos12.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvos12.ReadOnly = true;
            this.frvos12.Size = new System.Drawing.Size(112, 22);
            this.frvos12.TabIndex = 41;
            this.frvos12.Text = "0";
            this.frvos12.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvos12.ZeroIsValid = true;
            this.frvos12.Validated += new System.EventHandler(this.frvos12_Validated);
            // 
            // fos12
            // 
            this.fos12.AllowNegative = true;
            this.fos12.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fos12.Enabled = false;
            this.fos12.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fos12.Location = new System.Drawing.Point(397, 251);
            this.fos12.Name = "fos12";
            this.fos12.NumericPrecision = 15;
            this.fos12.NumericScaleOnFocus = 2;
            this.fos12.NumericScaleOnLostFocus = 2;
            this.fos12.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fos12.ReadOnly = true;
            this.fos12.Size = new System.Drawing.Size(97, 22);
            this.fos12.TabIndex = 40;
            this.fos12.Text = "0";
            this.fos12.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fos12.ZeroIsValid = true;
            // 
            // fadj11
            // 
            this.fadj11.AllowNegative = true;
            this.fadj11.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadj11.Enabled = false;
            this.fadj11.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadj11.Location = new System.Drawing.Point(608, 229);
            this.fadj11.Name = "fadj11";
            this.fadj11.NumericPrecision = 15;
            this.fadj11.NumericScaleOnFocus = 2;
            this.fadj11.NumericScaleOnLostFocus = 2;
            this.fadj11.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadj11.ReadOnly = true;
            this.fadj11.Size = new System.Drawing.Size(114, 22);
            this.fadj11.TabIndex = 39;
            this.fadj11.Text = "0";
            this.fadj11.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadj11.ZeroIsValid = true;
            // 
            // frvos11
            // 
            this.frvos11.AllowNegative = true;
            this.frvos11.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvos11.Enabled = false;
            this.frvos11.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvos11.Location = new System.Drawing.Point(495, 229);
            this.frvos11.Name = "frvos11";
            this.frvos11.NumericPrecision = 15;
            this.frvos11.NumericScaleOnFocus = 2;
            this.frvos11.NumericScaleOnLostFocus = 2;
            this.frvos11.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvos11.ReadOnly = true;
            this.frvos11.Size = new System.Drawing.Size(112, 22);
            this.frvos11.TabIndex = 38;
            this.frvos11.Text = "0";
            this.frvos11.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvos11.ZeroIsValid = true;
            this.frvos11.Validated += new System.EventHandler(this.frvos11_Validated);
            // 
            // fos11
            // 
            this.fos11.AllowNegative = true;
            this.fos11.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fos11.Enabled = false;
            this.fos11.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fos11.Location = new System.Drawing.Point(397, 229);
            this.fos11.Name = "fos11";
            this.fos11.NumericPrecision = 15;
            this.fos11.NumericScaleOnFocus = 2;
            this.fos11.NumericScaleOnLostFocus = 2;
            this.fos11.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fos11.ReadOnly = true;
            this.fos11.Size = new System.Drawing.Size(97, 22);
            this.fos11.TabIndex = 37;
            this.fos11.Text = "0";
            this.fos11.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fos11.ZeroIsValid = true;
            // 
            // fadj
            // 
            this.fadj.AllowNegative = true;
            this.fadj.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadj.Enabled = false;
            this.fadj.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadj.Location = new System.Drawing.Point(608, 341);
            this.fadj.Name = "fadj";
            this.fadj.NumericPrecision = 15;
            this.fadj.NumericScaleOnFocus = 2;
            this.fadj.NumericScaleOnLostFocus = 2;
            this.fadj.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadj.ReadOnly = true;
            this.fadj.Size = new System.Drawing.Size(114, 22);
            this.fadj.TabIndex = 51;
            this.fadj.Text = "0";
            this.fadj.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadj.ZeroIsValid = true;
            // 
            // frvos
            // 
            this.frvos.AllowNegative = true;
            this.frvos.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvos.Enabled = false;
            this.frvos.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvos.Location = new System.Drawing.Point(495, 341);
            this.frvos.Name = "frvos";
            this.frvos.NumericPrecision = 15;
            this.frvos.NumericScaleOnFocus = 2;
            this.frvos.NumericScaleOnLostFocus = 2;
            this.frvos.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvos.ReadOnly = true;
            this.frvos.Size = new System.Drawing.Size(112, 22);
            this.frvos.TabIndex = 50;
            this.frvos.Text = "0";
            this.frvos.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvos.ZeroIsValid = true;
            // 
            // fos
            // 
            this.fos.AllowNegative = true;
            this.fos.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fos.Enabled = false;
            this.fos.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fos.Location = new System.Drawing.Point(397, 341);
            this.fos.Name = "fos";
            this.fos.NumericPrecision = 15;
            this.fos.NumericScaleOnFocus = 2;
            this.fos.NumericScaleOnLostFocus = 2;
            this.fos.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fos.ReadOnly = true;
            this.fos.Size = new System.Drawing.Size(97, 22);
            this.fos.TabIndex = 49;
            this.fos.Text = "0";
            this.fos.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fos.ZeroIsValid = true;
            // 
            // fadjC
            // 
            this.fadjC.AllowNegative = true;
            this.fadjC.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadjC.Enabled = false;
            this.fadjC.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadjC.Location = new System.Drawing.Point(608, 295);
            this.fadjC.Name = "fadjC";
            this.fadjC.NumericPrecision = 15;
            this.fadjC.NumericScaleOnFocus = 2;
            this.fadjC.NumericScaleOnLostFocus = 2;
            this.fadjC.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadjC.ReadOnly = true;
            this.fadjC.Size = new System.Drawing.Size(114, 22);
            this.fadjC.TabIndex = 48;
            this.fadjC.Text = "0";
            this.fadjC.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadjC.ZeroIsValid = true;
            // 
            // frvosC
            // 
            this.frvosC.AllowNegative = true;
            this.frvosC.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvosC.Enabled = false;
            this.frvosC.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvosC.Location = new System.Drawing.Point(495, 295);
            this.frvosC.Name = "frvosC";
            this.frvosC.NumericPrecision = 15;
            this.frvosC.NumericScaleOnFocus = 2;
            this.frvosC.NumericScaleOnLostFocus = 2;
            this.frvosC.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvosC.ReadOnly = true;
            this.frvosC.Size = new System.Drawing.Size(112, 22);
            this.frvosC.TabIndex = 47;
            this.frvosC.Text = "0";
            this.frvosC.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvosC.ZeroIsValid = true;
            // 
            // fosC
            // 
            this.fosC.AllowNegative = true;
            this.fosC.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fosC.Enabled = false;
            this.fosC.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fosC.Location = new System.Drawing.Point(397, 295);
            this.fosC.Name = "fosC";
            this.fosC.NumericPrecision = 15;
            this.fosC.NumericScaleOnFocus = 2;
            this.fosC.NumericScaleOnLostFocus = 2;
            this.fosC.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fosC.ReadOnly = true;
            this.fosC.Size = new System.Drawing.Size(97, 22);
            this.fosC.TabIndex = 46;
            this.fosC.Text = "0";
            this.fosC.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fosC.ZeroIsValid = true;
            // 
            // fadj09
            // 
            this.fadj09.AllowNegative = true;
            this.fadj09.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadj09.Enabled = false;
            this.fadj09.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadj09.Location = new System.Drawing.Point(608, 181);
            this.fadj09.Name = "fadj09";
            this.fadj09.NumericPrecision = 15;
            this.fadj09.NumericScaleOnFocus = 2;
            this.fadj09.NumericScaleOnLostFocus = 2;
            this.fadj09.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadj09.ReadOnly = true;
            this.fadj09.Size = new System.Drawing.Size(114, 22);
            this.fadj09.TabIndex = 36;
            this.fadj09.Text = "0";
            this.fadj09.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadj09.ZeroIsValid = true;
            // 
            // frvos09
            // 
            this.frvos09.AllowNegative = true;
            this.frvos09.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvos09.Enabled = false;
            this.frvos09.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvos09.Location = new System.Drawing.Point(495, 181);
            this.frvos09.Name = "frvos09";
            this.frvos09.NumericPrecision = 15;
            this.frvos09.NumericScaleOnFocus = 2;
            this.frvos09.NumericScaleOnLostFocus = 2;
            this.frvos09.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvos09.ReadOnly = true;
            this.frvos09.Size = new System.Drawing.Size(112, 22);
            this.frvos09.TabIndex = 35;
            this.frvos09.Text = "0";
            this.frvos09.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvos09.ZeroIsValid = true;
            this.frvos09.Validated += new System.EventHandler(this.frvos09_Validated);
            // 
            // fos09
            // 
            this.fos09.AllowNegative = true;
            this.fos09.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fos09.Enabled = false;
            this.fos09.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fos09.Location = new System.Drawing.Point(397, 181);
            this.fos09.Name = "fos09";
            this.fos09.NumericPrecision = 15;
            this.fos09.NumericScaleOnFocus = 2;
            this.fos09.NumericScaleOnLostFocus = 2;
            this.fos09.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fos09.ReadOnly = true;
            this.fos09.Size = new System.Drawing.Size(97, 22);
            this.fos09.TabIndex = 34;
            this.fos09.Text = "0";
            this.fos09.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fos09.ZeroIsValid = true;
            // 
            // fadj08
            // 
            this.fadj08.AllowNegative = true;
            this.fadj08.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadj08.Enabled = false;
            this.fadj08.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadj08.Location = new System.Drawing.Point(608, 135);
            this.fadj08.Name = "fadj08";
            this.fadj08.NumericPrecision = 15;
            this.fadj08.NumericScaleOnFocus = 2;
            this.fadj08.NumericScaleOnLostFocus = 2;
            this.fadj08.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadj08.ReadOnly = true;
            this.fadj08.Size = new System.Drawing.Size(114, 22);
            this.fadj08.TabIndex = 33;
            this.fadj08.Text = "0";
            this.fadj08.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadj08.ZeroIsValid = true;
            // 
            // frvos08
            // 
            this.frvos08.AllowNegative = true;
            this.frvos08.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvos08.Enabled = false;
            this.frvos08.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvos08.Location = new System.Drawing.Point(495, 135);
            this.frvos08.Name = "frvos08";
            this.frvos08.NumericPrecision = 15;
            this.frvos08.NumericScaleOnFocus = 2;
            this.frvos08.NumericScaleOnLostFocus = 2;
            this.frvos08.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvos08.ReadOnly = true;
            this.frvos08.Size = new System.Drawing.Size(112, 22);
            this.frvos08.TabIndex = 32;
            this.frvos08.Text = "0";
            this.frvos08.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvos08.ZeroIsValid = true;
            this.frvos08.Validated += new System.EventHandler(this.frvos08_Validated);
            // 
            // fos08
            // 
            this.fos08.AllowNegative = true;
            this.fos08.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fos08.Enabled = false;
            this.fos08.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fos08.Location = new System.Drawing.Point(397, 135);
            this.fos08.Name = "fos08";
            this.fos08.NumericPrecision = 15;
            this.fos08.NumericScaleOnFocus = 2;
            this.fos08.NumericScaleOnLostFocus = 2;
            this.fos08.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fos08.ReadOnly = true;
            this.fos08.Size = new System.Drawing.Size(97, 22);
            this.fos08.TabIndex = 31;
            this.fos08.Text = "0";
            this.fos08.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fos08.ZeroIsValid = true;
            // 
            // fadjA
            // 
            this.fadjA.AllowNegative = true;
            this.fadjA.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fadjA.Enabled = false;
            this.fadjA.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fadjA.Location = new System.Drawing.Point(608, 84);
            this.fadjA.Name = "fadjA";
            this.fadjA.NumericPrecision = 15;
            this.fadjA.NumericScaleOnFocus = 2;
            this.fadjA.NumericScaleOnLostFocus = 2;
            this.fadjA.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fadjA.ReadOnly = true;
            this.fadjA.Size = new System.Drawing.Size(114, 22);
            this.fadjA.TabIndex = 30;
            this.fadjA.Text = "0";
            this.fadjA.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fadjA.ZeroIsValid = true;
            // 
            // frvosA
            // 
            this.frvosA.AllowNegative = true;
            this.frvosA.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.frvosA.Enabled = false;
            this.frvosA.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.frvosA.Location = new System.Drawing.Point(495, 84);
            this.frvosA.Name = "frvosA";
            this.frvosA.NumericPrecision = 15;
            this.frvosA.NumericScaleOnFocus = 2;
            this.frvosA.NumericScaleOnLostFocus = 2;
            this.frvosA.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.frvosA.ReadOnly = true;
            this.frvosA.Size = new System.Drawing.Size(112, 22);
            this.frvosA.TabIndex = 29;
            this.frvosA.Text = "0";
            this.frvosA.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.frvosA.ZeroIsValid = true;
            this.frvosA.Validated += new System.EventHandler(this.frvosA_Validated);
            // 
            // fosA
            // 
            this.fosA.AllowNegative = true;
            this.fosA.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            this.fosA.Enabled = false;
            this.fosA.Font = new System.Drawing.Font("Courier New", 9.75F);
            this.fosA.Location = new System.Drawing.Point(397, 84);
            this.fosA.Name = "fosA";
            this.fosA.NumericPrecision = 15;
            this.fosA.NumericScaleOnFocus = 2;
            this.fosA.NumericScaleOnLostFocus = 2;
            this.fosA.NumericValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.fosA.ReadOnly = true;
            this.fosA.Size = new System.Drawing.Size(97, 22);
            this.fosA.TabIndex = 28;
            this.fosA.Text = "0";
            this.fosA.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.fosA.ZeroIsValid = true;
            // 
            // bdwn_cl2
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.SteelBlue;
            this.ClientSize = new System.Drawing.Size(768, 547);
            this.Controls.Add(this.XAutoLbl);
            this.Controls.Add(this.fosB);
            this.Controls.Add(this.fosA2);
            this.Controls.Add(this.label14);
            this.Controls.Add(this.label10);
            this.Controls.Add(this.ftadjB);
            this.Controls.Add(this.ftadjA2);
            this.Controls.Add(this.ftadj13);
            this.Controls.Add(this.ftadj12);
            this.Controls.Add(this.ftadj11);
            this.Controls.Add(this.ftadj);
            this.Controls.Add(this.ftadjC);
            this.Controls.Add(this.ftadj09);
            this.Controls.Add(this.ftadj08);
            this.Controls.Add(this.ftadjA);
            this.Controls.Add(this.frvosB);
            this.Controls.Add(this.frvosA2);
            this.Controls.Add(this.fadjB);
            this.Controls.Add(this.fadjA2);
            this.Controls.Add(this.fadj13);
            this.Controls.Add(this.frvos13);
            this.Controls.Add(this.fos13);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.fadj12);
            this.Controls.Add(this.frvos12);
            this.Controls.Add(this.fos12);
            this.Controls.Add(this.fadj11);
            this.Controls.Add(this.frvos11);
            this.Controls.Add(this.fos11);
            this.Controls.Add(this.XcmdConfirm);
            this.Controls.Add(this.XcmdExit);
            this.Controls.Add(this.label36);
            this.Controls.Add(this.fremark_t);
            this.Controls.Add(this.fadj);
            this.Controls.Add(this.frvos);
            this.Controls.Add(this.fos);
            this.Controls.Add(this.fadjC);
            this.Controls.Add(this.frvosC);
            this.Controls.Add(this.fosC);
            this.Controls.Add(this.label33);
            this.Controls.Add(this.label32);
            this.Controls.Add(this.label31);
            this.Controls.Add(this.fadj09);
            this.Controls.Add(this.frvos09);
            this.Controls.Add(this.fos09);
            this.Controls.Add(this.fadj08);
            this.Controls.Add(this.frvos08);
            this.Controls.Add(this.fos08);
            this.Controls.Add(this.label30);
            this.Controls.Add(this.label28);
            this.Controls.Add(this.label27);
            this.Controls.Add(this.label26);
            this.Controls.Add(this.fadjA);
            this.Controls.Add(this.frvosA);
            this.Controls.Add(this.fosA);
            this.Controls.Add(this.label13);
            this.Controls.Add(this.label12);
            this.Controls.Add(this.label11);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.faccdate);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.flogseq);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.fclmno);
            this.Controls.Add(this.label66);
            this.Name = "bdwn_cl2";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Reserve Breakdown - Employee\'s Compensation";
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.TextBox fclmno;
        private System.Windows.Forms.Label label66;
        private System.Windows.Forms.TextBox flogseq;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox faccdate;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label13;
        private Shorty.Windows.Forms.NumericTextBox fadjA;
        private Shorty.Windows.Forms.NumericTextBox frvosA;
        private Shorty.Windows.Forms.NumericTextBox fosA;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.Label label30;
        private Shorty.Windows.Forms.NumericTextBox fadj08;
        private Shorty.Windows.Forms.NumericTextBox frvos08;
        private Shorty.Windows.Forms.NumericTextBox fos08;
        private Shorty.Windows.Forms.NumericTextBox fadj09;
        private Shorty.Windows.Forms.NumericTextBox frvos09;
        private Shorty.Windows.Forms.NumericTextBox fos09;
        private System.Windows.Forms.Label label31;
        private System.Windows.Forms.Label label32;
        private System.Windows.Forms.Label label33;
        private Shorty.Windows.Forms.NumericTextBox fadjC;
        private Shorty.Windows.Forms.NumericTextBox frvosC;
        private Shorty.Windows.Forms.NumericTextBox fosC;
        private Shorty.Windows.Forms.NumericTextBox fadj;
        private Shorty.Windows.Forms.NumericTextBox frvos;
        private Shorty.Windows.Forms.NumericTextBox fos;
        private System.Windows.Forms.TextBox fremark_t;
        private System.Windows.Forms.Label label36;
        private System.Windows.Forms.Button XcmdExit;
        private System.Windows.Forms.Button XcmdConfirm;
        private Shorty.Windows.Forms.NumericTextBox fadj11;
        private Shorty.Windows.Forms.NumericTextBox frvos11;
        private Shorty.Windows.Forms.NumericTextBox fos11;
        private Shorty.Windows.Forms.NumericTextBox fadj12;
        private Shorty.Windows.Forms.NumericTextBox frvos12;
        private Shorty.Windows.Forms.NumericTextBox fos12;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.Label label4;
        private Shorty.Windows.Forms.NumericTextBox fadj13;
        private Shorty.Windows.Forms.NumericTextBox frvos13;
        private Shorty.Windows.Forms.NumericTextBox fos13;
        private System.Windows.Forms.Label fadjA2;
        private System.Windows.Forms.Label fadjB;
        private System.Windows.Forms.Label frvosA2;
        private System.Windows.Forms.Label frvosB;
        private System.Windows.Forms.TextBox ftadj13;
        private System.Windows.Forms.TextBox ftadj12;
        private System.Windows.Forms.TextBox ftadj11;
        private System.Windows.Forms.TextBox ftadj;
        private System.Windows.Forms.TextBox ftadjC;
        private System.Windows.Forms.TextBox ftadj09;
        private System.Windows.Forms.TextBox ftadj08;
        private System.Windows.Forms.TextBox ftadjA;
        private System.Windows.Forms.Label ftadjB;
        private System.Windows.Forms.Label ftadjA2;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label fosB;
        private System.Windows.Forms.Label fosA2;
        private System.Windows.Forms.Label XAutoLbl;

    }
}