using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace INS.Forms
{
    public partial class reqcinv : Form
    {
        private tcvrlet tcvrlet;
        public string fctlid = "",key ="";
        public reqcinv()
        {
            InitializeComponent();
            InitCobBus();
        }
        public string GenPayee
        {
            get { return fpayee.Text; }
            set { fpayee.Text = value; }
        }
        public reqcinv(tcvrlet tcvrlet)
        {
            // TODO: Complete member initialization
            this.tcvrlet = tcvrlet;
            InitializeComponent();
            InitCobBus();
        }

        private void InitCobBus()
        {
            String[,] arr = new String[,] { { "Direct Claims", "D0200" }, { "Fac In Claims", "R0200" } };
            DataTable dt = new DataTable();
            dt.Columns.Add("String", typeof(String));
            dt.Columns.Add("Value", typeof(String));
            for (int i = 0; i < arr.GetLength(0); i++)
            {
                string strText = arr[i, 0], strValue = arr[i, 1];
                DataRow aRow = dt.NewRow();
                aRow[0] = strText;
                aRow[1] = strValue;
                dt.Rows.Add(aRow);
            }
            cobBus.DataSource = dt;
            cobBus.DisplayMember = "String";
            cobBus.ValueMember = "Value";
            fbilldate.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        public void query() {
            DateTime vfbk_fr, vfbk_to;
            vfbk_fr = DateTime.ParseExact(fbilldate.Text.Trim(), "yyyy.MM", System.Globalization.CultureInfo.InvariantCulture);
            vfbk_to = vfbk_fr.AddMonths(1).AddDays(-1);

            //string sql = "select * from opyinvh where fmodule = '" + cobBus.SelectedValue.ToString() + "' ";

            string sql = "select a.* from opyinvh a " +
                        "left join ocvrdet b on a.finvno = b.finvno  "+
                        "where (b.fctlid is null or abs(a.famount) - (isnull(abs(b.famount1),0)+isnull(abs(b.famount2),0)) <> 0) and a.fmodule = '" + cobBus.SelectedValue.ToString() + "' ";

            if (fbilldate.Text.Trim() != "    .")
            {
                sql = sql + "and (a.fbilldate between convert(datetime,'" + vfbk_fr.ToString("yyyy-MM-dd") + "',120)  and convert(datetime,'" + vfbk_to.ToString("yyyy-MM-dd") + "',120))  ";
            }
            if (finvno.Text != "")
            {
                sql = sql + " and a.finvno ='" + finvno.Text.Trim() + "'";
            }
            if (fpolno.Text != "")
            {
                sql = sql + " and a.fpolno ='" + fpolno.Text.Trim() + "'";
            }
            if (fclmno.Text != "")
            {
                sql = sql + " and a.fclmno ='" + fclmno.Text.Trim() + "'";
            }
            if (fcedepol.Text != "")
            {
                sql = sql + " and a.fcedepol ='" + fcedepol.Text.Trim() + "'";
            }
            if (fcedeclm.Text != "")
            {
                sql = sql + " and a.fcedeclm ='" + fcedeclm.Text.Trim() + "'";
            }
            if (fsitedesc.Text != "")
            {
                sql = sql + " and a.fsitedesc like '%" + fsitedesc.Text.Trim() + "%'";
            }
            if (fpayee.Text != "" || GenPayee !="")
            {
                if (fpayee.Text != "") { sql = sql + " and a.fpayee like '%" + fpayee.Text.Trim() + "%'"; }
                if (GenPayee != "") { sql = sql + " and a.fpayee like '%" + GenPayee.Trim() + "%'"; }
            }
            tcvrdet.DataSource = DBHelper.GetDataSet(sql);
            if (tcvrdet.RowCount > 0)
            {
                foreach (DataGridViewColumn Column in tcvrdet.Columns)
                {
                    Column.Visible = false;
                }
                tcvrdet.Columns["finvno"].Visible = true;
                tcvrdet.Columns["fpolno"].Visible = true;
                tcvrdet.Columns["fclmno"].Visible = true;
                tcvrdet.Columns["fpayee"].Visible = true;
                tcvrdet.Columns["famount"].Visible = true;
                tcvrdet.CurrentCell = tcvrdet.Rows[0].Cells["finvno"];
            }
            else
            {
                tcvrdet.Columns["finvno"].HeaderText = "Dr/Cr No.";
                tcvrdet.Columns["fpolno"].HeaderText = "Policy#";
                tcvrdet.Columns["fclmno"].HeaderText = "Claim No.";
                tcvrdet.Columns["fpayee"].HeaderText = "Payee";
                tcvrdet.Columns["famount"].HeaderText = "Amount"; ;
            }

            tcvrdet.CellFormatting +=
            new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.tcvrdet_CellFormatting);
        }

        private void button3_Click(object sender, EventArgs e)
        {
            query();
        }


        private void tcvrdet_CellFormatting(object sender,
     System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            tcvrdet.Columns["finvno"].HeaderText = "Dr/Cr No.";
            tcvrdet.Columns["fpolno"].HeaderText = "Policy#";
            tcvrdet.Columns["fclmno"].HeaderText = "Claim No.";
            tcvrdet.Columns["fpayee"].HeaderText = "Payee";
            tcvrdet.Columns["famount"].HeaderText = "Amount"; ;
            tcvrdet.Columns["famount"].DefaultCellStyle.Format = "N2";
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void btnconf_Click(object sender, EventArgs e)
        {
            tcvrlet.tinvlist(fctlid);
            this.Close();
        }

        private void tcvrdet_SelectionChanged(object sender, EventArgs e)
        {
            if (tcvrdet.CurrentCell != null)
            {
                int counter = tcvrdet.CurrentCell.RowIndex;
                if (tcvrdet.Rows[counter].Cells["fclass"].Value != null)
                {
                    fclass.Text = tcvrdet.Rows[counter].Cells["fclass"].Value.ToString().Trim();
                }
                if (tcvrdet.Rows[counter].Cells["fsclass"].Value != null)
                {
                    fsclass.Text = tcvrdet.Rows[counter].Cells["fsclass"].Value.ToString().Trim();
                }
                if (tcvrdet.Rows[counter].Cells["finvno"].Value != null)
                {
                    finvno2.Text = tcvrdet.Rows[counter].Cells["finvno"].Value.ToString().Trim();
                }
                if (tcvrdet.Rows[counter].Cells["fpolno"].Value != null)
                {
                    fpolno2.Text = tcvrdet.Rows[counter].Cells["fpolno"].Value.ToString().Trim();
                }
                if (tcvrdet.Rows[counter].Cells["fclmno"].Value != null)
                {
                    fclmno2.Text = tcvrdet.Rows[counter].Cells["fclmno"].Value.ToString().Trim();
                }
                if (tcvrdet.Rows[counter].Cells["fcedepol"].Value != null)
                {
                    fcedepol2.Text = tcvrdet.Rows[counter].Cells["fcedepol"].Value.ToString().Trim();
                }
                if (tcvrdet.Rows[counter].Cells["fcedeclm"].Value != null)
                {
                    fcedeclm2.Text = tcvrdet.Rows[counter].Cells["fcedeclm"].Value.ToString().Trim();
                }
                if (tcvrdet.Rows[counter].Cells["fsitedesc"].Value != null)
                {
                    fsitedesc2.Text = tcvrdet.Rows[counter].Cells["fsitedesc"].Value.ToString().Trim();
                }
                if (tcvrdet.Rows[counter].Cells["fpayee"].Value != null)
                {
                    fpayee2.Text = tcvrdet.Rows[counter].Cells["fpayee"].Value.ToString().Trim();
                }
                if (tcvrdet.Rows[counter].Cells["famount"].Value != null)
                {
                    famount.Text = tcvrdet.Rows[counter].Cells["famount"].Value.ToString().Trim();
                }
                if (tcvrdet.Rows[counter].Cells["fctlid"].Value != null)
                {
                    fctlid = tcvrdet.Rows[counter].Cells["fctlid"].Value.ToString().Trim();
                }
            }
        }
    }
}
