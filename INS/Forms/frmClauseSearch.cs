using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;
using INS.Ctrl;

namespace INS
{
    public partial class frmClauseSearch : Form
    {
        public frmClauseSearch()
        {
            InitializeComponent();
            FillData("", "");
        }

        public frmClauseSearch(DirectAttach directAttach)
        {
            this.directAttach = directAttach;
            InitializeComponent();
        }

        public frmClauseSearch(EndorAttach endorAttach)
        {
            // TODO: Complete member initialization
            this.endorAttach = endorAttach;
            InitializeComponent();
        }

        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "";
        public int attachlistrow =0;
        private String _fclass;
        private DirectAttach directAttach;
        private EndorAttach endorAttach;


        public String Fclass
        {
            set
            {
                _fclass = value;
            }
        }

        public void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            if (order == "" && query == "")
            {
                a = new SqlDataAdapter("select b.fid as Clause#, b.fdesc as Desc#, b.flogseq as Seq# from minsc a left join mclause b on a.fctlid=b.fctlid_a where a.fid ='" + _fclass + "' and fdoctype ='2' order by flogseq", c);
            }
            else if (order != "")
            {
                a = new SqlDataAdapter("select b.fid as Clause#, b.fdesc as Desc#, b.flogseq as Seq# from minsc a left join mclause b on a.fctlid=b.fctlid_a where a.fid ='" + _fclass + "' and fdoctype ='2' order by b.fid", c);
            }
            else if (query != "")
            {
                a = new SqlDataAdapter("select b.fid as Clause#, b.fdesc as Desc#, b.flogseq as Seq# from minsc a left join mclause b on a.fctlid=b.fctlid_a where a.fid ='" + _fclass + "' and fdoctype ='2' AND fdesc = '" + query + "'", c);
            }
            else
            {
                a = new SqlDataAdapter("select b.fid as Clause#, b.fdesc as Desc#, b.flogseq as Seq# from minsc a left join mclause b on a.fctlid=b.fctlid_a where a.fid ='" + _fclass + "' and fdoctype ='2' AND fdesc = '" + query + "' order by fid", c);
            }
            DataTable t = new DataTable();
            a.Fill(t);
            dataGridView1.DataSource = t;
            c.Close();
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {

            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }
                if (dataGridView1.Rows[row].Cells["Clause#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Clause#"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["Clause#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["Desc#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Seq#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Seq#"].Value.ToString().Length != 0)
                    {
                        label4.Text = dataGridView1.Rows[row].Cells["Seq#"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            if (flag == "Direct")
            {
                //string result2 = directAttach.CheckDuplicate(textBox1.Text.ToString().Trim(),"Click");
                //if (result2 != "")
                //{
                //    MessageBox.Show(result2);
                //}
                //else { 
                //    directAttach.ClauseValue = textBox1.Text.ToString().Trim();
                //    directAttach.ClauseDesc = label3.Text.ToString().Trim();
                //    directAttach.ClauseSeq = label4.Text.ToString().Trim();
                //    this.Close();
                //}

                directAttach.ClauseValue = textBox1.Text.ToString().Trim();
                directAttach.ClauseDesc = label3.Text.ToString().Trim();
                directAttach.ClauseContent = label3.Text.ToString().Trim();
                directAttach.ClauseSeq = label4.Text.ToString().Trim();
                this.Close();
            }
            if (flag == "Endor")
            {
                //string result2 = endorAttach.CheckDuplicate(textBox1.Text.ToString().Trim(), "Click");
                //if (result2 != "")
                //{
                //    MessageBox.Show(result2);
                //}
                //else
                //{
                //    endorAttach.ClauseValue = textBox1.Text.ToString().Trim();
                //    endorAttach.ClauseDesc = label3.Text.ToString().Trim();
                //    endorAttach.ClauseSeq = label4.Text.ToString().Trim();
                //    this.Close();
                //}

                endorAttach.ClauseValue = textBox1.Text.ToString().Trim();
                endorAttach.ClauseDesc = label3.Text.ToString().Trim();
                endorAttach.ClauseSeq = label4.Text.ToString().Trim();
                this.Close();
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
