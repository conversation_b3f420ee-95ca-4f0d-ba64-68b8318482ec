using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.SearchForm
{
    public partial class bdwn_ec2 : Form
    {
        public bool cEditMode = false;
        public Boolean Skip_Validate = false;
        private Business.SearchForm.bdwn_ec bdwn_ec;
        public bdwn_ec2()
        {
            InitializeComponent();
        }

        public bdwn_ec2(Business.SearchForm.bdwn_ec bdwn_ec)
        {
            // TODO: Complete member initialization
            this.bdwn_ec = bdwn_ec;
            InitializeComponent();
        }

        public void tpReload(string fctlid_a)
        {
            XcmdConfirm.Visible = false;
            string sql = "select * from ocabk_e a where fctlid_a= '" + fctlid_a + "'";
            DataTable dt = DBHelper.GetDataSet(sql);
            if (dt.Rows.Count > 0) {
                fttlday.Text = dt.Rows[0]["fttlday"].ToString();
                fnilpay1.Text = dt.Rows[0]["fnilpay1"].ToString();
                fnilpay2.Text = dt.Rows[0]["fnilpay2"].ToString();
                fnlength.Text = dt.Rows[0]["fnlength"].ToString();
                ffrm01.Text = dt.Rows[0]["ffrm01"].ToString();
                fto01.Text = dt.Rows[0]["fto01"].ToString();
                fnofday01.Text = dt.Rows[0]["fnofday01"].ToString();
                ffrm02.Text = dt.Rows[0]["ffrm02"].ToString();
                fto02.Text = dt.Rows[0]["fto02"].ToString();
                fnofday02.Text = dt.Rows[0]["fnofday02"].ToString();
                ffrm03.Text = dt.Rows[0]["ffrm03"].ToString();
                fto03.Text = dt.Rows[0]["fto03"].ToString();
                fnofday03.Text = dt.Rows[0]["fnofday03"].ToString();
                ffrm04.Text = dt.Rows[0]["ffrm04"].ToString();
                fto04.Text = dt.Rows[0]["fto04"].ToString();
                fnofday04.Text = dt.Rows[0]["fnofday04"].ToString();
                ffrm05.Text = dt.Rows[0]["ffrm05"].ToString();
                fto05.Text = dt.Rows[0]["fto05"].ToString();
                fnofday05.Text = dt.Rows[0]["fnofday05"].ToString();
                ffrm06.Text = dt.Rows[0]["ffrm06"].ToString();
                fto06.Text = dt.Rows[0]["fto06"].ToString();
                fnofday06.Text = dt.Rows[0]["fnofday06"].ToString();
                ffrm07.Text = dt.Rows[0]["ffrm07"].ToString();
                fto07.Text = dt.Rows[0]["fto07"].ToString();
                fnofday07.Text = dt.Rows[0]["fnofday07"].ToString();
                ffrm08.Text = dt.Rows[0]["ffrm08"].ToString();
                fto08.Text = dt.Rows[0]["fto08"].ToString();
                fnofday08.Text = dt.Rows[0]["fnofday08"].ToString();
                ffrm09.Text = dt.Rows[0]["ffrm09"].ToString();
                fto09.Text = dt.Rows[0]["fto09"].ToString();
                fnofday09.Text = dt.Rows[0]["fnofday09"].ToString();
                ffrm10.Text = dt.Rows[0]["ffrm10"].ToString();
                fto10.Text = dt.Rows[0]["fto10"].ToString();
                fnofday10.Text = dt.Rows[0]["fnofday10"].ToString();
                ffrm11.Text = dt.Rows[0]["ffrm11"].ToString();
                fto11.Text = dt.Rows[0]["fto11"].ToString();
                fnofday11.Text = dt.Rows[0]["fnofday11"].ToString();
                ffrm12.Text = dt.Rows[0]["ffrm12"].ToString();
                fto12.Text = dt.Rows[0]["fto12"].ToString();
                fnofday12.Text = dt.Rows[0]["fnofday12"].ToString();
                ffrm13.Text = dt.Rows[0]["ffrm13"].ToString();
                fto13.Text = dt.Rows[0]["fto13"].ToString();
                fnofday13.Text = dt.Rows[0]["fnofday13"].ToString();
                ffrm14.Text = dt.Rows[0]["ffrm14"].ToString();
                fto14.Text = dt.Rows[0]["fto14"].ToString();
                fnofday14.Text = dt.Rows[0]["fnofday14"].ToString();
                ffrm15.Text = dt.Rows[0]["ffrm15"].ToString();
                fto15.Text = dt.Rows[0]["fto15"].ToString();
                fnofday15.Text = dt.Rows[0]["fnofday15"].ToString();
            }
        }

        void ButtonAdd()
        {
            XcmdExit.Visible = false;
            ffrm15.BackColor = System.Drawing.Color.White;
            ffrm15.ReadOnly = !cEditMode;
            fto15.BackColor = System.Drawing.Color.White;
            fto15.ReadOnly = !cEditMode;
            ffrm14.BackColor = System.Drawing.Color.White;
            ffrm14.ReadOnly = !cEditMode;
            fto14.BackColor = System.Drawing.Color.White;
            fto14.ReadOnly = !cEditMode;
            ffrm13.BackColor = System.Drawing.Color.White;
            ffrm13.ReadOnly = !cEditMode;
            fto13.BackColor = System.Drawing.Color.White;
            fto13.ReadOnly = !cEditMode;
            ffrm12.BackColor = System.Drawing.Color.White;
            ffrm12.ReadOnly = !cEditMode;
            fto12.BackColor = System.Drawing.Color.White;
            fto12.ReadOnly = !cEditMode;
            ffrm11.BackColor = System.Drawing.Color.White;
            ffrm11.ReadOnly = !cEditMode;
            fto11.BackColor = System.Drawing.Color.White;
            fto11.ReadOnly = !cEditMode;
            ffrm10.BackColor = System.Drawing.Color.White;
            ffrm10.ReadOnly = !cEditMode;
            fto10.BackColor = System.Drawing.Color.White;
            fto10.ReadOnly = !cEditMode;
            ffrm09.BackColor = System.Drawing.Color.White;
            ffrm09.ReadOnly = !cEditMode;
            fto09.BackColor = System.Drawing.Color.White;
            fto09.ReadOnly = !cEditMode;
            ffrm08.BackColor = System.Drawing.Color.White;
            ffrm08.ReadOnly = !cEditMode;
            fto08.BackColor = System.Drawing.Color.White;
            fto08.ReadOnly = !cEditMode;
            ffrm07.BackColor = System.Drawing.Color.White;
            ffrm07.ReadOnly = !cEditMode;
            fto07.BackColor = System.Drawing.Color.White;
            fto07.ReadOnly = !cEditMode;
            ffrm06.BackColor = System.Drawing.Color.White;
            ffrm06.ReadOnly = !cEditMode;
            fto06.BackColor = System.Drawing.Color.White;
            fto06.ReadOnly = !cEditMode;
            ffrm05.BackColor = System.Drawing.Color.White;
            ffrm05.ReadOnly = !cEditMode;
            fto05.BackColor = System.Drawing.Color.White;
            fto05.ReadOnly = !cEditMode;
            ffrm04.BackColor = System.Drawing.Color.White;
            ffrm04.ReadOnly = !cEditMode;
            fto04.BackColor = System.Drawing.Color.White;
            fto04.ReadOnly = !cEditMode;
            ffrm03.BackColor = System.Drawing.Color.White;
            ffrm03.ReadOnly = !cEditMode;
            fto03.BackColor = System.Drawing.Color.White;
            fto03.ReadOnly = !cEditMode;
            ffrm02.BackColor = System.Drawing.Color.White;
            ffrm02.ReadOnly = !cEditMode;
            fto02.BackColor = System.Drawing.Color.White;
            fto02.ReadOnly = !cEditMode;
            ffrm01.BackColor = System.Drawing.Color.White;
            ffrm01.ReadOnly = !cEditMode;
            fto01.BackColor = System.Drawing.Color.White;
            fto01.ReadOnly = !cEditMode;
            fnilpay1.BackColor = System.Drawing.Color.White;
            fnilpay1.ReadOnly = !cEditMode;
            fnilpay2.BackColor = System.Drawing.Color.White;
            fnilpay2.ReadOnly = !cEditMode;
        }

        public void tpAdd()
        {
           XcmdConfirm.Visible = true;
           DataTable dt = bdwn_ec.ocabk_e;
           if (dt.Rows.Count > 0)
           {
               fttlday.Text = dt.Rows[0]["fttlday"].ToString();
               fnilpay1.Text = dt.Rows[0]["fnilpay1"].ToString();
               fnilpay2.Text = dt.Rows[0]["fnilpay2"].ToString();
               fnlength.Text = dt.Rows[0]["fnlength"].ToString();
               if (dt.Rows[0]["ffrm01"].ToString() != ""){
                   ffrm01.Text = Convert.ToDateTime(dt.Rows[0]["ffrm01"].ToString()).ToString("yyyy.MM.dd");}
               else { ffrm01.Text = dt.Rows[0]["ffrm01"].ToString(); }
               if (dt.Rows[0]["fto01"].ToString() != ""){
                   fto01.Text = Convert.ToDateTime(dt.Rows[0]["fto01"].ToString()).ToString("yyyy.MM.dd");}
               else { fto01.Text = dt.Rows[0]["fto01"].ToString(); }
               fnofday01.Text = dt.Rows[0]["fnofday01"].ToString();

               if (dt.Rows[0]["ffrm02"].ToString() != "")
               {
                   ffrm02.Text = Convert.ToDateTime(dt.Rows[0]["ffrm02"].ToString()).ToString("yyyy.MM.dd");
               }
               else { ffrm02.Text = dt.Rows[0]["ffrm02"].ToString(); }
               if (dt.Rows[0]["fto02"].ToString() != "")
               {
                   fto02.Text = Convert.ToDateTime(dt.Rows[0]["fto02"].ToString()).ToString("yyyy.MM.dd");
               }
               else { fto02.Text = dt.Rows[0]["fto02"].ToString(); }
               fnofday02.Text = dt.Rows[0]["fnofday02"].ToString();

               if (dt.Rows[0]["ffrm03"].ToString() != "")
               {
                   ffrm03.Text = Convert.ToDateTime(dt.Rows[0]["ffrm03"].ToString()).ToString("yyyy.MM.dd");
               }
               else { ffrm03.Text = dt.Rows[0]["ffrm03"].ToString(); }
               if (dt.Rows[0]["fto03"].ToString() != "")
               {
                   fto03.Text = Convert.ToDateTime(dt.Rows[0]["fto03"].ToString()).ToString("yyyy.MM.dd");
               }
               else { fto03.Text = dt.Rows[0]["fto03"].ToString(); }
               fnofday03.Text = dt.Rows[0]["fnofday03"].ToString();

               if (dt.Rows[0]["ffrm04"].ToString() != "")
               {
                   ffrm04.Text = Convert.ToDateTime(dt.Rows[0]["ffrm04"].ToString()).ToString("yyyy.MM.dd");
               }
               else { ffrm04.Text = dt.Rows[0]["ffrm04"].ToString(); }
               if (dt.Rows[0]["fto04"].ToString() != "")
               {
                   fto04.Text = Convert.ToDateTime(dt.Rows[0]["fto04"].ToString()).ToString("yyyy.MM.dd");
               }
               else { fto04.Text = dt.Rows[0]["fto04"].ToString(); }
               fnofday04.Text = dt.Rows[0]["fnofday04"].ToString();

               if (dt.Rows[0]["ffrm05"].ToString() != "")
               {
                   ffrm05.Text = Convert.ToDateTime(dt.Rows[0]["ffrm05"].ToString()).ToString("yyyy.MM.dd");
               }
               else { ffrm05.Text = dt.Rows[0]["ffrm05"].ToString(); }
               if (dt.Rows[0]["fto05"].ToString() != "")
               {
                   fto05.Text = Convert.ToDateTime(dt.Rows[0]["fto05"].ToString()).ToString("yyyy.MM.dd");
               }
               else { fto05.Text = dt.Rows[0]["fto05"].ToString(); }
               fnofday05.Text = dt.Rows[0]["fnofday05"].ToString();

               if (dt.Rows[0]["ffrm06"].ToString() != "")
               {
                   ffrm06.Text = Convert.ToDateTime(dt.Rows[0]["ffrm06"].ToString()).ToString("yyyy.MM.dd");
               }
               else { ffrm06.Text = dt.Rows[0]["ffrm06"].ToString(); }
               if (dt.Rows[0]["fto06"].ToString() != "")
               {
                   fto06.Text = Convert.ToDateTime(dt.Rows[0]["fto06"].ToString()).ToString("yyyy.MM.dd");
               }
               else { fto06.Text = dt.Rows[0]["fto06"].ToString(); }
               fnofday06.Text = dt.Rows[0]["fnofday06"].ToString();

               if (dt.Rows[0]["ffrm07"].ToString() != "")
               {
                   ffrm07.Text = Convert.ToDateTime(dt.Rows[0]["ffrm07"].ToString()).ToString("yyyy.MM.dd");
               }
               else { ffrm07.Text = dt.Rows[0]["ffrm07"].ToString(); }
               if (dt.Rows[0]["fto07"].ToString() != "")
               {
                   fto07.Text = Convert.ToDateTime(dt.Rows[0]["fto07"].ToString()).ToString("yyyy.MM.dd");
               }
               else { fto07.Text = dt.Rows[0]["fto07"].ToString(); }
               fnofday07.Text = dt.Rows[0]["fnofday07"].ToString();

               if (dt.Rows[0]["ffrm08"].ToString() != "")
               {
                   ffrm08.Text = Convert.ToDateTime(dt.Rows[0]["ffrm08"].ToString()).ToString("yyyy.MM.dd");
               }
               else { ffrm08.Text = dt.Rows[0]["ffrm08"].ToString(); }
               if (dt.Rows[0]["fto08"].ToString() != "")
               {
                   fto08.Text = Convert.ToDateTime(dt.Rows[0]["fto08"].ToString()).ToString("yyyy.MM.dd");
               }
               else { fto08.Text = dt.Rows[0]["fto08"].ToString(); }
               fnofday08.Text = dt.Rows[0]["fnofday08"].ToString();

               if (dt.Rows[0]["ffrm09"].ToString() != "")
               {
                   ffrm09.Text = Convert.ToDateTime(dt.Rows[0]["ffrm09"].ToString()).ToString("yyyy.MM.dd");
               }
               else { ffrm09.Text = dt.Rows[0]["ffrm09"].ToString(); }
               if (dt.Rows[0]["fto09"].ToString() != "")
               {
                   fto09.Text = Convert.ToDateTime(dt.Rows[0]["fto09"].ToString()).ToString("yyyy.MM.dd");
               }
               else { fto09.Text = dt.Rows[0]["fto09"].ToString(); }
               fnofday09.Text = dt.Rows[0]["fnofday09"].ToString();

               if (dt.Rows[0]["ffrm10"].ToString() != "")
               {
                   ffrm10.Text = Convert.ToDateTime(dt.Rows[0]["ffrm10"].ToString()).ToString("yyyy.MM.dd");
               }
               else { ffrm10.Text = dt.Rows[0]["ffrm10"].ToString(); }
               if (dt.Rows[0]["fto10"].ToString() != "")
               {
                   fto10.Text = Convert.ToDateTime(dt.Rows[0]["fto10"].ToString()).ToString("yyyy.MM.dd");
               }
               else { fto10.Text = dt.Rows[0]["fto10"].ToString(); }
               fnofday10.Text = dt.Rows[0]["fnofday10"].ToString();

               if (dt.Rows[0]["ffrm11"].ToString() != "")
               {
                   ffrm11.Text = Convert.ToDateTime(dt.Rows[0]["ffrm11"].ToString()).ToString("yyyy.MM.dd");
               }
               else { ffrm11.Text = dt.Rows[0]["ffrm11"].ToString(); }
               if (dt.Rows[0]["fto11"].ToString() != "")
               {
                   fto11.Text = Convert.ToDateTime(dt.Rows[0]["fto11"].ToString()).ToString("yyyy.MM.dd");
               }
               else { fto11.Text = dt.Rows[0]["fto11"].ToString(); }
               fnofday11.Text = dt.Rows[0]["fnofday11"].ToString();

               if (dt.Rows[0]["ffrm12"].ToString() != "")
               {
                   ffrm12.Text = Convert.ToDateTime(dt.Rows[0]["ffrm12"].ToString()).ToString("yyyy.MM.dd");
               }
               else { ffrm12.Text = dt.Rows[0]["ffrm12"].ToString(); }
               if (dt.Rows[0]["fto12"].ToString() != "")
               {
                   fto12.Text = Convert.ToDateTime(dt.Rows[0]["fto12"].ToString()).ToString("yyyy.MM.dd");
               }
               else { fto12.Text = dt.Rows[0]["fto12"].ToString(); }
               fnofday12.Text = dt.Rows[0]["fnofday12"].ToString();

               if (dt.Rows[0]["ffrm13"].ToString() != "")
               {
                   ffrm13.Text = Convert.ToDateTime(dt.Rows[0]["ffrm13"].ToString()).ToString("yyyy.MM.dd");
               }
               else { ffrm13.Text = dt.Rows[0]["ffrm13"].ToString(); }
               if (dt.Rows[0]["fto13"].ToString() != "")
               {
                   fto13.Text = Convert.ToDateTime(dt.Rows[0]["fto13"].ToString()).ToString("yyyy.MM.dd");
               }
               else { fto13.Text = dt.Rows[0]["fto13"].ToString(); }
               fnofday13.Text = dt.Rows[0]["fnofday13"].ToString();

               if (dt.Rows[0]["ffrm14"].ToString() != "")
               {
                   ffrm14.Text = Convert.ToDateTime(dt.Rows[0]["ffrm14"].ToString()).ToString("yyyy.MM.dd");
               }
               else { ffrm14.Text = dt.Rows[0]["ffrm14"].ToString(); }
               if (dt.Rows[0]["fto14"].ToString() != "")
               {
                   fto14.Text = Convert.ToDateTime(dt.Rows[0]["fto14"].ToString()).ToString("yyyy.MM.dd");
               }
               else { fto14.Text = dt.Rows[0]["fto14"].ToString(); }
               fnofday14.Text = dt.Rows[0]["fnofday14"].ToString();

               if (dt.Rows[0]["ffrm15"].ToString() != "")
               {
                   ffrm15.Text = Convert.ToDateTime(dt.Rows[0]["ffrm15"].ToString()).ToString("yyyy.MM.dd");
               }
               else { ffrm15.Text = dt.Rows[0]["ffrm15"].ToString(); }
               if (dt.Rows[0]["fto15"].ToString() != "")
               {
                   fto15.Text = Convert.ToDateTime(dt.Rows[0]["fto15"].ToString()).ToString("yyyy.MM.dd");
               }
               else { fto15.Text = dt.Rows[0]["fto15"].ToString(); }
               fnofday15.Text = dt.Rows[0]["fnofday15"].ToString();
           }
           ButtonAdd();
        }

        private void XPeriod01_Leave(object sender, EventArgs e)
        {
            if (!Skip_Validate)
            {
                DateTime value;
                if (!DateTime.TryParse(ffrm01.Text, out value) || !DateTime.TryParse(fto01.Text, out value))
                {
                    MessageBox.Show("Invalid Date");
                    ffrm01.Focus();
                }
                else
                {
                    DateTime startDate = Convert.ToDateTime(ffrm01.Text);
                    DateTime endDate = Convert.ToDateTime(fto01.Text);
                    if (endDate < startDate)
                    {
                        MessageBox.Show("Invalid Date");
                        ffrm01.Focus();
                    }
                    else { u_calvalue("XPERIOD01"); }
                }
            }
        }

        private void XPeriod02_Leave(object sender, EventArgs e)
        {
            if (!Skip_Validate)
            {
                DateTime value;
                if (!DateTime.TryParse(ffrm02.Text, out value) || !DateTime.TryParse(fto02.Text, out value))
                {
                    MessageBox.Show("Invalid Date");
                    ffrm02.Focus();
                }
                else
                {
                    DateTime startDate = Convert.ToDateTime(ffrm02.Text);
                    DateTime endDate = Convert.ToDateTime(fto02.Text);
                    if (endDate < startDate)
                    {
                        MessageBox.Show("Invalid Date");
                        ffrm02.Focus();
                    }
                    else { u_calvalue("XPERIOD02"); }
                }
            }
        }

        private void XPeriod03_Leave(object sender, EventArgs e)
        {
            if (!Skip_Validate)
            {
                DateTime value;
                if (!DateTime.TryParse(ffrm03.Text, out value) || !DateTime.TryParse(fto03.Text, out value))
                {
                    MessageBox.Show("Invalid Date");
                    ffrm03.Focus();
                }
                else
                {
                    DateTime startDate = Convert.ToDateTime(ffrm03.Text);
                    DateTime endDate = Convert.ToDateTime(fto03.Text);
                    if (endDate < startDate)
                    {
                        MessageBox.Show("Invalid Date");
                        ffrm03.Focus();
                    }
                    else { u_calvalue("XPERIOD03"); }
                }
            }
        }

        private void XPeriod04_Leave(object sender, EventArgs e)
        {
            if (!Skip_Validate)
            {
                DateTime value;
                if (!DateTime.TryParse(ffrm04.Text, out value) || !DateTime.TryParse(fto04.Text, out value))
                {
                    MessageBox.Show("Invalid Date");
                    ffrm04.Focus();
                }
                else
                {
                    DateTime startDate = Convert.ToDateTime(ffrm04.Text);
                    DateTime endDate = Convert.ToDateTime(fto04.Text);
                    if (endDate < startDate)
                    {
                        MessageBox.Show("Invalid Date");
                        ffrm04.Focus();
                    }
                    else { u_calvalue("XPERIOD04"); }
                }
            }
        }

        private void XPeriod05_Leave(object sender, EventArgs e)
        {
            if (!Skip_Validate)
            {
                DateTime value;
                if (!DateTime.TryParse(ffrm05.Text, out value) || !DateTime.TryParse(fto05.Text, out value))
                {
                    MessageBox.Show("Invalid Date");
                    ffrm05.Focus();
                }
                else
                {
                    DateTime startDate = Convert.ToDateTime(ffrm05.Text);
                    DateTime endDate = Convert.ToDateTime(fto05.Text);
                    if (endDate < startDate)
                    {
                        MessageBox.Show("Invalid Date");
                        ffrm05.Focus();
                    }
                    else { u_calvalue("XPERIOD05"); }
                }
            }
        }

        private void XPeriod06_Leave(object sender, EventArgs e)
        {
            if (!Skip_Validate)
            {
                DateTime value;
                if (!DateTime.TryParse(ffrm06.Text, out value) || !DateTime.TryParse(fto06.Text, out value))
                {
                    MessageBox.Show("Invalid Date");
                    ffrm06.Focus();
                }
                else
                {
                    DateTime startDate = Convert.ToDateTime(ffrm06.Text);
                    DateTime endDate = Convert.ToDateTime(fto06.Text);
                    if (endDate < startDate)
                    {
                        MessageBox.Show("Invalid Date");
                        ffrm06.Focus();
                    }
                    else { u_calvalue("XPERIOD06"); }
                }
            }
        }

        private void XPeriod07_Leave(object sender, EventArgs e)
        {
            if (!Skip_Validate)
            {
                DateTime value;
                if (!DateTime.TryParse(ffrm07.Text, out value) || !DateTime.TryParse(fto07.Text, out value))
                {
                    MessageBox.Show("Invalid Date");
                    ffrm07.Focus();
                }
                else
                {
                    DateTime startDate = Convert.ToDateTime(ffrm07.Text);
                    DateTime endDate = Convert.ToDateTime(fto07.Text);
                    if (endDate < startDate)
                    {
                        MessageBox.Show("Invalid Date");
                        ffrm07.Focus();
                    }
                    else { u_calvalue("XPERIOD07"); }
                }
            }
        }

        private void XPeriod08_Leave(object sender, EventArgs e)
        {
            if (!Skip_Validate)
            {
                DateTime value;
                if (!DateTime.TryParse(ffrm08.Text, out value) || !DateTime.TryParse(fto08.Text, out value))
                {
                    MessageBox.Show("Invalid Date");
                    ffrm08.Focus();
                }
                else
                {
                    DateTime startDate = Convert.ToDateTime(ffrm08.Text);
                    DateTime endDate = Convert.ToDateTime(fto08.Text);
                    if (endDate < startDate)
                    {
                        MessageBox.Show("Invalid Date");
                        ffrm08.Focus();
                    }
                    else { u_calvalue("XPERIOD08"); }
                }
            }
        }

        private void XPeriod09_Leave(object sender, EventArgs e)
        {
            if (!Skip_Validate)
            {
                DateTime value;
                if (!DateTime.TryParse(ffrm09.Text, out value) || !DateTime.TryParse(fto09.Text, out value))
                {
                    MessageBox.Show("Invalid Date");
                    ffrm09.Focus();
                }
                else
                {
                    DateTime startDate = Convert.ToDateTime(ffrm09.Text);
                    DateTime endDate = Convert.ToDateTime(fto09.Text);
                    if (endDate < startDate)
                    {
                        MessageBox.Show("Invalid Date");
                        ffrm09.Focus();
                    }
                    else { u_calvalue("XPERIOD09"); }
                }
            }
        }

        private void XPeriod10_Leave(object sender, EventArgs e)
        {
            if (!Skip_Validate)
            {
                DateTime value;
                if (!DateTime.TryParse(ffrm10.Text, out value) || !DateTime.TryParse(fto10.Text, out value))
                {
                    MessageBox.Show("Invalid Date");
                    ffrm10.Focus();
                }
                else
                {
                    DateTime startDate = Convert.ToDateTime(ffrm10.Text);
                    DateTime endDate = Convert.ToDateTime(fto10.Text);
                    if (endDate < startDate)
                    {
                        MessageBox.Show("Invalid Date");
                        ffrm10.Focus();
                    }
                    else { u_calvalue("XPERIOD10"); }
                }
            }
        }

        private void XPeriod11_Leave(object sender, EventArgs e)
        {
            if (!Skip_Validate)
            {
                DateTime value;
                if (!DateTime.TryParse(ffrm11.Text, out value) || !DateTime.TryParse(fto11.Text, out value))
                {
                    MessageBox.Show("Invalid Date");
                    ffrm11.Focus();
                }
                else
                {
                    DateTime startDate = Convert.ToDateTime(ffrm11.Text);
                    DateTime endDate = Convert.ToDateTime(fto11.Text);
                    if (endDate < startDate)
                    {
                        MessageBox.Show("Invalid Date");
                        ffrm11.Focus();
                    }
                    else { u_calvalue("XPERIOD11"); }
                }
            }
        }

        private void XPeriod12_Leave(object sender, EventArgs e)
        {
            if (!Skip_Validate)
            {
                DateTime value;
                if (!DateTime.TryParse(ffrm12.Text, out value) || !DateTime.TryParse(fto12.Text, out value))
                {
                    MessageBox.Show("Invalid Date");
                    ffrm12.Focus();
                }
                else
                {
                    DateTime startDate = Convert.ToDateTime(ffrm12.Text);
                    DateTime endDate = Convert.ToDateTime(fto12.Text);
                    if (endDate < startDate)
                    {
                        MessageBox.Show("Invalid Date");
                        ffrm12.Focus();
                    }
                    else { u_calvalue("XPERIOD12"); }
                }
            }
        }

        private void XPeriod13_Leave(object sender, EventArgs e)
        {
            if (!Skip_Validate)
            {
                DateTime value;
                if (!DateTime.TryParse(ffrm13.Text, out value) || !DateTime.TryParse(fto13.Text, out value))
                {
                    MessageBox.Show("Invalid Date");
                    ffrm13.Focus();
                }
                else
                {
                    DateTime startDate = Convert.ToDateTime(ffrm13.Text);
                    DateTime endDate = Convert.ToDateTime(fto13.Text);
                    if (endDate < startDate)
                    {
                        MessageBox.Show("Invalid Date");
                        ffrm13.Focus();
                    }
                    else { u_calvalue("XPERIOD13"); }
                }
            }
        }

        private void XPeriod14_Leave(object sender, EventArgs e)
        {
            if (!Skip_Validate)
            {
                DateTime value;
                if (!DateTime.TryParse(ffrm14.Text, out value) || !DateTime.TryParse(fto14.Text, out value))
                {
                    MessageBox.Show("Invalid Date");
                    ffrm14.Focus();
                }
                else
                {
                    DateTime startDate = Convert.ToDateTime(ffrm14.Text);
                    DateTime endDate = Convert.ToDateTime(fto14.Text);
                    if (endDate < startDate)
                    {
                        MessageBox.Show("Invalid Date");
                        ffrm14.Focus();
                    }
                    else { u_calvalue("XPERIOD14"); }
                }
            }
        }

        private void XPeriod15_Leave(object sender, EventArgs e)
        {
            if (!Skip_Validate)
            {
                DateTime value;
                if (!DateTime.TryParse(ffrm15.Text, out value) || !DateTime.TryParse(fto15.Text, out value))
                {
                    MessageBox.Show("Invalid Date");
                    ffrm15.Focus();
                }
                else
                {
                    DateTime startDate = Convert.ToDateTime(ffrm15.Text);
                    DateTime endDate = Convert.ToDateTime(fto15.Text);
                    if (endDate < startDate)
                    {
                        MessageBox.Show("Invalid Date");
                        ffrm15.Focus();
                    }
                    else { u_calvalue("XPERIOD15"); }
                }
            }
        }

        void u_calvalue(string p_varname) { 
            string lc_index="";decimal ln_nofdays = 0;
            if (p_varname.Contains("XPERIOD")){
                lc_index = p_varname.Substring(7, 2);
                MaskedTextBox ffrm = (MaskedTextBox)Controls.Find(string.Format("{0}", "ffrm" + lc_index), true).FirstOrDefault();
                MaskedTextBox fto = (MaskedTextBox)Controls.Find(string.Format("{0}", "fto" + lc_index), true).FirstOrDefault();
                TextBox fnofday = (TextBox)Controls.Find(string.Format("{0}", "fnofday" + lc_index), true).FirstOrDefault();
                if (ffrm.Text == ".  ." || fto.Text == ".  .") { ln_nofdays = 0; }
                else {
                    DateTime startDate = Convert.ToDateTime(ffrm.Text);
                    DateTime endDate = Convert.ToDateTime(fto.Text);
                    TimeSpan t = endDate - startDate;
                    ln_nofdays = t.Days + 1;
                }

                fttlday.Text = Fct.stFat(Fct.sdFat(fttlday.Text) - Fct.sdFat(fnofday.Text) + ln_nofdays);
                fnlength.Text = Fct.stFat(Fct.sdFat(fnlength.Text) - Fct.sdFat(fnofday.Text) + ln_nofdays);
                fnofday.Text = Fct.stFat(ln_nofdays);
            }
             if (p_varname == "FNILPAY"){
                fnlength.Text =  Fct.stFat(Fct.sdFat(fttlday.Text) - Fct.sdFat(fnilpay1.Text) - Fct.sdFat(fnilpay2.Text));
             }
        }

        private void XcmdConfirm_Click(object sender, EventArgs e)
        {
            if (bdwn_ec.ocabk_e.Rows.Count == 0)
            {
                DataRow newRow = bdwn_ec.ocabk_e.NewRow();
                bdwn_ec.ocabk_e.Rows.Add(newRow);
            }
            foreach (DataRow dr in bdwn_ec.ocabk_e.Rows)
            {
                if (ffrm01.Text == "    .  .") { dr["ffrm01"] = DBNull.Value; }
                else { dr["ffrm01"] = ffrm01.Text; }
                if (fto01.Text == "    .  .") { dr["fto01"] = DBNull.Value; }
                else { dr["fto01"] = fto01.Text; }
                dr["fnofday01"] = Fct.sdFat(fnofday01.Text);
                if (ffrm02.Text == "    .  .") { dr["ffrm02"] = DBNull.Value; }
                else { dr["ffrm02"] = ffrm02.Text; }
                if (fto02.Text == "    .  .") { dr["fto02"] = DBNull.Value; }
                else { dr["fto02"] = fto02.Text; }
                dr["fnofday02"] = Fct.sdFat(fnofday02.Text);
                if (ffrm03.Text == "    .  .") { dr["ffrm03"] = DBNull.Value; }
                else { dr["ffrm03"] = ffrm03.Text; }
                if (fto03.Text == "    .  .") { dr["fto03"] = DBNull.Value; }
                else { dr["fto03"] = fto03.Text; }
                dr["fnofday03"] = Fct.sdFat(fnofday03.Text);
                if (ffrm04.Text == "    .  .") { dr["ffrm04"] = DBNull.Value; }
                else { dr["ffrm04"] = ffrm04.Text; }
                if (fto04.Text == "    .  .") { dr["fto04"] = DBNull.Value; }
                else { dr["fto04"] = fto04.Text; }
                dr["fnofday04"] = Fct.sdFat(fnofday04.Text);
                if (ffrm05.Text == "    .  .") { dr["ffrm05"] = DBNull.Value; }
                else { dr["ffrm05"] = ffrm05.Text; }
                if (fto05.Text == "    .  .") { dr["fto05"] = DBNull.Value; }
                else { dr["fto05"] = fto05.Text; }
                dr["fnofday05"] = Fct.sdFat(fnofday05.Text);
                if (ffrm06.Text == "    .  .") { dr["ffrm06"] = DBNull.Value; }
                else { dr["ffrm06"] = ffrm06.Text; }
                if (fto06.Text == "    .  .") { dr["fto06"] = DBNull.Value; }
                else { dr["fto06"] = fto06.Text; }
                dr["fnofday06"] = Fct.sdFat(fnofday06.Text);
                if (ffrm07.Text == "    .  .") { dr["ffrm07"] = DBNull.Value; }
                else { dr["ffrm07"] = ffrm07.Text; }
                if (fto07.Text == "    .  .") { dr["fto07"] = DBNull.Value; }
                else { dr["fto07"] = fto07.Text; }
                dr["fnofday07"] = Fct.sdFat(fnofday07.Text);
                if (ffrm08.Text == "    .  .") { dr["ffrm08"] = DBNull.Value; }
                else { dr["ffrm08"] = ffrm08.Text; }
                if (fto08.Text == "    .  .") { dr["fto08"] = DBNull.Value; }
                else { dr["fto08"] = fto08.Text; }
                dr["fnofday08"] = Fct.sdFat(fnofday08.Text);
                if (ffrm09.Text == "    .  .") { dr["ffrm09"] = DBNull.Value; }
                else { dr["ffrm09"] = ffrm09.Text; }
                if (fto09.Text == "    .  .") { dr["fto09"] = DBNull.Value; }
                else { dr["fto09"] = fto09.Text; }
                dr["fnofday09"] = Fct.sdFat(fnofday09.Text);
                if (ffrm10.Text == "    .  .") { dr["ffrm10"] = DBNull.Value; }
                else { dr["ffrm10"] = ffrm10.Text; }
                if (fto10.Text == "    .  .") { dr["fto10"] = DBNull.Value; }
                else { dr["fto10"] = fto10.Text; }
                dr["fnofday10"] = Fct.sdFat(fnofday10.Text);
                if (ffrm11.Text == "    .  .") { dr["ffrm11"] = DBNull.Value; }
                else { dr["ffrm11"] = ffrm11.Text; }
                if (fto11.Text == "    .  .") { dr["fto11"] = DBNull.Value; }
                else { dr["fto11"] = fto11.Text; }
                dr["fnofday11"] = Fct.sdFat(fnofday11.Text);
                if (ffrm12.Text == "    .  .") { dr["ffrm12"] = DBNull.Value; }
                else { dr["ffrm12"] = ffrm12.Text; }
                if (fto12.Text == "    .  .") { dr["fto12"] = DBNull.Value; }
                else { dr["fto12"] = fto12.Text; }
                dr["fnofday12"] = Fct.sdFat(fnofday12.Text);
                if (ffrm13.Text == "    .  .") { dr["ffrm13"] = DBNull.Value; }
                else { dr["ffrm13"] = ffrm13.Text; }
                if (fto13.Text == "    .  .") { dr["fto13"] = DBNull.Value; }
                else { dr["fto13"] = fto13.Text; }
                dr["fnofday13"] = Fct.sdFat(fnofday13.Text);
                if (ffrm14.Text == "    .  .") { dr["ffrm14"] = DBNull.Value; }
                else { dr["ffrm14"] = ffrm14.Text; }
                if (fto14.Text == "    .  .") { dr["fto14"] = DBNull.Value; }
                else { dr["fto14"] = fto14.Text; }
                dr["fnofday14"] = Fct.sdFat(fnofday14.Text);
                if (ffrm15.Text == "    .  .") { dr["ffrm15"] = DBNull.Value; }
                else { dr["ffrm15"] = ffrm15.Text; }
                if (fto15.Text == "    .  .") { dr["fto15"] = DBNull.Value; }
                else { dr["fto15"] = fto15.Text; }
                dr["fnofday15"] = Fct.sdFat(fnofday15.Text);
                dr["fttlday"] = fttlday.Text;
                dr["fnilpay1"] = fnilpay1.Text;
                dr["fnilpay2"] = fnilpay2.Text;
                dr["fnlength"] = fnlength.Text;
            }
            bdwn_ec.fttldayValue = fttlday.Text.ToString();
            bdwn_ec.fnilpay1Value = fnilpay1.Text.ToString();
            bdwn_ec.fnilpay2Value = fnilpay2.Text.ToString();
            bdwn_ec.fnlengthValue = fnlength.Text.ToString();
            bdwn_ec.fnlength2Value = fnlength.Text.ToString();
            this.Close();
        }

        private void fnilpay1_Validated(object sender, EventArgs e)
        {
            fnlength.Text = Fct.stFat(Fct.sdFat(fttlday.Text) - Fct.sdFat(fnilpay1.Text) - Fct.sdFat(fnilpay2.Text));
        }

        private void XcmdExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void panel1_MouseEnter(object sender, EventArgs e)
        {
            Skip_Validate = true;
        }

        private void fnilpay1_Leave(object sender, EventArgs e)
        {
            fnlength.Text = Fct.stFat(Fct.sdFat(fttlday.Text) - Fct.sdFat(fnilpay1.Text) - Fct.sdFat(fnilpay2.Text));
       
        }

        private void fttlday_Leave(object sender, EventArgs e)
        {
            fnlength.Text = Fct.stFat(Fct.sdFat(fttlday.Text) - Fct.sdFat(fnilpay1.Text) - Fct.sdFat(fnilpay2.Text));
       
        }

        private void fnilpay2_Leave(object sender, EventArgs e)
        {
            fnlength.Text = Fct.stFat(Fct.sdFat(fttlday.Text) - Fct.sdFat(fnilpay1.Text) - Fct.sdFat(fnilpay2.Text));
       
        }

    }
}
