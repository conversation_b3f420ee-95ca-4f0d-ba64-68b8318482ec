using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;

namespace INS
{
    public partial class frmProducerSearch : Form
    {
        public frmProducerSearch()
        {
            InitializeComponent();
            FillData("", "");
        }

        public frmProducerSearch(Ctrl.DirectGen directGen)
        {
            // TODO: Complete member initialization
            this.directGen = directGen;
            InitializeComponent();
        }

        public frmProducerSearch(Ctrl.RIGen rIGen)
        {
            // TODO: Complete member initialization
            this.rIGen = rIGen;
            InitializeComponent();
        }

        public frmProducerSearch(Ctrl.EndorDirectGen endorDirectGen)
        {
            // TODO: Complete member initialization
            this.endorDirectGen = endorDirectGen;
            InitializeComponent();
        }

        public frmProducerSearch(Ctrl.Claim.REG rEG)
        {
            // TODO: Complete member initialization
            this.rEG = rEG;
            InitializeComponent();
        }

        public frmProducerSearch(Ctrl.Claim.AMD aMD)
        {
            // TODO: Complete member initialization
            this.aMD = aMD;
            InitializeComponent();
        }

        public frmProducerSearch(Ctrl.Claim.riREG riREG)
        {
            // TODO: Complete member initialization
            this.riREG = riREG;
            InitializeComponent();
        }

        public frmProducerSearch(Ctrl.Claim.riAMD riAMD)
        {
            // TODO: Complete member initialization
            this.riAMD = riAMD;
            InitializeComponent();
        }

        public frmProducerSearch(Ctrl.Claim.EciREG eciREG)
        {
            // TODO: Complete member initialization
            this.eciREG = eciREG;
            InitializeComponent();
        }

        public frmProducerSearch(Ctrl.Claim.EciAMD eciAMD)
        {
            // TODO: Complete member initialization
            this.eciAMD = eciAMD;
            InitializeComponent();
        }

        public frmProducerSearch(Ctrl.Claim.riEciAMD riEciAMD)
        {
            // TODO: Complete member initialization
            this.riEciAMD = riEciAMD;
            InitializeComponent();
        }

        public frmProducerSearch(Ctrl.Claim.riEciREG riEciREG)
        {
            // TODO: Complete member initialization
            this.riEciREG = riEciREG;
            InitializeComponent();
        }

        public frmProducerSearch(tginvh tginvh)
        {
            // TODO: Complete member initialization
            this.tginvh = tginvh;
            InitializeComponent();
        }

        public frmProducerSearch(Account.ctrl.acchk acchk)
        {
            // TODO: Complete member initialization
            this.acchk = acchk;
            InitializeComponent();
        }

        public frmProducerSearch(Account.ctrl.acset acset)
        {
            // TODO: Complete member initialization
            this.acset = acset;
            InitializeComponent();
        }

        public frmProducerSearch(Forms.selstat selstat)
        {
            // TODO: Complete member initialization
            this.selstat = selstat;
            InitializeComponent();
        }

        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "",ftype="";

        private Ctrl.DirectGen directGen;
        private Ctrl.RIGen rIGen;
        private Ctrl.EndorDirectGen endorDirectGen;
        private Ctrl.Claim.REG rEG;
        private Ctrl.Claim.AMD aMD;
        private Ctrl.Claim.riREG riREG;
        private Ctrl.Claim.riAMD riAMD;
        private Ctrl.Claim.EciREG eciREG;
        private Ctrl.Claim.EciAMD eciAMD;
        private Ctrl.Claim.riEciAMD riEciAMD;
        private Ctrl.Claim.riEciREG riEciREG;
        private tginvh tginvh;
        private Account.ctrl.acchk acchk;
        private Account.ctrl.acset acset;
        private Forms.selstat selstat;

        public void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            if (ftype == "") { ftype = "P"; }
            if (order == "" && query == "")
            {
                a = new SqlDataAdapter("select fid as Producer#,fdesc as Desc#,rtrim(fadd1)+rtrim(fadd2)+rtrim(fadd3)+rtrim(fadd4) as faddr from mprdr where ftype='" + ftype + "' and fstatus = 1 order by fid", c);
            }
            else if (order != "")
            {
                a = new SqlDataAdapter("select fid as Producer#,fdesc as Desc#,rtrim(fadd1)+rtrim(fadd2)+rtrim(fadd3)+rtrim(fadd4) as faddr from mprdr where ftype='" + ftype + "' and fstatus = 1 order by '" + order + "'", c);
            }
            else if (query != "")
            {
                a = new SqlDataAdapter("select fid as Producer#,fdesc as Desc#,rtrim(fadd1)+rtrim(fadd2)+rtrim(fadd3)+rtrim(fadd4) as faddr from mprdr where ftype='" + ftype + "' and fstatus = 1 AND fdesc = '" + query + "'", c);
            }
            else
            {
                a = new SqlDataAdapter("select fid as Producer#,fdesc as Desc#,rtrim(fadd1)+rtrim(fadd2)+rtrim(fadd3)+rtrim(fadd4) as faddr from mprdr where ftype='" + ftype + "' and fstatus = 1 AND fdesc = '" + query + "' order by fid", c);
            }
            DataTable t = new DataTable();
            a.Fill(t);
            dataGridView1.DataSource = t;
            c.Close();
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }
                if (dataGridView1.Rows[row].Cells["Producer#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Producer#"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["Producer#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["Desc#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["faddr"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["faddr"].Value.ToString().Length != 0)
                    {
                        label4.Text = dataGridView1.Rows[row].Cells["faddr"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            if (flag == "Direct")
            {
                directGen.ProValue = Fct.stFat(textBox1.Text.ToString());
                directGen.ProDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "RI")
            {
                rIGen.ProValue = Fct.stFat(textBox1.Text.ToString());
                rIGen.ProDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "Endor")
            {
                endorDirectGen.ProValue = Fct.stFat(textBox1.Text.ToString());
                endorDirectGen.ProDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "REG")
            {
                rEG.ProValue = Fct.stFat(textBox1.Text.ToString());
                rEG.ProDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "REG_adjr1")
            {
                rEG.Adj1Value = Fct.stFat(textBox1.Text.ToString());
                rEG.Adj1Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "REG_adjr2")
            {
                rEG.Adj2Value = Fct.stFat(textBox1.Text.ToString());
                rEG.Adj2Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "REG_solr1")
            {
                rEG.Sol1Value = Fct.stFat(textBox1.Text.ToString());
                rEG.Sol1Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "REG_solr2")
            {
                rEG.Sol2Value = Fct.stFat(textBox1.Text.ToString());
                rEG.Sol2Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "REG_hlhc")
            {
                rEG.HlhValue = Fct.stFat(textBox1.Text.ToString());
                rEG.HlhDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "AMD_adjr1")
            {
                aMD.Adj1Value = Fct.stFat(textBox1.Text.ToString());
                aMD.Adj1Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "AMD_adjr2")
            {
                aMD.Adj2Value = Fct.stFat(textBox1.Text.ToString());
                aMD.Adj2Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "AMD_solr1")
            {
                aMD.Sol1Value = Fct.stFat(textBox1.Text.ToString());
                aMD.Sol1Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "AMD_solr2")
            {
                aMD.Sol2Value = Fct.stFat(textBox1.Text.ToString());
                aMD.Sol2Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "AMD_hlhc")
            {
                riEciAMD.HlhValue = Fct.stFat(textBox1.Text.ToString());
                riEciAMD.HlhDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riREG")
            {
                riREG.ProValue = Fct.stFat(textBox1.Text.ToString());
                riREG.ProDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riREG_adjr1")
            {
                riREG.Adj1Value = Fct.stFat(textBox1.Text.ToString());
                riREG.Adj1Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riREG_adjr2")
            {
                riREG.Adj2Value = Fct.stFat(textBox1.Text.ToString());
                riREG.Adj2Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riREG_solr1")
            {
                riREG.Sol1Value = Fct.stFat(textBox1.Text.ToString());
                riREG.Sol1Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riREG_solr2")
            {
                riREG.Sol2Value = Fct.stFat(textBox1.Text.ToString());
                riREG.Sol2Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riAMD_adjr1")
            {
                riAMD.Adj1Value = Fct.stFat(textBox1.Text.ToString());
                riAMD.Adj1Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riAMD_adjr2")
            {
                riAMD.Adj2Value = Fct.stFat(textBox1.Text.ToString());
                riAMD.Adj2Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riAMD_solr1")
            {
                riAMD.Sol1Value = Fct.stFat(textBox1.Text.ToString());
                riAMD.Sol1Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riAMD_solr2")
            {
                riAMD.Sol2Value = Fct.stFat(textBox1.Text.ToString());
                riAMD.Sol2Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EciREG")
            {
                eciREG.ProValue = Fct.stFat(textBox1.Text.ToString());
                eciREG.ProDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EciREG_adjr1")
            {
                eciREG.Adj1Value = Fct.stFat(textBox1.Text.ToString());
                eciREG.Adj1Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EciREG_adjr2")
            {
                eciREG.Adj2Value = Fct.stFat(textBox1.Text.ToString());
                eciREG.Adj2Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EciREG_solr1")
            {
                eciREG.Sol1Value = Fct.stFat(textBox1.Text.ToString());
                eciREG.Sol1Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EciREG_solr2")
            {
                eciREG.Sol2Value = Fct.stFat(textBox1.Text.ToString());
                eciREG.Sol2Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EciREG_hlhc")
            {
                eciREG.HlhValue = Fct.stFat(textBox1.Text.ToString());
                eciREG.HlhDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riEciREG")
            {
                riEciREG.ProValue = Fct.stFat(textBox1.Text.ToString());
                riEciREG.ProDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riEciREG_adjr1")
            {
                riEciREG.Adj1Value = Fct.stFat(textBox1.Text.ToString());
                riEciREG.Adj1Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riEciREG_adjr2")
            {
                riEciREG.Adj2Value = Fct.stFat(textBox1.Text.ToString());
                riEciREG.Adj2Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riEciREG_solr1")
            {
                riEciREG.Sol1Value = Fct.stFat(textBox1.Text.ToString());
                riEciREG.Sol1Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riEciREG_solr2")
            {
                riEciREG.Sol2Value = Fct.stFat(textBox1.Text.ToString());
                riEciREG.Sol2Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riEciREG_hlhc")
            {
                riEciREG.HlhValue = Fct.stFat(textBox1.Text.ToString());
                riEciREG.HlhDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EciAMD_adjr1")
            {
                eciAMD.Adj1Value = Fct.stFat(textBox1.Text.ToString());
                eciAMD.Adj1Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EciAMD_adjr2")
            {
                eciAMD.Adj2Value = Fct.stFat(textBox1.Text.ToString());
                eciAMD.Adj2Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EciAMD_solr1")
            {
                eciAMD.Sol1Value = Fct.stFat(textBox1.Text.ToString());
                eciAMD.Sol1Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EciAMD_solr2")
            {
                eciAMD.Sol2Value = Fct.stFat(textBox1.Text.ToString());
                eciAMD.Sol2Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EciAMD_hlhc")
            {
                eciAMD.HlhValue = Fct.stFat(textBox1.Text.ToString());
                eciAMD.HlhDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riEciAMD")
            {
                riEciAMD.ProValue = Fct.stFat(textBox1.Text.ToString());
                riEciAMD.ProDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riEciAMD_adjr1")
            {
                riEciAMD.Adj1Value = Fct.stFat(textBox1.Text.ToString());
                riEciAMD.Adj1Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riEciAMD_adjr2")
            {
                riEciAMD.Adj2Value = Fct.stFat(textBox1.Text.ToString());
                riEciAMD.Adj2Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riEciAMD_solr1")
            {
                riEciAMD.Sol1Value = Fct.stFat(textBox1.Text.ToString());
                riEciAMD.Sol1Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riEciAMD_solr2")
            {
                riEciAMD.Sol2Value = Fct.stFat(textBox1.Text.ToString());
                riEciAMD.Sol2Desc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riEciAMD_hlhc")
            {
                riEciAMD.HlhValue = Fct.stFat(textBox1.Text.ToString());
                riEciAMD.HlhDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "tginvh")
            {
                tginvh.FdbtrValue  = Fct.stFat(textBox1.Text.ToString());
                tginvh.FdbtrDesc  = Fct.stFat(label3.Text.ToString());
                tginvh.Faddr = Fct.stFat(label4.Text.ToString());
                this.Close();
            }
            if (flag == "acchk")
            {
                acchk.FdbtrValue = Fct.stFat(textBox1.Text.ToString());
                acchk.FdbtrDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "acset")
            {
                acset.FdbtrValue = Fct.stFat(textBox1.Text.ToString());
                acset.FdbtrDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "selstat")
            {
                selstat.FdbtrValue = Fct.stFat(textBox1.Text.ToString());
                selstat.FdbtrDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
        }


        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        

    }
}
