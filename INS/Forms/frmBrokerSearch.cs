using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;

namespace INS
{
    public partial class frmBrokerSearch : Form
    {
        public frmBrokerSearch()
        {
            InitializeComponent();
            FillData("", "");
        }


        public frmBrokerSearch(Ctrl.Reins.FacProp facProp)
        {
            // TODO: Complete member initialization
            this.facProp = facProp;
            InitializeComponent();
        }

        public frmBrokerSearch(Ctrl.Reins.NonProp nonProp)
        {
            // TODO: Complete member initialization
            this.nonProp = nonProp;
            InitializeComponent();
        }

        public frmBrokerSearch(Ctrl.Reins.EndtFacProp endtFacProp)
        {
            // TODO: Complete member initialization
            this.endtFacProp = endtFacProp;
            InitializeComponent();
        }

        public frmBrokerSearch(Ctrl.Reins.FacPropPar facPropPar)
        {
            // TODO: Complete member initialization
            this.facPropPar = facPropPar;
            InitializeComponent();
        }

        public frmBrokerSearch(Ctrl.Reins.EndtFacPropPar endtFacPropPar)
        {
            // TODO: Complete member initialization
            this.endtFacPropPar = endtFacPropPar;
            InitializeComponent();
        }

        public frmBrokerSearch(Ctrl.Claim.REINSR rEINSR)
        {
            // TODO: Complete member initialization
            this.rEINSR = rEINSR;
            InitializeComponent();
        }

        public frmBrokerSearch(Ctrl.Claim.ADJ aDJ)
        {
            // TODO: Complete member initialization
            this.aDJ = aDJ;
            InitializeComponent();
        }

        public frmBrokerSearch(Ctrl.Claim.PAY pAY)
        {
            // TODO: Complete member initialization
            this.pAY = pAY;
            InitializeComponent();
        }

        public frmBrokerSearch(Ctrl.Claim.EciREINSR eciREINSR)
        {
            // TODO: Complete member initialization
            this.eciREINSR = eciREINSR;
            InitializeComponent();
        }

        public frmBrokerSearch(Ctrl.Claim.EciADJ eciADJ)
        {
            // TODO: Complete member initialization
            this.eciADJ = eciADJ;
            InitializeComponent();
        }

        public frmBrokerSearch(Ctrl.Claim.EciPAY eciPAY)
        {
            // TODO: Complete member initialization
            this.eciPAY = eciPAY;
            InitializeComponent();
        }

        public frmBrokerSearch(tginvh tginvh)
        {
            // TODO: Complete member initialization
            this.tginvh = tginvh;
            InitializeComponent();
        }

        public frmBrokerSearch(Account.ctrl.acchk acchk)
        {
            // TODO: Complete member initialization
            this.acchk = acchk;
            InitializeComponent();
        }

        public frmBrokerSearch(Account.ctrl.acset acset)
        {
            // TODO: Complete member initialization
            this.acset = acset;
            InitializeComponent();
        }

        public frmBrokerSearch(Forms.selstat selstat)
        {
            // TODO: Complete member initialization
            this.selstat = selstat;
            InitializeComponent();
        }

        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "";
        private Ctrl.Reins.FacProp facProp;
        private Ctrl.Reins.NonProp nonProp;
        private Ctrl.Reins.EndtFacProp endtFacProp;
        private Ctrl.Reins.FacPropPar facPropPar;
        private Ctrl.Reins.EndtFacPropPar endtFacPropPar;
        private Ctrl.Claim.REINSR rEINSR;
        private Ctrl.Claim.ADJ aDJ;
        private Ctrl.Claim.PAY pAY;
        private Ctrl.Claim.EciREINSR eciREINSR;
        private Ctrl.Claim.EciADJ eciADJ;
        private Ctrl.Claim.EciPAY eciPAY;
        private tginvh tginvh;
        private Account.ctrl.acchk acchk;
        private Account.ctrl.acset acset;
        private Forms.selstat selstat;

        public void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            if (order == "" && query == "")
            {
                a = new SqlDataAdapter("select fid as Leader#,fdesc as Desc#, rtrim(fadd1)+rtrim(fadd2)+rtrim(fadd3)+rtrim(fadd4) as faddr from mprdr where ftype='B' order by Desc#", c);
            }
            else if (order != "")
            {
                a = new SqlDataAdapter("select fid as Leader#,fdesc as Desc#, rtrim(fadd1)+rtrim(fadd2)+rtrim(fadd3)+rtrim(fadd4) as faddr from mprdr where ftype='B' order by Desc#", c);
            }
            else if (query != "")
            {
                a = new SqlDataAdapter("select fid as Leader#,fdesc as Desc#, rtrim(fadd1)+rtrim(fadd2)+rtrim(fadd3)+rtrim(fadd4) as faddr from mprdr where ftype='B' AND fdesc = '" + query + "' order by Desc#", c);
            }
            else
            {
                a = new SqlDataAdapter("select fid as Leader#,fdesc as Desc#, rtrim(fadd1)+rtrim(fadd2)+rtrim(fadd3)+rtrim(fadd4) as faddr from mprdr where ftype='B' AND fdesc = '" + query + "' order by Desc#", c);
            }
            DataTable t = new DataTable();
            a.Fill(t);
            dataGridView1.DataSource = t;
            this.dataGridView1.Columns[2].Visible = false;
            c.Close();
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }
                if (dataGridView1.Rows[row].Cells["Leader#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Leader#"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["Leader#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["Desc#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["faddr"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["faddr"].Value.ToString().Length != 0)
                    {
                        label4.Text = dataGridView1.Rows[row].Cells["faddr"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            if (flag == "Broker")
            {
                facProp.BrokerValue = Fct.stFat(textBox1.Text.ToString());
                facProp.BrokerDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "BrokerPar")
            {
                facPropPar.BrokerValue = Fct.stFat(textBox1.Text.ToString());
                facPropPar.BrokerDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EndtBroker")
            {
                endtFacProp.BrokerValue = Fct.stFat(textBox1.Text.ToString());
                endtFacProp.BrokerDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EndtBrokerPar")
            {
                endtFacPropPar.BrokerValue = Fct.stFat(textBox1.Text.ToString());
                endtFacPropPar.BrokerDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "NonBroker")
            {
                nonProp.BrokerValue = Fct.stFat(textBox1.Text.ToString());
                nonProp.BrokerDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "REINSR")
            {
                rEINSR.BrokerValue = Fct.stFat(textBox1.Text.ToString());
                rEINSR.BrokerDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "ADJ")
            {
                aDJ.BrokerValue = Fct.stFat(textBox1.Text.ToString());
                aDJ.BrokerDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "PAY")
            {
                pAY.BrokerValue = Fct.stFat(textBox1.Text.ToString());
                pAY.BrokerDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EciREINSR")
            {
                eciREINSR.BrokerValue = Fct.stFat(textBox1.Text.ToString());
                eciREINSR.BrokerDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EciADJ")
            {
                eciADJ.BrokerValue = Fct.stFat(textBox1.Text.ToString());
                eciADJ.BrokerDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EciPAY")
            {
                eciPAY.BrokerValue = Fct.stFat(textBox1.Text.ToString());
                eciPAY.BrokerDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "tginvh")
            {
                tginvh.FdbtrValue = Fct.stFat(textBox1.Text.ToString());
                tginvh.FdbtrDesc = Fct.stFat(label3.Text.ToString());
                tginvh.Faddr = Fct.stFat(label4.Text.ToString());
                this.Close();
            }
            if (flag == "acchk")
            {
                acchk.FdbtrValue = Fct.stFat(textBox1.Text.ToString());
                acchk.FdbtrDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "acset")
            {
                acset.FdbtrValue = Fct.stFat(textBox1.Text.ToString());
                acset.FdbtrDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "selstat")
            {
                selstat.FdbtrValue = Fct.stFat(textBox1.Text.ToString());
                selstat.FdbtrDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
        }


        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }


    }
}
