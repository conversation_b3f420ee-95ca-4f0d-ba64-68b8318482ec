using INS.INSClass;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
//FZ2YHY48
namespace INS.Business.SearchForm
{
    public partial class bdwn_cl : Form
    {

        public string fctlid, page = "", flag="";
        public bool cEditMode = false;
        public DataTable ocabk_c = new DataTable();
        public string db = InsEnvironment.DataBase.GetDbm();
        public Boolean Auto = false;
        private Ctrl.Claim.EciADJ eciADJ;
        public bdwn_cl()
        {
            InitializeComponent();
        }

        public bdwn_cl(Ctrl.Claim.EciADJ eciADJ)
        {
            // TODO: Complete member initialization
            this.eciADJ = eciADJ;
            InitializeComponent();
            foreach (Control control in this.Controls)              //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件             //添加事件
            }
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            TextBox focusTextBox = null;

            if (e.KeyCode == Keys.Enter)
            {                    //判断用户是否按下回车键
                if (sender is TextBox)
                {
                    focusTextBox = (TextBox)sender;
                    if (!focusTextBox.AcceptsReturn)
                        SendKeys.Send("{TAB}");
                }
                else SendKeys.Send("{TAB}");
            }
        }

        void init()
        { XcmdConfirm.Visible = cEditMode; XAutoLbl.Visible = Auto; }

        public void tpReload(string fctlid_a)
        {
            init();
            XcmdConfirm.Visible = false;
            string sql = "select * from ocabk_c a left join (select fclmno, flogseq, faccdate,fctlid from ocadj) b on a.fctlid_a = b.fctlid where fctlid_a= '" + fctlid_a + "'";
            DataTable dt = DBHelper.GetDataSet(sql);
            if (dt.Rows.Count > 0)
            {
                fclmno.Text = dt.Rows[0]["fclmno"].ToString();
                flogseq.Text = dt.Rows[0]["flogseq"].ToString();
                DateTime b = Convert.ToDateTime(dt.Rows[0]["faccdate"].ToString().Trim());
                faccdate.Text = b.ToString("yyyy.MM.dd");
                fos01.Text = dt.Rows[0]["fos01"].ToString();
                frvos01.Text = dt.Rows[0]["frvos01"].ToString();
                fadj01.Text = dt.Rows[0]["fadj01"].ToString();
                fos02.Text = dt.Rows[0]["fos02"].ToString();
                frvos02.Text = dt.Rows[0]["frvos02"].ToString();
                fadj02.Text = dt.Rows[0]["fadj02"].ToString();
                fos03.Text = dt.Rows[0]["fos03"].ToString();
                frvos03.Text = dt.Rows[0]["frvos03"].ToString();
                fadj03.Text = dt.Rows[0]["fadj03"].ToString();
                fos04.Text = dt.Rows[0]["fos04"].ToString();
                frvos04.Text = dt.Rows[0]["frvos04"].ToString();
                fadj04.Text = dt.Rows[0]["fadj04"].ToString();
                fos05.Text = dt.Rows[0]["fos05"].ToString();
                frvos05.Text = dt.Rows[0]["frvos05"].ToString();
                fadj05.Text = dt.Rows[0]["fadj05"].ToString();
                fos06.Text = dt.Rows[0]["fos06"].ToString();
                frvos06.Text = dt.Rows[0]["frvos06"].ToString();
                fadj06.Text = dt.Rows[0]["fadj06"].ToString();
                fos07.Text = dt.Rows[0]["fos07"].ToString();
                frvos07.Text = dt.Rows[0]["frvos07"].ToString();
                fadj07.Text = dt.Rows[0]["fadj07"].ToString();
                fos08.Text = dt.Rows[0]["fos08"].ToString();
                frvos08.Text = dt.Rows[0]["frvos08"].ToString();
                fadj08.Text = dt.Rows[0]["fadj08"].ToString();
                fos09.Text = dt.Rows[0]["fos09"].ToString();
                frvos09.Text = dt.Rows[0]["frvos09"].ToString();
                fadj09.Text = dt.Rows[0]["fadj09"].ToString();
                fos10.Text = dt.Rows[0]["fos10"].ToString();
                frvos10.Text = dt.Rows[0]["frvos10"].ToString();
                fadj10.Text = dt.Rows[0]["fadj10"].ToString();
                fos11.Text = dt.Rows[0]["fos11"].ToString();
                frvos11.Text = dt.Rows[0]["frvos11"].ToString();
                fadj11.Text = dt.Rows[0]["fadj11"].ToString();
                fos12.Text = dt.Rows[0]["fos12"].ToString();
                frvos12.Text = dt.Rows[0]["frvos12"].ToString();
                fadj12.Text = dt.Rows[0]["fadj12"].ToString();
                fos13.Text = dt.Rows[0]["fos13"].ToString();
                frvos13.Text = dt.Rows[0]["frvos13"].ToString();
                fadj13.Text = dt.Rows[0]["fadj13"].ToString();
                fosA.Text = dt.Rows[0]["fosA"].ToString();
                frvosA.Text = dt.Rows[0]["frvosA"].ToString();
                fadjA.Text = dt.Rows[0]["fadjA"].ToString();
                fosC.Text = dt.Rows[0]["fosC"].ToString();
                frvosC.Text = dt.Rows[0]["frvosC"].ToString();
                fadjC.Text = dt.Rows[0]["fadjC"].ToString();
                fos.Text = dt.Rows[0]["fos"].ToString();
                frvos.Text = dt.Rows[0]["frvos"].ToString();
                fadj.Text = dt.Rows[0]["fadj"].ToString();
                fremark_t.Text = dt.Rows[0]["fremark_t1"].ToString();
            }
        }

        void ButtonAdd()
        {
            XcmdExit.Visible = false;
            frvos01.Enabled = cEditMode;
            frvos02.Enabled = cEditMode;
            frvos03.Enabled = cEditMode;
            frvos04.Enabled = cEditMode;
            frvos05.Enabled = cEditMode;
            frvos06.Enabled = cEditMode;
            frvos07.Enabled = cEditMode;
            frvos08.Enabled = cEditMode;
            frvos09.Enabled = cEditMode;
            frvos10.Enabled = cEditMode;
            frvos11.Enabled = cEditMode;
            frvos12.Enabled = cEditMode;
            frvos13.Enabled = cEditMode;
            fremark_t.Enabled = cEditMode;
            frvos01.BackColor = System.Drawing.Color.White;
            frvos01.ReadOnly = !cEditMode;
            frvos02.BackColor = System.Drawing.Color.White;
            frvos02.ReadOnly = !cEditMode;
            frvos03.BackColor = System.Drawing.Color.White;
            frvos03.ReadOnly = !cEditMode;
            frvos04.BackColor = System.Drawing.Color.White;
            frvos04.ReadOnly = !cEditMode;
            frvos05.BackColor = System.Drawing.Color.White;
            frvos05.ReadOnly = !cEditMode;
            frvos06.BackColor = System.Drawing.Color.White;
            frvos06.ReadOnly = !cEditMode;
            frvos07.BackColor = System.Drawing.Color.White;
            frvos07.ReadOnly = !cEditMode;
            frvos08.BackColor = System.Drawing.Color.White;
            frvos08.ReadOnly = !cEditMode;
            frvos09.BackColor = System.Drawing.Color.White;
            frvos09.ReadOnly = !cEditMode;
            frvos10.BackColor = System.Drawing.Color.White;
            frvos10.ReadOnly = !cEditMode;
            frvos11.BackColor = System.Drawing.Color.White;
            frvos11.ReadOnly = !cEditMode;
            frvos12.BackColor = System.Drawing.Color.White;
            frvos12.ReadOnly = !cEditMode;
            frvos13.BackColor = System.Drawing.Color.White;
            frvos13.ReadOnly = !cEditMode;
            fremark_t.ReadOnly = !cEditMode;
        }

        public void tpAdd(string str, string flg, string date, string fctlid_a)
        {
            XcmdConfirm.Visible = true;
            if (ocabk_c == null)
            {
                if (fctlid_a.Length == 10)
                {
                    string sql1 = "select * from ocabk_c where fctlid_a ='" + fctlid_a + "'";
                    ocabk_c = DBHelper.GetDataSet(sql1);
                }
                else
                {
                    string sql = "select '' as flogseq,fos01, fos01 as frvos01, 0.0000 as fadj01, fos02,fos02 as frvos02,0.0000 as fadj02, fos03, fos03 as frvos03,0.0000 as fadj03, " +
                        "fos04, fos04 as frvos04,0.0000 as fadj04,fos05, fos05 as frvos05,0.0000 as fadj05,fos06,fos06 as frvos06,0.0000 as fadj06, " +
                        "fos07, fos07 as frvos07,0.0000 as fadj07,fos08, fos08 as frvos08,0.0000 as fadj08,fos09,fos09 as frvos09,0.0000 as fadj09, " +
                        "fos10, fos10 as frvos10,0.0000 as fadj10,fos11, fos11 as frvos11,0.0000 as fadj11,fos12, fos12 as frvos12,0.0000 as fadj12,fos13, fos13 as frvos13,0.0000 as fadj13,fosA, fosA as frvosA,0.0000 as fadjA,fosA2, fosA2 as frvosA2,0.0000 as fadjA2,fosB, fosB as frvosB,0.0000 as fadjB, " +
                        "fosC, fosC as frvosC,0.0000 as fadjC,fos, fos as frvos,0.0000 as fadj,'' as fremark_t1 from ocabk_oc a where fctlid_c = (select fctlid from oclaim where fclmno = '" + str + "')";
                    ocabk_c = DBHelper.GetDataSet(sql);
                }
            }
            fclmno.Text = str;
            flogseq.Text = flg;
            DataTable dt = ocabk_c;
            if (dt.Rows.Count > 0)
            {
                DateTime b = Convert.ToDateTime(date);
                faccdate.Text = b.ToString("yyyy.MM.dd");
                fos01.Text = dt.Rows[0]["fos01"].ToString();
                frvos01.Text = dt.Rows[0]["frvos01"].ToString();
                fadj01.Text = dt.Rows[0]["fadj01"].ToString();
                fos02.Text = dt.Rows[0]["fos02"].ToString();
                frvos02.Text = dt.Rows[0]["frvos02"].ToString();
                fadj02.Text = dt.Rows[0]["fadj02"].ToString();
                fos03.Text = dt.Rows[0]["fos03"].ToString();
                frvos03.Text = dt.Rows[0]["frvos03"].ToString();
                fadj03.Text = dt.Rows[0]["fadj03"].ToString();
                fos04.Text = dt.Rows[0]["fos04"].ToString();
                frvos04.Text = dt.Rows[0]["frvos04"].ToString();
                fadj04.Text = dt.Rows[0]["fadj04"].ToString();
                fos05.Text = dt.Rows[0]["fos05"].ToString();
                frvos05.Text = dt.Rows[0]["frvos05"].ToString();
                fadj05.Text = dt.Rows[0]["fadj05"].ToString();
                fos06.Text = dt.Rows[0]["fos06"].ToString();
                frvos06.Text = dt.Rows[0]["frvos06"].ToString();
                fadj06.Text = dt.Rows[0]["fadj06"].ToString();
                fos07.Text = dt.Rows[0]["fos07"].ToString();
                frvos07.Text = dt.Rows[0]["frvos07"].ToString();
                fadj07.Text = dt.Rows[0]["fadj07"].ToString();
                fos08.Text = dt.Rows[0]["fos08"].ToString();
                frvos08.Text = dt.Rows[0]["frvos08"].ToString();
                fadj08.Text = dt.Rows[0]["fadj08"].ToString();
                fos09.Text = dt.Rows[0]["fos09"].ToString();
                frvos09.Text = dt.Rows[0]["frvos09"].ToString();
                fadj09.Text = dt.Rows[0]["fadj09"].ToString();
                fos10.Text = dt.Rows[0]["fos10"].ToString();
                frvos10.Text = dt.Rows[0]["frvos10"].ToString();
                fadj10.Text = dt.Rows[0]["fadj10"].ToString();
                fos11.Text = dt.Rows[0]["fos11"].ToString();
                frvos11.Text = dt.Rows[0]["frvos11"].ToString();
                fadj11.Text = dt.Rows[0]["fadj11"].ToString();
                fos12.Text = dt.Rows[0]["fos12"].ToString();
                frvos12.Text = dt.Rows[0]["frvos12"].ToString();
                fadj12.Text = dt.Rows[0]["fadj12"].ToString();
                fos13.Text = dt.Rows[0]["fos13"].ToString();
                frvos13.Text = dt.Rows[0]["frvos13"].ToString();
                fadj13.Text = dt.Rows[0]["fadj13"].ToString();
                fosA.Text = dt.Rows[0]["fosA"].ToString();
                frvosA.Text = dt.Rows[0]["frvosA"].ToString();
                fadjA.Text = dt.Rows[0]["fadjA"].ToString();
                fosA2.Text = dt.Rows[0]["fosA2"].ToString();
                frvosA2.Text = dt.Rows[0]["frvosA2"].ToString();
                fadjA2.Text = dt.Rows[0]["fadjA2"].ToString();
                fosB.Text = dt.Rows[0]["fosB"].ToString();
                frvosB.Text = dt.Rows[0]["frvosB"].ToString();
                fadjB.Text = dt.Rows[0]["fadjB"].ToString();
                fosC.Text = dt.Rows[0]["fosC"].ToString();
                frvosC.Text = dt.Rows[0]["frvosC"].ToString();
                fadjC.Text = dt.Rows[0]["fadjC"].ToString();
                fos.Text = dt.Rows[0]["fos"].ToString();
                frvos.Text = dt.Rows[0]["frvos"].ToString();
                fadj.Text = dt.Rows[0]["fadj"].ToString();
                fremark_t.Text = dt.Rows[0]["fremark_t1"].ToString();
            }
            ButtonAdd();
        }

        void u_setpage(string p_name)
        {
            if (p_name == "THE REST")
            {
                frvos01.Enabled = cEditMode;
                frvos02.Enabled = cEditMode;
                frvos03.Enabled = cEditMode;
                frvos04.Enabled = cEditMode;
                frvos05.Enabled = cEditMode;
                frvos06.Enabled = cEditMode;
                frvos07.Enabled = cEditMode;
                frvos08.Enabled = cEditMode;
                frvos09.Enabled = cEditMode;
                frvos10.Enabled = cEditMode;
                frvos11.Enabled = cEditMode;
                frvos12.Enabled = cEditMode;
                frvos13.Enabled = cEditMode;
                fremark_t.Enabled = !cEditMode;
            }
        }

        void u_calvalue(string p_varname)
        {
            string lc_index = "";
            if (p_varname.Substring(5, 2) == "01" || p_varname.Substring(5, 2) == "02" || p_varname.Substring(5, 2) == "03" || p_varname.Substring(5, 2) == "04" || p_varname.Substring(5, 2) == "05" || p_varname.Substring(5, 2) == "06" || p_varname.Substring(5, 2) == "07" || p_varname.Substring(5, 2) == "08" || p_varname.Substring(5, 2) == "09" || p_varname.Substring(5, 2) == "10" || p_varname.Substring(5, 2) == "11" || p_varname.Substring(5, 2) == "12" || p_varname.Substring(5, 2) == "13")
            {
                lc_index = p_varname.Substring(5, 2);
            }
            if (p_varname.Substring(0, 5) == "FRVOS" && p_varname.Length == 7 && lc_index != "")
            {
                TextBox tb = (TextBox)Controls.Find(string.Format("{0}", "fadj" + lc_index), true).FirstOrDefault();
                TextBox fr = (TextBox)Controls.Find(string.Format("{0}", "frvos" + lc_index), true).FirstOrDefault();
                TextBox fo = (TextBox)Controls.Find(string.Format("{0}", "fos" + lc_index), true).FirstOrDefault();
                tb.Text = Fct.stFat(Fct.sdFat(fr.Text) - Fct.sdFat(fo.Text));
            }
            fadj.Text = Fct.stFat(Fct.sdFat(fadj01.Text) + Fct.sdFat(fadj02.Text) + Fct.sdFat(fadj03.Text) + Fct.sdFat(fadj04.Text) + Fct.sdFat(fadj05.Text) + Fct.sdFat(fadj06.Text) + Fct.sdFat(fadj07.Text) + Fct.sdFat(fadj08.Text) + Fct.sdFat(fadj09.Text) + Fct.sdFat(fadj10.Text) + Fct.sdFat(fadj11.Text) + Fct.sdFat(fadj12.Text) + Fct.sdFat(fadj13.Text));
            frvos.Text = Fct.stFat(Fct.sdFat(frvos01.Text) + Fct.sdFat(frvos02.Text) + Fct.sdFat(frvos03.Text) + Fct.sdFat(frvos04.Text) + Fct.sdFat(frvos05.Text) + Fct.sdFat(frvos06.Text) + Fct.sdFat(frvos07.Text) + Fct.sdFat(frvos08.Text) + Fct.sdFat(frvos09.Text) + Fct.sdFat(frvos10.Text) + Fct.sdFat(frvos11.Text) + Fct.sdFat(frvos12.Text) + Fct.sdFat(frvos13.Text));
            fadjA.Text = Fct.stFat(Fct.sdFat(fadj01.Text) + Fct.sdFat(fadj02.Text) + Fct.sdFat(fadj03.Text) + Fct.sdFat(fadj04.Text) + Fct.sdFat(fadj05.Text) + Fct.sdFat(fadj06.Text) + Fct.sdFat(fadj07.Text) + Fct.sdFat(fadj10.Text));
            fadjA2.Text = Fct.stFat(Fct.sdFat(fadj08.Text));
            fadjB.Text = Fct.stFat(Fct.sdFat(fadj09.Text));
            fadjC.Text = Fct.stFat(Fct.sdFat(fadj11.Text) + Fct.sdFat(fadj12.Text) + Fct.sdFat(fadj13.Text));
            frvosA.Text = Fct.stFat(Fct.sdFat(frvos01.Text) + Fct.sdFat(frvos02.Text) + Fct.sdFat(frvos03.Text) + Fct.sdFat(frvos04.Text) + Fct.sdFat(frvos05.Text) + Fct.sdFat(frvos06.Text) + Fct.sdFat(frvos07.Text) + Fct.sdFat(frvos10.Text));
            frvosA2.Text = Fct.stFat(Fct.sdFat(frvos08.Text));
            frvosB.Text = Fct.stFat(Fct.sdFat(frvos09.Text));
            frvosC.Text = Fct.stFat(Fct.sdFat(frvos11.Text) + Fct.sdFat(frvos12.Text) + Fct.sdFat(frvos13.Text));
        }

        private void frvos01_Validated(object sender, EventArgs e)
        {
            u_calvalue("FRVOS01");
        }

        private void frvos02_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(frvos02.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //frvos02.Focus();
            }
            else { }
            u_calvalue("FRVOS02"); 
        }

        private void frvos03_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(frvos03.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //frvos03.Focus();
            }
            else { }
            u_calvalue("FRVOS03");
        }

        private void frvos04_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(frvos04.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //frvos04.Focus();
            }
            else {  }
            u_calvalue("FRVOS04");
        }

        private void frvos05_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(frvos05.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //frvos05.Focus();
            }
            else { }
            u_calvalue("FRVOS05"); 
        }

        private void frvos10_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(frvos10.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //frvos10.Focus();
            }
            else {  }
            u_calvalue("FRVOS10");
        }

        private void frvos06_Validated(object sender, EventArgs e)
        {
            u_calvalue("FRVOS06");
        }

        private void frvos07_Validated(object sender, EventArgs e)
        {
            u_calvalue("FRVOS07");
        }

        private void frvos08_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(frvos08.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //frvos08.Focus();
            }
            else {  }
            u_calvalue("FRVOS08");
        }

        private void frvos09_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(frvos09.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //frvos09.Focus();
            }
            else {}
            u_calvalue("FRVOS09"); 
        }

        private void frvos11_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(frvos11.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //frvos11.Focus();
            }
            else {  }
            u_calvalue("FRVOS11");
        }

        private void frvos12_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(frvos12.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //frvos12.Focus();
            }
            else {  }
            u_calvalue("FRVOS12");
        }

        private void frvos13_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(frvos13.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //frvos13.Focus();
            }
            else {  }
            u_calvalue("FRVOS13");
        }

        private void XcmdConfirm_Click(object sender, EventArgs e)
        {
            if (ocabk_c.Rows.Count == 0)
            {
                DataRow newRow = ocabk_c.NewRow();
                ocabk_c.Rows.Add(newRow);
            }
            foreach (DataRow dr in ocabk_c.Rows)
            {
                dr["flogseq"] = Fct.sdFat(flogseq.Text);
                dr["fadj01"] = Fct.sdFat(fadj01.Text);
                dr["frvos01"] = Fct.sdFat(frvos01.Text);
                dr["fos01"] = Fct.sdFat(fos01.Text);
                dr["fadj02"] = Fct.sdFat(fadj02.Text);
                dr["frvos02"] = Fct.sdFat(frvos02.Text);
                dr["fos02"] = Fct.sdFat(fos02.Text);
                dr["fadj03"] = Fct.sdFat(fadj03.Text);
                dr["frvos03"] = Fct.sdFat(frvos03.Text);
                dr["fos03"] = Fct.sdFat(fos03.Text);
                dr["fadj04"] = Fct.sdFat(fadj04.Text);
                dr["frvos04"] = Fct.sdFat(frvos04.Text);
                dr["fos04"] = Fct.sdFat(fos04.Text);
                dr["fadj05"] = Fct.sdFat(fadj05.Text);
                dr["frvos05"] = Fct.sdFat(frvos05.Text);
                dr["fos05"] = Fct.sdFat(fos05.Text);
                dr["fadj06"] = Fct.sdFat(fadj06.Text);
                dr["frvos06"] = Fct.sdFat(frvos06.Text);
                dr["fos06"] = Fct.sdFat(fos06.Text);
                dr["fadj07"] = Fct.sdFat(fadj07.Text);
                dr["frvos07"] = Fct.sdFat(frvos07.Text);
                dr["fos07"] = Fct.sdFat(fos07.Text);
                dr["fadj08"] = Fct.sdFat(fadj08.Text);
                dr["frvos08"] = Fct.sdFat(frvos08.Text);
                dr["fos08"] = Fct.sdFat(fos08.Text);
                dr["fadj09"] = Fct.sdFat(fadj09.Text);
                dr["frvos09"] = Fct.sdFat(frvos09.Text);
                dr["fos09"] = Fct.sdFat(fos09.Text);
                dr["fadj10"] = Fct.sdFat(fadj10.Text);
                dr["frvos10"] = Fct.sdFat(frvos10.Text);
                dr["fos10"] = Fct.sdFat(fos10.Text);
                dr["fadj11"] = Fct.sdFat(fadj11.Text);
                dr["frvos11"] = Fct.sdFat(frvos11.Text);
                dr["fos11"] = Fct.sdFat(fos11.Text);
                dr["fadj12"] = Fct.sdFat(fadj12.Text);
                dr["frvos12"] = Fct.sdFat(frvos12.Text);
                dr["fos12"] = Fct.sdFat(fos12.Text);
                dr["fadj13"] = Fct.sdFat(fadj13.Text);
                dr["frvos13"] = Fct.sdFat(frvos13.Text);
                dr["fos13"] = Fct.sdFat(fos13.Text);
                dr["fadjA"] = Fct.sdFat(fadjA.Text);
                dr["frvosA"] = Fct.sdFat(frvosA.Text);
                dr["fosA"] = Fct.sdFat(fosA.Text);
                dr["fadjA2"] = Fct.sdFat(fadjA2.Text);
                dr["frvosA2"] = Fct.sdFat(frvosA2.Text);
                dr["fosA2"] = Fct.sdFat(fosA2.Text);
                dr["fadjB"] = Fct.sdFat(fadjB.Text);
                dr["frvosB"] = Fct.sdFat(frvosB.Text);
                dr["fosB"] = Fct.sdFat(fosB.Text);
                dr["fadjC"] = Fct.sdFat(fadjC.Text);
                dr["frvosC"] = Fct.sdFat(frvosC.Text);
                dr["fosC"] = Fct.sdFat(fosC.Text);
                dr["fadj"] = Fct.sdFat(fadj.Text);
                dr["frvos"] = Fct.sdFat(frvos.Text);
                dr["fos"] = Fct.sdFat(fos.Text);
            }
            if (flag == "EciADJ")
            {
                eciADJ.fclawamtValue = fadj.Text.ToString();
                eciADJ.DTocabk_c = ocabk_c;
                eciADJ.u_cadj();
            }
            this.Close();
        }

        private void XcmdExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

    }
}
