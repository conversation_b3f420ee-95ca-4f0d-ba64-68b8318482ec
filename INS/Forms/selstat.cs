using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace INS.Forms
{
    public partial class selstat : Form
    {
        public string fctlid = "";
        public DataTable mmain = new DataTable();
        public String FdbtrValue
        {
            set { fdbtr.Text = value; }
        }

        public String FdbtrDesc
        {
            set { fdbtrdesc.Text = value; }
        }

        BindData[] u_dtypearr = new BindData[]  {
                                 new BindData("%","%"),
                                 new BindData("M","Misc."),
                                 new BindData("C","Client"),
                                 new BindData("P","Producer"),
                                 new BindData("B","R/I Broker"),
                                 new BindData("R","Reinsurer")};
        BindData[] u_statidarr = new BindData[]  {
                                   new BindData("D0100","Premium (Dir)"),
                                   new BindData("D0104","Premium (Out)"),
                                   new BindData("D0200","Claims (Dir)"),
                                   new BindData("D0204","Claims (Out)"),
                                   new BindData("R0100","Premium (In)"),
                                   new BindData("R0200","Claims (In)"),
                                   new BindData("G0000","Miscellaneous")};
        private Account.ctrl.acset acset;
        public selstat()
        {
            InitializeComponent();
            fdbtrtype.DataSource = u_dtypearr;
            fdbtrtype.ValueMember = "ID";
            fdbtrtype.DisplayMember = "Item";
            fstatid.DataSource = u_statidarr;
            fstatid.ValueMember = "ID";
            fstatid.DisplayMember = "Item";
            mmain.Columns.Add("fobal_g", typeof(decimal));
            mmain.Columns.Add("fset_g", typeof(decimal));
            mmain.Columns.Add("fbal_g", typeof(decimal));
            mmain.Columns.Add("fobal_c", typeof(decimal));
            mmain.Columns.Add("fset_c", typeof(decimal));
            mmain.Columns.Add("fbal_c", typeof(decimal));
            mmain.Columns.Add("fobal_s", typeof(decimal));
            mmain.Columns.Add("fset_s", typeof(decimal));
            mmain.Columns.Add("fbal_s", typeof(decimal));
            mmain.Columns.Add("forgbal", typeof(decimal));
            mmain.Columns.Add("finvset", typeof(decimal));
            mmain.Columns.Add("fbalance", typeof(decimal));
        }

        public selstat(Account.ctrl.acset acset)
        {
            // TODO: Complete member initialization
            InitializeComponent();
            fstatid.DataSource = u_statidarr;
            fstatid.ValueMember = "ID";
            fstatid.DisplayMember = "Item";
            fdbtrtype.DataSource = u_dtypearr;
            fdbtrtype.ValueMember = "ID";
            fdbtrtype.DisplayMember = "Item";
            mmain.Columns.Add("fobal_g", typeof(decimal));
            mmain.Columns.Add("fset_g", typeof(decimal));
            mmain.Columns.Add("fbal_g", typeof(decimal));
            mmain.Columns.Add("fobal_c", typeof(decimal));
            mmain.Columns.Add("fset_c", typeof(decimal));
            mmain.Columns.Add("fbal_c", typeof(decimal));
            mmain.Columns.Add("fobal_s", typeof(decimal));
            mmain.Columns.Add("fset_s", typeof(decimal));
            mmain.Columns.Add("fbal_s", typeof(decimal));
            mmain.Columns.Add("forgbal", typeof(decimal));
            mmain.Columns.Add("finvset", typeof(decimal));
            mmain.Columns.Add("fbalance", typeof(decimal));
            this.acset = acset;
        }

        private void fdbtrtype_SelectedValueChanged(object sender, EventArgs e)
        {
            u_selswitch(fdbtrtype.SelectedValue.ToString(), fstatid.SelectedValue.ToString());
        }

        void u_selswitch(string p_fdbtrtype, string p_fstatid)
        { 
		    if(p_fdbtrtype == "%"){		
                fdbtrdesc.Visible = false;
                fdbtrBtn.Visible = false;
                fdbtr.Visible = false;
                fdbtrdesc2.Visible = false;
            }
            else if(p_fdbtrtype == "M"){
                fdbtrdesc.Visible = false;
                fdbtrBtn.Visible = false;
                fdbtr.Visible = false;
                fdbtrdesc2.Visible = true;
            }
            else{
                fdbtrdesc.Visible = true;
                fdbtrBtn.Visible = true;
                fdbtr.Visible = true;
                fdbtrdesc2.Visible = false;
            }

		    if (p_fstatid=="D0100" || p_fstatid=="D0104" ||  p_fstatid=="R0100")
            {		
                PolInfo.Visible = true;
		        ClaimInfo.Visible = false;
                fset_g.ReadOnly = false;
                fset_c.ReadOnly = false;
                fset_s.ReadOnly = false;
            }
            else if (p_fstatid=="D0200" || p_fstatid=="D0204" ||  p_fstatid=="R0200")
            {		
                PolInfo.Visible = false;
		        ClaimInfo.Visible = true;
                fset_g.ReadOnly = false;
                fset_c.ReadOnly = true;
                fset_s.ReadOnly = false;
            }
            else{ 
                PolInfo.Visible = true;
		        ClaimInfo.Visible = false;
                fset_s.ReadOnly = true;
            }
        }

        private void fdbtrBtn_Click(object sender, EventArgs e)
        {
            if (fdbtrtype.SelectedValue.ToString() == "P")
            {
                frmProducerSearch tempform = new frmProducerSearch(this);
                tempform.flag = "selstat";
                tempform.FillData("", "");
                tempform.ShowDialog();
            }
            else if (fdbtrtype.SelectedValue.ToString() == "C")
            {
                frmClientSearch tempform = new frmClientSearch(this);
                tempform.ftype = "C";
                tempform.flag = "selstat";
                tempform.FillData("", "");
                tempform.ShowDialog();
            }
            else if (fdbtrtype.SelectedValue.ToString() == "R")
            {
                frmReinsurerSearch tempform = new frmReinsurerSearch(this);
                tempform.flag = "selstat";
                tempform.FillData("", "");
                tempform.ShowDialog();
            }
            else
            {
                frmBrokerSearch tempform = new frmBrokerSearch(this);
                tempform.flag = "selstat";
                tempform.FillData("", "");
                tempform.ShowDialog();
            }
        }

        private void statNo_Click(object sender, EventArgs e)
        {
            frmset tempform = new frmset(this);
            tempform.lc_fdbtrtype = fdbtrtype.SelectedValue.ToString();
            tempform.lc_fdbtr = fdbtr.Text;
            tempform.lc_fdbrdesc = fdbtrdesc.Text;
            tempform.lc_fstatid = fstatid.SelectedValue.ToString();
            tempform.fstatno = fstatno.Text;
            tempform.FillData("", "");
            tempform.ShowDialog();
        }

        public void filldata(string fctlid) {
            fset_g.ReadOnly = false;
            fset_g.BackColor = System.Drawing.SystemColors.Window;
            fset_c.ReadOnly = false;
            fset_c.BackColor = System.Drawing.SystemColors.Window;
            fset_s.ReadOnly = false;
            fset_s.BackColor = System.Drawing.SystemColors.Window;
            string sql = "select * from ostat where fctlid ='"+fctlid+"'";
            DataTable dt = DBHelper.GetDataSet(sql);
            fdbtrtype2.Text = Fct.stFat(dt.Rows[0]["fdbtrtype"]);
            fdbtr2.Text = Fct.stFat(dt.Rows[0]["fdbtr"]);
            fdbtrdesc2.Text = Fct.stFat(dt.Rows[0]["fdbtrdesc"]);
            fstateid2.Text = Fct.stFat(dt.Rows[0]["fstatid"]);
            frefno.Text = Fct.stFat(dt.Rows[0]["frefno"]);
            fpolno.Text = Fct.stFat(dt.Rows[0]["fpolno"]);
            fendtno.Text = Fct.stFat(dt.Rows[0]["fendtno"]);
            fcedeclm.Text = Fct.stFat(dt.Rows[0]["fcedeclm"]);
            fsrcref2.Text = Fct.stFat(dt.Rows[0]["fsrcref"]);
            fsrcref.Text = Fct.stFat(dt.Rows[0]["fsrcref"]);
            fcedepol.Text = Fct.stFat(dt.Rows[0]["fcedepol"]);
            fcedeendt.Text = Fct.stFat(dt.Rows[0]["fcedeendt"]);
            fobal_g.Text = Fct.stFat(dt.Rows[0]["fbal_g"]);
            fset_g.Text = Fct.stFat(-Fct.sdFat(dt.Rows[0]["fbal_g"]));
            fbal_g.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fbal_g"]) - Fct.sdFat(dt.Rows[0]["fbal_g"]));
            fobal_c.Text = Fct.stFat(dt.Rows[0]["fbal_c"]);
            fset_c.Text = Fct.stFat(-Fct.sdFat(dt.Rows[0]["fbal_c"]));
            fbal_c.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fbal_c"]) - Fct.sdFat(dt.Rows[0]["fbal_c"]));
            fobal_s.Text = Fct.stFat(dt.Rows[0]["fbal_s"]);
            fset_s.Text = Fct.stFat(-Fct.sdFat(dt.Rows[0]["fbal_s"]));
            fbal_s.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fbal_s"]) - Fct.sdFat(dt.Rows[0]["fbal_s"]));
            forgbal.Text = Fct.stFat(dt.Rows[0]["fbalance"]);
            finvset.Text = Fct.stFat(-Fct.sdFat(dt.Rows[0]["fbalance"]));
            fbalance.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fbalance"]) - Fct.sdFat(dt.Rows[0]["fbalance"]));
        }

        private void button2_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            mmain.Clear();
            decimal ln_fobal_g = 0, ln_fset_g = 0, ln_fbal_g = 0, ln_fobal_c = 0, ln_fset_c = 0, ln_fbal_c = 0,
            ln_fobal_s = 0, ln_fset_s = 0, ln_fbal_s = 0, ln_forgbal = 0, ln_finvset = 0, ln_fbalance = 0;
            ln_fobal_g = Fct.sdFat(fobal_g.Text);
            ln_fset_g = Fct.sdFat(fset_g.Text);
            ln_fbal_g = Fct.sdFat(fbal_g.Text);
            ln_fobal_c = Fct.sdFat(fobal_c.Text);
            ln_fset_c = Fct.sdFat(fset_c.Text);
            ln_fbal_c = Fct.sdFat(fbal_c.Text);
            ln_fobal_s = Fct.sdFat(fobal_s.Text);
            ln_fset_s = Fct.sdFat(fset_s.Text);
            ln_fbal_s = Fct.sdFat(fbal_s.Text);
            ln_forgbal = Fct.sdFat(forgbal.Text);
            ln_finvset = Fct.sdFat(finvset.Text);
            ln_fbalance = Fct.sdFat(fbalance.Text);

            mmain.Rows.Add(ln_fobal_g, ln_fset_g, ln_fbal_g, ln_fobal_c, ln_fset_c, ln_fbal_c, ln_fobal_s, ln_fset_s, ln_fbal_s, ln_forgbal, ln_finvset, ln_fbalance);
            acset.selstat = mmain;
            acset.adddata(fctlid);
            this.Close();
        }

        void set_Valid() {
            fbal_g.Text = Fct.stFat(Fct.sdFat(fobal_g.Text) + Fct.sdFat(fset_g.Text));
            fbal_c.Text = Fct.stFat(Fct.sdFat(fobal_c.Text) + Fct.sdFat(fset_c.Text));
            fbal_s.Text = Fct.stFat(Fct.sdFat(fobal_s.Text) + Fct.sdFat(fset_s.Text));
            finvset.Text = Fct.stFat(Fct.sdFat(fset_g.Text) + Fct.sdFat(fset_s.Text) + Fct.sdFat(fset_c.Text));
            fbalance.Text = Fct.stFat(Fct.sdFat(fbal_g.Text) + Fct.sdFat(fbal_s.Text) + Fct.sdFat(fbal_c.Text));
        }

        private void fset_g_Validated(object sender, EventArgs e)
        {
            set_Valid();
        }

        private void fset_c_Validated(object sender, EventArgs e)
        {
            set_Valid();
        }

        private void fset_s_Validated(object sender, EventArgs e)
        {
            set_Valid();
        }


    }
}
