using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business.SearchForm
{
    public partial class selvo : Form
    {

        public string Policy, fctlid, fexten, Class, SubClass, fbus, flag;
        int Policylistrow = 0;
        private Endorsement endorsement;
        private RIEndt rIEndt;
        public selvo()
        {
            InitializeComponent();
        }


        public selvo(Endorsement endorsement)
        {
            // TODO: Complete member initialization
            this.endorsement = endorsement;
            InitializeComponent();
        }

        public selvo(RIEndt rIEndt)
        {
            // TODO: Complete member initialization
            this.rIEndt = rIEndt;
            InitializeComponent();
        }

        public void FillData()
        {
            String Sql = "select a.fctlid_p, a.fvseq, a.fctlid, case when a.fvseq=0 then null else a.fvseq end as V#, a.fvarno as VO, a.fendtno as [Endt No#] " +
                        "from polh a join (select max(fendtno) as fendtno,fvseq from polh where fpolno='" + Policy +"' and fconfirm=3 and fendttype<>1 group by fvseq) b on a.fendtno=b.fendtno " +
                        "order by a.fvseq";
            dataGridView1.DataSource = DBHelper.GetDataSet(Sql);
            this.dataGridView1.Columns[0].Visible = false;
            this.dataGridView1.Columns[1].Visible = false;
            this.dataGridView1.Columns[2].Visible = false;
            if (dataGridView1.RowCount == 0) { button2.Visible = false; }
            else { button2.Visible = true; }
        }


        private void Policylist_SelectionChanged(object sender, EventArgs e)
        {
            if (dataGridView1.CurrentCell != null)
            {
                Policylistrow = dataGridView1.CurrentCell.RowIndex;
            }
            else { dataGridView1.CurrentCell = dataGridView1.Rows[0].Cells["Policy#"]; }

            if (dataGridView1.Rows[Policylistrow].Cells["fctlid"].Value != null)
            {
                if (dataGridView1.Rows[Policylistrow].Cells["fctlid"].Value.ToString().Length != 0)
                {
                    fctlid = dataGridView1.Rows[Policylistrow].Cells["fctlid"].Value.ToString();
                }
            }
            
        }

        private void button1_Click(object sender, EventArgs e)
        {
            if (flag == "Endor")
            {
                endorsement.fendtype = "";
                endorsement.fexten = "";
                this.Close();
            }
            if (flag == "EndorRI")
            {
                rIEndt.fendtype = "";
                rIEndt.fexten = "";
                this.Close();
            } 
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (flag == "Endor")
            {
                endorsement.fctlid_v = fctlid;
                this.Close();
            }
            if (flag == "EndorRI")
            {
                rIEndt.fctlid_v = fctlid;
                this.Close();
            }
        }
    }
}
