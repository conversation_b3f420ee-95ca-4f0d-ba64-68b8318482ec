using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;

namespace INS
{
    public partial class frmSiteSearch : Form
    {
        public frmSiteSearch()
        {
            InitializeComponent();
            FillData("", "");
        }

        public frmSiteSearch(Ctrl.DirectGen directGen)
        {
            // TODO: Complete member initialization
            this.directGen = directGen;
            InitializeComponent();
        }


        public frmSiteSearch(Ctrl.RIGen rIGen)
        {
            // TODO: Complete member initialization
            this.rIGen = rIGen;
            InitializeComponent();
        }

        public frmSiteSearch(Ctrl.EndorDirectGen endorDirectGen)
        {
            // TODO: Complete member initialization
            this.endorDirectGen = endorDirectGen;
            InitializeComponent();
        }

        public frmSiteSearch(Ctrl.EndorRIGen endorRIGen)
        {
            // TODO: Complete member initialization
            this.endorRIGen = endorRIGen;
            InitializeComponent();
        }

        public frmSiteSearch(Ctrl.Claim.REG rEG)
        {
            // TODO: Complete member initialization
            this.rEG = rEG;
            InitializeComponent();
        }

        public frmSiteSearch(Ctrl.Claim.EciREG eciREG)
        {
            // TODO: Complete member initialization
            this.eciREG = eciREG;
            InitializeComponent();
        }

        public frmSiteSearch(Ctrl.Claim.riEciREG riEciREG)
        {
            // TODO: Complete member initialization
            this.riEciREG = riEciREG;
            InitializeComponent();
        }


        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "";
        private Ctrl.DirectGen directGen;
        private Ctrl.RIGen rIGen;
        private Ctrl.EndorDirectGen endorDirectGen;
        private Ctrl.EndorRIGen endorRIGen;
        private Ctrl.Claim.REG rEG;
        private Ctrl.Claim.EciREG eciREG;
        private Ctrl.Claim.riEciREG riEciREG;

        public void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            if (order == "" && query == "")
            {
                a = new SqlDataAdapter("select fid as Site#, fdesc as Desc# from [dbo].[msite] where ftype ='S' ORDER BY FID", c);
            }
            else if (order != "")
            {
                a = new SqlDataAdapter("select fid as Site#, fdesc as Desc# from [dbo].[msite] where ftype ='S' ORDER BY '" + order + "'", c);
            }
            else if (query != "")
            {
                a = new SqlDataAdapter("select fid as Site#, fdesc as Desc# from [dbo].[msite] where ftype ='S' and fdesc = '" + query + "'", c);
            }
            else
            {
                a = new SqlDataAdapter("select fid as Site#, fdesc as Desc# from [dbo].[msite] where ftype ='S' and fdesc = '" + query + "' order by fid", c);
            }
            DataTable t = new DataTable();
            a.Fill(t);
            dataGridView1.DataSource = t;
            c.Close();
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }
                if (dataGridView1.Rows[row].Cells["Site#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Site#"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["Site#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["Desc#"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            if (flag == "Direct")
            {
                directGen.SiteValue = textBox1.Text.ToString().Trim();
                directGen.SiteDesc = label3.Text.ToString().Trim();
                this.Close();
            }
            if (flag == "RI")
            {
                rIGen.SiteValue = textBox1.Text.ToString().Trim();
                rIGen.SiteDesc = label3.Text.ToString().Trim();
                this.Close();
            }
            if (flag == "Endor")
            {
                endorDirectGen.SiteValue = textBox1.Text.ToString().Trim();
                endorDirectGen.SiteDesc = label3.Text.ToString().Trim();
                this.Close();
            }
            if (flag == "EndorRI")
            {
                endorRIGen.SiteValue = textBox1.Text.ToString().Trim();
                endorRIGen.SiteDesc = label3.Text.ToString().Trim();
                this.Close();
            }
            if (flag == "REG")
            {
                rEG.SiteValue = textBox1.Text.ToString().Trim();
                rEG.SiteDesc = label3.Text.ToString().Trim();
                this.Close();
            }
            if (flag == "EciREG")
            {
                eciREG.SiteValue = textBox1.Text.ToString().Trim();
                eciREG.SiteDesc = label3.Text.ToString().Trim();
                this.Close();
            }
            if (flag == "riEciREG")
            {
                riEciREG.SiteValue = textBox1.Text.ToString().Trim();
                riEciREG.SiteDesc = label3.Text.ToString().Trim();
                this.Close();
            }
        }


        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        

    }
}
