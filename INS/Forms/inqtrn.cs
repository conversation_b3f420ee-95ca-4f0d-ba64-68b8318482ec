using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business
{
    public partial class inqtrn : Form
    {
        private Ctrl.DirectGen directGen;
        public string Dbo = InsEnvironment.DataBase.GetDbo();

        public inqtrn()
        {
            InitializeComponent();
        }

        public inqtrn(Ctrl.DirectGen directGen)
        {
            // TODO: Complete member initialization
            this.directGen = directGen;
            InitializeComponent();
        }

        public void filldata(string fctlid_p)
        {
            string sql = "SELECT	fctlid,fctlid_p,fctlid_e,fpolno,fendtno,fclmno,fsrcclm,fcedeclm,flosdate, " +
                "fosamt+ftosamt as fosamt,fosretn+ftosretn as fosretn,fostty+ftostty as fostty, " +
                "fosxol+ftosxol as fosxol,fosfac+ftosfac as fosfac,fosfacb+ftosfacb as fosfacb, " +
                "fosfacnp+fosfacnp as fosfacnp, " +
                "fpayamt+ftpayamt as fpayamt,fpayretn+ftpayretn as fpayretn,fpaytty+ftpaytty as fpaytty, " +
                "fpayxol+ftpayxol as fpayxol,fpayfac+ftpayfac as fpayfac,fpayfacb+ftpayfacb as fpayfacb, " +
                "fpayfacnp+fpayfacnp as fpayfacnp, " +
                "fincamt+ftincamt as fincamt,fincretn+ftincretn as fincretn,finctty+ftinctty as finctty, " +
                "fincxol+ftincxol as fincxol,fincfac+ftincfac as fincfac,fincfacb+ftincfacb as fincfacb, " +
                "fincfacnp+fincfacnp as fincfacnp, " +
                "frcyamt+ftrcyamt as frcyamt,frcyretn+ftrcyretn as frcyretn,frcytty+ftrcytty as frcytty, " +
                "frcyxol+ftrcyxol as frcyxol,frcyfac+ftrcyfac as frcyfac,frcyfacb+ftrcyfacb as frcyfacb, " +
                "frcyfacnp+frcyfacnp as frcyfacnp, " +
                "fmode FROM oclaim where fctlid_p = '" + fctlid_p + "' and fstatus = '3'";
            s2a.DataSource = DBHelper.GetDataSet(sql);
            foreach (DataGridViewColumn Column in s2a.Columns)
            {
                Column.Visible = false;
            }
            s2a.Columns["fclmno"].Visible = true;
            s2a.Columns["flosdate"].Visible = true;
            s2a.Columns["fosamt"].Visible = true;
            s2a.Columns["fpayamt"].Visible = true;
            s2a.Columns["fincamt"].Visible = true;
            s2a.Columns["frcyamt"].Visible = true;
            s2a.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.s2a_CellFormatting);

            string sql1 = "SELECT fctlid,fctlid_p,fpolno,fendtno,fsrcref,fcedepol,fcedeendt,fefffr,feffto,fgpm,ftdamt,fcomamt,fnpm " +
                          "FROM polh where fctlid_p = '" + fctlid_p + "' and fconfirm = '3' and (fgpm <> 0 or fnpm <> 0)";
            s1a.DataSource = DBHelper.GetDataSet(sql1);
            foreach (DataGridViewColumn Column in s1a.Columns)
            {
                Column.Visible = false;
            }
            s1a.Columns["fpolno"].Visible = true;
            s1a.Columns["fendtno"].Visible = true;
            s1a.Columns["fefffr"].Visible = true;
            s1a.Columns["feffto"].Visible = true;
            s1a.Columns["fgpm"].Visible = true;
            s1a.Columns["ftdamt"].Visible = true;
            s1a.Columns["fcomamt"].Visible = true;
            s1a.Columns["fnpm"].Visible = true;
            s1a.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.s1a_CellFormatting);
            caltotal(fctlid_p);

            string sql2 = "select * from LossRatio where fctlid_p = '" + fctlid_p + "'";
            DataTable dt = DBHelper.GetDataSet(sql2);
            if (dt.Rows.Count >0)
            {
                NoClosedClaim.Text = dt.Rows[0]["NoofClosedClaim"].ToString();
                NoofOSClaim.Text = dt.Rows[0]["NoofOSClaim"].ToString();
                GrossLossRatio.Text = (Fct.sdFat(dt.Rows[0]["GrossLossRatio"])*100).ToString();
                NetLossRatio.Text = (Fct.sdFat(dt.Rows[0]["NetLossRatio"]) * 100).ToString();
                allClaim.Text = dt.Rows[0]["allClaim"].ToString();
                Nowithoutpayment.Text = dt.Rows[0]["Noofwithpayment"].ToString();
                Form2.Text = dt.Rows[0]["NoofOSClaimWithoutForm2"].ToString();
            }
        }

        private void s2a_CellFormatting(object sender, System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            s2a.Columns["fclmno"].HeaderText = "Claim#";
            s2a.Columns["flosdate"].HeaderText = "DOL";
            s2a.Columns["flosdate"].DefaultCellStyle.Format = "dd'/'MM'/'yyyy";
            s2a.Columns["fosamt"].HeaderText = "O/S Amount";
            s2a.Columns["fpayamt"].HeaderText = "Claims Paid";
            s2a.Columns["fincamt"].HeaderText = "Incurred Loss";
            s2a.Columns["frcyamt"].HeaderText = "Recovery";
        }

        private void s1a_CellFormatting(object sender, System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            s1a.Columns["fpolno"].HeaderText = "Policy#";
            s1a.Columns["fendtno"].HeaderText = "Endt No.";
            s1a.Columns["fefffr"].HeaderText = "Effect Fr";
            s1a.Columns["fefffr"].DefaultCellStyle.Format = "dd'/'MM'/'yyyy";
            s1a.Columns["feffto"].HeaderText = "Effect To";
            s1a.Columns["feffto"].DefaultCellStyle.Format = "dd'/'MM'/'yyyy";
            s1a.Columns["fgpm"].HeaderText = "Gross Prem";
            s1a.Columns["ftdamt"].HeaderText = "Discount";
            s1a.Columns["fcomamt"].HeaderText = "Commission";
            s1a.Columns["fnpm"].HeaderText = "Net Prem";
        }

        void caltotal(string fctlid_p)
        {
            decimal ln_fgpm = 0, ln_ftdamt = 0, ln_fcomamt = 0, ln_fnpm = 0,
            ln_fosamt = 0, ln_fpayamt = 0, ln_fincamt = 0, ln_frcyamt = 0,
            ln_fosretn = 0, ln_fpayretn = 0, ln_fincretn = 0, ln_frcyretn = 0,
            ln_fostty = 0, ln_fpaytty = 0, ln_finctty = 0, ln_frcytty = 0,
            ln_fosxol = 0, ln_fpayxol = 0, ln_fincxol = 0, ln_frcyxol = 0,
            ln_fosfac = 0, ln_fpayfac = 0, ln_fincfac = 0, ln_frcyfac = 0,
            ln_fosfacb = 0, ln_fpayfacb = 0, ln_fincfacb = 0, ln_frcyfacb = 0,
            ln_fosfacnp = 0, ln_fpayfacnp = 0, ln_fincfacnp = 0, ln_frcyfacnp = 0;

            decimal ln_tgpm = 0, ln_tretn = 0, ln_ttty = 0, ln_tfac = 0, ln_tfacb = 0, ln_tfacnp = 0, ln_txol = 0;
            SqlParameter[] param = new SqlParameter[] {
                    new SqlParameter("@vfctlid_p", fctlid_p)};
            DataSet ds = new DataSet();
            ds = DBHelper.GetDtSetProd("inqtrnTol", Dbo, param);
            if (ds.Tables.Count != 0)
            {
                ln_fgpm = Fct.sdFat(ds.Tables[0].Rows[0]["ln_fgpm"]);
                ln_ftdamt = Fct.sdFat(ds.Tables[0].Rows[0]["ln_ftdamt"]);
                ln_fcomamt = Fct.sdFat(ds.Tables[0].Rows[0]["ln_fcomamt"]);
                ln_fnpm = Fct.sdFat(ds.Tables[0].Rows[0]["ln_fnpm"]);
                fgpm_bf.Text = ln_fgpm.ToString();
                ftdamt_bf.Text = ln_ftdamt.ToString();
                fcomamt_bf.Text = ln_fcomamt.ToString();
                fnpm_bf.Text = ln_fnpm.ToString();

                if (ds.Tables[1].Rows.Count > 0)
                {
                    ln_fosamt = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fosamt"]);
                    ln_fpayamt = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fpayamt"]);
                    ln_fincamt = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fincamt"]);
                    ln_frcyamt = Fct.sdFat(ds.Tables[1].Rows[0]["ln_frcyamt"]);
                    fosamt_bf.Text = ln_fosamt.ToString();
                    fpayamt_bf.Text = ln_fpayamt.ToString();
                    fincamt_bf.Text = ln_fincamt.ToString();
                    frcyamt_bf.Text = ln_frcyamt.ToString();

                    ln_fosamt = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fosamt"]);
                    ln_fosretn = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fosretn"]);
                    ln_fostty = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fostty"]);
                    ln_fosxol = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fosxol"]);
                    ln_fosfac = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fosfac"]);
                    ln_fosfacb = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fosfacb"]);
                    ln_fosfacnp = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fosfacnp"]);
                    fosamt_al.Text = ln_fosamt.ToString();
                    fosretn_al.Text = ln_fosretn.ToString();
                    fostty_al.Text = ln_fostty.ToString();
                    fosxol_al.Text = ln_fosxol.ToString();
                    fosfac_al.Text = ln_fosfac.ToString();
                    fosfacb_al.Text = ln_fosfacb.ToString();
                    fosfacnp_al.Text = ln_fosfacnp.ToString();

                    ln_fpayamt = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fpayamt"]);
                    ln_fpayretn = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fpayretn"]);
                    ln_fpaytty = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fpaytty"]);
                    ln_fpayxol = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fpayxol"]);
                    ln_fpayfac = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fpayfac"]);
                    ln_fpayfacb = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fpayfacb"]);
                    ln_fpayfacnp = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fpayfacnp"]);
                    fpayamt_al.Text = ln_fpayamt.ToString();
                    fpayretn_al.Text = ln_fpayretn.ToString();
                    fpaytty_al.Text = ln_fpaytty.ToString();
                    fpayxol_al.Text = ln_fpayxol.ToString();
                    fpayfac_al.Text = ln_fpayfac.ToString();
                    fpayfacb_al.Text = ln_fpayfacb.ToString();
                    fpayfacnp_al.Text = ln_fpayfacnp.ToString();

                    ln_fincamt = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fincamt"]);
                    ln_fincretn = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fincretn"]);
                    ln_finctty = Fct.sdFat(ds.Tables[1].Rows[0]["ln_finctty"]);
                    ln_fincxol = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fincxol"]);
                    ln_fincfac = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fincfac"]);
                    ln_fincfacb = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fincfacb"]);
                    ln_fincfacnp = Fct.sdFat(ds.Tables[1].Rows[0]["ln_fincfacnp"]);
                    fincamt_al.Text = ln_fincamt.ToString();
                    fincretn_al.Text = ln_fincretn.ToString();
                    finctty_al.Text = ln_finctty.ToString();
                    fincxol_al.Text = ln_fincxol.ToString();
                    fincfac_al.Text = ln_fincfac.ToString();
                    fincfacb_al.Text = ln_fincfacb.ToString();
                    fincfacnp_al.Text = ln_fincfacnp.ToString();

                    ln_frcyamt = Fct.sdFat(ds.Tables[1].Rows[0]["ln_frcyamt"]);
                    ln_frcyretn = Fct.sdFat(ds.Tables[1].Rows[0]["ln_frcyretn"]);
                    ln_frcytty = Fct.sdFat(ds.Tables[1].Rows[0]["ln_frcytty"]);
                    ln_frcyxol = Fct.sdFat(ds.Tables[1].Rows[0]["ln_frcyxol"]);
                    ln_frcyfac = Fct.sdFat(ds.Tables[1].Rows[0]["ln_frcyfac"]);
                    ln_frcyfacb = Fct.sdFat(ds.Tables[1].Rows[0]["ln_frcyfacb"]);
                    ln_frcyfacnp = Fct.sdFat(ds.Tables[1].Rows[0]["ln_frcyfacnp"]);
                    frcyamt_al.Text = ln_frcyamt.ToString();
                    frcyretn_al.Text = ln_frcyretn.ToString();
                    frcytty_al.Text = ln_frcytty.ToString();
                    frcyxol_al.Text = ln_frcyxol.ToString();
                    frcyfac_al.Text = ln_frcyfac.ToString();
                    frcyfacb_al.Text = ln_frcyfacb.ToString();
                    frcyfacnp_al.Text = ln_frcyfacnp.ToString();
                }
                ln_tgpm = Fct.sdFat(ds.Tables[2].Rows[0]["ln_tgpm"]);
                if (ds.Tables[3].Rows.Count > 0) {
                    ln_ttty = Fct.sdFat(ds.Tables[3].Rows[0]["ln_ttty"]);
                    ln_txol = Fct.sdFat(ds.Tables[3].Rows[0]["ln_txol"]);
                    ln_tfac = Fct.sdFat(ds.Tables[3].Rows[0]["ln_tfac"]);
                    ln_tfacb = Fct.sdFat(ds.Tables[3].Rows[0]["ln_tfacb"]);
                    ln_tfacnp = Fct.sdFat(ds.Tables[3].Rows[0]["ln_tfacnp"]);

                }

                ln_tretn = ln_tgpm - ln_ttty - ln_txol - ln_tfac - ln_tfacb - ln_tfacnp;
                fgpm.Text = ln_tgpm.ToString();
                fretn.Text = ln_tretn.ToString();
                ftty.Text = ln_ttty.ToString();
                ffac.Text = ln_tfac.ToString();
                ffacb.Text = ln_tfacb.ToString();
                fxol.Text = ln_txol.ToString();
                ffacnp.Text = ln_tfacnp.ToString();
            }
        }

        void calrevbrkdn(string fctlid_p, string fctlid_e)
        {
            decimal ln_tgpm = 0, ln_tretn = 0, ln_ttty = 0, ln_tfac = 0, ln_tfacb = 0, ln_tfacnp = 0, ln_txol = 0;
            DataSet ds1 = new DataSet();
            SqlParameter[] param = new SqlParameter[] {
                    new SqlParameter("@vfctlid_p", fctlid_p),
                    new SqlParameter("@vfctlid_e", fctlid_e)};
            ds1 = DBHelper.GetDtSetProd("inqtrnBrk", Dbo, param);
            if (ds1.Tables.Count > 0) { 
                ln_tgpm = Fct.sdFat(ds1.Tables[0].Rows[0]["ln_tgpm"]);
                ln_ttty = Fct.sdFat(ds1.Tables[1].Rows[0]["ln_ttty"]);
                ln_txol = Fct.sdFat(ds1.Tables[1].Rows[0]["ln_txol"]);
                ln_tfac = Fct.sdFat(ds1.Tables[1].Rows[0]["ln_tfac"]);
                ln_tfacb = Fct.sdFat(ds1.Tables[1].Rows[0]["ln_tfacb"]);
                ln_tfacnp = Fct.sdFat(ds1.Tables[1].Rows[0]["ln_tfacnp"]);
            }
            ln_tretn = ln_tgpm - ln_ttty - ln_txol - ln_tfac - ln_tfacb - ln_tfacnp;
            tgpm.Text = ln_tgpm.ToString();
            tretn.Text = ln_tretn.ToString();
            ttty.Text = ln_ttty.ToString();
            tfac.Text = ln_tfac.ToString();
            tfacb.Text = ln_tfacb.ToString();
            txol.Text = ln_txol.ToString();
            tfacnp.Text = ln_tfacnp.ToString();
        }

        private void s1a_CurrentCellChanged(object sender, EventArgs e)
        {
            try
            {
                int s1arow = 0;
                if (s1a.CurrentCell != null)
                {
                    s1arow = s1a.CurrentCell.RowIndex;
                }
                else { s1a.CurrentCell = s1a.Rows[0].Cells["fctlid"]; }
                fendtno.Text = s1a.Rows[s1arow].Cells["fendtno"].Value.ToString();
                textBox4.Text = s1a.Rows[s1arow].Cells["fpolno"].Value.ToString();
                textBox3.Text = s1a.Rows[s1arow].Cells["fpolno"].Value.ToString();
                textBox2.Text = s1a.Rows[s1arow].Cells["fpolno"].Value.ToString();
                fpolno.Text = s1a.Rows[s1arow].Cells["fpolno"].Value.ToString();
                calrevbrkdn(s1a.Rows[s1arow].Cells["fctlid_p"].Value.ToString(), s1a.Rows[s1arow].Cells["fctlid"].Value.ToString());
            }
            catch { }
        }

        private void s2a_CurrentCellChanged(object sender, EventArgs e)
        {
            try
            {
                int s2arow = 0;
                if (s2a.CurrentCell != null)
                {
                    s2arow = s2a.CurrentCell.RowIndex;
                }
                else { s2a.CurrentCell = s2a.Rows[0].Cells["fctlid"]; }
                fclmno.Text = s2a.Rows[s2arow].Cells["fclmno"].Value.ToString();
                fosamt.Text = s2a.Rows[s2arow].Cells["fosamt"].Value.ToString();
                fosretn.Text = s2a.Rows[s2arow].Cells["fosretn"].Value.ToString();
                fosfac.Text = s2a.Rows[s2arow].Cells["fosfac"].Value.ToString();
                fosfacnp.Text = s2a.Rows[s2arow].Cells["fosfacnp"].Value.ToString();
                fostty.Text = s2a.Rows[s2arow].Cells["fostty"].Value.ToString();
                fosfacb.Text = s2a.Rows[s2arow].Cells["fosfacb"].Value.ToString();
                fosxol.Text = s2a.Rows[s2arow].Cells["fosxol"].Value.ToString();

                fpayamt.Text = s2a.Rows[s2arow].Cells["fpayamt"].Value.ToString();
                fpayretn.Text = s2a.Rows[s2arow].Cells["fpayretn"].Value.ToString();
                fpayfac.Text = s2a.Rows[s2arow].Cells["fpayfac"].Value.ToString();
                fpayfacnp.Text = s2a.Rows[s2arow].Cells["fpayfacnp"].Value.ToString();
                fpaytty.Text = s2a.Rows[s2arow].Cells["fpaytty"].Value.ToString();
                fpayfacb.Text = s2a.Rows[s2arow].Cells["fpayfacb"].Value.ToString();
                fpayxol.Text = s2a.Rows[s2arow].Cells["fpayxol"].Value.ToString();

                fincamt.Text = s2a.Rows[s2arow].Cells["fincamt"].Value.ToString();
                fincretn.Text = s2a.Rows[s2arow].Cells["fincretn"].Value.ToString();
                fincfac.Text = s2a.Rows[s2arow].Cells["fincfac"].Value.ToString();
                fincfacnp.Text = s2a.Rows[s2arow].Cells["fincfacnp"].Value.ToString();
                finctty.Text = s2a.Rows[s2arow].Cells["finctty"].Value.ToString();
                fincfacb.Text = s2a.Rows[s2arow].Cells["fincfacb"].Value.ToString();
                fincxol.Text = s2a.Rows[s2arow].Cells["fincxol"].Value.ToString();

                frcyamt.Text = s2a.Rows[s2arow].Cells["frcyamt"].Value.ToString();
                frcyretn.Text = s2a.Rows[s2arow].Cells["frcyretn"].Value.ToString();
                frcyfac.Text = s2a.Rows[s2arow].Cells["frcyfac"].Value.ToString();
                frcyfacnp.Text = s2a.Rows[s2arow].Cells["frcyfacnp"].Value.ToString();
                frcytty.Text = s2a.Rows[s2arow].Cells["frcytty"].Value.ToString();
                frcyfacb.Text = s2a.Rows[s2arow].Cells["frcyfacb"].Value.ToString();
                frcyxol.Text = s2a.Rows[s2arow].Cells["frcyxol"].Value.ToString();
            }
            catch { }
        }


    }
}
