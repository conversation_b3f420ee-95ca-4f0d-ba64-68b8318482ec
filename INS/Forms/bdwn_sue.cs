using INS.INSClass;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
//FZ2YHY48
namespace INS.Business.SearchForm
{
    public partial class bdwn_sue : Form
    {

        public string fctlid, page = "", flag = "";
        public bool cEditMode = false;
        public string db = InsEnvironment.DataBase.GetDbm();
        private Ctrl.Claim.EciSUM eciSUM;
        public bdwn_sue()
        {
            InitializeComponent();
        }

        public bdwn_sue(Ctrl.Claim.EciSUM eciSUM)
        {
            // TODO: Complete member initialization
            this.eciSUM = eciSUM;
            InitializeComponent();
            foreach (Control control in this.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件             //添加事件
            }
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            TextBox focusTextBox = null;

            if (e.KeyCode == Keys.Enter)
            {                    //判断用户是否按下回车键
                if (sender is TextBox)
                {
                    focusTextBox = (TextBox)sender;
                    if (!focusTextBox.AcceptsReturn)
                        SendKeys.Send("{TAB}");
                }
                else SendKeys.Send("{TAB}");
            }
        }

        public void tpReload(string str)
        {
            fclmno.Text = str;
            string sql = "select * from ocabk_oe where fctlid_c =(select fctlid from oclaim where fclmno = '" + str + "')";
            DataTable dt = DBHelper.GetDataSet(sql);
            if (dt.Rows.Count > 0)
            {

                fos01.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fos01"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj01"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpay01"].ToString()));
                fpay01.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpay01"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpay01"].ToString()));
                finc01.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadj01"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj01"].ToString()));
                frvy01.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvy01"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvy01"].ToString()));
                fos02.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fos02"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj02"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpay02"].ToString()));
                fpay02.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpay02"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpay02"].ToString()));
                finc02.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadj02"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj02"].ToString()));
                frvy02.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvy02"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvy02"].ToString()));
                fos03.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fos03"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj03"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpay03"].ToString()));
                fpay03.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpay03"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpay03"].ToString()));
                finc03.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadj03"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj03"].ToString()));
                frvy03.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvy03"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvy03"].ToString()));
                fos04.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fos04"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj04"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpay04"].ToString()));
                fpay04.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpay04"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpay04"].ToString()));
                finc04.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadj04"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj04"].ToString()));
                frvy04.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvy04"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvy04"].ToString()));
                fos05.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fos05"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj05"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpay05"].ToString()));
                fpay05.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpay05"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpay05"].ToString()));
                finc05.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadj05"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj05"].ToString()));
                frvy05.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvy05"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvy05"].ToString()));
                fos06.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fos06"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj06"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpay06"].ToString()));
                fpay06.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpay06"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpay06"].ToString()));
                finc06.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadj06"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj06"].ToString()));
                frvy06.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvy06"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvy06"].ToString()));
                fos07.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fos07"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj07"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpay07"].ToString()));
                fpay07.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpay07"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpay07"].ToString()));
                finc07.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadj07"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj07"].ToString()));
                frvy07.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvy07"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvy07"].ToString()));
                fos08.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fos08"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj08"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpay08"].ToString()));
                fpay08.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpay08"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpay08"].ToString()));
                finc08.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadj08"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj08"].ToString()));
                frvy08.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvy08"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvy08"].ToString()));
                fos09.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fos09"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj09"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpay09"].ToString()));
                fpay09.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpay09"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpay09"].ToString()));
                finc09.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadj09"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj09"].ToString()));
                frvy09.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvy09"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvy09"].ToString()));
                fos10.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fos10"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj10"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpay10"].ToString()));
                fpay10.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpay10"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpay10"].ToString()));
                finc10.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadj10"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj10"].ToString()));
                frvy10.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvy10"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvy10"].ToString()));
                fosA.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fosA"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadjA"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpayA"].ToString()));
                fpayA.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpayA"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpayA"].ToString()));
                fincA.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadjA"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadjA"].ToString()));
                frvyA.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvyA"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvyA"].ToString()));
                fosB.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fosB"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadjB"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpayB"].ToString()));
                fpayB.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpayB"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpayB"].ToString()));
                fincB.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadjB"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadjB"].ToString()));
                frvyB.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvyB"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvyB"].ToString()));
                fosC.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fosC"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadjC"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpayC"].ToString()));
                fpayC.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpayC"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpayC"].ToString()));
                fincC.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadjC"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadjC"].ToString()));
                frvyC.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvyC"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvyC"].ToString()));
                fos.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fos"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpay"].ToString()));
                fpay.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpay"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpay"].ToString()));
                finc.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadj"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj"].ToString()));
                frvy.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvy"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvy"].ToString()));
            }
        }

        private void XcmdExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }



    }
}
