using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;

namespace INS
{
    public partial class frmInsuredSearch : Form
    {
        public frmInsuredSearch()
        {
            InitializeComponent();
            FillData("", "");
        }

        public frmInsuredSearch(Ctrl.DirectInsured_CAR directInsured_CAR)
        {
            // TODO: Complete member initialization
            this.directInsured_CAR = directInsured_CAR;
            InitializeComponent();
        }

        public frmInsuredSearch(Ctrl.RIInsured_CAR rIInsured_CAR)
        {
            // TODO: Complete member initialization
            this.rIInsured_CAR = rIInsured_CAR;
            InitializeComponent();
        }

        public frmInsuredSearch(Ctrl.DirectInsured_PAR directInsured_PAR)
        {
            // TODO: Complete member initialization
            this.directInsured_PAR = directInsured_PAR;
            InitializeComponent();
        }

        public frmInsuredSearch(Ctrl.RIInsured_PAR rIInsured_PAR)
        {
            // TODO: Complete member initialization
            this.rIInsured_PAR = rIInsured_PAR;
            InitializeComponent();
        }

        public frmInsuredSearch(Ctrl.EndorDirectInsured_CAR endorDirectInsured_CAR)
        {
            // TODO: Complete member initialization
            this.endorDirectInsured_CAR = endorDirectInsured_CAR;
            InitializeComponent();
        }

        public frmInsuredSearch(Ctrl.EndorDirectInsured_PAR endorDirectInsured_PAR)
        {
            // TODO: Complete member initialization
            this.endorDirectInsured_PAR = endorDirectInsured_PAR;
            InitializeComponent();
        }

        public frmInsuredSearch(Ctrl.EndorRIInsured_CAR endorRIInsured_CAR)
        {
            // TODO: Complete member initialization
            this.endorRIInsured_CAR = endorRIInsured_CAR;
            InitializeComponent();
        }

        public frmInsuredSearch(Ctrl.EndorRIInsured_PAR endorRIInsured_PAR)
        {
            // TODO: Complete member initialization
            this.endorRIInsured_PAR = endorRIInsured_PAR;
            InitializeComponent();
        }


        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "";
        public string section = "";
        private String _fclass;
        private Ctrl.DirectInsured_CAR directInsured_CAR;
        private Ctrl.DirectInsured_PAR directInsured_PAR;
        private Ctrl.RIInsured_CAR rIInsured_CAR;
        private Ctrl.RIInsured_PAR rIInsured_PAR;
        private Ctrl.EndorDirectInsured_CAR endorDirectInsured_CAR;
        private Ctrl.EndorDirectInsured_PAR endorDirectInsured_PAR;
        private Ctrl.EndorRIInsured_CAR endorRIInsured_CAR;
        private Ctrl.EndorRIInsured_PAR endorRIInsured_PAR;

        public String Fclass
        {
            set
            {
                _fclass = value;
            }
        }

        public void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            if (order == "" && query == "")
            {
                a = new SqlDataAdapter("select b.fid as Clause#, b.fdesc as Desc#, b.flogseq as Seq#, fsid as Item#, fcontent " +
                    "from minsc a left join misec b on a.fctlid=b.fctlid_a  " +
                    "left join (select b.fid,b.fctlid from minsc a " +
                    "left join misec b on a.fctlid=b.fctlid_a  " +
                    "where ftype =1 and fdoctype =1 ) c on c.fctlid = b.fctlid_1 " +
                    "where fdoctype =1 and ftype =2 and a.fid ='" + _fclass + "' and c.fid ='" + section + "'", c);
            }
            else if (order != "")
            {
                a = new SqlDataAdapter("select b.fid as Clause#, b.fdesc as Desc#, b.flogseq as Seq#, fsid as Item#, fcontent " +
                    "from minsc a left join misec b on a.fctlid=b.fctlid_a  " +
                    "left join (select b.fid,b.fctlid from minsc a " +
                    "left join misec b on a.fctlid=b.fctlid_a  " +
                    "where ftype =1 and fdoctype =1 ) c on c.fctlid = b.fctlid_1 " +
                    "where fdoctype =1 and ftype =2 and a.fid ='" + _fclass + "' and c.fid ='" + section + "' " +
                                        "order by '" + order + "'", c);
            }
            else if (query != "")
            {
                a = new SqlDataAdapter("select b.fid as Clause#, b.fdesc as Desc#, b.flogseq as Seq#, fsid as Item#, fcontent " +
                    "from minsc a left join misec b on a.fctlid=b.fctlid_a  " +
                    "left join (select b.fid,b.fctlid from minsc a " +
                    "left join misec b on a.fctlid=b.fctlid_a  " +
                    "where ftype =1 and fdoctype =1 ) c on c.fctlid = b.fctlid_1 " +
                    "where fdoctype =1 and ftype =2 and a.fid ='" + _fclass + "' and c.fid ='" + section + "' " +
                                        "AND b.fdesc = '" + query + "'", c);
            }
            else
            {
                a = new SqlDataAdapter("select b.fid as Clause#, b.fdesc as Desc#, b.flogseq as Seq#, fsid as Item#, fcontent " +
                    "from minsc a left join misec b on a.fctlid=b.fctlid_a  " +
                    "left join (select b.fid,b.fctlid from minsc a " +
                    "left join misec b on a.fctlid=b.fctlid_a  " +
                    "where ftype =1 and fdoctype =1 ) c on c.fctlid = b.fctlid_1 " +
                    "where fdoctype =1 and ftype =2 and a.fid ='" + _fclass + "' and c.fid ='" + section + "' " +
                                        "AND b.fdesc = '" + query + "' order by flogseq", c);
            }
            DataTable t = new DataTable();
            a.Fill(t);
            dataGridView1.DataSource = t;
            this.dataGridView1.Columns[2].Visible = false;
            this.dataGridView1.Columns[3].Visible = false;
            this.dataGridView1.Columns[4].Visible = false;
            c.Close();
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }
                if (dataGridView1.Rows[row].Cells["Clause#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Clause#"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["Clause#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["Desc#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Seq#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Seq#"].Value.ToString().Length != 0)
                    {
                        label4.Text = dataGridView1.Rows[row].Cells["Seq#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Item#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Item#"].Value.ToString().Length != 0)
                    {
                        label5.Text = dataGridView1.Rows[row].Cells["Item#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["fcontent"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["fcontent"].Value.ToString().Length != 0)
                    {
                        label6.Text = dataGridView1.Rows[row].Cells["fcontent"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            if (flag == "DirectInsured_CAR")
            {
                if (section == "SEC1A")
                {
                    directInsured_CAR.InsuredValue =  Fct.stFat(textBox1.Text.ToString());
                    directInsured_CAR.InsuredDesc =  Fct.stFat(label3.Text.ToString());
                    directInsured_CAR.InsuredSeq =  Fct.stFat(label4.Text.ToString());
                    directInsured_CAR.InsuredItem =  Fct.stFat(label5.Text.ToString());
                    directInsured_CAR.InsuredContent =  Fct.stFat(label6.Text.ToString());
                    directInsured_CAR.setControlFocus("afid");
                    this.Close();
                }
                else
                {
                    directInsured_CAR.InsuredValue2 =  Fct.stFat(textBox1.Text.ToString());
                    directInsured_CAR.InsuredDesc2 =  Fct.stFat(label3.Text.ToString());
                    directInsured_CAR.InsuredItem2 =  Fct.stFat(label5.Text.ToString());
                    directInsured_CAR.InsuredContent2 =  Fct.stFat(label6.Text.ToString());
                    this.Close();
                }

            }
            if (flag == "RIInsured_CAR")
            {
                if (section == "SEC1A")
                {
                    rIInsured_CAR.InsuredValue =  Fct.stFat(textBox1.Text.ToString());
                    rIInsured_CAR.InsuredDesc =  Fct.stFat(label3.Text.ToString());
                    rIInsured_CAR.InsuredSeq =  Fct.stFat(label4.Text.ToString());
                    rIInsured_CAR.InsuredItem =  Fct.stFat(label5.Text.ToString());
                    rIInsured_CAR.InsuredContent =  Fct.stFat(label6.Text.ToString());
                    this.Close();
                }
                else
                {
                    rIInsured_CAR.InsuredValue2 =  Fct.stFat(textBox1.Text.ToString());
                    rIInsured_CAR.InsuredDesc2 =  Fct.stFat(label3.Text.ToString());
                    rIInsured_CAR.InsuredItem2 =  Fct.stFat(label5.Text.ToString());
                    rIInsured_CAR.InsuredContent2 =  Fct.stFat(label6.Text.ToString());
                    this.Close();
                }

            }
            if (flag == "EndorDirectInsured_CAR")
            {
                if (section == "SEC1A")
                {
                    endorDirectInsured_CAR.InsuredValue =  Fct.stFat(textBox1.Text.ToString());
                    endorDirectInsured_CAR.InsuredDesc =  Fct.stFat(label3.Text.ToString());
                    endorDirectInsured_CAR.InsuredSeq =  Fct.stFat(label4.Text.ToString());
                    endorDirectInsured_CAR.InsuredItem =  Fct.stFat(label5.Text.ToString());
                    endorDirectInsured_CAR.InsuredContent =  Fct.stFat(label6.Text.ToString());
                    this.Close();
                }
                else
                {
                    endorDirectInsured_CAR.InsuredValue2 =  Fct.stFat(textBox1.Text.ToString());
                    endorDirectInsured_CAR.InsuredDesc2 =  Fct.stFat(label3.Text.ToString());
                    endorDirectInsured_CAR.InsuredItem2 =  Fct.stFat(label5.Text.ToString());
                    endorDirectInsured_CAR.InsuredContent2 =  Fct.stFat(label6.Text.ToString());
                    this.Close();
                }

            }
            if (flag == "EndorRIInsured_CAR")
            {
                if (section != "SEC1A")
                {
                    endorRIInsured_CAR.InsuredValue2 =  Fct.stFat(textBox1.Text.ToString());
                    endorRIInsured_CAR.InsuredDesc2 =  Fct.stFat(label3.Text.ToString());
                    endorRIInsured_CAR.InsuredItem2 =  Fct.stFat(label5.Text.ToString());
                    endorRIInsured_CAR.InsuredContent2 =  Fct.stFat(label6.Text.ToString());
                }
                this.Close();
            }

            if (flag == "DirectInsured_PAR")
            {
                if (section == "SEC1B")
                {
                    directInsured_PAR.InsuredValue =  Fct.stFat(textBox1.Text.ToString());
                    directInsured_PAR.InsuredDesc =  Fct.stFat(label3.Text.ToString());
                    directInsured_PAR.InsuredSeq =  Fct.stFat(label4.Text.ToString());
                    directInsured_PAR.InsuredItem =  Fct.stFat(label5.Text.ToString());
                    directInsured_PAR.InsuredContent =  Fct.stFat(label6.Text.ToString());
                    this.Close();
                }
                else
                {
                    directInsured_PAR.InsuredValue2 =  Fct.stFat(textBox1.Text.ToString());
                    directInsured_PAR.InsuredDesc2 = Fct.stFat( label3.Text.ToString());
                    directInsured_PAR.InsuredItem2 =  Fct.stFat(label5.Text.ToString());
                    directInsured_PAR.InsuredContent2 =  Fct.stFat(label6.Text.ToString());
                    this.Close();
                }

            }

            if (flag == "RIInsured_PAR")
            {
                if (section == "SEC1B")
                {

                    rIInsured_PAR.InsuredValue =  Fct.stFat(textBox1.Text.ToString());
                    rIInsured_PAR.InsuredDesc =  Fct.stFat(label3.Text.ToString());
                    rIInsured_PAR.InsuredSeq =  Fct.stFat(label4.Text.ToString());
                    rIInsured_PAR.InsuredItem =  Fct.stFat(label5.Text.ToString());
                    rIInsured_PAR.InsuredContent =  Fct.stFat(label6.Text.ToString());
                    this.Close();
                }
                else
                {
                    rIInsured_PAR.InsuredValue2 =  Fct.stFat(textBox1.Text.ToString());
                    rIInsured_PAR.InsuredDesc2 =  Fct.stFat(label3.Text.ToString());
                    rIInsured_PAR.InsuredItem2 =  Fct.stFat(label5.Text.ToString());
                    rIInsured_PAR.InsuredContent2 =  Fct.stFat(label6.Text.ToString());
                    this.Close();
                }
            }


            if (flag == "EndorDirectInsured_PAR")
            {
                if (section == "SEC1B")
                {
                    endorDirectInsured_PAR.InsuredValue =  Fct.stFat(textBox1.Text.ToString());
                    endorDirectInsured_PAR.InsuredDesc =  Fct.stFat(label3.Text.ToString());
                    endorDirectInsured_PAR.InsuredSeq =  Fct.stFat(label4.Text.ToString());
                    endorDirectInsured_PAR.InsuredItem =  Fct.stFat(label5.Text.ToString());
                    endorDirectInsured_PAR.InsuredContent =  Fct.stFat(label6.Text.ToString());
                    this.Close();
                }
                else
                {
                    endorDirectInsured_PAR.InsuredValue2 =  Fct.stFat(textBox1.Text.ToString());
                    endorDirectInsured_PAR.InsuredDesc2 =  Fct.stFat(label3.Text.ToString());
                    endorDirectInsured_PAR.InsuredItem2 =  Fct.stFat(label5.Text.ToString());
                    endorDirectInsured_PAR.InsuredContent2 =  Fct.stFat(label6.Text.ToString());
                    this.Close();
                }
            }

            if (flag == "EndorRIInsured_PAR")
            {
                if (section == "SEC1B")
                {
                    string result2 = endorRIInsured_PAR.CheckDuplicate(textBox1.Text.ToString().Trim(), "");
                    if (result2 != "")
                    {
                        MessageBox.Show(result2);
                    }
                    else
                    {
                        endorRIInsured_PAR.InsuredValue =  Fct.stFat(textBox1.Text.ToString());
                        endorRIInsured_PAR.InsuredDesc =  Fct.stFat(label3.Text.ToString());
                        endorRIInsured_PAR.InsuredSeq =  Fct.stFat(label4.Text.ToString());
                        endorRIInsured_PAR.InsuredItem =  Fct.stFat(label5.Text.ToString());
                        endorRIInsured_PAR.InsuredContent =  Fct.stFat(label6.Text.ToString());
                        this.Close();
                    }
                }
                else
                {
                    endorRIInsured_PAR.InsuredValue2 =  Fct.stFat(textBox1.Text.ToString());
                    endorRIInsured_PAR.InsuredDesc2 =  Fct.stFat(label3.Text.ToString());
                    endorRIInsured_PAR.InsuredItem2 =  Fct.stFat(label5.Text.ToString());
                    endorRIInsured_PAR.InsuredContent2 =  Fct.stFat(label6.Text.ToString());
                    this.Close();
                }
            }
        }


        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }


    }
}
