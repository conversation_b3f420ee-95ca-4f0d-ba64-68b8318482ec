using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;

namespace INS
{
    public partial class frmNatlosSearch : Form
    {
        public frmNatlosSearch()
        {
            InitializeComponent();
            FillData("", "");
        }
      
        public frmNatlosSearch(Ctrl.Claim.REG rEG)
        {
            // TODO: Complete member initialization
            this.rEG = rEG;
            InitializeComponent();
        }

        public frmNatlosSearch(Ctrl.Claim.AMD aMD)
        {
            // TODO: Complete member initialization
            this.aMD = aMD;
            InitializeComponent();
        }

        public frmNatlosSearch(Ctrl.Claim.riREG riREG)
        {
            // TODO: Complete member initialization
            this.riREG = riREG;
            InitializeComponent();
        }

        public frmNatlosSearch(Ctrl.Claim.riAMD riAMD)
        {
            // TODO: Complete member initialization
            this.riAMD = riAMD;
            InitializeComponent();
        }

        public frmNatlosSearch(Ctrl.Claim.EciREG eciREG)
        {
            // TODO: Complete member initialization
            this.eciREG = eciREG;
            InitializeComponent();
        }

        public frmNatlosSearch(Ctrl.Claim.EciAMD eciAMD)
        {
            // TODO: Complete member initialization
            this.eciAMD = eciAMD;
            InitializeComponent();
        }

        public frmNatlosSearch(Ctrl.Claim.riEciAMD riEciAMD)
        {
            // TODO: Complete member initialization
            this.riEciAMD = riEciAMD;
            InitializeComponent();
        }

        public frmNatlosSearch(Ctrl.Claim.riEciREG riEciREG)
        {
            // TODO: Complete member initialization
            this.riEciREG = riEciREG;
            InitializeComponent();
        }

        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "",ftype="",fclass = "";
        private Ctrl.Claim.REG rEG;
        private Ctrl.Claim.AMD aMD;
        private Ctrl.Claim.riREG riREG;
        private Ctrl.Claim.riAMD riAMD;
        private Ctrl.Claim.EciREG eciREG;
        private Ctrl.Claim.EciAMD eciAMD;
        private Ctrl.Claim.riEciAMD riEciAMD;
        private Ctrl.Claim.riEciREG riEciREG;

        public void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            if (ftype == "") { ftype = "1"; }
            if (order == "" && query == "")
            {
                a = new SqlDataAdapter("select fid as Code#,fdesc as Desc# from mnatlos where ftype='" + ftype + "' and fclass ='"+ fclass +"' order by fid", c);
            }
            else if (order != "")
            {
                a = new SqlDataAdapter("select fid as Code#,fdesc as Desc# from mnatlos where ftype='" + ftype + "' and fclass ='" + fclass + "' order by '" + order + "'", c);
            }
            else if (query != "")
            {
                a = new SqlDataAdapter("select fid as Code#,fdesc as Desc# from mnatlos where ftype='" + ftype + "' and fclass ='" + fclass + "' AND fdesc = '" + query + "'", c);
            }
            else
            {
                a = new SqlDataAdapter("select fid as Code#,fdesc as Desc# from mnatlos where ftype='" + ftype + "' and fclass ='" + fclass + "' AND fdesc = '" + query + "' order by fid", c);
            }
            DataTable t = new DataTable();
            a.Fill(t);
            dataGridView1.DataSource = t;
            c.Close();
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }
                if (dataGridView1.Rows[row].Cells["Code#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Code#"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["Code#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["Desc#"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            if (flag == "REG")
            {
                rEG.NatlosValue = Fct.stFat(textBox1.Text.ToString());
                rEG.NatlosDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "AMD")
            {
                aMD.NatlosValue = Fct.stFat(textBox1.Text.ToString());
                aMD.NatlosDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riREG")
            {
                riREG.NatlosValue = Fct.stFat(textBox1.Text.ToString());
                riREG.NatlosDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riAMD")
            {
                riAMD.NatlosValue = Fct.stFat(textBox1.Text.ToString());
                riAMD.NatlosDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EciREG")
            {
               eciREG.NatlosValue = Fct.stFat(textBox1.Text.ToString());
               eciREG.NatlosDesc = Fct.stFat(label3.Text.ToString());
               this.Close();
            }
            if (flag == "riEciREG")
            {
                riEciREG.NatlosValue = Fct.stFat(textBox1.Text.ToString());
                riEciREG.NatlosDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EciAMD")
            {
                eciAMD.NatlosValue = Fct.stFat(textBox1.Text.ToString());
                eciAMD.NatlosDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "riEciAMD")
            {
                riEciAMD.NatlosValue = Fct.stFat(textBox1.Text.ToString());
                riEciAMD.NatlosDesc = Fct.stFat(label3.Text.ToString());
                this.Close();
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        

    }
}
