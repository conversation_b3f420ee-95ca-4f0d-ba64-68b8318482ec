using INS.INSClass;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
//FZ2YHY48
namespace INS.Business.SearchForm
{
    public partial class bdwn_pyc : Form
    {

        public string fctlid_p, page = "", flag = "";
        public bool cEditMode = false;
        public string db = InsEnvironment.DataBase.GetDbm();
        public DataTable ocpbk_c = new DataTable();
        private Ctrl.Claim.EciADJ eciADJ;
        private Ctrl.Claim.EciPAY eciPAY;
        public bdwn_pyc()
        {
            InitializeComponent();
        }

        public bdwn_pyc(Ctrl.Claim.EciPAY eciPAY)
        {
            // TODO: Complete member initialization
            this.eciPAY = eciPAY;
            InitializeComponent();
            foreach (Control control in this.Controls)              //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件             //添加事件
            }
        }

        void init()
        { XcmdConfirm.Visible = cEditMode; }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            TextBox focusTextBox = null;

            if (e.KeyCode == Keys.Enter)
            {                    //判断用户是否按下回车键
                if (sender is TextBox)
                {
                    focusTextBox = (TextBox)sender;
                    if (!focusTextBox.AcceptsReturn)
                        SendKeys.Send("{TAB}");
                }
                else SendKeys.Send("{TAB}");
            }
        }

        public void tpReload(string fctlid_a)
        {
            init();
            string sql = "select * from ocpbk_c a left join (select fclmno, flogseq, finvno,fctlid from ocpay) b on a.fctlid_p = b.fctlid where fctlid_p = '" + fctlid_a + "'";
            DataTable dt = DBHelper.GetDataSet(sql);
            if (dt.Rows.Count > 0)
            {
                fclmno.Text = dt.Rows[0]["fclmno"].ToString();
                flogseq.Text = dt.Rows[0]["flogseq"].ToString();
                finvno.Text = dt.Rows[0]["finvno"].ToString();
                fos08.Text = dt.Rows[0]["fos08"].ToString();
                fpay08.Text = dt.Rows[0]["fpay08"].ToString();
                frvos08.Text = dt.Rows[0]["frvos08"].ToString();
                fosA2.Text = dt.Rows[0]["fosA2"].ToString();
                fpayA2.Text = dt.Rows[0]["fpayA2"].ToString();
                frvosA2.Text = dt.Rows[0]["frvosA2"].ToString();
                fos09.Text = dt.Rows[0]["fos09"].ToString();
                fpay09.Text = dt.Rows[0]["fpay09"].ToString();
                frvos09.Text = dt.Rows[0]["frvos09"].ToString();
                fos11.Text = dt.Rows[0]["fos11"].ToString();
                fpay11.Text = dt.Rows[0]["fpay11"].ToString();
                frvos11.Text = dt.Rows[0]["frvos11"].ToString();
                fos12.Text = dt.Rows[0]["fos12"].ToString();
                fpay12.Text = dt.Rows[0]["fpay12"].ToString();
                frvos12.Text = dt.Rows[0]["frvos12"].ToString();
                fos13.Text = dt.Rows[0]["fos13"].ToString();
                fpay13.Text = dt.Rows[0]["fpay13"].ToString();
                frvos13.Text = dt.Rows[0]["frvos13"].ToString();
                fosA.Text = dt.Rows[0]["fosA"].ToString();
                fpayA.Text = dt.Rows[0]["fpayA"].ToString();
                frvosA.Text = dt.Rows[0]["frvosA"].ToString();
                fosB.Text = dt.Rows[0]["fosB"].ToString();
                fpayB.Text = dt.Rows[0]["fpayB"].ToString();
                frvosB.Text = dt.Rows[0]["frvosB"].ToString();
                fosC.Text = dt.Rows[0]["fosC"].ToString();
                fpayC.Text = dt.Rows[0]["fpayC"].ToString();
                frvosC.Text = dt.Rows[0]["frvosC"].ToString();
                fos.Text = dt.Rows[0]["fos"].ToString();
                fpay.Text = dt.Rows[0]["fpay"].ToString();
                frvos.Text = dt.Rows[0]["frvos"].ToString();
            }
            XcmdExit.Visible = true;
            fpayA.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fpayA.ReadOnly = cEditMode;
            fpay08.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fpay08.ReadOnly = cEditMode;
            fpay09.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fpay09.ReadOnly = cEditMode;
            fpay11.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fpay11.ReadOnly = cEditMode;
            fpay12.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fpay12.ReadOnly = cEditMode;
            fpay13.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fpay13.ReadOnly = cEditMode;
        }

        void ButtonAdd()
        {
            XcmdExit.Visible = false;
            fpayA.BackColor = System.Drawing.Color.White;
            fpayA.ReadOnly = !cEditMode;
            fpay08.BackColor = System.Drawing.Color.White;
            fpay08.ReadOnly = !cEditMode;
            fpay09.BackColor = System.Drawing.Color.White;
            fpay09.ReadOnly = !cEditMode;
            fpay11.BackColor = System.Drawing.Color.White;
            fpay11.ReadOnly = !cEditMode;
            fpay12.BackColor = System.Drawing.Color.White;
            fpay12.ReadOnly = !cEditMode;
            fpay13.BackColor = System.Drawing.Color.White;
            fpay13.ReadOnly = !cEditMode;
        }

        public void tpAdd(string str, string flg)
        {
            XcmdConfirm.Visible = true;
            if (ocpbk_c.Rows.Count == 0)
            {
                if (fctlid_p.Length == 10)
                {
                    string sql1 = "select * from ocpbk_c where fctlid_p ='" + fctlid_p + "'";
                    ocpbk_c = DBHelper.GetDataSet(sql1);
                }
                else
                {
                    //string lfctlid_p = (int.Parse(fctlid_p) - 1).ToString().PadLeft(10, '0');
                    //string sql1 = "select * from ocpbk_c where fctlid_p ='" + lfctlid_p + "'";
                    //ocpbk_c = DBHelper.GetDataSet(sql1);
                    //if (ocpbk_c.Rows.Count == 0)
                    //{
                        string sql = "select '" + fctlid_p + "' as fctlid_p,'' as flogseq, fos01-ftpay01 as fos01, fos01-ftpay01 as frvos01, 0.0000 as fpay01, " +
                                    "fos02-ftpay02 as fos02, fos02-ftpay02 as frvos02, 0.0000 as fpay02, " +
                                    "fos03-ftpay03 as fos03, fos03-ftpay03 as frvos03,0.0000 as fpay03, " +
                                    "fos04-ftpay04 as fos04, fos04-ftpay04 as frvos04, 0.0000 as fpay04, " +
                                    "fos05-ftpay05 as fos05, fos05-ftpay05 as frvos05, 0.0000 as fpay05, " +
                                    "fos06-ftpay06 as fos06, fos06-ftpay06 as frvos06, 0.0000 as fpay06, " +
                                    "fos07-ftpay07 as fos07, fos07-ftpay07 as frvos07, 0.0000 as fpay07, " +
                                    "fos08-ftpay08 as fos08, fos08-ftpay08 as frvos08, 0.0000 as fpay08, " +
                                    "fos09-ftpay09 as fos09, fos09-ftpay09 as frvos09, 0.0000 as fpay09, " +
                                    "fos10-ftpay10 as fos10, fos10-ftpay10 as frvos10, 0.0000 as fpay10, " +
                                    "fos11-ftpay11 as fos11, fos11-ftpay11 as frvos11, 0.0000 as fpay11, " +
                                    "fos12-ftpay12 as fos12, fos12-ftpay12 as frvos12,0.0000 as fpay12, " +
                                    "fos13-ftpay13 as fos13, fos13-ftpay13 as frvos13, 0.0000 as fpay13, " +
                                    "fosA-ftpayA as fosA, fosA-ftpayA as frvosA, 0.0000 as fpayA, " +
                                    "fosA2-ftpayA2 as fosA2, fosA2-ftpayA2 as frvosA2, 0.0000 as fpayA2, " +
                                    "fosB-ftpayB as fosB, fosB-ftpayB as frvosB, 0.0000 as fpayB, " +
                                    "fosC-ftpayC as fosC, fosC-ftpayC as frvosC, 0.0000 as fpayC, " +
                                    "fos-ftpay as fos, fos-ftpay as frvos, 0.0000 as fpay from ocabk_oc where fctlid_c = (select fctlid from oclaim where fclmno = '" + str + "')";
                        ocpbk_c = DBHelper.GetDataSet(sql);
                }
            }
             DataTable dt = new DataTable();
             DataTable dt1 = new DataTable();
             if (ocpbk_c.Select("fctlid_p ='" + fctlid_p + "'").Length > 0)
             {
                 dt = ocpbk_c.Select("fctlid_p ='" + fctlid_p + "'").CopyToDataTable();
             }
             else {
                 string lfctlid_p = (int.Parse(fctlid_p) - 1).ToString().PadLeft(10, '0');
                 if (ocpbk_c.Select("fctlid_p ='" + lfctlid_p + "'").Length > 0)
                 {
                     dt1 = ocpbk_c.Select("fctlid_p ='" + lfctlid_p + "'").CopyToDataTable();
                 }
                 else
                 {
                     lfctlid_p = (int.Parse(fctlid_p) - 1).ToString();
                     if (ocpbk_c.Select("fctlid_p ='" + lfctlid_p + "'").Length > 0)
                     {
                         dt1 = ocpbk_c.Select("fctlid_p ='" + lfctlid_p + "'").CopyToDataTable();
                     }
                 }
             }
             if (dt1.Rows.Count > 0)
             {
                 fclmno.Text = str;
                 flogseq.Text = flg;
                 fos08.Text = dt1.Rows[0]["frvos08"].ToString();
                 frvos08.Text = dt1.Rows[0]["frvos08"].ToString();
                 fpay08.Text = "";
                 fos09.Text = dt1.Rows[0]["frvos09"].ToString();
                 frvos09.Text = dt1.Rows[0]["frvos09"].ToString();
                 fpay09.Text = "";
                 fos11.Text = dt1.Rows[0]["frvos11"].ToString();
                 frvos11.Text = dt1.Rows[0]["frvos11"].ToString();
                 fpay11.Text = "";
                 fos12.Text = dt1.Rows[0]["frvos12"].ToString();
                 frvos12.Text = dt1.Rows[0]["frvos12"].ToString();
                 fpay12.Text = "";
                 fos13.Text = dt1.Rows[0]["frvos13"].ToString();
                 frvos13.Text = dt1.Rows[0]["frvos13"].ToString();
                 fpay13.Text = "";
                 fosA.Text = dt1.Rows[0]["frvosA"].ToString();
                 frvosA.Text = dt1.Rows[0]["frvosA"].ToString();
                 fpayA.Text = "";
                 fosA2.Text = dt1.Rows[0]["frvosA2"].ToString();
                 frvosA2.Text = dt1.Rows[0]["frvosA2"].ToString();
                 fpayA2.Text = "";
                 fosB.Text = dt1.Rows[0]["frvosB"].ToString();
                 frvosB.Text = dt1.Rows[0]["frvosB"].ToString();
                 fpayB.Text = "";
                 fosC.Text = dt1.Rows[0]["frvosC"].ToString();
                 frvosC.Text = dt1.Rows[0]["frvosC"].ToString();
                 fpayC.Text = "";
                 fos.Text = dt1.Rows[0]["frvos"].ToString();
                 frvos.Text = dt1.Rows[0]["frvos"].ToString();
                 fpay.Text = "";
             }
            if (dt.Rows.Count > 0)
            {
                fclmno.Text = str;
                flogseq.Text = flg;
                fos08.Text = dt.Rows[0]["fos08"].ToString();
                frvos08.Text = dt.Rows[0]["frvos08"].ToString();
                fpay08.Text = dt.Rows[0]["fpay08"].ToString();
                fos09.Text = dt.Rows[0]["fos09"].ToString();
                frvos09.Text = dt.Rows[0]["frvos09"].ToString();
                fpay09.Text = dt.Rows[0]["fpay09"].ToString();
                fos11.Text = dt.Rows[0]["fos11"].ToString();
                frvos11.Text = dt.Rows[0]["frvos11"].ToString();
                fpay11.Text = dt.Rows[0]["fpay11"].ToString();
                fos12.Text = dt.Rows[0]["fos12"].ToString();
                frvos12.Text = dt.Rows[0]["frvos12"].ToString();
                fpay12.Text = dt.Rows[0]["fpay12"].ToString();
                fos13.Text = dt.Rows[0]["fos13"].ToString();
                frvos13.Text = dt.Rows[0]["frvos13"].ToString();
                fpay13.Text = dt.Rows[0]["fpay13"].ToString();
                fosA.Text = dt.Rows[0]["fosA"].ToString();
                frvosA.Text = dt.Rows[0]["frvosA"].ToString();
                fpayA.Text = dt.Rows[0]["fpayA"].ToString();
                fosA2.Text = dt.Rows[0]["fosA2"].ToString();
                frvosA2.Text = dt.Rows[0]["frvosA2"].ToString();
                fpayA2.Text = dt.Rows[0]["fpayA2"].ToString();
                fosB.Text = dt.Rows[0]["fosB"].ToString();
                frvosB.Text = dt.Rows[0]["frvosB"].ToString();
                fpayB.Text = dt.Rows[0]["fpayB"].ToString();
                fosC.Text = dt.Rows[0]["fosC"].ToString();
                frvosC.Text = dt.Rows[0]["frvosC"].ToString();
                fpayC.Text = dt.Rows[0]["fpayC"].ToString();
                fos.Text = dt.Rows[0]["fos"].ToString();
                frvos.Text = dt.Rows[0]["frvos"].ToString();
                fpay.Text = dt.Rows[0]["fpay"].ToString();
            }
            ButtonAdd();
        }

        void u_setpage(string p_name)
        {
            if (p_name == "THE REST")
            {
                fpayA.Enabled = cEditMode;
                fpay08.Enabled = cEditMode;
                fpay09.Enabled = cEditMode;
                fpay11.Enabled = cEditMode;
                fpay12.Enabled = cEditMode;
                fpay13.Enabled = cEditMode;
            }
        }

        void u_calvalue(string p_varname)
        {
            string lc_index = ""; decimal ln_opay = 0;
            if (p_varname.Substring(4,1) == "A" || p_varname.Substring(4,2) == "08" || p_varname.Substring(4,2) == "09" || p_varname.Substring(4,2) == "11" || p_varname.Substring(4,2) == "12" || p_varname.Substring(4,2) == "13")
            {
                if (p_varname.Substring(4, 1) == "A") { lc_index = p_varname.Substring(4, 1); }
                else { lc_index = p_varname.Substring(4, 2); }
            }
            if (p_varname.Substring(0, 4) == "FPAY" && p_varname.Length == 5 && lc_index == "A")
            {
                TextBox fp = (TextBox)Controls.Find(string.Format("{0}", "fpay" + lc_index), true).FirstOrDefault();
                TextBox fr = (TextBox)Controls.Find(string.Format("{0}", "frvos" + lc_index), true).FirstOrDefault();
                TextBox fo = (TextBox)Controls.Find(string.Format("{0}", "fos" + lc_index), true).FirstOrDefault();
                ln_opay = Fct.sdFat(Fct.sdFat(fo.Text) - Fct.sdFat(fr.Text));

                //TextBox ftp = (TextBox)Controls.Find(string.Format("{0}", "ftpay" + lc_index), true).FirstOrDefault();
                //ftp.Text = Fct.stFat(Fct.sdFat(ftp.Text) - ln_opay + Fct.sdFat(fp.Text));
                //ftpay.Text = Fct.stFat(Fct.sdFat(ftpay.Text) - ln_opay + Fct.sdFat(fp.Text));

                fr.Text = Fct.stFat(Fct.sdFat(fo.Text) - Fct.sdFat(fp.Text));
                fpay.Text = Fct.stFat(Fct.sdFat(fpay.Text) - ln_opay + Fct.sdFat(fp.Text));
                frvos.Text = Fct.stFat(Fct.sdFat(fos.Text) - Fct.sdFat(fpay.Text));
            }
            if (p_varname.Substring(0, 4) == "FPAY" && p_varname.Length == 6 && lc_index != "")
            {
                TextBox fp = (TextBox)Controls.Find(string.Format("{0}", "fpay" + lc_index), true).FirstOrDefault();
                TextBox fr = (TextBox)Controls.Find(string.Format("{0}", "frvos" + lc_index), true).FirstOrDefault();
                TextBox fo = (TextBox)Controls.Find(string.Format("{0}", "fos" + lc_index), true).FirstOrDefault();
                ln_opay = Fct.sdFat(Fct.sdFat(fo.Text) - Fct.sdFat(fr.Text));

                //TextBox ftp = (TextBox)Controls.Find(string.Format("{0}", "ftpay" + lc_index), false).FirstOrDefault();
                //ftp.Text = Fct.stFat(Fct.sdFat(ftp.Text) - ln_opay + Fct.sdFat(fp.Text));
                //ftpay.Text = Fct.stFat(Fct.sdFat(ftpay.Text) - ln_opay + Fct.sdFat(fp.Text));
                //ftpayA2.Text = ftpay08.Text;
                //ftpayB.Text = ftpay09.Text;
                //ftpayC.Text = Fct.stFat(Fct.sdFat(ftpay11.Text) + Fct.sdFat(ftpay12.Text) + Fct.sdFat(ftpay13.Text));

                fr.Text = Fct.stFat(Fct.sdFat(fo.Text) - Fct.sdFat(fp.Text));
                fpay.Text = Fct.stFat(Fct.sdFat(fpay.Text) - ln_opay + Fct.sdFat(fp.Text));
                frvos.Text = Fct.stFat(Fct.sdFat(fos.Text) - Fct.sdFat(fpay.Text));
                fpayA2.Text = fpay08.Text;
                fpayB.Text = fpay09.Text;
                fpayC.Text = Fct.stFat(Fct.sdFat(fpay11.Text) + Fct.sdFat(fpay12.Text) + Fct.sdFat(fpay13.Text));
                frvosA2.Text = Fct.stFat(Fct.sdFat(fosA2.Text) - Fct.sdFat(fpayA2.Text));
                frvosB.Text = Fct.stFat(Fct.sdFat(fosB.Text) - Fct.sdFat(fpayB.Text));
                frvosC.Text = Fct.stFat(Fct.sdFat(fosC.Text) - Fct.sdFat(fpayC.Text));
            }
        }

        private void XcmdConfirm_Click(object sender, EventArgs e)
        {
            if (ocpbk_c.Rows.Count == 0)
            {
                DataRow newRow = ocpbk_c.NewRow();
                ocpbk_c.Rows.Add(newRow);
            }
            else {
                DataRow[] Rows = ocpbk_c.Select("fctlid_p ='" + fctlid_p + "'");
                if (Rows == null || Rows.Length == 0)
                {
                    DataRow newRow = ocpbk_c.NewRow();
                    newRow["fctlid_p"] = fctlid_p;
                    ocpbk_c.Rows.Add(newRow);
                }
            }
            DataRow[] layrRow;
            layrRow = ocpbk_c.Select("fctlid_p ='" + fctlid_p + "'");
            foreach (DataRow dr in layrRow){
                dr["flogseq"] = Fct.sdFat(flogseq.Text);
                dr["fpay08"] = Fct.sdFat(fpay08.Text);
                dr["frvos08"] = Fct.sdFat(frvos08.Text);
                dr["fos08"] = Fct.sdFat(fos08.Text);
                dr["fpay09"] = Fct.sdFat(fpay09.Text);
                dr["frvos09"] = Fct.sdFat(frvos09.Text);
                dr["fos09"] = Fct.sdFat(fos09.Text);
                dr["fpay11"] = Fct.sdFat(fpay11.Text);
                dr["frvos11"] = Fct.sdFat(frvos11.Text);
                dr["fos11"] = Fct.sdFat(fos11.Text);
                dr["fpay12"] = Fct.sdFat(fpay12.Text);
                dr["frvos12"] = Fct.sdFat(frvos12.Text);
                dr["fos12"] = Fct.sdFat(fos12.Text);
                dr["fpay13"] = Fct.sdFat(fpay13.Text);
                dr["frvos13"] = Fct.sdFat(frvos13.Text);
                dr["fos13"] = Fct.sdFat(fos13.Text);
                dr["fpayA"] = Fct.sdFat(fpayA.Text);
                dr["frvosA"] = Fct.sdFat(frvosA.Text);
                dr["fosA"] = Fct.sdFat(fosA.Text);
                dr["fpayA2"] = Fct.sdFat(fpayA2.Text);
                dr["frvosA2"] = Fct.sdFat(frvosA2.Text);
                dr["fosA2"] = Fct.sdFat(fosA2.Text);
                dr["fpayB"] = Fct.sdFat(fpayB.Text);
                dr["frvosB"] = Fct.sdFat(frvosB.Text);
                dr["fosB"] = Fct.sdFat(fosB.Text);
                dr["fpayC"] = Fct.sdFat(fpayC.Text);
                dr["frvosC"] = Fct.sdFat(frvosC.Text);
                dr["fosC"] = Fct.sdFat(fosC.Text);
                dr["fpay"] = Fct.sdFat(fpay.Text);
                dr["frvos"] = Fct.sdFat(frvos.Text);
                dr["fos"] = Fct.sdFat(fos.Text);
            }
            if (flag == "EciPAY")
            {
                eciPAY.fclawamtValue = fpay.Text.ToString();
                eciPAY.DTocpbk_c = ocpbk_c;
                eciPAY.u_cpay();
                this.Close();
            }
        }

        private void XcmdExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void fpayA_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(fpayA.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                ////fpayA.Focus();
            }
            else {  }
            u_calvalue("FPAYA");
        }

        private void fpay08_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(fpay08.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //fpay08.Focus();
            }
            else {  }
            u_calvalue("FPAY08");
        }

        private void fpay09_Validated(object sender, EventArgs e)
        {
            u_calvalue("FPAY09"); 
        }

        private void fpay11_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(fpay11.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //fpay11.Focus();
            }
            else { }
            u_calvalue("FPAY11"); 
        }

        private void fpay12_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(fpay12.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //fpay12.Focus();
            }
            else {  }
            u_calvalue("FPAY12");
        }

        private void fpay13_Validated(object sender, EventArgs e)
        {
            if (Fct.sdFat(fpay13.Text) < 0)
            {
                //MessageBox.Show("Invalid Value");
                //fpay13.Focus();
            }
            else { }
            u_calvalue("FPAY13"); 
        }

    }
}
