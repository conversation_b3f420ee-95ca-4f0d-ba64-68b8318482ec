using INS.INSClass;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
//FZ2YHY48
namespace INS.Business.SearchForm
{
    public partial class bdwn_suc : Form
    {

        public string fctlid, page = "", flag="";
        public bool cEditMode = false;
        public string db = InsEnvironment.DataBase.GetDbm();
        private Ctrl.Claim.EciSUM eciSUM;
        public bdwn_suc()
        {
            InitializeComponent();
        }

        public bdwn_suc(Ctrl.Claim.EciSUM eciSUM)
        {
            // TODO: Complete member initialization
            this.eciSUM = eciSUM;
            InitializeComponent();
            foreach (Control control in this.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件             //添加事件
            }
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            TextBox focusTextBox = null;

            if (e.KeyCode == Keys.Enter)
            {                    //判断用户是否按下回车键
                if (sender is TextBox)
                {
                    focusTextBox = (TextBox)sender;
                    if (!focusTextBox.AcceptsReturn)
                        SendKeys.Send("{TAB}");
                }
                else SendKeys.Send("{TAB}");
            }
        }

        public void tpReload(string str)
        {
            fclmno.Text = str;
            string sql = "select * from ocabk_oc where fctlid_c =(select fctlid from oclaim where fclmno = '" + str + "')";
            DataTable dt = DBHelper.GetDataSet(sql);
            if (dt.Rows.Count > 0)
            {
                fos08.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fos08"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj08"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpay08"].ToString()));
                fpay08.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpay08"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpay08"].ToString()));
                finc08.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadj08"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj08"].ToString()));
                frvy08.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvy08"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvy08"].ToString()));
                fosA2.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fosA2"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadjA2"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpayA2"].ToString()));
                fpayA2.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpayA2"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpayA2"].ToString()));
                fincA2.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadjA2"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadjA2"].ToString()));
                frvyA2.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvyA2"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvyA2"].ToString()));
                fos09.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fos09"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj09"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpay09"].ToString()));
                fpay09.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpay09"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpay09"].ToString()));
                finc09.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadj09"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj09"].ToString()));
                frvy09.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvy09"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvy09"].ToString()));
                fos11.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fos11"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj11"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpay11"].ToString()));
                fpay11.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpay11"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpay11"].ToString()));
                finc11.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadj11"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj11"].ToString()));
                frvy11.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvy11"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvy11"].ToString()));
                fos12.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fos12"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj12"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpay12"].ToString()));
                fpay12.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpay12"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpay12"].ToString()));
                finc12.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadj12"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj12"].ToString()));
                frvy12.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvy12"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvy12"].ToString()));
                fos13.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fos13"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj13"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpay13"].ToString()));
                fpay13.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpay13"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpay13"].ToString()));
                finc13.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadj13"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj13"].ToString()));
                frvy13.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvy13"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvy13"].ToString()));
                fosA.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fosA"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadjA"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpayA"].ToString()));
                fpayA.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpayA"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpayA"].ToString()));
                fincA.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadjA"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadjA"].ToString()));
                frvyA.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvyA"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvyA"].ToString()));
                fosB.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fosB"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadjB"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpayB"].ToString()));
                fpayB.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpayB"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpayB"].ToString()));
                fincB.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadjB"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadjB"].ToString()));
                frvyB.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvyB"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvyB"].ToString()));
                fosC.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fosC"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadjC"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpayC"].ToString()));
                fpayC.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpayC"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpayC"].ToString()));
                fincC.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadjC"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadjC"].ToString()));
                frvyC.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvyC"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvyC"].ToString()));
                fos.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fos"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj"].ToString()) - Fct.sdFat(dt.Rows[0]["ftpay"].ToString()));
                fpay.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fpay"].ToString()) + Fct.sdFat(dt.Rows[0]["ftpay"].ToString()));
                finc.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["fadj"].ToString()) + Fct.sdFat(dt.Rows[0]["ftadj"].ToString()));
                frvy.Text = Fct.stFat(Fct.sdFat(dt.Rows[0]["frvy"].ToString()) + Fct.sdFat(dt.Rows[0]["ftrvy"].ToString()));
            }
        }

        private void XcmdExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

    }
}
