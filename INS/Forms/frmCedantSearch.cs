using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;

namespace INS
{
    public partial class frmCedantSearch : Form
    {
        public frmCedantSearch()
        {
            InitializeComponent();
            FillData("", "");
        }

        public frmCedantSearch(Ctrl.RIGen rIGen)
        {
            // TODO: Complete member initialization
            this.rIGen = rIGen;
            InitializeComponent();
        }

        public frmCedantSearch(Ctrl.EndorRIGen endorRIGen)
        {
            // TODO: Complete member initialization
            this.endorRIGen = endorRIGen;
            InitializeComponent();
        }

        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "";

        private Ctrl.RIGen rIGen;
        private Ctrl.EndorRIGen endorRIGen;

        public void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            if (order == "" && query == "")
            {
                a = new SqlDataAdapter("select fid as Cedant#,fdesc as Desc# from mprdr where ftype='R' order by fid", c);
            }
            else if (order != "")
            {
                a = new SqlDataAdapter("select fid as Cedant#,fdesc as Desc# from mprdr where ftype='R' order by '" + order +"'", c);
            }
            else if (query != "")
            {
                a = new SqlDataAdapter("select fid as Cedant#,fdesc as Desc# from mprdr where ftype='R' AND fdesc = '" + query + "'", c);
            }
            else
            {
                a = new SqlDataAdapter("select fid as Cedant#,fdesc as Desc# from mprdr where ftype='R' AND fdesc = '" + query + "' order by fid", c);
            }
            DataTable t = new DataTable();
            a.Fill(t);
            dataGridView1.DataSource = t;
            c.Close();
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }
                if (dataGridView1.Rows[row].Cells["Cedant#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Cedant#"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["Cedant#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["Desc#"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            if (flag == "RI")
            {
                rIGen.CliValue  =  Fct.stFat(textBox1.Text.ToString());
                rIGen.CliDesc =  Fct.stFat(label3.Text.ToString());
                this.Close();
            }
            if (flag == "EndorRI")
            {
                endorRIGen.CliValue =  Fct.stFat(textBox1.Text.ToString());
                endorRIGen.CliDesc =  Fct.stFat(label3.Text.ToString());
                this.Close();
            }
        }


        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        

    }
}
