using INS.INSClass;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
//FZ2YHY48
namespace INS.Business.SearchForm
{
    public partial class renewopt : Form
    {

        public string fctlid = "", page = "", flag = "";
        public string db = InsEnvironment.DataBase.GetDbm();
        private DirectPolicy directPolicy;

        public renewopt()
        {
            InitializeComponent();
        }

        public renewopt(DirectPolicy directPolicy)
        {
            // TODO: Complete member initialization
            this.directPolicy = directPolicy;
            InitializeComponent();
        }

     
        private void button2_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            if (radioButton1.Checked) {
                expnote tempform = new expnote(this);
                tempform.fctlid_p = fctlid;
                directPolicy.reopt = "1";
                tempform.FillData();
                tempform.ShowDialog();
            }
            if (radioButton2.Checked) {
                directPolicy.reopt = "2";
                //tempform.tabpage1reload();
                //tempform.buttoncontrolrenew("renew");
                //tempform.ShowDialog();
                this.Close();
            }  
        }

    }
}
