using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;
using INS.Ctrl;

namespace INS
{
    public partial class EndtExcessSearch : Form
    {
        public EndtExcessSearch()
        {
            InitializeComponent();
            FillData("", "");
        }

        public EndtExcessSearch(EndorExcess endorExcess)
        {
            // TODO: Complete member initialization
            this.endorExcess = endorExcess;
            InitializeComponent();
        }



        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "";
        public string section = "";
        public string fctlid_p = "";
        private String _fclass;
        private EndorExcess endorExcess;
        public string Dbo = InsEnvironment.DataBase.GetDbo();
        public String Fclass
        {
            set
            {
                _fclass = value;
            }
        }

        public void FillData(string order, string query)
        {
            SqlConnection c = DBConnect.dbConn;
            if (c.State == ConnectionState.Closed)
            { c.Open(); }
            SqlDataAdapter a;
            if (section == "1")
            {
                a = new SqlDataAdapter("select b.fid as Clause#,c.fdesc as Desc#,b.flogseq as Seq#, b.fsid as Item#, b.fcontent " +
                                        "from " + Dbo + "polh a  " +
                                        "left join " + Dbo + "oinsex b on a.fpolno = b.fpolno  " +
                                        "left join (select b.fid, b.fdesc from  minsc a  " +
                                        "left join misec b on a.fctlid=b.fctlid_a " +
                                        "left join (select b.fid,b.fctlid from minsc a  " +
                                        "left join misec b on a.fctlid=b.fctlid_a   " +
                                        "where ftype =1 and fdoctype =2 ) c on c.fctlid = b.fctlid_1  " +
                                        "where fdoctype =2 and b.ftype =2 and a.fid ='" + _fclass + "' and c.fid ='SEC1') c on c.fid = b.fid " +
                                        "where a.fctlid ='" + fctlid_p + "' and b.ftype ='SEC1'", c);
            }
            else
            {

                a = new SqlDataAdapter("select b.fid as Clause#,c.fdesc as Desc#,b.flogseq as Seq#, b.fsid as Item#, b.fcontent " +
                                         "from " + Dbo + "polh a  " +
                                         "left join " + Dbo + "oinsex b on a.fpolno = b.fpolno  " +
                                         "left join (select b.fid, b.fdesc from  minsc a  " +
                                         "left join misec b on a.fctlid=b.fctlid_a " +
                                         "left join (select b.fid,b.fctlid from minsc a  " +
                                         "left join misec b on a.fctlid=b.fctlid_a   " +
                                         "where ftype =1 and fdoctype =2 ) c on c.fctlid = b.fctlid_1  " +
                                         "where fdoctype =2 and b.ftype =2 and a.fid ='" + _fclass + "' and c.fid ='SEC2') c on c.fid = b.fid " +
                                         "where a.fctlid ='" + fctlid_p + "' and b.ftype ='SEC2'", c);
            }
            DataTable t = new DataTable();
            a.Fill(t);
            dataGridView1.DataSource = t;
            this.dataGridView1.Columns[2].Visible = false;
            this.dataGridView1.Columns[3].Visible = false;
            this.dataGridView1.Columns[4].Visible = false;
            c.Close();
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }
                if (dataGridView1.Rows[row].Cells["Clause#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Clause#"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["Clause#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["Desc#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Seq#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Seq#"].Value.ToString().Length != 0)
                    {
                        label4.Text = dataGridView1.Rows[row].Cells["Seq#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Item#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Item#"].Value.ToString().Length != 0)
                    {
                        label5.Text = dataGridView1.Rows[row].Cells["Item#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["fcontent"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["fcontent"].Value.ToString().Length != 0)
                    {
                        label6.Text = dataGridView1.Rows[row].Cells["fcontent"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            if (flag == "Endor")
            {
                if (section == "1")
                {
                    if (!endorExcess.CheckDuplicate(textBox1.Text.ToString(), "1", "Click"))
                    {
                        MessageBox.Show("Duplicate Value!");
                    }
                    else
                    {
                        endorExcess.addexcess_Click();
                        endorExcess.ExcessValue = Fct.stFat(textBox1.Text.ToString());
                        endorExcess.ExcessDesc = Fct.stFat(label3.Text.ToString());
                        endorExcess.ExcessSeq = Fct.stFat(label4.Text.ToString());
                        endorExcess.ExcessItem = Fct.stFat(label5.Text.ToString());
                        endorExcess.ExcessContent = Fct.stFat(label6.Text.ToString());
                        this.Close();
                    }

                }
                else
                {
                    if (!endorExcess.CheckDuplicate(textBox1.Text.ToString(), "2", "Click"))
                    {
                        MessageBox.Show("Duplicate Value!");
                    }
                    else
                    {
                        endorExcess.addexcess2_Click();
                        endorExcess.ExcessValue2 = Fct.stFat(textBox1.Text.ToString());
                        endorExcess.ExcessDesc2 = Fct.stFat(label3.Text.ToString());
                        endorExcess.ExcessSeq2 = Fct.stFat(label4.Text.ToString());
                        endorExcess.ExcessItem2 = Fct.stFat(label5.Text.ToString());
                        endorExcess.ExcessContent2 = Fct.stFat(label6.Text.ToString());
                        this.Close();
                    }
                }

            }
        }


        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }


    }
}
