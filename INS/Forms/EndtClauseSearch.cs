using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using INS.Business;
using INS.Ctrl;

namespace INS
{
    public partial class EndtClauseSearch : Form
    {
        public EndtClauseSearch()
        {
            InitializeComponent();
            FillData("", "");
        }

        public EndtClauseSearch(EndorAttach endorAttach)
        {
            // TODO: Complete member initialization
            this.endorAttach = endorAttach;
            InitializeComponent();
        }

        DES des = new DES();
        DBConnect operate = new DBConnect();
        public string flag = "";
        public int attachlistrow =0;
        public string fctlid_p = "";
        private String _fclass;
        private EndorAttach endorAttach;

        public String Fclass
        {
            set
            {
                _fclass = value;
            }
        }

        public void FillData(string order, string query)
        {
            string attsql = "select b.fid as Detail#, b.fdesc as Desc#, b.fendtno as [Endt No.], b.fitem, b.flogseq as Seq# from polh a " +
                     "left join oinsatt b on a.fpolno = b.fpolno "+
                     "where a.fctlid ='" + fctlid_p + "' order by flogseq";
            dataGridView1.DataSource = DBHelper.GetDataSet(attsql);
            this.dataGridView1.Columns[4].Visible = false;
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                int row = 0;
                if (dataGridView1.CurrentCell != null)
                {
                    row = dataGridView1.CurrentCell.RowIndex;
                }
                else { return; }
                if (dataGridView1.Rows[row].Cells["Detail#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Detail#"].Value.ToString().Length != 0)
                    {
                        textBox1.Text = dataGridView1.Rows[row].Cells["Detail#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Desc#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Desc#"].Value.ToString().Length != 0)
                    {
                        label3.Text = dataGridView1.Rows[row].Cells["Desc#"].Value.ToString();
                    }
                }
                if (dataGridView1.Rows[row].Cells["Seq#"].Value != null)
                {
                    if (dataGridView1.Rows[row].Cells["Seq#"].Value.ToString().Length != 0)
                    {
                        label4.Text = dataGridView1.Rows[row].Cells["Seq#"].Value.ToString();
                    }
                }
            }
            catch
            {
                MessageBox.Show("Have Error", "Warning",
                   MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

        }

        private void ComboBox1_SelectedIndexChanged(object sender, System.EventArgs e)
        {
            FillData(comboBox1.SelectedItem.ToString().Trim(), "");
        }

        private void Query_Click(object sender, EventArgs e)
        {
            if (flag == "Endor")
            {
                //string result2 = endorAttach.CheckDuplicate(textBox1.Text.ToString().Trim(), "Click");
                //if (result2 != "")
                //{
                //    MessageBox.Show(result2);
                //}
                //else
                //{
                    endorAttach.addatt_Click();
                    endorAttach.ClauseValue = textBox1.Text.ToString().Trim();
                    endorAttach.ClauseDesc = label3.Text.ToString().Trim();
                    endorAttach.ClauseSeq = label4.Text.ToString().Trim();
                    this.Close();
                //}
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            this.Close();
        }


    }
}
