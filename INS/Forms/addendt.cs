using INS.Ctrl;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business.SearchForm
{
    public partial class addendt : Form
    {
        private Endorsement endorsement;
        public string Class, flag, fbus;
        public string SubClass;
        public string Policy = "";
        private RIEndt rIEndt;

        public addendt()
        {
            InitializeComponent();
            Control();
        }

        public void Control()
        {
            if (Class == "CAR" || (Class == "EEC" && SubClass == "CONTRACTOR") || Class =="CLL")
            {
                panel1.Visible = true;
            }
        }

        public addendt(Endorsement endorsement)
        {
            // TODO: Complete member initialization
            this.endorsement = endorsement;
            InitializeComponent();
            Control();
        }

        public addendt(RIEndt rIEndt)
        {
            // TODO: Complete member initialization
            this.rIEndt = rIEndt;
            InitializeComponent();
            Control();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (flag == "Endor")
            {
                endorsement.fendtype = "";
                endorsement.fexten = "";
                this.Close();
            }
            if (flag == "EndorRI")
            {
                rIEndt.fendtype = "";
                rIEndt.fexten = "";
                this.Close();
            } 
        }

        private void button1_Click(object sender, EventArgs e)
        {
            if (flag == "Endor")
            {
                if (Class == "CAR" || (Class == "EEC" && SubClass == "CONTRACTOR") || Class == "CLL")
                {
                    foreach (Control ctrl in panel1.Controls)
                    {
                        if (ctrl is RadioButton)
                        {
                            if (radioButton1.Checked)
                            {
                                endorsement.fendtype = "1";
                            }
                            if (radioButton2.Checked)
                            {
                                endorsement.fendtype = "2";
                            }
                            if (radioButton3.Checked)
                            {
                                endorsement.fendtype = "3";
                            }
                        }
                    }
                }
                else
                {
                    endorsement.fendtype = "1";
                }
                foreach (Control ctrl in panel2.Controls)
                {
                    if (ctrl is RadioButton)
                    {
                        if (radioButton6.Checked)
                        {
                            endorsement.fexten = "1";
                        }
                        if (radioButton4.Checked)
                        {
                            endorsement.fexten = "2";
                        }
                        if (radioButton5.Checked)
                        {
                            endorsement.fexten = "3";
                        }
                    }
                }
                if (endorsement.fendtype != "" && endorsement.fexten != "")
                { this.Close(); }
            }
            if (flag == "EndorRI")
            {
                if (Class == "CAR" || (Class == "EEC") || Class == "CLL")
                {
                    foreach (Control ctrl in panel1.Controls)
                    {
                        if (ctrl is RadioButton)
                        {
                            if (radioButton1.Checked)
                            {
                                rIEndt.fendtype = "1";
                            }
                            if (radioButton2.Checked)
                            {
                                rIEndt.fendtype = "2";
                            }
                            if (radioButton3.Checked)
                            {
                                rIEndt.fendtype = "3";
                            }
                        }
                    }
                }
                else
                {
                    rIEndt.fendtype = "1";
                }
                foreach (Control ctrl in panel2.Controls)
                {
                    if (ctrl is RadioButton)
                    {
                        if (radioButton6.Checked)
                        {
                            rIEndt.fexten = "1";
                        }
                        if (radioButton4.Checked)
                        {
                            rIEndt.fexten = "2";
                        }
                        if (radioButton5.Checked)
                        {
                            rIEndt.fexten = "3";
                        }
                    }
                }
                if (rIEndt.fendtype != "" && rIEndt.fexten != "")
                { this.Close(); }
            }
           
        }

        private void radioButton3_CheckedChanged(object sender, EventArgs e)
        {
            radioButton4.Enabled = false;
            radioButton5.Enabled = false;
            radioButton6.Checked = true;
        }

        private void radioButton2_CheckedChanged(object sender, EventArgs e)
        {
            radioButton4.Enabled = true;
            radioButton5.Enabled = true;
        }

        private void radioButton1_CheckedChanged(object sender, EventArgs e)
        {
            radioButton4.Enabled = true;
            radioButton5.Enabled = true;
        }
    }
}
