using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Windows.Forms;

using InsEnvironment;

namespace INS
{
    static class Program

    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]

        static void Main()
       {
            EnvSettings.SetMstDbname("live_ilmdata");
            EnvSettings.SetOpDbname("live_ilodata"); 

            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new frmLogin());
        }
    }
}
