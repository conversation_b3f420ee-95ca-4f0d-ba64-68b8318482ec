using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using INS.INSClass;
namespace INS
{
    public partial class frmAddUser1 : Form
    {
        DES des = new DES();
        DBConnect operate = new DBConnect();
        public frmAddUser1()
        {
            InitializeComponent();
            pswControl();
        }
        public string UserName = "";
        public string right1 = "";
        public string right2 = "";
        public string right3 = "";
        public string right4 = "";
        public string right5 = "";
        public string right6 = "";
        public string right7 = "";
        public string right8 = "";
        public string right9 = "";
        public string right10 = "";
        public string right11 = "";
        public string right12 = "";
        public string right13 = "";
        public string right14 = "";
        public string right15 = "";
        public string cyle = "";
        public int life;
        public string len = "";

        void pswControl()
        {
            string str = "select * from xsyscomp";
            DataTable dt = new DataTable();
            dt = operate.GetTable(str);
            len = dt.Rows[0]["Fpasslen"].ToString();
            life = int.Parse(dt.Rows[0]["Fpasslife"].ToString());
            cyle = dt.Rows[0]["Fpasscyle"].ToString();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            if (textBox2.Text.Trim() == "")
            {
                MessageBox.Show("User ID Can't be Empty", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else if (textBox1.Text.Trim() == "")
            {
                MessageBox.Show("User Name Can't be Empty", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else if (textBox3.Text.Trim() == "")
            {
                MessageBox.Show("User CName Can't be Empty", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else if (textBox4.Text.Trim() == "")
            {
                MessageBox.Show("User Psd Can't be Empty", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else if (textBox4.Text.Length < 6)
            {
                MessageBox.Show("User Psd Can't be less 6 char", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else if (right1 == "")
            {
                MessageBox.Show("COIL Can't be Empty", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else if (right2 == "")
            {
                MessageBox.Show("COIS Can't be Empty", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else if (right3 == "")
            {
                MessageBox.Show("Business Can't be Empty", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else if (right4 == "")
            {
                MessageBox.Show("DR/CR Can't be Empty", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else if (right5 == "")
            {
                MessageBox.Show("Outward Reinsurance Can't be Empty", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else if (right6 == "")
            {
                MessageBox.Show("Renewal Notice Can't be Empty", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else if (right7 == "")
            {
                MessageBox.Show("Claims Can't be Empty", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else if (right8 == "")
            {
                MessageBox.Show("DR/CR Claims Can't be Empty", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else if (right9 == "")
            {
                MessageBox.Show("Claims Payment Approval Can't be Empty", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else if (right10 == "")
            {
                MessageBox.Show("Claims Cover Letter Can't be Empty", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else if (right11 == "")
            {
                MessageBox.Show("A/Cs Can't be Empty", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else if (right12 == "")
            {
                MessageBox.Show("Master (Business) Can't be Empty", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else if (right13 == "")
            {
                MessageBox.Show("Master (Claims) Can't be Empty", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else if (right14 == "")
            {
                MessageBox.Show("Master (Treaty) Can't be Empty", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else if (right15 == "")
            {
                MessageBox.Show("Management report Can't be Empty", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else
            {
                
                string str =
                    "select count(*) from muser where Fname ='" + textBox1.Text.Trim() + "'";
                int i = operate.SelectData(str);
                if (i > 0)
                {
                    MessageBox.Show("Duplicated User", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                else
                {
                    DateTime time = DateTime.Now;
                    string Addsql =
                        "insert into muser (Fcode,Fname,Fcname,Fpassword,Fpasshis,Fright01,Fright02,Fright03,Fright03a,Fright03b,Fright03c,Fright04,Fright04a,Fright04b,Fright04c,Fright05,Fright06,Fright07,Fright08,Fright09,Fenable,Fvdate,Ffailure,Finpuser,Finpdate,Fupduser,Fupddate) Values ('" + textBox2.Text.ToString() + "','" + textBox1.Text.ToString() + "','" + textBox3.Text.ToString() + "','" + des.encode(textBox4.Text.ToString()) + "','" + des.encode(textBox4.Text.ToString()) + "','" + right1 + "','" + right2 + "','" + right3 + "','" + right4 + "','" + right5 + "','" + right6 + "','" + right7 + "','" + right8 + "','" + right9 + "','" + right10 + "','" + right11 + "','" + right12 + "','" + right13 + "','" + right14 + "','" + right15 + "','1', '" + DateTime.Now.AddDays(life).ToString("yyyy-MM-dd HH:mm:ss") + "',1,'" + UserName + "','"
                        + time.ToString("yyyy-MM-dd HH:mm:ss") + "','"+ UserName +"','"
                        + time.ToString("yyyy-MM-dd HH:mm:ss") + "')";
                    if (operate.OperateData(Addsql) > 0)
                    {
                        MessageBox.Show("Have Added", "Show",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
               this.Close();
            }

        }

        private void button3_Click(object sender, EventArgs e)
        {
            if (comboBox1.SelectedItem.Equals("COIL"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right1 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("COIS"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right2 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Business"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right3 = label5.Text.ToString();
            }
            
            if (comboBox1.SelectedItem.Equals("DR/CR"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right4 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Outward Reinsurance"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right5 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Renewal Notice"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right6 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Claims"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right7 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("DR/CR Claims"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right8 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Claims Payment Approval"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right9 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Claims Cover letter"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right10 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("A/Cs"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right11 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Master (Business)"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right12 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Master (Claims)"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right13 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Master (Treaty)"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right14 = label5.Text.ToString();
            }
            if (comboBox1.SelectedItem.Equals("Management Report"))
            {
                string a; string b; string c; string d; string k; string f;
                string g; string h; string i; string j;
                if (coil1.Checked) a = "1";
                else a = "0";
                if (coil2.Checked) b = "1";
                else b = "0";
                if (coil3.Checked) c = "1";
                else c = "0";
                if (coil4.Checked) d = "1";
                else d = "0";
                if (coil5.Checked) k = "1";
                else k = "0";
                if (coil6.Checked) f = "1";
                else f = "0";
                if (coil7.Checked) g = "1";
                else g = "0";
                if (coil8.Checked) h = "1";
                else h = "0";
                if (coil9.Checked) i = "1";
                else i = "0";
                if (coil10.Checked) j = "1";
                else j = "0";
                label5.Text = a + b + c + d + k + f + g + h + i + j;
                right15 = label5.Text.ToString();
            }
        }

        private void button2_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void panel1_Paint(object sender, PaintEventArgs e)
        {

        }
    }
}
