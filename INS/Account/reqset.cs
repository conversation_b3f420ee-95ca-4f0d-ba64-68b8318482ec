using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business
{
    public partial class reqset : Form
    {
        public string Class = "";
        public string flag = "";
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        private tpyset tpyset;

        public reqset()
        {
            InitializeComponent();
            InitializeStatusName();
        }

        public reqset(tpyset tpyset)
        {
            // TODO: Complete member initialization
            this.tpyset = tpyset;
            InitializeComponent();
            InitializeStatusName();
        }

        void InitializeStatusName()
        {
            BindData[] u_Statusarr = new BindData[]  {
                                 new BindData("0","ALL"),
                                 new BindData("1","Yes"),
                                 new BindData("2","No")};
            fpost.DataSource = u_Statusarr;
            fpost.ValueMember = "ID";
            fpost.DisplayMember = "Item";
        }


        private void button1_Click(object sender, EventArgs e)
        {
            string sqladd = "";
            foreach (Control ctrl in panel1.Controls)
            {
                if (ctrl is TextBox)
                {
                    if (((TextBox)ctrl).Text==""){
                        sqladd = "";
                    }
                }
                if (ctrl is MaskedTextBox)
                {
                    if (((MaskedTextBox)ctrl).Text == "")
                    {
                        sqladd = "";
                    }
                }
            }

            if (fdocno.Text != "")
            {
                sqladd = "And a.fdocno = '" + fdocno.Text.Trim() + "' ";
            }
            if (fothref.Text != "")
            {
                sqladd = "And a.fothref = '" + fothref.Text.Trim() + "' ";
            }
            if (finvdate.Text != "    .  ." && feffto1.Text != "    .  .")
            {
                sqladd = sqladd + "And a.finvdate between '" + finvdate.Text + "' and  '" + feffto1.Text + "' ";
            }
            if (fpost.SelectedValue.ToString() != "0" && fpost != null)
            {
                sqladd = sqladd + "And a.fposted = '" + fpost.SelectedValue + "' ";
            }
            if (sqladd == "") { sqladd = sqladd + "And a.fmodule = 'JV000' "; }

            if (flag == "tpyset")
            {
                tpyset.FillData("", sqladd, "");
            }
            this.Close();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void reqset_Resize(object sender, EventArgs e)
        {
            if (WindowState == FormWindowState.Maximized)
            {
                //最大化时所需的操作 
                this.ClientSize = new System.Drawing.Size(755, 300);
                this.WindowState = FormWindowState.Normal;
            }
            else if (WindowState == FormWindowState.Minimized)
            {
                //最小化时所需的操作

                this.ClientSize = new System.Drawing.Size(50, 50);
                this.WindowState = FormWindowState.Normal;
            } 
        }


    }
}
