using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using System.Collections;
using System.Configuration;
using INS.Business.Report;
using INS.Business;

namespace INS
{
    public partial class tginvh : Form
    {
        BindData[] u_dtypearr = new BindData[]  {
                                 new BindData("M","Misc."),
                                 new BindData("C","Client"),
                                 new BindData("P","Producer"),
                                 new BindData("B","R/I Broker"),
                                 new BindData("R","Reinsurer")};
        BindData[] u_statidarr = new BindData[]  {
                                   new BindData("D0100","Premium (Dir)"),
                                   new BindData("D0104","Premium (Out)"),
                                   new BindData("D0200","Claims (Dir)"),
                                   new BindData("D0204","Claims (Out)"),
                                   new BindData("R0100","Premium (In)"),
                                   new BindData("R0200","Claims (In)"),
                                   new BindData("G0000","Miscellaneous")};

        DBConnect operate = new DBConnect();
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public string fctlid_1 = "", fbus = "";
        public String oldrow = "", fctlid = "";
        public Boolean requery = false;
        public Boolean Addflag = false, Updateflag = false;
        public Boolean Skip_Validate = true;
        private TextBox Cur_TextBox = null;

        public String CodeValue
        {
            set { fchgcode.Text = value; }
        }

        public String CodeDesc
        {
            set { fdesc.Text = value; }
        }

        public String FdbtrValue
        {
            set { fdbtr.Text = value; }
        }

        public String FdbtrDesc
        {
            set { fdbtrdesc.Text = value; }
        }

        public String Faddr
        {
            set { faddr_t.Text = value; }
        }

        public tginvh()
        {
            InitializeComponent();
            foreach (Control control in Clauses.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            fdbtrtype.DataSource = u_dtypearr;
            fdbtrtype.ValueMember = "ID";
            fdbtrtype.DisplayMember = "Item";
            fstatid.DataSource = u_statidarr;
            fstatid.ValueMember = "ID";
            fstatid.DisplayMember = "Item";
            //userRight();
        }

        public void setControlFocus(string ctrlName)
        {
            if (ctrlName == "add2") { Controls["panel16"].Controls[ctrlName].Focus(); }
            else if (ctrlName == "fdbtr" || ctrlName == "fdbtrdesc") { Controls[ctrlName].Focus(); }
            else
            {
                Controls["panel15"].Controls[ctrlName].Focus();
            }
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
            {
                SendKeys.Send("{TAB}");
            }
        }

        public void userRight()
        {
            string p_fright = InsEnvironment.LoginUser.GetUserRight4a();
            string g_user = InsEnvironment.LoginUser.GetUserCode();
            string finpuser = "";
            Boolean ll_con = false;
            if ((finpuser == g_user && p_fright.Substring(3, 1) == "1") || (finpuser != g_user && p_fright.Substring(4, 1) == "1"))
            {
                if (dupdate.Visible == true) { dupdate.Visible = true; lupdate.Visible = true; }
            }
            else
            {
                dupdate.Visible = false; lupdate.Visible = false;
            }
            if (p_fright.Substring(1, 1) == "1")
            {
                if (dprint.Visible == true) { dprint.Visible = true; lprint.Visible = true; }
            }
            else
            {
                dprint.Visible = false; lprint.Visible = false;
            }
            if ((finpuser == g_user && p_fright.Substring(5, 1) == "1") || (finpuser != g_user && p_fright.Substring(6, 1) == "1"))
            {
                ll_con = true;
            }
            if (ll_con == true)
            {
                if (lpost.Visible == true) { lpost.Visible = true; dpost.Visible = true; }
            }
            else
            {
                lpost.Visible = false; dpost.Visible = false;
            }
        }

        public void FillData(string order, string query, string list)
        {
            try
            {
                if (requery) { req.Visible = true; }
                string sql = "select a.finvno as [Dr/Cr No.], CONVERT(varchar(10),a.finvdate,102) as [Doc Date], a.fdbtrtype,fdbtr,fdbtrdesc, " +
                    "case when a.fposted=1 then 'Yes' else 'No' end as [Posted], " +
                    "a.fstatid,a.fremark,a.fcremark,a.faddr_t1,a.famount, " +
                    "a.finpuser,a.finpdate, a.fupduser,a.fupddate,a.fcnfuser,a.fcnfdate,a.fctlid " +
                    "from oginvh a " +
                    "where a.fbus ='" + fbus + "' ";
                if (query != "") { sql = sql + query; }
                sql = sql + "order by case when fposted=1 then '1' else '2' end desc,finvdate desc   ";

                dataGridView1.DataSource = DBHelper.GetDataSet(sql);
                for (int i = 6; i < dataGridView1.ColumnCount; i++)
                {
                    this.dataGridView1.Columns[i].Visible = false;
                }
                dataGridView1.CellFormatting +=
                new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.dataGridView1_CellFormatting);
            }
            catch { }
        }

        private void dataGridView1_CellFormatting(object sender,System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {

            // Set the background to red for negative values in the Balance column.
            if (dataGridView1.Columns[e.ColumnIndex].Name.Equals("Dr/Cr No."))
            {
                dataGridView1.Columns[e.ColumnIndex].Width = 85;
            }
            if (dataGridView1.Columns[e.ColumnIndex].Name.Equals("Doc Date"))
            {
                dataGridView1.Columns[e.ColumnIndex].Width = 62;
            }
            if (dataGridView1.Columns[e.ColumnIndex].Name.Equals("Posted"))
            {
                dataGridView1.Columns[e.ColumnIndex].Width = 35;
            }
            dataGridView1.Columns["fdbtr"].HeaderText = "A/C#";
            dataGridView1.Columns["fdbtrdesc"].HeaderText = " ";
            dataGridView1.Columns["fdbtrtype"].HeaderText = "A/C Type";
           
        }

        private void dataGridView1_CurrentCellChanged(object sender, EventArgs e)
        {
            if (dataGridView1.CurrentCell != null)
            {
                int counter;
                counter = dataGridView1.CurrentCell.RowIndex;
                if (dataGridView1.Rows[counter].Cells["Dr/Cr No."].Value != null)
                {
                    finvno.Text = dataGridView1.Rows[counter].Cells["Dr/Cr No."].Value.ToString().Trim();
                }

                if (dataGridView1.Rows[counter].Cells["Doc Date"].Value != null)
                {
                    try
                    {
                        string a = dataGridView1.Rows[counter].Cells["Doc Date"].Value.ToString().Trim();
                        DateTime b = Convert.ToDateTime(a);
                        finvdate.Text = b.ToString("yyyy.MM.dd");
                    }
                    catch { }
                }

                if (dataGridView1.Rows[counter].Cells["fdbtr"].Value != null)
                {
                    fdbtr.Text = dataGridView1.Rows[counter].Cells["fdbtr"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fdbtrdesc"].Value != null)
                {
                    fdbtrdesc.Text = dataGridView1.Rows[counter].Cells["fdbtrdesc"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["faddr_t1"].Value != null)
                {
                    faddr_t.Text = dataGridView1.Rows[counter].Cells["faddr_t1"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fremark"].Value != null)
                {
                    fremark.Text = dataGridView1.Rows[counter].Cells["fremark"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fcremark"].Value != null)
                {
                    fcremark.Text = dataGridView1.Rows[counter].Cells["fcremark"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["famount"].Value != null)
                {
                    afamount.Text = dataGridView1.Rows[counter].Cells["famount"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["finpuser"].Value != null)
                {
                    finpuser.Text = dataGridView1.Rows[counter].Cells["finpuser"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["finpdate"].Value != null)
                {
                    finpdate.Text = dataGridView1.Rows[counter].Cells["finpdate"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fupduser"].Value != null)
                {
                    fupduser.Text = dataGridView1.Rows[counter].Cells["fupduser"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fupddate"].Value != null)
                {
                    fupddate.Text = dataGridView1.Rows[counter].Cells["fupddate"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fcnfuser"].Value != null)
                {
                    fcnfuser.Text = dataGridView1.Rows[counter].Cells["fcnfuser"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fcnfdate"].Value != null)
                {
                    fcnfdate.Text = dataGridView1.Rows[counter].Cells["fcnfdate"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["Posted"].Value != null)
                {
                    fposted.SelectedItem = dataGridView1.Rows[counter].Cells["Posted"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fstatid"].Value != null)
                {
                    fstatid.SelectedValue = dataGridView1.Rows[counter].Cells["fstatid"].Value.ToString().Trim();
                }
                if (dataGridView1.Rows[counter].Cells["fdbtrtype"].Value != null)
                {
                    fdbtrtype.SelectedValue = dataGridView1.Rows[counter].Cells["fdbtrtype"].Value.ToString().Trim();
                }

                rowlabel.Text = counter.ToString();
                fctlid = dataGridView1.Rows[counter].Cells["fctlid"].Value.ToString().Trim();
                Reloadgridview2(fctlid);
            }
            PostbtnControl();
        }

        void Reloadgridview2(string fctlid_i)
        {
            string sql = "SELECT a.flogseq,a.fchgcode,a.famount,b.fdesc,a.fremark,a.fctlid FROM oginvd a " +
                "left join " + Dbm + "mchgcode b on a.fchgcode = b.fid where a.fctlid_i='" + fctlid_i + "' ";
            tinvd.DataSource = DBHelper.GetDataSet(sql);
            for (int i = 3; i < tinvd.ColumnCount; i++)
            {
                this.tinvd.Columns[i].Visible = false;
            }
            tinvd.CellFormatting +=
            new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.tinvd_CellFormatting);
        }

        private void tinvd_CellFormatting(object sender,
     System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            tinvd.Columns["flogseq"].HeaderText = "Seq#";
            tinvd.Columns["fchgcode"].HeaderText = "Charge Code";
            tinvd.Columns["famount"].HeaderText = "Amount";
            tinvd.Columns["famount"].DefaultCellStyle.Format = "N2";
            tinvd.Columns["flogseq"].DefaultCellStyle.Format = "N0";
        }

        private void tinvd_CurrentCellChanged(object sender, EventArgs e)
        {
            if (tinvd.CurrentCell != null)
            {
                int counter;
                counter = tinvd.CurrentCell.RowIndex;
                if (tinvd.Rows[counter].Cells["flogseq"].Value != null)
                {
                    flogseq.Text = tinvd.Rows[counter].Cells["flogseq"].Value.ToString().Trim();
                }
                if (tinvd.Rows[counter].Cells["fchgcode"].Value != null)
                {
                    fchgcode.Text = tinvd.Rows[counter].Cells["fchgcode"].Value.ToString().Trim();
                }
                if (tinvd.Rows[counter].Cells["fdesc"].Value != null)
                {
                    fdesc.Text = tinvd.Rows[counter].Cells["fdesc"].Value.ToString().Trim();
                }
                if (tinvd.Rows[counter].Cells["fremark"].Value != null)
                {
                    fremark_t.Text = tinvd.Rows[counter].Cells["fremark"].Value.ToString().Trim();
                }
                if (tinvd.Rows[counter].Cells["famount"].Value != null)
                {
                    famount.Text = tinvd.Rows[counter].Cells["famount"].Value.ToString().Trim();
                }
            }
        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            e.Cancel = true;
        }

        private void tabControl1_Selecting2(object sender, TabControlCancelEventArgs e)
        {
            e.Cancel = false;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", "", "");
        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", "", "");
        }

        private void Gener_Click(object sender, EventArgs e)
        {
            fcremark.Text = fremark.Text;
        }

        void PostbtnControl()
        {
            string sql = "select * from " + Dbm + "xsysperiod where fidtype='BILLMON'";
            DataTable dt = DBHelper.GetDataSet(sql);
            DateTime now = DateTime.Now;
            if (dt.Rows.Count > 0)
            {
                DateTime fdatefr = Convert.ToDateTime(dt.Rows[0]["fdatefr"]);
                DateTime fdateto = Convert.ToDateTime(dt.Rows[0]["fdateto"]);
                if (dt.Rows[0]["fextdto"].ToString().Trim() == "")
                {
                    fdateto = Convert.ToDateTime(dt.Rows[0]["fdateto"]);
                }
                else { fdateto = Convert.ToDateTime(dt.Rows[0]["fextdto"]); }
                DateTime finDate = Convert.ToDateTime(finvdate.Text);

                if (now.Ticks > fdatefr.Ticks && now.Ticks < fdateto.Ticks && fposted.SelectedItem.ToString() == "No" && fdateto >= finDate)
                {
                    lpost.Visible = true;
                    dpost.Visible = true;
                }
                else
                {
                    lpost.Visible = false;
                    dpost.Visible = false;
                }
                if (fposted != null)
                {
                    if (fposted.Text.ToString() == "Yes")
                    { 
                        lupdate.Visible = false; dupdate.Visible = false;
                        ldel.Visible = false; ddel.Visible = false; 
                    }
                    else { lupdate.Visible = true; dupdate.Visible = true;
                            ldel.Visible = true; ddel.Visible = true;
                    }
                }
            }
        }

        void Post()
        {
            string[] update1 = postPart1();
            string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                SqlCommand command = connection.CreateCommand();
                SqlTransaction transaction;

                // Start a local transaction.
                transaction = connection.BeginTransaction("SampleTransaction");

                // Must assign both transaction object and connection 
                // to Command object for a pending local transaction
                command.Connection = connection;
                command.Transaction = transaction;

                try
                {
                    if (update1 != null)
                    {
                        for (int i = 0; i < update1.Length; i++)
                        {
                            if (update1[i] != "" && update1[i] != null)
                            {
                                command.CommandText = update1[i];
                                command.ExecuteNonQuery();
                            }
                        }
                    }
                    transaction.Commit();
                }
                catch (Exception ex)
                {
                    MessageBox.Show("Haven't Been Posted", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    Console.WriteLine("Commit Exception Type: {0}", ex.GetType());
                    Console.WriteLine("  Message: {0}", ex.Message);

                    // Attempt to roll back the transaction. 
                    try
                    {
                        transaction.Rollback();
                    }
                    catch (Exception ex2)
                    {
                        // This catch block will handle any errors that may have occurred 
                        // on the server that would cause the rollback to fail, such as 
                        // a closed connection.
                        Console.WriteLine("Rollback Exception Type: {0}", ex2.GetType());
                        Console.WriteLine("  Message: {0}", ex2.Message);
                    }
                }
                connection.Close();
                FillData("", "", "");
            }
        }

        public string[] postPart1()
        {
            ArrayList PostSql = new ArrayList();
            Decimal ln_payable = 0;
            DateTime ld_billdate = DateTime.Today, ld_bkdate = DateTime.Today;
            String lc_type = "", lc_doctype = "", lc_seqno = "";
            string selectsql = "select * from oginvh where fctlid='" + fctlid + "'";
            DataTable dt_oinvh = DBHelper.GetDataSet(selectsql);
            if (dt_oinvh.Rows.Count > 0)
            {
                u_glac(tinvd.Rows[0].Cells["fchgcode"].Value.ToString());
                ln_payable = Convert.ToDecimal(dt_oinvh.Rows[0]["famount"]);
                if (ln_payable >= 0) { lc_doctype = "1"; } else { lc_doctype = "2"; }
                string sql = "select * from " + Dbm + "xsysperiod where fidtype='BILLMON'";
                DataTable dt = DBHelper.GetDataSet(sql);
                if (dt.Rows.Count > 0)
                {
                    if (ld_billdate > Convert.ToDateTime(dt.Rows[0]["fdateto"]) && Convert.IsDBNull(dt.Rows[0]["fnxtdate"]) == false)
                    { ld_billdate = Convert.ToDateTime(dt.Rows[0]["fnxtdate"]); }
                }
                if (lc_doctype == "1") { lc_type = "DRNO"; } else { lc_type = "CRNO"; }
                ld_bkdate = ld_billdate;
                lc_seqno = NEWID(lc_type, ld_billdate.Year.ToString());
            }
            string updsql = "update oginvh set finvno='" + lc_seqno.Trim() + "', fbilldate='" + ld_billdate.ToString("yyyy-MM-dd HH:mm:ss") + "',fbkdate='" + ld_bkdate.ToString("yyyy-MM-dd HH:mm:ss") + "',fcnfuser='" + InsEnvironment.LoginUser.GetUserCode() + "', " +
                            "fcnfdate='" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "',fposted = 1 where fctlid='" + fctlid + "' and fposted = 2";
            PostSql.Add(updsql);

            string updsql1 = "update " + Dbm + "xsysparm  set fnxtid=RIGHT('00000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 5) where fidtype='" + lc_type + "' and fuy ='" + ld_billdate.Year.ToString() + "'";
            PostSql.Add(updsql1);

            string updsql3 = "INSERT INTO [dbo].[ostat]([fctlid],[fbus],[fmodule],[fstatid],[fclass],[fctlid_i],[fctlid_oi],[frefdate],[frefno],[fpolno],[fendtno], " +
                            "[fclmno],[fsrcref],[fcedepol],[fcedeendt],[fsrcclm],[fcedeclm],[fpaytype],[fpaynat],[fefffr],[feffto],[flosdate],[finstall],[ftotinstal], " +
                            "[fpnetonly],[fdbtrtype],[fdbtr],[fdbtrdesc],[fclnt],[fsclnt],[fpmcur],[frate],[fgpm],[ftdamt],[fcomamt],[fothamt],[famount],[fcrterm], " +
                            "[finsd],[fremark],[fsetamt],[ftsetdr],[ftsetcr],[fbalance],[famount_b],[fsetamt_b],[ftsetdr_b],[ftsetcr_b],[frnddif_b],[fbalance_b], " +
                            "[ftsetdr_g],[ftsetcr_g],[ftsetdr_c],[ftsetcr_c],[ftsetdr_s],[ftsetcr_s],[fset_g],[fset_c],[fset_s],[fbal_g],[fbal_c],[fbal_s],[fstatus], " +
                            "[ffeerel],[fsetdate],[fdoctype],[item_id],[interco_id],[cont_id],[dept_id],[person_id],[fgldebtor]) " +
                            "select '" + newid("OSTAT") + "',a.fbus,fmodule,fmodule,'' as fclass,a.fctlid,'' as fctlid_oi, " +
                            "fbilldate,finvno,'' as fpolno,'' as fendtno,'' as fclmno, '' as fsrcref,'' as fcedepol,'' as fcedeendt,'' as fsrcclm, '' as fcedeclm,'' as fpaytype,'' as fpaynat, " +
                            "null,null,null,0,0,'2',fdbtrtype,fdbtr,fdbtrdesc,'' as fclnt,'' as fsclnt,a.fpmcur,'1.0000', " +
                            "0.0000,0.0000,0.0000,0.0000,famount,fcrterm, '',fremark,'0.0000','0.0000','0.0000', " +
                            "famount,famount,0.0000,'0.0000','0.0000','0.0000',famount,'0.0000','0.0000','0.0000','0.0000','0.0000','0.0000', " +
                            "'0.0000','0.0000','0.0000',famount,'0.0000','0.0000','1','2',null,fdoctype,'000','000','00','000','" + u_gl_map + "','" + u_gl_debtor + "' " +
                            "from oginvh a " +
                            "where a.fctlid='" + fctlid + "' ";
            PostSql.Add(updsql3);

            string updsql4 = "update " + Dbm + "xsysparm  set fnxtid=RIGHT('00000'+ LTRIM(STR(CAST(fnxtid as int)+1)), 10) where fidtype='OSTAT'";
            PostSql.Add(updsql4);

            return (string[])PostSql.ToArray(typeof(string));
        }

        string u_gl_debtor, u_gl_income, u_gl_map;

        string u_ex_debtor, u_ex_income;

        void u_glac(string fid)
        {
            DataTable tglac = operate.GetTable("select * from mchgcode where ftype='C' and fid ='" + fid + "' ");
            foreach (DataRow dr in tglac.Rows)
            {
                if (dr["fglcode"].ToString() == "")
                {
                    u_gl_debtor = "ZZZZZZZZ";
                }
                else
                {
                    u_gl_debtor = dr["fglcode"].ToString().Trim();
                }
                u_ex_debtor = dr["fexdesc"].ToString().Trim();

                if (dr["fglcode"].ToString() == "")
                {
                    u_gl_income = "ZZZZZZZZ";
                }
                else
                {
                    u_gl_income = dr["fglcode"].ToString().Trim();
                }
                u_ex_income = dr["fexdesc"].ToString().Trim();

                if (dr["fmap"].ToString() == "")
                {
                    u_gl_map = "00";
                }
                else
                {
                    u_gl_map = dr["fmap"].ToString().Trim();
                }
            }
        }

        public string NEWID(string type, string year)
        {
            string lc_seqno = "";
            string sql = "select rtrim(fprefix)+fnxtid as lc_seqno from " + Dbm + "xsysparm where fidtype='" + type + "' and fuy='" + year + "' ";
            DataTable dt = DBHelper.GetDataSet(sql);
            if (dt.Rows.Count > 0)
            {
                lc_seqno = dt.Rows[0]["lc_seqno"].ToString();
            }
            return lc_seqno.Trim();
        }

        public string newid(string type)
        {
            string lc_seqno = "";
            string sql = "select fnxtid as lc_seqno from " + Dbm + "xsysparm where fidtype='" + type + "' ";
            DataTable dt = DBHelper.GetDataSet(sql);
            if (dt.Rows.Count > 0)
            {
                lc_seqno = dt.Rows[0]["lc_seqno"].ToString();
            }
            return lc_seqno;
        }

        void print()
        {
            pmscinv tempform = new pmscinv(this);
            tempform.fctlid_1 = fctlid;
            tempform.p_calling = "MSC";
            tempform.ShowDialog();
        }

        private void lprint_Click(object sender, EventArgs e)
        {
            print();
        }

        private void dprint_Click(object sender, EventArgs e)
        {
            print();
        }

        private void req_Click(object sender, EventArgs e)
        {
            reqginv tempform = new reqginv(this);
            tempform.fbus = fbus;
            tempform.flag = "tginvh";
            tempform.Initializefdbtrtype();
            tempform.ShowDialog();
        }

        void btnctrl(string flag)
        {
            if (flag == "add" || flag == "update")
            {
                ladd.Visible = false;
                dadd.Visible = false;
                ldel.Visible = false;
                ddel.Visible = false;
                lupdate.Visible = false;
                dupdate.Visible = false;
                lpost.Visible = false;
                dpost.Visible = false;
                lexit.Visible = false;
                dexit.Visible = false;
                lprint.Visible = false;
                dprint.Visible = false;
                lsave.Visible = true;
                dsave.Visible = true;
                lcancel.Visible = true;
                dcancel.Visible = true;
            }
            if (flag == "back")
            {
                Addflag = false;
                Updateflag = false;
                foreach (Control ctrl in Clauses.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                    if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
                    if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                }
                foreach (Control ctrl in panel15.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                    if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
                }
                Gener.Visible = false;
                add2.Visible = false;
                del2.Visible = false;
                ladd.Visible = true;
                dadd.Visible = true;
                lpost.Visible = true;
                dpost.Visible = true;
                lexit.Visible = true;
                dexit.Visible = true;
                lprint.Visible = true;
                dprint.Visible = true;
                lsave.Visible = false;
                dsave.Visible = false;
                lcancel.Visible = false;
                dcancel.Visible = false;
                tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting2);
            }
        }

        private void dcancel_Click(object sender, EventArgs e)
        {
            btnctrl("back");
            reload(oldrow, "cancel");
        }

        void reload(string fid, string lab)
        {
            string str = "select a.finvno as [Dr/Cr No.], CONVERT(varchar(10),a.finvdate,102) as [Doc Date], a.fdbtrtype,fdbtr,fdbtrdesc, " +
                    "case when a.fposted=1 then 'Yes' else 'No' end as [Posted], " +
                    "a.fstatid,a.fremark,a.fcremark,a.faddr_t1,a.famount, " +
                    "a.finpuser,a.finpdate, a.fupduser,a.fupddate,a.fcnfuser,a.fcnfdate,a.fctlid " +
                    "from oginvh a " +
                    "where a.fbus ='" + fbus + "' ";
            str = str + "order by case when fposted=1 then '1' else '2' end desc,finvdate desc   ";
            DataTable dt = DBHelper.GetDataSet(str);
            if (dt.Rows.Count > 0)
            {
                dataGridView1.DataSource = dt;
                for (int i = 6; i < dataGridView1.ColumnCount; i++)
                {
                    this.dataGridView1.Columns[i].Visible = false;
                }
                dataGridView1.CellFormatting +=
                new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.dataGridView1_CellFormatting);
                
                if ((lab == "insert" || lab == "update") && fid != "")
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        if (dt.Rows[i]["fdbtr"].ToString().Trim() == fid.Trim())
                        {
                            dataGridView1.CurrentCell = dataGridView1.Rows[i].Cells["fdbtr"];
                            break;
                        }
                    }
                }

                if (lab == "cancel" && dataGridView1.CurrentCell != null)
                {
                    dataGridView1.CurrentCell = dataGridView1.Rows[int.Parse(fid)].Cells["fdbtr"];
                }
            }
            PostbtnControl();
        }

        private void dadd_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            add();
        }

        private void ladd_Click(object sender, EventArgs e)
        {
            tabControl1.SelectedTab = Clauses;
            add();
        }

        private void lpost_Click(object sender, EventArgs e)
        {
            Post();
        }

        private void dpost_Click(object sender, EventArgs e)
        {
            Post();
        }

        void add()
        {
            oldrow = rowlabel.Text;
            btnctrl("add");
            Addflag = true;
            add2.Visible = true;
            fstatid.Focus();
            fstatid.Enabled = true;
            fstatid.BackColor = System.Drawing.SystemColors.Window;
            fdbtrtype.Enabled = true;
            fdbtrtype.BackColor = System.Drawing.SystemColors.Window;
            fdbtrtype.SelectedIndex = 0;
            Gener.Visible = true;
            Gener.Enabled = true;
            foreach (Control ctrl in Clauses.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                }
            }
            foreach (Control ctrl in panel15.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                }
            }
            afamount.Text = "";
            XBTN.Enabled = true;
            fdbtr.ReadOnly = false;
            fdbtr.BackColor = System.Drawing.SystemColors.Window;
            fdbtr1.ReadOnly = false;
            fdbtr1.BackColor = System.Drawing.SystemColors.Window;
            faddr_t.ReadOnly = false;
            faddr_t.BackColor = System.Drawing.SystemColors.Window;
            fremark.ReadOnly = false;
            fremark.BackColor = System.Drawing.SystemColors.Window;
            fcremark.ReadOnly = false;
            fcremark.BackColor = System.Drawing.SystemColors.Window;
            DataTable dt = tinvd.DataSource as DataTable;
            dt.Clear(); tinvd.DataSource = dt;
            fposted.Text = "No";
            finvdate.Text = DateTime.Now.ToString("yyyy.MM.dd");
        }

        private void add2_Click(object sender, EventArgs e)
        {
            decimal Row = 0;
            if (tinvd.Rows.Count != 0)
            {
                Row = Fct.sdFat(tinvd.Rows[tinvd.Rows.Count - 1].Cells["flogseq"].Value);
                del2.Visible = true;
            }
            else { Row = 0; del2.Visible = false; }

            DataTable DTtinvd = tinvd.DataSource as DataTable;
            DataRow newCustomersRow = DTtinvd.NewRow();
            newCustomersRow["flogseq"] = Row + 1;
            DTtinvd.Rows.InsertAt(newCustomersRow, tinvd.Rows.Count);
            tinvd.DataSource = DTtinvd;
            tinvd.CurrentCell = tinvd.Rows[tinvd.Rows.Count - 1].Cells["flogseq"];

            foreach (Control ctrl in panel15.Controls)
            {
                if (ctrl is TextBox)
                {
                    if (ctrl.Name != "flogseq") { ((TextBox)ctrl).Text = ""; }
                    ((TextBox)ctrl).ReadOnly = false;
                    ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                }
            }

            fdesc.ReadOnly = true;
            fdesc.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            ChgBtn.Enabled = true;
            if (tinvd.Rows.Count != 0) { del2.Visible = true; }
        }

        private void del2_Click(object sender, EventArgs e)
        {
            if (tinvd.RowCount != 0)
            {
                try
                {
                    DataTable DTtinvd = tinvd.DataSource as DataTable;
                    DTtinvd.Rows.RemoveAt(tinvd.CurrentCell.RowIndex);
                    tinvd.DataSource = DTtinvd;
                    tinvd.CurrentCell = tinvd.Rows[tinvd.Rows.Count - 1].Cells["flogseq"];
                }
                catch { }
            }
            if (tinvd.RowCount == 0)
            {
                foreach (Control ctrl in panel15.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                }
                del2.Visible = false;
                ChgBtn.Enabled = false;
            }
        }

        private void fdbtrtype_SelectedValueChanged(object sender, EventArgs e)
        {
            if (fdbtrtype.SelectedValue.ToString() == "M")
            {
                label6.Visible = true;
                fdbtr1.Visible = true;
                label24.Visible = true;
                faddr_t.Visible = true;
                label5.Visible = false;
                fdbtr.Visible = false;
                XBTN.Visible = false;
                fdbtrdesc.Visible = false;
            }
            else
            {
                label6.Visible = false;
                fdbtr1.Visible = false;
                label24.Visible = false;
                faddr_t.Visible = false;
                label5.Visible = true;
                fdbtr.Visible = true;
                XBTN.Visible = true;
                fdbtrdesc.Visible = true;
                if ((Addflag == true || Updateflag == true) && !Skip_Validate)
                {
                    string[] result = FdbtrCheckValue(fdbtr.Text.ToString(), fdbtrtype.SelectedValue.ToString());
                    if (result != null) { fdbtrdesc.Text = result[0]; if (fdbtrdesc.Text == "") { fdbtr.Text = ""; } }
                }
            }
        }

        private void flogseq_TextChanged(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tinvd.CurrentCell != null)
                {
                    tinvd["flogseq", tinvd.CurrentCell.RowIndex].Value = Fct.snFat(flogseq.Text.ToString());
                }
            }
        }

        private void fchgcode_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tinvd.CurrentCell != null)
                {
                    Cur_TextBox = (TextBox)sender;
                    string desc = "";
                    Boolean ll_ok = CheckEmptyString(Cur_TextBox);
                    if (ll_ok)
                    {
                        desc = CheckValue(fchgcode.Text.ToString());
                        fdesc.Text = desc;
                    }
                    if (!ll_ok || desc == null)
                    {
                        if (desc == null && !Skip_Validate)
                        {
                            fdesc.Text = "";
                            MessageBox.Show("Invalid Value");
                        }
                        fchgcode.Focus();
                        fchgcode.SelectAll();
                    }
                }
            }
        }

        private void fchgcode_TextChanged(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tinvd.CurrentCell != null)
                {
                    tinvd["fchgcode", tinvd.CurrentCell.RowIndex].Value = fchgcode.Text.ToString();
                }
            }
        }

        private void fdesc_TextChanged(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tinvd.CurrentCell != null)
                {
                    tinvd["fdesc", tinvd.CurrentCell.RowIndex].Value = fdesc.Text.ToString();
                    fremark_t.Text = fdesc.Text;
                }
            }
        }

        private void fremark_t_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tinvd.CurrentCell != null)
                {
                    tinvd["fremark", tinvd.CurrentCell.RowIndex].Value = fremark_t.Text.ToString();
                }
            }
        }

        private void famount_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tinvd.CurrentCell != null)
                {
                    tinvd["famount", tinvd.CurrentCell.RowIndex].Value = Fct.sdFat(famount.Text);
                }
                totalcontrol();
            }
        }

        public Boolean CheckEmptyString(Control text_obj)
        {
            if (Skip_Validate)
            {
                return true;
            }
            if (text_obj.Text.Trim() == "" || text_obj.Text.Trim() == "    .  .")
            {
                MessageBox.Show("Invalid Value");
                return false;
            }
            return true;
        }

        public string CheckValue(string fid)
        {
            string str = "select fid, fdesc from " + Dbm + "mchgcode where ftype='C' and fid='" + fid + "'";
            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["odata"].ConnectionString);
            SqlCommand cmd = new SqlCommand(str, con);
            con.Open();
            using (SqlDataReader sdr = cmd.ExecuteReader())
            {
                if (sdr.HasRows && sdr.Read())
                {
                    string fdesc = sdr["fdesc"].ToString().Trim();
                    sdr.Close();
                    return fdesc;
                }
                else { return null; }
            }
        }

        private void fdbtr1_Validated(object sender, EventArgs e)
        {
            Cur_TextBox = (TextBox)sender;
            Boolean ll_ok = CheckEmptyString(Cur_TextBox);
            if (!ll_ok)
            {
                fdbtr1.Focus();
            }
        }

        private void fdbtr_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate && fdbtrtype.SelectedValue.ToString() != "M")
            {
                Cur_TextBox = (TextBox)sender;
                string desc = "";
                Boolean ll_ok = CheckEmptyString(Cur_TextBox);
                if (ll_ok)
                {
                    string[] result = FdbtrCheckValue(fdbtr.Text.ToString(), fdbtrtype.SelectedValue.ToString());
                    fdbtrdesc.Text = result[0];
                    faddr_t.Text = result[1];
                }
                if (!ll_ok || desc == null)
                {
                    if (desc == null && !Skip_Validate)
                    {
                        fdbtrdesc.Text = "";
                        MessageBox.Show("Invalid Value");
                    }
                    fdbtr.Focus();
                    fdbtr.SelectAll();
                }
            }
        }

        public string[] FdbtrCheckValue(string fid, string flag)
        {
            string str = "select fid, fdesc,rtrim(fadd1)+rtrim(fadd2)+rtrim(fadd3)+rtrim(fadd4) as faddr from " + Dbm + "mprdr where ftype='" + flag + "' and fid='" + fid + "'";
            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["odata"].ConnectionString);
            SqlCommand cmd = new SqlCommand(str, con);
            con.Open();
            using (SqlDataReader sdr = cmd.ExecuteReader())
            {
                if (sdr.HasRows && sdr.Read())
                {
                    ArrayList result = new ArrayList();
                    result.Add(sdr["fdesc"].ToString().Trim());
                    result.Add(sdr["faddr"].ToString().Trim());
                    sdr.Close();
                    return (string[])result.ToArray(typeof(string));
                }
                else { return null; }
            }
        }

        private void XBTN_Click(object sender, EventArgs e)
        {
            if (fdbtrtype.SelectedValue.ToString() == "P")
            {
                frmProducerSearch tempform = new frmProducerSearch(this);
                tempform.flag = "tginvh";
                tempform.FillData("", "");
                tempform.ShowDialog();
            }
            else if (fdbtrtype.SelectedValue.ToString() == "C")
            {
                frmClientSearch tempform = new frmClientSearch(this);
                tempform.ftype = "C";
                tempform.flag = "tginvh";
                tempform.FillData("", "");
                tempform.ShowDialog();
            }
            else if (fdbtrtype.SelectedValue.ToString() == "R")
            {
                frmReinsurerSearch tempform = new frmReinsurerSearch(this);
                tempform.flag = "tginvh";
                tempform.FillData("", "");
                tempform.ShowDialog();
            }
            else
            {
                frmBrokerSearch tempform = new frmBrokerSearch(this);
                tempform.flag = "tginvh";
                tempform.FillData("", "");
                tempform.ShowDialog();
            }
        }

        private void ChgBtn_Click(object sender, EventArgs e)
        {
            frmCodeSearch tempform = new frmCodeSearch(this);
            tempform.FillData("", "");
            tempform.ShowDialog();
        }

        private void Clauses_Enter(object sender, EventArgs e)
        {
            Skip_Validate = false;
        }

        private void Clauses_Leave(object sender, EventArgs e)
        {
            Skip_Validate = true;
        }

        private void lexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void dexit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        void update() {
            oldrow = rowlabel.Text;
            tabControl1.SelectedTab = Clauses;
            tabControl1.Selecting += new TabControlCancelEventHandler(tabControl1_Selecting);
            btnctrl("update");
            Updateflag = true;
            add2.Visible = true;
            del2.Visible = true;
            fstatid.Focus();
            fstatid.Enabled = true;
            fstatid.BackColor = System.Drawing.SystemColors.Window;
            fdbtrtype.Enabled = true;
            fdbtrtype.BackColor = System.Drawing.SystemColors.Window;
            Gener.Visible = true;
            Gener.Enabled = true;
            XBTN.Enabled = true;
            fdbtr.ReadOnly = false;
            fdbtr.BackColor = System.Drawing.SystemColors.Window;
            fdbtr1.ReadOnly = false;
            fdbtr1.BackColor = System.Drawing.SystemColors.Window;
            faddr_t.ReadOnly = false;
            faddr_t.BackColor = System.Drawing.SystemColors.Window;
            fremark.ReadOnly = false;
            fremark.BackColor = System.Drawing.SystemColors.Window;
            fcremark.ReadOnly = false;
            fcremark.BackColor = System.Drawing.SystemColors.Window;
            foreach (Control ctrl in panel15.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = false;
                    ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                }
            }

            fdesc.ReadOnly = true;
            fdesc.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            ChgBtn.Enabled = true;
        }

        private void lupdate_Click(object sender, EventArgs e)
        {
            update();
        }

        private void dupdate_Click(object sender, EventArgs e)
        {
            update();
        }

        void del()
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the information?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string deldatestr = "delete from oginvh where fctlid='" + fctlid + "'";
                string deldatestr1 = "delete from oginvd where fctlid_i='" + fctlid + "'";
                string deldatestr2 = "delete from ostat where fctlid_i='" + fctlid + "'";
                string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();

                    SqlCommand command = connection.CreateCommand();
                    SqlTransaction transaction;

                    // Start a local transaction.
                    transaction = connection.BeginTransaction("SampleTransaction");

                    // Must assign both transaction object and connection 
                    // to Command object for a pending local transaction
                    command.Connection = connection;
                    command.Transaction = transaction;

                    try
                    {
                        command.CommandText = deldatestr;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr1;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr2;
                        command.ExecuteNonQuery();
                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("Haven't Been Deleted", "Warning",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                FillData("", "", "");
                tabControl1.SelectedTab = Classes;
            }
        }

        private void ldel_Click(object sender, EventArgs e)
        {
            del();
        }

        private void ddel_Click(object sender, EventArgs e)
        {
            del();
        }

        public string u_validation()
        {
            string ctrlName = "";
            if (tinvd.RowCount == 0)
            {
                MessageBox.Show("BreakDown Record is not found!", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                add2.Focus();
                return "add2";
            }
            foreach (DataGridViewRow row in tinvd.Rows)
            {
                int SelectedIndex = tinvd.Rows.IndexOf(row);
                if (CheckValue(row.Cells["fchgcode"].Value.ToString()) == null)
                {
                    MessageBox.Show("Invalid valid!", "Warning",
                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    fchgcode.Focus();
                    tinvd.CurrentCell = tinvd.Rows[SelectedIndex].Cells["flogseq"];
                    return "fchgcode";
                }
               // if (Fct.sdFat(row.Cells["famount"].Value) <= 0)
               // {
               //     MessageBox.Show("Invalid Value!", "Warning",
               //MessageBoxButtons.OK, MessageBoxIcon.Information);
               //     famount.Focus();
               //     tinvd.CurrentCell = tinvd.Rows[SelectedIndex].Cells["flogseq"];
               //     return "famount";
               // }
            }
            if (Fct.stFat(fdbtr1.Text)=="" && fdbtrtype.SelectedValue.ToString()=="M") 
            {
                MessageBox.Show("Invalid Value!", "Warning",MessageBoxButtons.OK, MessageBoxIcon.Information);
                fdbtr1.Focus();
                return "fdbtr1";
            }
            if (fdbtrtype.SelectedValue.ToString() != "M" && FdbtrCheckValue(fdbtr.Text.ToString(), fdbtrtype.SelectedValue.ToString()) == null)
            {
                MessageBox.Show("Invalid Value!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                fdbtr.Focus();
                return "fdbtr";
            }
            return ctrlName;
        }

        private void dsave_Click(object sender, EventArgs e)
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really Save this item?", "Updated Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                string ctrlName = u_validation();
                if (ctrlName != "")
                {
                   // setControlFocus(ctrlName);
                    return;
                }
                else
                {
                    string[] sql1 = save();
                    string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        connection.Open();

                        SqlCommand command = connection.CreateCommand();
                        SqlTransaction transaction;

                        // Start a local transaction.
                        transaction = connection.BeginTransaction("SampleTransaction");

                        // Must assign both transaction object and connection 
                        // to Command object for a pending local transaction
                        command.Connection = connection;
                        command.Transaction = transaction;

                        try
                        {
                            if (sql1 != null)
                            {
                                for (int i = 0; i < sql1.Length; i++)
                                {
                                    if (sql1[i] != "" && sql1[i] != null)
                                    {
                                        command.CommandText = sql1[i];
                                        command.ExecuteNonQuery();
                                    }
                                }
                            }
                            transaction.Commit();
                            btnctrl("back");
                            FillData("", "", "");
                            reload("", "update");
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show("Haven't Been inserted!", "Warning",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                }
            }
        }

        public string[] save() {
            DateTime time = DateTime.Now;
            if (Addflag == true)
            {
                ArrayList addsql = new ArrayList();
                string user = InsEnvironment.LoginUser.GetUserCode();
                string fcinpname = InsEnvironment.LoginUser.GetChineseName();
                string date = time.ToString("yyyy-MM-dd");
                string fctlid_oginvh = Fct.NewId("oginvh");
                string fctlid_oginvd = Fct.NewId("oginvd");
                string fdoctype = "";
                if (Fct.sdFat(famount.Text) >= 0) { fdoctype = "1"; } else { fdoctype = "2"; }
                string oginvh_sql = "INSERT INTO [dbo].[oginvh]([fbus],[fmodule],[fstatid],[fctlid],[finvdate],[fbilldate],[fbkdate],[finvno],[fdbtrtype], "+
                        "[fdbtr],[fdbtrdesc],[faddr_t1],[fremark],[fcremark],[fpmcur],[famount],[famount_d],[fctldel],[fcrterm],[fdoctype], "+
                        "[fposted],[finpuser],[finpdate],[fupduser],[fupddate],[fcnfuser],[fcnfdate],[fvouchid],[fcinpname])   "+
                        "VALUES('G','G0000','" + fstatid.SelectedValue + "','" + fctlid_oginvh + "','" + date + "', " +
                        "'" + date + "','" + date + "','','" + fdbtrtype.SelectedValue + "','" + Fct.stFat(fdbtr.Text) + "', " +
                        "'" + Fct.stFat(fdbtrdesc.Text) + "','" + Fct.stFat(faddr_t.Text) + "','" + Fct.stFat(fremark.Text) + "','" + Fct.stFat(fcremark.Text) + "', " +
                        "'HK$','" + Fct.sdFat(afamount.Text) + "','" + Fct.sdFat(afamount.Text) + "','" + fctlid_oginvh + "',0, " +
                        "'" + fdoctype + "','2','" + user + "','" + date + "','" + user + "', " +
                        "'" + date + "','" + user + "','" + date + "',null,'" + fcinpname + "')";
                addsql.Add(oginvh_sql);

                string sqloginvh = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_oginvh) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='oginvh'";
                addsql.Add(sqloginvh);

                DataTable dt = tinvd.DataSource as DataTable;
                if (dt != null && dt.Rows.Count > 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        if (i != 0) { fctlid_oginvd = (int.Parse(fctlid_oginvd) + 1).ToString().PadLeft(10, '0'); }
                        decimal famounts = Fct.sdFat(dt.Rows[i]["famount"]);
                        string insertsql = "INSERT INTO [dbo].[oginvd] ([fbus],[fmodule],[fctlid_i],[fctlid],[flogseq],[fchgcode],[fremark],[fpmcur],[famount],[famount_d]) VALUES " +
                            "('G','G0000','" + fctlid_oginvh + "','" + fctlid_oginvd + "'," + Fct.snFat(dt.Rows[i]["flogseq"]) + ",'" + Fct.stFat(dt.Rows[i]["fchgcode"]) + "','" + Fct.stFat(dt.Rows[i]["fremark"]) + "','HK$','" + famounts + "','" + famounts + "')";
                        addsql.Add(insertsql);
                    }
                    string sqlfctlid = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_oginvd) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='oginvd'";
                    addsql.Add(sqlfctlid);
                }

                return (string[])addsql.ToArray(typeof(string));
            }
            else if (Updateflag == true)
            {
                ArrayList updatesql = new ArrayList();
                string user = InsEnvironment.LoginUser.GetUserCode();
                string date = time.ToString("yyyy-MM-dd");
                string oginvh_sql = "update oginvh set fupduser = '" + user + "', fupddate = '" + date + "',fdbtr = '" + Fct.stFat(fdbtr.Text) + "',fdbtrtype = '" + fdbtrtype.SelectedValue + "', " +
                    "fremark = '" + fremark.Text + "',fcremark = '" + fcremark.Text + "',faddr_t1 = '" + faddr_t.Text + "',fdbtrdesc = '" + Fct.stFat(fdbtrdesc.Text) + "',fstatid = '" + fstatid.SelectedValue + "', " +
                    "finvdate ='" + time.ToString("yyyy-MM-dd") + "' ,famount_d = '" + Fct.sdFat(afamount.Text) + "',famount = '" + Fct.sdFat(afamount.Text) + "', " +
                    "fdoctype = case when " + Fct.sdFat(famount.Text) + " >=0 then '1' else '2' end where fctlid ='" + fctlid + "'";
                updatesql.Add(oginvh_sql);
                string sql = "SELECT a.flogseq,a.fchgcode,a.famount,b.fdesc,a.fremark,a.fctlid FROM oginvd a " +
                    "left join " + Dbm + "mchgcode b on a.fchgcode = b.fid where a.fctlid_i='" + fctlid + "' ";
                DataTable dtOrg = DBHelper.GetDataSet(sql);
                DataTable dt = tinvd.DataSource as DataTable;
                if (dt != null)
                {
                    updatesql.AddRange(CompareDt(dtOrg, dt, "fctlid"));
                }
                return (string[])updatesql.ToArray(typeof(string));
            }
            else { return null; }
        }

        public string[] CompareDt(DataTable dt1, DataTable dt2, string keyField)
        {
            //为三个表拷贝表结构
            DataTable dtRetDel = dt1.Clone();
            DataTable dtRetAdd = dt1.Clone();
            DataTable dtRetDif = dt1.Clone();

            ArrayList updsql = new ArrayList();
            int colCount = dt1.Columns.Count;

            DataView dv1 = dt1.DefaultView;
            DataView dv2 = dt2.DefaultView;

            //先以第一个表为参照，看第二个表是修改了还是删除了
            foreach (DataRowView dr1 in dv1)
            {
                dv2.RowFilter = keyField + " = '" + dr1[keyField].ToString() + "'";
                if (dv2.Count > 0)
                {
                    if (!CompareUpdate(dr1, dv2[0]))//比较是否有不同的
                    {
                        dtRetDif.Rows.Add(dv2[0].Row.ItemArray);//修改后
                        continue;
                    }
                }
                else
                {
                    //已经被删除的
                    dtRetDel.Rows.Add(dr1.Row.ItemArray);
                }
            }

            //以第一个表为参照，看记录是否是新增的
            dv2.RowFilter = "";//清空条件
            foreach (DataRowView dr2 in dv2)
            {
                dv1.RowFilter = keyField + " = '" + dr2[keyField].ToString() + "'";
                if (dv1.Count == 0)
                {
                    //新增的
                    dtRetAdd.Rows.Add(dr2.Row.ItemArray);
                }
            }

            if (dtRetAdd != null && dtRetAdd.Rows.Count > 0)
            {
                string fctlid_oginvd = Fct.NewId("oginvd");
                for (int i = 0; i < dtRetAdd.Rows.Count; i++)
                {
                    if (i != 0) { fctlid_oginvd = (int.Parse(fctlid_oginvd) + 1).ToString().PadLeft(10, '0'); }
                    decimal famounts = Fct.sdFat(dtRetAdd.Rows[i]["famount"]);
                    string insertsql = "INSERT INTO [dbo].[oginvd] ([fbus],[fmodule],[fctlid_i],[fctlid],[flogseq],[fchgcode],[fremark],[fpmcur],[famount],[famount_d]) VALUES " +
                        "('G','G0000','" + fctlid + "','" + fctlid_oginvd + "'," + Fct.snFat(dtRetAdd.Rows[i]["flogseq"]) + ",'" + Fct.stFat(dtRetAdd.Rows[i]["fchgcode"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fremark"]) + "','HK$','" + famounts + "','" + famounts + "')";
                    updsql.Add(insertsql);
                }
                string sqlfctlid = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_oginvd) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='oginvd'";
                updsql.Add(sqlfctlid);
            }
            if (dtRetDif != null && dtRetDif.Rows.Count > 0)
            {
                for (int i = 0; i < dtRetDif.Rows.Count; i++)
                {
                    decimal famounts = Fct.sdFat(dtRetDif.Rows[i]["famount"]);
                    string update = "UPDATE [dbo].[oginvd] SET [flogseq] = '" + Fct.snFat(dtRetDif.Rows[i]["flogseq"]) + "',[fchgcode] = '" + Fct.stFat(dtRetDif.Rows[i]["fchgcode"]) + "',[fremark] = '" + Fct.stFat(dtRetDif.Rows[i]["fremark"]) + "',[famount] = '" + famounts + "',[famount_d] = '" + famounts + "' where fctlid='" + dtRetDif.Rows[i]["fctlid"].ToString().Trim() + "'";
                    updsql.Add(update);
                }
            }

            if (dtRetDel != null && dtRetDel.Rows.Count > 0)
            {
                for (int i = 0; i < dtRetDel.Rows.Count; i++)
                {
                    string del = "delete from oginvd where fctlid='" + dtRetDel.Rows[i]["fctlid"].ToString().Trim() + "'";
                    updsql.Add(del);
                }
            }

            return (string[])updsql.ToArray(typeof(string));
        }

        //比较是否有不同的
        private static bool CompareUpdate(DataRowView dr1, DataRowView dr2)
        {
            //行里只要有一项不一样，整个行就不一样,无需比较其它
            object val1;
            object val2;
            for (int i = 0; i < dr1.Row.ItemArray.Length; i++)
            {
                val1 = dr1[i];
                val2 = dr2[i];
                if (!val1.Equals(val2))
                {
                    return false;
                }
            }
            return true;
        }

        void totalcontrol() {
            Decimal famounts = 0;
            for (int i = 0; i < tinvd.Rows.Count; i++)
            {
                if (tinvd["famount", i].Value.ToString() != "")
                {
                    famounts = famounts + Convert.ToDecimal(tinvd["famount", i].Value);
                }
            }
            afamount.Text = famounts.ToString("n2");
        }

        private void tginvh_Resize(object sender, EventArgs e)
        {
            if (WindowState == FormWindowState.Maximized)
            {
                //最大化时所需的操作 
                this.ClientSize = new System.Drawing.Size(834, 596);
                this.WindowState = FormWindowState.Normal;
            }
            else if (WindowState == FormWindowState.Minimized)
            {
                //最小化时所需的操作

                this.ClientSize = new System.Drawing.Size(50, 50);
                this.WindowState = FormWindowState.Normal;
            } 
        }

        private void lprint_Click_1(object sender, EventArgs e)
        {
            print();
        }

        


    }
}

