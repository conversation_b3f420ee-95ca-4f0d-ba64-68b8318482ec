using CrystalDecisions.CrystalReports.Engine;
using INS.Business.objRpt;
using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business.Report
{
    public partial class ppyset : Form
    {
        public string Dbm = InsEnvironment.DataBase.GetDbm();
       
        //private string rpDirectory = Application.StartupPath.Replace("\\bin\\Debug", "\\objRpt");
        private string rpDirectory = "M:\\Software II\\New INS Runtime\\Setup\\objRpt";
        public string p_calling= "", fctlid = "";
        public int ln_flogseq = 0; public DateTime ld_faccdate;

        public string Dbo = InsEnvironment.DataBase.GetDbo();
        private tpyset tpyset;

        public ppyset()
        {
            InitializeComponent();
        }

        public ppyset(tpyset tpyset)
        {
            // TODO: Complete member initialization
            this.tpyset = tpyset;
            InitializeComponent();
        }

        private void PS_Click(object sender, EventArgs e)
        {
            progressBar1.Visible = true;
            progressBar1.Maximum = 50;//设置最大长度值 
            progressBar1.Value = 0;//设置当前值 
            progressBar1.Step = 5;//设置没次增长多少 
            for (int i = 0; i < 10; i++)//循环 
            {
                System.Threading.Thread.Sleep(1000);//暂停1秒 
                progressBar1.Value += progressBar1.Step;
            }
            callCrPrmReg();
            this.Close();
        }

        void callCrPrmReg()
        {
            string Dbo = InsEnvironment.DataBase.GetDbo();

            ReportDocument cryRpt = new ReportDocument();
            cryRpt.Load(rpDirectory + "\\acvoucher.rpt");

            string sql = "select distinct a.*,case when b.fctlid_j is null then '2' else '1' end as pchkvou, " +
                        "case when c.fctlid_j is null then '2' else '1' end as psetvou, " +
                        "case when d.fctlid_j is null then '2' else '1' end as padjvou, " +
                        "case when e.fctlid_1 is null then '2' else '1' end as pglvou " +
                        "from oacjnl a left join oacchk b on a.fctlid = b.fctlid_j "+
                        "left join oacset c on a.fctlid = c.fctlid_j "+
                        "left join oacadj d on a.fctlid = d.fctlid_j "+
                        "left join oactogl e on a.fctlid = e.fctlid_1 where a.fctlid ='" + fctlid + "'";
            DataTable dt = DBHelper.GetDataSet(sql);
            cryRpt.SetDataSource(dt);
            if (p_calling == "ACVOUCHER")
            {
                string chkvousql ="select a.*,b.fdesc as fbkdesc, "+
		                "case when a.famount>=0 then famount else 0 end as fdramt, "+
		                "case when a.famount<0 then abs(famount) else 0 end as fcramt, "+
		                "case when a.fualamt>=0 then fualamt else 0 end as udramt, "+
		                "case when a.fualamt<0 then abs(fualamt) else 0 end as ucramt "+
                        "from oacchk a," + Dbm + "mchgcode b where a.fglac = b.fid and fctlid_j ='" + fctlid + "'";
                DataTable chkvoudt = DBHelper.GetDataSet(chkvousql);
                if (chkvoudt.Rows.Count > 0) { cryRpt.Subreports["chkvou"].SetDataSource(chkvoudt); }

                string setvousql = "select a.*,case when a.finvset>=0 then finvset else 0 end as fdramt, " +
                        "case when a.finvset<0 then abs(finvset) else 0 end as fcramt from oacset a where fctlid_j ='" + fctlid + "'";
                DataTable setvoudt = DBHelper.GetDataSet(setvousql);
                cryRpt.Subreports["setvou"].SetDataSource(setvoudt);

                string adjvousql = "select	a.*, b.fdesc as fgldesc, "+
                        "case when a.famount>=0 then famount else 0 end as fdramt, " +
                        "case when a.famount<0 then abs(famount) else 0 end as fcramt " +
                        "from oacadj a," + Dbm + "mchgcode b where a.fglac = b.fid and fctlid_j ='" + fctlid + "'";
                DataTable adjvoudt = DBHelper.GetDataSet(adjvousql);
                if (adjvoudt.Rows.Count > 0) { cryRpt.Subreports["adjvou"].SetDataSource(adjvoudt); }
                

                string glvousql = "select a.*,b.fdesc,b.fnewgl as fglcode,b.fgldesc, "+
                        "case when a.famount>=0 then famount else 0 end as fdramt, " +
                        "case when a.famount<0 then abs(famount) else 0 end as fcramt " +
                        "from oactogl a," + Dbm + "mchgcode b where a.fglac = b.fid and fctlid_1 ='" + fctlid + "'";
                DataTable glvoudt = DBHelper.GetDataSet(glvousql);
                if (glvoudt.Rows.Count > 0) { cryRpt.Subreports["glvou"].SetDataSource(glvoudt); }
                
               
            }

            cryDocViewer temp_form = new cryDocViewer(cryRpt);
            temp_form.ShowDialog();
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void ppyset_Resize(object sender, EventArgs e)
        {
            if (WindowState == FormWindowState.Maximized)
            {
                //最大化时所需的操作 
                this.ClientSize = new System.Drawing.Size(687, 290);
                this.WindowState = FormWindowState.Normal;
            }
            else if (WindowState == FormWindowState.Minimized)
            {
                //最小化时所需的操作

                this.ClientSize = new System.Drawing.Size(50, 50);
                this.WindowState = FormWindowState.Normal;
            } 
        }
    }
}
