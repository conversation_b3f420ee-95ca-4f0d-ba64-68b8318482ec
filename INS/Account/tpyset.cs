using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;
using INS.INSClass;
using System.Collections;
using System.Configuration;
using INS.Business.Report;
using INS.Business;
using INS.Ctrl;
using INS.Account.ctrl;

namespace INS
{
    public partial class tpyset : Form
    {
        DBConnect operate = new DBConnect();
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public string fctlid_1 = "", fctlid = "", fposted = "";
        public String fpost = "", finpuser = "";
        public Boolean requery = false;
        public Boolean Addflag = false, Updateflag = false;
        public Boolean Skip_Validate = true;
        public decimal ln_fuamt = 0, ln_favset = 0;
        public DataTable mmain = new DataTable();

        acchk acchk = new acchk();
        acset acset = new acset();
        acadj acadj = new acadj();
        actogl actogl = new actogl();

        public tpyset()
        {
            InitializeComponent();
            mmain.Columns.Add("fuamt", typeof(decimal));
            mmain.Columns.Add("favset1", typeof(decimal));
            mmain.Columns.Add("favset2", typeof(decimal));
        }

        public void FillData(string order, string query, string list)
        {
            try
            {
                if (requery) { req.Visible = true; }
                string sql = "select a.ftype as [Type], a.fdocno as [Voucher No.],a.fothref as [Other Ref.],  CONVERT(varchar(10),a.fdocdate,102) as [Doc Date],  " +
                    "case when a.fposted=1 then 'Yes' else 'No' end as [Posted], fremark,fchkamt,fualamt,favset,fuamt, " +
                    "a.finpuser,a.finpdate, a.fupduser,a.fupddate,a.fcnfuser,a.fcnfdate,a.fctlid " +
                    "from oacjnl a " +
                    "where a.fmodule ='JV000' ";
                if (query != "") { sql = sql + query; }
                sql = sql + "order by case when fposted=1 then '1' else '2' end desc,fdocdate desc   ";

                vlist.DataSource = DBHelper.GetDataSet(sql);
                for (int i = 5; i < vlist.ColumnCount; i++)
                {
                    this.vlist.Columns[i].Visible = false;
                }
                vlist.CellFormatting +=
                new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.dataGridView1_CellFormatting);
            }
            catch { }
        }

        private void dataGridView1_CellFormatting(object sender, System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {

            // Set the background to red for negative values in the Balance column.
            if (vlist.Columns[e.ColumnIndex].Name.Equals("Voucher No."))
            {
                vlist.Columns[e.ColumnIndex].Width = 85;
            }
            if (vlist.Columns[e.ColumnIndex].Name.Equals("Doc Date"))
            {
                vlist.Columns[e.ColumnIndex].Width = 62;
            }
            if (vlist.Columns[e.ColumnIndex].Name.Equals("Posted"))
            {
                vlist.Columns[e.ColumnIndex].Width = 35;
            }
        }

        private void dataGridView1_CurrentCellChanged(object sender, EventArgs e)
        {
            if (vlist.CurrentCell != null)
            {
                int counter;
                counter = vlist.CurrentCell.RowIndex;
                mmain.Clear();
                fctlid = vlist.Rows[counter].Cells["fctlid"].Value.ToString().Trim();
                fposted = vlist.Rows[counter].Cells["Posted"].Value.ToString().Trim();
                ln_favset = Convert.ToDecimal(vlist.Rows[counter].Cells["favset"].Value.ToString().Trim());
                ln_fuamt = Convert.ToDecimal(vlist.Rows[counter].Cells["fuamt"].Value.ToString().Trim());
                mmain.Rows.Add(ln_fuamt, ln_favset);
                tabpage1reload();
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", "", "");
        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            FillData(comboBox1.Text.ToString() + "#", "", "");
        }

        private void tabControl1_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (Addflag == true || Updateflag == true)
            {
                if (tabControl1.SelectedIndex == 1)
                {
                    acchk.getMmain();
                    acchk.fadjamt = acadj.fiadjamt;
                    acchk.fsetamt = acset.fisetamt;
                    acchk.totalcontrol();
                }
                if (tabControl1.SelectedIndex == 2)
                {
                    acset.getMmain();
                    acset.fchkamt = acchk.fichkamt;
                    acset.fadjamt = acadj.fiadjamt;
                    acset.totalcontrol();
                }
                if (tabControl1.SelectedIndex == 3)
                {
                    acadj.getMmain();
                    acadj.fchkamt = acchk.fichkamt;
                    acadj.fsetamt = acset.fisetamt;
                    acadj.totalcontrol();
                }
            }
        }

        private void tabControl1_Selecting(object sender, TabControlCancelEventArgs e)
        {
            if (Addflag == true || Updateflag == true)
            {
                if (e.TabPage == list_set || e.TabPage == acgol)
                {
                    e.Cancel = true;
                }
            }
        }

        public Control buttonload(string flag)
        {
            btn temp_ctrlbutton = new btn();
            temp_ctrlbutton.fconfirm = fposted;
            temp_ctrlbutton.finpuser = finpuser.Trim();
            temp_ctrlbutton.sourceListRowCount = vlist.Rows.Count;
            if (flag == "Load") { temp_ctrlbutton.buttonload(""); }
            if (flag == "Add") { temp_ctrlbutton.buttoncontroladd(); }
            if (flag == "Back") { temp_ctrlbutton.buttoncontrolback(); }
            if (flag == "Mod") { temp_ctrlbutton.buttoncontrolupdate(); }
            if (flag == "Save") { temp_ctrlbutton.buttoncontrolsaveback(); }
            temp_ctrlbutton.UserControlButtonAddClicked += new EventHandler(add_Click);
            temp_ctrlbutton.UserControlButtonCancellClicked += new EventHandler(cancel_Click);
            temp_ctrlbutton.UserControlButtonDelClicked += new EventHandler(delete_Click);
            temp_ctrlbutton.UserControlButtonExitClicked += new EventHandler(exit_Click);
            temp_ctrlbutton.UserControlButtonModifyClicked += new EventHandler(modify_Click);
            temp_ctrlbutton.UserControlButtonSaveClicked += new EventHandler(save_Click);
            temp_ctrlbutton.UserControlButtonConfirmClicked += new EventHandler(confirm_Click);
            temp_ctrlbutton.UserControlButtonPrintClicked += new EventHandler(print_Click);
            temp_ctrlbutton.UserControlButtonImportClicked += new EventHandler(Import_Click);

            return temp_ctrlbutton;
        }

        public void buttoncontrolback()
        {
            Addflag = false;
            Updateflag = false;
        }

        void tabpage1reload()
        {
            panel9.Controls.Clear();
            panel9.Controls.Add(buttonload("Load"));
            tabPage2reload(fctlid);
            tabPage3reload(fctlid);
            tabPage4reload(fctlid);
            tabPage5reload(fctlid);
        }

        void tabPage2reload(string fctlid)
        {
            panel2.Controls.Clear();
            panel2.Controls.Add(buttonload("Load"));
            panel1.Controls.Clear();
            acchk.fctlid_j = fctlid;
            acchk.mmain = mmain;
            acchk.getMmain();
            acchk.buttoncontrolback();
            panel1.Controls.Add(acchk);
        }

        void tabPage3reload(string fctlid)
        {
            panel3.Controls.Clear();
            panel3.Controls.Add(buttonload("Load"));
            panel4.Controls.Clear();
            acset.fctlid_j = fctlid;
            acset.fposted = fposted;
            acset.mmain = mmain;
            acset.getMmain();
            acset.buttoncontrolback();
            panel4.Controls.Add(acset);
        }

        void tabPage4reload(string fctlid)
        {
            panel5.Controls.Clear();
            panel5.Controls.Add(buttonload("Load"));
            panel6.Controls.Clear();
            acadj.fctlid_j = fctlid;
            acadj.mmain = mmain;
            acadj.getMmain();
            acadj.buttoncontrolback();
            panel6.Controls.Add(acadj);
        }

        void tabPage5reload(string fctlid)
        {
            panel7.Controls.Clear();
            panel7.Controls.Add(buttonload("Load"));
            panel8.Controls.Clear();
            actogl.fctlid_j = fctlid;
            actogl.buttoncontrolback();
            panel8.Controls.Add(actogl);
        }

        public void buttoncontroladd()
        {
            Addflag = true;
            tabControl1.SelectedTab = acchk_set;
            tabpage2add();
            tabpage3add();
            tabpage4add();
        }

        void tabpage2add()
        {
            panel2.Controls.Clear();
            panel2.Controls.Add(buttonload("Add"));
            panel1.Controls.Clear();
            acchk.fctlid_j = fctlid;
            acchk.tabPage2add();
            panel1.Controls.Add(acchk);
        }

        void tabpage3add()
        {
            panel3.Controls.Clear();
            panel3.Controls.Add(buttonload("Add"));
            panel4.Controls.Clear();
            acset.fctlid_j = fctlid;
            acset.fposted = fposted;
            acset.tabPage3add();
            panel4.Controls.Add(acset);
        }

        void tabpage4add()
        {
            panel5.Controls.Clear();
            panel5.Controls.Add(buttonload("Add"));
            panel6.Controls.Clear();
            acadj.fctlid_j = fctlid;
            acadj.tabPage4add();
            panel6.Controls.Add(acadj);
        }

        public void buttoncontrolupdate()
        {
            Updateflag = true;
            tabControl1.SelectedTab = acchk_set;
            tabpage2update();
            tabpage3update();
            tabpage4update();
        }

        void tabpage2update()
        {
            panel2.Controls.Clear();
            panel2.Controls.Add(buttonload("Mod"));
            panel1.Controls.Clear();
            acchk.fctlid_j = fctlid;
            acchk.tabPage2update();
            panel1.Controls.Add(acchk);
        }

        void tabpage3update()
        {
            panel3.Controls.Clear();
            panel3.Controls.Add(buttonload("Mod"));
            panel4.Controls.Clear();
            acset.fctlid_j = fctlid;
            acset.fposted = fposted;
            acset.tabPage3update();
            panel4.Controls.Add(acset);
        }

        void tabpage4update()
        {
            panel5.Controls.Clear();
            panel5.Controls.Add(buttonload("Mod"));
            panel6.Controls.Clear();
            acadj.fctlid_j = fctlid;
            acadj.tabPage4update();
            panel6.Controls.Add(acadj);
        }

        private void req_Click(object sender, EventArgs e)
        {
            reqset tempform = new reqset(this);
            tempform.flag = "tpyset";
            tempform.ShowDialog();
        }

        private void Clauses_Enter(object sender, EventArgs e)
        {
            Skip_Validate = false;
        }

        private void Clauses_Leave(object sender, EventArgs e)
        {
            Skip_Validate = true;
        }

        void printCode()
        {
            ppyset tempform = new ppyset(this);
            tempform.fctlid = fctlid;
            tempform.p_calling = "ACVOUCHER";
            tempform.ShowDialog();
        }

        void delCode()
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really delete the information?", "Delete Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                string deldatestr = "delete from oacjnl where fctlid='" + fctlid + "'";
                string deldatestr1 = "delete from oacchk where fctlid_j='" + fctlid + "'";
                string deldatestr2 = "delete from oacadj where fctlid_j='" + fctlid + "'";
                string deldatestr3 = "delete from oacset where fctlid_j='" + fctlid + "'";
                string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();

                    SqlCommand command = connection.CreateCommand();
                    SqlTransaction transaction;

                    // Start a local transaction.
                    transaction = connection.BeginTransaction("SampleTransaction");

                    // Must assign both transaction object and connection 
                    // to Command object for a pending local transaction
                    command.Connection = connection;
                    command.Transaction = transaction;

                    try
                    {
                        command.CommandText = deldatestr;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr1;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr2;
                        command.ExecuteNonQuery();
                        command.CommandText = deldatestr3;
                        command.ExecuteNonQuery();
                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("Haven't Been Deleted", "Warning",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                FillData("", "", "");
                tabControl1.SelectedTab = acset_set;
            }
        }

        public Boolean Validation()
        {
            string ctrlName = "";
            ctrlName = acchk.u_validation();
            if (ctrlName != "")
            {
                tabControl1.SelectedIndex = 1;
                acchk.setControlFocus(ctrlName);
                return false;
            }
            ctrlName = acset.u_validation();
            if (ctrlName != "")
            {
                tabControl1.SelectedIndex = 2;
                acset.setControlFocus(ctrlName);
                return false;
            }
            ctrlName = acadj.u_validation();
            if (ctrlName != "")
            {
                tabControl1.SelectedIndex = 3;
                acadj.setControlFocus(ctrlName);
                return false;
            }
           
            return true;
        }

        void saveCode()
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you really Save this item?", "Updated Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                DateTime time = DateTime.Now;
                string ctrlName = "";
                if (Validation())
                {

                    acchk.fadjamt = acadj.totfadjamt;
                    acadj.fchkamt = acchk.totfchkamt;
                    acset.fchkamt = acchk.totfchkamt;
                    string result = "";
                    string[] sql1 = acchk.save();
                    string newfctlid = acchk.newfctlid;
                    string[] sql2 = acset.save(newfctlid);
                    string[] sql3 = acadj.save(newfctlid);

                    string connectionString = ConfigurationManager.ConnectionStrings["odata"].ConnectionString;
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        connection.Open();

                        SqlCommand command = connection.CreateCommand();
                        SqlTransaction transaction;

                        // Start a local transaction.
                        transaction = connection.BeginTransaction("SampleTransaction");

                        // Must assign both transaction object and connection 
                        // to Command object for a pending local transaction
                        command.Connection = connection;
                        command.Transaction = transaction;

                        try
                        {
                            if (sql1 != null)
                            {
                                for (int i = 0; i < sql1.Length; i++)
                                {
                                    if (sql1[i] != "" && sql1[i] != null)
                                    {
                                        command.CommandText = sql1[i];
                                        command.ExecuteNonQuery();
                                    }
                                }
                            }
                            if (sql2 != null)
                            {
                                for (int i = 0; i < sql2.Length; i++)
                                {
                                    if (sql2[i] != "" && sql2[i] != null)
                                    {
                                        command.CommandText = sql2[i];
                                        command.ExecuteNonQuery();
                                    }
                                }
                            }
                            if (sql3 != null)
                            {
                                for (int i = 0; i < sql3.Length; i++)
                                {
                                    if (sql3[i] != "" && sql3[i] != null)
                                    {
                                        command.CommandText = sql3[i];
                                        command.ExecuteNonQuery();
                                    }
                                }
                            }
                            transaction.Commit();
                            Addflag = false;
                            Updateflag = false;
                            FillData("", "", "");
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show("Haven't Been inserted!", "Warning",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                }
            }
        }

        private void tpyset_Resize(object sender, EventArgs e)
        {
            if (WindowState == FormWindowState.Maximized)
            {
                //最大化时所需的操作 
                this.ClientSize = new System.Drawing.Size(834, 645);
                this.WindowState = FormWindowState.Normal;
            }
            else if (WindowState == FormWindowState.Minimized)
            {
                //最小化时所需的操作

                this.ClientSize = new System.Drawing.Size(50, 50);
                this.WindowState = FormWindowState.Normal;
            }
        }

        private void add_Click(object sender, EventArgs e)
        {
            buttoncontroladd();
        }

        private void modify_Click(object sender, EventArgs e)
        {
            buttoncontrolupdate();
        }

        private void delete_Click(object sender, EventArgs e)
        {
            delCode();
        }

        private void save_Click(object sender, EventArgs e)
        {
            saveCode();
        }

        private void print_Click(object sender, EventArgs e)
        {
            printCode();
        }

        private void cancel_Click(object sender, EventArgs e)
        {
            buttoncontrolback();
            tabpage1reload();
        }

        private void exit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void confirm_Click(object sender, EventArgs e)
        {

        }

        private void Import_Click(object sender, EventArgs e)
        {

        }








    }
}

