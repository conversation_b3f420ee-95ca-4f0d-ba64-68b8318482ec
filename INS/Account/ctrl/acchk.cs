using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using INS.INSClass;
using System.Data.SqlClient;
using System.Collections;
using System.Configuration;

namespace INS.Account.ctrl
{
    public partial class acchk : UserControl
    {
        BindData[] u_dtypearr = new BindData[]  {
                                 new BindData("M","Misc."),
                                 new BindData("C","Client"),
                                 new BindData("P","Producer"),
                                 new BindData("B","R/I Broker"),
                                 new BindData("R","Reinsurer")};
        BindData[] u_statidarr = new BindData[]  {
                                   new BindData("D0100","Premium (Dir)"),
                                   new BindData("D0104","Premium (Out)"),
                                   new BindData("D0200","Claims (Dir)"),
                                   new BindData("D0204","Claims (Out)"),
                                   new BindData("R0100","Premium (In)"),
                                   new BindData("R0200","Claims (In)"),
                                   new BindData("G0000","Miscellaneous")};
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public Boolean Addflag = false, Updateflag = false;
        public string fctlid_j = "", favsetall = "", newfctlid = "";
        public Boolean Skip_Validate = true;
        private TextBox Cur_TextBox = null;
        private String Cur_StrBFedit = "";
        public DataTable mmain = new DataTable();
        public decimal fichkamt = 0, fadjamt = 0, fsetamt=0;

        public String CodeValue
        {
            set { fglac.Text = value; }
        }

        public decimal totfchkamt
        {
            get { return fichkamt; }
            set { fichkamt = value; }
        }

        public String CodeDesc
        {
            set { fglacdesc.Text = value; }
        }

        public String FdbtrValue
        {
            set { fdbtr.Text = value; }
        }

        public String FdbtrDesc
        {
            set { fdbtrdesc.Text = value; }
        }

        public void setControlFocus(string ctrlName)
        {
            if (ctrlName == "add2") { Controls["panel16"].Controls[ctrlName].Focus(); }
            else if (ctrlName == "fdbtr" || ctrlName == "fdbtrdesc") { Controls[ctrlName].Focus(); }
            //else { Controls["panel15"].Controls[ctrlName].Focus(); }
        }

        public acchk()
        {
            InitializeComponent();
            foreach (Control control in panel15.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
                if (control is TextBox)
                    control.Enter += new EventHandler(textBox_Enter);                //添加事件
            }
            fdbtrtype.DataSource = u_dtypearr;
            fdbtrtype.ValueMember = "ID";
            fdbtrtype.DisplayMember = "Item";
            fstatid.DataSource = u_statidarr;
            fstatid.ValueMember = "ID";
            fstatid.DisplayMember = "Item";
            mmain.Columns.Add("fuamt", typeof(decimal));
            mmain.Columns.Add("favset1", typeof(decimal));
            mmain.Columns.Add("favset2", typeof(decimal));
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            TextBox focusTextBox = null;

            if (e.KeyCode == Keys.Enter)
            {                    //判断用户是否按下回车键
                if (sender is TextBox)
                {
                    focusTextBox = (TextBox)sender;
                    if (!focusTextBox.AcceptsReturn)
                        SendKeys.Send("{TAB}");
                }
                else SendKeys.Send("{TAB}");
            }
        }

        private void textBox_Enter(object sender, EventArgs e)
        {
            Cur_TextBox = (TextBox)sender;
            Cur_StrBFedit = Cur_TextBox.Text;
        }

        public void buttoncontrolback() {
            Addflag = false;
            Updateflag = false;
            Cur_TextBox = null;
            add2.Visible = false;
            del2.Visible = false;
            foreach (Control ctrl in panel1.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = true;
                    ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (ctrl is MaskedTextBox)
                {
                    ((MaskedTextBox)ctrl).ReadOnly = true;
                    ((MaskedTextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
            }
            foreach (Control ctrl in panel15.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = true;
                    ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
            }
            tabPage2reload(fctlid_j);
        }

        public void tabPage2reload(string fctlid_j)
        {
            string sqlall = "select a.ftype, a.fdocno,a.fothref, a.fdocdate,  " +
                      "case when a.fposted=1 then 'Yes' else 'No' end as [Posted], fremark,fchkamt,fualamt,favset,fuamt, " +
                      "a.finpuser,a.finpdate, a.fupduser,a.fupddate,a.fcnfuser,a.fcnfdate,a.fctlid " +
                      "from oacjnl a " +
                      "where fctlid = '"+ fctlid_j +"'";
            DataTable dt = DBHelper.GetDataSet(sqlall);
            if (dt.Rows.Count > 0) {
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    fdocno.Text = Fct.stFat(dt.Rows[i]["fdocno"]);
                    string a = dt.Rows[i]["fdocdate"].ToString().Trim();
                    DateTime b = Convert.ToDateTime(a);
                    finvdate.Text = b.ToString("yyyy.MM.dd");
                    fothref.Text = Fct.stFat(dt.Rows[i]["fothref"]);
                    fremark.Text = Fct.stFat(dt.Rows[i]["fremark"]);
                    fchkamt.Text = Fct.sdFat(dt.Rows[i]["fchkamt"]).ToString();
                    fuamt.Text = Fct.sdFat(dt.Rows[i]["fuamt"]).ToString();
                    fualamt.Text = Fct.sdFat(dt.Rows[i]["fualamt"]).ToString();
                    favset.Text = Fct.sdFat(dt.Rows[i]["favset"]).ToString();
                    finpuser.Text = Fct.stFat(dt.Rows[i]["finpuser"]);
                    finpdate.Text = Fct.stFat(dt.Rows[i]["finpdate"]);
                    fupduser.Text = Fct.stFat(dt.Rows[i]["fupduser"]);
                    fupddate.Text = Fct.stFat(dt.Rows[i]["fupddate"]);
                    fcnfuser.Text = Fct.stFat(dt.Rows[i]["fcnfuser"]);
                    fcnfdate.Text = Fct.stFat(dt.Rows[i]["fcnfdate"]);
                    fposted.SelectedItem =Fct.stFat(dt.Rows[i]["Posted"]);
                    siCur.SelectedItem = "HK$";
                    siCur2.SelectedItem = "HK$";
                    siCur3.SelectedItem = "HK$";
                }
            }

            string sql = "SELECT a.flogseq,a.fglac,a.fdbtrtype,a.fdbtr,a.fchkref,a.famount,b.fdesc as fglacdesc,a.fdbtrdesc,a.fremark,a.fstatid,a.fstatno,a.fualamt,a.funrmrk,a.fctlid,0 as ofamount,0 as ofualamt FROM oacchk a " +
                "left join " + Dbm + "mchgcode b on a.fglac = b.fid where a.fctlid_j='" + fctlid_j + "' ";
            tacchk.DataSource = DBHelper.GetDataSet(sql);
            if (tacchk.RowCount >0)
            {
                for (int i = 6; i < tacchk.ColumnCount; i++)
                {
                    this.tacchk.Columns[i].Visible = false;
                }
                tacchk.CellFormatting +=
                new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.tacchk_CellFormatting);
            }
            else {
                DataTable dt1 = tacchk.DataSource as DataTable;
                dt1.Clear(); tacchk.DataSource = dt1;
                foreach (Control ctrl in panel15.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                }
                del2.Visible = false;
                tacchk.Columns["flogseq"].HeaderText = "Seq#";
                tacchk.Columns["fglac"].HeaderText = "Bank A/C";
                tacchk.Columns["fdbtrtype"].HeaderText = "Payee Type";
                tacchk.Columns["fdbtr"].HeaderText = "Payee#";
                tacchk.Columns["fchkref"].HeaderText = "Chq Ref#";
                tacchk.Columns["famount"].HeaderText = "Chq Amount";
            } 
        }

        public void tabPage2add() {
            Addflag = true;
            add2.Visible = true;
            foreach (Control ctrl in panel1.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                }
            }
            foreach (Control ctrl in panel15.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                }
            }
            foreach (Control ctrl in panel20.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                }
            }
            fctlid_j = ""; 
            fothref.ReadOnly = false;
            fothref.BackColor = System.Drawing.SystemColors.Window;
            fremark.ReadOnly = false;
            fremark.BackColor = System.Drawing.SystemColors.Window;
            DataTable dt = tacchk.DataSource as DataTable;
            dt.Clear(); tacchk.DataSource = dt;
            fposted.Text = "No";
            finvdate.Text = DateTime.Now.ToString("yyyy.MM.dd");
            mmain.Clear();
        }

        public void tabPage2update()
        {
            Updateflag = true;
            add2.Visible = true;
            del2.Visible = true;
            fstatid.Focus();
            fstatid.Enabled = true;
            fstatid.BackColor = System.Drawing.SystemColors.Window;
            fdbtrtype.Enabled = true;
            fdbtrtype.BackColor = System.Drawing.SystemColors.Window;
            fremark.ReadOnly = false;
            fremark.BackColor = System.Drawing.SystemColors.Window;
            fothref.ReadOnly = false;
            fothref.BackColor = System.Drawing.SystemColors.Window;
            foreach (Control ctrl in panel15.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = false;
                    ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                }
            }

            fglacdesc.ReadOnly = true;
            fglacdesc.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fstatno.ReadOnly = true;
            fstatno.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fglacBtn.Enabled = true;
        }


        private void tacchk_CellFormatting(object sender,
     System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            tacchk.Columns["flogseq"].HeaderText = "Seq#";
            tacchk.Columns["fglac"].HeaderText = "Bank A/C";
            tacchk.Columns["fdbtrtype"].HeaderText = "Payee Type";
            tacchk.Columns["fdbtr"].HeaderText = "Payee#";
            tacchk.Columns["fchkref"].HeaderText = "Chq Ref#";
            tacchk.Columns["famount"].HeaderText = "Chq Amount";
            tacchk.Columns["famount"].DefaultCellStyle.Format = "N2";
            tacchk.Columns["flogseq"].DefaultCellStyle.Format = "N0";
        }

        private void tacchk_CurrentCellChanged(object sender, EventArgs e)
        {
            if (tacchk.CurrentCell != null)
            {
                int counter;
                counter = tacchk.CurrentCell.RowIndex;
                if (tacchk.Rows[counter].Cells["flogseq"].Value != null)
                {
                    flogseq.Text = tacchk.Rows[counter].Cells["flogseq"].Value.ToString().Trim();
                }
                if (tacchk.Rows[counter].Cells["fglac"].Value != null)
                {
                    fglac.Text = tacchk.Rows[counter].Cells["fglac"].Value.ToString().Trim();
                }
                if (tacchk.Rows[counter].Cells["fglacdesc"].Value != null)
                {
                    fglacdesc.Text = tacchk.Rows[counter].Cells["fglacdesc"].Value.ToString().Trim();
                }
                if (tacchk.Rows[counter].Cells["fdbtrtype"].Value != null)
                {
                    fdbtrtype.SelectedValue = tacchk.Rows[counter].Cells["fdbtrtype"].Value.ToString().Trim();
                }
                if (tacchk.Rows[counter].Cells["fdbtr"].Value != null)
                {
                    fdbtr.Text = tacchk.Rows[counter].Cells["fdbtr"].Value.ToString().Trim();
                }
                if (tacchk.Rows[counter].Cells["fdbtrdesc"].Value != null)
                {
                    fdbtrdesc_m.Text = tacchk.Rows[counter].Cells["fdbtrdesc"].Value.ToString().Trim();
                    fdbtrdesc.Text = tacchk.Rows[counter].Cells["fdbtrdesc"].Value.ToString().Trim();
                }
                if (tacchk.Rows[counter].Cells["fchkref"].Value != null)
                {
                    fchkref.Text = tacchk.Rows[counter].Cells["fchkref"].Value.ToString().Trim();
                }
                if (tacchk.Rows[counter].Cells["famount"].Value != null)
                {
                    famount.Text = tacchk.Rows[counter].Cells["famount"].Value.ToString().Trim();
                }
                if (tacchk.Rows[counter].Cells["fremark"].Value != null)
                {
                    fremark_tacchk.Text = tacchk.Rows[counter].Cells["fremark"].Value.ToString().Trim();
                }
                if (tacchk.Rows[counter].Cells["ofamount"].Value != null)
                {
                    ofamount.Text = tacchk.Rows[counter].Cells["ofamount"].Value.ToString().Trim();
                }
                if (tacchk.Rows[counter].Cells["ofualamt"].Value != null)
                {
                    otacchk.Text = tacchk.Rows[counter].Cells["ofualamt"].Value.ToString().Trim();
                }
                if (tacchk.Rows[counter].Cells["fstatid"].Value != null)
                {
                    fstatid.SelectedValue = tacchk.Rows[counter].Cells["fstatid"].Value.ToString().Trim();
                }
                if (tacchk.Rows[counter].Cells["fstatno"].Value != null)
                {
                    fstatno.Text = tacchk.Rows[counter].Cells["fstatno"].Value.ToString().Trim();
                }
                if (tacchk.Rows[counter].Cells["fualamt"].Value != null)
                {
                    fualamt_tacchk.Text = tacchk.Rows[counter].Cells["fualamt"].Value.ToString().Trim();
                }
                if (tacchk.Rows[counter].Cells["funrmrk"].Value != null)
                {
                    funrmrk.Text = tacchk.Rows[counter].Cells["funrmrk"].Value.ToString().Trim();
                }
            }
        }

        public void btnctrlback(string flag)
        {
            Addflag = false;
            Updateflag = false;
            foreach (Control ctrl in panel1.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = true;
                    ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
                if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
            }
            foreach (Control ctrl in panel15.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = true;
                    ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
            }
            add2.Visible = false;
            del2.Visible = false;
        }

        private void flogseq_TextChanged(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tacchk.CurrentCell != null)
                {
                    tacchk["flogseq", tacchk.CurrentCell.RowIndex].Value = Fct.snFat(flogseq.Text.ToString());
                }
            }
        }

        private void fglac_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tacchk.CurrentCell != null)
                {
                    Cur_TextBox = (TextBox)sender;
                    string desc = "";
                    Boolean ll_ok = CheckEmptyString(Cur_TextBox);
                    if (ll_ok)
                    {
                        desc = CheckValue(fglac.Text.ToString());
                        fglacdesc.Text = desc;
                    }
                    if (!ll_ok || desc == null)
                    {
                        if (desc == null && !Skip_Validate)
                        {
                            fglacdesc.Text = "";
                            MessageBox.Show("Invalid Value");
                        }
                        fglac.Focus();
                        fglac.SelectAll();
                    }
                }
            }
        }

        private void fglac_TextChanged(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tacchk.CurrentCell != null)
                {
                    tacchk["fglac", tacchk.CurrentCell.RowIndex].Value = fglac.Text.ToString();
                }
            }
        }

        private void famount_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tacchk.CurrentCell != null)
                {
                    tacchk["famount", tacchk.CurrentCell.RowIndex].Value = Fct.sdFat(famount.Text);
                    tacchk["ofamount", tacchk.CurrentCell.RowIndex].Value = Fct.sdFat(Cur_StrBFedit);
                }
                totalcontrol();
            }
        }

        private void fdbtrtype_SelectedValueChanged(object sender, EventArgs e)
        {
                if (tacchk.CurrentCell != null && fdbtrtype.SelectedValue != null)
                {
                    if (fdbtrtype.SelectedValue.ToString() == "M")
                    {
                        fdbtrdesc_m.Visible = true;
                        fdbtr.Visible = false;
                        fdbtrBtn.Visible = false;
                        fdbtrdesc.Visible = false;
                    }
                    else
                    {
                        fdbtrdesc_m.Visible = false;
                        fdbtr.Visible = true;
                        fdbtrBtn.Visible = true;
                        fdbtrBtn.Enabled = true;
                        fdbtrdesc.Visible = true;
                    }
                    tacchk["fdbtrtype", tacchk.CurrentCell.RowIndex].Value = fdbtrtype.SelectedValue;
                }
        }

        private void fglacdesc_TextChanged(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tacchk.CurrentCell != null)
                {
                    tacchk["fglacdesc", tacchk.CurrentCell.RowIndex].Value = fglacdesc.Text.ToString();
                }
            }
        }

        private void fdbtr_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate && fdbtrtype.SelectedValue.ToString() != "M")
            {
                Cur_TextBox = (TextBox)sender;
                string desc = "";
                Boolean ll_ok = CheckEmptyString(Cur_TextBox);
                if (ll_ok)
                {
                    string[] result = FdbtrCheckValue(fdbtr.Text.ToString(), fdbtrtype.SelectedValue.ToString());
                    fdbtrdesc.Text = result[0];
                }
                if (!ll_ok || desc == null)
                {
                    if (desc == null && !Skip_Validate)
                    {
                        fdbtrdesc.Text = "";
                        MessageBox.Show("Invalid Value");
                    }
                    fdbtr.Focus();
                    fdbtr.SelectAll();
                }
            }
        }

        private void fdbtr_TextChanged(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tacchk.CurrentCell != null)
                {
                    tacchk["fdbtr", tacchk.CurrentCell.RowIndex].Value = fdbtr.Text.ToString();
                }
            }
        }

        private void fdbtrdesc_TextChanged(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tacchk.CurrentCell != null)
                {
                    tacchk["fdbtrdesc", tacchk.CurrentCell.RowIndex].Value = fdbtrdesc.Text.ToString();
                }
            }
        }

        private void fdbtrdesc_m_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tacchk.CurrentCell != null)
                {
                    tacchk["fdbtrdesc", tacchk.CurrentCell.RowIndex].Value = fdbtrdesc_m.Text.ToString();
                }
            }
        }

        private void fdbtrBtn_Click(object sender, EventArgs e)
        {
            if (fdbtrtype.SelectedValue.ToString() == "P")
            {
                frmProducerSearch tempform = new frmProducerSearch(this);
                tempform.flag = "acchk";
                tempform.FillData("", "");
                tempform.ShowDialog();
            }
            else if (fdbtrtype.SelectedValue.ToString() == "C")
            {
                frmClientSearch tempform = new frmClientSearch(this);
                tempform.ftype = "C";
                tempform.flag = "acchk";
                tempform.FillData("", "");
                tempform.ShowDialog();
            }
            else if (fdbtrtype.SelectedValue.ToString() == "R")
            {
                frmReinsurerSearch tempform = new frmReinsurerSearch(this);
                tempform.flag = "acchk";
                tempform.FillData("", "");
                tempform.ShowDialog();
            }
            else
            {
                frmBrokerSearch tempform = new frmBrokerSearch(this);
                tempform.flag = "acchk";
                tempform.FillData("", "");
                tempform.ShowDialog();
            }
        }

        private void fchkref_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tacchk.CurrentCell != null)
                {
                    tacchk["fchkref", tacchk.CurrentCell.RowIndex].Value = fchkref.Text.ToString();
                }
            }
        }

        private void fremark_tacchk_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tacchk.CurrentCell != null)
                {
                    tacchk["fremark", tacchk.CurrentCell.RowIndex].Value = fremark_tacchk.Text.ToString();
                }
            }
        }

        private void fstatid_SelectedIndexChanged(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tacchk.CurrentCell != null)
                {
                    tacchk["fstatid", tacchk.CurrentCell.RowIndex].Value = fstatid.SelectedValue;
                }
            }
        }

        private void fstatno_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tacchk.CurrentCell != null)
                {
                    tacchk["fstatno", tacchk.CurrentCell.RowIndex].Value = fstatno.Text.ToString();
                }
            }
        }

        private void fualamt_tacchk_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tacchk.CurrentCell != null)
                {
                    tacchk["fualamt", tacchk.CurrentCell.RowIndex].Value = Fct.sdFat(fualamt_tacchk.Text);
                    tacchk["ofualamt", tacchk.CurrentCell.RowIndex].Value = Fct.sdFat(Cur_StrBFedit);
                }
                totalcontrol();
            }
        }

        private void funrmrk_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tacchk.CurrentCell != null)
                {
                    tacchk["funrmrk", tacchk.CurrentCell.RowIndex].Value = funrmrk.Text.ToString();
                }
            }
        }

        public Boolean CheckEmptyString(Control text_obj)
        {
            if (Skip_Validate)
            {
                return true;
            }
            if (text_obj.Text.Trim() == "" || text_obj.Text.Trim() == "    .  .")
            {
                MessageBox.Show("Invalid Value");
                return false;
            }
            return true;
        }

        public string CheckValue(string fid)
        {
            string str = "select fid, fdesc from " + Dbm + "mchgcode where ftype='G' and fid='" + fid + "'";
            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["odata"].ConnectionString);
            SqlCommand cmd = new SqlCommand(str, con);
            con.Open();
            using (SqlDataReader sdr = cmd.ExecuteReader())
            {
                if (sdr.HasRows && sdr.Read())
                {
                    string fdesc = sdr["fdesc"].ToString().Trim();
                    sdr.Close();
                    return fdesc;
                }
                else { return null; }
            }
        }

        public string u_validation()
        {
            string ctrlName = "";
            //if (Fct.sdFat(fchkamt.Text)+Fct.sdFat(fualamt.Text)+Fct.sdFat(fadjamt.Text)+Fct.sdFat(fuamt.Text) == 0)
            //{
            //    MessageBox.Show("Invalid CR/DR Total!", "Warning",
            //        MessageBoxButtons.OK, MessageBoxIcon.Information);
            //    finvdate.Focus();
            //    return "finvdate";
            //}

            if (tacchk.RowCount == 0)
            {
                MessageBox.Show("Cheque record is not found!", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return "add2";
            }
            foreach (DataGridViewRow row in tacchk.Rows)
            {
                int SelectedIndex = tacchk.Rows.IndexOf(row);
                if (CheckValue(row.Cells["fglac"].Value.ToString()) == null)
                {
                    MessageBox.Show("Invalid valid!", "Warning",
                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    fglac.Focus();
                    tacchk.CurrentCell = tacchk.Rows[SelectedIndex].Cells["flogseq"];
                    return "fglac";
                }
                if (row.Cells["fdbtrtype"].Value.ToString() == "M" && row.Cells["fdbtrdesc"].Value.ToString()=="")
                {
                    MessageBox.Show("Invalid valid!", "Warning",
                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    fdbtrtype.Focus();
                    tacchk.CurrentCell = tacchk.Rows[SelectedIndex].Cells["flogseq"];
                    return "fdbtrtype";
                }
                if (row.Cells["fdbtrtype"].Value.ToString() != "M")
                {
                    string[] result = FdbtrCheckValue(row.Cells["fdbtr"].Value.ToString(), row.Cells["fdbtrtype"].Value.ToString());
                    if (result == null)
                    {
                        MessageBox.Show("Invalid valid!", "Warning",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        fdbtr.Focus();
                        tacchk.CurrentCell = tacchk.Rows[SelectedIndex].Cells["flogseq"];
                        return "fdbtr";
                    }
                }
                if (Fct.sdFat(row.Cells["famount"].Value) <= 0)
                {
                    MessageBox.Show("Invalid Value!", "Warning",
               MessageBoxButtons.OK, MessageBoxIcon.Information);
                    famount.Focus();
                    tacchk.CurrentCell = tacchk.Rows[SelectedIndex].Cells["flogseq"];
                    return "famount";
                }
            }
            return ctrlName;
        }

        public void totalcontrol()
        {
            Decimal famounts = 0, fualamts =0;
            Decimal famounto = 0, fualamto = 0, favseto = 0;
            Decimal favset1 = 0, favset2 = 0;
            if (mmain.Rows.Count > 0)
            {
              favset1 = Fct.sdFat(mmain.Rows[0]["favset1"]);
              favset2 = Fct.sdFat(mmain.Rows[0]["favset2"]);
            }
            mmain.Clear();
            for (int i = 0; i < tacchk.Rows.Count; i++)
            {
                if (tacchk["famount", i].Value.ToString() != "")
                {
                    famounts = famounts + Convert.ToDecimal(tacchk["famount", i].Value);
                }
                if (tacchk["fualamt", i].Value.ToString() != "")
                {
                    fualamts = fualamts + Convert.ToDecimal(tacchk["fualamt", i].Value);
                }
                if (tacchk["ofamount", i].Value.ToString() != "")
                {
                    famounto = famounto + Convert.ToDecimal(tacchk["ofamount", i].Value);
                }
                if (tacchk["ofualamt", i].Value.ToString() != "")
                {
                    fualamto = fualamto + Convert.ToDecimal(tacchk["ofualamt", i].Value);
                }
            }
            fichkamt = famounts + fualamts;
            fchkamt.Text = famounts.ToString("n2");
            fualamt.Text = fualamts.ToString("n2");
            favseto = -(famounto + fualamto);
            favset1 = favseto - (famounts + fualamts);
            favset.Text = (fichkamt + fadjamt).ToString("n2");
            fuamt.Text = fsetamt.ToString("n2");
        }

        public void getMmain()
        {
            if (mmain.Rows.Count > 0)
            {
                favset.Text = (Fct.sdFat(mmain.Rows[0]["favset1"]) + Fct.sdFat(mmain.Rows[0]["favset2"])).ToString();
                favset.Text = Convert.ToDecimal(favset.Text).ToString("n2");
                fuamt.Text = mmain.Rows[0]["fuamt"].ToString().Trim();
                fuamt.Text = Convert.ToDecimal(fuamt.Text).ToString("n2");
            }
        }

        public string[] FdbtrCheckValue(string fid, string flag)
        {
            string str = "select fid, fdesc,rtrim(fadd1)+rtrim(fadd2)+rtrim(fadd3)+rtrim(fadd4) as faddr from " + Dbm + "mprdr where ftype='" + flag + "' and fid='" + fid + "'";
            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["odata"].ConnectionString);
            SqlCommand cmd = new SqlCommand(str, con);
            con.Open();
            using (SqlDataReader sdr = cmd.ExecuteReader())
            {
                if (sdr.HasRows && sdr.Read())
                {
                    ArrayList result = new ArrayList();
                    result.Add(sdr["fdesc"].ToString().Trim());
                    result.Add(sdr["faddr"].ToString().Trim());
                    sdr.Close();
                    return (string[])result.ToArray(typeof(string));
                }
                else { return null; }
            }
        }

        private void add2_Click(object sender, EventArgs e)
        {
            decimal Row = 0;
            if (tacchk.Rows.Count != 0)
            {
                Row = Fct.sdFat(tacchk.Rows[tacchk.Rows.Count - 1].Cells["flogseq"].Value);
                del2.Visible = true;
            }
            else { Row = 0; del2.Visible = false; }

            DataTable DTtinvd = tacchk.DataSource as DataTable;
            DataRow newCustomersRow = DTtinvd.NewRow();
            newCustomersRow["flogseq"] = Row + 1;
            DTtinvd.Rows.InsertAt(newCustomersRow, tacchk.Rows.Count);
            tacchk.DataSource = DTtinvd;
            tacchk.CurrentCell = tacchk.Rows[tacchk.Rows.Count - 1].Cells["flogseq"];

            foreach (Control ctrl in panel15.Controls)
            {
                if (ctrl is TextBox)
                {
                    if (ctrl.Name != "flogseq") { ((TextBox)ctrl).Text = ""; }
                    ((TextBox)ctrl).ReadOnly = false;
                    ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                }
            }
            fstatid.Focus();
            fstatid.SelectedValue = "D0100";
            fstatid.Enabled = true;
            fstatid.BackColor = System.Drawing.SystemColors.Window;
            fdbtrtype.SelectedValue = "M";
            fdbtrtype.Enabled = true;
            fdbtrtype.BackColor = System.Drawing.SystemColors.Window;
            fglacdesc.ReadOnly = true;
            fglacdesc.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fstatno.ReadOnly = true;
            fstatno.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fglacBtn.Enabled = true;
            if (tacchk.Rows.Count != 0) { del2.Visible = true; }
        }

        private void del2_Click(object sender, EventArgs e)
        {
            if (tacchk.RowCount != 0)
            {
                try
                {
                    DataTable DTtinvd = tacchk.DataSource as DataTable;
                    DTtinvd.Rows.RemoveAt(tacchk.CurrentCell.RowIndex);
                    tacchk.DataSource = DTtinvd;
                    tacchk.CurrentCell = tacchk.Rows[tacchk.Rows.Count - 1].Cells["flogseq"];
                }
                catch { }
            }
            if (tacchk.RowCount == 0)
            {
                foreach (Control ctrl in panel15.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                }
                del2.Visible = false;
                fglacBtn.Enabled = false;
            }
        }


        public string[] save()
        {
            DateTime time = DateTime.Now;
            if (Addflag == true)
            {
                ArrayList addsql = new ArrayList();
                string user = InsEnvironment.LoginUser.GetUserCode();
                string fcinpname = InsEnvironment.LoginUser.GetChineseName();
                string date = time.ToString("yyyy-MM-dd");
                string fctlid_oacjnl = Fct.NewId("oacjnl");
                string fctlid_oacchk = Fct.NewId("oacchk");
                string fdoctype = "", ftype="";
                if (Fct.sdFat(fchkamt.Text) > 0) { ftype = "RV"; } else { ftype = "PV"; }
                if (Fct.sdFat(fchkamt.Text) == 0) { ftype = "AV"; }
                newfctlid = fctlid_oacjnl;
                string oacjnl_sql = "INSERT INTO [dbo].[oacjnl]([fctlid],[fmodule],[ftype],[fdocno],[fothref],[fdocdate],[fremark],[fcur],[frate], "+
                                    "[fchkamt],[fchkamt_b],[fadjamt],[fadjamt_b],[fualamt],[fualamt_b],[favset],[favset_b],[fuamt],[fuamt_b], "+
                                    "[finvset_b],[fexdiff_b],[frnddif_b],[faccdif_b],[fposted],[fautopick],[fctldel],[finpuser],[finpdate], "+
                                    "[fupduser],[fupddate],[fcnfuser],[fcnfdate],[fvouchid]) "+
                                    "VALUES('" + fctlid_oacjnl + "','JV000','" + ftype + "','" + fdocno.Text + "','" + fothref.Text + "',  " +
                                    "convert(datetime,'" + finvdate.Text.Trim() + "',102),'" + fremark.Text + "','HK$',1.0000,'" + Fct.sdFat(fchkamt.Text) + "',  " +
                                    "'" + Fct.sdFat(fchkamt.Text) + "','" + Fct.sdFat(fadjamt) + "','" + Fct.sdFat(fadjamt) + "','" + Fct.sdFat(fualamt.Text) + "',  " +
                                    "'"+ Fct.sdFat(fualamt.Text) +"','"+ Fct.sdFat(favset.Text) +"','"+ Fct.sdFat(favset.Text) +"','"+ Fct.sdFat(fuamt.Text) +"',  "+
                                    "'" + Fct.sdFat(fuamt.Text) + "',0,0,0,0,2,2,'','" + Fct.stFat(user) + "',  " +
                                    "'" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','" + Fct.stFat(user) + "',  " +
                                    "'" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','" + Fct.stFat(user) + "',  " +
                                    "'" + time.ToString("yyyy-MM-dd HH:mm:ss") + "','" + fothref.Text + "')";
                addsql.Add(oacjnl_sql);
                string sqloacjnl = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_oacjnl) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='oacjnl'";
                addsql.Add(sqloacjnl);

                DataTable dt = tacchk.DataSource as DataTable;
                if (dt != null && dt.Rows.Count > 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        if (i != 0) { fctlid_oacchk = (int.Parse(fctlid_oacchk) + 1).ToString().PadLeft(10, '0'); }
                        decimal famounts = Fct.sdFat(dt.Rows[i]["famount"]);
                        string insertsql = "INSERT INTO [dbo].[oacchk]([fctlid],[fctlid_j],[ftype],[flogseq],[fdocno],[fdocdate],[fglac],[fdbtrtype],[fdbtr], "+
                                            "[fdbtrdesc],[fchkref],[fcur],[frate],[famount],[famount_b],[fremark],[funrmrk],[fstatid],[fstatno],[fualamt],[fualamt_b])   "+
                                            "VALUES('" + fctlid_oacchk + "','" + fctlid_oacjnl + "','" + ftype + "','" + dt.Rows[i]["flogseq"] + "','','', " +
                                            "'" + dt.Rows[i]["fglac"] + "','" + dt.Rows[i]["fdbtrtype"] + "','" + dt.Rows[i]["fdbtr"] + "','" + dt.Rows[i]["fdbtrdesc"] + "','" + dt.Rows[i]["fchkref"] + "','HK$', " +
                                            "1.0000,'" + Fct.sdFat(dt.Rows[i]["famount"]) + "','" + Fct.sdFat(dt.Rows[i]["famount"]) + "','" + Fct.stFat(dt.Rows[i]["fremark"]) + "','" + Fct.stFat(dt.Rows[i]["funrmrk"]) + "', " +
                                            "'" + dt.Rows[i]["fstatid"] + "','" + dt.Rows[i]["fstatno"] + "','" + Fct.sdFat(dt.Rows[i]["fualamt"]) + "','" + Fct.sdFat(dt.Rows[i]["fualamt"]) + "')";
                        addsql.Add(insertsql);
                    }
                    string sqlfctlid = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_oacchk) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='oacchk'";
                    addsql.Add(sqlfctlid);
                }

                return (string[])addsql.ToArray(typeof(string));
            }
            else if (Updateflag == true)
            {
                ArrayList updatesql = new ArrayList();
                string user = InsEnvironment.LoginUser.GetUserCode();
                string date = time.ToString("yyyy-MM-dd");
                string ftype = "";
                if (Fct.sdFat(fchkamt.Text) > 0) { ftype = "RV"; } else { ftype = "PV"; }
                if (Fct.sdFat(fchkamt.Text) == 0) { ftype = "AV"; }
                string oginvh_sql = "UPDATE [dbo].[oacjnl] SET [ftype] = '" + ftype + "',[fdocno] = '" + fdocno.Text + "',[fothref] = '" + fothref.Text + "', " +
                                    "[fdocdate] = convert(datetime,'" + finvdate.Text.Trim() + "',102),[fremark] = '" + fremark.Text + "',[fchkamt] = '" + Fct.sdFat(fchkamt.Text) + "', " +
                                    "[fchkamt_b] = '" + Fct.sdFat(fchkamt.Text) + "',[fadjamt] = '" + Fct.sdFat(fadjamt) + "',[fadjamt_b] = '" + Fct.sdFat(fadjamt) + "', " +
                                    "[fualamt] = '" + Fct.sdFat(fualamt.Text) + "',[fualamt_b] = '" + Fct.sdFat(fualamt.Text) + "',[favset] = '" + Fct.sdFat(favset.Text) + "', " +
                                    "[favset_b] = '" + Fct.sdFat(favset.Text) + "',[fuamt] = '" + Fct.sdFat(fuamt.Text) + "',[fuamt_b] = '" + Fct.sdFat(fuamt.Text) + "', " +
                                    "[fupduser] = '" + Fct.stFat(user) + "', [fupddate] = '" + time.ToString("yyyy-MM-dd HH:mm:ss") + "' WHERE fctlid='" + fctlid_j + "'";
                updatesql.Add(oginvh_sql);
                string sql = "SELECT a.flogseq,a.fglac,a.fdbtrtype,a.fdbtr,a.fchkref,a.famount,b.fdesc as fglacdesc,a.fdbtrdesc,a.fremark,a.fstatid,a.fstatno,a.fualamt,a.funrmrk,a.fctlid,0 as ofamount,0 as ofualamt FROM oacchk a " +
                 "left join " + Dbm + "mchgcode b on a.fglac = b.fid where a.fctlid_j='" + fctlid_j + "' ";
                DataTable dtOrg = DBHelper.GetDataSet(sql);
                DataTable dt = tacchk.DataSource as DataTable;
                if (dt != null)
                {
                    updatesql.AddRange(CompareDt(dtOrg, dt, "fctlid"));
                }
                return (string[])updatesql.ToArray(typeof(string));
            }
            else { return null; }
        }

        public string[] CompareDt(DataTable dt1, DataTable dt2, string keyField)
        {
            //为三个表拷贝表结构
            DataTable dtRetDel = dt1.Clone();
            DataTable dtRetAdd = dt1.Clone();
            DataTable dtRetDif = dt1.Clone();

            ArrayList updsql = new ArrayList();
            int colCount = dt1.Columns.Count;

            DataView dv1 = dt1.DefaultView;
            DataView dv2 = dt2.DefaultView;

            //先以第一个表为参照，看第二个表是修改了还是删除了
            foreach (DataRowView dr1 in dv1)
            {
                dv2.RowFilter = keyField + " = '" + dr1[keyField].ToString() + "'";
                if (dv2.Count > 0)
                {
                    if (!CompareUpdate(dr1, dv2[0]))//比较是否有不同的
                    {
                        dtRetDif.Rows.Add(dv2[0].Row.ItemArray);//修改后
                        continue;
                    }
                }
                else
                {
                    //已经被删除的
                    dtRetDel.Rows.Add(dr1.Row.ItemArray);
                }
            }

            //以第一个表为参照，看记录是否是新增的
            dv2.RowFilter = "";//清空条件
            foreach (DataRowView dr2 in dv2)
            {
                dv1.RowFilter = keyField + " = '" + dr2[keyField].ToString() + "'";
                if (dv1.Count == 0)
                {
                    //新增的
                    dtRetAdd.Rows.Add(dr2.Row.ItemArray);
                }
            }

            if (dtRetAdd != null && dtRetAdd.Rows.Count > 0)
            {
                string fctlid_oacchk = Fct.NewId("oacchk");
                for (int i = 0; i < dtRetAdd.Rows.Count; i++)
                {
                    if (i != 0) { fctlid_oacchk = (int.Parse(fctlid_oacchk) + 1).ToString().PadLeft(10, '0'); }
                    decimal famounts = Fct.sdFat(dtRetAdd.Rows[i]["famount"]);
                    string ftype = "";
                    if (Fct.sdFat(fchkamt.Text) > 0) { ftype = "RV"; } else { ftype = "PV"; }
                    if (Fct.sdFat(fchkamt.Text) == 0) { ftype = "AV"; }
                    string insertsql = "INSERT INTO [dbo].[oacchk]([fctlid],[fctlid_j],[ftype],[flogseq],[fdocno],[fdocdate],[fglac],[fdbtrtype],[fdbtr], " +
                                             "[fdbtrdesc],[fchkref],[fcur],[frate],[famount],[famount_b],[fremark],[funrmrk],[fstatid],[fstatno],[fualamt],[fualamt_b])   " +
                                             "VALUES('" + fctlid_oacchk + "','" + dtRetAdd.Rows[i]["fctlid"] + "','" + ftype + "','" + dtRetAdd.Rows[i]["flogseq"] + "','" + dtRetAdd.Rows[i]["fdocno"] + "','" + dtRetAdd.Rows[i]["fdocdate"] + "', " +
                                             "'" + dtRetAdd.Rows[i]["fglac"] + "','" + dtRetAdd.Rows[i]["fdbtrtype"] + "','" + dtRetAdd.Rows[i]["fdbtr"] + "','" + dtRetAdd.Rows[i]["fdbtrdesc"] + "','" + dtRetAdd.Rows[i]["fchkref"] + "','HK$', " +
                                             "1.0000,'" + Fct.sdFat(dtRetAdd.Rows[i]["famount"]) + "','" + Fct.sdFat(dtRetAdd.Rows[i]["famount"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["fremark"]) + "','" + Fct.stFat(dtRetAdd.Rows[i]["funrmrk"]) + "', " +
                                             "'" + dtRetAdd.Rows[i]["fstatid"] + "','" + dtRetAdd.Rows[i]["fstatno"] + "','" + Fct.sdFat(dtRetAdd.Rows[i]["fualamt"]) + "','" + Fct.sdFat(dtRetAdd.Rows[i]["fualamt"]) + "')";
                    updsql.Add(insertsql);
                }
                string sqlfctlid = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_oacchk) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='oacchk'";
                updsql.Add(sqlfctlid);
            }
            if (dtRetDif != null && dtRetDif.Rows.Count > 0)
            {
                for (int i = 0; i < dtRetDif.Rows.Count; i++)
                {
                    decimal famounts = Fct.sdFat(dtRetDif.Rows[i]["famount"]);
                    string update = "UPDATE [dbo].[oacchk] SET [fdocno] = '',[fdocdate] = '',[fglac] = '" + dtRetDif.Rows[i]["fglac"] + "', " +
                                    "[fdbtrtype] = '" + dtRetDif.Rows[i]["fdbtrtype"] + "',[fdbtr] = '" + dtRetDif.Rows[i]["fdbtr"] + "',[fdbtrdesc] = '" + dtRetDif.Rows[i]["fdbtrdesc"] + "', " +
                                    "[fchkref] = '" + dtRetDif.Rows[i]["fchkref"] + "',[famount] = '" + Fct.sdFat(dtRetDif.Rows[i]["famount"]) + "', " +
                                    "[famount_b] = '" + Fct.sdFat(dtRetDif.Rows[i]["famount"]) + "',[fremark] = '" + Fct.stFat(dtRetDif.Rows[i]["fremark"]) + "',[funrmrk] = '" + Fct.stFat(dtRetDif.Rows[i]["funrmrk"]) + "', " +
                                    "[fstatid] = '" + dtRetDif.Rows[i]["fstatid"] + "',[fstatno] = '" + dtRetDif.Rows[i]["fstatno"] + "',[fualamt] = '" + Fct.sdFat(dtRetDif.Rows[i]["fualamt"]) + "', " +
                                    "[fualamt_b] = '" + Fct.sdFat(dtRetDif.Rows[i]["fualamt"]) + "' WHERE fctlid = '" + dtRetDif.Rows[i]["fctlid"].ToString().Trim() + "'";
                    updsql.Add(update);
                }
            }

            if (dtRetDel != null && dtRetDel.Rows.Count > 0)
            {
                for (int i = 0; i < dtRetDel.Rows.Count; i++)
                {
                    string del = "delete from oacchk where fctlid='" + dtRetDel.Rows[i]["fctlid"].ToString().Trim() + "'";
                    updsql.Add(del);
                }
            }

            return (string[])updsql.ToArray(typeof(string));
        }

        //比较是否有不同的
        private static bool CompareUpdate(DataRowView dr1, DataRowView dr2)
        {
            //行里只要有一项不一样，整个行就不一样,无需比较其它
            object val1;
            object val2;
            for (int i = 0; i < dr1.Row.ItemArray.Length; i++)
            {
                val1 = dr1[i];
                val2 = dr2[i];
                if (!val1.Equals(val2))
                {
                    return false;
                }
            }
            return true;
        }

        private void acchk_Enter(object sender, EventArgs e)
        {
            Skip_Validate = false;
        }

        private void acchk_Leave(object sender, EventArgs e)
        {
            Skip_Validate = true;
        }

        private void fglacBtn_Click(object sender, EventArgs e)
        {
            frmGlacSearch tempform = new frmGlacSearch(this);
            tempform.flag = "acchk";
            tempform.FillData("", "");
            tempform.ShowDialog();
        }

      

      

        

        

        

        
    }
}
