using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using INS.INSClass;
using System.Data.SqlClient;
using INS.Forms;
using System.Collections;

namespace INS.Account.ctrl
{
    public partial class acset : UserControl
    {
        BindData[] u_dtypearr = new BindData[]  {
                                 new BindData("M","Misc."),
                                 new BindData("C","Client"),
                                 new BindData("P","Producer"),
                                 new BindData("B","R/I Broker"),
                                 new BindData("R","Reinsurer")};
        BindData[] u_statidarr = new BindData[]  {
                                   new BindData("D0100","Premium (Dir)"),
                                   new BindData("D0104","Premium (Out)"),
                                   new BindData("D0200","Claims (Dir)"),
                                   new BindData("D0204","Claims (Out)"),
                                   new BindData("R0100","Premium (In)"),
                                   new BindData("R0200","Claims (In)"),
                                   new BindData("G0000","Miscellaneous")};
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public Boolean Addflag = false, Updateflag = false;
        public Boolean Skip_Validate = true;
        private TextBox Cur_TextBox = null;
        private String Cur_StrBFedit = "";
        public string fctlid_j = "", fposted = "";
        public string Dbo = InsEnvironment.DataBase.GetDbo();
        public DataTable mmain = new DataTable();
        public DataTable selstat = new DataTable();
        public decimal fisetamt = 0, fchkamt = 0, fadjamt=0;

        public String fuamtValue
        {
            get { return fuamt.Text; }
            set { fuamt.Text = value; }
        }

        public String favsetValue
        {
            get { return favset.Text; }
            set { favset.Text = value; }
        }

        public String FdbtrValue
        {
            set { fdbtr.Text = value; }
        }

        public String FdbtrDesc
        {
            set { fdbtrdesc.Text = value; }
        }

        public void setControlFocus(string ctrlName)
        {
            if (ctrlName == "add2") { Controls["panel16"].Controls[ctrlName].Focus(); }
            else if (ctrlName == "fdbtr" || ctrlName == "fdbtrdesc") { Controls[ctrlName].Focus(); }
            else { Controls["panel15"].Controls[ctrlName].Focus(); }
        }

        public acset()
        {
            InitializeComponent();
            mmain.Columns.Add("fuamt", typeof(decimal));
            mmain.Columns.Add("favset1", typeof(decimal));
            mmain.Columns.Add("favset2", typeof(decimal));
            foreach (Control control in panel1.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            foreach (Control control in panel3.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
                if (control is TextBox)
                    control.Enter += new EventHandler(textBox_Enter);                //添加事件
            }
            foreach (Control control in panel4.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
                if (control is TextBox)
                    control.Enter += new EventHandler(textBox_Enter);                //添加事件
            }
            foreach (Control control in panel5.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
                if (control is TextBox)
                    control.Enter += new EventHandler(textBox_Enter);                //添加事件
            }
            foreach (Control control in panel6.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
                if (control is TextBox)
                    control.Enter += new EventHandler(textBox_Enter);                //添加事件
            }
            fdbtrtype.DataSource = u_dtypearr;
            fdbtrtype.ValueMember = "ID";
            fdbtrtype.DisplayMember = "Item";
            fstatid.DataSource = u_statidarr;
            fstatid.ValueMember = "ID";
            fstatid.DisplayMember = "Item";
           
            selstat.Columns.Add("fobal_g", typeof(decimal));
            selstat.Columns.Add("fset_g", typeof(decimal));
            selstat.Columns.Add("fbal_g", typeof(decimal));
            selstat.Columns.Add("fobal_c", typeof(decimal));
            selstat.Columns.Add("fset_c", typeof(decimal));
            selstat.Columns.Add("fbal_c", typeof(decimal));
            selstat.Columns.Add("fobal_s", typeof(decimal));
            selstat.Columns.Add("fset_s", typeof(decimal));
            selstat.Columns.Add("fbal_s", typeof(decimal));
            selstat.Columns.Add("forgbal", typeof(decimal));
            selstat.Columns.Add("finvset", typeof(decimal));
            selstat.Columns.Add("fbalance", typeof(decimal));
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            TextBox focusTextBox = null;

            if (e.KeyCode == Keys.Enter)
            {                    //判断用户是否按下回车键
                if (sender is TextBox)
                {
                    focusTextBox = (TextBox)sender;
                    if (!focusTextBox.AcceptsReturn)
                        SendKeys.Send("{TAB}");
                }
                else SendKeys.Send("{TAB}");
            }
        }

        private void textBox_Enter(object sender, EventArgs e)
        {
            Cur_TextBox = (TextBox)sender;
            Cur_StrBFedit = Cur_TextBox.Text;
        }

        public void buttoncontrolback()
        {
            Addflag = false;
            Updateflag = false;
            Cur_TextBox = null;
            add.Visible = false;
            delete.Visible = false;
            foreach (Control ctrl in panel3.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = true;
                    ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (ctrl is MaskedTextBox)
                {
                    ((MaskedTextBox)ctrl).ReadOnly = true;
                    ((MaskedTextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
            }
            foreach (Control ctrl in panel4.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = true;
                    ((TextBox)ctrl).BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
            }
            foreach (Control ctrl in panel5.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = true;
                    ((TextBox)ctrl).BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
            }
            foreach (Control ctrl in panel6.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = true;
                    ((TextBox)ctrl).BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
            }
            foreach (Control ctrl in panel7.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = true;
                    ((TextBox)ctrl).BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
            }
            tabPage3reload(fctlid_j);
        }

        public void tabPage3reload(string fctlid_j)
        {
            SqlParameter[] param = new SqlParameter[] {
                    new SqlParameter("@fctlid_j", fctlid_j),
                    new SqlParameter("@fposted", fposted),};
            DataTable allrtninst = DBHelper.GetDataSetProd("tacset", Dbo, param);
            if (allrtninst.Rows.Count > 0)
            {
                tacset.DataSource = allrtninst;
                for (int i = 0; i < tacset.ColumnCount; i++)
                {
                    this.tacset.Columns[i].Visible = false;
                }
                tacset.Columns["flogseq"].Visible = true;
                tacset.Columns["fdbtrtype"].Visible = true;
                tacset.Columns["fdbtr"].Visible = true;
                tacset.Columns["fstatno"].Visible = true;
                tacset.Columns["forgbal"].Visible = true;
                tacset.Columns["finvset"].Visible = true;
                tacset.Columns["fbalance"].Visible = true;
                tacset.CellFormatting +=
                new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.tacset_CellFormatting);
                siCur.SelectedItem = "HK$";
            }
            else {
                tacset.DataSource = null;
                foreach (Control ctrl in panel3.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                }
                foreach (Control ctrl in panel4.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                }
                foreach (Control ctrl in panel5.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                }
                foreach (Control ctrl in panel6.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                }
                delete.Visible = false;
                for (int i = 0; i < tacset.ColumnCount; i++)
                {
                    this.tacset.Columns[i].Visible = false;
                }
                if (tacset.DataSource != null)
                {
                    tacset.Columns["flogseq"].Visible = true;
                    tacset.Columns["fdbtrtype"].Visible = true;
                    tacset.Columns["fdbtr"].Visible = true;
                    tacset.Columns["fstatno"].Visible = true;
                    tacset.Columns["forgbal"].Visible = true;
                    tacset.Columns["finvset"].Visible = true;
                    tacset.Columns["fbalance"].Visible = true;
                    tacset.Columns["flogseq"].HeaderText = "Seq#";
                    tacset.Columns["fdbtrtype"].HeaderText = "A/C Type";
                    tacset.Columns["fdbtr"].HeaderText = "A/C#";
                    tacset.Columns["fstatno"].HeaderText = "Statement#";
                    tacset.Columns["forgbal"].HeaderText = "Amount";
                    tacset.Columns["finvset"].HeaderText = "Settlement";
                    tacset.Columns["fbalance"].HeaderText = "Balance";
                }
                
            }
        }

        private void tacset_CellFormatting(object sender,
     System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            tacset.Columns["flogseq"].HeaderText = "Seq#";
            tacset.Columns["fdbtrtype"].HeaderText = "A/C Type";
            tacset.Columns["fdbtr"].HeaderText = "A/C#";
            tacset.Columns["fstatno"].HeaderText = "Statement#";
            tacset.Columns["forgbal"].HeaderText = "Amount";
            tacset.Columns["forgbal"].DefaultCellStyle.Format = "N2";
            tacset.Columns["finvset"].HeaderText = "Settlement";
            tacset.Columns["finvset"].DefaultCellStyle.Format = "N2";
            tacset.Columns["fbalance"].HeaderText = "Balance";
            tacset.Columns["fbalance"].DefaultCellStyle.Format = "N2";
            tacset.Columns["flogseq"].DefaultCellStyle.Format = "N0";
        }

        private void tacset_CurrentCellChanged(object sender, EventArgs e)
        {
            if (tacset.CurrentCell != null)
            {
                int counter;
                counter = tacset.CurrentCell.RowIndex;
                if (tacset.Rows[counter].Cells["flogseq"].Value != null)
                {
                    flogseq.Text = tacset.Rows[counter].Cells["flogseq"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fdbtrtype"].Value != null)
                {
                    fdbtrtype.SelectedValue = tacset.Rows[counter].Cells["fdbtrtype"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fdbtr"].Value != null)
                {
                    fdbtr.Text = tacset.Rows[counter].Cells["fdbtr"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fdbtrdesc"].Value != null)
                {
                    fdbtrdesc.Text = tacset.Rows[counter].Cells["fdbtrdesc"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fstatid"].Value != null)
                {
                    fstatid.SelectedValue = tacset.Rows[counter].Cells["fstatid"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fstatno"].Value != null)
                {
                    fstatno.Text = tacset.Rows[counter].Cells["fstatno"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fclmno"].Value != null)
                {
                    fclmno.Text = tacset.Rows[counter].Cells["fclmno"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fsrcref"].Value != null)
                {
                    fsrcref.Text = tacset.Rows[counter].Cells["fsrcref"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fcedeclm"].Value != null)
                {
                    fcedeclm.Text = tacset.Rows[counter].Cells["fcedeclm"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fsrcref"].Value != null)
                {
                    fsrcref2.Text = tacset.Rows[counter].Cells["fsrcref"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fcedepol"].Value != null)
                {
                    fcedepol.Text = tacset.Rows[counter].Cells["fcedepol"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fcedeendt"].Value != null)
                {
                    fcedeendt.Text = tacset.Rows[counter].Cells["fcedeendt"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fobal_g"].Value != null)
                {
                    fobal_g.Text = tacset.Rows[counter].Cells["fobal_g"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fset_g"].Value != null)
                {
                    fset_g.Text = tacset.Rows[counter].Cells["fset_g"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fbal_g"].Value != null)
                {
                    fbal_g.Text = tacset.Rows[counter].Cells["fbal_g"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fremark"].Value != null)
                {
                    fremark.Text = tacset.Rows[counter].Cells["fremark"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fobal_c"].Value != null)
                {
                    fobal_c.Text = tacset.Rows[counter].Cells["fobal_c"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fset_c"].Value != null)
                {
                    fset_c.Text = tacset.Rows[counter].Cells["fset_c"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fbal_c"].Value != null)
                {
                    fbal_c.Text = tacset.Rows[counter].Cells["fbal_c"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fcomrmrk"].Value != null)
                {
                    fcomrmrk.Text = tacset.Rows[counter].Cells["fcomrmrk"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fobal_s"].Value != null)
                {
                    fobal_s.Text = tacset.Rows[counter].Cells["fobal_s"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fset_s"].Value != null)
                {
                    fset_s.Text = tacset.Rows[counter].Cells["fset_s"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fbal_s"].Value != null)
                {
                    fbal_s.Text = tacset.Rows[counter].Cells["fbal_s"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fsusrmrk"].Value != null)
                {
                    fsusrmrk.Text = tacset.Rows[counter].Cells["fsusrmrk"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["forgbal"].Value != null)
                {
                    forgbal.Text = tacset.Rows[counter].Cells["forgbal"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["finvset"].Value != null)
                {
                    finvset.Text = tacset.Rows[counter].Cells["finvset"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fbalance"].Value != null)
                {
                    fbalance.Text = tacset.Rows[counter].Cells["fbalance"].Value.ToString().Trim();
                }
                if (tacset.Rows[counter].Cells["fadj"].Value != null)
                {
                    fadj.Text = tacset.Rows[counter].Cells["fadj"].Value.ToString().Trim();
                }
            }
        }

        public  void totalcontrol()
        {
            Decimal finvsets = 0;
            Decimal famounto = 0, favsetall = 0;
            Decimal favset1 = 0, favset2 = 0;
            if (mmain.Rows.Count > 0)
            {
                favset1 = Fct.sdFat(mmain.Rows[0]["favset1"]);
                favset2 = Fct.sdFat(mmain.Rows[0]["favset2"]);
            }
            mmain.Clear();
            for (int i = 0; i < tacset.Rows.Count; i++)
            {
                if (tacset["finvset", i].Value.ToString() != "")
                {
                    finvsets = finvsets + Convert.ToDecimal(tacset["finvset", i].Value);
                }
            }
            fisetamt = finvsets;
            fuamt.Text = finvsets.ToString("n2");
            favset.Text = (fchkamt + fadjamt).ToString("n2");
        }

        private void fdbtrBtn_Click(object sender, EventArgs e)
        {
            if (fdbtrtype.SelectedValue.ToString() == "P")
            {
                frmProducerSearch tempform = new frmProducerSearch(this);
                tempform.flag = "acset";
                tempform.FillData("", "");
                tempform.ShowDialog();
            }
            else if (fdbtrtype.SelectedValue.ToString() == "C")
            {
                frmClientSearch tempform = new frmClientSearch(this);
                tempform.ftype = "C";
                tempform.flag = "acset";
                tempform.FillData("", "");
                tempform.ShowDialog();
            }
            else if (fdbtrtype.SelectedValue.ToString() == "R")
            {
                frmReinsurerSearch tempform = new frmReinsurerSearch(this);
                tempform.flag = "acset";
                tempform.FillData("", "");
                tempform.ShowDialog();
            }
            else
            {
                frmBrokerSearch tempform = new frmBrokerSearch(this);
                tempform.flag = "acset";
                tempform.FillData("", "");
                tempform.ShowDialog();
            }
        }

        public void tabPage3add()
        {
            Addflag = true;
            add.Visible = true;
            foreach (Control ctrl in panel3.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                }
            }
            foreach (Control ctrl in panel4.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                }
            }
            foreach (Control ctrl in panel5.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                }
            }
            foreach (Control ctrl in panel6.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                }
            }
            foreach (Control ctrl in panel7.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                }
            }
            DataTable dt = tacset.DataSource as DataTable;
            dt.Clear(); tacset.DataSource = dt;
        }

        public void tabPage3update()
        {
            Updateflag = true;
            add.Visible = true;
            foreach (Control ctrl in panel3.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                }
            }
            DataTable dt = tacset.DataSource as DataTable;
            //dt.Clear(); tacset.DataSource = dt;
        }

        public void getMmain()
        {
            if (mmain.Rows.Count > 0)
            {
                favset.Text = (Fct.sdFat(mmain.Rows[0]["favset1"]) + Fct.sdFat(mmain.Rows[0]["favset2"])).ToString();
                favset.Text = Convert.ToDecimal(favset.Text).ToString("n2");
                fuamt.Text = mmain.Rows[0]["fuamt"].ToString().Trim();
                fuamt.Text = Convert.ToDecimal(fuamt.Text).ToString("n2");
            }
        }

        public string u_validation()
        {
            string ctrlName = "";
            foreach (DataGridViewRow row in tacset.Rows)
            {
                int SelectedIndex = tacset.Rows.IndexOf(row);

                if (Fct.sdFat(row.Cells["finvset"].Value) == 0 && Fct.sdFat(row.Cells["fadj"].Value) == 0)
                {
                    MessageBox.Show("Invalid Value!", "Warning",
               MessageBoxButtons.OK, MessageBoxIcon.Information);
                    finvset.Focus();
                    tacset.CurrentCell = tacset.Rows[SelectedIndex].Cells["flogseq"];
                    return "finvset";
                }
            }
            return ctrlName;
        }

        private void gen_Click(object sender, EventArgs e)
        {
            string lc_remark="", lc_comrmrk="", lc_susrmrk="", lc_text1, lc_text2="", lc_alias;

            lc_text1   = fstatno.Text;
            if (fclmno.Text !=""){
                lc_text2 = fpolno.Text + "/" + fclmno.Text;
                if (lc_text2 !=""){
                    lc_text2   = " ("+lc_text2+")";
                }
            }
            if (fpolno.Text !=""){
                lc_text2 = fpolno.Text + "/" + fendtno.Text;
                if (lc_text2 !=""){
                    lc_text2   = " ("+lc_text2+")";
                }
            }

            if (Fct.sdFat(fset_g.Text) != 0){
                if (Fct.sdFat(fset_g.Text)>0){
                    lc_remark  = "付"+lc_text1+lc_text2;
                }else{ lc_remark  = "收"+lc_text1+lc_text2;}
                fremark.Text = lc_remark;
            }
            if (Fct.sdFat(fset_c.Text) != 0){
                if (Fct.sdFat(fset_c.Text)>0){
                    lc_comrmrk  = "付"+lc_text1+lc_text2+"佣";
                }else{ lc_comrmrk  = "收"+lc_text1+lc_text2+"佣";}
                fcomrmrk.Text = lc_remark;
            }
            if (Fct.sdFat(fset_s.Text) != 0){
                lc_susrmrk="";
                fsusrmrk.Text = lc_susrmrk;
            }
        }

        private void add_Click(object sender, EventArgs e)
        {
            selstat tempform = new selstat(this);
            tempform.ShowDialog();
        }

        public void adddata(String fctlid)
        {
            decimal Row = 0;
            if (tacset.Rows.Count != 0)
            {
                Row = Fct.sdFat(tacset.Rows[tacset.Rows.Count - 1].Cells["flogseq"].Value);
                delete.Visible = true;
            }
            else { Row = 0; delete.Visible = false; }

            DataTable DTtacset = tacset.DataSource as DataTable;
            DataRow newCustomersRow = DTtacset.NewRow();
            newCustomersRow["flogseq"] = Row + 1;
            DTtacset.Rows.InsertAt(newCustomersRow, tacset.Rows.Count);
            tacset.DataSource = DTtacset;
            tacset.CurrentCell = tacset.Rows[tacset.Rows.Count - 1].Cells["flogseq"];

            flogseq.ReadOnly = false;
            flogseq.BackColor = System.Drawing.SystemColors.Window;
            fset_g.ReadOnly = false;
            fset_g.BackColor = System.Drawing.SystemColors.Window;
            fremark.ReadOnly = false;
            fremark.BackColor = System.Drawing.SystemColors.Window;
            fset_c.ReadOnly = false;
            fset_c.BackColor = System.Drawing.SystemColors.Window;
            fcomrmrk.ReadOnly = false;
            fcomrmrk.BackColor = System.Drawing.SystemColors.Window;
            fset_s.ReadOnly = false;
            fset_s.BackColor = System.Drawing.SystemColors.Window;
            fsusrmrk.ReadOnly = false;
            fsusrmrk.BackColor = System.Drawing.SystemColors.Window;
            fadj.ReadOnly = false;
            fadj.BackColor = System.Drawing.SystemColors.Window;
            if (tacset.Rows.Count != 0) { delete.Visible = true; }

            string sql = "select * from ostat where fctlid ='" + fctlid + "'";
            DataTable dt = DBHelper.GetDataSet(sql);
            fdbtrtype.SelectedValue = Fct.stFat(dt.Rows[0]["fdbtrtype"]);
            fdbtr.Text = Fct.stFat(dt.Rows[0]["fdbtr"]);
            fdbtrdesc.Text = Fct.stFat(dt.Rows[0]["fdbtrdesc"]);
            fstatid.SelectedValue = Fct.stFat(dt.Rows[0]["fstatid"]);
            fstatno.Text = Fct.stFat(dt.Rows[0]["frefno"]);
            fpolno.Text = Fct.stFat(dt.Rows[0]["fpolno"]);
            fclmno.Text = Fct.stFat(dt.Rows[0]["fclmno"]);
            fendtno.Text = Fct.stFat(dt.Rows[0]["fendtno"]);
            fcedeclm.Text = Fct.stFat(dt.Rows[0]["fcedeclm"]);
            fsrcref2.Text = Fct.stFat(dt.Rows[0]["fsrcref"]);
            fsrcref.Text = Fct.stFat(dt.Rows[0]["fsrcref"]);
            fcedepol.Text = Fct.stFat(dt.Rows[0]["fcedepol"]);
            fcedeendt.Text = Fct.stFat(dt.Rows[0]["fcedeendt"]);
            if (selstat.Rows.Count > 0)
            {
                fobal_g.Text = selstat.Rows[0]["fobal_g"].ToString().Trim();
                fset_g.Text = selstat.Rows[0]["fset_g"].ToString().Trim();
                fbal_g.Text = selstat.Rows[0]["fbal_g"].ToString().Trim();
                fobal_c.Text = selstat.Rows[0]["fobal_c"].ToString().Trim();
                fset_c.Text = selstat.Rows[0]["fset_c"].ToString().Trim();
                fbal_c.Text = selstat.Rows[0]["fbal_c"].ToString().Trim();
                fobal_s.Text = selstat.Rows[0]["fobal_s"].ToString().Trim();
                fset_s.Text = selstat.Rows[0]["fset_s"].ToString().Trim();
                fbal_s.Text = selstat.Rows[0]["fbal_s"].ToString().Trim();
                forgbal.Text = selstat.Rows[0]["forgbal"].ToString().Trim();
                finvset.Text = selstat.Rows[0]["finvset"].ToString().Trim();
                fbalance.Text = selstat.Rows[0]["fbalance"].ToString().Trim();
            }

            string lc_text1 = fstatno.Text.Trim(), lc_text2   = "",lc_remark  = "",lc_comrmrk ="",lc_susrmrk = "";

            if (fclmno.Text !="") {
                lc_text2 = "(" + fpolno.Text + "/" + fclmno.Text + ")";
            }
            
            if (fpolno.Text !="") {
                if (fendtno.Text !="") {lc_text2 = "(" + fpolno.Text + "/" + fendtno.Text + ")";}
                else {
                    lc_text2 = "(" + fpolno.Text + ")";
                }
            }

            if (Fct.sdFat(fset_g.Text) != 0){
                if (Fct.sdFat(fset_g.Text) > 0){
                    fremark.Text  = "付"+lc_text1+lc_text2;}
                else { fremark.Text  = "收"+lc_text1+lc_text2;}
            }
            if (Fct.sdFat(fset_c.Text) != 0){
                if (Fct.sdFat(fset_c.Text) > 0){
                    fcomrmrk.Text  = "付"+lc_text1+lc_text2+ "佣";}
                else { fcomrmrk.Text = "收" + lc_text1 + lc_text2 + "佣"; }
            }
            if (Fct.sdFat(fset_s.Text) != 0){
                fsusrmrk.Text ="冲暂收帐";
            }

            tacset["fctlid_s", tacset.CurrentCell.RowIndex].Value = fctlid;
            tacset["fdbtrtype", tacset.CurrentCell.RowIndex].Value = fdbtrtype.SelectedValue;
            tacset["fdbtr", tacset.CurrentCell.RowIndex].Value = fdbtr.Text;
            tacset["fdbtrdesc", tacset.CurrentCell.RowIndex].Value = fdbtrdesc.Text;
            tacset["fstatid", tacset.CurrentCell.RowIndex].Value = fstatid.SelectedValue;
            tacset["fstatno", tacset.CurrentCell.RowIndex].Value = fstatno.Text;
            tacset["fpolno", tacset.CurrentCell.RowIndex].Value = fpolno.Text;
            tacset["fclmno", tacset.CurrentCell.RowIndex].Value = fclmno.Text;
            tacset["fendtno", tacset.CurrentCell.RowIndex].Value = fendtno.Text;
            tacset["fcedeclm", tacset.CurrentCell.RowIndex].Value = fcedeclm.Text;
            tacset["fsrcref", tacset.CurrentCell.RowIndex].Value = fsrcref.Text;
            tacset["fcedepol", tacset.CurrentCell.RowIndex].Value = fcedepol.Text;
            tacset["fcedeendt", tacset.CurrentCell.RowIndex].Value = fcedeendt.Text;
            tacset["fobal_g", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(fobal_g.Text);
            tacset["fset_g", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(fset_g.Text);
            tacset["fbal_g", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(fbal_g.Text);
            tacset["fobal_c", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(fobal_c.Text);
            tacset["fset_c", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(fset_c.Text);
            tacset["fbal_c", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(fbal_c.Text);
            tacset["fobal_s", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(fobal_s.Text);
            tacset["fset_s", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(fset_s.Text);
            tacset["fbal_s", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(fbal_s.Text);
            tacset["forgbal", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(forgbal.Text);
            tacset["finvset", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(finvset.Text);
            tacset["fbalance", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(fbalance.Text);
            tacset["fremark", tacset.CurrentCell.RowIndex].Value = fremark.Text;
            tacset["fcomrmrk", tacset.CurrentCell.RowIndex].Value = fcomrmrk.Text;
            tacset["fsusrmrk", tacset.CurrentCell.RowIndex].Value = fsusrmrk.Text;
        }

        private void fset_g_Validated(object sender, EventArgs e)
        {
            set_Valid();
        }

        private void fset_c_Validated(object sender, EventArgs e)
        {
            set_Valid();
        }

        private void fset_s_Validated(object sender, EventArgs e)
        {
            set_Valid();
        }

        void set_Valid()
        {
            fbal_g.Text = Fct.stFat(Fct.sdFat(fobal_g.Text) + Fct.sdFat(fset_g.Text));
            fbal_c.Text = Fct.stFat(Fct.sdFat(fobal_c.Text) + Fct.sdFat(fset_c.Text));
            fbal_s.Text = Fct.stFat(Fct.sdFat(fobal_s.Text) + Fct.sdFat(fset_s.Text));
            finvset.Text = Fct.stFat(Fct.sdFat(fset_g.Text) + Fct.sdFat(fset_s.Text) + Fct.sdFat(fset_c.Text));
            fbalance.Text = Fct.stFat(Fct.sdFat(fbal_g.Text) + Fct.sdFat(fbal_s.Text) + Fct.sdFat(fbal_c.Text));
            tacset["fobal_g", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(fobal_g.Text);
            tacset["fset_g", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(fset_g.Text);
            tacset["fbal_g", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(fbal_g.Text);
            tacset["fobal_c", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(fobal_c.Text);
            tacset["fset_c", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(fset_c.Text);
            tacset["fbal_c", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(fbal_c.Text);
            tacset["fobal_s", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(fobal_s.Text);
            tacset["fset_s", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(fset_s.Text);
            tacset["fbal_s", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(fbal_s.Text);
            tacset["finvset", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(finvset.Text);
            tacset["fbalance", tacset.CurrentCell.RowIndex].Value = Fct.sdFat(fbalance.Text);
            totalcontrol();
        }

        private void delete_Click(object sender, EventArgs e)
        {
            if (tacset.RowCount != 0)
            {
                try
                {
                    DataTable DTtacset = tacset.DataSource as DataTable;
                    DTtacset.Rows.RemoveAt(tacset.CurrentCell.RowIndex);
                    tacset.DataSource = DTtacset;
                    tacset.CurrentCell = tacset.Rows[tacset.Rows.Count - 1].Cells["flogseq"];
                }
                catch { }
            }
            if (tacset.RowCount == 0)
            {
                foreach (Control ctrl in panel3.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                }
                foreach (Control ctrl in panel4.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                }
                foreach (Control ctrl in panel5.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                }
                foreach (Control ctrl in panel6.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                }
                delete.Visible = false;
            }
        }

        public string[] save(string newfctlid)
        {
            DateTime time = DateTime.Now;
            if (Addflag == true)
            {
                ArrayList addsql = new ArrayList();
                string user = InsEnvironment.LoginUser.GetUserCode();
                string fcinpname = InsEnvironment.LoginUser.GetChineseName();
                string date = time.ToString("yyyy-MM-dd");
                string fctlid_oacset = Fct.NewId("oacset");

                DataTable dt = tacset.DataSource as DataTable;
                if (dt != null && dt.Rows.Count > 0)
                {
                    string ftype = "";
                    if (Fct.sdFat(fchkamt) > 0) { ftype = "RV"; } else { ftype = "PV"; }
                    if (Fct.sdFat(fchkamt) == 0) { ftype = "AV"; }
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        if (i != 0) { fctlid_oacset = (int.Parse(fctlid_oacset) + 1).ToString().PadLeft(10, '0'); }
                        decimal famounts = Fct.sdFat(dt.Rows[i]["famount"]);
                        string insertsql = "INSERT INTO [dbo].[oacset]([fctlid],[fctlid_j],[flogseq],[ftype],[fdocno],[fdocdate],[fctlid_s],[fstatno],[fdbtrtype], "+
                                            "[fdbtr],[fdbtrdesc],[fcur],[frate],[famount],[famount_b],[fremark],[fcomrmrk],[fsusrmrk],[finvcur],[finvrate], "+
                                            "[finvset],[finvset_b],[fexdiff_b],[frnddif_b],[fset_g],[fset_c],[fset_s],[fadj],[forgbal],[forgbal_b],[fobal_g],[fobal_c],[fobal_s])    "+
                                            "VALUES('" + fctlid_oacset + "','" + newfctlid + "','" + dt.Rows[i]["flogseq"] + "','" + ftype + "','','', " +
                                            "'" + dt.Rows[i]["fctlid_s"] + "','" + dt.Rows[i]["fstatno"] + "','" + dt.Rows[i]["fdbtrtype"] + "','" + dt.Rows[i]["fdbtr"] + "','" + dt.Rows[i]["fdbtrdesc"] + "','HK$', " +
                                            "1.000,'" + Fct.sdFat(dt.Rows[i]["famount"]) + "','" + Fct.sdFat(dt.Rows[i]["famount"]) + "','" + dt.Rows[i]["fremark"] + "','" + dt.Rows[i]["fcomrmrk"] + "', " +
                                            "'" + dt.Rows[i]["fsusrmrk"] + "','HK$','1.0000','" + Fct.sdFat(dt.Rows[i]["finvset"]) + "','" + Fct.sdFat(dt.Rows[i]["finvset"]) + "', " +
                                            "0.0000,0.0000,'" + Fct.sdFat(dt.Rows[i]["fset_g"]) + "','" + Fct.sdFat(dt.Rows[i]["fset_c"]) + "','" + Fct.sdFat(dt.Rows[i]["fset_s"]) + "', " +
                                            "'" + Fct.sdFat(dt.Rows[i]["fadj"]) + "','" + Fct.sdFat(dt.Rows[i]["forgbal"]) + "','" + Fct.sdFat(dt.Rows[i]["forgbal"]) + "','" + Fct.sdFat(dt.Rows[i]["fobal_g"]) + "','" + Fct.sdFat(dt.Rows[i]["fobal_c"]) + "', " +
                                            "'" + Fct.sdFat(dt.Rows[i]["fobal_s"]) + "')";
                        addsql.Add(insertsql);
                    }
                    string sqlfctlid = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_oacset) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='oacset'";
                    addsql.Add(sqlfctlid);
                }

                return (string[])addsql.ToArray(typeof(string));
            }
            else if (Updateflag == true)
            {
                ArrayList updatesql = new ArrayList();
                string user = InsEnvironment.LoginUser.GetUserCode();
                string date = time.ToString("yyyy-MM-dd");
                SqlParameter[] param = new SqlParameter[] {
                    new SqlParameter("@fctlid_j", fctlid_j),
                    new SqlParameter("@fposted", fposted),};
                DataTable allrtninst = DBHelper.GetDataSetProd("tacset", Dbo, param);
                DataTable dt = tacset.DataSource as DataTable;
                if (dt != null)
                {
                    updatesql.AddRange(CompareDt(allrtninst, dt, "fctlid"));
                }
                return (string[])updatesql.ToArray(typeof(string));
            }
            else { return null; }
        }

        public string[] CompareDt(DataTable dt1, DataTable dt2, string keyField)
        {
            //为三个表拷贝表结构
            DataTable dtRetDel = dt1.Clone();
            DataTable dtRetAdd = dt1.Clone();
            DataTable dtRetDif = dt1.Clone();

            ArrayList updsql = new ArrayList();
            int colCount = dt1.Columns.Count;

            DataView dv1 = dt1.DefaultView;
            DataView dv2 = dt2.DefaultView;

            //先以第一个表为参照，看第二个表是修改了还是删除了
            foreach (DataRowView dr1 in dv1)
            {
                dv2.RowFilter = keyField + " = '" + dr1[keyField].ToString() + "'";
                if (dv2.Count > 0)
                {
                    if (!CompareUpdate(dr1, dv2[0]))//比较是否有不同的
                    {
                        dtRetDif.Rows.Add(dv2[0].Row.ItemArray);//修改后
                        continue;
                    }
                }
                else
                {
                    //已经被删除的
                    dtRetDel.Rows.Add(dr1.Row.ItemArray);
                }
            }

            //以第一个表为参照，看记录是否是新增的
            dv2.RowFilter = "";//清空条件
            foreach (DataRowView dr2 in dv2)
            {
                dv1.RowFilter = keyField + " = '" + dr2[keyField].ToString() + "'";
                if (dv1.Count == 0)
                {
                    //新增的
                    dtRetAdd.Rows.Add(dr2.Row.ItemArray);
                }
            }
            string ftype = "";
            if (Fct.sdFat(fchkamt) > 0) { ftype = "RV"; } else { ftype = "PV"; }
            if (Fct.sdFat(fchkamt) == 0) { ftype = "AV"; }

            if (dtRetAdd != null && dtRetAdd.Rows.Count > 0)
            {
                string fctlid_oacset = Fct.NewId("oacset");
                for (int i = 0; i < dtRetAdd.Rows.Count; i++)
                {
                    if (i != 0) { fctlid_oacset = (int.Parse(fctlid_oacset) + 1).ToString().PadLeft(10, '0'); }
                    string insertsql = "INSERT INTO [dbo].[oacset]([fctlid],[fctlid_j],[flogseq],[ftype],[fdocno],[fdocdate],[fctlid_s],[fstatno],[fdbtrtype], " +
                                            "[fdbtr],[fdbtrdesc],[fcur],[frate],[famount],[famount_b],[fremark],[fcomrmrk],[fsusrmrk],[finvcur],[finvrate], " +
                                            "[finvset],[finvset_b],[fexdiff_b],[frnddif_b],[fset_g],[fset_c],[fset_s],[fadj],[forgbal],[forgbal_b],[fobal_g],[fobal_c],[fobal_s])    " +
                                            "VALUES('" + fctlid_oacset + "','" + fctlid_j + "','" + dtRetAdd.Rows[i]["flogseq"] + "','" + ftype + "','" + dtRetAdd.Rows[i]["fdocno"] + "','" + dtRetAdd.Rows[i]["fdocdate"] + "', " +
                                            "'" + dtRetAdd.Rows[i]["fctlid_s"] + "','" + dtRetAdd.Rows[i]["fstatno"] + "','" + dtRetAdd.Rows[i]["fdbtrtype"] + "','" + dtRetAdd.Rows[i]["fdbtr"] + "','" + dtRetAdd.Rows[i]["fdbtrdesc"] + "','HK$', " +
                                            "1.000,'" + Fct.sdFat(dtRetAdd.Rows[i]["famount"]) + "','" + Fct.sdFat(dtRetAdd.Rows[i]["famount"]) + "','" + dtRetAdd.Rows[i]["fremark"] + "','" + dtRetAdd.Rows[i]["fcomrmrk"] + "', " +
                                            "'" + dtRetAdd.Rows[i]["fsusrmrk"] + "','HK$','1.0000','" + Fct.sdFat(dtRetAdd.Rows[i]["finvset"]) + "','" + Fct.sdFat(dtRetAdd.Rows[i]["finvset"]) + "', " +
                                            "0.0000,0.0000,'" + Fct.sdFat(dtRetAdd.Rows[i]["fset_g"]) + "','" + Fct.sdFat(dtRetAdd.Rows[i]["fset_c"]) + "','" + Fct.sdFat(dtRetAdd.Rows[i]["fset_s"]) + "', " +
                                            "'" + Fct.sdFat(dtRetAdd.Rows[i]["fadj"]) + "','" + Fct.sdFat(dtRetAdd.Rows[i]["forgbal"]) + "','" + Fct.sdFat(dtRetAdd.Rows[i]["forgbal"]) + "','" + Fct.sdFat(dtRetAdd.Rows[i]["fobal_g"]) + "','" + Fct.sdFat(dtRetAdd.Rows[i]["fobal_c"]) + "', " +
                                            "'" + Fct.sdFat(dtRetAdd.Rows[i]["fobal_s"]) + "')";
                    updsql.Add(insertsql);
                }
                string sqlfctlid = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_oacset) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='oacset'";
                updsql.Add(sqlfctlid);
            }
            if (dtRetDif != null && dtRetDif.Rows.Count > 0)
            {
                for (int i = 0; i < dtRetDif.Rows.Count; i++)
                {
                    string update = "UPDATE [dbo].[oacset]  SET [flogseq] ='" + dtRetDif.Rows[i]["flogseq"] + "',[ftype] = '" + ftype + "',[fdocno] = '" + dtRetDif.Rows[i]["fdocno"] + "', " +
                                    "[fdocdate] = '" + dtRetDif.Rows[i]["fdocdate"] + "',[fctlid_s] = '" + dtRetDif.Rows[i]["fctlid_s"] + "', " +
                                    "[fstatno] ='" + dtRetDif.Rows[i]["fstatno"] + "',[fdbtrtype] = '" + dtRetDif.Rows[i]["fdbtrtype"] + "',[fdbtr] = '" + dtRetDif.Rows[i]["fdbtr"] + "', " +
                                    "[fdbtrdesc] = '" + dtRetDif.Rows[i]["fdbtrdesc"] + "',[famount] ='" + Fct.sdFat(dtRetDif.Rows[i]["famount"]) + "',[famount_b] = '" + Fct.sdFat(dtRetDif.Rows[i]["famount"]) + "',[fremark] = '" + dtRetDif.Rows[i]["fremark"] + "', " +
                                    "[fcomrmrk] ='" + dtRetDif.Rows[i]["fcomrmrk"] + "',[fsusrmrk] = '" + dtRetDif.Rows[i]["fsusrmrk"] + "', " +
                                    "[finvset] = '" + Fct.sdFat(dtRetDif.Rows[i]["finvset"]) + "',[finvset_b] = '" + Fct.sdFat(dtRetDif.Rows[i]["finvset"]) + "', " +
                                    "[fset_g] = '" + Fct.sdFat(dtRetDif.Rows[i]["fset_g"]) + "',[fset_c] = '" + Fct.sdFat(dtRetDif.Rows[i]["fset_c"]) + "',[fset_s] = '" + Fct.sdFat(dtRetDif.Rows[i]["fset_s"]) + "', " +
                                    "[fadj] ='" + Fct.sdFat(dtRetDif.Rows[i]["fadj"]) + "',[forgbal] = '" + Fct.sdFat(dtRetDif.Rows[i]["forgbal"]) + "',[forgbal_b] = '" + Fct.sdFat(dtRetDif.Rows[i]["forgbal"]) + "', " +
                                    "[fobal_g] = '" + Fct.sdFat(dtRetDif.Rows[i]["fobal_g"]) + "',[fobal_c] = '" + Fct.sdFat(dtRetDif.Rows[i]["fobal_c"]) + "',[fobal_s] = '" + Fct.sdFat(dtRetDif.Rows[i]["fobal_s"]) + "'  " +
                                    "WHERE fctlid = '" + dtRetDif.Rows[i]["fctlid"] + "'";
                    updsql.Add(update);
                }
            }

            if (dtRetDel != null && dtRetDel.Rows.Count > 0)
            {
                for (int i = 0; i < dtRetDel.Rows.Count; i++)
                {
                    string del = "delete from oacset where fctlid='" + dtRetDel.Rows[i]["fctlid"].ToString().Trim() + "'";
                    updsql.Add(del);
                }
            }

            return (string[])updsql.ToArray(typeof(string));
        }

        //比较是否有不同的
        private static bool CompareUpdate(DataRowView dr1, DataRowView dr2)
        {
            //行里只要有一项不一样，整个行就不一样,无需比较其它
            object val1;
            object val2;
            for (int i = 0; i < dr1.Row.ItemArray.Length; i++)
            {
                val1 = dr1[i];
                val2 = dr2[i];
                if (!val1.Equals(val2))
                {
                    return false;
                }
            }
            return true;
        }

    }
}
