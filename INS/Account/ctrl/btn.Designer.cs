namespace INS.Ctrl
{
    partial class btn
    {
        /// <summary> 
        /// 設計工具所需的變數。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清除任何使用中的資源。
        /// </summary>
        /// <param name="disposing">如果應該處置 Managed 資源則為 true，否則為 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 元件設計工具產生的程式碼

        /// <summary> 
        /// 此為設計工具支援所需的方法 - 請勿使用程式碼編輯器
        /// 修改這個方法的內容。
        /// </summary>
        private void InitializeComponent()
        {
            this.panel23 = new System.Windows.Forms.Panel();
            this.AddPolicy = new System.Windows.Forms.Button();
            this.cancelpolicy = new System.Windows.Forms.Button();
            this.ModifyPolicy = new System.Windows.Forms.Button();
            this.savepolicy = new System.Windows.Forms.Button();
            this.DelPolicy = new System.Windows.Forms.Button();
            this.ConfirmPolicy = new System.Windows.Forms.Button();
            this.ExitPolicy = new System.Windows.Forms.Button();
            this.PrintPolicy = new System.Windows.Forms.Button();
            this.importpolicy = new System.Windows.Forms.Button();
            this.panel23.SuspendLayout();
            this.SuspendLayout();
            // 
            // panel23
            // 
            this.panel23.CausesValidation = false;
            this.panel23.Controls.Add(this.importpolicy);
            this.panel23.Controls.Add(this.AddPolicy);
            this.panel23.Controls.Add(this.cancelpolicy);
            this.panel23.Controls.Add(this.ModifyPolicy);
            this.panel23.Controls.Add(this.savepolicy);
            this.panel23.Controls.Add(this.DelPolicy);
            this.panel23.Controls.Add(this.ConfirmPolicy);
            this.panel23.Controls.Add(this.ExitPolicy);
            this.panel23.Controls.Add(this.PrintPolicy);
            this.panel23.Location = new System.Drawing.Point(0, 1);
            this.panel23.Name = "panel23";
            this.panel23.Size = new System.Drawing.Size(692, 36);
            this.panel23.TabIndex = 16;
            // 
            // AddPolicy
            // 
            this.AddPolicy.Location = new System.Drawing.Point(30, 8);
            this.AddPolicy.Name = "AddPolicy";
            this.AddPolicy.Size = new System.Drawing.Size(75, 21);
            this.AddPolicy.TabIndex = 4;
            this.AddPolicy.TabStop = false;
            this.AddPolicy.Text = "Add";
            this.AddPolicy.UseVisualStyleBackColor = true;
            this.AddPolicy.Click += new System.EventHandler(this.AddPolicy_Click);
            // 
            // cancelpolicy
            // 
            this.cancelpolicy.CausesValidation = false;
            this.cancelpolicy.Location = new System.Drawing.Point(563, 8);
            this.cancelpolicy.Name = "cancelpolicy";
            this.cancelpolicy.Size = new System.Drawing.Size(75, 21);
            this.cancelpolicy.TabIndex = 13;
            this.cancelpolicy.TabStop = false;
            this.cancelpolicy.Text = "Cancel";
            this.cancelpolicy.UseVisualStyleBackColor = true;
            this.cancelpolicy.Visible = false;
            this.cancelpolicy.Click += new System.EventHandler(this.cancelpolicy_Click);
            // 
            // ModifyPolicy
            // 
            this.ModifyPolicy.Location = new System.Drawing.Point(111, 8);
            this.ModifyPolicy.Name = "ModifyPolicy";
            this.ModifyPolicy.Size = new System.Drawing.Size(75, 21);
            this.ModifyPolicy.TabIndex = 5;
            this.ModifyPolicy.TabStop = false;
            this.ModifyPolicy.Text = "Modify";
            this.ModifyPolicy.UseVisualStyleBackColor = true;
            this.ModifyPolicy.Visible = false;
            this.ModifyPolicy.Click += new System.EventHandler(this.ModifyPolicy_Click);
            // 
            // savepolicy
            // 
            this.savepolicy.CausesValidation = false;
            this.savepolicy.Location = new System.Drawing.Point(484, 8);
            this.savepolicy.Name = "savepolicy";
            this.savepolicy.Size = new System.Drawing.Size(75, 21);
            this.savepolicy.TabIndex = 12;
            this.savepolicy.TabStop = false;
            this.savepolicy.Text = "Save";
            this.savepolicy.UseVisualStyleBackColor = true;
            this.savepolicy.Visible = false;
            this.savepolicy.Click += new System.EventHandler(this.savepolicy_Click);
            // 
            // DelPolicy
            // 
            this.DelPolicy.Location = new System.Drawing.Point(192, 8);
            this.DelPolicy.Name = "DelPolicy";
            this.DelPolicy.Size = new System.Drawing.Size(75, 21);
            this.DelPolicy.TabIndex = 6;
            this.DelPolicy.TabStop = false;
            this.DelPolicy.Text = "Delete";
            this.DelPolicy.UseVisualStyleBackColor = true;
            this.DelPolicy.Visible = false;
            this.DelPolicy.Click += new System.EventHandler(this.DelPolicy_Click);
            // 
            // ConfirmPolicy
            // 
            this.ConfirmPolicy.Location = new System.Drawing.Point(444, 8);
            this.ConfirmPolicy.Name = "ConfirmPolicy";
            this.ConfirmPolicy.Size = new System.Drawing.Size(75, 21);
            this.ConfirmPolicy.TabIndex = 7;
            this.ConfirmPolicy.TabStop = false;
            this.ConfirmPolicy.Text = "Confirm";
            this.ConfirmPolicy.UseVisualStyleBackColor = true;
            this.ConfirmPolicy.Visible = false;
            this.ConfirmPolicy.Click += new System.EventHandler(this.ConfirmPolicy_Click);
            // 
            // ExitPolicy
            // 
            this.ExitPolicy.Location = new System.Drawing.Point(596, 8);
            this.ExitPolicy.Name = "ExitPolicy";
            this.ExitPolicy.Size = new System.Drawing.Size(75, 21);
            this.ExitPolicy.TabIndex = 9;
            this.ExitPolicy.TabStop = false;
            this.ExitPolicy.Text = "Exit";
            this.ExitPolicy.UseVisualStyleBackColor = true;
            this.ExitPolicy.Click += new System.EventHandler(this.ExitPolicy_Click);
            // 
            // PrintPolicy
            // 
            this.PrintPolicy.Location = new System.Drawing.Point(520, 8);
            this.PrintPolicy.Name = "PrintPolicy";
            this.PrintPolicy.Size = new System.Drawing.Size(75, 21);
            this.PrintPolicy.TabIndex = 8;
            this.PrintPolicy.TabStop = false;
            this.PrintPolicy.Text = "Print";
            this.PrintPolicy.UseVisualStyleBackColor = true;
            this.PrintPolicy.Click += new System.EventHandler(this.PrintPolicy_Click);
            // 
            // importpolicy
            // 
            this.importpolicy.CausesValidation = false;
            this.importpolicy.Location = new System.Drawing.Point(367, 8);
            this.importpolicy.Name = "importpolicy";
            this.importpolicy.Size = new System.Drawing.Size(75, 21);
            this.importpolicy.TabIndex = 14;
            this.importpolicy.TabStop = false;
            this.importpolicy.Text = "Import";
            this.importpolicy.UseVisualStyleBackColor = true;
            this.importpolicy.Visible = false;
            this.importpolicy.Click += new System.EventHandler(this.importpolicy_Click);
            // 
            // btn
            // 
            this.AccessibleRole = System.Windows.Forms.AccessibleRole.WhiteSpace;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.panel23);
            this.Name = "btn";
            this.Size = new System.Drawing.Size(849, 40);
            this.panel23.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel23;
        private System.Windows.Forms.Button AddPolicy;
        private System.Windows.Forms.Button cancelpolicy;
        private System.Windows.Forms.Button ModifyPolicy;
        private System.Windows.Forms.Button savepolicy;
        private System.Windows.Forms.Button DelPolicy;
        private System.Windows.Forms.Button ConfirmPolicy;
        private System.Windows.Forms.Button ExitPolicy;
        private System.Windows.Forms.Button PrintPolicy;
        private System.Windows.Forms.Button importpolicy;
    }
}
