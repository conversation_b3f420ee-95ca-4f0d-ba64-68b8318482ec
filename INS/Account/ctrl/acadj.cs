using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using INS.INSClass;
using System.Data.SqlClient;
using System.Configuration;
using System.Collections;

namespace INS.Account.ctrl
{
    public partial class acadj : UserControl
    {
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public Boolean Addflag = false, Updateflag = false;
        public Boolean Skip_Validate = true;
        private TextBox Cur_TextBox = null;
        private String Cur_StrBFedit = "";
        public string fctlid_j = "";
        public DataTable mmain = new DataTable();
        public decimal fiadjamt = 0, fchkamt = 0, fsetamt = 0;

        public String CodeValue
        {
            set { fglac.Text = value; }
        }

        public String CodeDesc
        {
            set { fglacdesc.Text = value; }
        }

        public decimal totfadjamt
        {
            get { return fiadjamt; }
            set { fiadjamt = value; }
        }

        public void setControlFocus(string ctrlName)
        {
            if (ctrlName == "add2") { Controls["panel16"].Controls[ctrlName].Focus(); }
            else if (ctrlName == "fdbtr" || ctrlName == "fdbtrdesc") { Controls[ctrlName].Focus(); }
            else { Controls["panel15"].Controls[ctrlName].Focus(); }
        }

        public acadj()
        {
            InitializeComponent();
            mmain.Columns.Add("fuamt", typeof(decimal));
            mmain.Columns.Add("favset1", typeof(decimal));
            mmain.Columns.Add("favset2", typeof(decimal));
            foreach (Control control in panel1.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }
            foreach (Control control in panel9.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
                if (control is TextBox)
                    control.Enter += new EventHandler(textBox_Enter);                //添加事件
            }
        }

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            TextBox focusTextBox = null;

            if (e.KeyCode == Keys.Enter)
            {                    //判断用户是否按下回车键
                if (sender is TextBox)
                {
                    focusTextBox = (TextBox)sender;
                    if (!focusTextBox.AcceptsReturn)
                        SendKeys.Send("{TAB}");
                }
                else SendKeys.Send("{TAB}");
            }
        }

        private void textBox_Enter(object sender, EventArgs e)
        {
            Cur_TextBox = (TextBox)sender;
            Cur_StrBFedit = Cur_TextBox.Text;
        }

        public void buttoncontrolback()
        {
            Addflag = false;
            Updateflag = false;
            add.Visible = false;
            del.Visible = false;
            Cur_TextBox = null;
            foreach (Control ctrl in panel9.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = true;
                    ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (ctrl is MaskedTextBox)
                {
                    ((MaskedTextBox)ctrl).ReadOnly = true;
                    ((MaskedTextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
            }
            tabPage4reload(fctlid_j);
        }

        public void tabPage4reload(string fctlid_j)
        {
            string sql = "SELECT a.flogseq,a.fglac, a.famount, a.*,b.fdesc as fglacdesc,0 as ofamount FROM oacadj a " +
                  "left join " + Dbm + "mchgcode b on a.fglac = b.fid where a.fctlid_j='" + fctlid_j + "' ";
            tacadj.DataSource = DBHelper.GetDataSet(sql);
            if (tacadj.RowCount > 0)
            {
                for (int i = 3; i < tacadj.ColumnCount; i++)
                {
                    this.tacadj.Columns[i].Visible = false;
                }
                tacadj.CellFormatting +=
                new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.tacadj_CellFormatting);
                fcur2.Text = "HK$";
                fcur.Text = "HK$";
            }
            else {
                DataTable dt1 = tacadj.DataSource as DataTable;
                dt1.Clear(); tacadj.DataSource = dt1;
                foreach (Control ctrl in panel9.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                }
                del.Visible = false;
                for (int i = 3; i < tacadj.ColumnCount; i++)
                {
                    this.tacadj.Columns[i].Visible = false;
                }
                tacadj.Columns["flogseq"].HeaderText = "Seq#";
                tacadj.Columns["flogseq"].DefaultCellStyle.Format = "N0";
                tacadj.Columns["fglac"].HeaderText = "A/C";
                tacadj.Columns["famount"].HeaderText = "Adj Amount";
            }
        }

        public void btnctrlback(string flag)
        {
            Addflag = false;
            Updateflag = false;
            foreach (Control ctrl in panel9.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = true;
                    ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
                if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
            }
            add.Visible = false;
            del.Visible = false;
        }

        public void tabPage4add()
        {
            Addflag = true;
            add.Visible = true;
            foreach (Control ctrl in panel9.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                }
            } 
            foreach (Control ctrl in panel11.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).Text = "";
                }
            }
            DataTable dt = tacadj.DataSource as DataTable;
            dt.Clear(); tacadj.DataSource = dt;
        }

        public void tabPage4update()
        {
            Updateflag = true;
            add.Visible = true;
            del.Visible = true;
            foreach (Control ctrl in panel9.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = false;
                    ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                }
            }
            fglacdesc.ReadOnly = true;
            fglacdesc.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fglacBtn.Enabled = true;
        }

        private void tacadj_CellFormatting(object sender,
     System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            tacadj.Columns["flogseq"].HeaderText = "Seq#";
            tacadj.Columns["fglac"].HeaderText = "A/C";
            tacadj.Columns["famount"].HeaderText = "Adj Amount";
            tacadj.Columns["famount"].DefaultCellStyle.Format = "N2";
            tacadj.Columns["flogseq"].DefaultCellStyle.Format = "N0";
        }

        private void tacadj_CurrentCellChanged(object sender, EventArgs e)
        {
            if (tacadj.CurrentCell != null)
            {
                int counter;
                counter = tacadj.CurrentCell.RowIndex;
                if (tacadj.Rows[counter].Cells["flogseq"].Value != null)
                {
                    flogseq.Text = tacadj.Rows[counter].Cells["flogseq"].Value.ToString().Trim();
                }
                if (tacadj.Rows[counter].Cells["fglac"].Value != null)
                {
                    fglac.Text = tacadj.Rows[counter].Cells["fglac"].Value.ToString().Trim();
                }
                if (tacadj.Rows[counter].Cells["fglacdesc"].Value != null)
                {
                    fglacdesc.Text = tacadj.Rows[counter].Cells["fglacdesc"].Value.ToString().Trim();
                }
                if (tacadj.Rows[counter].Cells["famount"].Value != null)
                {
                    famount.Text = tacadj.Rows[counter].Cells["famount"].Value.ToString().Trim();
                }
                if (tacadj.Rows[counter].Cells["ofamount"].Value != null)
                {
                    ofamount.Text = tacadj.Rows[counter].Cells["ofamount"].Value.ToString().Trim();
                }
                if (tacadj.Rows[counter].Cells["fremark"].Value != null)
                {
                    fremark.Text = tacadj.Rows[counter].Cells["fremark"].Value.ToString().Trim();
                }
            }
        }

        private void add_Click(object sender, EventArgs e)
        {
            decimal Row = 0;
            if (tacadj.Rows.Count != 0)
            {
                Row = Fct.sdFat(tacadj.Rows[tacadj.Rows.Count - 1].Cells["flogseq"].Value);
                del.Visible = true;
            }
            else { Row = 0; del.Visible = false; }

            DataTable DTtacadj = tacadj.DataSource as DataTable;
            DataRow newCustomersRow = DTtacadj.NewRow();
            newCustomersRow["flogseq"] = Row + 1;
            DTtacadj.Rows.InsertAt(newCustomersRow, tacadj.Rows.Count);
            tacadj.DataSource = DTtacadj;
            tacadj.CurrentCell = tacadj.Rows[tacadj.Rows.Count - 1].Cells["flogseq"];

            foreach (Control ctrl in panel9.Controls)
            {
                if (ctrl is TextBox)
                {
                    if (ctrl.Name != "flogseq") { ((TextBox)ctrl).Text = ""; }
                    ((TextBox)ctrl).ReadOnly = false;
                    ((TextBox)ctrl).BackColor = System.Drawing.SystemColors.Window;
                }
            }
            fcur.Text = "HK$";
            fglacdesc.ReadOnly = true;
            fglacdesc.BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
            fglacBtn.Enabled = true;
            if (tacadj.Rows.Count != 0) { del.Visible = true; }
        }

        private void del_Click(object sender, EventArgs e)
        {
            if (tacadj.RowCount != 0)
            {
                try
                {
                    DataTable DTtacadj = tacadj.DataSource as DataTable;
                    DTtacadj.Rows.RemoveAt(tacadj.CurrentCell.RowIndex);
                    tacadj.DataSource = DTtacadj;
                    tacadj.CurrentCell = tacadj.Rows[tacadj.Rows.Count - 1].Cells["flogseq"];
                }
                catch { }
            }
            if (tacadj.RowCount == 0)
            {
                foreach (Control ctrl in panel9.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                }
                del.Visible = false;
                fglacBtn.Enabled = false;
            }
        }

        private void flogseq_TextChanged(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tacadj.CurrentCell != null)
                {
                    tacadj["flogseq", tacadj.CurrentCell.RowIndex].Value = Fct.snFat(flogseq.Text.ToString());
                }
            }
        }

        private void fglac_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tacadj.CurrentCell != null)
                {
                    Cur_TextBox = (TextBox)sender;
                    string desc = "";
                    Boolean ll_ok = CheckEmptyString(Cur_TextBox);
                    if (ll_ok)
                    {
                        desc = CheckValue(fglac.Text.ToString().Trim());
                        fglacdesc.Text = desc;
                    }
                    if (!ll_ok || desc == null)
                    {
                        if (desc == null && !Skip_Validate)
                        {
                            fglacdesc.Text = "";
                            MessageBox.Show("Invalid Value");
                        }
                        fglac.Focus();
                        fglac.SelectAll();
                    }
                }
            }
        }

        private void fglac_TextChanged(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tacadj.CurrentCell != null)
                {
                    tacadj["fglac", tacadj.CurrentCell.RowIndex].Value = fglac.Text.ToString();
                }
            }
        }

        private void famount_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tacadj.CurrentCell != null)
                {
                    tacadj["famount", tacadj.CurrentCell.RowIndex].Value = Fct.sdFat(famount.Text);
                    tacadj["ofamount", tacadj.CurrentCell.RowIndex].Value = Fct.sdFat(Cur_StrBFedit);    
                }
                totalcontrol();
            }
        }

        private void fremark_Validated(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tacadj.CurrentCell != null)
                {
                    tacadj["fremark", tacadj.CurrentCell.RowIndex].Value = fremark.Text.ToString();
                }
            }
        }

        private void fglacdesc_TextChanged(object sender, EventArgs e)
        {
            if ((Addflag == true || Updateflag == true) && !Skip_Validate)
            {
                if (tacadj.CurrentCell != null)
                {
                    tacadj["fglacdesc", tacadj.CurrentCell.RowIndex].Value = fglacdesc.Text.ToString();
                }
            }
        }

        public void totalcontrol()
        {
            Decimal famounts = 0;
            Decimal famounto = 0, favsetall = 0;
            Decimal favset1 = 0, favset2 = 0;
            if (mmain.Rows.Count > 0)
            {
                favset1 = Fct.sdFat(mmain.Rows[0]["favset1"]);
                favset2 = Fct.sdFat(mmain.Rows[0]["favset2"]);
            }
            mmain.Clear();
            for (int i = 0; i < tacadj.Rows.Count; i++)
            {
                if (tacadj["famount", i].Value.ToString() != "")
                {
                    famounts = famounts + Convert.ToDecimal(tacadj["famount", i].Value);
                }
                if (tacadj["ofamount", i].Value.ToString() != "")
                {
                    famounto = famounto + Convert.ToDecimal(tacadj["ofamount", i].Value);
                }
            }

            favset2 = favset2 + famounto - famounts;
            favset.Text = (fchkamt + famounts).ToString("n2");
            fiadjamt = famounts;
            fuamt.Text = fsetamt.ToString("n2");
        }

        public void getMmain()
        {
            if (mmain.Rows.Count > 0)
            {
                favset.Text = (Fct.sdFat(mmain.Rows[0]["favset1"]) + Fct.sdFat(mmain.Rows[0]["favset2"])).ToString();
                favset.Text = Convert.ToDecimal(favset.Text).ToString("n2");
                fuamt.Text = mmain.Rows[0]["fuamt"].ToString().Trim();
                fuamt.Text = Convert.ToDecimal(fuamt.Text).ToString("n2");
            }
        }

        public string u_validation()
        {
            string ctrlName = ""; decimal ln_totamt = 0;
            foreach (DataGridViewRow row in tacadj.Rows)
            {
                int SelectedIndex = tacadj.Rows.IndexOf(row);
                if (CheckValue(row.Cells["fglac"].Value.ToString()) == null)
                {
                    MessageBox.Show("Invalid valid!", "Warning",
                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    fglac.Focus();
                    tacadj.CurrentCell = tacadj.Rows[SelectedIndex].Cells["flogseq"];
                    return "fglac";
                }
                if (Fct.sdFat(row.Cells["famount"].Value) <= 0)
                {
                    MessageBox.Show("Invalid Value!", "Warning",
               MessageBoxButtons.OK, MessageBoxIcon.Information);
                    famount.Focus();
                    tacadj.CurrentCell = tacadj.Rows[SelectedIndex].Cells["flogseq"];
                    return "famount";
                }
                ln_totamt = ln_totamt + Fct.sdFat(row.Cells["famount"].Value);
            }
            if (ln_totamt ==0){
                MessageBox.Show("Invalid Adjustment Total!", "Warning",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
               famount.Focus();
               return "famount";
            }
            return ctrlName;
        }

        public Boolean CheckEmptyString(Control text_obj)
        {
            if (Skip_Validate)
            {
                return true;
            }
            if (text_obj.Text.Trim() == "" || text_obj.Text.Trim() == "    .  .")
            {
                MessageBox.Show("Invalid Value");
                return false;
            }
            return true;
        }

        public string CheckValue(string fid)
        {
            string str = "select fid, fdesc from " + Dbm + "mchgcode where ftype='G' and fid='" + fid + "'";
            SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["odata"].ConnectionString);
            SqlCommand cmd = new SqlCommand(str, con);
            con.Open();
            using (SqlDataReader sdr = cmd.ExecuteReader())
            {
                if (sdr.HasRows && sdr.Read())
                {
                    string fdesc = sdr["fdesc"].ToString().Trim();
                    sdr.Close();
                    return fdesc;
                }
                else { return null; }
            }
        }

        private void fglacBtn_Click(object sender, EventArgs e)
        {
            frmGlacSearch tempform = new frmGlacSearch(this);
            tempform.flag = "acadj";
            tempform.FillData("", "");
            tempform.ShowDialog();
        }

        private void acadj_Enter(object sender, EventArgs e)
        {
            Skip_Validate = false;
        }

        private void acadj_Leave(object sender, EventArgs e)
        {
            Skip_Validate = true;
        }

        public string[] save(string newfctlid)
        {
            DateTime time = DateTime.Now;
            if (Addflag == true)
            {
                ArrayList addsql = new ArrayList();
                string user = InsEnvironment.LoginUser.GetUserCode();
                string fcinpname = InsEnvironment.LoginUser.GetChineseName();
                string date = time.ToString("yyyy-MM-dd");
                string fctlid_oacadj = Fct.NewId("oacadj");

                DataTable dt = tacadj.DataSource as DataTable;
                if (dt != null && dt.Rows.Count > 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        if (i != 0) { fctlid_oacadj = (int.Parse(fctlid_oacadj) + 1).ToString().PadLeft(10, '0'); }
                        decimal famounts = Fct.sdFat(dt.Rows[i]["famount"]);
                        string insertsql = "INSERT INTO [dbo].[oacadj]([fctlid],[fctlid_j],[flogseq],[fdocno],[fdocdate],[fglac],[fcur],[frate],[famount],[famount_b],[fremark]) "+
                                "VALUES('" + fctlid_oacadj + "','" + newfctlid + "','" + dt.Rows[i]["flogseq"] + "','" + dt.Rows[i]["fdocno"] + "','" + dt.Rows[i]["fdocdate"] + "','" + dt.Rows[i]["fglac"] + "', " +
                                "'HK$','1.0000','" + dt.Rows[i]["famount"] + "','" + dt.Rows[i]["famount"] + "','" + dt.Rows[i]["fremark"] + "')";
                        addsql.Add(insertsql);
                    }
                    string sqlfctlid = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_oacadj) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='oacadj'";
                    addsql.Add(sqlfctlid);
                }

                return (string[])addsql.ToArray(typeof(string));
            }
            else if (Updateflag == true)
            {
                ArrayList updatesql = new ArrayList();
                string user = InsEnvironment.LoginUser.GetUserCode();
                string date = time.ToString("yyyy-MM-dd");
                string sql = "SELECT a.flogseq,a.fglac, a.famount, a.*,b.fdesc as fglacdesc,0 as ofamount FROM oacadj a " +
                  "left join " + Dbm + "mchgcode b on a.fglac = b.fid where a.fctlid_j='" + fctlid_j + "' ";
                DataTable dtOrg = DBHelper.GetDataSet(sql);
                DataTable dt = tacadj.DataSource as DataTable;
                if (dt != null)
                {
                    updatesql.AddRange(CompareDt(dtOrg, dt, "fctlid"));
                }
                return (string[])updatesql.ToArray(typeof(string));
            }
            else { return null; }
        }

        public string[] CompareDt(DataTable dt1, DataTable dt2, string keyField)
        {
            //为三个表拷贝表结构
            DataTable dtRetDel = dt1.Clone();
            DataTable dtRetAdd = dt1.Clone();
            DataTable dtRetDif = dt1.Clone();

            ArrayList updsql = new ArrayList();
            int colCount = dt1.Columns.Count;

            DataView dv1 = dt1.DefaultView;
            DataView dv2 = dt2.DefaultView;

            //先以第一个表为参照，看第二个表是修改了还是删除了
            foreach (DataRowView dr1 in dv1)
            {
                dv2.RowFilter = keyField + " = '" + dr1[keyField].ToString() + "'";
                if (dv2.Count > 0)
                {
                    if (!CompareUpdate(dr1, dv2[0]))//比较是否有不同的
                    {
                        dtRetDif.Rows.Add(dv2[0].Row.ItemArray);//修改后
                        continue;
                    }
                }
                else
                {
                    //已经被删除的
                    dtRetDel.Rows.Add(dr1.Row.ItemArray);
                }
            }

            //以第一个表为参照，看记录是否是新增的
            dv2.RowFilter = "";//清空条件
            foreach (DataRowView dr2 in dv2)
            {
                dv1.RowFilter = keyField + " = '" + dr2[keyField].ToString() + "'";
                if (dv1.Count == 0)
                {
                    //新增的
                    dtRetAdd.Rows.Add(dr2.Row.ItemArray);
                }
            }

            if (dtRetAdd != null && dtRetAdd.Rows.Count > 0)
            {
                string fctlid_oacadj = Fct.NewId("oacadj");
                for (int i = 0; i < dtRetAdd.Rows.Count; i++)
                {
                    if (i != 0) { fctlid_oacadj = (int.Parse(fctlid_oacadj) + 1).ToString().PadLeft(10, '0'); }
                    string insertsql = "INSERT INTO [dbo].[oacadj]([fctlid],[fctlid_j],[flogseq],[fdocno],[fdocdate],[fglac],[fcur],[frate],[famount],[famount_b],[fremark]) "+
                                "VALUES('" + fctlid_oacadj + "','" + fctlid_j + "','" + dtRetAdd.Rows[i]["flogseq"] + "','" + dtRetAdd.Rows[i]["fdocno"] + "','" + dtRetAdd.Rows[i]["fdocdate"] + "','" + dtRetAdd.Rows[i]["fglac"] + "', " +
                                "'HK$','1.0000','" + dtRetAdd.Rows[i]["famount"] + "','" + dtRetAdd.Rows[i]["famount"] + "','" + dtRetAdd.Rows[i]["fremark"] + "')";
                        
                    updsql.Add(insertsql);
                }
                string sqlfctlid = "update " + Dbm + "xsysparm set fnxtid='" + (int.Parse(fctlid_oacadj) + 1).ToString().PadLeft(10, '0') + "' where fidtype ='oacadj'";
                updsql.Add(sqlfctlid);
            }
            if (dtRetDif != null && dtRetDif.Rows.Count > 0)
            {
                for (int i = 0; i < dtRetDif.Rows.Count; i++)
                {
                    string update = "UPDATE [dbo].[oacadj]   SET [flogseq] = '" + dtRetDif.Rows[i]["flogseq"] + "',[fdocno] = '" + dtRetDif.Rows[i]["fdocno"] + "', " +
                            "[fdocdate] = '" + dtRetDif.Rows[i]["fdocdate"] + "',[fglac] = '" + dtRetDif.Rows[i]["fglac"] + "',[famount] = '" + dtRetDif.Rows[i]["famount"] + "', " +
                            "[famount_b] = '" + dtRetDif.Rows[i]["famount"] + "',[fremark] = '" + dtRetDif.Rows[i]["fremark"] + "' WHERE fctlid = '" + dtRetDif.Rows[i]["fctlid"] + "'";
                    updsql.Add(update);
                }
            }

            if (dtRetDel != null && dtRetDel.Rows.Count > 0)
            {
                for (int i = 0; i < dtRetDel.Rows.Count; i++)
                {
                    string del = "delete from oacadj where fctlid='" + dtRetDel.Rows[i]["fctlid"].ToString().Trim() + "'";
                    updsql.Add(del);
                }
            }

            return (string[])updsql.ToArray(typeof(string));
        }

        //比较是否有不同的
        private static bool CompareUpdate(DataRowView dr1, DataRowView dr2)
        {
            //行里只要有一项不一样，整个行就不一样,无需比较其它
            object val1;
            object val2;
            for (int i = 0; i < dr1.Row.ItemArray.Length; i++)
            {
                val1 = dr1[i];
                val2 = dr2[i];
                if (!val1.Equals(val2))
                {
                    return false;
                }
            }
            return true;
        }
    }
}
