using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using INS.Business;

namespace INS.Ctrl
{
    public partial class btn : UserControl
    {
        public Boolean Addflag = false;
        public Boolean updateflag = false;
        public string fconfirm = "", fconfirmCheck="",fclass ="";
        public string flags = "",finpuser;

        public Int32 sourceListRowCount;
        public Boolean ll_getno, ll_con;

        public btn()
        {
            InitializeComponent();
        }

        public void userRight() { 
            string p_fright = InsEnvironment.LoginUser.GetUserRight03();
            string g_fright3c  = InsEnvironment.LoginUser.GetUserRight3c();
            string g_user = InsEnvironment.LoginUser.GetUserCode();

            ll_getno = false;
            ll_con = false;

            if (p_fright.Substring (2,1)=="1"){
                if (AddPolicy.Visible == true) { AddPolicy.Visible = true; }
            }else {
                AddPolicy.Visible = false;
            }
            if (((finpuser == g_user && p_fright.Substring (3,1) == "1") || (finpuser != g_user && p_fright.Substring (4,1) == "1")) && sourceListRowCount !=0){
                if (ModifyPolicy.Visible == true) { ModifyPolicy.Visible = true; }
            }
            else {
                ModifyPolicy.Visible = false;
            }
            if (p_fright.Substring (1,1)=="1" && sourceListRowCount !=0){
                if (PrintPolicy.Visible == true) { PrintPolicy.Visible = true; }
            }else {
                PrintPolicy.Visible = false;
            }
            if (((finpuser == g_user && p_fright.Substring(7, 1) == "1") || (finpuser != g_user && p_fright.Substring(8, 1) == "1")) && sourceListRowCount != 0)
            {
                if (DelPolicy.Visible == true) { DelPolicy.Visible = true; }
            }else {
                 DelPolicy.Visible = false;
            }
            if ((finpuser == g_user && p_fright.Substring(3, 1) == "1") || (finpuser != g_user && p_fright.Substring(4, 1) == "1"))
            {
                ll_getno = true;
            }
            if ((finpuser == g_user && p_fright.Substring (5,1) == "1") || (finpuser != g_user && p_fright.Substring (6,1) == "1"))
            {
                ll_con = true;
            }
            if ((ll_getno == true || ll_con == true) && sourceListRowCount != 0)
            {
                if (ConfirmPolicy.Visible == true) { ConfirmPolicy.Visible = true; }
            }
            else {
                ConfirmPolicy.Visible = false;
            }

            return;
        }

        public void buttonload(string flag) {
            if (fconfirm == "No")
            {
                ModifyPolicy.Visible = true;
                DelPolicy.Visible = true;
                ConfirmPolicy.Visible = true;
            }
            else
            {
                ModifyPolicy.Visible = false;
                DelPolicy.Visible = false;
                ConfirmPolicy.Visible = false;
            }
            userRight();
        }

        public void buttoncontrolback()
        {
            savepolicy.Visible = false;
            cancelpolicy.Visible = false;
            AddPolicy.Visible = true;
            PrintPolicy.Visible = true;
            ExitPolicy.Visible = true;
            userRight();
        }
        public void buttoncontroladd() {
            savepolicy.Visible = true;
            cancelpolicy.Visible = true;
            AddPolicy.Visible = false;
            ModifyPolicy.Visible = false;
            DelPolicy.Visible = false;
            PrintPolicy.Visible = false;
            ExitPolicy.Visible = false;
            ConfirmPolicy.Visible = false;
            userRight();
        }
        public void buttoncontrolupdate() {
            savepolicy.Visible = true;
            cancelpolicy.Visible = true;
            AddPolicy.Visible = false;
            ModifyPolicy.Visible = false;
            DelPolicy.Visible = false;
            PrintPolicy.Visible = false;
            ExitPolicy.Visible = false;
            ConfirmPolicy.Visible = false;
            userRight();
        }
        public void buttoncontrolsaveback() {
            savepolicy.Visible = false;
            cancelpolicy.Visible = false;
            AddPolicy.Visible = true;
            ModifyPolicy.Visible = true;
            DelPolicy.Visible = true;
            PrintPolicy.Visible = true;
            ExitPolicy.Visible = true;
            ConfirmPolicy.Visible = true;
            userRight();
        }

        public event EventHandler UserControlButtonAddClicked;

        private void OnUserControlButtonAddClick()
        {
            if (UserControlButtonAddClicked != null)
            {
                UserControlButtonAddClicked(this, EventArgs.Empty);
            }
        }

        public void AddPolicy_Click(object sender, EventArgs e)
        {
            buttoncontroladd();
            OnUserControlButtonAddClick();
        }

        public event EventHandler UserControlButtonModifyClicked;

        private void OnUserControlButtonModifyClick()
        {
            if (UserControlButtonModifyClicked != null)
            {
                UserControlButtonModifyClicked(this, EventArgs.Empty);
            }
        }

        private void ModifyPolicy_Click(object sender, EventArgs e)
        {
            buttoncontrolupdate();
            OnUserControlButtonModifyClick();
        }

        public event EventHandler UserControlButtonDelClicked;

        private void OnUserControlButtonDelClick()
        {
            if (UserControlButtonDelClicked != null)
            {
                UserControlButtonDelClicked(this, EventArgs.Empty);
            }
        }

        private void DelPolicy_Click(object sender, EventArgs e)
        {
            OnUserControlButtonDelClick();
        }

        public event EventHandler UserControlButtonSaveClicked;

        private void OnUserControlButtonSaveClick()
        {
            if (UserControlButtonSaveClicked != null)
            {
                UserControlButtonSaveClicked(this, EventArgs.Empty);
            }
        }

        private void savepolicy_Click(object sender, EventArgs e)
        {
            OnUserControlButtonSaveClick();
        }

        public event EventHandler UserControlButtonCancellClicked;

        private void OnUserControlButtonCancellClick()
        {
            if (UserControlButtonCancellClicked != null)
            {
                UserControlButtonCancellClicked(this, EventArgs.Empty);
            }
        }

        private void cancelpolicy_Click(object sender, EventArgs e)
        {
            DialogResult myResult;
            myResult = MessageBox.Show("Are you sure you want to cancel without saving?", "Cancel Confirmation", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            if (myResult == DialogResult.OK)
            {
                buttoncontrolback();
                OnUserControlButtonCancellClick();
            }
        }

        public event EventHandler UserControlButtonExitClicked;

        private void OnUserControlButtonExitClick()
        {
            if (UserControlButtonExitClicked != null)
            {
                UserControlButtonExitClicked(this, EventArgs.Empty);
            }
        }

        private void ExitPolicy_Click(object sender, EventArgs e)
        {
            OnUserControlButtonExitClick();
        }
       
        public event EventHandler UserControlButtonConfirmClicked;

        private void UserControlButtonConfirmClick()
        {
            if (UserControlButtonConfirmClicked != null)
            {
                UserControlButtonConfirmClicked(this, EventArgs.Empty);
            }
        }

        private void ConfirmPolicy_Click(object sender, EventArgs e)
        {
            UserControlButtonConfirmClick();
        }

        public event EventHandler UserControlButtonPrintClicked;

        private void UserControlButtonPrintClick()
        {
            if (UserControlButtonPrintClicked != null)
            {
                UserControlButtonPrintClicked(this, EventArgs.Empty);
            }
        }

        private void PrintPolicy_Click(object sender, EventArgs e)
        {
            UserControlButtonPrintClick();
        }

        public event EventHandler UserControlButtonImportClicked;

        private void UserControlButtonImportClick()
        {
            if (UserControlButtonImportClicked != null)
            {
                UserControlButtonImportClicked(this, EventArgs.Empty);
            }
        }

        private void importpolicy_Click(object sender, EventArgs e)
        {
            UserControlButtonImportClick();
        }

    }
}
