using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using INS.INSClass;

namespace INS.Account.ctrl
{
    public partial class actogl : UserControl
    {
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        public string fctlid_j = "";
        public actogl()
        {
            InitializeComponent();
        }

        public void buttoncontrolback()
        {
            foreach (Control ctrl in panel17.Controls)
            {
                if (ctrl is TextBox)
                {
                    ((TextBox)ctrl).ReadOnly = true;
                    ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (ctrl is MaskedTextBox)
                {
                    ((MaskedTextBox)ctrl).ReadOnly = true;
                    ((MaskedTextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                }
                if (ctrl is ComboBox) { ((ComboBox)ctrl).Enabled = false; }
                if (ctrl is Button) { ((Button)ctrl).Enabled = false; }
            }
            tabPage5reload(fctlid_j);
        }

        public void tabPage5reload(string fctlid_j)
        {
            string sql = "select a.fglac, a.famount, a.*,b.fdesc as fglacdesc from oactogl a left join " + Dbm + "mchgcode b on a.fglac = b.fid where a.fctlid_1='" + fctlid_j + "' ";
            tactogl.DataSource = DBHelper.GetDataSet(sql);
            if (tactogl.RowCount > 0)
            {
                for (int i = 2; i < tactogl.ColumnCount; i++)
                {
                    this.tactogl.Columns[i].Visible = false;
                }
                tactogl.CellFormatting +=
                new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.tactogl_CellFormatting);
                siCur.SelectedItem = "HK$";
                siCur2.SelectedItem = "HK$";
            }
            else {
                DataTable dt = tactogl.DataSource as DataTable;
                dt.Clear(); tactogl.DataSource = dt;
                foreach (Control ctrl in panel17.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        ((TextBox)ctrl).Text = "";
                        ((TextBox)ctrl).ReadOnly = true;
                        ((TextBox)ctrl).BackColor =System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(185)))));
                    }
                }
            }
        }

        private void tactogl_CellFormatting(object sender,
     System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            tactogl.Columns["fglac"].HeaderText = "A/C#";
            tactogl.Columns["famount"].HeaderText = "Amount";
            tactogl.Columns["famount"].DefaultCellStyle.Format = "N2";
        }

        private void tactogl_CurrentCellChanged(object sender, EventArgs e)
        {
            if (tactogl.CurrentCell != null)
            {
                int counter;
                counter = tactogl.CurrentCell.RowIndex;
                if (tactogl.Rows[counter].Cells["fglac"].Value != null)
                {
                    fglac.Text = tactogl.Rows[counter].Cells["fglac"].Value.ToString().Trim();
                }
                if (tactogl.Rows[counter].Cells["fglacdesc"].Value != null)
                {
                    fglacdesc.Text = tactogl.Rows[counter].Cells["fglacdesc"].Value.ToString().Trim();
                }
                if (tactogl.Rows[counter].Cells["famount"].Value != null)
                {
                    famount.Text = tactogl.Rows[counter].Cells["famount"].Value.ToString().Trim();
                }
            }
        }


        
    }
}
