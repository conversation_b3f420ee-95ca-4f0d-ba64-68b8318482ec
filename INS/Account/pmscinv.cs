using CrystalDecisions.CrystalReports.Engine;
using CrystalDecisions.Shared;
using INS.Business.objRpt;
using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Web.Script.Serialization;
using System.Windows.Forms;

namespace INS.Business.Report
{
    public partial class pmscinv : Form
    {
        BindData[] u_fdoctype = new BindData[]  {
                                 new BindData("1", "DR/CR Note" ),
                                 new BindData("2", "憑 單")};
        //private string rpDirectory = Application.StartupPath.Replace("\\bin\\Debug", "\\objRpt");
        private string rpDirectory = "M:\\Software II\\New INS Runtime\\Setup\\objRpt";
        public string fctlid_1 = "", p_calling = "";
        private tginvh tginvh;
        public string finvno = "", fbilldate = "", fpayee = "", fcremark = "", yccamount = "", yamount = "", fcinpname = "", fpolno = "", fclass = "";
        public pmscinv()
        {
            InitializeComponent();
            fdoctype.DataSource = u_fdoctype;
            fdoctype.ValueMember = "ID";
            fdoctype.DisplayMember = "Item";
        }

        public pmscinv(tginvh tginvh)
        {
            // TODO: Complete member initialization
            this.tginvh = tginvh;
            InitializeComponent();
            fdoctype.DataSource = u_fdoctype;
            fdoctype.ValueMember = "ID";
            fdoctype.DisplayMember = "Item";
            fdoctype.SelectedValue = "2";
        }


        private void PS_Click(object sender, EventArgs e)
        {
            progressBar1.Visible = true;
            progressBar1.Maximum = 50;//设置最大长度值 
            progressBar1.Value = 0;//设置当前值 
            progressBar1.Step = 5;//设置没次增长多少 
            for (int i = 0; i < 10; i++)//循环 
            {
                System.Threading.Thread.Sleep(1000);//暂停1秒 
                progressBar1.Value += progressBar1.Step;
            }
            callCrPrmReg();
            this.Close();
        }

        void callCrPrmReg()
        {
            string fcpytype = "";
            string Dbo = InsEnvironment.DataBase.GetDbo();
            DataTable dtall = new DataTable();
            DataTable dt = new DataTable();
            DataTable dt1 = new DataTable();
            DataTable dt2 = new DataTable();
            DataTable dt3 = new DataTable();

            SqlParameter[] paramall = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };
            dtall = DBHelper.GetDataSetProd("MSC", Dbo, paramall);
            dtall.Clear();
            if (pcpy.Checked)
            {
                fcpytype = "P";
                SqlParameter[] param = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };
                dt = DBHelper.GetDataSetProd("MSC", Dbo, param);
            }
            if (acpy.Checked)
            {
                fcpytype = "A";
                SqlParameter[] param = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };
                dt1 = DBHelper.GetDataSetProd("MSC", Dbo, param);
            }
            if (icpy.Checked)
            {
                fcpytype = "I";
                SqlParameter[] param = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };

                dt2 = DBHelper.GetDataSetProd("MSC", Dbo, param);
            }
            //if (ccpy.Checked)
            //{
            //    fcpytype = "C";
            //    SqlParameter[] param = new SqlParameter[] {
            //            new SqlParameter("@fctlid_1", fctlid_1),
            //            new SqlParameter("@fcpytype", fcpytype)
            //    };

            //    dt3 = DBHelper.GetDataSetProd("MSC", Dbo, param);
            //}

            ReportDocument cryRpt = new ReportDocument();

            if (fdoctype.SelectedValue == "2")
            { cryRpt.Load(rpDirectory + "\\drac.rpt"); }
            else
            {
                cryRpt.Load(rpDirectory + "\\drcrmsc.rpt");
            }

            object[] obj = new object[dtall.Columns.Count];
            if (dt.Rows.Count > 0)
            {
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    dt.Rows[i].ItemArray.CopyTo(obj, 0);
                    dtall.Rows.Add(obj);
                }
            }
            if (dt1.Rows.Count > 0)
            {
                for (int i = 0; i < dt1.Rows.Count; i++)
                {
                    dt1.Rows[i].ItemArray.CopyTo(obj, 0);
                    dtall.Rows.Add(obj);
                }
            }
            if (dt2.Rows.Count > 0)
            {
                for (int i = 0; i < dt2.Rows.Count; i++)
                {
                    dt2.Rows[i].ItemArray.CopyTo(obj, 0);
                    dtall.Rows.Add(obj);
                }
            }
            if (dt3.Rows.Count > 0)
            {
                for (int i = 0; i < dt3.Rows.Count; i++)
                {
                    dt3.Rows[i].ItemArray.CopyTo(obj, 0);
                    dtall.Rows.Add(obj);
                }
            }

            if (fdoctype.SelectedValue == "1")
            {
                if (p_calling == "MSC")
                {
                    SqlParameter[] param = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                    };
                    DataTable INVD = DBHelper.GetDataSetProd("MSC2", Dbo, param);
                    cryRpt.Subreports["INVD"].SetDataSource(INVD);
                    cryRpt.Subreports["INVD2"].SetDataSource(INVD);
                }

            }
            else
            {
                string numStr = "";
                foreach (DataRow dr in dtall.Rows)
                {
                    numStr = NumGetString.NumGetStr(Convert.ToDouble(dr["yamount"]));
                    dr["yccamount"] = numStr;
                }
                cryRpt.DataDefinition.FormulaFields["z_yccamount"].Text = String.Format("'{0}'", numStr);

            }
            cryRpt.SetDataSource(dtall);
            cryDocViewer temp_form = new cryDocViewer(cryRpt);
            temp_form.ShowDialog();
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void pmscinv_Resize(object sender, EventArgs e)
        {
            if (WindowState == FormWindowState.Maximized)
            {
                //最大化时所需的操作 
                this.ClientSize = new System.Drawing.Size(687, 363);
                this.WindowState = FormWindowState.Normal;
            }
            else if (WindowState == FormWindowState.Minimized)
            {
                //最小化时所需的操作

                this.ClientSize = new System.Drawing.Size(50, 50);
                this.WindowState = FormWindowState.Normal;
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            if (fdoctype.SelectedValue == "2")
            {
                progressBar1.Visible = true;
                progressBar1.Maximum = 50;//设置最大长度值 
                progressBar1.Value = 0;//设置当前值 
                progressBar1.Step = 5;//设置没次增长多少 
                for (int i = 0; i < 10; i++)//循环 
                {
                    System.Threading.Thread.Sleep(100);//暂停1秒 
                    progressBar1.Value += progressBar1.Step;
                }

                string fcpytype = "";
                string Dbo = InsEnvironment.DataBase.GetDbo();
                DataTable dtall = new DataTable();
                DataTable dt = new DataTable();
                DataTable dt1 = new DataTable();
                DataTable dt2 = new DataTable();
                DataTable dt3 = new DataTable();

                SqlParameter[] paramall = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };
                dtall = DBHelper.GetDataSetProd("MSC", Dbo, paramall);
                dtall.Clear();
                if (pcpy.Checked)
                {
                    fcpytype = "P";
                    SqlParameter[] param = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };
                    dt = DBHelper.GetDataSetProd("MSC", Dbo, param);
                }
                if (acpy.Checked)
                {
                    fcpytype = "A";
                    SqlParameter[] param = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };
                    dt1 = DBHelper.GetDataSetProd("MSC", Dbo, param);
                }
                if (icpy.Checked)
                {
                    fcpytype = "I";
                    SqlParameter[] param = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };

                    dt2 = DBHelper.GetDataSetProd("MSC", Dbo, param);
                }

                ReportDocument cryRpt = new ReportDocument();

                if (fdoctype.SelectedValue == "2")
                { cryRpt.Load(rpDirectory + "\\drac.rpt"); }
                else
                {
                    cryRpt.Load(rpDirectory + "\\drcrmsc.rpt");
                }

                object[] obj = new object[dtall.Columns.Count];
                if (dt.Rows.Count > 0)
                {
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        dt.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt1.Rows.Count > 0)
                {
                    for (int i = 0; i < dt1.Rows.Count; i++)
                    {
                        dt1.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt2.Rows.Count > 0)
                {
                    for (int i = 0; i < dt2.Rows.Count; i++)
                    {
                        dt2.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }
                if (dt3.Rows.Count > 0)
                {
                    for (int i = 0; i < dt3.Rows.Count; i++)
                    {
                        dt3.Rows[i].ItemArray.CopyTo(obj, 0);
                        dtall.Rows.Add(obj);
                    }
                }

                if (fdoctype.SelectedValue == "1")
                {
                    if (p_calling == "MSC")
                    {
                        SqlParameter[] param = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                    };
                        DataTable INVD = DBHelper.GetDataSetProd("MSC2", Dbo, param);
                        cryRpt.Subreports["INVD"].SetDataSource(INVD);
                        cryRpt.Subreports["INVD2"].SetDataSource(INVD);
                    }

                }
                else
                {
                    string numStr = "";
                    foreach (DataRow dr in dtall.Rows)
                    {
                        numStr = NumGetString.NumGetStr(Convert.ToDouble(dr["yamount"]));
                        dr["yccamount"] = numStr;
                    }
                    cryRpt.DataDefinition.FormulaFields["z_yccamount"].Text = String.Format("'{0}'", numStr);

                }
                cryRpt.SetDataSource(dtall);

                foreach (DataRow dr in dtall.Rows)
                {
                    finvno = dr["finvno"].ToString().Trim();
                    //fpolno = dr["fpolno"].ToString().Trim();
                    fbilldate = dr["fbilldate"].ToString();
                    fpayee = dr["fpayee"].ToString();
                    fcremark = "發票號碼:" + dr["finvno"].ToString().Trim() + "\r\n" + dr["fcremark"].ToString() +"\r\n 請於備妥支票後通知業務部林桂芳(內線7324)"; 
                    yccamount = dr["yccamount"].ToString();
                    yamount = dr["yamount"].ToString();
                    fcinpname = dr["fcinpname"].ToString();
                    //fclass = dr["fclass"].ToString().Trim();
                    GenerDrNote(fclass, "A");
                }
                byte[] fileBytes = ReadfileToByte.ReadFileToByte("M:\\COIL_ePolicy\\Account\\" + finvno.Trim().Replace("/", "").Replace("\\", "") + "A.pdf");

                String res = PostFileToServer("https://pur.csci.com.hk/push/api/push", fileBytes, null, Encoding.UTF8);
                MessageBox.Show(res.ToString(), "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                progressBar1.Value = 0;
            }
            else
            {
                MessageBox.Show("請選憑單", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        void GenerDrNote(string fclass, string Type)
        {
            string fcpytype = "";
            string Dbo = InsEnvironment.DataBase.GetDbo();
            DataTable dtall = new DataTable();
            DataTable dt = new DataTable();
            DataTable dt1 = new DataTable();
            DataTable dt2 = new DataTable();
            DataTable dt3 = new DataTable();

            SqlParameter[] paramall = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };
            dtall = DBHelper.GetDataSetProd("MSC", Dbo, paramall);
            dtall.Clear();

            if (Type =="A")
            {
                fcpytype = "A";
                SqlParameter[] param = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                };
                dt1 = DBHelper.GetDataSetProd("MSC", Dbo, param);
            }


            ReportDocument cryRpt = new ReportDocument();
            cryRpt.Load(rpDirectory + "\\drcrmsc.rpt");

            object[] obj = new object[dtall.Columns.Count];

            if (dt1.Rows.Count > 0)
            {
                for (int i = 0; i < dt1.Rows.Count; i++)
                {
                    dt1.Rows[i].ItemArray.CopyTo(obj, 0);
                    dtall.Rows.Add(obj);
                }
            }

            if (p_calling == "MSC")
            {
                SqlParameter[] param = new SqlParameter[] {
                        new SqlParameter("@fctlid_1", fctlid_1),
                        new SqlParameter("@fcpytype", fcpytype)
                    };
                DataTable INVD = DBHelper.GetDataSetProd("MSC2", Dbo, param);
                cryRpt.Subreports["INVD"].SetDataSource(INVD);
                cryRpt.Subreports["INVD2"].SetDataSource(INVD);
            }

            cryRpt.SetDataSource(dtall);

            try
            {
                ExportOptions CrExportOptions;
                DiskFileDestinationOptions CrDiskFileDestinationOptions = new DiskFileDestinationOptions();
                PdfRtfWordFormatOptions CrFormatTypeOptions = new PdfRtfWordFormatOptions();
                CrDiskFileDestinationOptions.DiskFileName = "M:\\PushToA8\\" + finvno.Trim().Replace("/", "").Replace("\\", "") + ".doc";

                CrExportOptions = cryRpt.ExportOptions;
                {
                    CrExportOptions.ExportDestinationType = ExportDestinationType.DiskFile;
                    CrExportOptions.ExportFormatType = ExportFormatType.WordForWindows;
                    CrExportOptions.DestinationOptions = CrDiskFileDestinationOptions;
                    CrExportOptions.FormatOptions = CrFormatTypeOptions;
                }
                cryRpt.Export();
                Word2PDF WP = new Word2PDF();
                WP.word2PDF("M:\\PushToA8\\" + finvno.Trim().Replace("/", "").Replace("\\", "") + ".doc", "M:\\PushToA8\\" + finvno.Trim().Replace("/", "").Replace("\\", "") + Type + ".pdf");
                ToA8.SignPloNote(finvno.Trim().Replace("/", "").Replace("\\", "") + Type + ".pdf", "Account", fpolno);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }

        }

        public string PostFileToServer(string url, byte[] filedata, byte[] extraFileData, Encoding encoder)
        {
            HttpClient client = new HttpClient();
            var json = Newtonsoft.Json.JsonConvert.SerializeObject(new { Fctlid = fctlid_1, Finvno = finvno, Fbilldate = fbilldate, fpayee = fpayee.Trim(), Fcremark = fcremark, Yccamount = yccamount, Yamount = yamount, Fcinpname = fcinpname.Trim(), UserId = InsEnvironment.LoginUser.GetUserADCode(), Postype = "OinvhDRCR_Acc", FileContent = filedata, ExtraFileContent = extraFileData });
            HttpContent httpContent = new StringContent(json, Encoding.UTF8, "application/json");
            HttpResponseMessage response = client.PostAsync(url, httpContent).Result;
            var result = response.Content.ReadAsStringAsync().Result;
            var s = Newtonsoft.Json.JsonConvert.DeserializeObject(result);
            JavaScriptSerializer js = new JavaScriptSerializer();//实例化一个能够序列化数据的类
            JsonOutput oput = js.Deserialize<JsonOutput>(s.ToString()); //将json数据转化为对象类型并赋值给list
            return oput.Message;
        }

        private void fdoctype_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (fdoctype.SelectedValue == "1")
            {
                pcpy.Checked = true;
                acpy.Checked = true;
                icpy.Checked = true;
            }
            else
            {
                pcpy.Checked = false;
                acpy.Checked = true;
                icpy.Checked = false;
            }

        }
    }
}
