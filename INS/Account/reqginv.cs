using INS.INSClass;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace INS.Business
{
    public partial class reqginv : Form
    {
        public string Class = "";
        public string fbus = "";
        public string flag = "";
        public string Dbm = InsEnvironment.DataBase.GetDbm();
        private tginvh tginvh;

        public reqginv()
        {
            InitializeComponent();
            InitializeStatusName();
            Initializefdbtrtype();
        }

        public reqginv(tginvh tginvh)
        {
            // TODO: Complete member initialization
            this.tginvh = tginvh;
            InitializeComponent();
            InitializeStatusName();
            Initializefdbtrtype();
        }

        void InitializeStatusName()
        {
            BindData[] u_Statusarr = new BindData[]  {
                                 new BindData("0","ALL"),
                                 new BindData("1","Yes"),
                                 new BindData("2","No")};
            fpost.DataSource = u_Statusarr;
            fpost.ValueMember = "ID";
            fpost.DisplayMember = "Item";
        }

        public void Initializefdbtrtype()
        {
            BindData[] u_dtypearr = new BindData[]  {
                                 new BindData("A","ALL."),
                                 new BindData("C","Client"),
                                 new BindData("P","Producer"),
                                 new BindData("B","R/I Broker"),
                                 new BindData("R","Reinsurer")};
            fdbtrtype.DataSource = u_dtypearr;
            fdbtrtype.ValueMember = "ID";
            fdbtrtype.DisplayMember = "Item";

            BindData[] u_statidarr = new BindData[]  {
                                   new BindData("A","All Statement"),
                                   new BindData("D0100","Premium (Dir)"),
                                   new BindData("D0104","Premium (Out)"),
                                   new BindData("D0200","Claims (Dir)"),
                                   new BindData("D0204","Claims (Out)"),
                                   new BindData("R0100","Premium (In)"),
                                   new BindData("R0200","Claims (In)")};
            fstatid.DataSource = u_statidarr;
            fstatid.ValueMember = "ID";
            fstatid.DisplayMember = "Item";
        }

        private void button1_Click(object sender, EventArgs e)
        {
            string sqladd = "";
            foreach (Control ctrl in panel1.Controls)
            {
                if (ctrl is TextBox)
                {
                    if (((TextBox)ctrl).Text==""){
                        sqladd = "";
                    }
                }
                if (ctrl is MaskedTextBox)
                {
                    if (((MaskedTextBox)ctrl).Text == "")
                    {
                        sqladd = "";
                    }
                }
            }

            if (finvno.Text != "")
            {
                sqladd = "And a.finvno = '" + finvno.Text.Trim() + "' ";
            }
            if (fdbtrtype.SelectedValue.ToString().Trim() != "A")
            {
                sqladd = sqladd + "And a.fdbtrtype = '" + fdbtrtype.SelectedValue + "' ";
            }
            if (fstatid.SelectedValue.ToString().Trim() != "A")
            {
                sqladd = sqladd + "And a.fstatid = '" + fstatid.SelectedValue + "' ";
            }
            if (fdbtr.Text != "")
            {
                sqladd = sqladd + "And a.fdbtr = '" + fdbtr.Text.Trim() + "' ";
            }
            if (fdbtrdesc.Text != "")
            {
                sqladd = sqladd + "And a.fdbtrdesc = '" + fdbtrdesc.Text.Trim() + "' ";
            }
            if (finvdate.Text != "    .  ." && feffto1.Text != "    .  .")
            {
                sqladd = sqladd + "And a.finvdate between '" + finvdate.Text + "' and  '" + feffto1.Text + "' ";
            }
            if (fpost.SelectedValue.ToString() != "0" && fpost != null)
            {
                sqladd = sqladd + "And a.fposted = '" + fpost.SelectedValue + "' ";
            }
            if (sqladd == "") { sqladd = sqladd + "And a.fbus = '" + fbus + "' "; }
            
            if (flag == "tginvh")
            {
                tginvh.FillData("", sqladd, "");
            }
            this.Close();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void reqginv_Resize(object sender, EventArgs e)
        {
            if (WindowState == FormWindowState.Maximized)
            {
                //最大化时所需的操作 
                this.ClientSize = new System.Drawing.Size(755, 300);
                this.WindowState = FormWindowState.Normal;
            }
            else if (WindowState == FormWindowState.Minimized)
            {
                //最小化时所需的操作

                this.ClientSize = new System.Drawing.Size(50, 50);
                this.WindowState = FormWindowState.Normal;
            } 
        }


    }
}
