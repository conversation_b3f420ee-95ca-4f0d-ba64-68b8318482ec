using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows;
using System.Windows.Forms;
using INS.INSClass;
using INS.Claim;
using INS.Account;
using INS.Business.Inquiry;
using INS.Business;

namespace INS
{
    public partial class FrmMain1 : Form
    {
        public FrmMain1()
        {
            InitializeComponent();
        }

        public string User = InsEnvironment .LoginUser .GetUserCode ();
        public string str = "";
        DBConnect operate = new DBConnect();
        SizeAuto asc = new SizeAuto(); 
        private void FrmMain1_Load(object sender, EventArgs e)
        {
            asc.controllInitializeSize(this);  
            if (User == "")
            {
                Frm_Demo Convert = new Frm_Demo();
                frmLogin Login = new frmLogin();
                Login.ShowDialog();
            }
            else
            {
                //toolStripStatusLabel2.Text = User;
                string sql = "select * from muser where Fcode='" + User + "'";
                DataTable dt = operate.GetTable(sql);
                string Fright01 = dt.Rows[0]["Fright01"].ToString();
                string Fright02 = dt.Rows[0]["Fright02"].ToString();
                string Fright03 = dt.Rows[0]["Fright03"].ToString();
                string Fright03a = dt.Rows[0]["Fright3a"].ToString();
                string Fright03b = dt.Rows[0]["Fright3b"].ToString();
                string Fright03c = dt.Rows[0]["Fright3c"].ToString();
                string Fright04 = dt.Rows[0]["Fright04"].ToString();
                string Fright04a = dt.Rows[0]["Fright4a"].ToString();
                string Fright04b = dt.Rows[0]["Fright4b"].ToString();
                string Fright04c = dt.Rows[0]["Fright4c"].ToString();
                string Fright05 = dt.Rows[0]["Fright05"].ToString();
                string Fright06 = dt.Rows[0]["Fright06"].ToString();
                string Fright07 = dt.Rows[0]["Fright07"].ToString();
                string Fright08 = dt.Rows[0]["Fright08"].ToString();
                string Fright09 = dt.Rows[0]["Fright09"].ToString();
                string xsuper = dt.Rows[0]["xsuper"].ToString();
                if (xsuper == "1")
                {
                    helpToolStripMenuItem.Enabled = true; 
                }
                if (Fright03.Substring(0, 1) == "0")
                {
                    foreach (TreeNode tn in treeView1.Nodes)
                    {
                        foreach (TreeNode child in tn.Nodes)
                        {
                            if (child.Name.ToString() == "FrmBDirect")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "FrmBCOInsur")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "FrmBRII")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "BNode3")
                            {
                                child.ForeColor = Color.Gray;
                                if (child.Nodes.Count > 0)
                                {
                                    for (int i = child.Nodes.Count - 1; i >= 0; i--)
                                    {
                                        child.Nodes[i].Remove();
                                    }
                                }
                            }
                        }
                    }
                }
                if (Fright03.Substring(9, 1) == "0")
                {
                    foreach (TreeNode tn in treeView1.Nodes)
                    {
                        foreach (TreeNode child in tn.Nodes)
                        {
                            if (child.Name.ToString() == "FrmBReport")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            
                        }
                    }
                }
                if (Fright03c.Substring(2, 1) == "0")
                {
                    foreach (TreeNode tn in treeView1.Nodes)
                    {
                        foreach (TreeNode child in tn.Nodes)
                        {
                            if (child.Name.ToString() == "BNode5")
                            {
                                child.ForeColor = Color.Gray;
                                if (child.Nodes.Count > 0)
                                {
                                    for (int i = child.Nodes.Count - 1; i >= 0; i--)
                                    {
                                        child.Nodes[i].Remove();
                                    }
                                }
                            }

                        }
                    }
                }
                if (Fright03a.Substring(0, 1) == "0")
                {
                    foreach (TreeNode tn in treeView1.Nodes)
                    {
                        foreach (TreeNode child in tn.Nodes)
                        {
                            if (child.Name.ToString() == "FrmBNotes")
                            {
                                child.ForeColor = Color.Gray;
                            }

                        }
                    }
                }
                if (Fright04.Substring(0, 1) == "0")
                {
                    foreach (TreeNode tn in treeView1.Nodes)
                    {
                        foreach (TreeNode child in tn.Nodes)
                        {
                            if (child.Name.ToString() == "FrmCDirect")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "FrmCCOInsur")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "FrmCRII")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "Node14")
                            {
                                child.ForeColor = Color.Gray;
                                if (child.Nodes.Count > 0)
                                {
                                    for (int i = child.Nodes.Count - 1; i >= 0; i--)
                                    {
                                        child.Nodes[i].Remove();
                                    }
                                }
                            }
                        }
                    }
                }
                if (Fright04.Substring(9, 1) == "0")
                {
                    foreach (TreeNode tn in treeView1.Nodes)
                    {
                        foreach (TreeNode child in tn.Nodes)
                        {
                            if (child.Name.ToString() == "FrmCReport")
                            {
                                child.ForeColor = Color.Gray;
                            }

                        }
                    }
                }
                if (Fright04a.Substring(0, 1) == "0")
                {
                    foreach (TreeNode tn in treeView1.Nodes)
                    {
                        foreach (TreeNode child in tn.Nodes)
                        {
                            if (child.Name.ToString() == "FrmCNotes")
                            {
                                child.ForeColor = Color.Gray;
                            }

                        }
                    }
                }
                if (Fright04c.Substring(0, 1) == "0")
                {
                    foreach (TreeNode tn in treeView1.Nodes)
                    {
                        foreach (TreeNode child in tn.Nodes)
                        {
                            if (child.Name.ToString() == "Node17")
                            {
                                child.ForeColor = Color.Gray;
                            }

                        }
                    }
                }
                if (Fright05.Substring(0, 1) == "0")
                {
                    foreach (TreeNode tn in treeView1.Nodes)
                    {
                        foreach (TreeNode child in tn.Nodes)
                        {
                            if (child.Name.ToString() == "Node18")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "Node19")
                            {
                                child.ForeColor = Color.Gray;
                            }
                        }
                    }
                }
                if (Fright05.Substring(9, 1) == "0")
                {
                    foreach (TreeNode tn in treeView1.Nodes)
                    {
                        foreach (TreeNode child in tn.Nodes)
                        {
                            if (child.Name.ToString() == "FrmAReport")
                            {
                                child.ForeColor = Color.Gray;
                            }

                        }
                    }
                }
                if (Fright06.Substring(0, 1) == "0")
                {
                    foreach (TreeNode tn in treeView1.Nodes)
                    {
                        foreach (TreeNode child in tn.Nodes)
                        {
                            if (child.Name.ToString() == "frmAtt")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "frmExcessClause")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "frmSite")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "frmProject")
                            {
                                child.ForeColor = Color.Gray;
                            }
                        }
                    }
                }
                if (Fright07.Substring(0, 1) == "0")
                {
                    foreach (TreeNode tn in treeView1.Nodes)
                    {
                        foreach (TreeNode child in tn.Nodes)
                        {
                            if (child.Name.ToString() == "frmLossAdjuster")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "frmSolicitor")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "frmHealth")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "frmNatureLoss")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "frmSubContractor")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "frmAccident")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "frmJobNature")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "frmSelectPolicy")
                            {
                                child.ForeColor = Color.Gray;
                            }
                        }
                    }
                }
                if (Fright08.Substring(0, 1) == "0")
                {
                    foreach (TreeNode tn in treeView1.Nodes)
                    {
                        foreach (TreeNode child in tn.Nodes)
                        {
                            if (child.Name.ToString() == "frmProducer")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "frmClient")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "frmBroker")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "frmReinsurer")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "frmInsClass")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "frmInsClass")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "frmInsInterest")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "frmMiscCode")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "frmChargeCode")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "frmGL")
                            {
                                child.ForeColor = Color.Gray;
                            }
                            if (child.Name.ToString() == "frmTreaty")
                            {
                                child.ForeColor = Color.Gray;
                                if (child.Nodes.Count > 0)
                                {
                                    for (int i = child.Nodes.Count - 1; i >= 0; i--)
                                    {
                                        child.Nodes[i].Remove();
                                    }
                                }
                            }
                            if (child.Name.ToString() == "frmIBNR")
                            {
                                child.ForeColor = Color.Gray;
                            }
                        }
                    }
                }

            }
        }

        private void treeView1_BeforeSelect(object sender, TreeViewCancelEventArgs e)
        {
            if (Color.Gray == e.Node.ForeColor)
                e.Cancel = true;
        }

        private void FrmMain1_SizeChanged(object sender, EventArgs e)
        {
            asc.controlAutoSize(this);
        }  

        private void treeView1_AfterSelect(object sender, TreeViewEventArgs e)
        {

            str = e.Node.Name;//取得相应treeview结点的text值
            bool remark = true;

            for (int i = 0; i < this.MdiChildren.Length; i++)

                if (this.MdiChildren[i].Name.Equals(str)) //查看有没有相同的MDI子窗体
                {
                    this.MdiChildren[i].Activate(); //子窗体已经被创立，激活它
                    remark = false;
                    break;
                }

            if (remark) // 末创立，建立子窗体
            {

                panel1.Controls.Clear();
                if (str == "frmAtt" && e.Node.ForeColor != Color.Gray)
                {
                    frmAtt temp_form = new frmAtt();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();

                }
                if (str == "frmClient" && e.Node.ForeColor != Color.Gray)
                {
                    frmLossAdjuster temp_form = new frmLossAdjuster("C");
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    temp_form.master = "C";
                    temp_form.FillData("", "");
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();

                }
                if (str == "frmMiscCode" && e.Node.ForeColor != Color.Gray)
                {
                    frmMiscCode temp_form = new frmMiscCode();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();

                }
                if (str == "frmFac" && e.Node.ForeColor != Color.Gray)
                {
                    frmFac temp_form = new frmFac();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    //temp_form.Show();

                }
                if (str == "frmExcessClause" && e.Node.ForeColor != Color.Gray)
                {
                    frmExcessClause temp_form = new frmExcessClause();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "frmSite" && e.Node.ForeColor != Color.Gray)
                {
                    frmSite temp_form = new frmSite();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "frmChargeCode" && e.Node.ForeColor != Color.Gray)
                {
                    frmChargeCode temp_form = new frmChargeCode();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "frmProject" && e.Node.ForeColor != Color.Gray)
                {
                    frmProject temp_form = new frmProject();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "frmLossAdjuster" && e.Node.ForeColor != Color.Gray)
                {
                    frmLossAdjuster temp_form = new frmLossAdjuster("L");
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.master = "L";
                    temp_form.Text = str;
                    temp_form.FillData("", "");
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "frmSolicitor" && e.Node.ForeColor != Color.Gray)
                {
                    frmLossAdjuster temp_form = new frmLossAdjuster("S");
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    temp_form.master = "S";
                    temp_form.FillData("", "");
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "frmHealth" && e.Node.ForeColor != Color.Gray)
                {
                    frmLossAdjuster temp_form = new frmLossAdjuster("H");
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    temp_form.master = "H";
                    temp_form.FillData("", "");
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "frmNatureLoss" && e.Node.ForeColor != Color.Gray)
                {
                    frmNatureLoss temp_form = new frmNatureLoss();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "frmSubContractor" && e.Node.ForeColor != Color.Gray)
                {
                    frmSubContractor temp_form = new frmSubContractor();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "frmAccident" && e.Node.ForeColor != Color.Gray)
                {
                    frmAccident temp_form = new frmAccident();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "frmJobNature" && e.Node.ForeColor != Color.Gray)
                {
                    frmJobNature temp_form = new frmJobNature();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "frmDept" && e.Node.ForeColor != Color.Gray)
                {
                    frmDept temp_form = new frmDept();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "frmSurplus" && e.Node.ForeColor != Color.Gray)
                {
                    frmSurplus temp_form = new frmSurplus();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "frmGL" && e.Node.ForeColor != Color.Gray)
                {
                    frmGL temp_form = new frmGL();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "frmXOL" && e.Node.ForeColor != Color.Gray)
                {
                    frmXOL2 temp_form = new frmXOL2();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "frmSelectPolicy" && e.Node.ForeColor != Color.Gray)
                {
                    frmSelectPolicy temp_form = new frmSelectPolicy();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "frmProducer" && e.Node.ForeColor != Color.Gray)
                {
                    frmLossAdjuster temp_form = new frmLossAdjuster("P");
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    temp_form.master = "P";
                    temp_form.FillData("", "");
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "frmBroker" && e.Node.ForeColor != Color.Gray)
                {
                    frmLossAdjuster temp_form = new frmLossAdjuster("B");
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    temp_form.master = "B";
                    temp_form.FillData("", "");
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "frmReinsurer" && e.Node.ForeColor != Color.Gray)
                {
                    frmLossAdjuster temp_form = new frmLossAdjuster("R");
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    temp_form.master = "R";
                    temp_form.FillData("", "");
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "frmInsClass" && e.Node.ForeColor != Color.Gray)
                {
                    frmInsClass temp_form = new frmInsClass();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "frmInsInterest" && e.Node.ForeColor != Color.Gray)
                {
                    frmInsInterest temp_form = new frmInsInterest();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "FrmBDirect" && e.Node.ForeColor != Color.Gray)
                {
                    FrmBDirect temp_form = new FrmBDirect();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "tcvrlet" && e.Node.ForeColor != Color.Gray)
                {
                    tcvrlet temp_form = new tcvrlet();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "FrmDirectInquiry" && e.Node.ForeColor != Color.Gray)
                {
                    DirectInquiry temp_form = new DirectInquiry();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.FillData("");
                    temp_form.Show();
                }
                if (str == "FrmCOInquiry" && e.Node.ForeColor != Color.Gray)
                {
                    COInquiry temp_form = new COInquiry();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.FillData("");
                    temp_form.Show();
                }
                if (str == "FrmRIInquiry" && e.Node.ForeColor != Color.Gray)
                {
                    RIInquiry temp_form = new RIInquiry();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
              //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.FillData("");
                    temp_form.Show();
                }
                
                if (str == "FrmBCOInsur" && e.Node.ForeColor != Color.Gray)
                {
                    FrmBCOInsur temp_form = new FrmBCOInsur();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "FrmBRII" && e.Node.ForeColor != Color.Gray)
                {
                    FrmBRII temp_form = new FrmBRII();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "FrmBReport" && e.Node.ForeColor != Color.Gray)
                {
                    FrmBReport temp_form = new FrmBReport();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "undauto" && e.Node.ForeColor != Color.Gray)
                {
                    undauto temp_form = new undauto();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "FrmBNotes" && e.Node.ForeColor != Color.Gray)
                {
                    FrmBNotes temp_form = new FrmBNotes();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "FrmCDirect" && e.Node.ForeColor != Color.Gray)
                {
                    FrmCDirect temp_form = new FrmCDirect();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "FrmCCOInsur" && e.Node.ForeColor != Color.Gray)
                {
                    FrmCCOInsur temp_form = new FrmCCOInsur();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "FrmCRII" && e.Node.ForeColor != Color.Gray)
                {
                    FrmCRII temp_form = new FrmCRII();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "DirectClaimInquiry" && e.Node.ForeColor != Color.Gray)
                {
                    DirectClaimInquiry temp_form = new DirectClaimInquiry();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.FillData("");
                    temp_form.Show();
                }
                if (str == "RIClaimInquiry" && e.Node.ForeColor != Color.Gray)
                {
                    RIClaimInquiry temp_form = new RIClaimInquiry();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.FillData("");
                    temp_form.Show();
                }
                if (str == "FrmCReport" && e.Node.ForeColor != Color.Gray)
                {
                    FrmCReport temp_form = new FrmCReport();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "FrmCNotes" && e.Node.ForeColor != Color.Gray)
                {
                    FrmCNotes temp_form = new FrmCNotes();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "FrmAReport" && e.Node.ForeColor != Color.Gray)
                {
                    FrmAReport temp_form = new FrmAReport();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "tginvh" && e.Node.ForeColor != Color.Gray)
                {
                    tginvh temp_form = new tginvh();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    temp_form.fbus = "G";
                    temp_form.FillData("", "", "");
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "tpyset" && e.Node.ForeColor != Color.Gray)
                {
                    tpyset temp_form = new tpyset();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    temp_form.FillData("", "", "");
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
                if (str == "ialevy" && e.Node.ForeColor != Color.Gray)
                {
                    frmLevy temp_form = new frmLevy();
                    temp_form.TopLevel = false;
                    temp_form.Parent = panel1;
                    temp_form.Dock = DockStyle.Fill;
                    temp_form.Name = str;
                    temp_form.Text = str;
                    temp_form.FillData("", "");
                    //设置子窗体无标题栏
                    temp_form.FormBorderStyle = FormBorderStyle.None;
                    temp_form.WindowState = FormWindowState.Maximized;
                    temp_form.FormClosed += new FormClosedEventHandler(form_FormClosed);
                    temp_form.Show();
                }
            }
        }

        private void form_FormClosed(object sender, FormClosedEventArgs e)
        {
            treeView1.SelectedNode = null;
        }

        private void fileToolStripMenuItem_Click(object sender, EventArgs e)
        {
            frmChangePwd ChangePwd = new frmChangePwd();
            ChangePwd.name = User;
            ChangePwd.ShowDialog();
        }

        private void logOutToolStripMenuItem_Click(object sender, EventArgs e)
        {
            System.Windows.Forms.Application.Exit();
            User = "";
        }

        private void addUserToolStripMenuItem_Click(object sender, EventArgs e)
        {
            edituser AddUser = new edituser();
            //AddUser.UserName = User;
            AddUser.ShowDialog();
        }

        private void systemModeToolStripMenuItem_Click(object sender, EventArgs e)
        {

        }


    }
}
