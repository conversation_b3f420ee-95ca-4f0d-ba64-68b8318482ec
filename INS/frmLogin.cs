using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Data.SqlClient;

using InsEnvironment;
using INS.INSClass;

namespace INS
{
    public partial class frmLogin : Form
    {
        public frmLogin()
        {
            InitializeComponent();

            foreach (Control control in this.Controls)                //循环窗体的控件
            {
                control.KeyDown += new KeyEventHandler(control_KeyDown);                //添加事件
            }

            xsdr.Read();

            if (!xsdr.HasRows)
            {
                MessageBox.Show("Company information is not found, operation aborted!", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);

                xcmd.Dispose();
                xsdr.Close();
                conn.Close();

                this.Dispose();
                this.Close();
            }
            else
            {
                set_Company_Param(xsdr);

                xcmd.Dispose();
                xsdr.Close();
            }
        }

        DBConnect operate = new DBConnect();
       
        static SqlConnection conn = DBConnect.dbConn;   
        static string sqlCmd = "select * from xsyscomp";
        static SqlCommand xcmd = new SqlCommand(sqlCmd, conn);
        static SqlDataReader xsdr = xcmd.ExecuteReader();

        void control_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)                        //判断用户是否按下回车键
            {
                SendKeys.Send("{TAB}");
            }
        }

        private void set_Company_Param (SqlDataReader dr)
        {
            CompInfo.SetChineseComp(dr["Fchname"].ToString());
            CompInfo.SetChaddr1(dr["Fchaddr1"].ToString());
            CompInfo.SetChaddr2(dr["Fchaddr2"].ToString());
            CompInfo.SetChaddr3(dr["Fchaddr3"].ToString());
            CompInfo.SetEnglishComp(dr["Fname"].ToString());
            CompInfo.SetEngaddr1(dr["Faddr1"].ToString());
            CompInfo.SetEngaddr2(dr["Faddr2"].ToString());
            CompInfo.SetEngaddr3(dr["Faddr3"].ToString());
            CompInfo.SetEngaddr4(dr["Faddr4"].ToString());
            CompInfo.SetBaseCur(dr["Fbasecur"].ToString());
            CompInfo.SetTel(dr["Ftel"].ToString());
            CompInfo.SetFax(dr["Ffax"].ToString());
            CompInfo.SetPassLen(Convert.ToInt32(dr["Fpasslen"]));
            CompInfo.SetPasslife(Convert.ToInt32(dr["Fpasslife"]));
            CompInfo.SetPassCyle(Convert.ToInt32(dr["Fpasscyle"]));
            CompInfo.SetLoginTry(Convert.ToInt32(dr["Flogintry"]));
            CompInfo.SetBusign(dr["fbusign"].ToString());
            CompInfo.SetCusign(dr["fcusign"].ToString());
            CompInfo.SetBchkby(dr["fbchkby"].ToString());
            CompInfo.SetCchkby(dr["fcchkby"].ToString());
            CompInfo.SetBpreby(dr["fbpreby"].ToString());
            CompInfo.SetCpreby(dr["fcpreby"].ToString());
        }

        private void set_User_Param(SqlDataReader dr)
        {
            LoginUser.SetUserCode(dr["Fcode"].ToString());
            LoginUser.SetEnglishName(dr["Fname"].ToString());
            LoginUser.SetChineseName(dr["Fcname"].ToString());
            LoginUser.SetUserADCode(dr["Fapcode"].ToString());

            LoginUser.SetUserRight01(dr["Fright01"].ToString());
            LoginUser.SetUserRight02(dr["Fright02"].ToString());
            LoginUser.SetUserRight03(dr["Fright03"].ToString());
            LoginUser.SetUserRight3a(dr["Fright3a"].ToString());
            LoginUser.SetUserRight3b(dr["Fright3b"].ToString());
            LoginUser.SetUserRight3c(dr["Fright3c"].ToString());

            LoginUser.SetUserRight04(dr["Fright04"].ToString());
            LoginUser.SetUserRight4a(dr["Fright4a"].ToString());
            LoginUser.SetUserRight4b(dr["Fright4b"].ToString());
            LoginUser.SetUserRight4c(dr["Fright4c"].ToString());
            LoginUser.SetUserRight4d(dr["Fright4d"].ToString());
            LoginUser.SetUserRight4e(dr["Fright4e"].ToString());
            LoginUser.SetUserRight4f(dr["Fright4f"].ToString());

            LoginUser.SetUserRight05(dr["Fright05"].ToString());
            LoginUser.SetUserRight06(dr["Fright06"].ToString());
            LoginUser.SetUserRight07(dr["Fright07"].ToString());
            LoginUser.SetUserRight08(dr["Fright08"].ToString());
            LoginUser.SetUserRight09(dr["Fright09"].ToString());

            LoginUser.SetUserBRight03(dr["Bright03"].ToString());
            LoginUser.SetUserBRight04(dr["Bright04"].ToString());
            LoginUser.SetUserBRight05(dr["Bright05"].ToString());
            LoginUser.SetUserBRight06(dr["Bright06"].ToString());
            LoginUser.SetUserBRight07(dr["Bright07"].ToString());
            LoginUser.SetUserBRight08(dr["Bright08"].ToString());
        }

        private void btnLogin_Click_1(object sender, EventArgs e)
        {
            Boolean proceed = true;
            string name = txtUserName.Text.Trim();
//            DES des = new DES();
//            string key = des.GenerateKey();

            string pwd = txtUserPwd.Text.Trim();
            if (pwd.Length < 10)
                pwd = pwd.PadRight(10, ' ');
            else
                pwd = pwd.Substring(0, 10);

//            SqlConnection conn = DBConnect.dbConn;   
       
            Int32 maxLogin = 99999;
            
            string sqlString = "select * from muser where Fcode = '{0}'";
            sqlString = string.Format(sqlString, name);

            SqlCommand cmd = new SqlCommand(sqlString, conn);
            SqlDataReader sdr = cmd.ExecuteReader();

            sdr.Read();
            cmd.Dispose();

            if (txtUserName.Text.Trim() == "" || txtUserPwd.Text.Trim() == "")
                proceed = false;

            if (!sdr.HasRows)
                proceed = false;

            Int32 DateTime_diff = -1;
            DateTime time, Ftime;
            String decode_pwd="", Login_enable="";
            Int32 No_of_Failure=0;

            if (proceed)
            {
                time = Convert.ToDateTime(DateTime.Now.ToShortDateString());
                Ftime = Convert.ToDateTime(sdr["Fvdate"].ToString());
                DateTime_diff = (Ftime - time).Days;

                decode_pwd = UtilityFunc.EnCode_Str(sdr["Fpassword"].ToString(), false);
                No_of_Failure = Convert.ToInt32(sdr["Ffailure"]);
                Login_enable = sdr["fenable"].ToString().Trim();
            }

            if (proceed && DateTime_diff < 0)
            {
                sqlString = "update muser set Fenable = '2' where Fcode = '{0}'";
                sqlString = string.Format(sqlString, name);

                sdr.Close();

                SqlCommand UpdateCmd = new SqlCommand(sqlString, conn);
                UpdateCmd.ExecuteNonQuery();

                UpdateCmd.Dispose();

                proceed = false;
            }

            if (proceed && pwd != decode_pwd)
            {
                if (Login_enable == "1")
                {
                    if (++No_of_Failure >= maxLogin)
                    {
                        No_of_Failure = 0;
                        Login_enable = "2";
                    }

                    sqlString = "update muser set Ffailure = {0}, Fenable = '{1}' where Fcode = '{2}'";
                    sqlString = string.Format(sqlString, No_of_Failure, Login_enable, name);

                    sdr.Close();

                    SqlCommand UpdateCmd = new SqlCommand(sqlString, conn);
                    UpdateCmd.ExecuteNonQuery();

                    UpdateCmd.Dispose();
                }

                proceed = false;
            }
            
            if (proceed && Login_enable != "1")
                proceed = false;

            if (proceed)
            {
                set_User_Param(sdr);
                
                sqlString = "update muser set Ffailure = 0 where Fcode = '{0}'";
                sqlString = string.Format(sqlString, name);

                sdr.Close();

                SqlCommand UpdateCmd = new SqlCommand(sqlString, conn);
                UpdateCmd.ExecuteNonQuery();

                UpdateCmd.Dispose();
            }

            sdr.Close();

            DateTime_diff ++;

            if (proceed && DateTime_diff <= 14)
                MessageBox.Show("Change Password!  Current Password remains "+DateTime_diff.ToString("G")+
                                " day(s) Valid", "Warning",MessageBoxButtons.OK, MessageBoxIcon.Information);

            if (proceed && DateTime_diff <= 7)
            {
                frmChangePwd form = new frmChangePwd();
                form.name = name;
                form.flag = "changepsw";
                this.Hide();
                form.ShowDialog();
            }
            
            if (proceed)
            {
                FrmMain1 Main = new FrmMain1();
                Main.User = name;
                this.Hide();

                Main.ShowDialog();
              
                conn.Close();
                this.Close();

            }
            else
            {
                if (Login_enable != "1") {
                    MessageBox.Show("The User has been blocked!", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("Invalid User or Password!", "Warning",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                txtUserName.Text = "";
                txtUserPwd.Text = "";
                txtUserName.Focus();
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

    }
}
