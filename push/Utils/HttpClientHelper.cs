using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Web;

namespace Utils
{
    public static class HttpClientHelper
    {
        public static string[] FetchResultsViaHttpGet(string url, params string[] valueJsonPaths)
        {
            if (string.IsNullOrWhiteSpace(url))
                throw new ArgumentNullException(nameof(url));
            if (valueJsonPaths == null || valueJsonPaths.Length == 0)
                throw new ArgumentNullException(nameof(valueJsonPaths));
            using (HttpClient client = new HttpClient())
            {
                var responseMessage = client.GetAsync(url).Result;
                var content = responseMessage.Content.ReadAsStringAsync().Result;
                if (!responseMessage.IsSuccessStatusCode)
                {
                    var error = JsonReaderHelper.ReadJsonPathsValueAsString(content, "error")[0];
                    throw new Exception(error);
                }
                var ssoToken = JsonReaderHelper.ReadJsonPathsValueAsString(content, valueJsonPaths);
                return ssoToken;
            }
        }

        public static string[] FetchResultsViaHttpPost(string url, string sContent, string accessToken=null, params string[] valueJsonPaths)
        {
            if (string.IsNullOrWhiteSpace(url))
                throw new ArgumentNullException(nameof(url));
            if (valueJsonPaths == null || valueJsonPaths.Length == 0)
                throw new ArgumentNullException(nameof(valueJsonPaths));
            HttpContent hc = new StringContent(sContent, Encoding.UTF8, "application/json");
            using (HttpClient client = new HttpClient())
            {
                if (!string.IsNullOrEmpty(accessToken))
                {
                    //设置请求头中的验证(访问令牌)。下面两种方式都可以
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", HttpUtility.UrlEncode(accessToken));
                    //client.DefaultRequestHeaders.Add("Authorization", "Bearer "+HttpUtility.UrlEncode(accessToken));
                }
                var responseMessage = client.PostAsync(url, hc).Result;
                var s = responseMessage.Content.ReadAsStringAsync().Result;
                if (!responseMessage.IsSuccessStatusCode)
                {
                    var error = JsonReaderHelper.ReadJsonPathsValueAsString(s, "error")[0];
                    throw new Exception(error);
                }
                var ssoToken = JsonReaderHelper.ReadJsonPathsValueAsString(s, valueJsonPaths);
                return ssoToken;
            }
        }

        public static string[] FetchResultsViaHttpPostCAP4(string url, string sContent, string accessToken = null, params string[] valueJsonPaths)
        {
            if (string.IsNullOrWhiteSpace(url))
                throw new ArgumentNullException(nameof(url));
            if (valueJsonPaths == null || valueJsonPaths.Length == 0)
                throw new ArgumentNullException(nameof(valueJsonPaths));
            HttpContent hc = new StringContent(sContent, Encoding.UTF8, "application/json");
            using (HttpClient client = new HttpClient())
            {
                if (!string.IsNullOrEmpty(accessToken))
                {
                    //设置请求头中的验证(访问令牌)。下面两种方式都可以
                    //client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("token", HttpUtility.UrlEncode(accessToken));
                    client.DefaultRequestHeaders.Add("token", HttpUtility.UrlEncode(accessToken));
                }
                var responseMessage = client.PostAsync(url, hc).Result;
                var s = responseMessage.Content.ReadAsStringAsync().Result;
                if (!responseMessage.IsSuccessStatusCode)
                {
                    var error = JsonReaderHelper.ReadJsonPathsValueAsString(s, "error")[0];
                    throw new Exception(error);
                }
                var ssoToken = JsonReaderHelper.ReadJsonPathsValueAsString(s, valueJsonPaths);
                return ssoToken;
            }
        }

        public static string FetchResultsViaHttpPostCAP3(string url, string sContent, string accessToken = null, params string[] valueJsonPaths)
        {
            if (string.IsNullOrWhiteSpace(url))
                throw new ArgumentNullException(nameof(url));
            if (valueJsonPaths == null || valueJsonPaths.Length == 0)
                throw new ArgumentNullException(nameof(valueJsonPaths));
            HttpContent hc = new StringContent(sContent, Encoding.UTF8, "application/json");
            using (HttpClient client = new HttpClient())
            {
                if (!string.IsNullOrEmpty(accessToken))
                {
                    //设置请求头中的验证(访问令牌)。下面两种方式都可以
                    //client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("token", HttpUtility.UrlEncode(accessToken));
                    client.DefaultRequestHeaders.Add("token", HttpUtility.UrlEncode(accessToken));
                }
                var responseMessage = client.PostAsync(url, hc).Result;
                var s = responseMessage.Content.ReadAsStringAsync().Result;
                if (!responseMessage.IsSuccessStatusCode)
                {
                    var error = JsonReaderHelper.ReadJsonPathsValueAsString(s, "error")[0];
                    throw new Exception(error);
                }
                //var ssoToken = JsonReaderHelper.ReadJsonPathsValueAsString(s, valueJsonPaths);
                return s;
            }
        }

        public static string[] FetchResultsViaHttpPost(string url, string sContent, Dictionary<string,string> dictHeaders=null, params string[] valueJsonPaths)
        {
            if (string.IsNullOrWhiteSpace(url))
                throw new ArgumentNullException(nameof(url));
            if (valueJsonPaths == null || valueJsonPaths.Length == 0)
                throw new ArgumentNullException(nameof(valueJsonPaths));
            HttpContent hc = new StringContent(sContent, Encoding.UTF8);
            using (HttpClient client = new HttpClient())
            {
                foreach(var item in dictHeaders)
                {
                    client.DefaultRequestHeaders.Add(item.Key, item.Value);
                }
                var responseMessage = client.PostAsync(url, hc).Result;
                var s = responseMessage.Content.ReadAsStringAsync().Result;
                if (!responseMessage.IsSuccessStatusCode)
                {
                    var error = JsonReaderHelper.ReadJsonPathsValueAsString(s, "error")[0];
                    throw new Exception(error);
                }
                Console.WriteLine(s);
                var results = JsonReaderHelper.ReadJsonPathsValueAsString(s, valueJsonPaths);
                return results;
            }
        }
    }
}
